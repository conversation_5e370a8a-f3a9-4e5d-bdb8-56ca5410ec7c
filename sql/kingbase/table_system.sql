-- "middle"."midoffice_schedule" definition

-- Drop table

-- DROP TABLE "middle"."midoffice_schedule";

CREATE TABLE "middle"."midoffice_schedule" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"title" character varying(1020 byte) NULL,
	"start_time" timestamp(0) without time zone NULL,
	"end_time" timestamp(0) without time zone NULL,
	"address" character varying(1020 byte) NULL,
	"type" integer NULL,
	"remark" character varying(1020 byte) NULL,
	"source" integer NULL,
	"create_time" timestamp(0) without time zone NOT NULL,
	"update_time" timestamp(0) without time zone NOT NULL,
	"deleted" boolean NOT NULL,
	"process_instance_id" character varying(1020 byte) NULL,
	"is_attend" character varying(40 byte) NULL,
	"meeting_status" character varying(40 byte) NULL,
	"apply_type" character varying(40 byte) NULL,
	CONSTRAINT "cons134219533_1fea7567_3BF7F1F8" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."midoffice_schedule" IS '日程表';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN "middle"."midoffice_schedule"."id" IS '主键';
COMMENT ON COLUMN "middle"."midoffice_schedule"."user_id" IS '用户id';
COMMENT ON COLUMN "middle"."midoffice_schedule"."title" IS '标题';
COMMENT ON COLUMN "middle"."midoffice_schedule"."start_time" IS '开始时间';
COMMENT ON COLUMN "middle"."midoffice_schedule"."end_time" IS '结束时间';
COMMENT ON COLUMN "middle"."midoffice_schedule"."address" IS '地点';
COMMENT ON COLUMN "middle"."midoffice_schedule"."type" IS '类型（0=会议，1=活动，2=事务，3=其他）';
COMMENT ON COLUMN "middle"."midoffice_schedule"."remark" IS '备注';
COMMENT ON COLUMN "middle"."midoffice_schedule"."source" IS '0:自定义 1:指派';
COMMENT ON COLUMN "middle"."midoffice_schedule"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."midoffice_schedule"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."midoffice_schedule"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."midoffice_schedule"."process_instance_id" IS '流程id';
COMMENT ON COLUMN "middle"."midoffice_schedule"."is_attend" IS '参会回执 0:不参会 1:参会 NULL:未回应/不需回执/会议已结束';
COMMENT ON COLUMN "middle"."midoffice_schedule"."meeting_status" IS '会议状态 0:审批不通过 1:待审批 2:待开始 3:会议中 4:已结束 5:已取消';
COMMENT ON COLUMN "middle"."midoffice_schedule"."apply_type" IS '申请类型 0:会议申请 1:会议室预定';


-- "middle"."midoffice_shortcut" definition

-- Drop table

-- DROP TABLE "middle"."midoffice_shortcut";

CREATE TABLE "middle"."midoffice_shortcut" (
	"id" bigint AUTO_INCREMENT,
	"client_id" bigint NULL,
	"name" character varying(256 byte) NOT NULL,
	"link_url" character varying(1020 byte) NULL,
	"remark" character varying(1020 byte) NULL,
	"sort" bigint NOT NULL,
	"svg_icon" character varying(800 byte) NULL,
	"menu_id" bigint NULL,
	"status" boolean NOT NULL DEFAULT 0,
	"creator" character varying(256 byte) NULL,
	"create_time" timestamp(0) without time zone NULL,
	"updater" character varying(256 byte) NULL,
	"update_time" timestamp(0) without time zone NULL,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219535_9cfd9fd8_5A42C840" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."midoffice_shortcut" IS '快捷入口表';

-- Column comments

COMMENT ON COLUMN "middle"."midoffice_shortcut"."id" IS '主键';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."client_id" IS '所属子系统id';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."name" IS '快捷入口名称';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."link_url" IS '快捷入口链接';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."remark" IS '备注';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."sort" IS '排序';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."svg_icon" IS '图标';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."menu_id" IS '菜单id';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."status" IS '状态';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."creator" IS '创建人';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."updater" IS '更新人';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."midoffice_shortcut"."deleted" IS '是否删除';


-- "middle"."midoffice_shortcut_user" definition

-- Drop table

-- DROP TABLE "middle"."midoffice_shortcut_user";

CREATE TABLE "middle"."midoffice_shortcut_user" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"shortcut_id" bigint NOT NULL,
	"creator" character varying(256 byte) NULL,
	"create_time" timestamp(0) without time zone NULL,
	"updater" character varying(256 byte) NULL,
	"update_time" timestamp(0) without time zone NULL,
	"deleted" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219632_83067e5e_F72FB070" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."midoffice_shortcut_user" IS '快捷入口用户关联表';

-- Column comments

COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."id" IS '主键';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."user_id" IS '用户id';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."shortcut_id" IS '快捷入口id';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."creator" IS '创建人';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."updater" IS '更新人';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."midoffice_shortcut_user"."deleted" IS '是否删除';


-- "middle"."midoffice_todo" definition

-- Drop table

-- DROP TABLE "middle"."midoffice_todo";

CREATE TABLE "middle"."midoffice_todo" (
	"id" bigint AUTO_INCREMENT,
	"type" tinyint NOT NULL,
	"urgency_level" character varying(10 char) NULL,
	"title" character varying(255 char) NOT NULL,
	"submitter" character varying(30 char) NOT NULL,
	"remark" character varying(255 char) NULL,
	"submit_time" timestamp without time zone NOT NULL,
	"subsystem_id" integer NOT NULL,
	"link_url" character varying(255 char) NULL,
	"process_id" character varying(64 char) NULL,
	"process_type" character varying(64 char) NULL,
	"process_status" tinyint NULL,
	"task_code" character varying(64 char) NULL,
	"todo_user_id" character varying(30 char) NULL,
	"receipt" boolean NULL DEFAULT 0,
	"start_time" timestamp without time zone NULL,
	"end_time" timestamp without time zone NULL,
	"meeting_address" character varying(200 char) NULL,
	"status" tinyint NOT NULL,
	"creator" bigint NULL,
	"create_time" timestamp without time zone NOT NULL,
	"updater" bigint NULL,
	"update_time" timestamp without time zone NOT NULL,
	"deleted" boolean NOT NULL DEFAULT 0,
	"item" tinyint NULL,
	"todo_id" character varying(50 char) NULL,
	"receiving_source_tenant_id" bigint NULL,
	"permission" character varying(50 char) NULL,
	"people_ineraction_source" character varying(255 char) NULL,
	CONSTRAINT "cons134219699" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."midoffice_todo" IS '待办事项表';

-- Column comments

COMMENT ON COLUMN "middle"."midoffice_todo"."id" IS '主键';
COMMENT ON COLUMN "middle"."midoffice_todo"."type" IS '类型（见枚举）';
COMMENT ON COLUMN "middle"."midoffice_todo"."urgency_level" IS '缓急性（见政务字典）';
COMMENT ON COLUMN "middle"."midoffice_todo"."title" IS '标题';
COMMENT ON COLUMN "middle"."midoffice_todo"."submitter" IS '发起人';
COMMENT ON COLUMN "middle"."midoffice_todo"."remark" IS '备注';
COMMENT ON COLUMN "middle"."midoffice_todo"."submit_time" IS '发起时间';
COMMENT ON COLUMN "middle"."midoffice_todo"."subsystem_id" IS '发起子系统id';
COMMENT ON COLUMN "middle"."midoffice_todo"."link_url" IS '跳转链接';
COMMENT ON COLUMN "middle"."midoffice_todo"."process_id" IS '唯一标识';
COMMENT ON COLUMN "middle"."midoffice_todo"."process_type" IS '流程类型';
COMMENT ON COLUMN "middle"."midoffice_todo"."process_status" IS '流程状态';
COMMENT ON COLUMN "middle"."midoffice_todo"."task_code" IS '任务节点';
COMMENT ON COLUMN "middle"."midoffice_todo"."todo_user_id" IS '待办人';
COMMENT ON COLUMN "middle"."midoffice_todo"."receipt" IS '参会人是否需要回执 0:否 1:是';
COMMENT ON COLUMN "middle"."midoffice_todo"."start_time" IS '会议开始时间';
COMMENT ON COLUMN "middle"."midoffice_todo"."end_time" IS '会议结束时间';
COMMENT ON COLUMN "middle"."midoffice_todo"."meeting_address" IS '会议室地点';
COMMENT ON COLUMN "middle"."midoffice_todo"."status" IS '状态（0=待办，1=已办）';
COMMENT ON COLUMN "middle"."midoffice_todo"."creator" IS '创建人';
COMMENT ON COLUMN "middle"."midoffice_todo"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."midoffice_todo"."updater" IS '更新人';
COMMENT ON COLUMN "middle"."midoffice_todo"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."midoffice_todo"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."midoffice_todo"."item" IS '事项';
COMMENT ON COLUMN "middle"."midoffice_todo"."todo_id" IS '待办id';
COMMENT ON COLUMN "middle"."midoffice_todo"."receiving_source_tenant_id" IS '收文来源机构id';
COMMENT ON COLUMN "middle"."midoffice_todo"."permission" IS '权限标识';
COMMENT ON COLUMN "middle"."midoffice_todo"."people_ineraction_source" IS '政民互动来源';


-- "middle"."system_calendar_wnl" definition

-- Drop table

-- DROP TABLE "middle"."system_calendar_wnl";

CREATE TABLE "middle"."system_calendar_wnl" (
	"gregorian_date" timestamp(6) without time zone NOT NULL,
	"gregorian_year" bigint NULL,
	"gregorian_month" bigint NULL,
	"gregorian_day" bigint NULL,
	"week_day" character varying(200 byte) NULL,
	"lunar_year" bigint NULL,
	"lunar_month" character varying(200 byte) NULL,
	"lunar_day" character varying(200 byte) NULL,
	"zodiac" character varying(200 byte) NULL,
	"leap_month" bigint NULL,
	"year_branch" character varying(200 byte) NULL,
	"month_branch" character varying(200 byte) NULL,
	"day_branch" character varying(200 byte) NULL,
	"solar_term" character varying(200 byte) NULL,
	"solar_term_time" character varying(200 byte) NULL,
	"gregorian_festival" character varying(200 byte) NULL,
	"lunar_festival" character varying(200 byte) NULL,
	"special_festivals" character varying(200 byte) NULL,
	"holiday_type" integer NULL,
	"holiday_name" character varying(800 byte) NULL
);
CREATE INDEX "index190672685193700_35a744ca_E1E128C5" ON middle.system_calendar_wnl USING btree (gregorian_date);
COMMENT ON TABLE "middle"."system_calendar_wnl" IS '万年历';

-- Column comments

COMMENT ON COLUMN "middle"."system_calendar_wnl"."gregorian_date" IS '日期';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."gregorian_year" IS '公历年';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."gregorian_month" IS '公历月';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."gregorian_day" IS '公历日';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."week_day" IS '星期';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."lunar_year" IS '阴历年';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."lunar_month" IS '阴历月';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."lunar_day" IS '阴历日';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."zodiac" IS '生肖';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."leap_month" IS '闰月';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."year_branch" IS '年干支';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."month_branch" IS '月干支';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."day_branch" IS '日干支';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."solar_term" IS '节气';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."solar_term_time" IS '节气时间';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."gregorian_festival" IS '公历节日';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."lunar_festival" IS '农历节日';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."special_festivals" IS '特殊节日';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."holiday_type" IS '节假日类型，enum(0, 1, 2, 3),分别表示 工作日、周末、节日、调休';
COMMENT ON COLUMN "middle"."system_calendar_wnl"."holiday_name" IS '节假日名称';


-- "middle"."system_dept" definition

-- Drop table

-- DROP TABLE "middle"."system_dept";

CREATE TABLE "middle"."system_dept" (
	"id" bigint AUTO_INCREMENT,
	"name" character varying(120 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"parent_id" bigint NOT NULL DEFAULT 0,
	"sort" bigint NOT NULL DEFAULT 0,
	"leader_user_id" bigint NULL,
	"phone" character varying(44 byte) NULL,
	"email" character varying(200 byte) NULL,
	"status" integer NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219759_4b1ecbb0_3D3EB88E" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_dept" IS '组织表';

-- Column comments

COMMENT ON COLUMN "middle"."system_dept"."id" IS '部门id';
COMMENT ON COLUMN "middle"."system_dept"."name" IS '部门名称';
COMMENT ON COLUMN "middle"."system_dept"."parent_id" IS '父部门id';
COMMENT ON COLUMN "middle"."system_dept"."sort" IS '显示顺序';
COMMENT ON COLUMN "middle"."system_dept"."leader_user_id" IS '负责人';
COMMENT ON COLUMN "middle"."system_dept"."phone" IS '联系电话';
COMMENT ON COLUMN "middle"."system_dept"."email" IS '邮箱';
COMMENT ON COLUMN "middle"."system_dept"."status" IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_dept"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_dept"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_dept"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_dept"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_dept"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_dept"."tenant_id" IS '租户编号';


-- "middle"."system_dict_data" definition

-- Drop table

-- DROP TABLE "middle"."system_dict_data";

CREATE TABLE "middle"."system_dict_data" (
	"id" bigint AUTO_INCREMENT,
	"sort" bigint NOT NULL DEFAULT 0,
	"parent_id" bigint NULL,
	"label" character varying(400 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"value" character varying(400 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"dict_type" character varying(400 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"status" integer NOT NULL DEFAULT 0,
	"color_type" character varying(400 byte) NULL DEFAULT NULL::varcharbyte,
	"image_url" character varying(2000 byte) NULL,
	"css_class" character varying(800 byte) NULL DEFAULT NULL::varcharbyte,
	"remark" character varying(2000 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(6) without time zone NOT NULL,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(6) without time zone NOT NULL,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219500_f7fb538_C8FF8F26" PRIMARY KEY (id)
);
CREATE INDEX "value_idx_b93c6c5a_DBB9F72F" ON middle.system_dict_data USING btree (value);
COMMENT ON TABLE "middle"."system_dict_data" IS '字典数据表';

-- Column comments

COMMENT ON COLUMN "middle"."system_dict_data"."id" IS '字典编码';
COMMENT ON COLUMN "middle"."system_dict_data"."sort" IS '字典排序';
COMMENT ON COLUMN "middle"."system_dict_data"."parent_id" IS '父字典id';
COMMENT ON COLUMN "middle"."system_dict_data"."label" IS '字典标签';
COMMENT ON COLUMN "middle"."system_dict_data"."value" IS '字典键值';
COMMENT ON COLUMN "middle"."system_dict_data"."dict_type" IS '字典类型';
COMMENT ON COLUMN "middle"."system_dict_data"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_dict_data"."color_type" IS '颜色类型';
COMMENT ON COLUMN "middle"."system_dict_data"."image_url" IS '图片地址';
COMMENT ON COLUMN "middle"."system_dict_data"."css_class" IS 'css 样式';
COMMENT ON COLUMN "middle"."system_dict_data"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_dict_data"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_dict_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_dict_data"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_dict_data"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_dict_data"."deleted" IS '是否删除';


-- "middle"."system_dict_type" definition

-- Drop table

-- DROP TABLE "middle"."system_dict_type";

CREATE TABLE "middle"."system_dict_type" (
	"id" bigint AUTO_INCREMENT,
	"name" character varying(400 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"type" character varying(400 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"status" integer NOT NULL DEFAULT 0,
	"remark" character varying(2000 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(6) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(6) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219662_41828045_5321D39A" PRIMARY KEY (id),
	CONSTRAINT "dict_type_48069889_3636F890" UNIQUE (type)
);
COMMENT ON TABLE "middle"."system_dict_type" IS '字典类型表';

-- Column comments

COMMENT ON COLUMN "middle"."system_dict_type"."id" IS '字典主键';
COMMENT ON COLUMN "middle"."system_dict_type"."name" IS '字典名称';
COMMENT ON COLUMN "middle"."system_dict_type"."type" IS '字典类型';
COMMENT ON COLUMN "middle"."system_dict_type"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_dict_type"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_dict_type"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_dict_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_dict_type"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_dict_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_dict_type"."deleted" IS '是否删除';


-- "middle"."system_error_code" definition

-- Drop table

-- DROP TABLE "middle"."system_error_code";

CREATE TABLE "middle"."system_error_code" (
	"id" bigint AUTO_INCREMENT,
	"type" integer NOT NULL DEFAULT 0,
	"application_name" character varying(200 byte) NOT NULL,
	"code" bigint NOT NULL DEFAULT 0,
	"message" character varying(2048 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"memo" character varying(2048 byte) NULL DEFAULT NULL::varcharbyte,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219440_7e427da4_B398ED7" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_error_code" IS '错误码表';

-- Column comments

COMMENT ON COLUMN "middle"."system_error_code"."id" IS '错误码编号';
COMMENT ON COLUMN "middle"."system_error_code"."type" IS '错误码类型';
COMMENT ON COLUMN "middle"."system_error_code"."application_name" IS '应用名';
COMMENT ON COLUMN "middle"."system_error_code"."code" IS '错误码编码';
COMMENT ON COLUMN "middle"."system_error_code"."message" IS '错误码错误提示';
COMMENT ON COLUMN "middle"."system_error_code"."memo" IS '备注';
COMMENT ON COLUMN "middle"."system_error_code"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_error_code"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_error_code"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_error_code"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_error_code"."deleted" IS '是否删除';


-- "middle"."system_login_log" definition

-- Drop table

-- DROP TABLE "middle"."system_login_log";

CREATE TABLE "middle"."system_login_log" (
	"id" bigint AUTO_INCREMENT,
	"log_type" bigint NOT NULL,
	"trace_id" character varying(256 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"user_id" bigint NOT NULL DEFAULT 0,
	"user_type" integer NOT NULL DEFAULT 0,
	"username" character varying(200 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"result" integer NOT NULL,
	"user_ip" character varying(200 byte) NOT NULL,
	"user_agent" character varying(2048 byte) NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219441_ba5a824f_288AD457" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_login_log" IS '系统访问记录';

-- Column comments

COMMENT ON COLUMN "middle"."system_login_log"."id" IS '访问ID';
COMMENT ON COLUMN "middle"."system_login_log"."log_type" IS '日志类型';
COMMENT ON COLUMN "middle"."system_login_log"."trace_id" IS '链路追踪编号';
COMMENT ON COLUMN "middle"."system_login_log"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_login_log"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_login_log"."username" IS '用户账号';
COMMENT ON COLUMN "middle"."system_login_log"."result" IS '登陆结果';
COMMENT ON COLUMN "middle"."system_login_log"."user_ip" IS '用户 IP';
COMMENT ON COLUMN "middle"."system_login_log"."user_agent" IS '浏览器 UA';
COMMENT ON COLUMN "middle"."system_login_log"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_login_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_login_log"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_login_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_login_log"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_login_log"."tenant_id" IS '租户编号';


-- "middle"."system_menu" definition

-- Drop table

-- DROP TABLE "middle"."system_menu";

CREATE TABLE "middle"."system_menu" (
	"id" bigint AUTO_INCREMENT,
	"client_id" bigint NULL,
	"name" character varying(200 byte) NOT NULL,
	"permission" character varying(400 byte) NULL DEFAULT NULL::varcharbyte,
	"type" integer NOT NULL,
	"sort" bigint NOT NULL DEFAULT 0,
	"parent_id" bigint NOT NULL DEFAULT 0,
	"path" character varying(800 byte) NULL DEFAULT NULL::varcharbyte,
	"icon" character varying(400 byte) NULL DEFAULT '#'::varcharbyte,
	"component" character varying(1020 byte) NULL,
	"status" integer NOT NULL DEFAULT 0,
	"shortcut" boolean NOT NULL DEFAULT 0,
	"shortcut_icon" character varying(800 byte) NULL,
	"visible" boolean NOT NULL DEFAULT 1,
	"keep_alive" boolean NOT NULL DEFAULT 1,
	"creator" character varying(256 byte) NULL,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0
);


-- "middle"."system_notice" definition

-- Drop table

-- DROP TABLE "middle"."system_notice";

CREATE TABLE "middle"."system_notice" (
	"id" bigint AUTO_INCREMENT,
	"title" character varying(200 byte) NOT NULL,
	"content" text NOT NULL,
	"type" integer NOT NULL,
	"status" integer NOT NULL DEFAULT 0,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219443_7bb0789b_AF1D8897" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_notice" IS '通知公告表';

-- Column comments

COMMENT ON COLUMN "middle"."system_notice"."id" IS '公告ID';
COMMENT ON COLUMN "middle"."system_notice"."title" IS '公告标题';
COMMENT ON COLUMN "middle"."system_notice"."content" IS '公告内容';
COMMENT ON COLUMN "middle"."system_notice"."type" IS '公告类型（1通知 2公告）';
COMMENT ON COLUMN "middle"."system_notice"."status" IS '公告状态（0正常 1关闭）';
COMMENT ON COLUMN "middle"."system_notice"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_notice"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_notice"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_notice"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_notice"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_notice"."tenant_id" IS '租户编号';


-- "middle"."system_oauth2_access_token" definition

-- Drop table

-- DROP TABLE "middle"."system_oauth2_access_token";

CREATE TABLE "middle"."system_oauth2_access_token" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"user_type" integer NOT NULL,
	"access_token" character varying(1020 byte) NOT NULL,
	"refresh_token" character varying(128 byte) NOT NULL,
	"client_id" character varying(1020 byte) NOT NULL,
	"scopes" character varying(1020 byte) NULL,
	"expires_time" timestamp(0) without time zone NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219444_1dbe7b86_576F8E28" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_oauth2_access_token" IS 'OAuth2 访问令牌';

-- Column comments

COMMENT ON COLUMN "middle"."system_oauth2_access_token"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."access_token" IS '访问令牌';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."refresh_token" IS '刷新令牌';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."client_id" IS '客户端编号';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."scopes" IS '授权范围';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."expires_time" IS '过期时间';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_oauth2_access_token"."tenant_id" IS '租户编号';


-- "middle"."system_oauth2_approve" definition

-- Drop table

-- DROP TABLE "middle"."system_oauth2_approve";

CREATE TABLE "middle"."system_oauth2_approve" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"user_type" integer NOT NULL,
	"client_id" character varying(1020 byte) NOT NULL,
	"scope" character varying(1020 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"approved" boolean NOT NULL DEFAULT 0,
	"expires_time" timestamp(0) without time zone NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219445_ce1ed48e_B2DF1EF4" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_oauth2_approve" IS 'OAuth2 批准表';

-- Column comments

COMMENT ON COLUMN "middle"."system_oauth2_approve"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."client_id" IS '客户端编号';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."scope" IS '授权范围';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."approved" IS '是否接受';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."expires_time" IS '过期时间';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_oauth2_approve"."tenant_id" IS '租户编号';


-- "middle"."system_oauth2_client" definition

-- Drop table

-- DROP TABLE "middle"."system_oauth2_client";

CREATE TABLE "middle"."system_oauth2_client" (
	"id" bigint AUTO_INCREMENT,
	"code" character varying(400 byte) NULL,
	"client_id" character varying(1020 byte) NOT NULL,
	"secret" character varying(1020 byte) NOT NULL,
	"name" character varying(1020 byte) NOT NULL,
	"logo" character varying(1020 byte) NOT NULL,
	"path" character varying(800 byte) NULL,
	"description" character varying(1020 byte) NULL,
	"status" integer NOT NULL,
	"access_token_validity_seconds" bigint NOT NULL,
	"refresh_token_validity_seconds" bigint NOT NULL,
	"redirect_uris" character varying(1020 byte) NULL,
	"sort" bigint NULL,
	"authorized_grant_types" character varying(1020 byte) NOT NULL,
	"scopes" character varying(1020 byte) NULL,
	"auto_approve_scopes" character varying(1020 byte) NULL,
	"authorities" character varying(1020 byte) NULL,
	"resource_ids" character varying(1020 byte) NULL,
	"additional_information" character varying(16384 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219679_95741b37_25CA1386" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_oauth2_client" IS 'OAuth2 客户端表';

-- Column comments

COMMENT ON COLUMN "middle"."system_oauth2_client"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_oauth2_client"."code" IS '应用标识';
COMMENT ON COLUMN "middle"."system_oauth2_client"."client_id" IS '客户端编号';
COMMENT ON COLUMN "middle"."system_oauth2_client"."secret" IS '客户端密钥';
COMMENT ON COLUMN "middle"."system_oauth2_client"."name" IS '应用名';
COMMENT ON COLUMN "middle"."system_oauth2_client"."logo" IS '应用图标';
COMMENT ON COLUMN "middle"."system_oauth2_client"."path" IS '应用地址';
COMMENT ON COLUMN "middle"."system_oauth2_client"."description" IS '应用描述';
COMMENT ON COLUMN "middle"."system_oauth2_client"."status" IS '状态';
COMMENT ON COLUMN "middle"."system_oauth2_client"."access_token_validity_seconds" IS '访问令牌的有效期';
COMMENT ON COLUMN "middle"."system_oauth2_client"."refresh_token_validity_seconds" IS '刷新令牌的有效期';
COMMENT ON COLUMN "middle"."system_oauth2_client"."redirect_uris" IS '可重定向的 URI 地址';
COMMENT ON COLUMN "middle"."system_oauth2_client"."sort" IS '显示顺序';
COMMENT ON COLUMN "middle"."system_oauth2_client"."authorized_grant_types" IS '授权类型';
COMMENT ON COLUMN "middle"."system_oauth2_client"."scopes" IS '授权范围';
COMMENT ON COLUMN "middle"."system_oauth2_client"."auto_approve_scopes" IS '自动通过的授权范围';
COMMENT ON COLUMN "middle"."system_oauth2_client"."authorities" IS '权限';
COMMENT ON COLUMN "middle"."system_oauth2_client"."resource_ids" IS '资源';
COMMENT ON COLUMN "middle"."system_oauth2_client"."additional_information" IS '附加信息';
COMMENT ON COLUMN "middle"."system_oauth2_client"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_oauth2_client"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_oauth2_client"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_oauth2_client"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_oauth2_client"."deleted" IS '是否删除';


-- "middle"."system_oauth2_code" definition

-- Drop table

-- DROP TABLE "middle"."system_oauth2_code";

CREATE TABLE "middle"."system_oauth2_code" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"user_type" integer NOT NULL,
	"code" character varying(128 byte) NOT NULL,
	"client_id" character varying(1020 byte) NOT NULL,
	"scopes" character varying(1020 byte) NULL DEFAULT NULL::varcharbyte,
	"expires_time" timestamp(0) without time zone NOT NULL,
	"redirect_uri" character varying(1020 byte) NULL,
	"state" character varying(1020 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219447_668d1f8_9E0E83F0" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_oauth2_code" IS 'OAuth2 授权码表';

-- Column comments

COMMENT ON COLUMN "middle"."system_oauth2_code"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_oauth2_code"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_oauth2_code"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_oauth2_code"."code" IS '授权码';
COMMENT ON COLUMN "middle"."system_oauth2_code"."client_id" IS '客户端编号';
COMMENT ON COLUMN "middle"."system_oauth2_code"."scopes" IS '授权范围';
COMMENT ON COLUMN "middle"."system_oauth2_code"."expires_time" IS '过期时间';
COMMENT ON COLUMN "middle"."system_oauth2_code"."redirect_uri" IS '可重定向的 URI 地址';
COMMENT ON COLUMN "middle"."system_oauth2_code"."state" IS '状态';
COMMENT ON COLUMN "middle"."system_oauth2_code"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_oauth2_code"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_oauth2_code"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_oauth2_code"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_oauth2_code"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_oauth2_code"."tenant_id" IS '租户编号';


-- "middle"."system_oauth2_refresh_token" definition

-- Drop table

-- DROP TABLE "middle"."system_oauth2_refresh_token";

CREATE TABLE "middle"."system_oauth2_refresh_token" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"refresh_token" character varying(128 byte) NOT NULL,
	"user_type" integer NOT NULL,
	"client_id" character varying(1020 byte) NOT NULL,
	"scopes" character varying(1020 byte) NULL,
	"expires_time" timestamp(0) without time zone NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219448_38d44eb9_7FEF3DD3" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_oauth2_refresh_token" IS '刷新令牌';

-- Column comments

COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."refresh_token" IS '刷新令牌';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."client_id" IS '客户端编号';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."scopes" IS '授权范围';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."expires_time" IS '过期时间';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_oauth2_refresh_token"."tenant_id" IS '租户编号';


-- "middle"."system_operate_log" definition

-- Drop table

-- DROP TABLE "middle"."system_operate_log";

CREATE TABLE "middle"."system_operate_log" (
	"id" bigint AUTO_INCREMENT,
	"trace_id" character varying(256 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"user_id" bigint NOT NULL,
	"user_type" integer NOT NULL DEFAULT 0,
	"module" character varying(200 byte) NOT NULL,
	"name" character varying(200 byte) NOT NULL,
	"type" bigint NOT NULL DEFAULT 0,
	"content" character varying(32000 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"exts" character varying(2048 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"request_method" character varying(64 byte) NULL DEFAULT NULL::varcharbyte,
	"request_url" character varying(1020 byte) NULL DEFAULT NULL::varcharbyte,
	"user_ip" character varying(200 byte) NULL,
	"user_agent" character varying(1600 byte) NULL,
	"java_method" character varying(2048 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"java_method_args" character varying(32000 byte) NULL DEFAULT NULL::varcharbyte,
	"start_time" timestamp(0) without time zone NOT NULL,
	"duration" bigint NOT NULL,
	"result_code" bigint NOT NULL DEFAULT 0,
	"result_msg" character varying(2048 byte) NULL DEFAULT NULL::varcharbyte,
	"result_data" character varying(16000 byte) NULL DEFAULT NULL::varcharbyte,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219972_a954e8cd_B80E6BCD" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_operate_log" IS '操作日志记录';

-- Column comments

COMMENT ON COLUMN "middle"."system_operate_log"."id" IS '日志主键';
COMMENT ON COLUMN "middle"."system_operate_log"."trace_id" IS '链路追踪编号';
COMMENT ON COLUMN "middle"."system_operate_log"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_operate_log"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_operate_log"."module" IS '模块标题';
COMMENT ON COLUMN "middle"."system_operate_log"."name" IS '操作名';
COMMENT ON COLUMN "middle"."system_operate_log"."type" IS '操作分类';
COMMENT ON COLUMN "middle"."system_operate_log"."content" IS '操作内容';
COMMENT ON COLUMN "middle"."system_operate_log"."exts" IS '拓展字段';
COMMENT ON COLUMN "middle"."system_operate_log"."request_method" IS '请求方法名';
COMMENT ON COLUMN "middle"."system_operate_log"."request_url" IS '请求地址';
COMMENT ON COLUMN "middle"."system_operate_log"."user_ip" IS '用户 IP';
COMMENT ON COLUMN "middle"."system_operate_log"."user_agent" IS '浏览器 UA';
COMMENT ON COLUMN "middle"."system_operate_log"."java_method" IS 'Java 方法名';
COMMENT ON COLUMN "middle"."system_operate_log"."java_method_args" IS 'Java 方法的参数';
COMMENT ON COLUMN "middle"."system_operate_log"."start_time" IS '操作时间';
COMMENT ON COLUMN "middle"."system_operate_log"."duration" IS '执行时长';
COMMENT ON COLUMN "middle"."system_operate_log"."result_code" IS '结果码';
COMMENT ON COLUMN "middle"."system_operate_log"."result_msg" IS '结果提示';
COMMENT ON COLUMN "middle"."system_operate_log"."result_data" IS '结果数据';
COMMENT ON COLUMN "middle"."system_operate_log"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_operate_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_operate_log"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_operate_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_operate_log"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_operate_log"."tenant_id" IS '租户编号';


-- "middle"."system_post" definition

-- Drop table

-- DROP TABLE "middle"."system_post";

CREATE TABLE "middle"."system_post" (
	"id" bigint AUTO_INCREMENT,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	"code" character varying(256 byte) NOT NULL,
	"name" character varying(200 byte) NOT NULL,
	"sort" bigint NOT NULL,
	"status" integer NOT NULL,
	"remark" character varying(2000 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219668_4a56af28_89BE8920" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_post" IS '岗位信息表';

-- Column comments

COMMENT ON COLUMN "middle"."system_post"."id" IS '岗位ID';
COMMENT ON COLUMN "middle"."system_post"."tenant_id" IS '机构ID';
COMMENT ON COLUMN "middle"."system_post"."code" IS '岗位标识';
COMMENT ON COLUMN "middle"."system_post"."name" IS '岗位名称';
COMMENT ON COLUMN "middle"."system_post"."sort" IS '显示顺序';
COMMENT ON COLUMN "middle"."system_post"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_post"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_post"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_post"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_post"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_post"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_post"."deleted" IS '是否删除';


-- "middle"."system_region" definition

-- Drop table

-- DROP TABLE "middle"."system_region";

CREATE TABLE "middle"."system_region" (
	"id" bigint NOT NULL,
	"code" bigint NULL,
	"name" character varying(128 byte) NULL,
	"parent_id" bigint NULL,
	"level" integer NULL,
	CONSTRAINT "cons134219669_e188ad7d_C56B300E" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_region" IS '医疗系统地区表';

-- Column comments

COMMENT ON COLUMN "middle"."system_region"."id" IS '主键';
COMMENT ON COLUMN "middle"."system_region"."code" IS '行政区划代码';
COMMENT ON COLUMN "middle"."system_region"."name" IS '名称';
COMMENT ON COLUMN "middle"."system_region"."parent_id" IS '上级id';
COMMENT ON COLUMN "middle"."system_region"."level" IS 'level';


-- "middle"."system_role" definition

-- Drop table

-- DROP TABLE "middle"."system_role";

CREATE TABLE "middle"."system_role" (
	"id" bigint AUTO_INCREMENT,
	"name" character varying(200 byte) NOT NULL,
	"code" character varying(400 byte) NOT NULL,
	"sort" bigint NOT NULL,
	"data_scope" integer NOT NULL DEFAULT 1,
	"data_scope_dept_ids" character varying(2000 byte),
	"status" integer NOT NULL,
	"type" integer NOT NULL,
	"remark" character varying(2000 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	"client_id" bigint NULL,
	CONSTRAINT "cons134219670_633c6b95_AE9FE5AF" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_role" IS '角色信息表';

-- Column comments

COMMENT ON COLUMN "middle"."system_role"."id" IS '角色ID';
COMMENT ON COLUMN "middle"."system_role"."name" IS '角色名称';
COMMENT ON COLUMN "middle"."system_role"."code" IS '角色权限字符串';
COMMENT ON COLUMN "middle"."system_role"."sort" IS '显示顺序';
COMMENT ON COLUMN "middle"."system_role"."data_scope" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';
COMMENT ON COLUMN "middle"."system_role"."data_scope_dept_ids" IS '数据范围(指定部门数组)';
COMMENT ON COLUMN "middle"."system_role"."status" IS '角色状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_role"."type" IS '角色类型';
COMMENT ON COLUMN "middle"."system_role"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_role"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_role"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_role"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_role"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_role"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_role"."tenant_id" IS '租户编号';
COMMENT ON COLUMN "middle"."system_role"."client_id" IS '应用id';


-- "middle"."system_role_menu" definition

-- Drop table

-- DROP TABLE "middle"."system_role_menu";

CREATE TABLE "middle"."system_role_menu" (
	"id" bigint AUTO_INCREMENT,
	"role_id" bigint NOT NULL,
	"menu_id" bigint NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219452_ee256949_D166EFD1" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_role_menu" IS '角色和菜单关联表';

-- Column comments

COMMENT ON COLUMN "middle"."system_role_menu"."id" IS '自增编号';
COMMENT ON COLUMN "middle"."system_role_menu"."role_id" IS '角色ID';
COMMENT ON COLUMN "middle"."system_role_menu"."menu_id" IS '菜单ID';
COMMENT ON COLUMN "middle"."system_role_menu"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_role_menu"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_role_menu"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_role_menu"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_role_menu"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_role_menu"."tenant_id" IS '租户编号';


-- "middle"."system_sensitive_word" definition

-- Drop table

-- DROP TABLE "middle"."system_sensitive_word";

CREATE TABLE "middle"."system_sensitive_word" (
	"id" bigint AUTO_INCREMENT,
	"name" character varying(1020 byte) NOT NULL,
	"description" character varying(2048 byte) NULL,
	"tags" character varying(1020 byte) NULL,
	"status" integer NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"c1" boolean NULL DEFAULT 0,
	CONSTRAINT "cons134219453_52267bd5_2B63ECB7" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_sensitive_word" IS '敏感词';

-- Column comments

COMMENT ON COLUMN "middle"."system_sensitive_word"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_sensitive_word"."name" IS '敏感词';
COMMENT ON COLUMN "middle"."system_sensitive_word"."description" IS '描述';
COMMENT ON COLUMN "middle"."system_sensitive_word"."tags" IS '标签数组';
COMMENT ON COLUMN "middle"."system_sensitive_word"."status" IS '状态';
COMMENT ON COLUMN "middle"."system_sensitive_word"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_sensitive_word"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_sensitive_word"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_sensitive_word"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_sensitive_word"."deleted" IS '是否删除';


-- "middle"."system_sms_channel" definition

-- Drop table

-- DROP TABLE "middle"."system_sms_channel";

CREATE TABLE "middle"."system_sms_channel" (
	"id" bigint AUTO_INCREMENT,
	"signature" character varying(48 byte) NOT NULL,
	"code" character varying(252 byte) NOT NULL,
	"status" integer NOT NULL,
	"remark" character varying(1020 byte) NULL,
	"api_key" character varying(512 byte) NOT NULL,
	"api_secret" character varying(512 byte) NULL,
	"callback_url" character varying(1020 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219454_348aede0_60581B83" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_sms_channel" IS '短信渠道';

-- Column comments

COMMENT ON COLUMN "middle"."system_sms_channel"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_sms_channel"."signature" IS '短信签名';
COMMENT ON COLUMN "middle"."system_sms_channel"."code" IS '渠道编码';
COMMENT ON COLUMN "middle"."system_sms_channel"."status" IS '开启状态';
COMMENT ON COLUMN "middle"."system_sms_channel"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_sms_channel"."api_key" IS '短信 API 的账号';
COMMENT ON COLUMN "middle"."system_sms_channel"."api_secret" IS '短信 API 的秘钥';
COMMENT ON COLUMN "middle"."system_sms_channel"."callback_url" IS '短信发送回调 URL';
COMMENT ON COLUMN "middle"."system_sms_channel"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_sms_channel"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_sms_channel"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_sms_channel"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_sms_channel"."deleted" IS '是否删除';


-- "middle"."system_sms_code" definition

-- Drop table

-- DROP TABLE "middle"."system_sms_code";

CREATE TABLE "middle"."system_sms_code" (
	"id" bigint AUTO_INCREMENT,
	"mobile" character varying(44 byte) NOT NULL,
	"code" character varying(24 byte) NOT NULL,
	"create_ip" character varying(60 byte) NOT NULL,
	"scene" integer NOT NULL,
	"today_index" integer NOT NULL,
	"used" integer NOT NULL,
	"used_time" timestamp(0) without time zone NULL,
	"used_ip" character varying(1020 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219455_10176bd7_E14D947C" PRIMARY KEY (id)
);
CREATE INDEX "idx_mobile_447ad246_5CE23382" ON middle.system_sms_code USING btree (mobile);
COMMENT ON TABLE "middle"."system_sms_code" IS '手机验证码';

-- Column comments

COMMENT ON COLUMN "middle"."system_sms_code"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_sms_code"."mobile" IS '手机号';
COMMENT ON COLUMN "middle"."system_sms_code"."code" IS '验证码';
COMMENT ON COLUMN "middle"."system_sms_code"."create_ip" IS '创建 IP';
COMMENT ON COLUMN "middle"."system_sms_code"."scene" IS '发送场景';
COMMENT ON COLUMN "middle"."system_sms_code"."today_index" IS '今日发送的第几条';
COMMENT ON COLUMN "middle"."system_sms_code"."used" IS '是否使用';
COMMENT ON COLUMN "middle"."system_sms_code"."used_time" IS '使用时间';
COMMENT ON COLUMN "middle"."system_sms_code"."used_ip" IS '使用 IP';
COMMENT ON COLUMN "middle"."system_sms_code"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_sms_code"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_sms_code"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_sms_code"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_sms_code"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_sms_code"."tenant_id" IS '租户编号';


-- "middle"."system_sms_log" definition

-- Drop table

-- DROP TABLE "middle"."system_sms_log";

CREATE TABLE "middle"."system_sms_log" (
	"id" bigint AUTO_INCREMENT,
	"channel_id" bigint NULL,
	"channel_code" character varying(252 byte) NULL,
	"template_id" bigint NULL,
	"template_code" character varying(252 byte) NULL,
	"template_type" integer NULL,
	"template_content" character varying(1020 byte) NULL,
	"template_params" character varying(1020 byte) NULL,
	"api_template_id" character varying(252 byte) NULL,
	"mobile" character varying(44 byte) NOT NULL,
	"user_id" bigint NULL,
	"user_type" integer NULL,
	"send_status" integer NOT NULL DEFAULT 0,
	"send_time" timestamp(0) without time zone NULL,
	"send_code" bigint NULL,
	"send_msg" character varying(1020 byte) NULL,
	"api_send_code" character varying(252 byte) NULL,
	"api_send_msg" character varying(1020 byte) NULL,
	"api_request_id" character varying(1020 byte) NULL,
	"api_serial_no" character varying(1020 byte) NULL,
	"receive_status" integer NOT NULL DEFAULT 0,
	"receive_time" timestamp(0) without time zone NULL,
	"api_receive_code" character varying(252 byte) NULL,
	"api_receive_msg" character varying(1020 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219456_181c9863_C8AC5621" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_sms_log" IS '短信日志';

-- Column comments

COMMENT ON COLUMN "middle"."system_sms_log"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_sms_log"."channel_id" IS '短信渠道编号';
COMMENT ON COLUMN "middle"."system_sms_log"."channel_code" IS '短信渠道编码';
COMMENT ON COLUMN "middle"."system_sms_log"."template_id" IS '模板编号';
COMMENT ON COLUMN "middle"."system_sms_log"."template_code" IS '模板编码';
COMMENT ON COLUMN "middle"."system_sms_log"."template_type" IS '短信类型';
COMMENT ON COLUMN "middle"."system_sms_log"."template_content" IS '短信内容';
COMMENT ON COLUMN "middle"."system_sms_log"."template_params" IS '短信参数';
COMMENT ON COLUMN "middle"."system_sms_log"."api_template_id" IS '短信 API 的模板编号';
COMMENT ON COLUMN "middle"."system_sms_log"."mobile" IS '手机号';
COMMENT ON COLUMN "middle"."system_sms_log"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_sms_log"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_sms_log"."send_status" IS '发送状态';
COMMENT ON COLUMN "middle"."system_sms_log"."send_time" IS '发送时间';
COMMENT ON COLUMN "middle"."system_sms_log"."send_code" IS '发送结果的编码';
COMMENT ON COLUMN "middle"."system_sms_log"."send_msg" IS '发送结果的提示';
COMMENT ON COLUMN "middle"."system_sms_log"."api_send_code" IS '短信 API 发送结果的编码';
COMMENT ON COLUMN "middle"."system_sms_log"."api_send_msg" IS '短信 API 发送失败的提示';
COMMENT ON COLUMN "middle"."system_sms_log"."api_request_id" IS '短信 API 发送返回的唯一请求 ID';
COMMENT ON COLUMN "middle"."system_sms_log"."api_serial_no" IS '短信 API 发送返回的序号';
COMMENT ON COLUMN "middle"."system_sms_log"."receive_status" IS '接收状态';
COMMENT ON COLUMN "middle"."system_sms_log"."receive_time" IS '接收时间';
COMMENT ON COLUMN "middle"."system_sms_log"."api_receive_code" IS 'API 接收结果的编码';
COMMENT ON COLUMN "middle"."system_sms_log"."api_receive_msg" IS 'API 接收结果的说明';
COMMENT ON COLUMN "middle"."system_sms_log"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_sms_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_sms_log"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_sms_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_sms_log"."deleted" IS '是否删除';


-- "middle"."system_sms_template" definition

-- Drop table

-- DROP TABLE "middle"."system_sms_template";

CREATE TABLE "middle"."system_sms_template" (
	"id" bigint AUTO_INCREMENT,
	"type" integer NOT NULL,
	"status" integer NOT NULL,
	"code" character varying(252 byte) NOT NULL,
	"name" character varying(252 byte) NOT NULL,
	"content" character varying(1020 byte) NOT NULL,
	"params" character varying(1020 byte) NOT NULL,
	"remark" character varying(1020 byte) NULL,
	"api_template_id" character varying(252 byte) NOT NULL,
	"channel_id" bigint NOT NULL,
	"channel_code" character varying(252 byte) NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219457_aec6eda6_511786F4" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_sms_template" IS '短信模板';

-- Column comments

COMMENT ON COLUMN "middle"."system_sms_template"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_sms_template"."type" IS '短信签名';
COMMENT ON COLUMN "middle"."system_sms_template"."status" IS '开启状态';
COMMENT ON COLUMN "middle"."system_sms_template"."code" IS '模板编码';
COMMENT ON COLUMN "middle"."system_sms_template"."name" IS '模板名称';
COMMENT ON COLUMN "middle"."system_sms_template"."content" IS '模板内容';
COMMENT ON COLUMN "middle"."system_sms_template"."params" IS '参数数组';
COMMENT ON COLUMN "middle"."system_sms_template"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_sms_template"."api_template_id" IS '短信 API 的模板编号';
COMMENT ON COLUMN "middle"."system_sms_template"."channel_id" IS '短信渠道编号';
COMMENT ON COLUMN "middle"."system_sms_template"."channel_code" IS '短信渠道编码';
COMMENT ON COLUMN "middle"."system_sms_template"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_sms_template"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_sms_template"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_sms_template"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_sms_template"."deleted" IS '是否删除';


-- "middle"."system_social_user" definition

-- Drop table

-- DROP TABLE "middle"."system_social_user";

CREATE TABLE "middle"."system_social_user" (
	"id" bigint AUTO_INCREMENT,
	"type" integer NOT NULL,
	"openid" character varying(128 byte) NOT NULL,
	"token" character varying(1024 byte) NULL,
	"raw_token_info" character varying(4096 byte) NOT NULL,
	"nickname" character varying(128 byte) NULL,
	"avatar" character varying(1020 byte) NULL,
	"raw_user_info" character varying(4096 byte) NOT NULL,
	"code" character varying(1024 byte) NOT NULL,
	"state" character varying(1024 byte) NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219458_bfca8f64_F83C7039" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_social_user" IS '社交用户表';

-- Column comments

COMMENT ON COLUMN "middle"."system_social_user"."id" IS '主键(自增策略)';
COMMENT ON COLUMN "middle"."system_social_user"."type" IS '社交平台的类型';
COMMENT ON COLUMN "middle"."system_social_user"."openid" IS '社交 openid';
COMMENT ON COLUMN "middle"."system_social_user"."token" IS '社交 token';
COMMENT ON COLUMN "middle"."system_social_user"."raw_token_info" IS '原始 Token 数据，一般是 JSON 格式';
COMMENT ON COLUMN "middle"."system_social_user"."nickname" IS '用户昵称';
COMMENT ON COLUMN "middle"."system_social_user"."avatar" IS '用户头像';
COMMENT ON COLUMN "middle"."system_social_user"."raw_user_info" IS '原始用户数据，一般是 JSON 格式';
COMMENT ON COLUMN "middle"."system_social_user"."code" IS '最后一次的认证 code';
COMMENT ON COLUMN "middle"."system_social_user"."state" IS '最后一次的认证 state';
COMMENT ON COLUMN "middle"."system_social_user"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_social_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_social_user"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_social_user"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_social_user"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_social_user"."tenant_id" IS '租户编号';


-- "middle"."system_social_user_bind" definition

-- Drop table

-- DROP TABLE "middle"."system_social_user_bind";

CREATE TABLE "middle"."system_social_user_bind" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"user_type" integer NOT NULL,
	"social_type" integer NOT NULL,
	"social_user_id" bigint NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219459_da1b0f87_F8ABC1AB" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_social_user_bind" IS '社交绑定表';

-- Column comments

COMMENT ON COLUMN "middle"."system_social_user_bind"."id" IS '主键(自增策略)';
COMMENT ON COLUMN "middle"."system_social_user_bind"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_social_user_bind"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_social_user_bind"."social_type" IS '社交平台的类型';
COMMENT ON COLUMN "middle"."system_social_user_bind"."social_user_id" IS '社交用户的编号';
COMMENT ON COLUMN "middle"."system_social_user_bind"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_social_user_bind"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_social_user_bind"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_social_user_bind"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_social_user_bind"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_social_user_bind"."tenant_id" IS '租户编号';


-- "middle"."system_tenant" definition

-- Drop table

-- DROP TABLE "middle"."system_tenant";

CREATE TABLE "middle"."system_tenant" (
	"id" bigint AUTO_INCREMENT,
	"company_type" integer NULL,
	"name" character varying(120 byte) NOT NULL,
	"unified_social_credit_code" character varying(400 byte) NULL,
	"register_region" character varying(400 byte) NULL,
	"register_address" character varying(800 byte) NULL,
	"location_region" character varying(400 byte) NULL,
	"location_address" character varying(400 byte) NULL,
	"legal_representative_name" character varying(400 byte) NULL,
	"legal_representative_id_card" character varying(400 byte) NULL,
	"inst_user_type" character varying(400 byte) NULL,
	"business_license_url" character varying(1200 byte) NULL,
	"contact_user_id" bigint NULL,
	"contact_nickname" character varying(120 byte) NULL,
	"contact_name" character varying(120 byte) NOT NULL,
	"contact_mobile" character varying(2000 byte) NULL,
	"status" integer NOT NULL DEFAULT 0,
	"domains" character varying(1024 byte) NULL DEFAULT NULL::varcharbyte,
	"package_id" bigint NULL,
	"expire_time" timestamp(0) without time zone NOT NULL,
	"account_count" bigint NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"registry_status" integer NOT NULL DEFAULT 0,
	"tenant_type" integer NULL,
	"tenant_level" integer NULL,
	"level_range" character varying(800 byte) NULL,
	"guide_tenant_id" bigint NULL,
	CONSTRAINT "cons134219760_ce3e6e6b_B3D14593" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_tenant" IS '机构表';

-- Column comments

COMMENT ON COLUMN "middle"."system_tenant"."id" IS '租户编号';
COMMENT ON COLUMN "middle"."system_tenant"."company_type" IS '单位类型';
COMMENT ON COLUMN "middle"."system_tenant"."name" IS '机构全称（原租户名）';
COMMENT ON COLUMN "middle"."system_tenant"."unified_social_credit_code" IS '统一社会信用代码';
COMMENT ON COLUMN "middle"."system_tenant"."register_region" IS '注册地区划';
COMMENT ON COLUMN "middle"."system_tenant"."register_address" IS '注册地地址';
COMMENT ON COLUMN "middle"."system_tenant"."location_region" IS '所在地区划';
COMMENT ON COLUMN "middle"."system_tenant"."location_address" IS '所在地地址';
COMMENT ON COLUMN "middle"."system_tenant"."legal_representative_name" IS '法定代表人姓名';
COMMENT ON COLUMN "middle"."system_tenant"."legal_representative_id_card" IS '法人代表身份证号码';
COMMENT ON COLUMN "middle"."system_tenant"."inst_user_type" IS '机构用户类型';
COMMENT ON COLUMN "middle"."system_tenant"."business_license_url" IS '营业执照';
COMMENT ON COLUMN "middle"."system_tenant"."contact_user_id" IS '联系人的用户编号';
COMMENT ON COLUMN "middle"."system_tenant"."contact_nickname" IS '机构管理员姓名';
COMMENT ON COLUMN "middle"."system_tenant"."contact_name" IS '用户名（原联系人）';
COMMENT ON COLUMN "middle"."system_tenant"."contact_mobile" IS '机构管理员手机号码（联系手机）';
COMMENT ON COLUMN "middle"."system_tenant"."status" IS '租户状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_tenant"."domains" IS '绑定域名';
COMMENT ON COLUMN "middle"."system_tenant"."package_id" IS '租户套餐编号';
COMMENT ON COLUMN "middle"."system_tenant"."expire_time" IS '过期时间';
COMMENT ON COLUMN "middle"."system_tenant"."account_count" IS '账号数量';
COMMENT ON COLUMN "middle"."system_tenant"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_tenant"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_tenant"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_tenant"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_tenant"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_tenant"."registry_status" IS '注册状态';
COMMENT ON COLUMN "middle"."system_tenant"."tenant_type" IS '机构类型';
COMMENT ON COLUMN "middle"."system_tenant"."tenant_level" IS '机构级别';
COMMENT ON COLUMN "middle"."system_tenant"."level_range" IS '范围';
COMMENT ON COLUMN "middle"."system_tenant"."guide_tenant_id" IS '业务指导单位';


-- "middle"."system_tenant_approval" definition

-- Drop table

-- DROP TABLE "middle"."system_tenant_approval";

CREATE TABLE "middle"."system_tenant_approval" (
	"id" bigint AUTO_INCREMENT,
	"linked_tenant_id" bigint NULL,
	"company_type" integer NULL,
	"name" character varying(120 byte) NOT NULL,
	"unified_social_credit_code" character varying(400 byte) NULL,
	"register_region" character varying(400 byte) NULL,
	"register_address" character varying(800 byte) NULL,
	"location_region" character varying(400 byte) NULL,
	"location_address" character varying(400 byte) NULL,
	"legal_representative_name" character varying(400 byte) NULL,
	"legal_representative_id_card" character varying(400 byte) NULL,
	"inst_user_type" character varying(400 byte) NULL,
	"business_license_url" character varying(1200 byte) NULL,
	"contact_user_id" bigint NULL,
	"contact_nickname" character varying(120 byte) NULL,
	"contact_name" character varying(120 byte) NOT NULL,
	"contact_mobile" character varying(2000 byte) NULL,
	"tenant_status" integer NOT NULL DEFAULT 0,
	"domains" character varying(1024 byte) NULL DEFAULT NULL::varcharbyte,
	"package_id" bigint NULL,
	"expire_time" timestamp(0) without time zone NULL,
	"account_count" bigint NULL,
	"creator" character varying(256 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"approval_type" integer NULL,
	"change_content" character varying(1600 byte) NULL,
	"applicant_id" bigint NULL,
	"approval_time" timestamp(0) without time zone NULL DEFAULT CURRENT_TIMESTAMP,
	"approver_id" bigint NULL,
	"approval_status" integer NULL,
	"examine_time" timestamp(0) without time zone NULL,
	"remark" character varying(1600 byte) NULL,
	"tenant_type" integer NOT NULL,
	"tenant_level" integer NULL,
	"level_range" character varying(512 byte) NULL,
	"guide_tenant_id" bigint NULL,
	CONSTRAINT "cons134219461_69fee787_5F67D4FB" PRIMARY KEY (id, tenant_type)
);
COMMENT ON TABLE "middle"."system_tenant_approval" IS '机构认证审批表';

-- Column comments

COMMENT ON COLUMN "middle"."system_tenant_approval"."id" IS '审批申请id';
COMMENT ON COLUMN "middle"."system_tenant_approval"."linked_tenant_id" IS '租户id';
COMMENT ON COLUMN "middle"."system_tenant_approval"."company_type" IS '单位类型';
COMMENT ON COLUMN "middle"."system_tenant_approval"."name" IS '机构全称（原租户名）';
COMMENT ON COLUMN "middle"."system_tenant_approval"."unified_social_credit_code" IS '统一社会信用代码';
COMMENT ON COLUMN "middle"."system_tenant_approval"."register_region" IS '注册地区划';
COMMENT ON COLUMN "middle"."system_tenant_approval"."register_address" IS '注册地地址';
COMMENT ON COLUMN "middle"."system_tenant_approval"."location_region" IS '所在地区划';
COMMENT ON COLUMN "middle"."system_tenant_approval"."location_address" IS '所在地地址';
COMMENT ON COLUMN "middle"."system_tenant_approval"."legal_representative_name" IS '法定代表人姓名';
COMMENT ON COLUMN "middle"."system_tenant_approval"."legal_representative_id_card" IS '法人代表身份证号码';
COMMENT ON COLUMN "middle"."system_tenant_approval"."inst_user_type" IS '机构用户类型';
COMMENT ON COLUMN "middle"."system_tenant_approval"."business_license_url" IS '营业执照';
COMMENT ON COLUMN "middle"."system_tenant_approval"."contact_user_id" IS '联系人的用户编号';
COMMENT ON COLUMN "middle"."system_tenant_approval"."contact_nickname" IS '机构管理员姓名';
COMMENT ON COLUMN "middle"."system_tenant_approval"."contact_name" IS '用户名（原联系人）';
COMMENT ON COLUMN "middle"."system_tenant_approval"."contact_mobile" IS '机构管理员手机号码（联系手机）';
COMMENT ON COLUMN "middle"."system_tenant_approval"."tenant_status" IS '租户状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_tenant_approval"."domains" IS '绑定域名';
COMMENT ON COLUMN "middle"."system_tenant_approval"."package_id" IS '租户套餐编号';
COMMENT ON COLUMN "middle"."system_tenant_approval"."expire_time" IS '过期时间';
COMMENT ON COLUMN "middle"."system_tenant_approval"."account_count" IS '账号数量';
COMMENT ON COLUMN "middle"."system_tenant_approval"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_tenant_approval"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_tenant_approval"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_tenant_approval"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_tenant_approval"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_tenant_approval"."approval_type" IS '认证类型（0注册，1变更）';
COMMENT ON COLUMN "middle"."system_tenant_approval"."change_content" IS '变更内容';
COMMENT ON COLUMN "middle"."system_tenant_approval"."applicant_id" IS '申请人用户编号';
COMMENT ON COLUMN "middle"."system_tenant_approval"."approval_time" IS '申请时间';
COMMENT ON COLUMN "middle"."system_tenant_approval"."approver_id" IS '审批人用户编号';
COMMENT ON COLUMN "middle"."system_tenant_approval"."approval_status" IS '审批状态（0待审批，1审批通过，2审批未通过）';
COMMENT ON COLUMN "middle"."system_tenant_approval"."examine_time" IS '审批时间';
COMMENT ON COLUMN "middle"."system_tenant_approval"."remark" IS '审核备注';
COMMENT ON COLUMN "middle"."system_tenant_approval"."tenant_type" IS '机构类型';
COMMENT ON COLUMN "middle"."system_tenant_approval"."tenant_level" IS '机构级别';
COMMENT ON COLUMN "middle"."system_tenant_approval"."level_range" IS '范围';
COMMENT ON COLUMN "middle"."system_tenant_approval"."guide_tenant_id" IS '业务指导单位';


-- "middle"."system_tenant_package" definition

-- Drop table

-- DROP TABLE "middle"."system_tenant_package";

CREATE TABLE "middle"."system_tenant_package" (
	"id" bigint AUTO_INCREMENT,
	"name" character varying(120 byte) NOT NULL,
	"status" integer NOT NULL DEFAULT 0,
	"remark" character varying(1024 byte) NULL DEFAULT NULL::varcharbyte,
	"menu_ids" character varying(8192 byte) NOT NULL,
	"creator" character varying(256 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219462_2a5289d1_3A97198" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_tenant_package" IS '租户套餐表';

-- Column comments

COMMENT ON COLUMN "middle"."system_tenant_package"."id" IS '套餐编号';
COMMENT ON COLUMN "middle"."system_tenant_package"."name" IS '套餐名';
COMMENT ON COLUMN "middle"."system_tenant_package"."status" IS '租户状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_tenant_package"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_tenant_package"."menu_ids" IS '关联的菜单编号';
COMMENT ON COLUMN "middle"."system_tenant_package"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_tenant_package"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_tenant_package"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_tenant_package"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_tenant_package"."deleted" IS '是否删除';


-- "middle"."system_tenant_type" definition

-- Drop table

-- DROP TABLE "middle"."system_tenant_type";

CREATE TABLE "middle"."system_tenant_type" (
	"id" bigint AUTO_INCREMENT,
	"name" character varying(400 byte) NOT NULL,
	"open_registry" boolean NOT NULL,
	"status" boolean NOT NULL,
	"remark" character varying(400 byte) NULL,
	"creator" character varying(256 byte) NOT NULL,
	"create_time" timestamp(0) without time zone NOT NULL,
	"updater" character varying(256 byte) NULL,
	"update_time" timestamp(0) without time zone NOT NULL,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219463_a1b9fc50_BE27935E" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_tenant_type" IS '机构用户类型表';

-- Column comments

COMMENT ON COLUMN "middle"."system_tenant_type"."id" IS '主键';
COMMENT ON COLUMN "middle"."system_tenant_type"."name" IS '机构用户类型名称';
COMMENT ON COLUMN "middle"."system_tenant_type"."open_registry" IS '是否开放注册';
COMMENT ON COLUMN "middle"."system_tenant_type"."status" IS '状态';
COMMENT ON COLUMN "middle"."system_tenant_type"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_tenant_type"."creator" IS '创建人';
COMMENT ON COLUMN "middle"."system_tenant_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_tenant_type"."updater" IS '更新人';
COMMENT ON COLUMN "middle"."system_tenant_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_tenant_type"."deleted" IS '是否删除';


-- "middle"."system_tenant_type_role" definition

-- Drop table

-- DROP TABLE "middle"."system_tenant_type_role";

CREATE TABLE "middle"."system_tenant_type_role" (
	"id" bigint AUTO_INCREMENT,
	"tenant_type_id" bigint NOT NULL,
	"role_id" character varying(400 byte) NOT NULL,
	"creator" character varying(256 byte) NOT NULL,
	"create_time" timestamp(0) without time zone NOT NULL,
	"updater" character varying(256 byte) NULL,
	"update_time" timestamp(0) without time zone NOT NULL,
	"deleted" boolean NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219464_5af91d88_225A8AF9" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_tenant_type_role" IS '机构用户类型角色绑定表';

-- Column comments

COMMENT ON COLUMN "middle"."system_tenant_type_role"."id" IS '主键';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."tenant_type_id" IS '机构用户类型id';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."role_id" IS '角色id';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."creator" IS '创建人';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."updater" IS '更新人';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_tenant_type_role"."deleted" IS '是否删除';


-- "middle"."system_user_post" definition

-- Drop table

-- DROP TABLE "middle"."system_user_post";

CREATE TABLE "middle"."system_user_post" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL DEFAULT 0,
	"post_id" bigint NOT NULL DEFAULT 0,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219671_985d9614_F1150FA9" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_user_post" IS '用户岗位表';

-- Column comments

COMMENT ON COLUMN "middle"."system_user_post"."id" IS 'id';
COMMENT ON COLUMN "middle"."system_user_post"."user_id" IS '用户ID';
COMMENT ON COLUMN "middle"."system_user_post"."post_id" IS '岗位ID';
COMMENT ON COLUMN "middle"."system_user_post"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_user_post"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_user_post"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_user_post"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_user_post"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_user_post"."tenant_id" IS '租户编号';


-- "middle"."system_user_role" definition

-- Drop table

-- DROP TABLE "middle"."system_user_role";

CREATE TABLE "middle"."system_user_role" (
	"id" bigint AUTO_INCREMENT,
	"user_id" bigint NOT NULL,
	"role_id" bigint NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219672_f361c86b_3166A8ED" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_user_role" IS '用户和角色关联表';

-- Column comments

COMMENT ON COLUMN "middle"."system_user_role"."id" IS '自增编号';
COMMENT ON COLUMN "middle"."system_user_role"."user_id" IS '用户ID';
COMMENT ON COLUMN "middle"."system_user_role"."role_id" IS '角色ID';
COMMENT ON COLUMN "middle"."system_user_role"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_user_role"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_user_role"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_user_role"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_user_role"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_user_role"."tenant_id" IS '租户编号';


-- "middle"."system_user_session" definition

-- Drop table

-- DROP TABLE "middle"."system_user_session";

CREATE TABLE "middle"."system_user_session" (
	"id" bigint AUTO_INCREMENT,
	"token" character varying(128 byte) NOT NULL,
	"user_id" bigint NOT NULL,
	"user_type" integer NOT NULL DEFAULT 0,
	"session_timeout" timestamp(0) without time zone NOT NULL,
	"username" character varying(120 byte) NOT NULL,
	"user_ip" character varying(200 byte) NOT NULL,
	"user_agent" character varying(2048 byte) NOT NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL DEFAULT 0,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	CONSTRAINT "cons134219467_db47c287_DCD24CB7" PRIMARY KEY (id)
);
COMMENT ON TABLE "middle"."system_user_session" IS '用户在线 Session';

-- Column comments

COMMENT ON COLUMN "middle"."system_user_session"."id" IS '编号';
COMMENT ON COLUMN "middle"."system_user_session"."token" IS '会话编号';
COMMENT ON COLUMN "middle"."system_user_session"."user_id" IS '用户编号';
COMMENT ON COLUMN "middle"."system_user_session"."user_type" IS '用户类型';
COMMENT ON COLUMN "middle"."system_user_session"."session_timeout" IS '会话超时时间';
COMMENT ON COLUMN "middle"."system_user_session"."username" IS '用户账号';
COMMENT ON COLUMN "middle"."system_user_session"."user_ip" IS '用户 IP';
COMMENT ON COLUMN "middle"."system_user_session"."user_agent" IS '浏览器 UA';
COMMENT ON COLUMN "middle"."system_user_session"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_user_session"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_user_session"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_user_session"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_user_session"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_user_session"."tenant_id" IS '租户编号';


-- "middle"."system_users" definition

-- Drop table

-- DROP TABLE "middle"."system_users";

CREATE TABLE "middle"."system_users" (
	"id" bigint AUTO_INCREMENT,
	"username" character varying(120 byte) NOT NULL,
	"password" character varying(400 byte) NOT NULL DEFAULT NULL::varcharbyte,
	"nickname" character varying(120 byte) NOT NULL,
	"remark" character varying(2000 byte) NULL,
	"dept_id" bigint NULL,
	"post_ids" character varying(1020 byte) NULL,
	"email" character varying(200 byte) NULL DEFAULT NULL::varcharbyte,
	"mobile" character varying(44 byte) NULL DEFAULT NULL::varcharbyte,
	"sex" bigint NULL,
	"avatar" character varying(800 byte) NULL DEFAULT NULL::varcharbyte,
	"status" integer NOT NULL DEFAULT 0,
	"login_ip" character varying(200 byte) NULL DEFAULT NULL::varcharbyte,
	"login_date" timestamp(0) without time zone NULL,
	"creator" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"create_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"updater" character varying(256 byte) NULL DEFAULT NULL::varcharbyte,
	"update_time" timestamp(0) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"deleted" boolean NOT NULL,
	"tenant_id" bigint NOT NULL DEFAULT 0,
	"is_real_name_authentication" boolean NOT NULL DEFAULT 0,
	"password_update_time" timestamp(6) without time zone NULL DEFAULT CURRENT_TIMESTAMP,
	"sort" bigint NULL,
	"app_cid" character varying(800 byte) NULL,
	CONSTRAINT "cons134219680_c0472286_5A3D32D7" PRIMARY KEY (id),
	CONSTRAINT "cons134219682_20d10fe5_47E1DC55" CHECK ((sex >= 0)) ENABLE NOVALIDATE,
	CONSTRAINT "cons134219682_20d10fe5_47E1DC55_EB4FDC68" CHECK ((sex >= 0)) ENABLE NOVALIDATE,
	CONSTRAINT "idx_username_afe2a9f8_3E072B27" UNIQUE (username, update_time, tenant_id)
);
COMMENT ON TABLE "middle"."system_users" IS '用户信息表';

-- Column comments

COMMENT ON COLUMN "middle"."system_users"."id" IS '用户ID';
COMMENT ON COLUMN "middle"."system_users"."username" IS '用户账号';
COMMENT ON COLUMN "middle"."system_users"."password" IS '密码';
COMMENT ON COLUMN "middle"."system_users"."nickname" IS '用户昵称';
COMMENT ON COLUMN "middle"."system_users"."remark" IS '备注';
COMMENT ON COLUMN "middle"."system_users"."dept_id" IS '部门ID';
COMMENT ON COLUMN "middle"."system_users"."post_ids" IS '岗位编号数组';
COMMENT ON COLUMN "middle"."system_users"."email" IS '用户邮箱';
COMMENT ON COLUMN "middle"."system_users"."mobile" IS '手机号码';
COMMENT ON COLUMN "middle"."system_users"."sex" IS '用户性别';
COMMENT ON COLUMN "middle"."system_users"."avatar" IS '头像地址';
COMMENT ON COLUMN "middle"."system_users"."status" IS '帐号状态（0正常 1停用）';
COMMENT ON COLUMN "middle"."system_users"."login_ip" IS '最后登录IP';
COMMENT ON COLUMN "middle"."system_users"."login_date" IS '最后登录时间';
COMMENT ON COLUMN "middle"."system_users"."creator" IS '创建者';
COMMENT ON COLUMN "middle"."system_users"."create_time" IS '创建时间';
COMMENT ON COLUMN "middle"."system_users"."updater" IS '更新者';
COMMENT ON COLUMN "middle"."system_users"."update_time" IS '更新时间';
COMMENT ON COLUMN "middle"."system_users"."deleted" IS '是否删除';
COMMENT ON COLUMN "middle"."system_users"."tenant_id" IS '租户编号';
COMMENT ON COLUMN "middle"."system_users"."is_real_name_authentication" IS '是否实名认证';
COMMENT ON COLUMN "middle"."system_users"."password_update_time" IS '密码更新时间';
COMMENT ON COLUMN "middle"."system_users"."sort" IS '显示顺序';
COMMENT ON COLUMN "middle"."system_users"."app_cid" IS 'app设备绑定码';
