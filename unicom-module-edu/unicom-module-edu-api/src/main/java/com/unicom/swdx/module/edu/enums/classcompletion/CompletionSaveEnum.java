package com.unicom.swdx.module.edu.enums.classcompletion;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 模板是否暂存
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum CompletionSaveEnum {

    /**
     * 暂存
     */
    TEMPORARY(0, "是"),

    /**
     * 使用
     */
    NOT_TEMPORARY(1, "否");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByPeriod(Integer period) {
        for (CompletionSaveEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getPeriodByDesc(String desc) {
        for (CompletionSaveEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
