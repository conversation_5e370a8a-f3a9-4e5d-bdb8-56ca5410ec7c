package com.unicom.swdx.module.edu.enums.teacherinformation;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 师资相关的一些字典的type
 * @date 2024-10-11
 */
public enum TeacherInformationSourceEnum {

    // 校内
    INNER(0, "校内"),
    // 校外
    OUTSIDE(1, "校外");

    private final Integer type;

    private final String desc;

    TeacherInformationSourceEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static List<Integer> getTypeList() {
        List<Integer> list = new ArrayList<>();
        for (TeacherInformationSourceEnum item : TeacherInformationSourceEnum.values()) {
            list.add(item.getType());
        }
        return list;
    }

    /**
     * 根据类型获取描述
     *
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(String type) {
        for (TeacherInformationSourceEnum item : TeacherInformationSourceEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     *
     * @param desc 描述
     * @return 类型值
     */
    public static Integer getTypeByDesc(String desc) {
        for (TeacherInformationSourceEnum item : TeacherInformationSourceEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
