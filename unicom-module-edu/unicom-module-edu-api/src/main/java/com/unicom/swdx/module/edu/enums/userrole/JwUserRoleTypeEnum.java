package com.unicom.swdx.module.edu.enums.userrole;


import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 教务这边的用户角色类型枚举 - 教师、学员、调训管理员
 * @date 2025-01-15
 */
@Getter
public enum JwUserRoleTypeEnum {

    /**
     * 用户角色：教师
     */
    TEACHER(0, "教师"),

    /**
     * 用户角色：学员
     */
    TRAINEE(1, "学员"),

    /**
     * 用户角色：调训单位管理员
     */
    UNIT_ADMIN(2, "调训单位管理员"),

    /**
     * 用户角色：机构管理员
     */
    TENANT_ADMIN(3, "机构管理员");

    /**
     * 用户角色类型
     */
    private final Integer roleType;

    /**
     * 用户角色描述
     */
    private final String desc;


    JwUserRoleTypeEnum(Integer roleType, String desc) {
        this.roleType = roleType;
        this.desc = desc;
    }

    public static JwUserRoleTypeEnum getByRoleType(Integer roleType) {
        for (JwUserRoleTypeEnum value : values()) {
            if (value.getRoleType().equals(roleType)) {
                return value;
            }
        }
        return null;
    }

    public static JwUserRoleTypeEnum getByDesc(String desc) {
        for (JwUserRoleTypeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }

}
