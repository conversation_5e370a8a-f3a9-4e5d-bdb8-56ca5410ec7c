package com.unicom.swdx.module.edu.enums.trainee;

/**
 * <AUTHOR>
 * @Description: 课程状态枚举
 * @date 2024-10-11
 */
public enum TraineeStatusEnum {

    REGISTERED(797, "已报名"),
    REPORTED(798, "已报到"),
    GRADUATED(799, "已结业"),
    DROPPED_OUT(800, "已退学");

    private final Integer status;

    private final String desc;

    TraineeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (TraineeStatusEnum item : TraineeStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (TraineeStatusEnum item : TraineeStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
