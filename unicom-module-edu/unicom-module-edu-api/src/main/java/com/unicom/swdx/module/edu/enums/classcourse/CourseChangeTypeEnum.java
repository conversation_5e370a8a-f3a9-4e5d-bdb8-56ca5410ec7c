package com.unicom.swdx.module.edu.enums.classcourse;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 调课类型
 * @date 2024-10-30
 */
@Getter
@AllArgsConstructor
public enum CourseChangeTypeEnum {

    /**
     * 调课
     */
    EXCHANGE(1, "调课"),

    /**
     * 更换教室
     */
    CHANGE_CLASSROOM(2, "更换教室"),

    /**
     * 更换上课时间
     */
    CHANGE_TIME(3,"更换上课时间"),

    /**
     * 更换老师
     */
    CHANGE_TEACHER(4, "更换老师"),

    /**
     * 取消授课
     */
    CANCEL(5, "取消授课");


    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param code 值
     * @return 描述
     */
        public static String getDescByCode(Integer code) {
        for (CourseChangeTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (CourseChangeTypeEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
