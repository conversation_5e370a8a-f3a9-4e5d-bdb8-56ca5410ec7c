package com.unicom.swdx.module.edu.enums.classcompletion;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 结业考核数据来源
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum DataSourceEnum {

    /**
     * 事假次数
     */
    PERSONAL_LEAVE_NUM(0, "事假次数"),
    /**
     * 病假次数
     */
    SICK_LEAVE_NUM(1, "病假次数"),
    /**
     * 五会假次数
     */
    FIVE_CANS_LEAVE_NUM(2, "五会假次数"),
    /**
     * 事假天数
     */
    PERSONAL_LEAVE_DAYS(3, "事假天数"),
    /**
     * 病假天数
     */
    SICK_LEAVE_DAYS(4, "病假天数"),
    /**
     * 五会假天数
     */
    FIVE_CANS_LEAVE_DAYS(5, "五会假天数"),
    /**
     * 到课率
     */
    CLASS_ATTENDANCE_RATE(6, "到课率"),
    /**
     * 就餐率
     */
    MEAL_ATTENDANCE_RATE(7, "就餐率"),
    /**
     * 住宿率
     */
    ACCOMMODATION_RATE(8, "住宿率"),
    /**
     * 课程评估
     */
    COURSE_EVALUATION_RATE(9, "评课率"),
    /**
     * 迟到次数
     */
    LATE_COUNT(10, "迟到次数");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByPeriod(Integer period) {
        for (DataSourceEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getPeriodByDesc(String desc) {
        for (DataSourceEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
