package com.unicom.swdx.module.edu.enums.courses;


/**
 * <AUTHOR>
 * @Description: 课程类型枚举
 * @date 2024-10-11
 */
public enum CoursesTypeEnum {

    TOPIC_COURSE(1, "专题"),

    OPTIONAL_COURSE(2, "选修课"),

    TEACHING_ACTIVITY(3, "教学活动");

    private final Integer type;

    private final String desc;

    CoursesTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型获取描述
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (CoursesTypeEnum item : CoursesTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     * @param desc 描述
     * @return 类型值
     */
    public static Integer getTypeByDesc(String desc) {
        for (CoursesTypeEnum item : CoursesTypeEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
