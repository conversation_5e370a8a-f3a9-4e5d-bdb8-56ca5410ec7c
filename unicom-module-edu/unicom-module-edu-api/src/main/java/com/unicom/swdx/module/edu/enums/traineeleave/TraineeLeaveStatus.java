package com.unicom.swdx.module.edu.enums.traineeleave;

/**
 * <AUTHOR>
 * @Description: 学员请假类型
 * @date 2024-11-5
 */
public enum TraineeLeaveStatus {

    DRAFT(0, "草稿"),

    CANCEL(1, "已撤回"),

    WAIT(2, "待审批"),

    ING(3, "审批中"),

    OK(4, "已通过"),

    NO(5, "已拒绝"),

    CANCELING(6, "撤回待审批");


    private final Integer code;

    private final String desc;

    TraineeLeaveStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值获取描述
     * @param code 值
     * @return 描述
     */
    public static String getDescByStatus(Integer code) {
        for (TraineeLeaveStatus item : TraineeLeaveStatus.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取值
     * @param desc 描述
     * @return 值
     */
    public static Integer getStatusByDesc(String desc) {
        for (TraineeLeaveStatus item : TraineeLeaveStatus.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
