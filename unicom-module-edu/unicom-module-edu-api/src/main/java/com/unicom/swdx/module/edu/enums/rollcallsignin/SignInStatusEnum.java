package com.unicom.swdx.module.edu.enums.rollcallsignin;

/**
 * <AUTHOR>
 * @Description: 大课考勤、点名签到状态枚举
 * @date 2025-01-17
 */
public enum SignInStatusEnum {

    /**
     * 0 - 大课考勤
     */
    NORMAL(0, "正常"),

    /**
     * 1 - 点名签到
     */
    WITHDRAW(1, "撤回状态");

    private final Integer status;

    private final String desc;

    SignInStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (SignInStatusEnum item : SignInStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (SignInStatusEnum item : SignInStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
