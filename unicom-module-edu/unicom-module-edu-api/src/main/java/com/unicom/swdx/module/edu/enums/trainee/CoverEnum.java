package com.unicom.swdx.module.edu.enums.trainee;

/**
 * <AUTHOR>
 * @Description: 导入数据是否覆盖枚举
 * @date 2024-10-11
 */
public enum CoverEnum {

    NO(0, "不覆盖"),

    YES(1, "覆盖");

    private final Integer code;

    private final String desc;

    CoverEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param code 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer code) {
        for (CoverEnum item : CoverEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (CoverEnum item : CoverEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
