package com.unicom.swdx.module.edu.api.signupunit;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 调训单位相关 Api调用
 * @date 2024-10-15
 */
@FeignClient(name = ApiConstants.NAME)
@Api(tags = "调训单位相关服务")
public interface SignUpUnitApi {

    String PREFIX = ApiConstants.PREFIX + "/sign-up-unit";

    @GetMapping(PREFIX + "/is-unit-admin")
    @ApiOperation("查询用户是否是调训单位管理员")
    CommonResult<Boolean> isUnitAdmin(@RequestParam(value = "userId") Long userId);

    @GetMapping(PREFIX + "/getUnitIdByUserId")
    @ApiOperation("查询用户是否是调训单位管理员")
    CommonResult<Long> getUnitIdByUserId(@RequestParam(value = "userId") Long userId);

    @GetMapping(PREFIX + "/getPhoneList")
    @ApiOperation("查询用户是否是调训单位管理员")
    CommonResult<List<String>> getPhoneList();
}
