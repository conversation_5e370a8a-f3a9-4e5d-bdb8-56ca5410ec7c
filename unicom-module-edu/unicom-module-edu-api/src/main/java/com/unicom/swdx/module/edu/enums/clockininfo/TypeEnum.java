package com.unicom.swdx.module.edu.enums.clockininfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 教学计划状态
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum TypeEnum {

    /**
     * 到课
     */
    COURSE(0, "到课"),

    /**
     * 就餐
     */
    DINE(1, "就餐"),

    /**
     * 住宿
     */
    LIVE(2,"住宿");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByCode(Integer period) {
        for (TypeEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (TypeEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
