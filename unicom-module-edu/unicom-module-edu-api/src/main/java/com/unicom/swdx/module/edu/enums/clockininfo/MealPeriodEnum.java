package com.unicom.swdx.module.edu.enums.clockininfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 教学计划状态
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum MealPeriodEnum {

    /**
     * 早餐
     */
    BREAKFAST(0, "早餐"),

    /**
     * 午餐
     */
    LUNCH(1, "午餐"),

    /**
     * 晚餐
     */
    DINNER(2,"晚餐");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByCode(Integer period) {
        for (MealPeriodEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (MealPeriodEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
