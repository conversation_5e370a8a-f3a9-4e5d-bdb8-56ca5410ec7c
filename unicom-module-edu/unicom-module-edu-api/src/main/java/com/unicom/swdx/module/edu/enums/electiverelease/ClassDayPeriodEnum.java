package com.unicom.swdx.module.edu.enums.electiverelease;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 上课时间段午别枚举 上午 下午 晚上
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum ClassDayPeriodEnum {

    /**
     * 上午
     */
    AM(0, "上午"),

    /**
     * 下午
     */
    PM(1, "下午"),

    /**
     * 晚上
     */
    NIGHT(2, "晚上");

    private final Integer period;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByPeriod(Integer period) {
        for (ClassDayPeriodEnum item : values()) {
            if (item.getPeriod().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getPeriodByDesc(String desc) {
        for (ClassDayPeriodEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getPeriod();
            }
        }
        return null;
    }

}