package com.unicom.swdx.module.edu.enums.clockininfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 考勤到率计算规则
 * @date 2025-01-21
 */
@Getter
@AllArgsConstructor
public enum ClockRateRuleEnum {

    /**
     * 实到/（应到-请假）
     */
    HAS_LEAVE(0, "实到/（应到-请假）"),

    /**
     * 实到/应到
     */
    NO_LEAVE(1, "实到/应到");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByCode(Integer period) {
        for (ClockRateRuleEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (ClockRateRuleEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
