package com.unicom.swdx.module.edu.enums.courses;


/**
 * <AUTHOR>
 * @Description: 课程类型枚举
 * @date 2024-10-11
 */
public enum CoursesPeriodEnum {

    MORNING("0", "上午"),

    AFTERNOON("1", "下午"),

    EVENING("2", "晚上");

    private final String type;

    private final String desc;

    CoursesPeriodEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型获取描述
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (CoursesPeriodEnum item : CoursesPeriodEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     * @param desc 描述
     * @return 类型值
     */
    public static String getTypeByDesc(String desc) {
        for (CoursesPeriodEnum item : CoursesPeriodEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
