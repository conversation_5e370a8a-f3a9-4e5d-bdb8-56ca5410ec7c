package com.unicom.swdx.module.edu.enums.teacherinformation;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 师资相关的一些字典的type
 * @date 2024-10-11
 */
public enum TeacherInformationDictEnum {

    SYSTEM_USER_SEX("system_user_sex", "性别"),
    NATION("nation", "民族"),
    POLITICAL_IDENTITY("political_identity", "政治面貌"),
    PERSON_EDUCATION("person_education", "学历"),
    PERSON_ACADEMIC_DEGREE("person_academic_degree", "学位"),
    PERSON_RANK("person_rank", "职级"),
    PERSON_ADMINISTRATIVE_POSITION_RANK("person_administrative_position_rank", "行政级别");

    private final String type;

    private final String desc;

    TeacherInformationDictEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static List<String> getTypeList() {
        List<String> list = new ArrayList<>();
        for (TeacherInformationDictEnum item : TeacherInformationDictEnum.values()) {
            list.add(item.getType());
        }
        return list;
    }

    /**
     * 根据类型获取描述
     *
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(String type) {
        for (TeacherInformationDictEnum item : TeacherInformationDictEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     *
     * @param desc 描述
     * @return 类型值
     */
    public static String getTypeByDesc(String desc) {
        for (TeacherInformationDictEnum item : TeacherInformationDictEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
