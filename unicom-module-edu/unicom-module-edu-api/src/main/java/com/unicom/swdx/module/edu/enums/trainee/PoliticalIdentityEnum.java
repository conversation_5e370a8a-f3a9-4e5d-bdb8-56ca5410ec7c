package com.unicom.swdx.module.edu.enums.trainee;

/**
 * <AUTHOR>
 * @Description: 课程状态枚举
 * @date 2024-10-11
 */
public enum PoliticalIdentityEnum {

    COMMUNIST_PARTY_MEMBER(785, "中共党员"),
    COMMUNIST_YOUTH_LEAGUE_MEMBER(786, "共青团员"),
    MASSES(787, "群众"),
    DEMOCRATIC_PARTY_MEMBER(788, "民主党派成员"),
    INDEPENDENT(789, "无党派人士");

    private final Integer status;

    private final String desc;

    PoliticalIdentityEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (PoliticalIdentityEnum item : PoliticalIdentityEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (PoliticalIdentityEnum item : PoliticalIdentityEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
