package com.unicom.swdx.module.edu.enums.evaluationresponse;

/**
 * <AUTHOR>
 * @Description: 问卷已评、未评状态枚举
 * @date 2024-10-11
 */
public enum EvaluationStatusEnum {

    UNEVALUATED(0, "未评"),

    EVALUATED(1, "已评");

    private final Integer status;

    private final String desc;

    EvaluationStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (EvaluationStatusEnum item : EvaluationStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (EvaluationStatusEnum item : EvaluationStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
