package com.unicom.swdx.module.edu.enums.clockcalendar;

/**
 * <AUTHOR>
 * @Description: 大课考勤、点名签到状态枚举
 * @date 2025-01-17
 */
public enum ClockCalendarStatusEnum {

    /**
     * 0 - 考勤日历开启
     */
    ON(0, "开启"),

    /**
     * 1 - 考勤日历关闭
     */
    OFF(1, "关闭");

    private final Integer status;

    private final String desc;

    ClockCalendarStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (ClockCalendarStatusEnum item : ClockCalendarStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (ClockCalendarStatusEnum item : ClockCalendarStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
