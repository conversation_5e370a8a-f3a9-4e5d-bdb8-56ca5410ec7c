package com.unicom.swdx.module.edu.enums.trainee;

import com.unicom.swdx.module.edu.enums.courses.CoursesDictEnum;

/**
 * <AUTHOR>
 * @Description: 学员相关的枚举
 * @date 2024-10-11
 */
public enum TraineeDictTypeEnum {

    EDUCATIONAL_LEVEL("stu-educational", "文化程度"),
    JOB_LEVEL("stu_rank", "职级"),
    POLITICAL_IDENTITY("political_identity", "政治面貌"),
    TRAINEE_STATUS("trainee_status", "学员状态"),
    ETHNIC("nation", "民族");

    private final String type;

    private final String desc;

    TraineeDictTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型获取描述
     *
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(String type) {
        for (CoursesDictEnum item : CoursesDictEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     *
     * @param desc 描述
     * @return 类型值
     */
    public static String getTypeByDesc(String desc) {
        for (CoursesDictEnum item : CoursesDictEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }

}
