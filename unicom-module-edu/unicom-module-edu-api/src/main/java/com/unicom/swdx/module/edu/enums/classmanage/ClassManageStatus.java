package com.unicom.swdx.module.edu.enums.classmanage;

public enum ClassManageStatus {

//    班级状态，0-报名中，1-报名结束，2-开班中，3-已结业，4-未开始

    REGISTER_ING(0, "报名中"),

    REGISTER_END(1, "报名结束"),

    OPENING(2, "开班中"),

    CLASS_END(3, "已结业"),

    UN_BEGIN(4, "未开始");


    private final Integer code;

    private final String desc;

    ClassManageStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值获取描述
     * @param code 值
     * @return 描述
     */
    public static String getDescByStatus(Integer code) {
        for (ClassManageStatus item : ClassManageStatus.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取值
     * @param desc 描述
     * @return 值
     */
    public static Integer getStatusByDesc(String desc) {
        for (ClassManageStatus item : ClassManageStatus.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }
}
