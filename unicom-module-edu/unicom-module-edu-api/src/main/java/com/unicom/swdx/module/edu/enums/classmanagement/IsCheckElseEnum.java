package com.unicom.swdx.module.edu.enums.classmanagement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 是否开启考勤
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum IsCheckElseEnum {

    /**
     * 关闭
     */
    OFF(1, "关闭"),

    /**
     * 开启
     */
    ON(0, "开启");


    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByCode(Integer period) {
        for (IsCheckElseEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (IsCheckElseEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
