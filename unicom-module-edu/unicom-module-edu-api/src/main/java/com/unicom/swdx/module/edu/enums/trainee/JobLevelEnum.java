package com.unicom.swdx.module.edu.enums.trainee;

/**
 * <AUTHOR>
 * @Description: 课程状态枚举
 * @date 2024-10-11
 */
public enum JobLevelEnum {

//    ZHENG_TING(1, "正厅"),
//    FU_TING(2, "副厅"),
//    ZHENG_CHU(3, "正处"),
//    FU_CHU(4, "副处"),
//    ZHENG_KE(5, "正科"),
//    FU_KE(6, "副科"),
//    FIRST_LEVEL_INSPECTOR(7, "一级巡视员"),
//    SECOND_LEVEL_INSPECTOR(8, "二级巡视员"),
//    FIRST_LEVEL_RESEARCHER(9, "一级调研员"),
//    SECOND_LEVEL_RESEARCHER(10, "二级调研员"),
//    THIRD_LEVEL_RESEARCHER(11, "三级调研员"),
//    FOURTH_LEVEL_RESEARCHER(12, "四级调研员"),
//    FIRST_LEVEL_PRINCIPAL_CLERK(13, "一级主任科员"),
//    SECOND_LEVEL_PRINCIPAL_CLERK(14, "二级主任科员"),
//    THIRD_LEVEL_PRINCIPAL_CLERK(15, "三级主任科员"),
//    FOURTH_LEVEL_PRINCIPAL_CLERK(16, "四级主任科员"),
//    CLERK(17, "科员"),
//    OFFICE_WORKER(18, "办事员"),
//    UNCLASSIFIED(19, "未定职人员");
    FIRST_LEVEL_INSPECTOR(498, "一级巡视员"),
    SECOND_LEVEL_INSPECTOR(499, "二级巡视员"),
    FIRST_LEVEL_RESEARCHER(500, "一级调研员"),
    SECOND_LEVEL_RESEARCHER(501, "二级调研员"),
    THIRD_LEVEL_RESEARCHER(502, "三级调研员"),
    FOURTH_LEVEL_RESEARCHER(503, "四级调研员"),
    FIRST_LEVEL_PRINCIPAL_CLERK(504, "一级主任科员"),
    SECOND_LEVEL_PRINCIPAL_CLERK(505, "二级主任科员"),
    THIRD_LEVEL_PRINCIPAL_CLERK(506, "三级主任科员"),
    FOURTH_LEVEL_PRINCIPAL_CLERK(507, "四级主任科员"),
    FIRST_CLERK(508, "一级科员"),
    SECOND_CLERK(509, "二级科员");

    private final Integer status;

    private final String desc;

    JobLevelEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (JobLevelEnum item : JobLevelEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (JobLevelEnum item : JobLevelEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
