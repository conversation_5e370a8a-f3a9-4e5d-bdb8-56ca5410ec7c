package com.unicom.swdx.module.edu.enums.teachertodoitems;

/**
 * <AUTHOR>
 * @Description: 班主任待办事项状态枚举
 * @date 2024-10-11
 */
public enum TeacherTodoStatusEnum {

    /**
     * 未办
     */
    UNDO(0, "未办"),

    /**
     * 已办
     */
    DONE(1, "已办");

    private final Integer status;

    private final String desc;

    TeacherTodoStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (TeacherTodoStatusEnum item : TeacherTodoStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (TeacherTodoStatusEnum item : TeacherTodoStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
