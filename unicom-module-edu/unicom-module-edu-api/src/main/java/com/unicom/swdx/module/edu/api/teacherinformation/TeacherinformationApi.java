package com.unicom.swdx.module.edu.api.teacherinformation;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @Description: 业中的一些Api调用
 * @date 2024-10-15
 */
@FeignClient(name = ApiConstants.NAME)
@Api(tags = "edu数据服务")
public interface TeacherinformationApi {

    String PREFIX = ApiConstants.PREFIX + "/teacherinformation";

    @GetMapping(PREFIX + "/convertTeacherAndInsert")
    @ApiOperation("获取业中部门列表")
    CommonResult<Boolean>  convertTeacherAndInsert(@RequestParam("tenantId")Long tenantId);


}
