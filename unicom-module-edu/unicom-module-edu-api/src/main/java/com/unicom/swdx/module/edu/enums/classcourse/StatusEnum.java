package com.unicom.swdx.module.edu.enums.classcourse;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 教学计划状态
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {

    /**
     * 暂存
     */
    TEMPORARY("0", "暂存"),

    /**
     * 已发布
     */
    PUBLISHED("1", "发布");


    private final String code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByPeriod(Integer period) {
        for (StatusEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static String getPeriodByDesc(String desc) {
        for (StatusEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
