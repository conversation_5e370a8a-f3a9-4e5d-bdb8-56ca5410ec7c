package com.unicom.swdx.module.edu.enums.xcxmsg;

/**
 * <AUTHOR>
 * @Description: 服务通知类型
 * @date 2024-11-19
 */
public enum XcxMsgSendType {

    LEAVE_FIN(0, "您的请假审批已完成", "?route_type=stu&next_page=pages_student_leave_detail_index&client_id=xueyuanH5", "请查收"),

    //到课0，住宿1，就餐2
    BJ_KQ(1, "班级考勤", "?route_type=stu&next_page=pages_stu_attendance_daily&client_id=xueyuanH5", "请注意及时签到"),

    DM_QD(2, "点名签到-签到提醒", "?route_type=stu&next_page=pages_stu_attendance_call&client_id=xueyuanH5", "请注意及时签到"),

    DK_KQ(3, "大课考勤-签到提醒", "?route_type=stu&next_page=pages_stu_attendance_course&client_id=xueyuanH5", "请注意及时签到"),

    XK_TX(4, "选课通知", "?route_type=stu&next_page=pages_stu_selectCourse&client_id=xueyuanH5", "请查看详情前往小程序完成本次选课"),

    KC_PJ(5, "课程评价", "?route_type=stu&next_page=pages_stu_evaluateCourse_unreviewed&client_id=xueyuanH5", "请查看详情前往小程序对本次课程进行评价"),

    PC_SK_TX(6, "定时授课提醒", "pages-home/mineInfo/personalSchedule/index", " "),

    KC_PJ_ML(7, "课程评价目录", "?route_type=stu&next_page=pages_stu_evaluateCourse_index&client_id=xueyuanH5", "请查看详情前往小程序对课程进行评价"),

    LEAVE_APPROVAL(8,"请假审批提醒","?route_type=edu&next_page=pages_teacher_leaveDetail_index&client_id=banzhurenH5","请查看详情前往小程序进行请假审批"),

    LEAVE_REPORT(9,"学员你好，温馨提醒您离校报备未填写","?route_type=stu&next_page=pages_stu_departureReport_add&client_id=xueyuanH5","点击查看离校报备");

    private final Integer code;

    private final String desc;

    private final String path;

    private final String remark;


    XcxMsgSendType(Integer code, String desc, String path, String remark) {
        this.code = code;
        this.desc = desc;
        this.path = path;
        this.remark = remark;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getPath() {
        return path;
    }

    public String getRemark() {
        return remark;
    }


    /**
     * 根据值获取描述
     * @param code 值
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        for (XcxMsgSendType item : XcxMsgSendType.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据值获取path
     * @param code 值
     * @return path
     */
    public static String getPathByCode(Integer code) {
        for (XcxMsgSendType item : XcxMsgSendType.values()) {
            if (item.getCode().equals(code)) {
                return item.getPath();
            }
        }
        return null;
    }

    /**
     * 根据值获取remark
     * @param code 值
     * @return remark
     */
    public static String getRemarkByCode(Integer code) {
        for (XcxMsgSendType item : XcxMsgSendType.values()) {
            if (item.getCode().equals(code)) {
                return item.getRemark();
            }
        }
        return null;
    }

    public static XcxMsgSendType getByCode(Integer code) {
        for (XcxMsgSendType item : XcxMsgSendType.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
