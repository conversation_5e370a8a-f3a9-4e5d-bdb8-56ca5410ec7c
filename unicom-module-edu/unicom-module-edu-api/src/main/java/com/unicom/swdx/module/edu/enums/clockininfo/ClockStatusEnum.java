package com.unicom.swdx.module.edu.enums.clockininfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 教学计划状态
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum ClockStatusEnum {

    /**
     * 未打卡
     */
    NOT_DONE(0, "未打卡"),

    /**
     * 已打卡
     */
    DONE(1, "已打卡"),

    /**
     * 迟到
     */
    LATE(2,"迟到"),

    /**
     * 请假
     */
    LEAVE(3,"请假");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByCode(Integer period) {
        for (ClockStatusEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (ClockStatusEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
