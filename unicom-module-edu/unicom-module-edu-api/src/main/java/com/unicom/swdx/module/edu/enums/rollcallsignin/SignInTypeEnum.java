package com.unicom.swdx.module.edu.enums.rollcallsignin;

/**
 * <AUTHOR>
 * @Description: 大课考勤、点名签到枚举
 * @date 2024-11-05
 */
public enum SignInTypeEnum {

    /**
     * 0 - 大课考勤
     */
    LECTURE_ATTENDANCE(0, "大课考勤"),

    /**
     * 1 - 点名签到
     */
    ROLL_CALL_SIGN_IN(1, "点名签到");

    private final Integer status;

    private final String desc;

    SignInTypeEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (SignInTypeEnum item : SignInTypeEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (SignInTypeEnum item : SignInTypeEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
