package com.unicom.swdx.module.edu.enums.classcompletion;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 模板是否为默认模板
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum DefaultCompletionEnum {

    /**
     * 默认模板
     */
    DEFAULT(0, "默认模板"),

    /**
     * 非模板模板
     */
    NOT_DEFAULT(1, "非模板模板");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByPeriod(Integer period) {
        for (DefaultCompletionEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getPeriodByDesc(String desc) {
        for (DefaultCompletionEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
