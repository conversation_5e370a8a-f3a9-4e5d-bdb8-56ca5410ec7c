package com.unicom.swdx.module.edu.enums.trainee;

/**
 * <AUTHOR>
 * @Description: 课程状态枚举
 * @date 2024-10-11
 */
public enum EducationalLevelEnum {

    // 1-博士研究生 2-硕士研究生 3-大学本科 4-在职研究生 5-大学专科 6-党校研究生 7-其他
    UNDERWAY(790, "博士研究生"),
    MASTER(791, "硕士研究生"),
    BACHELOR(792, "大学本科"),
    ON_THE_JOB(793, "在职研究生"),
    JUNIOR_COLLEGE(794, "大学专科"),
    PARTY_SCHOOL(795, "党校研究生"),
    OTHER(796, "其他");

    private final Integer status;

    private final String desc;

    EducationalLevelEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (EducationalLevelEnum item : EducationalLevelEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (EducationalLevelEnum item : EducationalLevelEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
