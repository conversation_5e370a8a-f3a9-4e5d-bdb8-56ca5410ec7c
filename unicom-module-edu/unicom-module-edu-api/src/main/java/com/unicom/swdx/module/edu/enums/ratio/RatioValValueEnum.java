package com.unicom.swdx.module.edu.enums.ratio;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 率数最大值枚举
 * @date 2024-11-05
 */
@Getter
public enum RatioValValueEnum {

    /**
     * 百分比最大值 100
     */
    MAX_PERCENTAGE(100F, "百分比最大值"),

    /**
     * 百分比最小值 0
     */
    MIN_PERCENTAGE(0F, "百分比最小值"),

    /**
     * 百分比最小值 0
     */
    MIN_DECIMAL(0F, "小数最小值"),

    /**
     * 小数最大值 1
     */
    MAX_DECIMAL(1F, "小数最大值");

    private final Float value;

    private final String desc;

    RatioValValueEnum(Float value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 最小、最大值之内 设置百分比值
     * @param value 原始值
     * @return 设置值
     */
    public static Float getValidValueByPercentage(Float value) {
        if (Objects.isNull(value)) {
            return null;
        }
        Float v = value > MAX_PERCENTAGE.getValue() ? MAX_PERCENTAGE.getValue() : value;
        return v < MIN_PERCENTAGE.getValue() ? MIN_PERCENTAGE.getValue() : v;
    }

    /**
     * 最大值之内 设置小数值
     * @param value 原始值
     * @return 设置值
     */
    public static Float getValidValueByDecimal(Float value) {
        if (Objects.isNull(value)) {
            return null;
        }
        Float v = value > MAX_DECIMAL.getValue() ? MAX_DECIMAL.getValue() : value;
        return v < MIN_DECIMAL.getValue() ? MIN_DECIMAL.getValue() : v;
    }

}
