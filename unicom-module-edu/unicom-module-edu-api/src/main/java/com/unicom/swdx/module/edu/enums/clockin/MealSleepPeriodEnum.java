package com.unicom.swdx.module.edu.enums.clockin;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description: 用餐住宿枚举
 * @date 2024-10-11
 */
@Getter
@AllArgsConstructor
public enum MealSleepPeriodEnum {


    BREAKFAST(0, "早餐"),
    LUNCH(1, "午餐"),
    DINNER(2, "晚餐"),
    SLEEPING(3, "住宿");

    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByPeriod(Integer period) {
        for (MealSleepPeriodEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getPeriodByDesc(String desc) {
        for (MealSleepPeriodEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
