package com.unicom.swdx.module.edu.enums.electiverelease;

/**
 * <AUTHOR>
 * @Description: 学员待选、已选选修课发布信息类型枚举
 * @date 2024-10-11
 */
public enum TraineeReleaseCoursesStatusEnum {

    UNSELECT(0, "待选"),

    SELECTED(1, "已选");

    private final Integer type;

    private final String desc;

    TraineeReleaseCoursesStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (TraineeReleaseCoursesStatusEnum item : TraineeReleaseCoursesStatusEnum.values()) {
            if (item.getType().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (TraineeReleaseCoursesStatusEnum item : TraineeReleaseCoursesStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }

}
