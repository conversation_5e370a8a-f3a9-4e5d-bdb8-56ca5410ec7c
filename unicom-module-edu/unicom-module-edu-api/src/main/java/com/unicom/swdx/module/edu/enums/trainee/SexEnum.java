package com.unicom.swdx.module.edu.enums.trainee;

/**
 * <AUTHOR>
 * @Description: 课程状态枚举
 * @date 2024-10-11
 */
public enum SexEnum {

    FEMALE(2, "女"),

    MALE(1, "男");

    private final Integer status;

    private final String desc;

    SexEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (SexEnum item : SexEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (SexEnum item : SexEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
