package com.unicom.swdx.module.edu.enums.traineeleave;

/**
 * <AUTHOR>
 * @Description: 学员请假类型
 * @date 2024-11-5
 */
public enum LeaveTaskStatus {

    START(0, "发起"),

    CANCEL(1, "撤回"),

    WAITING(2, "未审批"),

    APPROVE(4, "已通过"),

    REJECT(5, "已拒绝");


    private final Integer code;

    private final String desc;

    LeaveTaskStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值获取描述
     * @param code 值
     * @return 描述
     */
    public static String getDescByStatus(Integer code) {
        for (LeaveTaskStatus item : LeaveTaskStatus.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取值
     * @param desc 描述
     * @return 值
     */
    public static Integer getStatusByDesc(String desc) {
        for (LeaveTaskStatus item : LeaveTaskStatus.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
