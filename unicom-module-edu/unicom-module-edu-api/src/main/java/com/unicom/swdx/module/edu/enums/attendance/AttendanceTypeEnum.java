package com.unicom.swdx.module.edu.enums.attendance;

/**
 * <AUTHOR>
 * @Description: 考勤类型 0-到课 1-就餐 2-住宿
 * @date 2024-11-05
 */
public enum AttendanceTypeEnum {

    /**
     * 0 - 到课
     */
    CLASS_ATTENDANCE(0, "到课考勤"),

    /**
     * 1 - 就餐
     */
    MEAL_ATTENDANCE(1, "就餐考勤"),
    /**
     * 2 - 住宿
     */
    ACCOMMODATION_ATTENDANCE(2, "住宿考勤");

    private final Integer status;

    private final String desc;

    AttendanceTypeEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (AttendanceTypeEnum item : AttendanceTypeEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (AttendanceTypeEnum item : AttendanceTypeEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
