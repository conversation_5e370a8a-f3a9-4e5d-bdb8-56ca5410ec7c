package com.unicom.swdx.module.edu.enums.attendance;

/**
 * <AUTHOR>
 * @Description: 学员点名签到状态枚举 0-未到 1-已到
 * @date 2024-11-05
 */
public enum AttendanceStatusEnum {

    /**
     * 0 - 未到
     */
    NOT_ARRIVE(0, "未打卡"),

    /**
     * 1 - 已到
     */
    ARRIVED(1, "已打卡"),

    /**
     * 2 - 迟到
     */
    LATE(2, "迟到"),

    /**
     * 3 - 请假
     */
    LEAVE(3, "请假");

    private final Integer status;

    private final String desc;

    AttendanceStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (AttendanceStatusEnum item : AttendanceStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (AttendanceStatusEnum item : AttendanceStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
