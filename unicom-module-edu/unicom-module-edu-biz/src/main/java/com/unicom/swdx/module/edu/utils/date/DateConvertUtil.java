package com.unicom.swdx.module.edu.utils.date;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DateUtils
 * @Author: lty
 * @Date: 2024/10/29 11:14
 */
public class DateConvertUtil {

    /**
     * 获取给定日期所在周的周一和周日日期
     * @param dateString 给定的日期字符串，格式为"yyyy-MM-dd"
     * @return 包含周一和周日日期的List<String>
     */
    public static List<String> getWeekStartAndEnd(String dateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateString, formatter);

        // 计算周一和周日的日期
        LocalDate startOfWeek = date.with(DayOfWeek.MONDAY);
        LocalDate endOfWeek = date.with(DayOfWeek.SUNDAY);

        List<String> weekDates = new ArrayList<>();
        weekDates.add(startOfWeek.format(formatter));
        weekDates.add(endOfWeek.format(formatter));

        return weekDates;
    }

}
