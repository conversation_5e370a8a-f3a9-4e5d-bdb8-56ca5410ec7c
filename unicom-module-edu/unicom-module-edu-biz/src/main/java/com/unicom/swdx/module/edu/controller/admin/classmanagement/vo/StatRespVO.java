package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 首页统计返回VO")
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class StatRespVO {

    @ApiModelProperty(value = "调训单位数")
    private Integer signUpUnitNum;

    @ApiModelProperty(value = "成功报名学员数")
    private Integer traineeRegisteredNum;

    @ApiModelProperty(value = "成功报到学员数")
    private Integer traineeReportedNum;

    @ApiModelProperty(value = "办班总数")
    private Integer classNum;

    @ApiModelProperty(value = "未开班数")
    private Integer classNotStartedNum;

    @ApiModelProperty(value = "正在开班数")
    private Integer classStartingNum;

    @ApiModelProperty(value = "已结业班级数")
    private Integer classFinishedNum;
}
