package com.unicom.swdx.module.edu.utils.serialnumber;

import com.unicom.swdx.framework.common.pojo.PageParam;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * <AUTHOR>
 * @Description: 页面数据序号生成工具类
 * @date 2024-10-12
 */
public class PageDataSerialNumberUtil {
    private PageDataSerialNumberUtil() {}

    /**
     * 分页信息生成序号列表
     * @param isSerialDesc 序号是否降序排
     * @param total 数据总量
     * @param pageParam 分页参数 包含pageNo、pageSize
     * @param pageDataSize 当前pageNo的数据量
     * @return List<Long> 生成的长度为pageDataSize的序号列表
     */
    public static List<Long> generateSerialNumberList(Boolean isSerialDesc, long total, PageParam pageParam, int pageDataSize) {
        Integer pageNo = pageParam.getPageNo();
        Integer pageSize = pageParam.getPageSize();
        // 排序列表
        List<Long> serialNumberList;
        // 根据数据总量、每页大小、当前页码计算出该批数据的序号
        if (Boolean.TRUE.equals(isSerialDesc)) {
            // 序号倒排
            long startSerialNumber = total - (long) (pageNo - 1) * pageSize;
            serialNumberList = LongStream.rangeClosed(startSerialNumber - pageDataSize + 1, startSerialNumber)
                    .boxed()
                    .sorted(Collections.reverseOrder())
                    .collect(Collectors.toList());
        } else {
            // 默认或指定正排
            long startSerialNumber = (long) (pageNo - 1) * pageSize + 1;
            serialNumberList = LongStream.range(startSerialNumber, startSerialNumber + pageDataSize)
                    .boxed()
                    .collect(Collectors.toList());
        }
        // 设置序号
        return serialNumberList;
    }

    public static void main(String[] args) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(1);
        pageParam.setPageSize(10);
        System.out.println(generateSerialNumberList(true, 100, pageParam, 10));
        System.out.println(generateSerialNumberList(false, 100, pageParam, 10));
    }

}
