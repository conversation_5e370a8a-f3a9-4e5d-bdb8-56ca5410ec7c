package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 撤回已评问卷 Request VO")
@Data
public class EvaluationRevokeReqVO {

    @Schema(description = "评估响应ID", required = true, example = "1024")
    @NotNull(message = "评估响应ID不能为空")
    private Long id;

    @Schema(description = "是否强制撤回（对已过评价有效期的问卷）", example = "false")
    private Boolean forceRevoke;

}