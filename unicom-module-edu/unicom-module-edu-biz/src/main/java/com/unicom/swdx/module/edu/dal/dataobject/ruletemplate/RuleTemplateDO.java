package com.unicom.swdx.module.edu.dal.dataobject.ruletemplate;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 考勤规则模版 DO
 *
 * <AUTHOR>
 */
@TableName("edu_rule_template")
@KeySequence("edu_rule_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleTemplateDO extends TenantBaseDO {

    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 0-到课考勤，1-就餐考勤，2-住宿考勤
     */
    private Integer ruleType;
    /**
     * 考勤地点，预留
     */
    private Integer locations;
    /**
     * 上课前打卡时间
     */
    private Integer beforeClassTime;
    /**
     * 上课后打卡时间
     */
    private Integer afterClassTime;
    /**
     * 早餐打卡开始时间
     */
    private LocalTime breakfastStartTime;
    /**
     * 早餐打卡结束时间
     */
    private LocalTime breakfastEndTime;
    /**
     * 午餐打卡开始时间
     */
    private LocalTime lunchStartTime;
    /**
     * 午餐打卡结束时间
     */
    private LocalTime lunchEndTime;
    /**
     * 晚餐打卡开始时间
     */
    private LocalTime dinnerStartTime;
    /**
     * 晚餐打卡结束时间
     */
    private LocalTime dinnerEndTime;
    /**
     * 住宿打卡开始时间
     */
    private LocalTime putUpStartTime;
    /**
     * 住宿打卡结束时间
     */
    private LocalTime putUpEndTime;
    /**
     * 状态，0-启用，1-禁用
     */
    private Integer status;
    /**
     * 校区，字典值
     */
    private Integer campus;
    /**
     * 默认规则，0-是，1-否
     */
    private Integer defaultRule;

}
