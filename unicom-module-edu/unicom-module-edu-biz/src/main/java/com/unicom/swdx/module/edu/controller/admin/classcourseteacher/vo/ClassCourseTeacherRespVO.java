package com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 课程表-教师-授课关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassCourseTeacherRespVO extends ClassCourseTeacherBaseVO {

    @ApiModelProperty(value = "唯一标识符，自增", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
