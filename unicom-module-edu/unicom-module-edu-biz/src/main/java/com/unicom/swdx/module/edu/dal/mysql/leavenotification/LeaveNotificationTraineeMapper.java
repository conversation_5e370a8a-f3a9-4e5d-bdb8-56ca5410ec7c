package com.unicom.swdx.module.edu.dal.mysql.leavenotification;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.dal.dataobject.leavenotification.LeaveNotificationTraineeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LeaveNotificationTraineeMapper extends BaseMapperX<LeaveNotificationTraineeDO> {

    default List<LeaveNotificationTraineeDO> selectListByNotificationId(Long notificationId) {
        return selectList(new LambdaQueryWrapperX<LeaveNotificationTraineeDO>()
                .eq(LeaveNotificationTraineeDO::getNotificationId, notificationId));
    }

    default List<LeaveNotificationTraineeDO> selectListByNotificationIdAndStatus(Long notificationId, Integer status) {
        return selectList(new LambdaQueryWrapperX<LeaveNotificationTraineeDO>()
                .eq(LeaveNotificationTraineeDO::getNotificationId, notificationId)
                .eq(LeaveNotificationTraineeDO::getStatus, status));
    }

    default LeaveNotificationTraineeDO selectByNotificationIdAndTraineeId(Long notificationId, Long traineeId) {
        return selectOne(new LambdaQueryWrapperX<LeaveNotificationTraineeDO>()
                .eq(LeaveNotificationTraineeDO::getNotificationId, notificationId)
                .eq(LeaveNotificationTraineeDO::getTraineeId, traineeId));
    }

    default Long countByNotificationIdAndStatus(Long notificationId, Integer status) {
        return selectCount(new LambdaQueryWrapperX<LeaveNotificationTraineeDO>()
                .eq(LeaveNotificationTraineeDO::getNotificationId, notificationId)
                .eq(LeaveNotificationTraineeDO::getStatus, status));
    }
}