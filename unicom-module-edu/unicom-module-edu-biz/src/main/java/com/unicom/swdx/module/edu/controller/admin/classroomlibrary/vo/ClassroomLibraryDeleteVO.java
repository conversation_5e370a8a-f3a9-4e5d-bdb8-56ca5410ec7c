package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("管理后台 - EduClassroomLibrary更新 Request VO")
@Data
@ToString(callSuper = true)
public class ClassroomLibraryDeleteVO {

    @ApiModelProperty(value = "批量删除", required = true)
    private String ids;

}
