package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 选修课发布的班级范围分页 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassManagementElectiveReleaseRespVO extends ClassManagementRespVO {

    @ApiModelProperty(value = "排课表ID", example = "1")
    private Long classCourseId;
}
