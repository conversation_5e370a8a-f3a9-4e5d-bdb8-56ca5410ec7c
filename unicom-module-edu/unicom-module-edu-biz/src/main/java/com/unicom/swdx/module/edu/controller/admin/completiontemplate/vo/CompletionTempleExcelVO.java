package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * EduClassManagement Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CompletionTempleExcelVO {

    @ExcelProperty("模版名称")
    @ApiModelProperty(value = "模版名称序号 0")
    private String templateName;

    @ExcelProperty("校区")
    @ApiModelProperty(value = "校区序号 1")
    private String campus;

    @ExcelProperty("默认模版")
    @ApiModelProperty(value = "默认模版序号 2")
    private String defaultRule;

}
