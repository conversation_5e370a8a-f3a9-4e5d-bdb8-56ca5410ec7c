package com.unicom.swdx.module.edu.dal.dataobject.notificationmessage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName("edu_notification_message")
@KeySequence("edu_notification_message_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMessageDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 发布人
     */
    private String publisher;
    /**
     * 标题
     */
    private String title;
    /**
     * 站内信内容
     */
    private String content;
    /**
     * 是否置顶,1-是，0-否
     */
    private Integer isTop;
    /**
     * 是否发布，1-发布，2-存草稿箱
     */
    private Integer isPublish;
    /**
     * 状态，1-上架，0-下架
     */
    private Integer status;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    /**
     * 存草稿箱时间
     */
    private LocalDateTime draftsTime;
    /**
     * 置顶时间
     */
    private LocalDateTime topTime;

}
