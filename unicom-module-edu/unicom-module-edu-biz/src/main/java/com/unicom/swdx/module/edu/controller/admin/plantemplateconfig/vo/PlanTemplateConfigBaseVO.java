package com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.math.BigDecimal;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 教学计划模版配置 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PlanTemplateConfigBaseVO {

    @ApiModelProperty(value = "教学计划模版ID", required = true)
    @NotNull(message = "教学计划模版ID不能为空")
    private Long templateId;

    @ApiModelProperty(value = "星期几（如：1代表周一等）", required = true)
    @NotNull(message = "星期几（如：1代表周一等）不能为空")
    private String dayOfWeek;

    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）", required = true)
    @NotNull(message = "时间段（0上午，1下午，2晚上）不能为空")
    private String period;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

}
