package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免信息导入有问题
public class ExportTraineeInfoExcelVO {

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "性别")
    private String sex;

    @ExcelProperty(value = "手机号码")
    private String phone;

    @ExcelProperty(value = "职级")
    private String jobLevel;

    @ExcelProperty(value = "职务")
    private String position;

    @ExcelProperty(value = "政治面貌")
    private String politicalIdentity;

    @ExcelProperty(value = "报名时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String createTime;

    @ExcelProperty(value = "学员状态")
    private String status;

}

