package com.unicom.swdx.module.edu.controller.admin.plandetail.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 教学计划详情 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PlanDetailExcelVO {

    @ExcelProperty("唯一标识")
    private Long id;

    @ExcelProperty("教学计划id")
    private Long planId;

    @ExcelProperty("日期")
    private String date;

    @ExcelProperty("时间段（0上午，1下午，2晚上）")
    private String period;

    @ExcelProperty("开始时间")
    private LocalDateTime beginTime;

    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

}
