package com.unicom.swdx.module.edu.service.noticeannouncement;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExcelVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExportParamsVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagerDeleteVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * EduNoticeAnnouncement Service 接口
 *
 * <AUTHOR>
 */
public interface NoticeAnnouncementService {

    /**
     * 创建EduNoticeAnnouncement
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createNoticeAnnouncement(@Valid NoticeAnnouncementCreateReqVO createReqVO);

    /**
     * 更新EduNoticeAnnouncement
     *
     * @param updateReqVO 更新信息
     */
    void updateNoticeAnnouncement(@Valid NoticeAnnouncementUpdateReqVO updateReqVO);

    /**
     * 删除EduNoticeAnnouncement
     *
     * @param id 编号
     */
    void deleteNoticeAnnouncement(Integer id);
    /**
     * 批量删除EduNoticeAnnouncement
     *
     * @param noticeDeleteVO
     */
    void deleteNoticeAnnouncementBatch(NoticeDeleteVO noticeDeleteVO);

    /**
     * 获得EduNoticeAnnouncement
     *
     * @param id 编号
     * @return EduNoticeAnnouncement
     */
    List<NoticeAnnouncementUrlVO> getNoticeAnnouncement(Integer id);

    /**
     * 获得EduNoticeAnnouncement列表
     *
     * @param ids 编号
     * @return EduNoticeAnnouncement列表
     */
    List<NoticeAnnouncementDO> getNoticeAnnouncementList(Collection<Integer> ids);

    /**
     * 获得EduNoticeAnnouncement分页
     *
     * @param pageReqVO 分页查询
     * @return EduNoticeAnnouncement分页
     */
    PageResult<NoticeAnnouncementRespVO> getNoticeAnnouncementPage(NoticeAnnouncementPageReqVO pageReqVO);
    /**
     * 批量发布
     *
     * @param noticeDeleteVO
     */
    void publishBatch(NoticeDeleteVO noticeDeleteVO);

    /**
     * 获得EduClassManagement列表, 用于 Excel 导出
     * @param reqVO
     * @return
     */
    void getNoticeAnnouncementInfoList(NoticeAnnouncementExportVO reqVO, HttpServletResponse response) throws IOException;

    HashMap<Integer, List<ClassManagementDO>> selectClassManagementList(ClassManagementPageReqVO reqVO);

}
