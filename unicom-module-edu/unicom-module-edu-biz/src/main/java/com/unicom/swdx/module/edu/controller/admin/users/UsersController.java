package com.unicom.swdx.module.edu.controller.admin.users;

import com.unicom.swdx.framework.apilog.core.filter.ApiAccessLogFilter;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;


import com.unicom.swdx.framework.excel.core.util.ExcelUtils;



import com.unicom.swdx.module.edu.controller.admin.users.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.users.UsersDO;
import com.unicom.swdx.module.edu.service.users.UsersService;

@Tag(name = "管理后台 - 用户信息")
@RestController
@RequestMapping("/edu/users")
@Validated
public class UsersController {

    @Resource
    private UsersService usersService;

    @PostMapping("/create")
    @Operation(summary = "创建用户信息")
    @PreAuthorize("@ss.hasPermission('edu:users:create')")
    public CommonResult<Long> createUsers(@Valid @RequestBody UsersSaveReqVO createReqVO) {
        return success(usersService.createUsers(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新用户信息")
    @PreAuthorize("@ss.hasPermission('edu:users:update')")
    public CommonResult<Boolean> updateUsers(@Valid @RequestBody UsersSaveReqVO updateReqVO) {
        usersService.updateUsers(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除用户信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:users:delete')")
    public CommonResult<Boolean> deleteUsers(@RequestParam("id") Long id) {
        usersService.deleteUsers(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:users:query')")
    public CommonResult<UsersRespVO> getUsers(@RequestParam("id") Long id) {
        UsersDO users = usersService.getUsers(id);
        return success(BeanUtils.toBean(users, UsersRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户信息分页")
    @PreAuthorize("@ss.hasPermission('edu:users:query')")
    public CommonResult<PageResult<UsersRespVO>> getUsersPage(@Valid UsersPageReqVO pageReqVO) {
        PageResult<UsersDO> pageResult = usersService.getUsersPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UsersRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户信息 Excel")
    @PreAuthorize("@ss.hasPermission('edu:users:export')")
    public void exportUsersExcel(@Valid UsersPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UsersDO> list = usersService.getUsersPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户信息.xls", "数据", UsersRespVO.class,
                        BeanUtils.toBean(list, UsersRespVO.class));
    }


    @PostMapping("/useraccesslog")
    @PermitAll
    public CommonResult<Boolean> useraccesslog() {

        if(ApiAccessLogFilter.logenable ==false){
            ApiAccessLogFilter.logenable =true;
        }else{
            ApiAccessLogFilter.logenable =false;
        }
        return success(ApiAccessLogFilter.logenable);
    }

}
