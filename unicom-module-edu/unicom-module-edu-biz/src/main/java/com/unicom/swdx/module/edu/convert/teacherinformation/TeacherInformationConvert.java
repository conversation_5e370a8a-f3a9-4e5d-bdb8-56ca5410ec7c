package com.unicom.swdx.module.edu.convert.teacherinformation;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.*;

import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TeacherInformationConvert {

    TeacherInformationConvert INSTANCE = Mappers.getMapper(TeacherInformationConvert.class);

    TeacherInformationDO convert(TeacherInformationCreateReqVO bean);

    TeacherInformationDO convert(TeacherInformationUpdateReqVO bean);

    TeacherInformationRespVO convert(TeacherInformationDO bean);

    List<TeacherInformationRespVO> convertList(List<TeacherInformationDO> bean);

    PageResult<TeacherInformationRespVO> convertPage(PageResult<TeacherInformationDO> bean);

    List<TeacherInformationExcelVO> convertExcel(List<TeacherInformationDO> list);

    List<TeacherInformationExcelVO> convertExportList(List<TeacherInformationRespVO> list);

    TeacherInformationUpdateReqVO convertToUpdateReqVO(TeacherInformationDO teacher);

    //将业务中台接口中返回的 userid 对应的老师表的systemId
    @Mapping(source = "userId", target = "systemId")
    List<TeacherInformationDO> convertDTOList(List<PersonnalRespDTO> personnals);

    List<TeacherInformationImportResultExcelVO> convertList02(List<TeacherInformationImportExcelVO> list);
}
