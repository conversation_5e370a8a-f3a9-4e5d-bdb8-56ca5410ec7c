package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

@ApiModel("管理后台 - 课程更改 Request VO")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CourseChangeReqVO extends CourseChangeBaseVO{

    @ApiModelProperty(value = "申请排班的课程id")
    private Long applyId;

    @ApiModelProperty(value = "更改后的教室id")
    private Integer classroomId;

    @ApiModelProperty(value = "更改后的教室名")
    private String classroom;

    @ApiModelProperty(value = "更改后的老师id")
    private Long teacherId;

    @ApiModelProperty(value = "更改后的老师名")
    private String teacherName;

//    @ApiModelProperty(value = "更改后的日期")
//    private LocalDate date;
//
//    @ApiModelProperty(value = "更改后的午段 0上午-1下午-2晚上")
//    private String period;
//
//    @ApiModelProperty(value = "更改后的时间段")
//    private String time;

    @ApiModelProperty(value = "更改后的时间段对应的单元格id")
    private Long changeId;

    @ApiModelProperty(value = "是否取消授课 0否-1是")
    private Integer isCancel;

    @ApiModelProperty(value = "多教师id")
    private String teacherIdString;

    @ApiModelProperty(value = "是否不同步合班授课调课")
    private Boolean notMergeSync;
}
