package com.unicom.swdx.module.edu.controller.admin.plantemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.math.BigDecimal;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 教学计划模版 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PlanTemplateExcelVO {

    @ExcelProperty("唯一标识符，自增")
    private Long id;

    @ExcelProperty("模版名称")
    private String name;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
