package com.unicom.swdx.module.edu.controller.admin.leavenotification.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 离校报备创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveNotificationCreateReqVO extends LeaveNotificationBaseVO {

    // 继承了 LeaveNotificationBaseVO 中的所有字段
    // 由于是第一次创建，name 字段会由后端自动生成为"第N次离校申请"，所以前端不需要传递

}