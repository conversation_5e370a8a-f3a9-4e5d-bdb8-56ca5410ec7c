package com.unicom.swdx.module.edu.controller.admin.traineegroup.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: TraineeGroupReqVO
 * @Author: lty
 * @Date: 2024/10/17 17:03
 */
@Data
@ApiModel(value = "学员分组请求 VO")
public class TraineeGroupReqVO extends PageParam {

    private Long classId;

    private Long groupId;

    private Integer status;
    private List<Integer> includeColumnIndexes;
    private List<Long> idList;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "排序")
    private Boolean isDesc;

}
