package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
@ApiModel("任课信息 VO")
public class CourseInformationRespVO {

    /**
     * 课程主键
     */
    @ApiModelProperty(value = "课程主键")
    private Long id;

    /**
     * 师资-课程关系表主键
     */
    @ApiModelProperty(value = "师资-课程关系表主键")
    private Long relationId;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    @Size(min = 1, max = 50, message = "课程名称长度为 1-50 个字符")
    private String name;

    /**
     * 课程来源(1-专题课、2-选修课、3-教学活动)
     */
    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课、3-教学活动)")
    private Integer coursesType;

    /**
     * 课程分类
     */
    @ApiModelProperty(value = "课程分类字典ID")
    private Long themeId;

    /**
     * 教学形式
     */
    @ApiModelProperty(value = "教学形式字典ID")
    private Long educateFormId;

    /**
     * 课程分类名称
     */
    @ApiModelProperty(value = "课程分类")
    private String theme;

    /**
     * 教学形式名称
     */
    @ApiModelProperty(value = "教学形式")
    private String educateForm;

    /**
     * 授课次数
     */
    @ApiModelProperty(value = "授课次数")
    private Long lectureTime;

    /**
     * 是否已安排授课
     */
    @ApiModelProperty(value = "是否已安排授课")
    private Boolean isArranged;
}
