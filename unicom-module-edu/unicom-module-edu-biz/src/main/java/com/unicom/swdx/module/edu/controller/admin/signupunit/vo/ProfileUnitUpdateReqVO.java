package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - EduSignUpUnit更新 Request VO")
@Data
@ToString(callSuper = true)
public class ProfileUnitUpdateReqVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    @NotNull(message = "主键id，自增不能为空")
    private Integer id;

    @ApiModelProperty(value = "单位名称")
    private String unitChargePeople;

    @ApiModelProperty(value = "负责人电话")
    private String phone;

    @ApiModelProperty(value = "办公电话")
    private String officePhone;
}
