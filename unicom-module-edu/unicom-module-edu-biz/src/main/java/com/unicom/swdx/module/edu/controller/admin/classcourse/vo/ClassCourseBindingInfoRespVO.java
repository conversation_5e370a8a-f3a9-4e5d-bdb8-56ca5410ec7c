package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 班级课程安排更新 Request VO")
@Data
@ToString(callSuper = true)
public class ClassCourseBindingInfoRespVO {

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "针对更新绑定关系的班级id", required = true)
    @NotNull(message = "请选择绑定操作为新增/删除，true新增，false删除")
    private List<ClassCourseDO> classCourseDOList;

}
