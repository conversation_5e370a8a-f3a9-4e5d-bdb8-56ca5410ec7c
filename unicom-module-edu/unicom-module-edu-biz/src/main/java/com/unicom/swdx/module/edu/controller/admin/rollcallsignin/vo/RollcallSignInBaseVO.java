package com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import com.unicom.swdx.module.edu.enums.rollcallsignin.SignInTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 大课考勤、点名签到信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class RollcallSignInBaseVO {

    @ApiModelProperty(value = "点名签到标题", example = "点名签到")
    private String title;

    @ApiModelProperty(value = "关联的课表 ID", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "关联的班级表 ID", required = true, example = "1")
    @NotNull(message = "关联的班级表 ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "0-大课考勤 1-点名签到", example = "1")
    private Integer type;

    @ApiModelProperty(value = "打卡开始时间", required = true, example = "2020-01-01 00:00:00")
    @NotNull(message = "打卡开始时间不能为空")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime checkStartTime;

    @ApiModelProperty(value = "打卡结束时间", required = true, example = "2020-01-01 00:00:00")
    @NotNull(message = "打卡结束时间不能为空")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime checkEndTime;

    @ApiModelProperty(value = "打卡位置的纬度", required = true, example = "10.2552")
    @NotNull(message = "打卡位置的纬度不能为空")
    private String latitude;

    @ApiModelProperty(value = "打卡位置的经度", required = true, example = "20.25552")
    @NotNull(message = "打卡位置的经度不能为空")
    private String longitude;

    @ApiModelProperty(value = "打卡位置的地址", example = "中国")
    @NotNull(message = "打卡位置的地址不能为空")
    private String address;

    @ApiModelProperty(value = "打卡范围半径", required = true, example = "50.50")
    @NotNull(message = "打卡范围半径不能为空")
    private BigDecimal radius;

    @ApiModelProperty(value = "是否结束签到", example = "true")
    private Boolean ended;
    /**
     * {@link SignInTypeEnum}
     */
    @ApiModelProperty(value = "发起的考勤状态 0-正常 1-撤回状态", example = "0")
    private Integer status;
}
