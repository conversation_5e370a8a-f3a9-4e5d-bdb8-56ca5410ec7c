package com.unicom.swdx.module.edu.controller.admin.todoitems;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsReqVO;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveCreateReqVO;
import com.unicom.swdx.module.edu.service.todoitems.TodoItemsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "app端 - 待办事项")
@RestController
@RequestMapping("/edu/todo-items")
@Validated
public class TodoItemsController {

    @Resource
    private TodoItemsService teacherTodoItemsService;


    @PostMapping("/listForClassAdvisor")
    @ApiOperation("获得班主任待办事项列表")
    // @PreAuthorize("@ss.hasPermission('edu:teacher-todo-items:query')")
    public CommonResult<List<TodoItemsRespVO>> getTeacherTodoItemsList(@Valid @RequestBody TodoItemsReqVO reqVO) {
        List<TodoItemsRespVO> respVOList = teacherTodoItemsService.getTeacherTodoItemsList(reqVO);
        return success(respVOList);
    }

    @GetMapping("/homeListForClassAdvisor")
    @ApiOperation("获得首页班主任待办事项列表")
    // @PreAuthorize("@ss.hasPermission('edu:teacher-todo-items:query')")
    public CommonResult<List<TodoItemsRespVO>> getTeacherTodoItemsHomeList(@RequestParam("classId") Long classId) {
        List<TodoItemsRespVO> respVOList = teacherTodoItemsService.getTeacherTodoItemsHomeList(classId);
        return success(respVOList);
    }

    @PostMapping("/addItems")
    @ApiOperation("新增请假代办")
    public CommonResult<Boolean> addTraineeLeaveTodoItem(@RequestBody TraineeLeaveCreateReqVO reqVO) {
        teacherTodoItemsService.addTraineeLeaveTodoItem(reqVO);
        return success(true);
    }

}
