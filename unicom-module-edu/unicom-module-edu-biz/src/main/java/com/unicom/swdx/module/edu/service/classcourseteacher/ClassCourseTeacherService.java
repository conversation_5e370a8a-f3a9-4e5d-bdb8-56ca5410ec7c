package com.unicom.swdx.module.edu.service.classcourseteacher;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 课程表-教师-授课关系 Service 接口
 *
 * <AUTHOR>
 */
public interface ClassCourseTeacherService {

    /**
     * 创建课程表-教师-授课关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createClassCourseTeacher(@Valid ClassCourseTeacherCreateReqVO createReqVO);

    /**
     * 更新课程表-教师-授课关系
     *
     * @param updateReqVO 更新信息
     */
    void updateClassCourseTeacher(@Valid ClassCourseTeacherUpdateReqVO updateReqVO);

    /**
     * 删除课程表-教师-授课关系
     *
     * @param id 编号
     */
    void deleteClassCourseTeacher(Long id);

    /**
     * 获得课程表-教师-授课关系
     *
     * @param id 编号
     * @return 课程表-教师-授课关系
     */
    ClassCourseTeacherDO getClassCourseTeacher(Long id);

    /**
     * 获得课程表-教师-授课关系列表
     *
     * @param ids 编号
     * @return 课程表-教师-授课关系列表
     */
    List<ClassCourseTeacherDO> getClassCourseTeacherList(Collection<Long> ids);

    /**
     * 获得课程表-教师-授课关系分页
     *
     * @param pageReqVO 分页查询
     * @return 课程表-教师-授课关系分页
     */
    PageResult<ClassCourseTeacherDO> getClassCourseTeacherPage(ClassCourseTeacherPageReqVO pageReqVO);

    /**
     * 获得课程表-教师-授课关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 课程表-教师-授课关系列表
     */
    List<ClassCourseTeacherDO> getClassCourseTeacherList(ClassCourseTeacherExportReqVO exportReqVO);

}
