package com.unicom.swdx.module.edu.controller.admin.options;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;



import com.unicom.swdx.module.edu.controller.admin.options.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.service.options.OptionsService;

@Tag(name = "管理后台 - 选项")
@RestController
@RequestMapping("/edu/options")
@Validated
public class OptionsController {

    @Resource
    private OptionsService optionsService;

    @PostMapping("/create")
    @Operation(summary = "创建选项")
    @PreAuthorize("@ss.hasPermission('edu:options:create')")
    public CommonResult<Long> createOptions(@Valid @RequestBody OptionsSaveReqVO createReqVO) {
        return success(optionsService.createOptions(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新选项")
    @PreAuthorize("@ss.hasPermission('edu:options:update')")
    public CommonResult<Boolean> updateOptions(@Valid @RequestBody OptionsSaveReqVO updateReqVO) {
        optionsService.updateOptions(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除选项")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:options:delete')")
    public CommonResult<Boolean> deleteOptions(@RequestParam("id") Long id) {
        optionsService.deleteOptions(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得选项")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:options:query')")
    public CommonResult<OptionsRespVO> getOptions(@RequestParam("id") Long id) {
        OptionsDO options = optionsService.getOptions(id);
        return success(BeanUtils.toBean(options, OptionsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得选项分页")
    @PreAuthorize("@ss.hasPermission('edu:options:query')")
    public CommonResult<PageResult<OptionsRespVO>> getOptionsPage(@Valid OptionsPageReqVO pageReqVO) {
        PageResult<OptionsDO> pageResult = optionsService.getOptionsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OptionsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出选项 Excel")
    @PreAuthorize("@ss.hasPermission('edu:options:export')")

    public void exportOptionsExcel(@Valid OptionsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OptionsDO> list = optionsService.getOptionsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "选项.xls", "数据", OptionsRespVO.class,
                        BeanUtils.toBean(list, OptionsRespVO.class));
    }

}
