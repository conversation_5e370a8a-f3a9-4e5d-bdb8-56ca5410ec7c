package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
public class ClassTimeTableReqVO {

    private Long classId;
    private Long teacherId;
    private String dateBeg;
    private String dateEnd;
    private Long tenantId;

    /**
     * 用于全校课表获取的当前开班中的班级id
     */
    private List<Long> classIdList;

    //班级显示次序
    private String classOrder;
}
