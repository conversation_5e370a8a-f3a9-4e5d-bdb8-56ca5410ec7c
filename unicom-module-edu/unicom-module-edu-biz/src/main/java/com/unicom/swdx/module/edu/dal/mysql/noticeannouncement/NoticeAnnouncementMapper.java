package com.unicom.swdx.module.edu.dal.mysql.noticeannouncement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementExportVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementPageReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * EduNoticeAnnouncement Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeAnnouncementMapper extends BaseMapperX<NoticeAnnouncementDO> {

//    default PageResult<NoticeAnnouncementDO> selectPage(NoticeAnnouncementPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeAnnouncementDO>()
//                .eqIfPresent(NoticeAnnouncementDO::getPublisher, reqVO.getPublisher())
//                .eqIfPresent(NoticeAnnouncementDO::getTitle, reqVO.getTitle())
//                .eqIfPresent(NoticeAnnouncementDO::getContent, reqVO.getContent())
//                .eqIfPresent(NoticeAnnouncementDO::getIsTop, reqVO.getIsTop())
//                .eqIfPresent(NoticeAnnouncementDO::getIsPublish, reqVO.getIsPublish())
//                .eqIfPresent(NoticeAnnouncementDO::getStatus, reqVO.getStatus())
//                .orderByDesc(NoticeAnnouncementDO::getId));
//    }


    /**
     * 插入url
     * @param noticeAnnouncementUrlDO
     */
    void setNoticeAnnouncementUrl(@Param("reqVO") NoticeAnnouncementUrlDO noticeAnnouncementUrlDO);

    /**
     * 删除url
     * @param id
     */
    void deleteNoticeAnnouncementUrl(@Param("id") Integer id);

    /**
     * 分页查询
     * @param buildPage
     * @param pageReqVO
     * @return
     */
    List<NoticeAnnouncementDO> selectPageList(IPage<NoticeAnnouncementDO> buildPage, @Param("pageReqVO") NoticeAnnouncementPageReqVO pageReqVO,@Param("classId") Long classId);

    /**
     * 分页查询
     * @param id
     * @return
     */

    List<NoticeAnnouncementUrlDO> selectByOneId(@Param("id") Integer id);

    /**
     * 置顶 1- 置顶  2-非置顶
     * @param id
     */
    void isTopNoticeAnnouncement(@Param("id") Integer id, @Param("isTop") Integer isTop, @Param("localDateTime") LocalDateTime localDateTime);

    /**
     * 非置顶
     * @param id
     */
    void notIsTopNoticeAnnouncement(@Param("id") Integer id, @Param("isTop") Integer isTop);

    /**
     * 上下架 1-上架  2 -下架
     * @param id
     */
    void isUpOrDownNoticeAnnouncement(@Param("id") Integer id, @Param("status") Integer status);

    /**
     * 发布 批量
     * @param id
     */
    void updatePublishById(@Param("id") Integer id, @Param("localDateTime") LocalDateTime localDateTime);
    /**
     * 导出
     * @param pageReqVO
     * @return
     */
    List<NoticeAnnouncementDO> selectListInfo(@Param("pageReqVO") NoticeAnnouncementExportVO pageReqVO);


    List<NoticeAnnouncementUrlDO> selectByOneIds(@Param("idList") List<Integer> noticeAnnouncementIds);

    List<NoticeAnnouncementDO> selectByClassIds(@Param("classIds") List<Long> classIds);
}
