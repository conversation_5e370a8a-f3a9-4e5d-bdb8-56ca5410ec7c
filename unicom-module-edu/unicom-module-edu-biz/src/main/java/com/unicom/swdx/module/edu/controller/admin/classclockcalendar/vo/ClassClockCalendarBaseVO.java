package com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 班级考勤日历 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassClockCalendarBaseVO {

    @ApiModelProperty(value = "早餐考勤，0-开，1-关")
    private Integer breakfast;

    @ApiModelProperty(value = "午餐考勤，0-开，1-关")
    private Integer lunch;

    @ApiModelProperty(value = "晚餐考勤，0-开，1-关")
    private Integer dinner;

    @ApiModelProperty(value = "住宿考勤，0-开，1-关")
    private Integer putUp;

    @ApiModelProperty(value = "考勤日期")
    private LocalDate clockDate;

    @ApiModelProperty(value = "是否节假日，0-是，1-否")
    private Integer isHoliday;

    @ApiModelProperty(value = "班级id", required = true)
    @NotNull(message = "班级id不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

}
