package com.unicom.swdx.module.edu.controller.admin.electiverelease;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.courseinfo.CourseInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesReqVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesRespVO;
import com.unicom.swdx.module.edu.convert.electiverelease.ElectiveReleaseConvert;
import com.unicom.swdx.module.edu.service.electiverelease.ElectiveReleaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "管理后台 - 选修课发布信息")
@RestController
@RequestMapping("/edu/elective-release")
@Validated
public class ElectiveReleaseController {

    @Resource
    private ElectiveReleaseService electiveReleaseService;

    @PostMapping("/create")
    @ApiOperation("创建选修课发布信息")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:create')")
    public CommonResult<Long> createElectiveRelease(@Valid @RequestBody ElectiveReleaseCreateReqVO createReqVO) {
        return success(electiveReleaseService.createElectiveRelease(createReqVO));
    }

    @PostMapping("/delete")
    @ApiOperation("删除选修课发布信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-release:delete')")
    public CommonResult<Boolean> deleteElectiveRelease(@RequestParam("id") Long id) {
        electiveReleaseService.deleteElectiveRelease(id);
        return success(true);
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除选修课发布信息（支持勾选和条件）")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:delete')")
    public CommonResult<Boolean> batchDeleteElectiveRelease(@Valid @RequestBody ElectiveReleasePageReqVO reqVO) {
        electiveReleaseService.batchDeleteElectiveRelease(reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得选修课发布信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<ElectiveReleaseGetRespVO> getElectiveRelease(@RequestParam("id") Long id) {
        ElectiveReleaseGetRespVO electiveRelease = electiveReleaseService.getElectiveRelease(id);
        return success(electiveRelease);
    }

    @PostMapping("/page")
    @ApiOperation("获得选修课发布信息分页")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<PageResult<ElectiveReleasePageRespVO>> getElectiveReleasePage(@Valid @RequestBody ElectiveReleasePageReqVO pageVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        pageVO.setIds(null);
        PageResult<ElectiveReleasePageRespVO> pageResult = electiveReleaseService.getElectiveReleasePage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/get-trainee-release-list")
    @ApiOperation("学员移动端-选修课-学员相关待选或已选的的选课发布信息")
    @ApiImplicitParam(name = "status", value = "0-待选的选修课发布 1-已选的选修课发布", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<List<AppElectiveReleaseTraineeRespVO>> getTraineeElectiveReleaseList(@RequestParam("status") Integer status) {
        List<AppElectiveReleaseTraineeRespVO> list = electiveReleaseService.getElectiveReleaseAndCoursesListByTraineeIdAndStatus(status);
        return success(list);
    }

    @GetMapping("/list-app")
    @ApiOperation("班主任移动端-获得选修课发布信息列表")
    @ApiImplicitParam(name = "classId", value = "指定班级ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<List<ElectiveReleasePageRespVO>> getElectiveReleaseListForApp(@RequestParam("classId") Long classId) {
        List<ElectiveReleasePageRespVO> list = electiveReleaseService.getElectiveReleaseListByClassId(classId);
        return success(list);
    }

    @PostMapping("/export-excel")
    @ApiOperation("导出选修课发布信息(包含选课人数信息) Excel（支持勾选和条件）")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:export')")
    @OperateLog(type = EXPORT)
    public void exportElectiveReleaseExcel(@Valid @RequestBody ElectiveReleasePageReqVO reqVO,
                                           HttpServletResponse response) throws IOException {
        List<ElectiveReleaseExcelVO> list = electiveReleaseService.getExportElectiveReleaseExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "选修课发布信息.xls",
                "数据", ElectiveReleaseExcelVO.class, list, reqVO.getIncludeColumnIndexes());

    }

    @PostMapping("/export-simple-excel")
    @ApiOperation("导出选修课发布信息(不包含选课人数信息) Excel（支持勾选和条件）")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:export')")
    @OperateLog(type = EXPORT)
    public void exportSimpleElectiveReleaseExcel(@Valid @RequestBody ElectiveReleasePageReqVO reqVO,
                                                 HttpServletResponse response) throws IOException {
        // 屏蔽勾选导出
        reqVO.setIds(null);
        List<ElectiveReleaseExcelVO> list = electiveReleaseService.getExportElectiveReleaseExcel(reqVO);
        List<ElectiveReleaseSimpleExcelVO> simpleList = ElectiveReleaseConvert.INSTANCE.convertList03(list);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "选修课发布信息.xls",
                "数据", ElectiveReleaseSimpleExcelVO.class, simpleList, reqVO.getIncludeColumnIndexes());

    }

    @PostMapping("/selected-info-page")
    @ApiOperation("教务管理-选修课管理-获得选修课发布已选人数分页列表信息")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<PageResult<ElectiveReleaseSelectedInfoRespVO>> getSelectedInfoPage(@Valid @RequestBody ElectiveReleaseSelectedInfoPageReqVO pageVO) {
        PageResult<ElectiveReleaseSelectedInfoRespVO> pageResult = electiveReleaseService.getSelectedInfoPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/list-simple-classes-info")
    @ApiOperation("教务管理-选修课管理-获得选修课发布的班级下拉信息")
    @ApiImplicitParam(name = "releaseId", value = "发布ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<List<ClassInfoVO>> getSimpleClassesInfoList(@RequestParam("releaseId") Long releaseId) {
        List<ClassInfoVO> list = electiveReleaseService.getSimpleClassesInfoList(releaseId);
        return success(list);
    }

    @GetMapping("/list-simple-courses-info")
    @ApiOperation("教务管理-选修课管理-获得选修课发布的课程下拉信息")
    @ApiImplicitParam(name = "releaseId", value = "发布ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<List<CourseInfoVO>> getSimpleCoursesInfoList(@RequestParam("releaseId") Long releaseId) {
        List<CourseInfoVO> list = electiveReleaseService.getSimpleCoursesInfoList(releaseId);
        return success(list);
    }

    @PostMapping("/export-selected-info-excel")
    @ApiOperation("教务管理-选修课管理-导出选修课发布已选人数分页列表信息")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:export')")
    @OperateLog(type = EXPORT)
    public void getExportSelectedInfoList(@Valid @RequestBody ElectiveReleaseSelectedInfoPageReqVO reqVO,
                                          HttpServletResponse response) throws IOException {
        List<ElectiveReleaseSelectedInfoExcelVO> list = electiveReleaseService.getExportSelectedInfoList(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "已选人数信息.xls",
                "数据", ElectiveReleaseSelectedInfoExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/release-course-page")
    @ApiOperation("查看选修课发布信息里面的选修课程信息分页")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:query')")
    public CommonResult<PageResult<ElectiveReleaseCoursesRespVO>> getReleaseCoursePage(@Valid @RequestBody ElectiveReleaseCoursesReqVO reqVO) {
        PageResult<ElectiveReleaseCoursesRespVO> pageResult = electiveReleaseService.getReleaseCoursePage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/export-release-course-excel")
    @ApiOperation("导出选修课发布信息里面的选修课程信息")
    @PreAuthorize("@ss.hasPermission('edu:elective-release:export')")
    @OperateLog(type = EXPORT)
    public void exportReleaseCourseExcel(@Valid @RequestBody ElectiveReleaseCoursesReqVO reqVO,
                                         HttpServletResponse response) throws IOException {
        electiveReleaseService.exportReleaseCourseExcel(reqVO, response);
    }
}
