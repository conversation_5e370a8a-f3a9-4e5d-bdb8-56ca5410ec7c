package com.unicom.swdx.module.edu.service.classcommittee;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeRespVO;
import com.unicom.swdx.module.edu.convert.classcommittee.ClassCommitteeConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcommittee.ClassCommitteeDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcommittee.ClassCommitteeMapper;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.CLASS_COMMITTEE_NOT_EXISTS;

/**
 * @ClassName: TraineeGroupServiceImpl
 * @Author: lty
 * @Date: 2024/10/17 11:29
 */
@Service
@Validated
@Slf4j
public class ClassCommitteeServiceImpl extends ServiceImpl<ClassCommitteeMapper, ClassCommitteeDO> implements ClassCommitteeService {

    @Resource
    private TraineeService traineeService;
    @Override
    public Long addClassCommittee(ClassCommitteeReqVO reqVO) {

        //校验班委名字是否重复
        ClassCommitteeDO classCommitteeDO = this.baseMapper.selectOne(new LambdaQueryWrapper<ClassCommitteeDO>()
                .eq(ClassCommitteeDO::getClassCommitteeName, reqVO.getClassCommitteeName())
                .eq(ClassCommitteeDO::getClassId, reqVO.getClassId()));

        if (classCommitteeDO != null){
            //抛异常
        }

        Long tenantId = SecurityFrameworkUtils.getTenantId();

        ClassCommitteeDO classCommittee = ClassCommitteeDO.builder()
                .classId(reqVO.getClassId())
                .classCommitteeName(reqVO.getClassCommitteeName())
                .nameShow(reqVO.getNameShow())
                .tenantId(tenantId.intValue())
                .build();

        this.baseMapper.insert(classCommittee);

        return classCommittee.getId();
    }

    @Override
    public Integer editClassCommittee(ClassCommitteeReqVO reqVO) {

        //查询是否存在
        ClassCommitteeDO classCommitteeDO = this.baseMapper.selectById(reqVO.getId());
        if (classCommitteeDO == null){
            throw exception(CLASS_COMMITTEE_NOT_EXISTS);
        }
        classCommitteeDO.setClassCommitteeName(reqVO.getClassCommitteeName());
        classCommitteeDO.setNameShow(reqVO.getNameShow());

        return this.baseMapper.updateById(classCommitteeDO);
    }

    @Override
    public Boolean deleteClassCommittee(Long id) {

        //查询是否存在
        ClassCommitteeDO classCommitteeDO = this.baseMapper.selectById(id);
        if (classCommitteeDO == null){
            throw exception(CLASS_COMMITTEE_NOT_EXISTS);
        }

        //查询绑定该班委的学员
        List<TraineeDO> list = traineeService.selectTraineeByClassCommitteeId(id);
        if (!list.isEmpty()){
            //取消绑定班委
            list.forEach(traineeDO -> {
                traineeDO.setClassCommitteeId(0L);
            });
            traineeService.updateBatchById(list);
        }

        this.baseMapper.deleteById(id);

        return true;
    }

    @Override
    public List<Map<String, String>> getClassCommittee(Long classId) {

        List<ClassCommitteeDO> list = this.baseMapper.selectList(new LambdaQueryWrapper<ClassCommitteeDO>()
                .eq(ClassCommitteeDO::getClassId, classId)
                .orderBy(true, true,ClassCommitteeDO::getId));
        List<Map<String, String>> list1 = list.stream().map(classCommitteeDO -> {
            Map<String, String> map = new HashMap<>();
            map.put("value", classCommitteeDO.getId().toString());
            map.put("label", classCommitteeDO.getClassCommitteeName());
            return map;
        }).collect(Collectors.toList());
        Map<String, String> map = new HashMap<>();
        map.put("value", "0");
        map.put("label", "未分配");
        list1.add(map);
        return list1;
    }

    @Override
    public List<ClassCommitteeRespVO> getClassCommitteeList(Long classId, String name) {

        List<ClassCommitteeDO> list = this.baseMapper.selectList(new LambdaQueryWrapper<ClassCommitteeDO>()
                .eq(ClassCommitteeDO::getClassId, classId)
                .like(StrUtil.isNotBlank(name), ClassCommitteeDO::getClassCommitteeName, name)
                .orderBy(true, true, ClassCommitteeDO::getId));
        List<ClassCommitteeRespVO> list1 = ClassCommitteeConvert.INSTANCE.covertList(list);

        for (int i = 0; i < list1.size(); i++) {

            list1.get(i).setIndex(i+1);
        }


        return list1;

    }
}
