package com.unicom.swdx.module.edu.convert.xcxmsg;

import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.*;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigRespVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveProcessDO;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface XcxMsgConvert {

    XcxMsgConvert INSTANCE = Mappers.getMapper(XcxMsgConvert.class);

    XcxMsgConfigRespVO convert(XcxMsgConfigDO configDO);

    XcxMsgConfigDO convert(XcxMsgConfigUpdateReqVO reqVO);

}
