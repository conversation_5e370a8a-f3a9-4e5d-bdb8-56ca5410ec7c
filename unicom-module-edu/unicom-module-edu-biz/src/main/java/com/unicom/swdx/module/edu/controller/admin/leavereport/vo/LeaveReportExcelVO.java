package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 离校报备 Excel VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class LeaveReportExcelVO {

    @ExcelProperty(value = "序号", index = 0)
    private Integer serialNumber;

    @ExcelProperty(value = "离校报备名称", index = 1)
    private String name;

    @ExcelProperty(value = "放假时间", index = 2)
    private String holidayTime;

    @ExcelProperty(value = "班级名称", index = 3)
    private String classname;

    @ExcelProperty(value = "班主任", index = 4)
    private String classTeacher;

    @ExcelProperty(value = "状态", index = 5)
    private String status;

    @ExcelProperty(value = "发布时间", index = 6)
    private LocalDateTime createTime;

    @ExcelIgnore
    private LocalDateTime startTime;

    @ExcelIgnore
    private LocalDateTime endTime;

    @ExcelIgnore
    private Integer totalStudents;

    @ExcelIgnore
    private Integer leftStudents;

    @ExcelIgnore
    private Integer notLeftStudents;

    @ExcelIgnore
    private String leaveRate;

}
