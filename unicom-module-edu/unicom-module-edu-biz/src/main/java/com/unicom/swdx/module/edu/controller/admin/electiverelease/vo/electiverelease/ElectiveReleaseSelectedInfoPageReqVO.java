package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 管理后台 - 选修课发布信息已选人数列表分页 Request VO
 */
@ApiModel("管理后台 - 选修课发布信息已选人数列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleaseSelectedInfoPageReqVO extends PageParam {

    @ApiModelProperty(value = "发布ID", required = true, example = "1024")
    @NotNull(message = "发布ID不能为空")
    private Long releaseId;

    @ApiModelProperty(value = "是否是班主任-限定该班主任班级的发布信息", example = "false-非班主任(默认) true-班主任")
    private Boolean isClassMaster;

    @ApiModelProperty(hidden = true)
    private List<Long> classIdList;

    @ApiModelProperty(value = "选修课名称", example = "选修课")
    private String courseName;

    @ApiModelProperty(value = "选修课ID", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "班次名称", example = "1班")
    private String className;

    @ApiModelProperty(value = "班次ID", example = "1")
    private Long classId;

    @ApiModelProperty(value = "学员姓名", example = "王小明")
    private String traineeName;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}
