package com.unicom.swdx.module.edu.controller.admin.todoitems.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("小程序 - 班主任待办事项Request VO")
@Data
@ToString(callSuper = true)
public class TodoItemsReqVO {

    @ApiModelProperty(value = "待办状态列表，0-未办 1-已办", example = "[0,1]")
    @NotNull(message = "待办状态列表不能为空")
    private List<Integer> statusList;

    @ApiModelProperty(value = "待办事项类型编码(0请假申请、1报名确认)", example = "0")
    private Integer type;

    @ApiModelProperty(value = "班级ID", example = "1")
    @NotNull(message = "班级ID不能为空")
    private Long classId;

    @ApiModelProperty(value = "开始时间", example = "2022-01-01 00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间", example = "2022-01-02 23:59:59")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDate;
}
