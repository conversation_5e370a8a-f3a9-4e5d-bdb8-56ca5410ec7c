package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @ClassName: TraineeLeaveBaseVO
 * @Author: youxiaoyan
 * @Date: 2024/11/5
 */
@Data
@ApiModel(value = "学员请假 base VO")
public class TraineeLeaveBaseVO {

//    @ApiModelProperty(value = "学员ID")
//    private Long traineeId;

    @ApiModelProperty(value = "学员请假标题")
    private String title;

    @ApiModelProperty(value = "班级ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "请假类型", required = true)
    private Integer leaveType;

    @ApiModelProperty(value = "请假开始时间", required = true)
    private String startTimeStr;

    @ApiModelProperty(value = "请假结束时间", required = true)
    private String endTimeStr;

    @ApiModelProperty(value = "请假天数", required = true)
    private Float days;

    @ApiModelProperty(value = "请假事由")
    private String reason;

    @ApiModelProperty(value = "附件")
    private String accessory;

    @ApiModelProperty(value = "学员id")
    private Long traineeId;

    public void setStartTimeStr(String startTimeStr) {
        if(Objects.nonNull(startTimeStr) && StringUtils.isNotBlank(startTimeStr) && startTimeStr.length() > 19){
            this.startTimeStr = StringUtils.substring(startTimeStr, 0, 19);
        }else {
            this.startTimeStr = startTimeStr;
        }
    }

    public void setEndTimeStr(String endTimeStr) {
        if(Objects.nonNull(endTimeStr) && StringUtils.isNotBlank(endTimeStr) && endTimeStr.length() > 19){
            this.endTimeStr = StringUtils.substring(endTimeStr, 0, 19);
        }else {
            this.endTimeStr = endTimeStr;
        }
    }

}
