package com.unicom.swdx.module.edu.dal.mysql.plandetail;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.plandetail.PlanDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.plandetail.vo.*;

/**
 * 教学计划详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanDetailMapper extends BaseMapperX<PlanDetailDO> {

    default PageResult<PlanDetailDO> selectPage(PlanDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlanDetailDO>()
                .eqIfPresent(PlanDetailDO::getPlanId, reqVO.getPlanId())
                .betweenIfPresent(PlanDetailDO::getDate, reqVO.getDate())
                .eqIfPresent(PlanDetailDO::getPeriod, reqVO.getPeriod())
                .betweenIfPresent(PlanDetailDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(PlanDetailDO::getEndTime, reqVO.getEndTime())
                .orderByDesc(PlanDetailDO::getId));
    }

    default List<PlanDetailDO> selectList(PlanDetailExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PlanDetailDO>()
                .eqIfPresent(PlanDetailDO::getPlanId, reqVO.getPlanId())
                .betweenIfPresent(PlanDetailDO::getDate, reqVO.getDate())
                .eqIfPresent(PlanDetailDO::getPeriod, reqVO.getPeriod())
                .betweenIfPresent(PlanDetailDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(PlanDetailDO::getEndTime, reqVO.getEndTime())
                .orderByDesc(PlanDetailDO::getId));
    }

}
