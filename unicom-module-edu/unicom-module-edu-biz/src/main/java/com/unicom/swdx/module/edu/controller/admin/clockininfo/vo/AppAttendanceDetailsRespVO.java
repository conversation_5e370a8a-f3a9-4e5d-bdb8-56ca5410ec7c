package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 班主任移动端-班级考勤 Resp VO
 * @date 2024-11-12
 */
@ApiModel("班主任移动端-班级考勤 Resp VO")
@Data
public class AppAttendanceDetailsRespVO {

    @ApiModelProperty(value = "排课表id", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "上课午别 0-上午 1-下午 2-晚上", example = "1")
    private Integer coursePeriod;

    @ApiModelProperty(value = "上课时间", example = "2024-10-10 09:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classStartTime;

    @ApiModelProperty(value = "考勤日期", example = "2024-10-10")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate date;

    @ApiModelProperty(value = "班级id", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "考勤类型 0-到课 1-就餐 2-住宿", example = "0")
    private Integer type;

    @ApiModelProperty(value = "就餐时间段 0-早餐 1-午餐 2-晚餐", example = "1")
    private Integer mealPeriod;

    @ApiModelProperty(value = "应到人数", example = "1")
    private Integer attendanceExpected;

    @ApiModelProperty(value = "实到人数", example = "1")
    private Integer attendanceActual;

    @ApiModelProperty(value = "未到到人数", example = "1")
    private Integer attendanceAbsent;

    @ApiModelProperty(value = "请假人数", example = "1")
    private Integer attendanceLeave;

    @ApiModelProperty(value = "考勤时间范围", example = "1")
    private String checkTime;

}
