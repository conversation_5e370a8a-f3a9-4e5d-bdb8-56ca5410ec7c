package com.unicom.swdx.module.edu.dal.mysql.plantemplateconfig;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplateconfig.PlanTemplateConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.*;

/**
 * 教学计划模版配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanTemplateConfigMapper extends BaseMapperX<PlanTemplateConfigDO> {

    default PageResult<PlanTemplateConfigDO> selectPage(PlanTemplateConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlanTemplateConfigDO>()
                .eqIfPresent(PlanTemplateConfigDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(PlanTemplateConfigDO::getDayOfWeek, reqVO.getDayOfWeek())
                .eqIfPresent(PlanTemplateConfigDO::getPeriod, reqVO.getPeriod())
                .betweenIfPresent(PlanTemplateConfigDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(PlanTemplateConfigDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(PlanTemplateConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlanTemplateConfigDO::getId));
    }

    default List<PlanTemplateConfigDO> selectList(PlanTemplateConfigExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PlanTemplateConfigDO>()
                .eqIfPresent(PlanTemplateConfigDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(PlanTemplateConfigDO::getDayOfWeek, reqVO.getDayOfWeek())
                .eqIfPresent(PlanTemplateConfigDO::getPeriod, reqVO.getPeriod())
                .betweenIfPresent(PlanTemplateConfigDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(PlanTemplateConfigDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(PlanTemplateConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlanTemplateConfigDO::getId));
    }

}
