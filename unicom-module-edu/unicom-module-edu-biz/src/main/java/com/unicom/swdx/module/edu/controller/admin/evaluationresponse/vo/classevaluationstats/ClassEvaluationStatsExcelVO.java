package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 班次评估统计-导出 Excel VO
 * @date 2024-11-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class ClassEvaluationStatsExcelVO {

    @ExcelProperty(value = "班次编码")
    private String classNameCode;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "班主任")
    private String teacherName;

    @ExcelProperty(value = "开班日期")
    private LocalDateTime classOpenTime;

    @ExcelProperty(value = "结业日期")
    private LocalDateTime completionTime;

    @ExcelProperty(value = "平均参评率")
    private String averageRatioStr;

    @ExcelProperty(value = "平均分")
    private String averageScoreStr;
}
