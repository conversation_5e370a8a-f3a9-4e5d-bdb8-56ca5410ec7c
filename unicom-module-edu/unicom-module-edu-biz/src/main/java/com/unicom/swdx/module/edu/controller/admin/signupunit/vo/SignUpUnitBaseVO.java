package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* EduSignUpUnit Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SignUpUnitBaseVO {

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位管理员用户名")
    private String username;

    @ApiModelProperty(value = "单位分类")
    private Integer unitClassification;

    @ApiModelProperty(value = "单位责任人")
    private String unitChargePeople;

    @ApiModelProperty(value = "负责人电话")
    private String phone;

    @ApiModelProperty(value = "状态，1-启用，2-禁用")
    private Integer status;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "名额人数")
    private Integer capacity;

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "是否限制人数，0-限制，1-不限制")
    private Integer isRestrict;

    @ApiModelProperty(value = "模版，模版-1，默认-0")
    private Integer template;

    @ApiModelProperty(value = "办公电话")
    private String officePhone;
}
