package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel("学员移动端 - 选修课发布信息(包含课程信息) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppElectiveReleaseTraineeRespVO extends ElectiveReleasePageSimpleRespVO {

    @ApiModelProperty(value = "选修课信息列表，包含课程名称、教师名称、教室ID、名称")
    private List<ElectiveReleaseCoursesSubRespVO> coursesList;

}
