package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 班级课程安排 Response VO")
@Data
@ToString(callSuper = true)
public class ClassCourseByClassManagementRespVO {

    @ApiModelProperty(value = "班级Id")
    private Long classId;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "班主任id")
    private Long classTeacherLeadId;

    @ApiModelProperty(value = "班主任姓名")
    private String classTeacherLeadName;

    //todo 确认数据来源
    @ApiModelProperty(value = "教务老师id")
    private String eduTeacherIds;

    //todo 确认数据来源
    @ApiModelProperty(value = "教务老师姓名")
    private String eduTeacherName;

    @ApiModelProperty(value = "人数")
    private Integer classPeopleCount;

    @ApiModelProperty(value = "班次类型  1基本培训班、2省内委托班、3省外委托班、4会议活动班、5其他")
    private Integer classType;

    @ApiModelProperty(value = "班次类型字典名称")
    private String classTypeDictName;

    //todo 确认数据来源
    @ApiModelProperty(value = "办学地点")
    private String schoolLocation;

    @ApiModelProperty(value = "课程安排")
    private List<ClassCourseRespVO> classCourseVOList;

    /**
     * 开班时间
     */
    private LocalDateTime classOpenTime;
    /**
     * 结业时间
     */
    private LocalDateTime completionTime;




}
