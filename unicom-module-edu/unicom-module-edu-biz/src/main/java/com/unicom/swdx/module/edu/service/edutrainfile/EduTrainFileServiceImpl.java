package com.unicom.swdx.module.edu.service.edutrainfile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageCreateVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageRespVO;
import com.unicom.swdx.module.edu.convert.edutrainfile.EduTrainFileConvert;
import com.unicom.swdx.module.edu.dal.dataobject.edutrainfile.EduTrainFileDO;
import com.unicom.swdx.module.edu.dal.mysql.edutrainFile.EduTrainFileMapper;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 * @since 2024-12-26 15:13:46
 */
@Service
@Slf4j
public class EduTrainFileServiceImpl extends ServiceImpl<EduTrainFileMapper, EduTrainFileDO> implements EduTrainFileService {

    @Autowired
    private EduTrainFileMapper eduTrainFileMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public PageResult<EduTrainFilePageRespVO> findPage(EduTrainFilePageReqVO pageVo) {

        PageResult<EduTrainFileDO> eduTrainFileDOPageResult = eduTrainFileMapper.selectPage(pageVo, new LambdaQueryWrapperX<EduTrainFileDO>()
                .likeIfPresent(EduTrainFileDO::getTitle, pageVo.getTitle()).orderByDesc(EduTrainFileDO::getPublishTime));

        List<EduTrainFilePageRespVO> eduTrainFilePageRespVOS = EduTrainFileConvert.INSTANCE.convertList(eduTrainFileDOPageResult.getList());

        for (EduTrainFilePageRespVO eduTrainFilePageRespVO : eduTrainFilePageRespVOS) {
            eduTrainFilePageRespVO.setPublishTime(eduTrainFilePageRespVO.getPublishTime().split("\\.")[0]);
        }

        return new PageResult<>(eduTrainFilePageRespVOS,eduTrainFileDOPageResult.getTotal());
    }

    @Override
    public List<EduTrainFilePageRespVO> listForHome() {
        List<EduTrainFileDO> list = eduTrainFileMapper.listForHome();
        return EduTrainFileConvert.INSTANCE.convertList(list);
    }

    @Override
    public List<EduTrainFileDO> findList(EduTrainFilePageReqVO pageVo){
        List<EduTrainFileDO> eduTrainFileDOS = eduTrainFileMapper.selectList(new LambdaQueryWrapperX<EduTrainFileDO>()
                .likeIfPresent(EduTrainFileDO::getTitle, pageVo.getTitle()).orderByDesc(EduTrainFileDO::getPublishTime));
        return eduTrainFileDOS;
    }

    @Override
    public EduTrainFileDO selectById(Long id) {
        return eduTrainFileMapper.selectById(id);
    }

    @Override
    public boolean add(EduTrainFilePageCreateVO eduTrainFilePageCreateVO) {
        EduTrainFileDO eduTrainFileDO = new EduTrainFileDO();
        eduTrainFileDO.setFile(eduTrainFilePageCreateVO.getFile());
        eduTrainFileDO.setTitle(eduTrainFilePageCreateVO.getTitle());
        AdminUserRespDTO user = adminUserApi.getUser(getLoginUserId()).getData();
        eduTrainFileDO.setUserId(getLoginUserId());
        eduTrainFileDO.setUserName(user.getNickname());

        return save(eduTrainFileDO);
    }

    @Override
    public boolean update(EduTrainFilePageCreateVO eduTrainFilePageCreateVO) {
        EduTrainFileDO eduTrainFileDO = selectById(eduTrainFilePageCreateVO.getId());
        eduTrainFileDO.setFile(eduTrainFilePageCreateVO.getFile());
        eduTrainFileDO.setTitle(eduTrainFilePageCreateVO.getTitle());


        DateTimeFormatter formatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        eduTrainFileDO.setPublishTime(LocalDateTime.now().format(formatter));
        eduTrainFileDO.setUpdateTime(LocalDateTime.now());
        return updateById(eduTrainFileDO);
    }

    @Override
    public int deleteById(Long id) {
        return eduTrainFileMapper.deleteById(id);
    }
}
