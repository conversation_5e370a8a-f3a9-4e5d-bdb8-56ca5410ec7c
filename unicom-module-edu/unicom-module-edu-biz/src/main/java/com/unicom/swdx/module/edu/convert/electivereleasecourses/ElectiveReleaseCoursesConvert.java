package com.unicom.swdx.module.edu.convert.electivereleasecourses;

import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesExcelVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesRespVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubBaseVO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 选修课发布课程关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveReleaseCoursesConvert {

    ElectiveReleaseCoursesConvert INSTANCE = Mappers.getMapper(ElectiveReleaseCoursesConvert.class);


    List<ElectiveReleaseCoursesDO> convertList(List<ElectiveReleaseCoursesSubBaseVO> coursesList);

    List<ElectiveReleaseCoursesExcelVO> convertList02(List<ElectiveReleaseCoursesRespVO> list);
}
