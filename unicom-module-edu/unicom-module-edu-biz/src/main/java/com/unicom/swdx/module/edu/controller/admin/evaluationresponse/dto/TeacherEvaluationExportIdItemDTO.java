package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description: 教师评估-导出 指定导出时 需要指定class_course_id和教师id两个字段确定一行
 * @date 2025-01-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeacherEvaluationExportIdItemDTO {

    @ApiModelProperty(value = "排课表ID", example = "1")
    @NotNull(message = "排课表ID不能为空")
    private Long classCourseId;

    @ApiModelProperty(value = "课程ID", example = "1")
    // @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @ApiModelProperty(value = "教师ID", example = "1")
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;
}
