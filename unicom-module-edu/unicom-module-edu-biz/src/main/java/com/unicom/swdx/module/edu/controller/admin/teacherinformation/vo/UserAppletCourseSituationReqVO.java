package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UserAppletCourseSituationReqVO extends UserAppletBaseVO {


    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "页码不能为空")
    private Integer pageNo;

    /**
     * 分页大小
     */
    @ApiModelProperty(value = "分页大小", required = true)
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;


}
