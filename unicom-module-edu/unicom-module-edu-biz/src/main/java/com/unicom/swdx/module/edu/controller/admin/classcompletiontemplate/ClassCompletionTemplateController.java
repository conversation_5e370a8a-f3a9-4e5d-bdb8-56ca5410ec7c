package com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.module.edu.convert.classcompletiontemplate.ClassCompletionTemplateConvert;
import com.unicom.swdx.module.edu.service.classcompletiontemplate.ClassCompletionTemplateService;

@Api(tags = "管理后台 - 班级结业考核模版设置")
@RestController
@RequestMapping("/edu/class-completion-template")
@Validated
public class ClassCompletionTemplateController {

    @Resource
    private ClassCompletionTemplateService classCompletionTemplateService;

    @PostMapping("/create")
    @ApiOperation("创建结业考核模版设置")
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:create')")
    public CommonResult<List<Long>> createClassCompletionTemplate(@Valid @RequestBody ClassCompletionTemplateCreateReqVO createReqVO) {
        return success(classCompletionTemplateService.createClassCompletionTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新结业考核模版设置")
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:update')")
    public CommonResult<Boolean> updateClassCompletionTemplate(@Valid @RequestBody ClassCompletionTemplateUpdateReqVO updateReqVO) {
        classCompletionTemplateService.updateClassCompletionTemplate(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除结业考核模版设置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:delete')")
    public CommonResult<Boolean> deleteClassCompletionTemplate(@RequestParam("id") Long id) {
        classCompletionTemplateService.deleteClassCompletionTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得结业考核模版设置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:query')")
    public CommonResult<ClassCompletionTemplateRespVO> getClassCompletionTemplate(@RequestParam("id") Integer id) {
        ClassCompletionTemplateDO classCompletionTemplate = classCompletionTemplateService.getClassCompletionTemplate(id);
        return success(ClassCompletionTemplateConvert.INSTANCE.convert(classCompletionTemplate));
    }

    @GetMapping("/list")
    @ApiOperation("获得结业考核模版设置列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:query')")
    public CommonResult<List<ClassCompletionTemplateRespVO>> getClassCompletionTemplateList(@RequestParam("ids") Collection<Integer> ids) {
        List<ClassCompletionTemplateDO> list = classCompletionTemplateService.getClassCompletionTemplateList(ids);
        return success(ClassCompletionTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得结业考核模版设置分页")
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:query')")
    public CommonResult<PageResult<ClassCompletionTemplateRespVO>> getClassCompletionTemplatePage(@Valid ClassCompletionTemplatePageReqVO pageVO) {
        PageResult<ClassCompletionTemplateDO> pageResult = classCompletionTemplateService.getClassCompletionTemplatePage(pageVO);
        return success(ClassCompletionTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出结业考核模版设置 Excel")
    @PreAuthorize("@ss.hasPermission('edu:class-completion-template:export')")
    @OperateLog(type = EXPORT)
    public void exportClassCompletionTemplateExcel(@Valid ClassCompletionTemplateExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ClassCompletionTemplateDO> list = classCompletionTemplateService.getClassCompletionTemplateList(exportReqVO);
        // 导出 Excel
        List<ClassCompletionTemplateExcelVO> datas = ClassCompletionTemplateConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "结业考核模版设置.xls", "数据", ClassCompletionTemplateExcelVO.class, datas);
    }

}
