package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "班主任审批学员请假 request VO")
@Valid
public class TraineeLeaveDealReqVO {

    @ApiModelProperty(value = "请假id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long leaveId;

    @ApiModelProperty(value = "是否同意", required = true)
    @NotNull
    private Boolean isAgree;

    @ApiModelProperty(value = "拒绝原因/审批意见")
    private String opinion;

    @ApiModelProperty(value = "转办人id")
    private Long transferUserId;

    @ApiModelProperty(value = "转办人姓名")
    private String transferUserName;

    @ApiModelProperty(value = "业中用户id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "是否转办", required = true)
    @NotNull
    private Boolean isTransfer;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

}
