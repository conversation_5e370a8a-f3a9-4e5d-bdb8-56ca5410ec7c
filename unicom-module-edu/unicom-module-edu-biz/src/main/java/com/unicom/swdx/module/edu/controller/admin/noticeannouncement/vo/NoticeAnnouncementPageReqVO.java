package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - EduNoticeAnnouncement分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NoticeAnnouncementPageReqVO extends PageParam {

    @ApiModelProperty(value = "发布人")
    private String publisher;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "通知公告内容")
    private String content;

    @ApiModelProperty(value = "是否置顶,1-是，0-否")
    private Integer isTop;

    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "1-发布，2-存草稿箱")
    @NotNull(message = "isPublish不能为空")
    private Integer isPublish;

    @ApiModelProperty(value = "状态，1-上架，0-下架")
    private Integer status;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

//    @ApiModelProperty(value = "1-不查附件，2-查附件")
//    @NotNull(message = "isAccessor不能为空")
//    private String isAccessory;

    @ApiModelProperty(value = "0-升序，1-降序")
    @NotNull(message = "change不能为空")
    private Integer change;

    @ApiModelProperty(value = "1-标题，2-发布时间，3-存草稿箱时间")
    @NotNull(message = "tag不能为空")
    private Integer tag;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "截止时间")
    private String endTime;

    @ApiModelProperty(value = "小程序-传 1")
    private String smallProgram;

    @ApiModelProperty(value = "系统模块：1-学员管理系统 ，2-教务管理系统/参训系统")
    private Integer moduleCode;

    @ApiModelProperty(value = "学员id")
    private Long traineeId;

    @ApiModelProperty(value = "教师在业中的userId")
    private Long classTeacherLead;

    @ApiModelProperty(value = "发布人id")
    private Long publishId;

}
