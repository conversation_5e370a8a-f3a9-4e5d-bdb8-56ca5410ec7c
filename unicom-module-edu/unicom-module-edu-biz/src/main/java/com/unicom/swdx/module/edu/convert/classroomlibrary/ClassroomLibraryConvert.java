package com.unicom.swdx.module.edu.convert.classroomlibrary;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryRespVO;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.ClassroomLibraryUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * EduClassroomLibrary Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassroomLibraryConvert {

    ClassroomLibraryConvert INSTANCE = Mappers.getMapper(ClassroomLibraryConvert.class);

    ClassroomLibraryDO convert(ClassroomLibraryCreateReqVO bean);

    ClassroomLibraryDO convert(ClassroomLibraryUpdateReqVO bean);

    ClassroomLibraryRespVO convert(ClassroomLibraryDO bean);

    PageResult<ClassroomLibraryRespVO> convertPage(PageResult<ClassroomLibraryDO> page);


}
