package com.unicom.swdx.module.edu.service.schoolaccommodationattendance;

import java.util.*;
import javax.validation.*;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 全校就餐住宿考勤 Service 接口
 *
 * <AUTHOR>
 */
public interface SchoolAccommodationAttendanceService extends IService<SchoolAccommodationAttendanceDO> {

    /**
     * 创建全校就餐住宿考勤
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createSchoolAccommodationAttendance(@Valid SchoolAccommodationAttendanceCreateReqVO createReqVO);

    /**
     * 更新全校就餐住宿考勤
     *
     * @param updateReqVO 更新信息
     */
    void updateSchoolAccommodationAttendance(@Valid SchoolAccommodationAttendanceUpdateReqVO updateReqVO);

    /**
     * 更新全校就餐住宿考勤
     *
     * @param updateReqVO 更新信息
     */
    void updateSchoolAccommodationAttendanceBatch(@Valid SchoolAccommodationAttendanceParamsVO updateReqVO);

    /**
     * 删除全校就餐住宿考勤
     *
     * @param id 编号
     */
    void deleteSchoolAccommodationAttendance(Integer id);

    /**
     * 获得全校就餐住宿考勤
     *
     * @param id 编号
     * @return 全校就餐住宿考勤
     */
    SchoolAccommodationAttendanceDO getSchoolAccommodationAttendance(Integer id);

    /**
     * 获得全校就餐住宿考勤列表
     *
     * @param ids 编号
     * @return 全校就餐住宿考勤列表
     */
    List<SchoolAccommodationAttendanceDO> getSchoolAccommodationAttendanceList(Collection<Integer> ids);

    /**
     * 获得全校就餐住宿考勤分页
     *
     * @param pageReqVO 分页查询
     * @return 全校就餐住宿考勤分页
     */
    List<SchoolAccommodationAttendanceDO> getSchoolAccommodationAttendancePage(SchoolAccommodationAttendancePageReqVO pageReqVO);

}
