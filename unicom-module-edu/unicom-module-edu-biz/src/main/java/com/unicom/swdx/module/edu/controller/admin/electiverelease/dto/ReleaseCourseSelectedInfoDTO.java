package com.unicom.swdx.module.edu.controller.admin.electiverelease.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 发布课程 的可选人数信息
 * @date 2024-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReleaseCourseSelectedInfoDTO {

    /**
     * 发布课程id
     */
    private Long releaseCourseId;

    /**
     * 应选人数
     */
    private Long selectedNum;

}
