package com.unicom.swdx.module.edu.utils.excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 专题库 导入结果响应 VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelImportResultRespVO {

    @ApiModelProperty(value = "导入成功数量", example = "20")
    private int successNumber;

    @ApiModelProperty(value = "导入失败数量", example = "20")
    private int failNumber;

    @ApiModelProperty(value = "导入失败名单excel下载路径")
    private String excelPath;

    @ApiModelProperty(value = "导入失败名单excel文件名")
    private String excelFileName;
}
