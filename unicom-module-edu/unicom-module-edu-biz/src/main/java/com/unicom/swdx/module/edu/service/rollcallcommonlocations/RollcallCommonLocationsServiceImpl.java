package com.unicom.swdx.module.edu.service.rollcallcommonlocations;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.dto.RollcallCommonLocationsAddDTO;
import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo.RollcallCommonLocationsRespVO;
import com.unicom.swdx.module.edu.convert.rollcallcommonlocations.RollcallCommonLocationsConvert;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallcommonlocations.RollcallCommonLocationsDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.mysql.rollcallcommonlocations.RollcallCommonLocationsMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 大课考勤、点名签到信息常用地点 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RollcallCommonLocationsServiceImpl extends ServiceImpl<RollcallCommonLocationsMapper, RollcallCommonLocationsDO> implements RollcallCommonLocationsService {

    @Resource
    private RollcallCommonLocationsMapper rollcallCommonLocationsMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    // 常用地点数量限制
    private static final int MAX_COMMON_LOCATIONS_COUNT = 8;

    /**
     * 获得一个教师大课考勤或者点名签到的常用地点
     *
     * @param type 0 大课考勤 1 点名签到
     * @return 常用地点列表
     */
    @Override
    public List<RollcallCommonLocationsRespVO> getListForTeacher(Integer type) {
        // 根据用户获取教师信息
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(getLoginUserId());
        if (Objects.isNull(teacherInformationDO)) {
            throw exception(TEACHER_NOT_EXISTS);
        }
        // 获取教师常用地点(创建时间排序) 限制数量8个
        List<RollcallCommonLocationsDO> list = rollcallCommonLocationsMapper
                .getListForTeacher(teacherInformationDO.getId(), type);
        if (list.size() > MAX_COMMON_LOCATIONS_COUNT) {
            list = list.subList(0, MAX_COMMON_LOCATIONS_COUNT);
        }
        return RollcallCommonLocationsConvert.INSTANCE.convert(list);
    }

    /**
     * 删除一个常用地点
     *
     * @param id 常用地点id
     */
    @Override
    public void deleteOne(Long id) {
        RollcallCommonLocationsDO rollcallCommonLocationsDO = rollcallCommonLocationsMapper.selectById(id);
        if (Objects.nonNull(rollcallCommonLocationsDO)) {
            rollcallCommonLocationsMapper.deleteById(id);
        }
    }

    /**
     * 添加一个常用地点
     *
     * @param dto 常用地点信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOne(RollcallCommonLocationsAddDTO dto) {
        validateAdd(dto);
        RollcallCommonLocationsDO commonLocationsDO = RollcallCommonLocationsConvert.INSTANCE.convert2(dto);
        List<RollcallCommonLocationsDO> byAddressList = rollcallCommonLocationsMapper
                .selectListByAddress(commonLocationsDO.getAddress(),
                        commonLocationsDO.getTeacherId(),
                        commonLocationsDO.getType());
        if (!byAddressList.isEmpty()) {
            RollcallCommonLocationsDO updateDO = byAddressList.get(0);
            updateDO.setLatitude(commonLocationsDO.getLatitude());
            updateDO.setLongitude(commonLocationsDO.getLongitude());
            updateDO.setRadius(commonLocationsDO.getRadius());
            updateDO.setUpdateTime(LocalDateTime.now());
            rollcallCommonLocationsMapper.updateById(updateDO);
            // 需要删除的id
            if (byAddressList.size() > 1) {
                List<Long> deleteIds = byAddressList.subList(1, byAddressList.size())
                        .stream().map(RollcallCommonLocationsDO::getId).collect(Collectors.toList());
                rollcallCommonLocationsMapper.deleteBatchIds(deleteIds);
            }
        } else {
            rollcallCommonLocationsMapper.insert(commonLocationsDO);
        }
    }


    /**
     * 获得一个教师最近一次选择的半径范围
     *
     * @param type 0 大课考勤 1 点名签到
     * @return 半径
     */
    @Override
    public BigDecimal getLastRadius(Integer type) {
        // 根据用户获取教师信息
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(getLoginUserId());
        if (Objects.isNull(teacherInformationDO)) {
            throw exception(TEACHER_NOT_EXISTS);
        }
        // 获取最近一次的常用记录
        RollcallCommonLocationsDO lastDO = rollcallCommonLocationsMapper
                .getLast(teacherInformationDO.getId(), type);
        if (Objects.nonNull(lastDO)) {
            return lastDO.getRadius();
        }
        return null;
    }

    private void validateAdd(RollcallCommonLocationsAddDTO dto) {
        if (Objects.isNull(dto.getTeacherId())) {
            throw exception(ROLLCALL_RECORD_HISTORY_TEACHER_ID_IS_EMPTY);
        }
        if (Objects.isNull(dto.getType())) {
            throw exception(ROLLCALL_RECORD_HISTORY_TYPE_IS_EMPTY);
        }
        if (Objects.isNull(dto.getAddress())) {
            throw exception(ROLLCALL_RECORD_HISTORY_ADDRESS_IS_EMPTY);
        }
    }
}
