package com.unicom.swdx.module.edu.service.courseChange;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseUpdateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassTimeTableRespVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.WeekTimetableReqVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationPageReqVO;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 调课记录 Service 接口
 *
 * <AUTHOR>
 */
public interface CourseChangeService {

    /**
     * 获得调课记录分页
     *
     * @param reqVO 查询条件
     * @return 调课记录分页
     */
    PageResult<CourseChangeRespVO> getCourseChangePage(CourseChangePageReqVO reqVO);

    List<CourseChangeExcelVO> getCourseChangeExportList(CourseChangeExportReqVO exportReqVO);

    List<WeekTimetableRespVO> getTimetableOfWeek(WeekTimetableReqVO reqVO);

    void exchange(CourseExchangeReqVO reqVO);

    void change(CourseChangeReqVO reqVO);

    List<Object> getFreeTime(Long classId);

    /**
     * 调课时对已有排课的格子替换为另外选的课程
     *
     * @param updateReqVO 更新信息
     */
    void updateClassCourse(@Valid CourseChangeUpdateReqVO updateReqVO);

    /**
     * 根据班级课程ID获取课程变更信息
     * 此方法用于查询特定班级课程的变更详情，以便用户了解课程在变更前后的具体信息
     *
     * @param classCourseId 班级课程ID，用于标识特定的班级课程记录
     * @return CourseChangeRespVO 返回课程变更响应对象，包含变更前后的课程详情如果无变更信息，则返回null
     */
    CourseChangeRespVO getCourseChangeInfoByClassCourseId(Long classCourseId);
}
