package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WeekTimetableInfoDTO {

    // 课表单元格id
    private Long id;

    // 班级id
    private Long classId;

    // 日期
    private String date;

    // 午段
    private String period;

    // 课程id
    private Long courseId;

    // 课程名称
    private String courseName;

    // 课程类型
    private String courseType;

    // 教学形式字典id
    private Long educateFormId;

    // 活动类型字典id
    private Long activityType;

    // 开始时间
    private LocalDateTime beginTime;

    // 结束时间
    private LocalDateTime endTime;

    // 教师id
    private Long teacherId;

    // 教师
    private String teacherName;

    // 教室id
    private Long classroomId;

    // 教室
    private String classroom;

    // 是否发布
    private Boolean isTemporary;

    // 是否合班授课
    private Boolean isMerge;

    /**
     * 多教师
     */
    private String teacherIdString;

    /**
     * 是否部门授课
     */
    private Boolean department;

}
