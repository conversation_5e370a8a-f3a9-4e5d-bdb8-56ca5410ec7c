package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 班级课程安排分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassCoursePageReqVO extends PageParam {

    @ApiModelProperty(value = "班级id")
    private Long classId;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "时间范围")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] betweenTime;

//    @ApiModelProperty(value = "开始时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] beginTime;
//
//    @ApiModelProperty(value = "结束时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] endTime;

    @ApiModelProperty(value = "教师id")
    private Long teacherId;

    @ApiModelProperty(value = "教师id集合")
    private List<Long> teacherIds;

    @ApiModelProperty(value = "教室id")
    private Long classroomId;

    @ApiModelProperty(value = "是否暂存")
    private Boolean isTemporary;

    @ApiModelProperty(value = "是否合班授课")
    private Boolean isMerge;

    @ApiModelProperty(value = "是否调课")
    private Boolean isChange;

    @ApiModelProperty(value = "教学计划id")
    private Long planId;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）")
    private String period;

    @ApiModelProperty(value = "冲突信息")
    private String conflictInfo;

    @ApiModelProperty(value = "校区")
    private Integer campus;

    @ApiModelProperty(value = "时间区间")
    private LocalDateTime[] timeRange;

}
