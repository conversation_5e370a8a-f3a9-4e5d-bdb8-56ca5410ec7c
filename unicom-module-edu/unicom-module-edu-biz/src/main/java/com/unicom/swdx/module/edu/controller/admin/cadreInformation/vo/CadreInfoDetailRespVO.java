package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * @ClassName: CadreInformationDO
 * @Author: lty
 * @Date: 2024/12/19 17:08
 */
@Data
@ToString(callSuper = true)
public class CadreInfoDetailRespVO {

    /**
     * 班次编码
     */
    private String classNameCode;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次属性
     */
    private Integer classAttribute;

    /**
     * 开班日期
     */
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate classOpenTime;

    /**
     * 结业日期
     */
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate completionTime;


    /**
     * 年度
     */
    private Integer year;

    /**
     * 学期
     */
    private Integer semester;

    /**
     * 班主任
     */
    private String classTeacherLead;


}
