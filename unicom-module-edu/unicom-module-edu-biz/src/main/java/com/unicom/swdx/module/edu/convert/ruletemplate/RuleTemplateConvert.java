package com.unicom.swdx.module.edu.convert.ruletemplate;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;

/**
 * 考勤规则模版 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RuleTemplateConvert {

    RuleTemplateConvert INSTANCE = Mappers.getMapper(RuleTemplateConvert.class);

    RuleTemplateDO convert(RuleTemplateCreateReqVO bean);

    RuleTemplateDO convert(RuleTemplateUpdateReqVO bean);

    RuleTemplateRespVO convert(RuleTemplateDO bean);

    List<RuleTemplateRespVO> convertList(List<RuleTemplateDO> list);

    PageResult<RuleTemplateRespVO> convertPage(PageResult<RuleTemplateDO> page);

    List<RuleTemplateExcelVO> convertList02(List<RuleTemplateDO> list);

}
