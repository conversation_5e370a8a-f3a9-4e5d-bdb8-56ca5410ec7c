package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel("管理后台 - EduClassroomLibrary更新 Request VO")
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class CadreBatchDeleteReqVO {

    @ApiModelProperty(value = "批量删除id", required = true)
    private List<Long> ids;

}
