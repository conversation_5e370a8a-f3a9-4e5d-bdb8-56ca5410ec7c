package com.unicom.swdx.module.edu.dal.mysql.xcxmsg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface XcxMsgConfigMapper extends BaseMapperX<XcxMsgConfigDO> {

    List<XcxMsgConfigRespVO> selectPageByReqVO(IPage page, @Param("reqVO") XcxMsgConfigPageReqVO reqVO);

    List<Long> selectCourseMsgIds(@Param("dateTime") String dateTime,@Param("tenantId") Long tenantId);

    List<Long> selectAttendanceMsgIds(@Param("dateTime") String dateTime,@Param("tenantId") Long tenantId);

    List<Long> selectEvaluateMsgIds(@Param("detailTime") String detailTime,@Param("tenantId") Long tenantId);
}
