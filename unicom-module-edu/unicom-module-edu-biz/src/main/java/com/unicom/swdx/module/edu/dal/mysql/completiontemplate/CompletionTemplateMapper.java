package com.unicom.swdx.module.edu.dal.mysql.completiontemplate;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementPageReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 结业考核模版设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CompletionTemplateMapper extends BaseMapperX<CompletionTemplateDO> {

    default PageResult<CompletionTemplateDO> selectPage(CompletionTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CompletionTemplateDO>()
                .eqIfPresent(CompletionTemplateDO::getSerialNumber, reqVO.getSerialNumber())
                .likeIfPresent(CompletionTemplateDO::getColumnName, reqVO.getColumnName())
                .eqIfPresent(CompletionTemplateDO::getConversionAnnouncement, reqVO.getConversionAnnouncement())
                .eqIfPresent(CompletionTemplateDO::getMaxScore, reqVO.getMaxScore())
                .eqIfPresent(CompletionTemplateDO::getInitialScore, reqVO.getInitialScore())
                .eqIfPresent(CompletionTemplateDO::getAcquisitionMode, reqVO.getAcquisitionMode())
                .eqIfPresent(CompletionTemplateDO::getDataSource, reqVO.getDataSource())
                .likeIfPresent(CompletionTemplateDO::getAssessmentName, reqVO.getAssessmentName())
                .eqIfPresent(CompletionTemplateDO::getCampus, reqVO.getCampus())
                .eqIfPresent(CompletionTemplateDO::getDefaultRule, reqVO.getDefaultRule())
                .likeIfPresent(CompletionTemplateDO::getTemplateName, reqVO.getTemplateName())
                .likeIfPresent(CompletionTemplateDO::getModuleName, reqVO.getModuleName())
                .orderByDesc(CompletionTemplateDO::getId));
    }

    default List<CompletionTemplateDO> selectList(CompletionTemplateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CompletionTemplateDO>()
                .eqIfPresent(CompletionTemplateDO::getSerialNumber, reqVO.getSerialNumber())
                .likeIfPresent(CompletionTemplateDO::getColumnName, reqVO.getColumnName())
                .eqIfPresent(CompletionTemplateDO::getConversionAnnouncement, reqVO.getConversionAnnouncement())
                .eqIfPresent(CompletionTemplateDO::getMaxScore, reqVO.getMaxScore())
                .eqIfPresent(CompletionTemplateDO::getInitialScore, reqVO.getInitialScore())
                .eqIfPresent(CompletionTemplateDO::getAcquisitionMode, reqVO.getAcquisitionMode())
                .eqIfPresent(CompletionTemplateDO::getDataSource, reqVO.getDataSource())
                .likeIfPresent(CompletionTemplateDO::getAssessmentName, reqVO.getAssessmentName())
                .eqIfPresent(CompletionTemplateDO::getCampus, reqVO.getCampus())
                .eqIfPresent(CompletionTemplateDO::getDefaultRule, reqVO.getDefaultRule())
                .likeIfPresent(CompletionTemplateDO::getTemplateName, reqVO.getTemplateName())
                .likeIfPresent(CompletionTemplateDO::getModuleName, reqVO.getModuleName())
                .orderByDesc(CompletionTemplateDO::getId));
    }

//    default CompletionTemplateDO selectByTemplateName(String templateName){
//        LambdaQueryWrapper<CompletionTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CompletionTemplateDO::getTemplateName, templateName);
//        return selectOne(queryWrapper);
//    }

//    default CompletionTemplateDO selectByDefaultRule(Integer campus){
//        LambdaQueryWrapper<CompletionTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CompletionTemplateDO::getDefaultRule, 0)
//                .eq(CompletionTemplateDO::getCampus, campus);
//        return selectOne(queryWrapper);
//    }

    /**
     * 唯一性校验
     * 模版名称唯一性校验
     *  @param templateName
     */
    List<CompletionTemplateDO> selectByTemplateName(@Param("templateName") String templateName);
    /**
     * 唯一性校验
     * 同校区默认规则
     * 唯一性校验
     *  @param campus
     */
    List<CompletionTemplateDO> selectByDefaultRule(@Param("defaultRule") Integer defaultRule,@Param("campus") Integer campus);

    /**
     * 根据模版名称删除模版
     *  @param idCode
     */
    void deleteCompletionTemplateByName(@Param("idCode") String idCode);
    /**
     * 唯一性校验 编辑
     * 模版名称唯一性校验
     *  @param templateName
     */
    List<CompletionTemplateDO> selectByTemplateNameEdit(@Param("idCode") String idCode, @Param("templateName") String templateName);
    /**
     * 唯一性校验
     * 同校区默认规则
     * 唯一性校验
     *  @param campus
     *  @param defaultRule
     */
    List<CompletionTemplateDO> selectByDefaultRuleEdit(@Param("defaultRule") Integer defaultRule,@Param("campus") Integer campus, @Param("idCode") String idCode);


    /**
     * 根据idCode查询模板
     *  @param idCode
     */
    default List<CompletionTemplateDO> selectByTemplateCode(String idCode) {

        LambdaQueryWrapper<CompletionTemplateDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompletionTemplateDO::getIdCode, idCode);
//        wrapper.eq(CompletionTemplateDO::getSave,1);
        return selectList(wrapper);
    }

    /**
     * 根据模版id获取模版数据
     * 获取一条模版数据
     *  @param idCode
     */
    List<CompletionTemplateDO> selectOneByIdCode(@Param("idCode") String idCode);
    /**
     * 模板列表 分页
     * @param buildPage
     * @param reqVO
     * @return
     */
    List<CompletionTemplateDO> selectPageList(IPage<CompletionTemplateDO> buildPage, @Param("reqVO") CompletionTemplatePageReqVO reqVO);

    List<CompletionTemplateDO> selectPageListAll(@Param("reqVO") CompletionTemplatePageReqVO reqVO);

    /**
     * 模板列表 导出
     * @param reqVO
     * @return
     */
    List<CompletionTemplateDO> selectListAll(@Param("reqVO") CompletionTemplateExportReqVO reqVO);


    Integer selectCountByIdCode(@Param("idCode") String idCode);


    /**
     * 唯一性校验
     * 同校区默认规则
     * 唯一性校验
     *  @param campus
     */
    List<ClassCompletionTemplateDO> selectClassDefaultRule(@Param("defaultRule") Integer defaultRule, @Param("campus") Integer campus);

    default Long getBuildInTemplate(Integer campus) {

        return selectCount(new LambdaQueryWrapper<CompletionTemplateDO>()
                .eq(CompletionTemplateDO::getBuiltinTemplate, 1)
                .or(wrapper -> wrapper
                        .eq(CompletionTemplateDO::getTemplateName, "百分制量化模板")
                        .eq(CompletionTemplateDO::getCampus, campus)
                )
        );

    }

    List<CompletionTemplateDO> getDefaultTemplateByIdCode(@Param("idCode") String idCode, @Param("rule") Integer rule);

    String getDefaultTemplateByCampus(Integer campus);

    Long getMainCampus(@Param("tenantId") Long tenantId);
}
