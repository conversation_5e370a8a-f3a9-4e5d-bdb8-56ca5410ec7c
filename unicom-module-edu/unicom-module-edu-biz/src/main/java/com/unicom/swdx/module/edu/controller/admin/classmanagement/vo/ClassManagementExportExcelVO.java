package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * EduClassManagement Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ClassManagementExportExcelVO {

    @ExcelProperty(value = "班次编码")
    @ApiModelProperty(value = "班次编码序号 0")
    private String classNameCode;

    @ExcelProperty(value = "班次名称")
    @ApiModelProperty(value = "班次名称序号 1")
    private String className;

    @ExcelProperty(value = "办班类型")
    @ApiModelProperty(value = "办班类型 2")
    private String classTypeDict;

    @ExcelProperty(value = "班次属性")
    @ApiModelProperty(value = "班次属性序号 3")
    private String classAttribute;

    @ExcelProperty(value = "班主任")
    @ApiModelProperty(value = "班主任序号 4")
    private String classTeacherLead;

    @ExcelProperty(value = "教务老师")
    @ApiModelProperty(value = "教务老师序号 5")
    private String administrativeTeachersName;

    @ExcelProperty(value = "开班时间")
    @ApiModelProperty(value = "开班时间序号 6")
    private LocalDate classOpenTime;

    @ExcelProperty(value = "结业时间")
    @ApiModelProperty(value = "结业时间序号 7")
    private LocalDate completionTime;

    @ExcelProperty(value = "轮次")
    @ApiModelProperty(value = "轮次序号 8")
    private String turn;

    @ExcelProperty(value = "校区")
    @ApiModelProperty(value = "校区序号 9")
    private String campus;

    @ExcelProperty(value = "班级状态")
    @ApiModelProperty(value = "班级状态序号10")
    private String classStatus;

    @ExcelProperty(value = "排序号")
    @ApiModelProperty(value = "排序号 11")
    private Integer sort;

    @ExcelProperty(value = "学员人数")
    @ApiModelProperty(value = "学员人数 12")
    private Integer classPeopleCount;

    @ExcelProperty(value = "辅导老师")
    @ApiModelProperty(value = "辅导老师 13")
    private String coachTeacher;

    @ExcelProperty(value = "考勤状态")
    @ApiModelProperty(value = "考勤状态 14")
    private String clockInStatus;

    @ExcelProperty(value = "学期")
    @ApiModelProperty(value = "学期 15")
    private String semester;
}
