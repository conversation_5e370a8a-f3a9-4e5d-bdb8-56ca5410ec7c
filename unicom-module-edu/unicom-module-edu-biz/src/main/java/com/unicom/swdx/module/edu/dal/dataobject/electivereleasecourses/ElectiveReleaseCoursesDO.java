package com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 发布课程 DO
 *
 * <AUTHOR>
 */
@TableName("edu_elective_release_courses")
@KeySequence("edu_elective_release_courses_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElectiveReleaseCoursesDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 选修课发布信息ID
     */
    private Long releaseId;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 授课教师ID
     */
    private Long teacherId;
    /**
     * 上课教室ID
     */
    private Long classroomId;
    /**
     * 系统内部门
     */
    private Long deptId;
    /**
     * 上课日期
     */
    @TableField(exist = false)
    private LocalDate classDate;

    /**
     * 午别时间 0-上午 1-下午 2-晚上
     */
    @TableField(exist = false)
    private Integer dayPeriod;

}
