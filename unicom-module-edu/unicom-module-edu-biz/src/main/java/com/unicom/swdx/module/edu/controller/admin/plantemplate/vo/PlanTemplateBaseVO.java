package com.unicom.swdx.module.edu.controller.admin.plantemplate.vo;

import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.PlanTemplateConfigBaseVO;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 教学计划模版 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PlanTemplateBaseVO {

    @ApiModelProperty(value = "模版名称", required = true)
    @NotNull(message = "模版名称不能为空")
    private String name;

    /**
     * 教学计划模版配置信息
     */
    @ApiModelProperty(value = "教学计划配置信息")
    List<PlanTemplateConfigBaseVO> planTemplateConfigBaseVOList;

}
