package com.unicom.swdx.module.edu.controller.admin.frequency;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.frequency.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.frequency.FrequencyDO;
import com.unicom.swdx.module.edu.convert.frequency.FrequencyConvert;
import com.unicom.swdx.module.edu.service.frequency.FrequencyService;

@Api(tags = "管理后台 - 使用次数")
@RestController
@RequestMapping("/edu/frequency")
@Validated
public class FrequencyController {

    @Resource
    private FrequencyService frequencyService;

    @PostMapping("/create")
    @ApiOperation("创建使用次数")
    @PreAuthorize("@ss.hasPermission('edu:frequency:create')")
    public CommonResult<Long> createFrequency(@Valid @RequestBody FrequencyCreateReqVO createReqVO) {
        return success(frequencyService.createFrequency(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新使用次数")
    @PreAuthorize("@ss.hasPermission('edu:frequency:update')")
    public CommonResult<Boolean> updateFrequency(@Valid @RequestBody FrequencyUpdateReqVO updateReqVO) {
        frequencyService.updateFrequency(updateReqVO);
        return success(true);
    }

    @GetMapping("/getByLoginUser")
    @ApiOperation("本人获得使用次数")
    @PreAuthorize("@ss.hasPermission('edu:frequency:query')")
    public CommonResult<FrequencyRespVO> getByLoginUser() {
        FrequencyDO frequency = frequencyService.getByLoginUser();
        return success(FrequencyConvert.INSTANCE.convert(frequency));
    }

    @PostMapping("/updateByLoginUser")
    @ApiOperation("本人更新使用次数")
    @PreAuthorize("@ss.hasPermission('edu:frequency:update')")
    public CommonResult<Boolean> updateByLoginUser() {
        frequencyService.updateByLoginUser();
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除使用次数")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:frequency:delete')")
    public CommonResult<Boolean> deleteFrequency(@RequestParam("id") Long id) {
        frequencyService.deleteFrequency(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得使用次数")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:frequency:query')")
    public CommonResult<FrequencyRespVO> getFrequency(@RequestParam("id") Long id) {
        FrequencyDO frequency = frequencyService.getFrequency(id);
        return success(FrequencyConvert.INSTANCE.convert(frequency));
    }

    @GetMapping("/list")
    @ApiOperation("获得使用次数列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:frequency:query')")
    public CommonResult<List<FrequencyRespVO>> getFrequencyList(@RequestParam("ids") Collection<Long> ids) {
        List<FrequencyDO> list = frequencyService.getFrequencyList(ids);
        return success(FrequencyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得使用次数分页")
    @PreAuthorize("@ss.hasPermission('edu:frequency:query')")
    public CommonResult<PageResult<FrequencyRespVO>> getFrequencyPage(@Valid FrequencyPageReqVO pageVO) {
        PageResult<FrequencyDO> pageResult = frequencyService.getFrequencyPage(pageVO);
        return success(FrequencyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出使用次数 Excel")
    @PreAuthorize("@ss.hasPermission('edu:frequency:export')")
    @OperateLog(type = EXPORT)
    public void exportFrequencyExcel(@Valid FrequencyExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<FrequencyDO> list = frequencyService.getFrequencyList(exportReqVO);
        // 导出 Excel
        List<FrequencyExcelVO> datas = FrequencyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "使用次数.xls", "数据", FrequencyExcelVO.class, datas);
    }

}
