package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免信息导入有问题
public class ClassInfoImportStrExcelVO {

    @ExcelProperty("序号（黄色为必填项，灰色为非必填项）")
    private String iSort;

    @ExcelProperty("班次名称")
    private String className;

    @ExcelProperty("班次类型")
    private String classType;

    @ExcelProperty("班次属性")
    private String classAttribute;

    @ExcelProperty("省内/省外")
    private String regionType;

    @ExcelProperty("年度")
    private String year;

    @ExcelProperty("学期")
    private String semester;

    @ExcelProperty("学制")
    private String learningSystem;

    @ExcelProperty("委托单位名称")
    private String turn;

    @ExcelProperty("培训对象")
    private String trainingObject;

    @ExcelProperty("预计人数")
    private String peopleNumber;

    @ExcelProperty("校区")
    private String campus;

    @ExcelProperty("住宿地点")
    private String accommodationLocation;

    @ExcelProperty("是否接送站")
    private String stationTransfer;

    @ExcelProperty("接送地点")
    private String transferLocation;

    @ExcelProperty("报到日期")
    private String reportingTime;

    @ExcelProperty("报到时间段")
    private String reportPeriod;

    @ExcelProperty("报名开始日期")
    private String registrationStartTime;

    @ExcelProperty("报名结束日期")
    private String registrationEndTime;

    @ExcelProperty("开班日期")
    private String classOpenTime;

    @ExcelProperty("结业日期")
    private String completionTime;

    @ExcelProperty("返程时间段")
    private String returnPeriod;

    @ApiModelProperty(value = "缴费报道，1-是，2-否")
    @ExcelProperty("缴费报道")
    private String paymentReport;

    @ApiModelProperty(value = "考勤评课，1-是，2-否")
    @ExcelProperty("考勤评课")
    private String evaluate;

    @ExcelProperty("排序号")
    private String sort;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("开班领导")
    private String programInitiator;

    @ExcelProperty("结业领导")
    private String programCloser;

}

