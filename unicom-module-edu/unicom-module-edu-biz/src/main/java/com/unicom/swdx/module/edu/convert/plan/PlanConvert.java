package com.unicom.swdx.module.edu.convert.plan;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.plan.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;


@Mapper
public interface PlanConvert {

    PlanConvert INSTANCE = Mappers.getMapper(PlanConvert.class);

    PlanDO convert(PlanCreateReqVO bean);

    PlanDO convert(PlanUpdateReqVO bean);

    PlanRespVO convert(PlanDO bean);

    List<PlanRespVO> convertList(List<PlanDO> list);

    PageResult<PlanRespVO> convertPage(PageResult<PlanDO> page);

    List<PlanExcelVO> convertList02(List<PlanDO> list);

}
