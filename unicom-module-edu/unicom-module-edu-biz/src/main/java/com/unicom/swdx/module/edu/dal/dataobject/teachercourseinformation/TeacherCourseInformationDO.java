package com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 师资-任课信息中间 DO
 *
 * <AUTHOR>
 */
@TableName("edu_teacher_course_information")
@KeySequence("edu_teacher_course_information_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherCourseInformationDO extends TenantBaseDO {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 课程ID
     */
    private Long coursesId;
    /**
     * 教师ID
     */
    private Long teacherId;
    /**
     * 部门 ID
     */
    private Long deptId;

}
