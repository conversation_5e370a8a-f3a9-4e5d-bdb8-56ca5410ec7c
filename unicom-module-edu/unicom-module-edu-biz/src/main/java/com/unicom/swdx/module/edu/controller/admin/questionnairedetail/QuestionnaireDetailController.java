package com.unicom.swdx.module.edu.controller.admin.questionnairedetail;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;



import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.module.edu.service.questionnairedetail.QuestionnaireDetailService;

@Tag(name = "管理后台 - 评估问卷与问题关联")
@RestController
@RequestMapping("/edu/questionnaire-detail")
@Validated
public class QuestionnaireDetailController {

    @Resource
    private QuestionnaireDetailService questionnaireDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建评估问卷与问题关联")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-detail:create')")
    public CommonResult<Long> createQuestionnaireDetail(@Valid @RequestBody QuestionnaireDetailSaveReqVO createReqVO) {
        return success(questionnaireDetailService.createQuestionnaireDetail(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新评估问卷与问题关联")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-detail:update')")
    public CommonResult<Boolean> updateQuestionnaireDetail(@Valid @RequestBody QuestionnaireDetailSaveReqVO updateReqVO) {
        questionnaireDetailService.updateQuestionnaireDetail(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除评估问卷与问题关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-detail:delete')")
    public CommonResult<Boolean> deleteQuestionnaireDetail(@RequestParam("id") Long id) {
        questionnaireDetailService.deleteQuestionnaireDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得评估问卷与问题关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-detail:query')")
    public CommonResult<QuestionnaireDetailRespVO> getQuestionnaireDetail(@RequestParam("id") Long id) {
        QuestionnaireDetailDO questionnaireDetail = questionnaireDetailService.getQuestionnaireDetail(id);
        return success(BeanUtils.toBean(questionnaireDetail, QuestionnaireDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得评估问卷与问题关联分页")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-detail:query')")
    public CommonResult<PageResult<QuestionnaireDetailRespVO>> getQuestionnaireDetailPage(@Valid QuestionnaireDetailPageReqVO pageReqVO) {
        PageResult<QuestionnaireDetailDO> pageResult = questionnaireDetailService.getQuestionnaireDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionnaireDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出评估问卷与问题关联 Excel")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-detail:export')")

    public void exportQuestionnaireDetailExcel(@Valid QuestionnaireDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireDetailDO> list = questionnaireDetailService.getQuestionnaireDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "评估问卷与问题关联.xls", "数据", QuestionnaireDetailRespVO.class,
                        BeanUtils.toBean(list, QuestionnaireDetailRespVO.class));
    }

}
