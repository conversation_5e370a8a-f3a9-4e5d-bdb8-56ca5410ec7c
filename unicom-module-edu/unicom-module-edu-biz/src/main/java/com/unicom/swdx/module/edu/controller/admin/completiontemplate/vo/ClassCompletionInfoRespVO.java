package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@ApiModel("管理后台 - 结业考核模版设置 Response VO")
@Data
@ToString(callSuper = true)
public class ClassCompletionInfoRespVO {

    private Long id;

    @ApiModelProperty(value = "学员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名")
    private String traineeName;

    @ApiModelProperty(value = "小组名称")
    private String groupName;

    @ApiModelProperty(value = "单位职务")
    private String unitPosition;

    @ApiModelProperty(value = "班级职务")
    private String classPosition;

    @ApiModelProperty(value = "分数")
    private Map<String,String> mapScore;

}
