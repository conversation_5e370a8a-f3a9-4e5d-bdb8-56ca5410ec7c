package com.unicom.swdx.module.edu.convert.cadreInformation;

import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.CadreInfoImportExcelVO;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.CadreInformationRespVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.AddTraineeInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeBaseVO;
import com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * EduClassroomLibrary Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CadreInformationConvert {

    CadreInformationConvert INSTANCE = Mappers.getMapper(CadreInformationConvert.class);

    CadreInformationDO covert(TraineeBaseVO reqVO);

    List<CadreInformationDO> convertImportList(List<CadreInfoImportExcelVO> list);

    CadreInformationRespVO covertInfo(CadreInformationDO byId);

    List<TraineeDO> covertList(List<CadreInformationDO> cadreInfoList);
}
