package com.unicom.swdx.module.edu.convert.leavereport;

import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportDetailCreateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LeaveReportDetailConvert {

    LeaveReportDetailConvert INSTANCE = Mappers.getMapper(LeaveReportDetailConvert.class);

    LeaveReportDetailDO convert(LeaveReportDetailCreateReqVO createReqVO);
}
