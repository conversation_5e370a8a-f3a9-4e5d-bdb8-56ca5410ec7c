package com.unicom.swdx.module.edu.utils.validate;

import cn.hutool.core.util.StrUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 号码校验工具类
 *
 * <AUTHOR>
 */
public class ValidateMatchUtils {

    /**
     * 正则：手机号（简单）, 1字头＋10位数字即可.
     */
    private static final String REGEX_MOBILE_SIMPLE = "0?(13|14|15|16|17|18|19)[0-9]{9}";
    private static final Pattern PATTERN_REGEX_MOBILE_SIMPLE = Pattern.compile(REGEX_MOBILE_SIMPLE);

    /**
     * 正则：手机号（精确）, 已知3位前缀＋8位数字
     * <p>
     * 移动：134(0-8)、135、136、137、138、139、147、150、151、152、157、158、159、178、182、183、184、187、188
     * </p>
     * <p>
     * ：130、131、132、145、155、156、165、166、167、175、176、185、186
     * </p>
     * <p>
     * 电信：133、153、173、177、180、181、189、19（0-9）
     * </p>
     * <p>
     * 全球星：1349
     * </p>
     * <p>
     * 虚拟运营商：170
     * </p>
     */
    private static final String REGEX_MOBILE_EXACT = "^((13[0-9])|(14[5,8])|(15[0-3,5-9])|(16[5-7])|(17[0,3,5-8])|(18[0-9])|(19[0-9]))\\d{8}$";
    private static final Pattern PATTERN_REGEX_MOBILE_EXACT = Pattern.compile(REGEX_MOBILE_EXACT);

    /**
     * 正则：固定电话号码,可带区号,然后至少6,8位数字
     */
    private static final String REGEX_TEL = "^(\\d{3,4}-)?\\d{6,8}$";
    private static final Pattern PATTERN_REGEX_TEL = Pattern.compile(REGEX_TEL);

    /**
     * 正则：邮箱, 有效字符(不支持中文), 且中间必须有@,后半部分必须有.
     */
    private static final String REGEX_EMAIL = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    private static final Pattern PATTERN_REGEX_EMAIL = Pattern.compile(REGEX_EMAIL);

    /**
     * 正则：URL, 必须有"://",前面必须是英文,后面不能有空格
     */
    private static final String REGEX_URL = "[a-zA-z]+://[^\\s]*";
    private static final Pattern PATTERN_REGEX_URL = Pattern.compile(REGEX_URL);

    /**
     * 正则：yyyy-MM-dd格式的日期校验,已考虑平闰年
     */
    private static final String REGEX_DATE = "^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$";
    private static final Pattern PATTERN_REGEX_DATE = Pattern.compile(REGEX_DATE);

    /**
     * 正则：IP地址
     */
    private static final String REGEX_IP = "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)";
    private static final Pattern PATTERN_REGEX_IP = Pattern.compile(REGEX_IP);


    /**
     * 正则：车牌号
     */
    private static final String REGEX_CAR = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{3,4}[A-Z0-9挂学警港澳]{1}$";
    private static final Pattern PATTERN_REGEX_CAR = Pattern.compile(REGEX_CAR);


    /**
     * 正则：人名
     */
    private static final String REGEX_NAME = "^([\\u4e00-\u9fa5]{1,20}|[a-zA-Z\\.\\s]{1,20})$";
    private static final Pattern PATTERN_REGEX_NAME = Pattern.compile(REGEX_NAME);

    /**
     * 正则：mac地址
     */
    private static final String REGEX_MAC = "([A-Fa-f0-9]{2}-){5}[A-Fa-f0-9]{2}";
    private static final Pattern PATTERN_REGEX_MAC = Pattern.compile(REGEX_MAC);

    /**
     * 正则：学号校验
     */
    private static final String REGEX_NUMBER = "^[a-z0-9A-Z]+$";
    private static final Pattern PATTERN_REGEX_NUMBER = Pattern.compile(REGEX_NUMBER);

    /**
     * 正则：护照号
     */
    private static final String REGEX_PASSPORT_NUMBER = "^(14|15)\\d{7}$|^G\\d{8}$|^P\\d{7}$|^(S|D)\\d{7,8}$";
    private static final Pattern PASSPORT_NUMBER_PATTERN = Pattern.compile(REGEX_PASSPORT_NUMBER);

    /**
     * 正则：军官证
     */
    private static final String REGEX_OFFICER_NUMBER_PATTERN = "^[\\u4E00-\\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$";
    private static final Pattern OFFICER_NUMBER_PATTERN = Pattern.compile(REGEX_OFFICER_NUMBER_PATTERN);

    /**
     * 正则：学工号
     */
    private static final String REGEX_WORK_NUMBER_PATTERN = "^[a-zA-Z0-9]{1,36}$";

    private static final String REGEX_YEAR_PATTERN = "^(19|20)\\d{2}$";

    private static final Pattern WORK_NUMBER_PATTERN = Pattern.compile(REGEX_WORK_NUMBER_PATTERN);

    /**
     * 正则：正整数
     */
    private static final String REGEX_POSITIVE_INTEGER = "^[-\\+]?[\\d]*$";
    private static final Pattern POSITIVE_INTEGER_PATTERN = Pattern.compile(REGEX_WORK_NUMBER_PATTERN);

    private static final Pattern YEAR_PATTERN = Pattern.compile(REGEX_YEAR_PATTERN);


    /**
     * 正则：邮政编码
     */
    private static final String REGEX_POST_CODE = "^\\d{6}$";
    private static final Pattern PATTERN_REGEX_POST_CODE = Pattern.compile(REGEX_POST_CODE);


    //封装方法：

    /**
     * 验证正整数 (校验包含字母和数字)
     */
    public static boolean isPositiveInteger(String str) {
        return isMatch(POSITIVE_INTEGER_PATTERN, str);
    }

    /**
     * 验证邮政编码
     */
    public static boolean isPostCode(String str) {
        return isMatch(PATTERN_REGEX_POST_CODE, str);
    }

    /**
     * 验证正整数
     */
    public static boolean isInteger(String str) {
        return isMatch(YEAR_PATTERN, str);
    }


    /**
     * 验证手机号（简单）
     */
    public static boolean isMobileSimple(String str) {
        return isMatch(PATTERN_REGEX_MOBILE_SIMPLE, str);
    }

    /**
     * 验证手机号（精确）
     */
    public static boolean isMobileExact(String str) {
        return isMatch(PATTERN_REGEX_MOBILE_EXACT, str);
    }

    /**
     * 验证固定电话号码
     */
    public static boolean isTel(String str) {
        return isMatch(PATTERN_REGEX_TEL, str);
    }

    /**
     * 验证护照号
     */
    public static boolean isValidPassportNumber(String str) {
        return isMatch(PASSPORT_NUMBER_PATTERN, str);
    }

    /**
     * 验证军官证
     */
    public static boolean isValidOfficerNumber(String str) {
        return isMatch(OFFICER_NUMBER_PATTERN, str);
    }

    /**
     * 验证学工号
     */
    public static boolean isValidWorkNumber(String str) {
        return isMatch(WORK_NUMBER_PATTERN, str);
    }

    /**
     * 验证邮箱
     */
    public static boolean isEmail(String str) {
        return isMatch(PATTERN_REGEX_EMAIL, str);
    }

    /**
     * 验证URL
     */
    public static boolean isUrl(String str) {
        return isMatch(PATTERN_REGEX_URL, str);
    }

    /**
     * 验证yyyy-MM-dd格式的日期校验,已考虑平闰年
     */
    public static boolean isDate(String str) {
        return isMatch(PATTERN_REGEX_DATE, str);
    }

    /**
     * 验证IP地址
     */
    public static boolean isIp(String str) {
        return isMatch(PATTERN_REGEX_IP, str);
    }

    /**
     * 验证车牌号
     */
    public static boolean isCar(String str) {
        return isMatch(PATTERN_REGEX_CAR, str);
    }

    /**
     * 验证人名
     */
    public static boolean isName(String str) {
        return isMatch(PATTERN_REGEX_NAME, str);
    }

    /**
     * 验证mac
     */
    public static boolean isMac(String str) {
        return isMatch(PATTERN_REGEX_MAC, str);
    }

    /**
     * 验证学号只有大小写字母与数字
     */
    public static boolean isStudentNumber(String str) {
        return isMatch(PATTERN_REGEX_NUMBER, str);
    }


    public static boolean isMatch(Pattern pattern, String str) {
        return StrUtil.isNotEmpty(str) && pattern.matcher(str).matches();
    }


    //测试方法
    public static void main(String[] args) {
        String str = "D09009999";
        String pattern = "^(14|15)\\d{7}$|^G\\d{8}$|^P\\d{7}$|^(S|D)\\d{7,8}$\n";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(str);
        System.out.println(m.matches());
    }

}
