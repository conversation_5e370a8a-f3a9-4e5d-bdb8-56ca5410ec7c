package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 选修课发布课程关联的课程信息分页 Request VO
 */
@ApiModel("管理后台 - 选修课发布课程关联的课程信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleaseCoursesReqVO extends PageParam {

    @ApiModelProperty(value = "发布信息ID", required = true, example = "1")
    @NotNull(message = "发布信息ID不能为空")
    private Long releaseId;

    @ApiModelProperty(value = "是否是班主任-限定该班主任班级的发布信息", example = "false-非班主任(默认) true-班主任")
    private Boolean isClassMaster;

    @ApiModelProperty(hidden = true)
    private List<Long> classIdList;

    @ApiModelProperty(value = "选修课名称", example = "1")
    private String courseName;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;
}
