package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel("管理后台 - 选修课发布信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleaseCreateReqVO extends ElectiveReleaseBaseVO {

    @ApiModelProperty(value = "选修课信息列表，包含课程名称、教师名称、教室ID", required = true)
    @NotEmpty(message = "请选择课程")
    private @Valid List<ElectiveReleaseCoursesSubBaseVO> coursesList;

    @ApiModelProperty(value = "班级范围排课ID列表", required = true, example = "[1,2,3]")
    @NotEmpty(message = "班级范围排课ID列表")
    private List<Long> classCourseIdList;
}
