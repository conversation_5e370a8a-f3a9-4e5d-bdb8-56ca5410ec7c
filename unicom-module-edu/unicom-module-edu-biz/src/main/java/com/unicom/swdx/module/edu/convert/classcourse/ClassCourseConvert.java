package com.unicom.swdx.module.edu.convert.classcourse;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangeUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;


@Mapper
public interface ClassCourseConvert {

    ClassCourseConvert INSTANCE = Mappers.getMapper(ClassCourseConvert.class);

    ClassCourseDO convert(ClassCourseCreateReqVO bean);

    ClassCourseDO convert(ClassCourseUpdateReqVO bean);

    ClassCourseRespVO convert(ClassCourseDO bean);

    List<ClassCourseRespVO> convertList(List<ClassCourseDO> list);

    PageResult<ClassCourseRespVO> convertPage(PageResult<ClassCourseDO> page);

    List<ClassCourseExcelVO> convertList02(List<ClassCourseDO> list);

    List<ClassCourseDO> convertToDoList(List<ClassCourseUpdateReqVO> updateList);

    ClassCourseDO convertCourseChangeVOToDO(CourseChangeUpdateReqVO updateReqVO);
}
