package com.unicom.swdx.module.edu.dal.dataobject.ruletemplate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * EduClassManagement DO
 *
 * <AUTHOR>
 */
@TableName("edu_rule_location")
@KeySequence("edu_rule_location_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleTemplateLocationDO extends TenantBaseDO {

    /**
     * 主键id,自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 规则模版表的id
     */
    private Long ruleTemplateId;
    /**
     * 地点名称
     */
    private String locationName;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 打卡范围
     */
    private Integer range;

}
