package com.unicom.swdx.module.edu.dal.dataobject.rollcallrecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 学员点名签到记录 DO
 *
 * <AUTHOR>
 */
@TableName("edu_rollcall_record")
@KeySequence("edu_rollcall_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RollcallRecordDO extends TenantBaseDO {

    /**
     * 记录主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 点名签到表ID
     */
    private Long rollcallId;
    /**
     * 学员ID
     */
    private Long traineeId;
    /**
     * 学员打卡状态 0-未到 1-已到
     */
    private Integer status;
    /**
     * 系统内部门
     */
    private Long deptId;

}
