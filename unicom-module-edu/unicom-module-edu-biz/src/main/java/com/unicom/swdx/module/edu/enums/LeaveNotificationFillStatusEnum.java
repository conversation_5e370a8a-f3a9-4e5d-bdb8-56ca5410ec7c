package com.unicom.swdx.module.edu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 离校报备填报 - 状态枚举
 */
@Getter
@AllArgsConstructor
public enum LeaveNotificationFillStatusEnum {

    /**
     * 未填写
     */
    NOT_FILLED(1, "未填写"),

    /**
     * 离校
     */
    LEAVING(2, "离校"),

    /**
     * 不离校
     */
    STAYING(3, "不离校");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名
     */
    private final String name;

}