package com.unicom.swdx.module.edu.controller.admin.coursechange;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseUpdateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassTimeTableRespVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.WeekTimetableReqVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationPageReqVO;
import com.unicom.swdx.module.edu.service.courseChange.CourseChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "管理后台 - 调课和调课记录")
@RestController
@RequestMapping("/edu/courseChange")
@Validated
public class CourseChangeController {

    @Resource
    private CourseChangeService courseChangeService;

    @PostMapping("/pageList")
    @ApiOperation("获得调课记录分页")
    public CommonResult<PageResult<CourseChangeRespVO>> getCourseChangePage(@RequestBody CourseChangePageReqVO pageVO) {
        PageResult<CourseChangeRespVO> pageResult = courseChangeService.getCourseChangePage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/getCourseChangeInfo")
    @ApiOperation("根据课表id获取最近一次的调课记录")
    public CommonResult<CourseChangeRespVO> getCourseChangeInfoByClassCourseId(@RequestParam("classCourseId") Long classCourseId) {
        CourseChangeRespVO respVO = courseChangeService.getCourseChangeInfoByClassCourseId(classCourseId);
        return success(respVO);
    }

    @GetMapping("/getTimetableOfWeek")
    @ApiOperation("调课页获取某周课表")
    public CommonResult<List<WeekTimetableRespVO>> getTimetableOfWeek(WeekTimetableReqVO reqVO) {
        List<WeekTimetableRespVO> respVO = courseChangeService.getTimetableOfWeek(reqVO);
        return success(respVO);
    }

    @PostMapping("/exchange")
    @ApiOperation("课程互换")
    public CommonResult<Boolean> exchange(@RequestBody CourseExchangeReqVO reqVO) {
        courseChangeService.exchange(reqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation("课程更改")
    public CommonResult<Boolean> change(@RequestBody CourseChangeReqVO reqVO) {
        courseChangeService.change(reqVO);
        return success(true);
    }

    @GetMapping("/getFreeTime")
    @ApiOperation("获取教学计划表中空闲的时间段")
    public CommonResult<List<Object>> getFreeTime(Long classId) {
        return success(courseChangeService.getFreeTime(classId));
    }

    @GetMapping("/export")
    @ApiOperation("导出调课记录")
    @OperateLog(type = EXPORT)
    public void exportExcel(@Valid CourseChangeExportReqVO exportReqVO,
                                              HttpServletResponse response) throws IOException {
        List<CourseChangeExcelVO> list = courseChangeService.getCourseChangeExportList(exportReqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "调课记录.xls",
                "数据", CourseChangeExcelVO.class, null, list, exportReqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/updateClassCourse")
    @ApiOperation("调课时对已有排课的格子替换为另外选的课程")
    public CommonResult<Boolean> updateClassCourse(@Valid @RequestBody CourseChangeUpdateReqVO updateReqVO) {
        courseChangeService.updateClassCourse(updateReqVO);
        return success(true);
    }

}
