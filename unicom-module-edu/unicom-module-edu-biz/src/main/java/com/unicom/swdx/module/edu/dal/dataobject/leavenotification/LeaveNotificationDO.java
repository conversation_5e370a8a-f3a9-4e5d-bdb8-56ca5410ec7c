package com.unicom.swdx.module.edu.dal.dataobject.leavenotification;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 离校报备 DO
 */
@TableName("edu_leave_notification")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveNotificationDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 离校报备名称
     */
    private String name;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班主任ID
     */
    private Long headTeacherId;

    /**
     * 班主任姓名
     */
    private String headTeacherName;

    /**
     * 放假开始时间
     */
    private LocalDateTime startTime;

    /**
     * 放假结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态
     *
     * 枚举 {@link com.unicom.swdx.module.edu.enums.NotificationStatusEnum}
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取创建时间（重写父类方法）
     */
    @Override
    public LocalDateTime getCreateTime() {
        return super.getCreateTime();
    }
}