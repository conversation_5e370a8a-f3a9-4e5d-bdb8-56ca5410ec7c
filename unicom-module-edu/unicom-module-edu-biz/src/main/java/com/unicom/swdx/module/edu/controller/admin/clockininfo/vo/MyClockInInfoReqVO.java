package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

/**
 * @ClassName: MyClockInInfoReqVO
 * @Author: lty
 * @Date: 2024/11/6 16:18
 */
@Data
@ToString(callSuper = true)
public class MyClockInInfoReqVO {

    @ApiModelProperty("学员id")
    private Long traineeId;

    @ApiModelProperty("考勤日期")
    private String date;

}
