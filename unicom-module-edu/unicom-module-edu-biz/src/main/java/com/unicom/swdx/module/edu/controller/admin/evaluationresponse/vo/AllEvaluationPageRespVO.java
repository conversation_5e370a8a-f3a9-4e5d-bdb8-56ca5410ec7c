package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 所有学院评估详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AllEvaluationPageRespVO {
    private String studentName;

    private String className;

    private String courseName;

    private String teacherName;

    private String lectureTime;

    private Integer handle;

    private Integer score;

    private Integer traineeStatus;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;

    private Long classCourseId;

    private Long questionnaireId;

    private String studentId;

    private String teacherId;

    private Boolean department;
}
