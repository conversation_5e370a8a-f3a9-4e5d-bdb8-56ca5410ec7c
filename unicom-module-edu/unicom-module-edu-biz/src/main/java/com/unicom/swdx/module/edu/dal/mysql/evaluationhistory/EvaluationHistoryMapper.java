package com.unicom.swdx.module.edu.dal.mysql.evaluationhistory;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationhistory.EvaluationHistoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 历史评价数据 Mapper
 */
@Mapper
public interface EvaluationHistoryMapper extends BaseMapperX<EvaluationHistoryDO> {

    default PageResult<EvaluationHistoryDO> selectPage(HistoryDataPageReqVO reqVO) {
        return selectPage(reqVO, buildQuery(reqVO));
    }

    default LambdaQueryWrapperX<EvaluationHistoryDO> buildQuery(HistoryDataPageReqVO reqVO) {
        // 创建基本查询
        LambdaQueryWrapperX<EvaluationHistoryDO> query = new LambdaQueryWrapperX<>();

        // 条件查询
        query.likeIfPresent(EvaluationHistoryDO::getKcName, reqVO.getCourseName())
                .likeIfPresent(EvaluationHistoryDO::getJxxs, reqVO.getTeachingForm())
                .likeIfPresent(EvaluationHistoryDO::getOrgName, reqVO.getOrgName())
                .likeIfPresent(EvaluationHistoryDO::getTeacherName, reqVO.getTeacherName())
                .likeIfPresent(EvaluationHistoryDO::getTeacherSource, reqVO.getTeacherSource())
                .likeIfPresent(EvaluationHistoryDO::getClassName, reqVO.getClassName());

        // 处理授课时间范围查询
        if (reqVO.getStartTime() != null || reqVO.getEndTime() != null) {
            // 如果只有开始时间
            if (reqVO.getStartTime() != null && reqVO.getEndTime() == null) {
                LocalDateTime startDateTime = reqVO.getStartTime().atStartOfDay();
                query.ge(EvaluationHistoryDO::getSksj, startDateTime);
            }
            // 如果只有结束时间
            else if (reqVO.getStartTime() == null && reqVO.getEndTime() != null) {
                // 结束时间设置为第二天的00:00:00，使用小于比较，确保不包含下一天的数据
                LocalDateTime endDateTime = reqVO.getEndTime().plusDays(1).atStartOfDay();
                query.lt(EvaluationHistoryDO::getSksj, endDateTime);
            }
            // 如果同时有开始和结束时间
            else if (reqVO.getStartTime() != null && reqVO.getEndTime() != null) {
                // 确保开始日期不晚于结束日期
                if (!reqVO.getStartTime().isAfter(reqVO.getEndTime())) {
                    LocalDateTime startDateTime = reqVO.getStartTime().atStartOfDay();
                    // 结束时间设置为第二天的00:00:00，使用小于比较，确保不包含下一天的数据
                    LocalDateTime endDateTime = reqVO.getEndTime().plusDays(1).atStartOfDay();
                    query.ge(EvaluationHistoryDO::getSksj, startDateTime)
                         .lt(EvaluationHistoryDO::getSksj, endDateTime);
                }
            }
        }

        // 只有当ids非空时才添加此条件
        if (!CollectionUtils.isEmpty(reqVO.getIds())) {
            query.inIfPresent(EvaluationHistoryDO::getId, reqVO.getIds());
        }

        // 排序
        query.orderByDesc(EvaluationHistoryDO::getSksj);

        return query;
    }
}
