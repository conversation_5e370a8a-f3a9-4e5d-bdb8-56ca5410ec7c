package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;
import java.util.Set;

@ApiModel("师资查询分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TeacherInformationPageReqVO extends PageParam {
    @ApiModelProperty(value = "姓名/手机号", example = "unicom", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "师资来源", example = "1", notes = "0校内，1校外")
    private Integer source;

    @ApiModelProperty(value = "排序字段(默认按序号) 0-按id 1-按名称")
    @Range(min = 0, max = 1, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

    @ApiModelProperty(value = "多选id（删除导出时使用）")
    private List<Long> ids;

    @ApiModelProperty(value = "当前页面对应ids序号列表（导出时使用）")
    private List<Long> serialNumberList;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}
