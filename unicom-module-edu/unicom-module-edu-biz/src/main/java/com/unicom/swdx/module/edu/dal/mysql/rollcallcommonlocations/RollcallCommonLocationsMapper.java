package com.unicom.swdx.module.edu.dal.mysql.rollcallcommonlocations;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallcommonlocations.RollcallCommonLocationsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 大课考勤、点名签到常用地点 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RollcallCommonLocationsMapper extends BaseMapperX<RollcallCommonLocationsDO> {

    /**
     * 获取教师常用地点列表
     *
     * @param teacherId 教师id
     * @param type      0 大课考勤、1 点名签到
     * @return 常用地点列表
     */
    default List<RollcallCommonLocationsDO> getListForTeacher(Long teacherId, Integer type) {
        return selectList(new LambdaQueryWrapperX<RollcallCommonLocationsDO>()
                .eq(RollcallCommonLocationsDO::getTeacherId, teacherId)
                .eq(RollcallCommonLocationsDO::getType, type)
                .orderByDesc(RollcallCommonLocationsDO::getUpdateTime));
    }

    /**
     * 根据地址名称和教师查询
     *
     * @param address   地址
     * @param teacherId 教师id
     * @param type      0 大课考勤、1 点名签到
     * @return 查询结果
     */
    default List<RollcallCommonLocationsDO> selectListByAddress(String address,
                                                                Long teacherId,
                                                                Integer type) {
        return selectList(new LambdaQueryWrapperX<RollcallCommonLocationsDO>()
                .eq(RollcallCommonLocationsDO::getAddress, address)
                .eq(RollcallCommonLocationsDO::getTeacherId, teacherId)
                .eq(RollcallCommonLocationsDO::getType, type));
    }

    /**
     * 获取最近一次选择的地点记录
     *
     * @param teacherId 教师id
     * @param type      0 大课考勤、1 点名签到
     * @return 查询结果
     */
    default RollcallCommonLocationsDO getLast(Long teacherId, Integer type) {
        return selectOne(new LambdaQueryWrapperX<RollcallCommonLocationsDO>()
                .eq(RollcallCommonLocationsDO::getTeacherId, teacherId)
                .eq(RollcallCommonLocationsDO::getType, type)
                .orderByDesc(RollcallCommonLocationsDO::getUpdateTime)
                .last("limit 1"));
    }
}
