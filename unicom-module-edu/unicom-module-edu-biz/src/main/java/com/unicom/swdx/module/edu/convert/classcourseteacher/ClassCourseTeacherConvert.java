package com.unicom.swdx.module.edu.convert.classcourseteacher;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;

/**
 * 课程表-教师-授课关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCourseTeacherConvert {

    ClassCourseTeacherConvert INSTANCE = Mappers.getMapper(ClassCourseTeacherConvert.class);

    ClassCourseTeacherDO convert(ClassCourseTeacherCreateReqVO bean);

    ClassCourseTeacherDO convert(ClassCourseTeacherUpdateReqVO bean);

    ClassCourseTeacherRespVO convert(ClassCourseTeacherDO bean);

    List<ClassCourseTeacherRespVO> convertList(List<ClassCourseTeacherDO> list);

    PageResult<ClassCourseTeacherRespVO> convertPage(PageResult<ClassCourseTeacherDO> page);

    List<ClassCourseTeacherExcelVO> convertList02(List<ClassCourseTeacherDO> list);

}
