package com.unicom.swdx.module.edu.service.frequency;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.frequency.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.frequency.FrequencyDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 使用次数 Service 接口
 *
 * <AUTHOR>
 */
public interface FrequencyService {

    /**
     * 创建使用次数
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFrequency(@Valid FrequencyCreateReqVO createReqVO);

    /**
     * 更新使用次数
     *
     * @param updateReqVO 更新信息
     */
    void updateFrequency(@Valid FrequencyUpdateReqVO updateReqVO);

    /**
     * 删除使用次数
     *
     * @param id 编号
     */
    void deleteFrequency(Long id);

    /**
     * 获得使用次数
     *
     * @param id 编号
     * @return 使用次数
     */
    FrequencyDO getFrequency(Long id);

    /**
     * 获得使用次数列表
     *
     * @param ids 编号
     * @return 使用次数列表
     */
    List<FrequencyDO> getFrequencyList(Collection<Long> ids);

    /**
     * 获得使用次数分页
     *
     * @param pageReqVO 分页查询
     * @return 使用次数分页
     */
    PageResult<FrequencyDO> getFrequencyPage(FrequencyPageReqVO pageReqVO);

    /**
     * 获得使用次数列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 使用次数列表
     */
    List<FrequencyDO> getFrequencyList(FrequencyExportReqVO exportReqVO);

    /**
     * 更新使用次数
     */
    void updateByLoginUser();

    /**
     * 获得登录用户的使用次数
     * @return
     */
    FrequencyDO getByLoginUser();
}
