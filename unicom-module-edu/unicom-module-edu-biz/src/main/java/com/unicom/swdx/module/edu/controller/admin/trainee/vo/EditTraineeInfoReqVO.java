package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName: EditTraineeInfoReqVO
 * @Author: lty
 * @Date: 2024/10/14 10:57
 */

@ApiModel("管理后台 - 学员编辑 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EditTraineeInfoReqVO extends TraineeBaseVO{

}
