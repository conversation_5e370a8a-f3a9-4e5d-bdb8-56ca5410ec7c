package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: zhouhk
 * @Date: 2024/12/20
 */

@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeStatByUnitPageRespVO extends TraineeStatPageRespVO{

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "是否委托班（短班级）")
    private Boolean shortClass;

    @ApiModelProperty(value = "政治面貌")
    private String politicalIdentity;

    @ApiModelProperty(value = "文化程度")
    private String educationalLevel;

    @ApiModelProperty(value = "民族")
    private String ethnic;

    @ApiModelProperty(value = "学员状态")
    private String status;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "校区")
    private Integer campus;
}
