package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 我的评估-评分详情 分页查询 Response VO
 * @date 2024-11-19
 */
@ApiModel("我的评估-评分详情 分页查询 Response VO")
@Data
public class MyEvaluationDetailPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "分值（两位小数）", example = "100.00")
    private String scoreStr;

    @ApiModelProperty(value = "分值", example = "100.0000")
    private Float score;

    @ApiModelProperty(value = "人数", example = "5")
    private Integer count;

}
