package com.unicom.swdx.module.edu.dal.mysql.questioncategorymanagement;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.*;

/**
 * 题目类别管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionCategoryManagementMapper extends BaseMapperX<QuestionCategoryManagementDO> {

    default List<QuestionCategoryManagementDO> selectList(QuestionCategoryManagementListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<QuestionCategoryManagementDO>()
                .likeIfPresent(QuestionCategoryManagementDO::getFullName, reqVO.getFullName())
                .eqIfPresent(QuestionCategoryManagementDO::getCreateDept, reqVO.getCreateDept())
                .betweenIfPresent(QuestionCategoryManagementDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(QuestionCategoryManagementDO::getParentId, reqVO.getParentId())
                .eqIfPresent(QuestionCategoryManagementDO::getBuiltIn, reqVO.getBuiltIn())
                .orderByDesc(QuestionCategoryManagementDO::getId));
    }

	default QuestionCategoryManagementDO selectByParentIdAndFullName(Long parentId, String fullName) {
	    return selectOne(QuestionCategoryManagementDO::getParentId, parentId, QuestionCategoryManagementDO::getFullName, fullName);
	}

    default Long selectCountByParentId(Long parentId) {
        return selectCount(QuestionCategoryManagementDO::getParentId, parentId);
    }

    QuestionCategoryManagementDO getDefaultCategory();

    List<QuestionRespVO> selectCategoryList();

    Long selectDefault();

    Long getBuiltInCategoryId();
}