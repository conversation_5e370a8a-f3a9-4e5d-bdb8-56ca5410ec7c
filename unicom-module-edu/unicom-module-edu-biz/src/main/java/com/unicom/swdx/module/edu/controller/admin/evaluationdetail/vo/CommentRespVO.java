package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 意见详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CommentRespVO {

    /**
     * 意见内容
     */
    private String content;

    /**
     * 最低分理由
     */
    @Schema(description = "最低分理由")
    private String lowScoreReason;

}
