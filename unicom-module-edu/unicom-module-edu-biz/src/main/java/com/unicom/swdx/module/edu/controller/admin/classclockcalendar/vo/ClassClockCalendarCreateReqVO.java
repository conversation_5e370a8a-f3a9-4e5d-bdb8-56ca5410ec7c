package com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 班级考勤日历创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassClockCalendarCreateReqVO extends ClassClockCalendarBaseVO {

    @ApiModelProperty(value = "生成的年份")
    private Integer year;

}
