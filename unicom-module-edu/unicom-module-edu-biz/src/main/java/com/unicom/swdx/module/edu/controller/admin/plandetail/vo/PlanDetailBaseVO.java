package com.unicom.swdx.module.edu.controller.admin.plandetail.vo;

import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 教学计划详情 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PlanDetailBaseVO {

    /**
     * 教学计划id
     */
    @ApiModelProperty(value = "教学计划id", required = true)
    @NotNull(message = "教学计划id不能为空")
    private Long planId;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", required = true)
    @NotNull(message = "日期不能为空")
    private String date;

    /**
     * 时间段（0上午，1下午，2晚上）
     */
    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）", required = true)
    @NotNull(message = "时间段（0上午，1下午，2晚上）不能为空")
    private String period;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

}
