package com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 点名签到常用地点 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RollcallCommonLocationsRespVO extends RollcallCommonLocationsBaseVO {

    @ApiModelProperty(value = "主键", required = true, example = "1")
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true, example = "2020-10-31 09:13:26")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", required = true, example = "2020-10-31 09:13:26")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime updateTime;
}
