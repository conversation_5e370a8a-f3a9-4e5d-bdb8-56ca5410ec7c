package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - EduClassManagement更新 Request VO")
@Data
@ToString(callSuper = true)
public class ClassClockingInUpdateReqVO {

    @ApiModelProperty(value = "班级id")
    @NotNull(message = "班级id不能为空")
    private Long classId;

    @ApiModelProperty(value = "到课考勤开关，0-开，1-关")
    private Long attendanceCheck;

    @ApiModelProperty(value = "就餐考勤开关，0-开，1-关")
    private Long mealAttendance;

    @ApiModelProperty(value = "住宿考勤开关，0-开，1-关")
    private Long checkIn;

    @ApiModelProperty(value = "到课考勤id")
    private Long attendanceCheckId;

    @ApiModelProperty(value = "就餐考勤id")
    private Long mealAttendanceId;

    @ApiModelProperty(value = "住宿考勤id")
    private Long checkInId;

    @ApiModelProperty(value = "校区字典值")
    private Integer campus;

}
