package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel("管理后台 - 课程库授课记录 Resp VO")
@Data
public class CoursesTeachingRecordTopicExcelVO {

    @ExcelProperty(value = "专题名称")
    private String courseName;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "授课教师")
    private String teacherNames;

    @ExcelProperty(value = "授课时间")
    private String classDuration;

}
