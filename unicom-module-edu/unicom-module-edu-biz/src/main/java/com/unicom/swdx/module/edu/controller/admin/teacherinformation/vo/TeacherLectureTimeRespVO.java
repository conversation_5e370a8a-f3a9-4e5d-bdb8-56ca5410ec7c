package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("管理后台 - 授课信息 Resp VO")
@Data
public class TeacherLectureTimeRespVO {

    /**
     * 结束时间：格式（"endTime": "2024-11-01 09:00:00"）
     */
    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id", required = true)
    private Long courseId;

    /**
     * 教师id
     */
    @ApiModelProperty(value = "教师id", required = true)
    private Long teacherId;

}
