package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 查询一个班级的考勤三率请求参数
 * @date 2024-11-08
 */
@ApiModel("考勤三率 Request VO")
@Data
public class AttendanceRateReqVO {

    @ApiModelProperty(value = "开始时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "班级id", example = "1")
    @NotNull(message = "班级id不能为空")
    private Long classId;

}
