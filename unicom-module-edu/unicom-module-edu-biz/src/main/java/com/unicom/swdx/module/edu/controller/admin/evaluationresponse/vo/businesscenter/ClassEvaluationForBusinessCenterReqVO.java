package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("业中首页-仪表盘 - 课程评估情况 Request VO")
@Data
public class ClassEvaluationForBusinessCenterReqVO {

    @ApiModelProperty(value = "查询下钻级别 0:全部部门 1:部门 2:个人", example = "1")
    private Integer resType;

    @ApiModelProperty(value = "年度", example = "1")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期 不填全部", example = "1")
    private Integer classTerm;

    @ApiModelProperty(value = "开始时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "部门ID列表范围控制", example = "1")
    private List<Long> deptIdList;

    @ApiModelProperty(value = "部门级-指定部门", example = "1")
    private Long deptId;

    @ApiModelProperty(value = "业中教师用户ID（systemId）", example = "1")
    private Long teacherSystemId;

    @ApiModelProperty(value = "教务教师id - 教师级 查询用", hidden = true)
    private Long teacherId;

    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;

    @ApiModelProperty(value = "限定课程结束时间", hidden = true)
    private LocalDateTime courseEndTime;
}