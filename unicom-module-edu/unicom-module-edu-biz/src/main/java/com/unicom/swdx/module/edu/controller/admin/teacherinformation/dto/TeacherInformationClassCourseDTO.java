package com.unicom.swdx.module.edu.controller.admin.teacherinformation.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 日程安排-携带教师部门、课程教学形式、排课信息DTO
 */

@Data
public class TeacherInformationClassCourseDTO {

    /**
     * 排课id
     */
    private Long classCourseId;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 上课开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 上课结束时间
     */
    private LocalDateTime endTime;

    /**
     * 教师部门id
     */
    private Long teacherDeptId;

    /**
     * 教师id
     */
    private Long teacherId;

    /**
     * 排课课程教学形式id
     */
    private Long educateFormId;

    /**
     * 教室
     */
    private Long classRoomId;

    /**
     * 是否合班授课
     */
    private Boolean isMerge;

    /**
     * 上课日期
     */
    private String date;

    /**
     * 上课时间段
     */
    private Integer period;

    /**
     * 是否部门授课
     */
    private Boolean department;


}
