package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@ApiModel("业中首页-仪表盘-教学课时响应VO")
@Data
public class TeachingHoursForBusinessCenterRespVO {

    @ApiModelProperty(value = "总课时", example = "50")
    private Integer workHoursTotal;

    @ApiModelProperty(value = "总课程数", example = "1")
    private Long courseTimes;
}
