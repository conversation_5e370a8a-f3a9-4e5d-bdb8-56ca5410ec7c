package com.unicom.swdx.module.edu.service.teachercourseinformation;

import cn.hutool.core.collection.CollectionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationSimpleRespVO;
import com.unicom.swdx.module.edu.convert.teachercourseinformation.TeacherCourseInformationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.mysql.teachercourseinformation.TeacherCourseInformationMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.TEACHER_COURSE_INFORMATION_NOT_EXISTS;

/**
 * 师资-任课信息中间 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TeacherCourseInformationServiceImpl implements TeacherCourseInformationService {

    @Resource
    private TeacherCourseInformationMapper teacherCourseInformationMapper;

    @Override
    public Long createTeacherCourseInformation(TeacherCourseInformationCreateReqVO createReqVO) {
        // 插入
        TeacherCourseInformationDO teacherCourseInformation = TeacherCourseInformationConvert.INSTANCE.convert(createReqVO);
        teacherCourseInformationMapper.insert(teacherCourseInformation);
        // 返回
        return teacherCourseInformation.getId();
    }

    @Override
    public Long batchCreateTeacherCourseInformation(TeacherCourseInformationBatchCreateReqVO createReqVO) {
        List<TeacherCourseInformationDO> teacherCourseInformations = new ArrayList<>();
        List<Long> coursesIds = createReqVO.getCoursesIds();
        Long teacherId = createReqVO.getTeacherId();
        for(Long coursesId : coursesIds) {
            TeacherCourseInformationDO teacherCourseInformation = new TeacherCourseInformationDO();
            teacherCourseInformation.setCoursesId(coursesId);
            teacherCourseInformation.setTeacherId(teacherId);
            teacherCourseInformations.add(teacherCourseInformation);
        }
        // 批量插入
        teacherCourseInformationMapper.insertBatch(teacherCourseInformations);
        // 返回
        return teacherCourseInformations.stream().count();
    }

    /**
     * 根据课程id获取其授课教师下拉框
     *
     * @param courseId 课程id
     * @return 下拉框
     */
    @Override
    public List<TeacherInformationSimpleRespVO> getSimpleListByCourseId(Long courseId) {
        return teacherCourseInformationMapper.getSimpleListByCourseId(courseId);
    }

    @Override
    public void updateTeacherCourseInformation(TeacherCourseInformationUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateTeacherCourseInformationExists(updateReqVO.getId());
        // 更新
        TeacherCourseInformationDO updateObj = TeacherCourseInformationConvert.INSTANCE.convert(updateReqVO);
        teacherCourseInformationMapper.updateById(updateObj);
    }

    @Override
    public void deleteTeacherCourseInformation(Long id) {
        // 校验存在
        this.validateTeacherCourseInformationExists(id);
        // 删除
        teacherCourseInformationMapper.deleteById(id);
    }

    private void validateTeacherCourseInformationExists(Long id) {
        if (teacherCourseInformationMapper.selectById(id) == null) {
            throw exception(TEACHER_COURSE_INFORMATION_NOT_EXISTS);
        }
    }

    @Override
    public TeacherCourseInformationDO getTeacherCourseInformation(Long id) {
        return teacherCourseInformationMapper.selectById(id);
    }

    @Override
    public List<TeacherCourseInformationDO> getTeacherCourseInformationList(Collection<Long> ids) {
        if(CollectionUtil.isEmpty(ids))
            return new ArrayList<>();
        return teacherCourseInformationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TeacherCourseInformationDO> getTeacherCourseInformationPage(TeacherCourseInformationPageReqVO pageReqVO) {
        return teacherCourseInformationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TeacherCourseInformationDO> getTeacherCourseInformationList(TeacherCourseInformationExportReqVO exportReqVO) {
        return teacherCourseInformationMapper.selectList(exportReqVO);
    }

}
