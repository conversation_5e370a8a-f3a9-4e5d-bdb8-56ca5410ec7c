package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 班次评估统计 - 学员已评、未评详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraineeEvaluatedAndUnEvaluatedDetailPageReqVO extends PageParam {

    @ApiModelProperty(value = "学员ID", example = "陈平安")
    @NotNull(message = "学员ID不能为空")
    private Long traineeId;

    @ApiModelProperty(value = "课程名称", example = "选修课")
    private String courseName;

    @ApiModelProperty(value = "0-未评 1-已评", example = "1")
    @NotNull(message = "状态不能为空")
    @Range(min = 0, max = 1, message = "状态范围：0-未评 1-已评")
    private Integer status;

    @ApiModelProperty(value = "多选id（导出时使用）")
    private List<Long> ids;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "是否降序排列(默认降序)")
    private Boolean isDesc;
}