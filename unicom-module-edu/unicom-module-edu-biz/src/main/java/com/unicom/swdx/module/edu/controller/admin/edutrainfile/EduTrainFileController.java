package com.unicom.swdx.module.edu.controller.admin.edutrainfile;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFileExcelVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageCreateVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationPageReqVO;
import com.unicom.swdx.module.edu.convert.edutrainfile.EduTrainFileConvert;
import com.unicom.swdx.module.edu.dal.dataobject.edutrainfile.EduTrainFileDO;
import com.unicom.swdx.module.edu.service.edutrainfile.EduTrainFileService;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 *
 *
 * <AUTHOR>
 * @since 2024-12-26 15:13:46
 */
@RestController
@RequestMapping("/edu/eduTrainFile")
@Api(tags = " 控制器")
public class EduTrainFileController {

    @Autowired
    private EduTrainFileService eduTrainFileService;

    /**
     * 分页查询
     *
     * @param pageVo
     * @return
     */
    @ApiOperation(value = "分页查询",notes = "分页查询",produces = "application/json")
    @ApiResponses({@ApiResponse(code = 200, message = "查询成功")})
    @GetMapping("/page")
    public CommonResult<PageResult<EduTrainFilePageRespVO>> findPage(@Valid EduTrainFilePageReqVO pageVo) {
        PageResult<EduTrainFilePageRespVO> result = eduTrainFileService.findPage(pageVo);
        return success(result);
    }

    /**
     * 首页最新五条调训文件
     */
    @ApiOperation(value = "首页最新五条调训文件",notes = "首页最新五条调训文件",produces = "application/json")
    @ApiResponses({@ApiResponse(code = 200, message = "查询成功")})
    @GetMapping("/listForHome")
    public CommonResult<List<EduTrainFilePageRespVO>> listForHome() {
        List<EduTrainFilePageRespVO> result = eduTrainFileService.listForHome();
        return success(result);
    }

    /**
     * 列表查询
     *
     * @param pageVo
     * @return
     */
    @ApiOperation(value = "列表查询",notes = "列表查询",produces = "application/json")
    @ApiResponses({@ApiResponse(code = 200, message = "查询成功")})
    @GetMapping("/list")
    public CommonResult<List<EduTrainFileDO>> findList(@Valid EduTrainFilePageReqVO pageVo) {
        List<EduTrainFileDO> result = eduTrainFileService.findList(pageVo);
        return success(result);
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询", notes = "查询详情")
    @ApiResponses({@ApiResponse(code = 200, message = "查询成功")})
    @GetMapping("/get")
    public CommonResult<EduTrainFileDO> selectById(@RequestParam("id") Long id) {
        EduTrainFileDO eduTrainFileDO = eduTrainFileService.selectById(id);
        return success(eduTrainFileDO);
    }

    /**
     * 新增
     *
     * @param eduTrainFilePageCreateVO
     * @return
     */
    @ApiOperation(value = "新增", notes = "新增数据")
    @ApiResponses({@ApiResponse(code = 200, message = "操作成功")})
    @PostMapping("/create")
    public CommonResult<Boolean> add( @Validated  @RequestBody EduTrainFilePageCreateVO eduTrainFilePageCreateVO) {
        boolean result = eduTrainFileService.add(eduTrainFilePageCreateVO);
        return success(result);
    }

    /**
     * 修改
     *
     * @param eduTrainFilePageCreateVO
     * @return
     */
    @ApiOperation(value = "修改", notes = "修改数据")
    @ApiResponses({@ApiResponse(code = 200, message = "操作成功")})
    @PostMapping("/update")
    public CommonResult<Boolean> update(@Validated @RequestBody EduTrainFilePageCreateVO eduTrainFilePageCreateVO) {
        boolean result = eduTrainFileService.update(eduTrainFilePageCreateVO);
        return success(result);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除", notes = "删除数据")
    @PostMapping("/delete")
    public CommonResult<Integer> deleteById(@RequestParam("id") Long id) {
        int result = eduTrainFileService.deleteById(id);
        return success(result);
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出调训文件")
    @PreAuthorize("@ss.hasPermission('edu:train-file:export')")
    @OperateLog(type = EXPORT)
    public void exportTeacherInformationExcel(@Valid EduTrainFilePageReqVO pageVo,
                                              HttpServletResponse response) throws IOException {
        List<EduTrainFileDO> list = eduTrainFileService.findList(pageVo);

        for (EduTrainFileDO eduTrainFileDO : list) {
            try {
                // 将 JSON 字符串转换为 JSONArray
                JSONArray jsonArray = new JSONArray(eduTrainFileDO.getFile());

                // 存储 fileName 的列表
                List<String> fileNames = new ArrayList<>();

                // 遍历 JSON 数组
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i);

                    // 获取 "r" 字段中的 "data" 数组
                    JSONObject r = item.getJSONObject("response");
                    JSONArray dataArray = r.getJSONArray("data");

                    // 如果 data 数组非空，提取其中的 fileName 字段
                    if (dataArray.size() > 0) {
                        JSONObject dataItem = dataArray.getJSONObject(0);
                        String fileName = dataItem.getStr("fileName");

                        // 添加到文件名列表中
                        fileNames.add(fileName);
                    }
                }

                eduTrainFileDO.setFile(fileNames.stream().reduce((k,v)->k+","+v).get());

            } catch (Exception e) {
                e.printStackTrace();
            }

            eduTrainFileDO.setPublishTime(eduTrainFileDO.getPublishTime().split("\\.")[0]);
        }




        List<EduTrainFileExcelVO> eduTrainFileExcelVOS = EduTrainFileConvert.INSTANCE.fileDOConvertFileExcelVO(list);

        // 导出 Excel
        // ExcelUtils.write(response, "师资信息.xls", "数据", TeacherInformationExcelVO.class, list);
        ExcelUtils.writeByIncludeColumnIndexes(response, "师资库.xls",
                "数据", EduTrainFileExcelVO.class, null, eduTrainFileExcelVOS, pageVo.getIncludeColumnIndexes());
    }

}
