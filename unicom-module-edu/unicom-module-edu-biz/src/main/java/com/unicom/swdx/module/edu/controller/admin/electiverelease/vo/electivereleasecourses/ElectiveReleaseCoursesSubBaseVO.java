package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 选修课发布课程关联 Request VO
 */
@ApiModel("管理后台 - 选修课发布信息中选修课发布课程关联 Request VO")
@Data
public class ElectiveReleaseCoursesSubBaseVO {

    @ApiModelProperty(value = "课程ID", required = true, example = "1")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @ApiModelProperty(value = "授课教师ID", required = true, example = "1")
    @NotNull(message = "授课教师ID不能为空")
    private Long teacherId;

    @ApiModelProperty(value = "上课教室ID", required = true, example = "1")
    @NotNull(message = "上课教室ID不能为空")
    private Long classroomId;

    @ApiModelProperty(value = "课程创建时间", required = true, example = "2020-10-24 01:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}
