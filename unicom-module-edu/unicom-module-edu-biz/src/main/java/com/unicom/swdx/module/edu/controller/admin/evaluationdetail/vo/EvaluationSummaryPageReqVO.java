package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto.TeacherEvaluationExportIdItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 学员评估汇总分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaluationSummaryPageReqVO extends PageParam {
    @ApiModelProperty(value = "班级名称", example = "班级1")
    private String className;

    @ApiModelProperty(value = "课程名称", example = "课程1")
    private String courseName;

    @ApiModelProperty(value = "所属部门")
    private String deptName;

    @ApiModelProperty(value = "教师ID", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "教师姓名", example = "教师1")
    private String teacherName;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}
