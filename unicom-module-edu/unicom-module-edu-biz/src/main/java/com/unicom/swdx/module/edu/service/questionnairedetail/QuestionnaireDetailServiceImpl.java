package com.unicom.swdx.module.edu.service.questionnairedetail;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.questionnairedetail.QuestionnaireDetailMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 评估问卷与问题关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionnaireDetailServiceImpl implements QuestionnaireDetailService {

    @Resource
    private QuestionnaireDetailMapper questionnaireDetailMapper;

    @Override
    public Long createQuestionnaireDetail(QuestionnaireDetailSaveReqVO createReqVO) {
        // 插入
        QuestionnaireDetailDO questionnaireDetail = BeanUtils.toBean(createReqVO, QuestionnaireDetailDO.class);
        questionnaireDetailMapper.insert(questionnaireDetail);
        // 返回
        return questionnaireDetail.getId();
    }

    @Override
    public void updateQuestionnaireDetail(QuestionnaireDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionnaireDetailExists(updateReqVO.getId());
        // 更新
        QuestionnaireDetailDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireDetailDO.class);
        questionnaireDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionnaireDetail(Long id) {
        // 校验存在
        validateQuestionnaireDetailExists(id);
        // 删除
        questionnaireDetailMapper.deleteById(id);
    }

    private void validateQuestionnaireDetailExists(Long id) {
        if (questionnaireDetailMapper.selectById(id) == null) {
            throw exception(QUESTIONNAIRE_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public QuestionnaireDetailDO getQuestionnaireDetail(Long id) {
        return questionnaireDetailMapper.selectById(id);
    }

    @Override
    public PageResult<QuestionnaireDetailDO> getQuestionnaireDetailPage(QuestionnaireDetailPageReqVO pageReqVO) {
        return questionnaireDetailMapper.selectPage(pageReqVO);
    }

}