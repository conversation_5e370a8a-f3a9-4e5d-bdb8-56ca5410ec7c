package com.unicom.swdx.module.edu.convert.courses;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;

/**
 * 课程库 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CoursesConvert {

    CoursesConvert INSTANCE = Mappers.getMapper(CoursesConvert.class);

    CoursesDO convert(CoursesCreateReqVO bean);

    CoursesDO convert(CoursesUpdateReqVO bean);

    CoursesRespVO convert(CoursesDO bean);

    List<CoursesRespVO> convertList(List<CoursesDO> list);

    PageResult<CoursesRespVO> convertPage(PageResult<CoursesDO> page);

    List<CoursesExcelVO> convertList02(List<CoursesRespVO> coursesRespVOList);

    List<CoursesActivityExcelVO> convertList03(List<CoursesRespVO> coursesRespVOList);

    List<CoursesTopicImportExcelVO> convertList04(List<CoursesOptionalImportExcelVO> list);

    List<CoursesTopicImportResultExcelVO> convertList05(List<CoursesTopicImportExcelVO> list);

    List<CoursesOptionalImportResultExcelVO> convertList06(List<CoursesTopicImportResultExcelVO> errorImportList);

    List<CoursesTopicImportResultExcelVO> convertList07(List<CoursesOptionalImportExcelVO> coursesOptionalImportExcelVOS);

    List<CoursesTeachingRecordTopicExcelVO> convertList08(List<CoursesTeachingRecordRespVO> list);

    List<CoursesTeachingRecordElectiveExcelVO> convertList09(List<CoursesTeachingRecordRespVO> list);

    List<CoursesActivityImportResultExcelVO> convertList10(List<CoursesActivityImportExcelVO> list);
}
