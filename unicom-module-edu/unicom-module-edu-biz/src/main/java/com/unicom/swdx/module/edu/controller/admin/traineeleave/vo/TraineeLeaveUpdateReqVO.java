package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * @ClassName: TraineeLeaveCreateReqVO
 * @Author: youxiaoyan
 * @Date: 2024/11/5
 */
@Data
@ApiModel(value = "班主任修改请假内容 request VO")
@Validated
public class TraineeLeaveUpdateReqVO {

    @ApiModelProperty(value = "请假id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull
    private Long id;

    @ApiModelProperty(value = "请假类型", required = true)
    private Integer leaveType;

    @ApiModelProperty(value = "请假开始时间", required = true)
    private String startTimeStr;

    @ApiModelProperty(value = "请假结束时间", required = true)
    private String endTimeStr;

    @ApiModelProperty(value = "请假天数", required = true)
    private Float days;

    @ApiModelProperty(value = "请假事由")
    private String reason;

    @ApiModelProperty(value = "附件")
    private String accessory;

    @ApiModelProperty(value = "修改事由")
    private String modifyReason;
}

