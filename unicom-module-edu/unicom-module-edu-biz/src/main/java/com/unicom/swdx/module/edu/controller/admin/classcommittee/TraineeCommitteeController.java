package com.unicom.swdx.module.edu.controller.admin.classcommittee;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcommittee.ClassCommitteeDO;
import com.unicom.swdx.module.edu.service.classcommittee.ClassCommitteeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 班委")
@RestController
@RequestMapping("/edu/classCommittee")
@Validated
public class TraineeCommitteeController {

    @Resource
    private ClassCommitteeService classCommitteeService;

    /**
     * 新增班委
     * @param reqVO
     * @return
     */
    @PostMapping("/addClassCommittee")
    @ApiOperation(value = "新增班委")
    @PreAuthorize("@ss.hasPermission('edu:class-committee:add')")
    public CommonResult<Long> addClassCommittee(@RequestBody ClassCommitteeReqVO reqVO) {
        return success(classCommitteeService.addClassCommittee(reqVO));
    }

    /**
     * 编辑班委
     * @param reqVO
     * @return
     */
    @PostMapping("/editClassCommittee")
    @ApiOperation(value = "编辑班委")
    @PreAuthorize("@ss.hasPermission('edu:class-committee:edit')")
    public CommonResult<Integer> editClassCommittee(@RequestBody ClassCommitteeReqVO reqVO) {
        return success(classCommitteeService.editClassCommittee(reqVO));
    }

    /**
     * 删除班委
     * @param id
     * @return
     */
    @PostMapping("/deleteClassCommittee")
    @ApiOperation(value = "删除班委")
    @PreAuthorize("@ss.hasPermission('edu:class-committee:delete')")
    public CommonResult<Boolean> deleteClassCommittee(Long id) {
        return success(classCommitteeService.deleteClassCommittee(id));
    }

    /**
     * 查询班委
     * @param
     * @return
     */
    @GetMapping("/getClassCommittee")
    @ApiOperation(value = "查询")
    @PreAuthorize("@ss.hasPermission('edu:class-committee:get')")
    public CommonResult<List<Map<String,String>>> getClassCommittee(Long classId) {
        return success(classCommitteeService.getClassCommittee(classId));
    }

    /**
     * 查询班委List
     * @param
     * @return
     */
    @GetMapping("/getClassCommitteeList")
    @ApiOperation(value = "查询班委List")
    @PreAuthorize("@ss.hasPermission('edu:class-committee:get')")
    public CommonResult<List<ClassCommitteeRespVO>> getClassCommitteeList(Long classId, String name) {
        return success(classCommitteeService.getClassCommitteeList(classId,name));
    }

}
