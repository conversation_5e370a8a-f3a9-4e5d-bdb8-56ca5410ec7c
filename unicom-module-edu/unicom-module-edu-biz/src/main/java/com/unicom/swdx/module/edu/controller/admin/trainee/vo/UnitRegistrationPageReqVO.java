package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class UnitRegistrationPageReqVO extends PageParam {
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "学员状态")
    private Integer status;

    @ApiModelProperty(value = "单位名称 1未报到 2已报到")
    private Integer reportStatus;

}
