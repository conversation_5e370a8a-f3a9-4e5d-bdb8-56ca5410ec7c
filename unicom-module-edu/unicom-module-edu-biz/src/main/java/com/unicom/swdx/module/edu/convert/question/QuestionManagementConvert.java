package com.unicom.swdx.module.edu.convert.question;

import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface QuestionManagementConvert {
    QuestionManagementConvert Instance = Mappers.getMapper(QuestionManagementConvert.class);

    QuestionManagementRespVO convertDO(QuestionManagementDO questionManagementDO);

    List<QuestionManagementRespVO> convertList(List<QuestionManagementDO> questionManagementList);
}
