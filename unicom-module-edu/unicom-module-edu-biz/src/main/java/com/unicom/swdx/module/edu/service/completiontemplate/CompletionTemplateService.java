package com.unicom.swdx.module.edu.service.completiontemplate;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 结业考核模版设置 Service 接口
 *
 * <AUTHOR>
 */
public interface CompletionTemplateService {

    /**
     * 创建结业考核模版设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createCompletionTemplate(@Valid CompletionTemplateCreateReqVO createReqVO);

    /**
     * 创建结业考核模版设置
     *
     * @param addReqVO 创建信息
     * @return 编号
     */
    List<Integer> addCompletionTemplate(@Valid List<CompletionTemplateCreateReqVO> addReqVO);
    /**
     * 更新结业考核模版设置
     *
     * @param updateReqVO 更新信息
     */
    void updateCompletionTemplate(@Valid CompletionTemplateUpdateReqVO updateReqVO);
    /**
     * 更新结业考核模版设置
     *
     * @param editReqVO 更新信息
     */
    void updateCompletionTemplateData(@Valid List<CompletionTemplateUpdateReqVO> editReqVO);
    /**
     * 删除结业考核模版设置
     *
     * @param id 编号
     */
    void deleteCompletionTemplate(Integer id);
    /**
     * 删除模版
     *
     * @param idCode 模版编码
     */
    void deleteCompletionTemplateByName(String idCode);

    /**
     * 批量删除模版
     *
     * @param idCode 模版编码
     */
    void deleteCompletionTemplateByNameBatch(List<String> idCode);

    /**
     * 获得结业考核模版设置
     *
     * @param id 编号
     * @return 结业考核模版设置
     */
    CompletionTemplateDO getCompletionTemplate(Integer id);

    /**
     * 获得结业考核模版设置列表
     *
     * @param idCode 编号
     * @return 结业考核模版设置列表
     */
    List<CompletionTemplateDO> getCompletionTemplateList(String idCode);

    /**
     * 获得结业考核模版设置分页
     *
     * @param pageReqVO 分页查询
     * @return 结业考核模版设置分页
     */
    PageResult<CompletionTemplateDO> getCompletionTemplatePage(HttpServletRequest request,CompletionTemplatePageReqVO pageReqVO);

    List<CompletionTemplateDO> getCompletionTemplatePageList(HttpServletRequest request, CompletionTemplatePageReqVO pageReqVO);

    /**
     * 获得结业考核模版设置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 结业考核模版设置列表
     */
    List<CompletionTempleExcelVO> getCompletionTemplateListExport(CompletionTemplateExportReqVO exportReqVO);

    void exportExcel(HttpServletRequest request,Integer classId,Integer type ,HttpServletResponse response) throws IOException;

    ClassCompletionRespVO getClassCompletion(Long classId);

    void regenerate(Long classId);

    List<ClassCompletionInfoRespVO> getClassCompletionInfo(HttpServletRequest request,Integer classId);

    boolean saveInfo(List<SaveClassCompletionInfoReqVO> list);

    Boolean generationTemplate();

    SchoolFeedbackFormRespVO schoolFeedbackForm(Long classId);
}
