package com.unicom.swdx.module.edu.service.frequency;

import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import org.apache.catalina.security.SecurityUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.frequency.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.frequency.FrequencyDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.frequency.FrequencyConvert;
import com.unicom.swdx.module.edu.dal.mysql.frequency.FrequencyMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 使用次数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FrequencyServiceImpl implements FrequencyService {

    @Resource
    private FrequencyMapper frequencyMapper;

    @Override
    public Long createFrequency(FrequencyCreateReqVO createReqVO) {
        // 插入
        FrequencyDO frequency = FrequencyConvert.INSTANCE.convert(createReqVO);
        frequencyMapper.insert(frequency);
        // 返回
        return frequency.getId();
    }

    @Override
    public void updateFrequency(FrequencyUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateFrequencyExists(updateReqVO.getId());
        // 更新
        FrequencyDO updateObj = FrequencyConvert.INSTANCE.convert(updateReqVO);
        frequencyMapper.updateById(updateObj);
    }

    @Override
    public void deleteFrequency(Long id) {
        // 校验存在
        this.validateFrequencyExists(id);
        // 删除
        frequencyMapper.deleteById(id);
    }

    private void validateFrequencyExists(Long id) {
        if (frequencyMapper.selectById(id) == null) {
            throw exception(FREQUENCY_NOT_EXISTS);
        }
    }

    @Override
    public FrequencyDO getFrequency(Long id) {
        return frequencyMapper.selectById(id);
    }

    @Override
    public List<FrequencyDO> getFrequencyList(Collection<Long> ids) {
        return frequencyMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<FrequencyDO> getFrequencyPage(FrequencyPageReqVO pageReqVO) {
        return frequencyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<FrequencyDO> getFrequencyList(FrequencyExportReqVO exportReqVO) {
        return frequencyMapper.selectList(exportReqVO);
    }

    @Override
    public void updateByLoginUser() {
        // 获取当前登录用户
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 查询当前用户的记录是否存在
        FrequencyDO frequencyDO = frequencyMapper.selectOne("user_id", userId);
        if (frequencyDO == null) {
            // 如果不存在，则创建一条记录
            FrequencyCreateReqVO createReqVO = new FrequencyCreateReqVO();
            createReqVO.setUserId(userId);
            createReqVO.setNumberOfTimes(1L);
            createFrequency(createReqVO);
        }else {
            //如果存在，则次数加一
            frequencyDO.setNumberOfTimes(frequencyDO.getNumberOfTimes() + 1);
            frequencyMapper.updateById(frequencyDO);
        }
    }

    @Override
    public FrequencyDO getByLoginUser(){
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        return frequencyMapper.selectOne("user_id", userId);
    }

}
