package com.unicom.swdx.module.edu.service.questionnairedetail;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 评估问卷与问题关联 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionnaireDetailService {

    /**
     * 创建评估问卷与问题关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionnaireDetail(@Valid QuestionnaireDetailSaveReqVO createReqVO);

    /**
     * 更新评估问卷与问题关联
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionnaireDetail(@Valid QuestionnaireDetailSaveReqVO updateReqVO);

    /**
     * 删除评估问卷与问题关联
     *
     * @param id 编号
     */
    void deleteQuestionnaireDetail(Long id);

    /**
     * 获得评估问卷与问题关联
     *
     * @param id 编号
     * @return 评估问卷与问题关联
     */
    QuestionnaireDetailDO getQuestionnaireDetail(Long id);

    /**
     * 获得评估问卷与问题关联分页
     *
     * @param pageReqVO 分页查询
     * @return 评估问卷与问题关联分页
     */
    PageResult<QuestionnaireDetailDO> getQuestionnaireDetailPage(QuestionnaireDetailPageReqVO pageReqVO);

}