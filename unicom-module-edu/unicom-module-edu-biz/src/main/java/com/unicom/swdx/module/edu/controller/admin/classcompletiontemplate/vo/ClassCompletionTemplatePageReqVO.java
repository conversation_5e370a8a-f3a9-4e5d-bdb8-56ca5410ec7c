package com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 结业考核模版设置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassCompletionTemplatePageReqVO extends PageParam {

    @ApiModelProperty(value = "序列号")
    private String serialNumber;

    @ApiModelProperty(value = "二级列名")
    private String columnName;

    @ApiModelProperty(value = "换算公式")
    private String conversionAnnouncement;

    @ApiModelProperty(value = "分值上限")
    private Integer maxScore;

    @ApiModelProperty(value = "初始分")
    private Integer initialScore;

    @ApiModelProperty(value = "获取方式,0-初始值上修改，1-自动获取，2-固定值")
    private Integer acquisitionMode;

    @ApiModelProperty(value = "数据来源，0-事假、1-病假、2-五会假、3-到课率、4-就餐率、5-住宿率、6-评课率、7-迟到次数")
    private Integer dataSource;

    @ApiModelProperty(value = "考核名称")
    private String assessmentName;

    @ApiModelProperty(value = "所属校区")
    private Integer campus;

    @ApiModelProperty(value = "默认规则，0-是，1-否")
    private Integer defaultRule;

    @ApiModelProperty(value = "模版名称")
    private String templateName;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    @ApiModelProperty(value = "唯一编码")
    private String idCode;

    @ApiModelProperty(value = "班级id")
    private Long classId;

}
