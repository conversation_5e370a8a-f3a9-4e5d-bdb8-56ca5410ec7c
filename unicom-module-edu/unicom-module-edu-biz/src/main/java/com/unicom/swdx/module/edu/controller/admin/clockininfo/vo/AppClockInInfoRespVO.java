package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @ClassName: AppClockInInfoRespVO
 * @Author: ZHK
 * @Date: 2024/11/6 16:18
 */
@Data
@ToString(callSuper = true)
public class AppClockInInfoRespVO{

    /**
     * 打卡的学员id
     */
    @ApiModelProperty(value = "打卡的学员id")
    private Long traineeId;

    /**
     * 打卡开始时间
     */
    @ApiModelProperty(value = "打卡开始时间")
    private LocalDateTime checkBeginTime;

    /**
     * 打卡结束时间
     */
    @ApiModelProperty(value = "打卡结束时间")
    private LocalDateTime checkEndTime;

    /**
     * 考勤地点（经纬度）
     */
    @ApiModelProperty(value = "考勤地点（经纬度）")
    private String areaPoint;

    /**
     * 考勤范围
     */
    @ApiModelProperty(value = "考勤范围")
    private String range;

    /**
     * 打卡状态
     */
    @ApiModelProperty(value = "打卡状态")
    private String status;

    /**
     * 打卡记录Id（用于签到接口传参）
     */
    @ApiModelProperty(value = "打卡记录Id（用于签到接口传参）")
    private Integer checkId;

    /**
     * 发起的考勤Id（大课考勤或者打卡记录id）
     */
    @ApiModelProperty(value = "发起的考勤Id")
    private Long signInId;

    /**
     * 上课开始时间
     */
    @ApiModelProperty(value = "上课开始时间")
    private LocalDateTime beginTime;

    /**
     * 上课结束时间
     */
    @ApiModelProperty(value = "上课结束时间")
    private LocalDateTime endTime;

    /**
     * 任课老师
     */
    @ApiModelProperty(value = "任课老师")
    private String teacherName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseName;

    /**
     * 上课地点（教室）
     */
    @ApiModelProperty(value = "上课地点（教室）")
    private String className;

    /**
     * 班级课程表Id
     */
    @ApiModelProperty(value = "班级课程表Id")
    private Long classCourseId;


    /**
     * 班级Id
     */
    @ApiModelProperty(value = "班级Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    //住宿考勤信息

    /**
     * 住宿地点（宿舍）
     */
    @ApiModelProperty(value = "住宿地点（宿舍）")
    private String hostelName;

    //就餐考勤信息

    /**
     * 就餐地点
     */
    @ApiModelProperty(value = "就餐地点")
    private String canteenName;

    /**
     * 早餐打卡时间范围
     */
    @ApiModelProperty(value = "早餐打卡时间范围")
    private String breakfastTimeRange;

    /**
     * 午餐打卡时间范围
     */
    @ApiModelProperty(value = "午餐打卡时间范围")
    private String lunchTimeRange;

    /**
     * 晚餐打卡时间范围
     */
    @ApiModelProperty(value = "晚餐打卡时间范围")
    private String dinnerTimeRange;

    //点名签到

    /**
     * 签到标题
     */
    @ApiModelProperty(value = "签到标题")
    private String title;

    /**
     * 打卡地点
     */
    @ApiModelProperty(value = "打卡地点")
    private String location;

}
