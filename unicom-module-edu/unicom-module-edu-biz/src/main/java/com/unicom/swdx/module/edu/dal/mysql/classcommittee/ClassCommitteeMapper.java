package com.unicom.swdx.module.edu.dal.mysql.classcommittee;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcommittee.ClassCommitteeDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineegroup.TraineeGroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCommitteeMapper extends BaseMapperX<ClassCommitteeDO> {

}
