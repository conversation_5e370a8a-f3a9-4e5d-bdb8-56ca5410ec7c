package com.unicom.swdx.module.edu.service.evaluationhistory.impl;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageRespVO;
import com.unicom.swdx.module.edu.convert.evaluationhistory.EvaluationHistoryConvert;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationhistory.EvaluationHistoryDO;
import com.unicom.swdx.module.edu.dal.mysql.evaluationhistory.EvaluationHistoryMapper;
import com.unicom.swdx.module.edu.service.evaluationhistory.EvaluationHistoryService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 历史评价数据 Service 实现类
 */
@Service
@Validated
public class EvaluationHistoryServiceImpl implements EvaluationHistoryService {

    @Resource
    private EvaluationHistoryMapper evaluationHistoryMapper;

    @Override
    public PageResult<HistoryDataPageRespVO> getHistoryDataPage(HistoryDataPageReqVO pageReqVO) {
        // 处理日期范围查询 - 对日期进行处理，确保包含整天的数据
        processDateTimeRange(pageReqVO);

        PageResult<EvaluationHistoryDO> pageResult = evaluationHistoryMapper.selectPage(pageReqVO);
        return EvaluationHistoryConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public List<HistoryDataExcelVO> getHistoryDataExportList(HistoryDataPageReqVO pageReqVO) {
        // 处理日期范围查询 - 对日期进行处理，确保包含整天的数据
        processDateTimeRange(pageReqVO);

        // 查询全部数据
        List<EvaluationHistoryDO> list = evaluationHistoryMapper.selectList(
                evaluationHistoryMapper.buildQuery(pageReqVO).orderByDesc(EvaluationHistoryDO::getSksj));
        return EvaluationHistoryConvert.INSTANCE.convertList02(list);
    }

    /**
     * 处理日期范围，将LocalDate转换为LocalDateTime供Mapper查询使用
     * 确保开始时间包含当天的开始，结束时间包含当天的结束
     * 
     * @param pageReqVO 查询请求参数
     */
    private void processDateTimeRange(HistoryDataPageReqVO pageReqVO) {
        // 这部分不需要处理，因为Mapper会自动处理LocalDate到LocalDateTime的转换
        // Mapper内部会使用between查询，将开始日期和结束日期转换成合适的时间范围
    }
}
