package com.unicom.swdx.module.edu.controller.admin.todoitems.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班主任待办事项 报名确认内容详情
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ECContentDTO {

    /**
     * 未确认人数
     */
    private Integer unconfirmedNumber;

    /**
     * 已确认人数
     */
    private Integer confirmedNumber;

    /**
     * 班级总人数
     */
    private Integer totalClassSize;

    /**
     * 该类转json字符串
     */
    public String toJsonString() {
        return "{\"unconfirmedNumber\":" + unconfirmedNumber
                + ",\"confirmedNumber\":" + confirmedNumber
                + ",\"totalClassSize\":" + totalClassSize + "}";
    }
}
