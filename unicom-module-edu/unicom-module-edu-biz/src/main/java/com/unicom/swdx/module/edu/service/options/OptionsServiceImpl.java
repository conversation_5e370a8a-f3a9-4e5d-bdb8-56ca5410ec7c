package com.unicom.swdx.module.edu.service.options;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.options.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.options.OptionsMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OptionsServiceImpl implements OptionsService {

    @Resource
    private OptionsMapper optionsMapper;

    @Override
    public Long createOptions(OptionsSaveReqVO createReqVO) {
        // 插入
        OptionsDO options = BeanUtils.toBean(createReqVO, OptionsDO.class);
        optionsMapper.insert(options);
        // 返回
        return options.getId();
    }

    @Override
    public void updateOptions(OptionsSaveReqVO updateReqVO) {
        // 校验存在
        validateOptionsExists(updateReqVO.getId());
        // 更新
        OptionsDO updateObj = BeanUtils.toBean(updateReqVO, OptionsDO.class);
        optionsMapper.updateById(updateObj);
    }

    @Override
    public void deleteOptions(Long id) {
        // 校验存在
        validateOptionsExists(id);
        // 删除
        optionsMapper.deleteById(id);
    }

    private void validateOptionsExists(Long id) {
        if (optionsMapper.selectById(id) == null) {
            throw exception(OPTIONS_NOT_EXISTS);
        }
    }

    @Override
    public OptionsDO getOptions(Long id) {
        return optionsMapper.selectById(id);
    }

    @Override
    public PageResult<OptionsDO> getOptionsPage(OptionsPageReqVO pageReqVO) {
        return optionsMapper.selectPage(pageReqVO);
    }

}