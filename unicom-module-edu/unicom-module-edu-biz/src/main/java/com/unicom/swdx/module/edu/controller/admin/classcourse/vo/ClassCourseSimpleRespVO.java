package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("班级课程安排下拉列表 Resp VO")
@Data
public class ClassCourseSimpleRespVO {

    @ApiModelProperty(value = "排课表id", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "课程id", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "1")
    private String courseName;

    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课、3-教学活动)", example = "1")
    private Integer courseType;

    @ApiModelProperty(value = "上课日期", example = "2024-10-15")
    private String classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    private Integer dayPeriod;

    @ApiModelProperty(value = "上课时段开始时间", example = "00:00")
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课时段结束时间", example = "00:00")
    private String classEndTimeStr;

    /**
     * 上课开始时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classStartTime;

    /**
     * 上课结束时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classEndTime;


}
