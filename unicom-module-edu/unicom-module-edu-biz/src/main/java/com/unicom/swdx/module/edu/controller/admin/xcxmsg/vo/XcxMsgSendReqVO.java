package com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(description = "一键发送小程序服务通知请求体")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XcxMsgSendReqVO {

    @ApiModelProperty(value = "学员ID列表")
    private List<Long> traineeIds;

    @ApiModelProperty(value = "通知类型：班级考勤-1，点名签到-2，大课考勤-3，选课提醒-4，课程评价-5")
    private Integer informType;

    @ApiModelProperty(value = "类型是班级考勤提醒时，还需要把考勤项传入，例如：早餐；其他类型不传")
    private String informItem;

    @ApiModelProperty(value = "类型是点名签到/大课考勤/班级考勤时，需要把打卡开始和结束时间用波浪号连接传入，例如：2024-11-11 10:00:00~11:00:00")
    private String informPeriod;

    @ApiModelProperty(value = "跳转参数，只能携带一个，格式为参数名+下划线+值，如：type_2")
    private String redirectParam;

}
