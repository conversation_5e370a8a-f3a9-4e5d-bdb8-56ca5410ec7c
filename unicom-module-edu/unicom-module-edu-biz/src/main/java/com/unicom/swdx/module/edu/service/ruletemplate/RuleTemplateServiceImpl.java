package com.unicom.swdx.module.edu.service.ruletemplate;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO;
import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.*;
import com.unicom.swdx.module.edu.convert.ruletemplate.RuleTemplateConvert;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateLocationDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.ruletemplate.RuleTemplateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 考勤规则模版 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RuleTemplateServiceImpl extends ServiceImpl<RuleTemplateMapper,RuleTemplateDO> implements RuleTemplateService {

    @Resource
    private RuleTemplateMapper ruleTemplateMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    /**
     * 根据班级id获取到课考勤规则
     * @param classId 班级id
     * @return 到课考勤规则
     */
    @Override
    public RuleTemplateRespVO getAttendanceCheckRuleTemplateByClassId(Long classId){
        // 获取该节课的到课考勤时间信息
        ClassRuleClockingInVO classClockInByAttendanceCheck = classManagementMapper.getClassClockInByAttendanceCheck(classId);
        // 获取到课考勤规则
        if (classClockInByAttendanceCheck == null || classClockInByAttendanceCheck.getAttendanceCheck() == null) {
            log.info("classId = {}, 到课考勤规则模板未配置", classId);
            throw exception(RULE_TEMPLATE_CLASS_NOT_EXISTS);
        }
        Long attendanceCheckId = classClockInByAttendanceCheck.getAttendanceCheck();
        RuleTemplateRespVO ruleTemplate = this.getRuleTemplate(attendanceCheckId);
        // 根据考勤规则中的上课前打卡时间、上课后打卡时间，得出并设置返回类中的打卡时间范围
        if (ruleTemplate == null) {
            log.info("classId = {}, 到课考勤规则模板id = {}不存在", classId, attendanceCheckId);
            throw exception(RULE_TEMPLATE_NOT_EXISTS);
        }
        return ruleTemplate;
    }

    @Override
    public Long createRuleTemplate(RuleTemplateCreateReqVO createReqVO) {

        //校区唯一性校验  一个校区只能拥有一个默认规则
        //只有使用默认规则的情况下才需要判断唯一性
        if(createReqVO.getDefaultRule() == 0){
            campusDefaultRules(null, createReqVO.getCampus(), createReqVO.getRuleType());
        }

        //规则名称唯一性校验
        campusNameRules(null,createReqVO.getRuleType() ,createReqVO.getRuleName());

        // 插入
        RuleTemplateDO ruleTemplate = RuleTemplateConvert.INSTANCE.convert(createReqVO);
        ruleTemplateMapper.insert(ruleTemplate);


        for(RuleTemplateLocationVO list : createReqVO.getRuleLocationList()){
            //在规则表中插入地点信息
            RuleTemplateLocationDO ruleTemplateLocationDO = new RuleTemplateLocationDO();

            ruleTemplateLocationDO.setRuleTemplateId(ruleTemplate.getId());
            ruleTemplateLocationDO.setLocationName(list.getLocationName());
            ruleTemplateLocationDO.setLatitude(list.getLatitude());
            ruleTemplateLocationDO.setLongitude(list.getLongitude());
            ruleTemplateLocationDO.setRange(list.getRange());

            ruleTemplateMapper.insertLocalInfo(ruleTemplateLocationDO);
        }


        // 返回
        return ruleTemplate.getId();
    }

    @Override
    public void updateRuleTemplate(RuleTemplateUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateRuleTemplateExists(updateReqVO.getId());


        //只有默认规则的情况下才需要判断唯一性
        if(updateReqVO.getDefaultRule() == 0){
            //校验校区默认规则
            campusDefaultRules(updateReqVO.getId(), updateReqVO.getCampus(), updateReqVO.getRuleType());
        }

        //规则名称唯一性校验
        campusNameRules(updateReqVO.getId(), updateReqVO.getRuleType(), updateReqVO.getRuleName());

        //禁用规则前判断该规则是否有班级在使用
        if(updateReqVO.getStatus() == 1){
            // 判断规则是否有班级在使用
            Integer count = ruleTemplateMapper.getClassClockCount(updateReqVO.getId());
            if(count > 0){
                throw exception(RULE_TEMPLATE_BAN_STATUS);
            }
        }


        // 更新
        RuleTemplateDO updateObj = RuleTemplateConvert.INSTANCE.convert(updateReqVO);
        ruleTemplateMapper.updateById(updateObj);

        //先删再插
        ruleTemplateMapper.deletedRuleLocation(updateReqVO.getId());

        // 更新中间表数据
        for(RuleTemplateLocationVO list : updateReqVO.getRuleLocationList()){
            //在规则表中插入地点信息
            RuleTemplateLocationDO ruleTemplateLocationDO = new RuleTemplateLocationDO();

            ruleTemplateLocationDO.setRuleTemplateId(updateReqVO.getId());
            ruleTemplateLocationDO.setLocationName(list.getLocationName());
            ruleTemplateLocationDO.setLatitude(list.getLatitude());
            ruleTemplateLocationDO.setLongitude(list.getLongitude());
            ruleTemplateLocationDO.setRange(list.getRange());

            ruleTemplateMapper.insertLocalInfo(ruleTemplateLocationDO);
        }

        //使缓存失效
        String cacheKey = "getRuleTemplate" + updateReqVO.getId();
        resultCache.invalidate(cacheKey);
    }

    @Override
    public void deleteRuleTemplate(Long id) {
        // 校验存在
        this.validateRuleTemplateExists(id);

        // 判断规则是否有班级在使用
        Integer count = ruleTemplateMapper.getClassClockCount(id);
        if(count > 0){
            throw exception(RULE_TEMPLATE_BIND_EXISTS);
        }

        //删除中间表的数据
        ruleTemplateMapper.deletedRuleLocation(id);

        // 删除
        ruleTemplateMapper.deleteById(id);
    }

    /**
     * 批量删除考勤规则模版
     *
     * @param ruleBatchDeleteVO
     */
    @Override
    public void deleteRuleTemplateBatch(RuleBatchDeleteVO ruleBatchDeleteVO) {

        String ids = ruleBatchDeleteVO.getIds();
        //用,分割
        String[] arrayId = ids.split(",");

        //将数组转成一个 List<Integer>
        List<Integer> idList = Arrays.stream(arrayId)
                .map(Integer::parseInt)
                .collect(Collectors.toList());

       for(Integer id : idList){
           // 判断规则是否有班级在使用
           Integer count = ruleTemplateMapper.getClassClockCount(Long.valueOf(id));
           if(count > 0){
               throw exception(RULE_TEMPLATE_BIND_BATCH_EXISTS);
           }
       }

        ruleTemplateMapper.deleteBatchIds(idList);

        for(Integer id : idList){
            //删除中间表的数据
            ruleTemplateMapper.deletedRuleLocation(Long.valueOf(id));
        }

    }

    private void validateRuleTemplateExists(Long id) {
        if (ruleTemplateMapper.selectById(id) == null) {
            throw exception(RULE_TEMPLATE_NOT_EXISTS);
        }
    }



    public static Cache<String, RuleTemplateRespVO > resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(1024)   // 设定最大容量
                    .expireAfterWrite(5L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    public RuleTemplateRespVO getRuleTemplate(Long id) {


        RuleTemplateRespVO  listtemp =null;
        try {
            listtemp = resultCache.get("getRuleTemplate" + id, () -> {
                RuleTemplateRespVO ruleTemplateRespVO = new RuleTemplateRespVO();

                //获取规则表的信息
                RuleTemplateDO listRule = ruleTemplateMapper.selectById(id);

                if(listRule == null){
                    throw exception(RULE_TEMPLATE_NOT_EXISTS);
                }

                ruleTemplateRespVO.setId(listRule.getId());
                ruleTemplateRespVO.setCreateTime(listRule.getCreateTime());
                ruleTemplateRespVO.setRuleName(listRule.getRuleName());
                ruleTemplateRespVO.setRuleType(listRule.getRuleType());
                ruleTemplateRespVO.setStatus(listRule.getStatus());
                ruleTemplateRespVO.setCampus(listRule.getCampus());
                ruleTemplateRespVO.setDefaultRule(listRule.getDefaultRule());

                if(listRule.getBeforeClassTime() != null){
                    ruleTemplateRespVO.setBeforeClassTime(listRule.getBeforeClassTime());
                }

                if(listRule.getAfterClassTime() != null){
                    ruleTemplateRespVO.setAfterClassTime(listRule.getAfterClassTime());
                }

                if(listRule.getBreakfastStartTime() != null){
                    ruleTemplateRespVO.setBreakfastStartTime(listRule.getBreakfastStartTime());
                }

                if(listRule.getBreakfastEndTime() != null){
                    ruleTemplateRespVO.setBreakfastEndTime(listRule.getBreakfastEndTime());
                }

                if(listRule.getLunchStartTime() != null){
                    ruleTemplateRespVO.setLunchStartTime(listRule.getLunchStartTime());
                }

                if(listRule.getLunchEndTime() != null){
                    ruleTemplateRespVO.setLunchEndTime(listRule.getLunchEndTime());
                }

                if(listRule.getDinnerStartTime() != null){
                    ruleTemplateRespVO.setDinnerStartTime(listRule.getDinnerStartTime());
                }

                if(listRule.getDinnerEndTime() != null){
                    ruleTemplateRespVO.setDinnerEndTime(listRule.getDinnerEndTime());
                }

                if(listRule.getPutUpStartTime() != null){
                    ruleTemplateRespVO.setPutUpStartTime(listRule.getPutUpStartTime());
                }

                if(listRule.getPutUpEndTime() != null){
                    ruleTemplateRespVO.setPutUpEndTime(listRule.getPutUpEndTime());
                }


                //获取中间表的信息  考勤地点
                List<RuleTemplateLocationDO> lists = ruleTemplateMapper.selectListTemplateLocation(id);


                // 初始化 ruleLocationList，如果还没有初始化
                if (ruleTemplateRespVO.getRuleLocationList() == null) {
                    ruleTemplateRespVO.setRuleLocationList(new ArrayList<>());
                }

                for (RuleTemplateLocationDO list : lists){

                    RuleTemplateLocationVO locationVO = new RuleTemplateLocationVO();

                    locationVO.setLatitude(list.getLatitude());
                    locationVO.setLongitude(list.getLongitude());
                    locationVO.setRange(list.getRange());
                    locationVO.setLocationName(list.getLocationName());

                    ruleTemplateRespVO.getRuleLocationList().add(locationVO);
                }


                return ruleTemplateRespVO;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return listtemp;



    }

    @Override
    public List<RuleTemplateDO> getRuleTemplateList(Collection<Integer> ids) {
        return ruleTemplateMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<RuleTemplateDO> getRuleTemplatePage(RuleTemplatePageReqVO pageReqVO) {
        return ruleTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public List<RuleTemplateDO> getRuleTemplateList(RuleTemplateExportReqVO exportReqVO) {
        return ruleTemplateMapper.selectList(exportReqVO);
    }

    /**
     * 获得考勤规则模版列表
     *
     * @param listVO
     * @return 考勤规则模版
     */
    @Override
    public List<RuleTemplateVO> getRuleTemplateListByType(RuleTemplateListVO listVO) {

        List<RuleTemplateVO> list = ruleTemplateMapper.selectListByType(listVO.getRuleType());

        return list;
    }


    /**
     *  判断校区中的默认规则时候只有一个
     *  @param id
     *  @param campusId
     *  @param ruleType
     */
    public void campusDefaultRules(Long id, Integer campusId, Integer ruleType){
        //在数据库中检索
        RuleTemplateDO ruleTemplateDO = ruleTemplateMapper.getCampusDefaultRules(campusId, ruleType);

        if(ruleTemplateDO == null){
            return ;
        }

        if(id == null){

            //到课考勤
            if(ruleType == 0){
                throw exception(RULE_TEMPLATE_DEFAULT_ATTENDANCE_CHECK_EXISTS);
            }

            //就餐考勤
            if(ruleType == 1){
                throw exception(RULE_TEMPLATE_DEFAULT_MEAL_ATTENDANCE_EXISTS);
            }

            //住宿考勤
            if(ruleType == 2){
                throw exception(RULE_TEMPLATE_DEFAULT_CHECK_IN_EXISTS);
            }
        }


        if(!ruleTemplateDO.getId().equals(id)){

            //到课考勤
            if(ruleType == 0){
                throw exception(RULE_TEMPLATE_DEFAULT_ATTENDANCE_CHECK_EXISTS);
            }

            //就餐考勤
            if(ruleType == 1){
                throw exception(RULE_TEMPLATE_DEFAULT_MEAL_ATTENDANCE_EXISTS);
            }

            //住宿考勤
            if(ruleType == 2){
                throw exception(RULE_TEMPLATE_DEFAULT_CHECK_IN_EXISTS);
            }
        }
    }

    /**
     * 唯一性校验
     * 规则名称
     * 同一个类型的规则唯一
     * 到课、就餐、住宿
     *  @param id
     *  @param ruleType
     *  @param name
     */
    public void campusNameRules(Long id, Integer ruleType, String name){

        //在数据库中 匹配数据
        RuleTemplateDO ruleTemplateDO = ruleTemplateMapper.selectByNameAndType(name, ruleType);

        if(ruleTemplateDO == null){
            return ;
        }

        if(id == null){
            //到课考勤
            if(ruleType == 0){
                throw exception(RULE_TEMPLATE_ATTENDANCE_NAME_CHECK_EXISTS);
            }

            //就餐考勤
            if(ruleType == 1){
                throw exception(RULE_TEMPLATE_MEAL_ATTENDANCE_NAME_EXISTS);
            }

            //住宿考勤
            if(ruleType == 2){
                throw exception(RULE_TEMPLATE_CHECK_IN_NAME_EXISTS);
            }
        }

        if(!ruleTemplateDO.getId().equals(id)){
            //到课考勤
            if(ruleType == 0){
                throw exception(RULE_TEMPLATE_ATTENDANCE_NAME_CHECK_EXISTS);
            }

            //就餐考勤
            if(ruleType == 1){
                throw exception(RULE_TEMPLATE_MEAL_ATTENDANCE_NAME_EXISTS);
            }

            //住宿考勤
            if(ruleType == 2){
                throw exception(RULE_TEMPLATE_CHECK_IN_NAME_EXISTS);
            }
        }
    }
}
