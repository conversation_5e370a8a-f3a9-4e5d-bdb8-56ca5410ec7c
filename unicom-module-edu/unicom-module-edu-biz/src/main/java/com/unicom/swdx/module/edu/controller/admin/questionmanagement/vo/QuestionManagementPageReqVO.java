package com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import java.math.BigDecimal;

import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 题目管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionManagementPageReqVO extends PageParam {

    @Schema(description = "题干")
    private String stem;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "题目类型(字典)", example = "1")
    private String questionType;

    @Schema(description = "分数")
    private BigDecimal score;

    @Schema(description = "所属类别", example = "10861")
    private Long categoryId;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "一票否决说明题1是0不是")
    private String oneBallotVetoResult;

    @Schema(description = "是否被添加逻辑(0没有 ，1 有 )")
    private Short isLogic;

    @ApiModelProperty(value = "排序字段(默认按创建时间) 0-按id 1-按题目类型")
    @Range(min = 0, max = 1, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

}