package com.unicom.swdx.module.edu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 离校报备 - 状态枚举
 */
@Getter
@AllArgsConstructor
public enum NotificationStatusEnum {

    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),

    /**
     * 已结束
     */
    COMPLETED(2, "已结束");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名
     */
    private final String name;

}