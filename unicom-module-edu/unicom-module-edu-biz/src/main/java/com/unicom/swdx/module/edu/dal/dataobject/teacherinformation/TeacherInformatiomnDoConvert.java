package com.unicom.swdx.module.edu.dal.dataobject.teacherinformation;

import cn.hutool.core.util.ObjectUtil;
import com.unicom.swdx.module.system.api.user.dto.UserInputDTO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class TeacherInformatiomnDoConvert {

    public static UserInputDTO toUserInputDTO(TeacherInformationDO teacherInfo) {
        if (teacherInfo == null) {
            return null;
        }

        UserInputDTO userInputDTO = new UserInputDTO();
        userInputDTO.setNickname(teacherInfo.getName()); // 设置昵称为老师姓名
        userInputDTO.setMobile(teacherInfo.getContactInformation()); // 设置手机号
        userInputDTO.setSystemId(teacherInfo.getSystemId());    // 设置系统ID
        userInputDTO.setTeacherid(teacherInfo.getId());
        userInputDTO.setUserid(teacherInfo.getUserId());
        return userInputDTO;
    }


    // 集合转换
    public static List<UserInputDTO> toUserInputDTOList(List<TeacherInformationDO> teacherInfoList) {
        if (teacherInfoList == null || teacherInfoList.isEmpty()) {
            return Collections.emptyList(); // 返回空集合
        }

        return teacherInfoList.stream()
                .map(TeacherInformatiomnDoConvert::toUserInputDTO) // 使用单个转换方法
                .filter(Objects::nonNull)
                .filter(it-> ObjectUtil.isNotEmpty(it.getSystemId())) //去掉systemid为null的数据
                .collect(Collectors.toList());
    }
}
