package com.unicom.swdx.module.edu.service.todoitems;

import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsReqVO;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveCreateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.todoitems.TodoItemsDO;

import java.util.List;

/**
 * 班主任待办事项 Service 接口
 *
 * <AUTHOR>
 */
public interface TodoItemsService {

    /**
     * 获得班主任待办事项列表
     *
     * @param reqVO 请求参数
     * @return 班主任待办事项列表
     */
    List<TodoItemsRespVO> getTeacherTodoItemsList(TodoItemsReqVO reqVO);

    /**
     * 获得班主任首页待办事项列表
     *
     * @return 班主任首页待办事项列表
     */
    List<TodoItemsRespVO> getTeacherTodoItemsHomeList(Long classId);

    /**
     * 刷新某个班级学员确认报道数据 - 计算和更新学员报名确认待办事项
     *
     * @param classId 班级ID
     */
    void refreshEnrollmentConfirmationByClassId(Long classId);

    /**
     * 刷新某个班级学员确认报道数据 - 计算和更新学员报名确认待办事项
     *
     * @param classManagementDO 班级信息
     */
    void refreshEnrollmentConfirmationByClass(ClassManagementDO classManagementDO);

    /**
     * 增加学员请假待办事项
     *
     * @param reqVO 请假信息
     */
    void addTraineeLeaveTodoItem(TraineeLeaveCreateReqVO reqVO);

    /**
     * 更新请假待办事项状态
     *
     * @param leaveId 请假id
     */
    void updateTraineeLeaveTodoItem(Long leaveId, Integer leaveStatus);

    void removeTodoItemsByLeaveIds(List<Long> leaveIds);
}
