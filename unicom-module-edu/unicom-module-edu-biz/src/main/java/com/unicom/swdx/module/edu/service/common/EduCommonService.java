package com.unicom.swdx.module.edu.service.common;

import java.util.List;
import java.util.Map;

/**
 * 公共 Service 接口
 *
 * <AUTHOR>
 */
public interface EduCommonService {

    /**
     * 初始化校区数据缓存
     */
    void initCampusDataCache();

    /**
     * 根据租户ID和校区ID获取校区名称
     *
     * @param tenantId 租户ID
     * @param campusId 校区ID
     * @return 校区名称，如果不存在则返回null
     */
    String getCampusName(Long tenantId, Long campusId);

    // 根据校区名称列表获取校区ID列表
    List<Long> getCampusIdsByTenantIdAndCampusName(Long tenantId, List<String> campusNames);

    /**
     * 根据校区ID获取校区名称
     *
     * @param campusId 校区ID
     * @return 校区名称，如果不存在则返回null
     */
    String getCampusNameById(Long campusId);

    /**
     * 根据校区ID获取校区名称
     *
     * @param campusIds 校区ID
     * @return 校区名称，如果不存在则返回null
     */
    String getCampusNamesByIds(List<Long> campusIds);

    /**
     * 根据租户ID和校区ID获取校区名称
     *
     * @param tenantId 租户ID
     * @return 校区名称，如果不存在则返回null
     */
    Map<Long, String> getCampusDataByTenantId(Long tenantId);

    Map<Long, String> getCampusMapByCampusIds(List<Long> campusIdList);
}
