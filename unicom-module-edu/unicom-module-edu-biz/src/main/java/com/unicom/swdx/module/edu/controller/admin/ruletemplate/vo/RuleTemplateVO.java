package com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.LocalTime;

/**
 * 考勤规则模版 DO
 *
 * <AUTHOR>
 */
@Data
public class RuleTemplateVO{

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "0-到课考勤，1-就餐考勤，2-住宿考勤", required = true)
    private Integer ruleType;

    @ApiModelProperty(value = "考勤地点，预留")
    private Integer locations;

    @ApiModelProperty(value = "上课前打卡时间")
    private Integer beforeClassTime;

    @ApiModelProperty(value = "上课后打卡时间")
    private Integer afterClassTime;

    @ApiModelProperty(value = "早餐打卡开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime breakfastStartTime;

    @ApiModelProperty(value = "早餐打卡结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime breakfastEndTime;

    @ApiModelProperty(value = "午餐打卡开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime lunchStartTime;

    @ApiModelProperty(value = "午餐打卡结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime lunchEndTime;

    @ApiModelProperty(value = "晚餐打卡开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime dinnerStartTime;

    @ApiModelProperty(value = "晚餐打卡结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime dinnerEndTime;

    @ApiModelProperty(value = "住宿打卡开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime putUpStartTime;

    @ApiModelProperty(value = "住宿打卡结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime putUpEndTime;

    @ApiModelProperty(value = "状态，0-启用，1-禁用")
    private Integer status;

    @ApiModelProperty(value = "校区，字典值")
    private Integer campus;

    @ApiModelProperty(value = "默认规则，0-是，1-否")
    private Integer defaultRule;

}
