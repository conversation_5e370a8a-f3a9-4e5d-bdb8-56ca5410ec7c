package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TeacherEvaluationResponseVO {
    /**
     * 评估结果主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 评估问卷id
     */
    private Long questionnaireId;
    /**
     * 评卷人id
     */
    private Long studentId;
    /**
     * 发卷人id
     */
    private String issuer;
    /**
     * 发卷人部门id
     */
    private Long deptId;
    /**
     * 班级教师id
     */
    private String teacherId;
    /**
     * 课程id
     */
    private Long classCourseId;
    /**
     * 总分
     */
    private BigDecimal score;
    /**
     * 评分等级
     */
    private String grade;
    /**
     * 是否评卷 （0 否， 1是）
     */
    private Boolean handle;
    /**
     * 免评价类型（0 必须评价 ，1 请假了不用评）
     */
    private Boolean remarktype;

    /**
     * 问卷过期时间
     */
    private LocalDateTime expireTime;

    private Integer rated;

    private Integer unrated;

    @ApiModelProperty(value = "课程名称", example = "课程")
    private String courseName;

    @ApiModelProperty(value = "上课日期", example = "2021-01-01")
    private String classTime;

    @ApiModelProperty(value = "授课教师名称", example = "教师")
    private String teacherName;

    @ApiModelProperty(value = "是否已完成", example = "教师")
    private Boolean isDone;

    @ApiModelProperty(value = "是否部门评课", example = "教师")
    private Boolean department;

    private ClassCourseDO classCourseDO;


    private List<TraineeDO> ratedStudent;

    private List<TraineeDO> unratedStudent;
}
