package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 查询一个班级的考勤三率请求参数
 * @date 2024-11-08
 */
@ApiModel("业中首页-仪表盘-考勤三率 Request VO")
@Data
public class AttendanceRateForBusinessCenterReqVO {
    @ApiModelProperty(value = "年度", example = "1")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期 不填全部", example = "1")
    private Integer classTerm;

    @ApiModelProperty(value = "开始时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "班级id", example = "1")
    private Long classId;

    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;
}
