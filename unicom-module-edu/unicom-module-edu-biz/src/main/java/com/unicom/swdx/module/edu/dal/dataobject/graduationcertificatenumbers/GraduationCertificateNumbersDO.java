package com.unicom.swdx.module.edu.dal.dataobject.graduationcertificatenumbers;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 学员结业证书编号 DO
 *
 * <AUTHOR>
 */
@TableName("edu_graduation_certificate_numbers")
@KeySequence("edu_graduation_certificate_numbers_seq")
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GraduationCertificateNumbersDO extends TenantBaseDO {

    /**
     * 评估结果主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 学员id
     */
    private Long traineeId;
    /**
     * 证书编码
     */
    private String certificateNumber;
    /**
     * 部门id
     */
    private Long deptId;
}