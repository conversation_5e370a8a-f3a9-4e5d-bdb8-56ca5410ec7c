package com.unicom.swdx.module.edu.dal.dataobject.questionlogic;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 问题逻辑 DO
 *
 * <AUTHOR>
 */
@TableName("pg_question_logic")
@KeySequence("pg_question_logic_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionLogicDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 逻辑问题id
     */
    private Long questionId;
    /**
     * 关联问题id
     */
    private Long logicQuestionId;
    /**
     * 对应问卷id
     */
    private Long questionnaireId;
    /**
     * 打分题的分数
     */
    private Integer score;
    /**
     * 选择题的选项
     */
    private Long option;

}