package com.unicom.swdx.module.edu.dal.mysql.traineeleave;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveProcessDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TraineeLeaveProcessMapper extends BaseMapperX<TraineeLeaveProcessDO> {

    List<TraineeLeaveRespVO> selectTeacherList(@Param("userId") Long userId, @Param("classId") Long classId, @Param("status") Integer status);

}
