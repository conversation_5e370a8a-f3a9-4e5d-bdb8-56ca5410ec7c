package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - TraineeInfoPageReqVO Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraineeInfoPageReqVO extends PageParam {

    @ApiModelProperty(value = "班次id")
    private Long classId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty("学员姓名或手机号")
    private String nameOrPhone;


}
