package com.unicom.swdx.module.edu.convert.classcommittee;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExcelVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcommittee.ClassCommitteeDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * EduClassManagement Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCommitteeConvert {

    ClassCommitteeConvert INSTANCE = Mappers.getMapper(ClassCommitteeConvert.class);

    List<ClassCommitteeRespVO> covertList(List<ClassCommitteeDO> list);
}
