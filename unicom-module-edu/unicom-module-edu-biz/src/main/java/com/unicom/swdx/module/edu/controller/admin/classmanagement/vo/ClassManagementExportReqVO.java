package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel(value = "管理后台 - EduClassManagement Excel 导出 Request VO", description = "参数和 ClassManagementPageReqVO 是一致的")
@Data
public class ClassManagementExportReqVO {

    @ApiModelProperty(value = "班次编码")
    private String classNameCode;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "办班类型")
    private Integer classTypeDictId;

    @ApiModelProperty(value = "班级属性")
    private String classAttribute;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "1上学期，2下学期")
    private Integer semester;

    @ApiModelProperty(value = "学制")
    private Integer learningSystem;

    @ApiModelProperty(value = "培训对象")
    private String trainingObject;

    @ApiModelProperty(value = "预计人数")
    private Integer peopleNumber;

    @ApiModelProperty(value = "轮次")
    private String turn;

    @ApiModelProperty(value = "校区")
    private String campus;

    @ApiModelProperty(value = "报道时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime reportingTime;

    @ApiModelProperty(value = "开班时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime classOpenTime;

    @ApiModelProperty(value = "结业时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime completionTime;

    @ApiModelProperty(value = "报名开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime registrationStartTime;

    @ApiModelProperty(value = "缴费报道，1-是，2-否")
    private Integer paymentReport;

    @ApiModelProperty(value = "考勤评课，1-是，2-否")
    private Integer evaluate;

    @ApiModelProperty(value = "附件")
    private String accessory;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "1-发布中，2-待发布")
    private Integer publish;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "报名结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime registrationEndTime;

    @ApiModelProperty(value = "班主任")
    private Long classTeacherLead;

    @ApiModelProperty(value = "辅导老师")
    private String coachTeacher;

}
