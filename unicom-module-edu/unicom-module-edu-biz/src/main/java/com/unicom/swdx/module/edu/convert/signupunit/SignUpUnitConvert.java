package com.unicom.swdx.module.edu.convert.signupunit;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.signupunit.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * EduSignUpUnit Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SignUpUnitConvert {

    SignUpUnitConvert INSTANCE = Mappers.getMapper(SignUpUnitConvert.class);

    SignUpUnitDO convert(SignUpUnitCreateReqVO bean);

    SignUpUnitDO convert(SignUpUnitUpdateReqVO bean);

    SignUpUnitDO convert(ProfileUnitUpdateReqVO bean);

    SignUpUnitRespVO convert(SignUpUnitDO bean);

    List<SignUpUnitRespVO> convertList(List<SignUpUnitDO> list);

    PageResult<SignUpUnitRespVO> convertPage(PageResult<SignUpUnitDO> page);

    List<SignUpUnitDO> convertImportList(List<SignUnitImportTemplateExcelVO> list);

    List<SignUnitExcelVO> convertExportList(List<SignUpUnitDO> unitList);

    default List<SignUpUnitDO> convertList1(List<SignUnitAssignCapacityReqVO.AssignCapacityVO> addList) {
        if ( addList == null ) {
            return null;
        }

        List<SignUpUnitDO> list = new ArrayList<SignUpUnitDO>( addList.size() );
        for ( SignUnitAssignCapacityReqVO.AssignCapacityVO assignCapacityVO : addList ) {

            if ( assignCapacityVO == null ) {
                return null;
            }

            SignUpUnitDO signUpUnitDO = new SignUpUnitDO();

            signUpUnitDO.setParentId( Long.valueOf(assignCapacityVO.getId()) );
            signUpUnitDO.setUnitName( assignCapacityVO.getUnitName() );
            signUpUnitDO.setUnitClassification( assignCapacityVO.getUnitClassification() );
            signUpUnitDO.setCapacity( assignCapacityVO.getCapacity() );
            signUpUnitDO.setIsRestrict( assignCapacityVO.getIsRestrict() );
            signUpUnitDO.setTemplate( assignCapacityVO.getTemplate() );

            signUpUnitDO.setPhone( assignCapacityVO.getPhone() );
            signUpUnitDO.setUnitChargePeople( assignCapacityVO.getUnitChargePeople() );

            list.add( signUpUnitDO );
        }
        return list;
    }

    List<SignUpUnitDO> convertList2(List<SignUnitAssignCapacityReqVO.AssignCapacityVO> updateList);

}
