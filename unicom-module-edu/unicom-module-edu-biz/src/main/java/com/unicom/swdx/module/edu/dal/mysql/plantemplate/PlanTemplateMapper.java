package com.unicom.swdx.module.edu.dal.mysql.plantemplate;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplate.PlanTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.plantemplate.vo.*;

/**
 * 教学计划模版 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanTemplateMapper extends BaseMapperX<PlanTemplateDO> {

    default PageResult<PlanTemplateDO> selectPage(PlanTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlanTemplateDO>()
                .likeIfPresent(PlanTemplateDO::getName, reqVO.getName())
                .betweenIfPresent(PlanTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlanTemplateDO::getId));
    }

    default List<PlanTemplateDO> selectList(PlanTemplateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PlanTemplateDO>()
                .likeIfPresent(PlanTemplateDO::getName, reqVO.getName())
                .betweenIfPresent(PlanTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlanTemplateDO::getId));
    }

}
