package com.unicom.swdx.module.edu.dal.dataobject.classcourseorder;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.edu.enums.classcourse.CourseChangeTypeEnum;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 班级课程安排 DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_course_order")
@KeySequence("edu_class_course_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCourseOrderDO extends TenantBaseDO {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 班次顺序
     */
    private String classIdOrder;
}
