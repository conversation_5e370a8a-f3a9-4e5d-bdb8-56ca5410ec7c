package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 评估项VO")
@Data
@ToString(callSuper = true)
public class QuestionAnswerSubmitVO {

    private Long optionId;

    private Integer score;

    private String content;

    private Long questionId;

    private Long questionnaireId;

    private Long classCourseId;
}
