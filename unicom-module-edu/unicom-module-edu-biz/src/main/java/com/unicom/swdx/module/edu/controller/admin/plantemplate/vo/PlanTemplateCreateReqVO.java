package com.unicom.swdx.module.edu.controller.admin.plantemplate.vo;

import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.PlanConfigBaseVO;
import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.PlanTemplateConfigBaseVO;
import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 教学计划模版创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanTemplateCreateReqVO extends PlanTemplateBaseVO {



}
