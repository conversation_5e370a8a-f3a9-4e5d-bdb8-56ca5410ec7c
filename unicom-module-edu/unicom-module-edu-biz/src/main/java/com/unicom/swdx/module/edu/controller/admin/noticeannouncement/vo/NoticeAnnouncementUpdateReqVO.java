package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - EduNoticeAnnouncement更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NoticeAnnouncementUpdateReqVO extends NoticeAnnouncementBaseVO {

    @ApiModelProperty(value = "主键，自增", required = true)
    @NotNull(message = "主键，自增不能为空")
    private Integer id;

}
