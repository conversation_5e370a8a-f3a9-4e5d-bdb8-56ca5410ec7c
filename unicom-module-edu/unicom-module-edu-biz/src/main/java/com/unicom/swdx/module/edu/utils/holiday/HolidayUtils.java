package com.unicom.swdx.module.edu.utils.holiday;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.unicom.swdx.module.edu.dal.dataobject.courses.HolidayDO;
import com.unicom.swdx.module.edu.dal.mysql.courses.HolidayMapper;

import java.text.SimpleDateFormat;
import java.util.*;
/**
 * @ClassName: HolidayUtils
 * @Author: ZHK
 * @Date: 2024/10/28 14:11
 * @Description: TODO
 * Source: https://blog.csdn.net/qq_36978700/article/details/128478681
 */
public class HolidayUtils {
    /**
     * java获取国家法定节假日和周末
     * @param year /
     * @param month /
     * @return /
     */
    public static Set<String> JJR(int year, int month  , HolidayMapper holidayMapper) {

//        //获取所有的周末
//        Set<String> monthWekDay = getMonthWekDay(year, month);
        //http://timor.tech/api/holiday api文档地址
        //        Integer code = jjr.getInteger("code");
//        if (code != 0) {
//            return monthWekDay;
//        }

        JSONObject jjr = getJjr(year, month ,holidayMapper);
        //new TreeSet<>()自动排序
        Set<String> monthWekDay = new TreeSet<>();
        Map<String, Map<String, Object>> holiday = (Map<String, Map<String, Object>>) jjr.get("holiday");
        Set<String> strings = holiday.keySet();
        for (String str : strings) {
            Map<String, Object> stringObjectMap = holiday.get(str);
            Integer wage = (Integer) stringObjectMap.get("wage");
            String date = (String) stringObjectMap.get("date");
            //筛选掉补班
            if (wage.equals(1)) {
                monthWekDay.remove(date);
            } else {
                monthWekDay.add(date);
            }
        }
        return monthWekDay;
    }

    /**
     * 获取节假日不含周末
     * @param year /
     * @param month /
     * @return /
     */
    private static JSONObject getJjr(int year, int month , HolidayMapper holidayMapper) {

        HolidayDO holidayDO =holidayMapper.selectOne("year" , year);

        String result = null;

        if(holidayDO==null){
            //todo 存在访问超时问题，直接用浏览器打开“http://timor.tech/api/holiday/year/2024?type=Y&week=Y”响应很快
            String url = "http://timor.tech/api/holiday/year/"+year+"?type=Y&week=Y";
            result = HttpUtil.get(url,10000);
            holidayMapper.insert(new HolidayDO(year, result));
        }else{
            result = holidayDO.getHoliday();
        }

        JSONObject jsonObject = JSONObject.parseObject(result);
        return jsonObject;

    }

    /**
     * 获取周末  月从0开始
     * @param year /
     * @param month /
     * @return /
     */
    public static Set<String> getMonthWekDay(int year, int month) {
        month = month - 1 ;
        Set<String> dateList = new HashSet<>();
        SimpleDateFormat simdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = new GregorianCalendar(year, month, 1);
        Calendar endCalendar = new GregorianCalendar(year, month, 1);
        endCalendar.add(Calendar.MONTH, 1);
        while (true) {
            int weekday = calendar.get(Calendar.DAY_OF_WEEK);
            if (weekday == 1 || weekday == 7) {
                dateList.add(simdf.format(calendar.getTime()));
            }
            calendar.add(Calendar.DATE, 1);
            if (calendar.getTimeInMillis() >= endCalendar.getTimeInMillis()) {
                break;
            }
        }
        return dateList;
    }
}
