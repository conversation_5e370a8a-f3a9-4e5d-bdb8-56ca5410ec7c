package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("班主任移动端 - 课评查看 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CourseEvaluationResponseVO {

    @ApiModelProperty(value = "排课id", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "问卷id", example = "1")
    private Long questionnaireId;

    @ApiModelProperty(value = "课程名称", example = "课程")
    private String courseName;

    @ApiModelProperty(value = "上课日期", example = "2021-01-01")
    private String classDate;

    @ApiModelProperty(value = "上课开始时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classBeginTime;

    @ApiModelProperty(value = "授课教师名称", example = "授课教师")
    private String teacherName;

    @ApiModelProperty(value = "待评分人数", example = "1")
    private Long expectedCount;

    @ApiModelProperty(value = "已评分人数", example = "1")
    private Long evaluatedCount;

    @ApiModelProperty(value = "未评分人数", example = "1")
    private Long unevaluatedCount;

    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课)", example = "1")
    private Integer coursesType;

    @ApiModelProperty(value = "选修课课评详情")
    private List<OptionalCourseEvaluationDetailVO> optionalCourseEvaluationDetailVOS;
}
