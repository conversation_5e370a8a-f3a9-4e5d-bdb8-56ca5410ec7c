package com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * EduNoticeAnnouncement DO
 *
 * <AUTHOR>
 */
@TableName("edu_notice_file_url")
@KeySequence("edu_notice_file_url_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeAnnouncementUrlDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 通知公告主键id
     */
    private Integer noticeId;
    /**
     * url
     */
    private String url;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件内容
     */
    private String content;
    /**
     * 发布人
     */
    private String publisher;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 标题
     */
    private String title;
    /**
     * 置顶
     */
    private Integer isTop;
    /**
     * 1-发布，2草稿箱
     */
    private Integer isPublish;
    /**
     * 1-上架， 2-下架
     */
    private Integer status;
    /**
     * 存草稿箱时间
     */
    private LocalDateTime draftsTime;
    /**
     * 置顶时间
     */
    private LocalDateTime topTime;
    /**
     * 班级范围
     */
    private String classIds;
}
