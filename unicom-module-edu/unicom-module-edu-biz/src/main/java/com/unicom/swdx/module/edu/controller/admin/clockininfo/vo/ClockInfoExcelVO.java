package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * EduClassManagement Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ClockInfoExcelVO {

    @ApiModelProperty(value = "日期序号 0")
    @ExcelProperty("日期")
    private String courseDate;
    /**
     * 到课类型，0-上午，1-下午，2晚上，就餐类型，0-早餐，1-中餐，2-晚餐
     */
    @ApiModelProperty(value = "类型序号 1")
    @ExcelProperty("类型")
    private String type;

    @ApiModelProperty(value = "课程名称 序号 2")
    @ExcelProperty("课程名称")
    private String courseName;

    @ApiModelProperty(value = "应到人数 序号 3")
    @ExcelProperty("应到人数")
    private Long traineeCount;

    @ApiModelProperty(value = "实到人数 序号 4")
    @ExcelProperty("实到人数")
    private Long attendanceCount;

    @ApiModelProperty(value = "迟到人数 序号 5")
    @ExcelProperty("迟到人数")
    private Long latecomersCount;

    @ApiModelProperty(value = "请假人数 序号 6")
    @ExcelProperty("请假人数")
    private Long vacateCount;

    @ApiModelProperty(value = "未到人数 序号 7")
    @ExcelProperty("未到人数")
    private Long noShowCount;

    @ApiModelProperty(value = "到课比率 序号 8")
    @ExcelProperty("到课率(%)")
    private String courseRatio;

    @ApiModelProperty(value = "就餐比率 序号 9")
    @ExcelProperty("就餐率(%)")
    private String mealRatio;

    @ApiModelProperty(value = "住宿比率 序号 10")
    @ExcelProperty("住宿率(%)")
    private String checkInRatio;
}
