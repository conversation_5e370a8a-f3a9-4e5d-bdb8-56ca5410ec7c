package com.unicom.swdx.module.edu.service.teachercourseinformation;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationSimpleRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 师资-任课信息中间 Service 接口
 *
 * <AUTHOR>
 */
public interface TeacherCourseInformationService {

    /**
     * 创建师资-任课信息中间
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTeacherCourseInformation(@Valid TeacherCourseInformationCreateReqVO createReqVO);

    /**
     * 更新师资-任课信息中间
     *
     * @param updateReqVO 更新信息
     */
    void updateTeacherCourseInformation(@Valid TeacherCourseInformationUpdateReqVO updateReqVO);

    /**
     * 删除师资-任课信息中间
     *
     * @param id 编号
     */
    void deleteTeacherCourseInformation(Long id);

    /**
     * 获得师资-任课信息中间
     *
     * @param id 编号
     * @return 师资-任课信息中间
     */
    TeacherCourseInformationDO getTeacherCourseInformation(Long id);

    /**
     * 获得师资-任课信息中间列表
     *
     * @param ids 编号
     * @return 师资-任课信息中间列表
     */
    List<TeacherCourseInformationDO> getTeacherCourseInformationList(Collection<Long> ids);

    /**
     * 获得师资-任课信息中间分页
     *
     * @param pageReqVO 分页查询
     * @return 师资-任课信息中间分页
     */
    PageResult<TeacherCourseInformationDO> getTeacherCourseInformationPage(TeacherCourseInformationPageReqVO pageReqVO);

    /**
     * 获得师资-任课信息中间列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 师资-任课信息中间列表
     */
    List<TeacherCourseInformationDO> getTeacherCourseInformationList(TeacherCourseInformationExportReqVO exportReqVO);

    Long batchCreateTeacherCourseInformation(TeacherCourseInformationBatchCreateReqVO createReqVO);

    /**
     * 根据课程id获取其授课教师下拉框
     * @param courseId 课程id
     * @return 下拉框
     */
    List<TeacherInformationSimpleRespVO> getSimpleListByCourseId(Long courseId);
}
