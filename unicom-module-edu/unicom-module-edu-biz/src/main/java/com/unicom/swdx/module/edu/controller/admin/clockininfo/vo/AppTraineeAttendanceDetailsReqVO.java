package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 班主任移动端-班级考勤-查看考勤项学员考勤情况 Req VO
 * @date 2024-11-12
 */
@ApiModel("班主任移动端-班级考勤-查看考勤项学员考勤情况 Req VO")
@Data
public class AppTraineeAttendanceDetailsReqVO {

    @ApiModelProperty(value = "考勤日期", required = true, example = "2024-10-10")
    @NotNull(message = "考勤日期不能为空")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate date;

    @ApiModelProperty(value = "考勤类型 0-到课 1-就餐 2-住宿", required = true, example = "0")
    @NotNull(message = "考勤类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "班级id", required = true, example = "1")
    @NotNull(message = "班级id不能为空")
    private Long classId;

    @ApiModelProperty(value = "学生状态查询 0-未打卡 1-已打卡 2-迟到 3-请假 null-全部", example = "0")
    private Integer traineeStatus;

    @ApiModelProperty(value = "学生请假状态查询 2-待审批 3-审批中 4-已通过", example = "0")
    private Integer leaveStatus;

    @ApiModelProperty(value = "就餐时间段 0-早餐 1-午餐 2-晚餐", example = "1")
    private Integer mealPeriod;

    @ApiModelProperty(value = "排课表id", example = "1")
    private Long classCourseId;

}
