package com.unicom.swdx.module.edu.dal.dataobject.notificationmessage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

@TableName("edu_notification_message_unit")
@KeySequence("edu_notification_message_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMessageUnitDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 站内信主键id
     */
    private Integer noticeId;
    /**
     * 单位id
     */
    private Integer unitId;
    /**
     * 是否已读，1-未读，2-已读
     */
    private Integer isRead;
    /**
     * 是否展示，1-展示，2-不展示
     */
    private Integer displayStatus;

}
