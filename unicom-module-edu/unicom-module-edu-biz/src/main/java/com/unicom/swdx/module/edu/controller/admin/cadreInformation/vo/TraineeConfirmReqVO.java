package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@ApiModel("管理后台 - TraineeConfirmReqVO Request VO")
@Data
@ToString(callSuper = true)
public class TraineeConfirmReqVO {

    @ApiModelProperty(value = "班次id")
    private Long classId;

    @ApiModelProperty(value = "报名人员id列表")
    private List<Long> ids;

    @ApiModelProperty(value = "单位id")
    private Long unitId;
}
