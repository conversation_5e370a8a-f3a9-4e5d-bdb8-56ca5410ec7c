package com.unicom.swdx.module.edu.convert.plandetail;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.plandetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plandetail.PlanDetailDO;

/**
 * 教学计划详情 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanDetailConvert {

    PlanDetailConvert INSTANCE = Mappers.getMapper(PlanDetailConvert.class);

    PlanDetailDO convert(PlanDetailCreateReqVO bean);

    PlanDetailDO convert(PlanDetailUpdateReqVO bean);

    PlanDetailRespVO convert(PlanDetailDO bean);

    List<PlanDetailRespVO> convertList(List<PlanDetailDO> list);

    PageResult<PlanDetailRespVO> convertPage(PageResult<PlanDetailDO> page);

    List<PlanDetailExcelVO> convertList02(List<PlanDetailDO> list);

}
