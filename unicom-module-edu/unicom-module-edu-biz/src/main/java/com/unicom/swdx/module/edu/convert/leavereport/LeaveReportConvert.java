package com.unicom.swdx.module.edu.convert.leavereport;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportRespVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.StudentLeaveReportRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface LeaveReportConvert {

    LeaveReportConvert INSTANCE = Mappers.getMapper(LeaveReportConvert.class);

    LeaveReportDO convert(LeaveReportCreateReqVO createReqVO);

    List<LeaveReportRespVO> convertList(List<LeaveReportDO> leaveReportList);

    List<StudentLeaveReportRespVO> convertList1(List<LeaveReportDO> leaveReportList);

    PageResult<LeaveReportRespVO> convertPage(PageResult<LeaveReportDO> page);
}
