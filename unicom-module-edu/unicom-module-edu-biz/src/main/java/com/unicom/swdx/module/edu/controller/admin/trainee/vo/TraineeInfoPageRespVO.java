package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: TraineeInfoPageRespVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeInfoPageRespVO {
    @ApiModelProperty(value = "学员")
    private String id;
    @ApiModelProperty(value = "序号")
    private Integer index;
    @ApiModelProperty(value = "学员姓名")
    private String name;
    @ApiModelProperty(value = "学员性别")
    private String sex;
    @ApiModelProperty(value = "学员手机号")
    private String phone;
    @ApiModelProperty(value = "职务")
    private String position;
    @ApiModelProperty(value = "职级")
    private Integer jobLevel;
    @ApiModelProperty(value = "政治面貌")
    private Integer politicalIdentity;
    @ApiModelProperty(value = "学员状态")
    private Integer status;
    @ApiModelProperty("报名时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;
}
