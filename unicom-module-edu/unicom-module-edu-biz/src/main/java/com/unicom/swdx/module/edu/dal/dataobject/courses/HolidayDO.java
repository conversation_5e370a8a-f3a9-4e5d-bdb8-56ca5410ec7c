package com.unicom.swdx.module.edu.dal.dataobject.courses;


import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("edu_holiday")
@KeySequence("edu_holiday_pkey") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HolidayDO {

    private int year;


    private String holiday;

}
