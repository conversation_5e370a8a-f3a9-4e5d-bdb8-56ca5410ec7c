package com.unicom.swdx.module.edu.wx.qywx;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2024/1/23 14:26
 **/
@Data
@ConfigurationProperties(prefix = "wx.qywx")
@RefreshScope
public class QywxProperties {

    private String corpid;

    private String corpsecret;

    private String redirect_uri;

    private String agentid;

}
