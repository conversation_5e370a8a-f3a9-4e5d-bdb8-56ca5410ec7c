package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * @ClassName: CadreInformationDO
 * @Author: lty
 * @Date: 2024/12/19 17:08
 */
@Data
@ToString(callSuper = true)
public class CadreInformationRespVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 学员名称
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 身份证
     */
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    private String cardNo;

    /**
     * 手机号
     */
    private String phone;


    /**
     * 班级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 文化程度
     */
    private Integer educationalLevel;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    /**
     * 种族
     */
    private Integer ethnic;

    /**
     * 职务
     */
    private String position;

    /**
     * 职级
     */
    private Integer jobLevel;

    /**
     * 毕业院校
     */
    private String graduationSchool;

    /**
     * 政治面貌
     */
    private Integer politicalIdentity;


    /**
     * 备注
     */
    private String remark;


    /**
     * 学员照片
     */
    private String photo;


    /**
     * 单位id
     */
    private Long unitId;

    /**
     * 老教务职级
     */
    private String oldjobLevel;

}
