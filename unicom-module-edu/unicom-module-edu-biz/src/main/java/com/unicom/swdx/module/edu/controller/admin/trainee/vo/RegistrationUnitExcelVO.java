package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class RegistrationUnitExcelVO {
    @ExcelProperty(value = "序号")
    private Integer index;

    @ExcelProperty(value = "学员姓名")
    private String name;

    @ExcelProperty(value = "学员性别")
    private String sex;

    @ExcelProperty(value = "学员身份证")
    private String cardNo;

    @ExcelProperty(value = "学员手机号")
    private String phone;

    @ExcelProperty(value = "学员所在单位")
    private String unitName;

    @ExcelProperty(value = "文化程度")
    private String educationalLevel;

    @ExcelProperty(value = "学员职务")
    private String position;

    @ExcelProperty(value = "职级")
    private String jobLevel;

    @ExcelProperty(value = "学员状态")
    private String status;

    @ExcelProperty(value = "政治面貌")
    private String politicalIdentity;


}
