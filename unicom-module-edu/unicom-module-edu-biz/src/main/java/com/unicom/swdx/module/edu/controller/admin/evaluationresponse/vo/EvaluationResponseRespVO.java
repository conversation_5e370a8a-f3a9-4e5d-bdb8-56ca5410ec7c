package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 课程评价记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EvaluationResponseRespVO {

    @Schema(description = "评估结果主键",  example = "15291")
    @ExcelProperty("评估结果主键")
    private Integer id;

    @Schema(description = "评估问卷id", example = "4615")
    @ExcelProperty("评估问卷id")
    private Integer questionnaireId;

    @Schema(description = "评卷人id", example = "27859")
    @ExcelProperty("评卷人id")
    private Integer studentId;

    @Schema(description = "发卷人id")
    @ExcelProperty("发卷人id")
    private String issuer;

    @Schema(description = "发卷人部门id", example = "18040")
    @ExcelProperty("发卷人部门id")
    private Integer deptId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "班级教师id", example = "9441")
    @ExcelProperty("班级教师id")
    private String teacherId;

    @Schema(description = "排课id", example = "23817")
    @ExcelProperty("排课id")
    private Integer classCourseId;

    @Schema(description = "总分")
    @ExcelProperty("总分")
    private BigDecimal score;

    @Schema(description = "评分等级")
    @ExcelProperty("评分等级")
    private String grade;

    @Schema(description = "是否评卷 （0 否， 1是）")
    @ExcelProperty("是否评卷 （0 否， 1是）")
    private Boolean handle;

    @Schema(description = "免评价类型（0 必须评价 ，1 请假了不用评）", example = "2")
    @ExcelProperty("免评价类型（0 必须评价 ，1 请假了不用评）")
    private Boolean remarktype;

    @ApiModelProperty(value = "是否部门评课", example = "教师")
    private Boolean department;
}
