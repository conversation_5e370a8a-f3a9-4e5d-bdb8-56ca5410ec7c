package com.unicom.swdx.module.edu.dal.dataobject.electivereleaseclasses;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 发布信息班级关联 DO
 *
 * <AUTHOR>
 */
@TableName("edu_elective_release_classes")
@KeySequence("edu_elective_release_classes_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElectiveReleaseClassesDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 选修课发布信息ID
     */
    private Long releaseId;
    /**
     * 班级ID
     */
    private Long classId;
    /**
     * 排课表ID
     */
    private Long classCourseId;
    /**
     * 系统内部门
     */
    private Long deptId;

}
