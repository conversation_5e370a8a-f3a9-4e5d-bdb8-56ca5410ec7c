package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class UserAppletDashboardVO {

    @ApiModelProperty(value = "教学总工时")
    private Integer workHourTotal;

    @ApiModelProperty(value = "专题课总工时")
    private Integer topicCourseTime;

    @ApiModelProperty(value = "选修课总工时")
    private Integer optionalCourseTime;

    @ApiModelProperty(value = "均分")
    private BigDecimal avgScore;

    @ApiModelProperty(value = "专题课次数")
    private Integer coreCourseTimes;

    @ApiModelProperty(value = "专题课占比")
    private BigDecimal coreCourseProp;

    @ApiModelProperty(value = "选修课次数")
    private Integer alterCourseTimes;

    @ApiModelProperty(value = "选修课占比")
    private BigDecimal alterCourseProp;

    @ApiModelProperty(value = "教学方式")
    private List<UserAppletTeachingMethodsVO> teachingMethods;

}
