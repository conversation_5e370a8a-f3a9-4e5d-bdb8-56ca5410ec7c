package com.unicom.swdx.module.edu.api.signupunit;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.service.signupunit.SignUpUnitService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;


@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class SignUpUnitApiImpl implements SignUpUnitApi{

    @Resource
    private SignUpUnitService signUpUnitService;

    @Override
    @TenantIgnore
    public CommonResult<Boolean> isUnitAdmin(Long userId) {
        SignUpUnitDO signUpUnitDO = signUpUnitService.getByUserId(userId);
        return CommonResult.success(Objects.nonNull(signUpUnitDO));
    }

    @Override
    @TenantIgnore
    public CommonResult<Long> getUnitIdByUserId(Long userId) {
        SignUpUnitDO signUpUnitDO = signUpUnitService.getByUserId(userId);
        if (Objects.isNull(signUpUnitDO)) {
            return CommonResult.success(null);
        }
        return CommonResult.success(Long.valueOf(signUpUnitDO.getId()));
    }

    @Override
    @TenantIgnore
    public CommonResult<List<String>> getPhoneList() {
        List<String> phoneList = signUpUnitService.getPhoneList();
        if (Objects.isNull(phoneList)) {
            return CommonResult.success(null);
        }
        return CommonResult.success(phoneList);
    }
}
