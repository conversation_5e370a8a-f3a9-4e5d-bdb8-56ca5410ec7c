package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 业中首页-仪表盘 - 课程评估情况 Response VO
 * @date 2024-11-19
 */
@ApiModel("业中首页-仪表盘 - 课程评估情况 Response VO")
@Data
public class ClassEvaluationForBusinessCenterRespVO {

    @ApiModelProperty(value = "评课率", example = "10.1")
    private String evalRate;

    @ApiModelProperty(value = "评课平均分", example = "4.5")
    private String courseAvgScore;

    @ApiModelProperty(value = "部门排名", example = "4")
    private Integer deptRank;

    @ApiModelProperty(value = "排名详情列表数据")
    private List<RankDetailVO> rankDetailVOList;

}
