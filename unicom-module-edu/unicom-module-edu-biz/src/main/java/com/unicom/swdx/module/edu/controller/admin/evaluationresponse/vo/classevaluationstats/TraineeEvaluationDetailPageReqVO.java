package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 班次评估统计 - 学员参评详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraineeEvaluationDetailPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "陈平安")
    private String traineeName;

    @ApiModelProperty(value = "班次ID", example = "1")
    private Long classId;

    @ApiModelProperty(value = "多选id（导出时使用）")
    private List<Long> ids;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "排序字段(默认按创建时间倒序 0-默认按创建时间 1-按学员姓名")
    @Range(min = 0, max = 1, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序排列(默认降序)")
    private Boolean isDesc;

}