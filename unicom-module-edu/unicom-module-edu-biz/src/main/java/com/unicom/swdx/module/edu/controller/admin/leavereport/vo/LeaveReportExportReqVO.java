package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 离校申请导出 Request VO")
@Data
public class LeaveReportExportReqVO {

    @Schema(description = "多选id（导出时使用）")
    private List<Long> ids;

    @Schema(description = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}