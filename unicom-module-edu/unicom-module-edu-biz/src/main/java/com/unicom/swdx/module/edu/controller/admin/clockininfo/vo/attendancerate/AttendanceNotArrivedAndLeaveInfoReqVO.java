package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 查询学生未签、请假详情列表
 * @date 2024-11-08
 */
@ApiModel("查询学生未签、请假详情列表 Request VO")
@Data
public class AttendanceNotArrivedAndLeaveInfoReqVO {

    @ApiModelProperty(value = "开始时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "学员ID", example = "1")
    @NotNull(message = "学员ID不能为空")
    private Long traineeId;

    @ApiModelProperty(value = "类型 0-未到详情 1-请假详情", example = "0")
    @NotNull(message = "查看类型不能为空")
    @Range(min = 0, max = 1, message = "查看类型不存在")
    private Integer type;

}
