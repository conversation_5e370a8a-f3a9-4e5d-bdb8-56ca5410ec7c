package com.unicom.swdx.module.edu.dal.dataobject.leavenotification;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 离校报备学员填报 DO
 */
@TableName("edu_leave_notification_trainee")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveNotificationTraineeDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 离校报备ID
     */
    private Long notificationId;

    /**
     * 学员ID
     */
    private Long traineeId;

    /**
     * 学员姓名
     */
    private String traineeName;

    /**
     * 学员手机号
     */
    private String traineeMobile;

    /**
     * 是否为手机号脱敏状态
     */
    private Boolean mobileMasked;

    /**
     * 填报状态
     *
     * 枚举 {@link com.unicom.swdx.module.edu.enums.LeaveNotificationFillStatusEnum}
     */
    private Integer status;

    /**
     * 填报时间
     */
    private LocalDateTime fillTime;

    /**
     * 离校原因
     */
    private String leaveReason;

    /**
     * 离校地址
     */
    private String leaveAddress;

    /**
     * 交通方式
     */
    private String transportation;

    /**
     * 预计返校时间
     */
    private LocalDateTime expectedReturnTime;

}