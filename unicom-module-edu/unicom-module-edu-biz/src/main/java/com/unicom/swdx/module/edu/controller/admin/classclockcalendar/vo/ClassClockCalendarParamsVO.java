package com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo;

import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.SchoolAccommodationAttendanceUpdateReqVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* 全校就餐住宿考勤 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassClockCalendarParamsVO {

    @ApiModelProperty(value = "VO对象")
    private ClassClockCalendarUpdateReqVO classClockCalendarUpdateReqVO;

    @ApiModelProperty(value = "id列表")
    private List<Integer> ids;
}
