package com.unicom.swdx.module.edu.controller.admin.homepage;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.HomePageDataCardRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeEthnicAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeLevelAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeUnitAnalysisRespVO;
import com.unicom.swdx.module.edu.service.homepage.ExcelExportService;
import com.unicom.swdx.module.edu.service.homepage.HomePageService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 学员首页")
@RestController
@RequestMapping("/edu/trainee/homePage")
@Validated
public class HomePageController {

    @Resource
    private HomePageService homePageService;



    /**
     * 数据卡片
     * @return HomePageDataCardRespVO
     */
    @GetMapping("/dataCard")
    @ApiOperation(value = "数据卡片")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<HomePageDataCardRespVO> getDataCard() {
        return success(homePageService.getDataCard());
    }


    /**
     * 学员职级分析
     * @return TraineeLevelAnalysisRespVO
     */
    @GetMapping("/traineeLevelAnalysis")
    @ApiOperation(value = "学员职级分析")
    public CommonResult<TraineeLevelAnalysisRespVO> traineeLevelAnalysis() {
        return success(homePageService.traineeLevelAnalysis());
    }


    /**
     * 在校学员来源单位类型分布
     * @return TraineeLevelAnalysisRespVO
     */
    @GetMapping("/traineeUnitAnalysis")
    @ApiOperation(value = "在校学员来源单位类型分布")
    public CommonResult<List<TraineeUnitAnalysisRespVO>> traineeUnitAnalysis() {
        return success(homePageService.traineeUnitAnalysis());
    }

    /**
     * 在校学员民族分布
     * @return HomePageDataCardRespVO
     */
    @GetMapping("/traineeEthnicAnalysis")
    @ApiOperation(value = "在校学员民族分布")
    public CommonResult<TraineeLevelAnalysisRespVO> traineeEthnicAnalysis() {
        return success(homePageService.traineeEthnicAnalysis());
    }

    /**
     * 学历分析
     * @return TraineeLevelAnalysisRespVO
     */
    @GetMapping("/traineeEducationAnalysis")
    @ApiOperation(value = "学历分析")
    public CommonResult<List<TraineeUnitAnalysisRespVO>> traineeEducationAnalysis() {
        return success(homePageService.traineeEducationAnalysis());
    }

    /**
     * 在校学员性别分析
     * @return HomePageDataCardRespVO
     */
    @GetMapping("/traineeSexAnalysis")
    @ApiOperation(value = "在校学员性别分析")
    public CommonResult<TraineeLevelAnalysisRespVO> traineeSexAnalysis() {
        return success(homePageService.traineeSexAnalysis());
    }

    /**
     * 年龄分析
     * @return HomePageDataCardRespVO
     */
    @GetMapping("/traineeAgeAnalysis")
    @ApiOperation(value = "年龄分析")
    public CommonResult<List<TraineeUnitAnalysisRespVO>> traineeAgeAnalysis() {
        return success(homePageService.traineeAgeAnalysis());
    }



}
