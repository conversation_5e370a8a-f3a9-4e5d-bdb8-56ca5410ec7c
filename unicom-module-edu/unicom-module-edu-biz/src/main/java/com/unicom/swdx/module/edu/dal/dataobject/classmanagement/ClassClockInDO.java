package com.unicom.swdx.module.edu.dal.dataobject.classmanagement;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * EduClassManagement DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_clock_in")
@KeySequence("edu_class_clock_in_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassClockInDO extends TenantBaseDO {

    /**
     * 主键id,自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 班次ID
     */
    private Long classId;
    /**
     * 到课考勤id
     */
    private Long attendanceCheck;
    /**
     * 就餐考勤id
     */
    private Long mealAttendance;
    /**
     * 住宿考勤id
     */
    private Long checkIn;

}
