package com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("参训系统 - 站内信 Delete VO")
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class NotificationMessageDeleteVO {

    @ApiModelProperty(value = "批量", required = true)
    private String ids;
}
