package com.unicom.swdx.module.edu.dal.dataobject.completiontemplate;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 结业考核模版设置 DO
 *
 * <AUTHOR>
 */
@TableName("edu_completion_template")
@KeySequence("edu_completion_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompletionTemplateDO extends TenantBaseDO {


    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 二级列名
     */
    private String columnName;
    /**
     * 换算公式
     */
    private String conversionAnnouncement;
    /**
     * 分值上限
     */
    private Integer maxScore;
    /**
     * 初始分
     */
    private Integer initialScore;
    /**
     * 获取方式,0-初始值上修改，1-自动获取，2-固定值
     */
    private Integer acquisitionMode;
    /**
     * 数据来源，0-事假、1-病假、2-五会假、3-到课率、4-就餐率、5-住宿率、6-评课率、7-迟到次数
     */
    private Integer dataSource;
    /**
     * 考核名称
     */
    private String assessmentName;
    /**
     * 所属校区
     */
    private Integer campus;
    /**
     * 默认规则，0-是，1-否
     */
    private Integer defaultRule;
    /**
     * 模版名称
     */
    private String templateName;
    /**
     * 模块名称
     */
    private String moduleName;
    /**
     * 唯一编码
     */
    private String idCode;

    /**
     * 是否为内置模板，0-否，1-是
     */
    private Integer builtinTemplate;

}
