package com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 评估问卷与问题关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionnaireDetailPageReqVO extends PageParam {

    @Schema(description = "问题主键", example = "3908")
    private Long questionId;

    @Schema(description = "试卷主键", example = "31586")
    private Long questionnaireId;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "0不是1是一票否决")
    private Integer oneBallotVeto;

}