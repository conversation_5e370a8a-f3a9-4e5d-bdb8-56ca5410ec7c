package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


@ApiModel("管理后台 - EduClassroomLibrary分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassroomLibraryPageReqVO extends PageParam {

    @ApiModelProperty(value = "教室名称")
    private String className;

//    @ApiModelProperty(value = "校区名称")
//    private String campusName;

    @ApiModelProperty(value = "建筑名称")
    private String buildingName;

    @ApiModelProperty(value = "容纳人数")
    private Integer capacity;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "升序降序，0-升序，1-降序")
    private Integer change;

}
