package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 部门评估-分页查询 Response VO
 * @date 2024-11-19
 */
@ApiModel("部门评估-分页查询 Response VO")
@Data
public class DeptEvaluationPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "所属部门", example = "公共管理部")
    private String deptName;

    @ApiModelProperty(value = "所属部门ID", example = "1001")
    private Long deptId;

    @ApiModelProperty(value = "教师ID", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "教师姓名", example = "教师")
    private String teacherName;

    @ApiModelProperty(value = "授课次数", example = "10")
    private Integer teachingCount;

    @ApiModelProperty(value = "班次次数", example = "5")
    private Integer classCount;

    @ApiModelProperty(value = "平均分(两位小数)", example = "90.00")
    private String averageScoreStr;

    @ApiModelProperty(value = "平均排名分(两位小数)", example = "82.00")
    private String averageRankScoreStr;

    @ApiModelProperty(value = "平均排名分", example = "82.001", hidden = true)
    private Double averageRankScore;

    @ApiModelProperty(value = "平均分", example = "90.452", hidden = true)
    private Float averageScore;
}
