package com.unicom.swdx.module.edu.convert.noticeannouncement;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementRespVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementUpdateReqVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementUrlVO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * EduNoticeAnnouncement Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface NoticeAnnouncementConvert {

    NoticeAnnouncementConvert INSTANCE = Mappers.getMapper(NoticeAnnouncementConvert.class);

    NoticeAnnouncementDO convert(NoticeAnnouncementCreateReqVO bean);

    NoticeAnnouncementDO convert(NoticeAnnouncementUpdateReqVO bean);

    List<NoticeAnnouncementUrlVO> doConvertUrlVO(List<NoticeAnnouncementUrlDO> bean);

    NoticeAnnouncementRespVO convert(NoticeAnnouncementDO bean);

    List<NoticeAnnouncementRespVO> convertList(List<NoticeAnnouncementDO> list);

    PageResult<NoticeAnnouncementRespVO> convertPage(PageResult<NoticeAnnouncementDO> page);

}
