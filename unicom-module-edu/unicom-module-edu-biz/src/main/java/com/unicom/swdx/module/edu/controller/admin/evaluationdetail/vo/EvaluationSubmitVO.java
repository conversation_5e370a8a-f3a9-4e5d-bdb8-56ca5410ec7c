package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 评估问卷提交VO")
@Data
@ToString(callSuper = true)
public class EvaluationSubmitVO {

    private Long questionnaireId;

    private Long classCourseId;

    @Schema(description = "学员ID")
    private Long studentId;

    List<QuestionAnswerSubmitVO> answers;

    private Integer score;

    private Integer grade;

    @Schema(description = "最低分理由说明")
    private String lowScoreReason;

    @Schema(description = "问卷提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "问卷撤回截止时间")
    private LocalDateTime revocableTime;
}
