package com.unicom.swdx.module.edu.convert.planconfig;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;

/**
 * 教学计划配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanConfigConvert {

    PlanConfigConvert INSTANCE = Mappers.getMapper(PlanConfigConvert.class);

    PlanConfigDO convert(PlanConfigCreateReqVO bean);

    PlanConfigDO convert(PlanConfigUpdateReqVO bean);

    PlanConfigRespVO convert(PlanConfigDO bean);

    List<PlanConfigRespVO> convertList(List<PlanConfigDO> list);

    PageResult<PlanConfigRespVO> convertPage(PageResult<PlanConfigDO> page);

    List<PlanConfigExcelVO> convertList02(List<PlanConfigDO> list);

    List<PlanConfigDO> convertToDoList(List<PlanConfigBaseVO> planConfigList);

    List<PlanConfigBaseVO> convertToBaseVoList(List<PlanConfigDO> existingConfigList);
}
