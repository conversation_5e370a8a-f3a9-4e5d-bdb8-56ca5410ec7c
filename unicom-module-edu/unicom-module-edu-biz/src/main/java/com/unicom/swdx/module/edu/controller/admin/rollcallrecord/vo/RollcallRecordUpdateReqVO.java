package com.unicom.swdx.module.edu.controller.admin.rollcallrecord.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 学员点名签到记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RollcallRecordUpdateReqVO extends RollcallRecordBaseVO {

    @ApiModelProperty(value = "记录主键", required = true)
    @NotNull(message = "记录主键不能为空")
    private Long id;

}
