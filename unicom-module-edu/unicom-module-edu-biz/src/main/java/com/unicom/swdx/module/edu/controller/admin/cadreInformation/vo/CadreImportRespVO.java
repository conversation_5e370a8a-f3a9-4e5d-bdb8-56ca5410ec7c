package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@ApiModel("管理后台 - 学员导入 Response VO")
@Data
@Builder
public class CadreImportRespVO {

    /**
     * 错误信息
     */
    private List<String> errorMessages;

    /**
     * 导入成功的数据条数
     */
    private Integer count;
    /**
     * 1-全部导入成功，2-部分导出成功，3-全部导入失败
     */
    private Integer tag;


}
