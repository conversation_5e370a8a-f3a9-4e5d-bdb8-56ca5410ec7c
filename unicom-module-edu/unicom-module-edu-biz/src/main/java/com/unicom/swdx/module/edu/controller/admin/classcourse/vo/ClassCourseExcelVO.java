package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

import javax.validation.constraints.NotNull;


@Data
public class ClassCourseExcelVO {

    @ExcelProperty("唯一标识")
    private Long id;

    @ExcelProperty("班级id")
    private Long classId;

    @ExcelProperty("课程id")
    private Long courseId;

    @ExcelProperty("开始时间")
    private LocalDateTime beginTime;

    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @ExcelProperty("教师id")
    private Long teacherId;

    @ExcelProperty("教室id")
    private Long classroomId;

    @ExcelProperty("是否暂存")
    private Boolean isTemporary;

    @ExcelProperty("是否合班授课")
    private Boolean isMerge;

    @ExcelProperty("是否调课")
    private Boolean isChange;

    @ExcelProperty("教学计划id")
    private Long planId;

    @ExcelProperty("日期")
    private String date;

    @ExcelProperty("时间段（0上午，1下午，2晚上）不能为空")
    private String period;

    @ExcelProperty("冲突信息")
    private String conflictInfo;
}
