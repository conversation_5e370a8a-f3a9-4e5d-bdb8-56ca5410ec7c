package com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 结业考核模版设置 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ClassCompletionTemplateExcelVO {

    @ExcelProperty("主键id，自增")
    private Long id;

    @ExcelProperty("序列号")
    private String serialNumber;

    @ExcelProperty("二级列名")
    private String columnName;

    @ExcelProperty("换算公式")
    private String conversionAnnouncement;

    @ExcelProperty("分值上限")
    private Integer maxScore;

    @ExcelProperty("初始分")
    private Integer initialScore;

    @ExcelProperty("获取方式,0-初始值上修改，1-自动获取，2-固定值")
    private Integer acquisitionMode;

    @ExcelProperty("数据来源，0-事假、1-病假、2-五会假、3-到课率、4-就餐率、5-住宿率、6-评课率、7-迟到次数")
    private Integer dataSource;

    @ExcelProperty("考核名称")
    private String assessmentName;

    @ExcelProperty("所属校区")
    private Integer campus;

    @ExcelProperty("默认规则，0-是，1-否")
    private Integer defaultRule;

    @ExcelProperty("模版名称")
    private String templateName;

    @ExcelProperty("创建时间")
    private Date createTime;

    @ExcelProperty("模块名称")
    private String moduleName;

    @ExcelProperty("唯一编码")
    private String idCode;

    @ExcelProperty("班级id")
    private Long classId;

}
