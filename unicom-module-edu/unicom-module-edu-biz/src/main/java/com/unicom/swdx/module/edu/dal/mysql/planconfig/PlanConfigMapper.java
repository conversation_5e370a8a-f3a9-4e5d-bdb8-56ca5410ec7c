package com.unicom.swdx.module.edu.dal.mysql.planconfig;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.*;

/**
 * 教学计划配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanConfigMapper extends BaseMapperX<PlanConfigDO> {

    default PageResult<PlanConfigDO> selectPage(PlanConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlanConfigDO>()
                .eqIfPresent(PlanConfigDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(PlanConfigDO::getDayOfWeek, reqVO.getDayOfWeek())
                .eqIfPresent(PlanConfigDO::getPeriod, reqVO.getPeriod())
                .betweenIfPresent(PlanConfigDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(PlanConfigDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(PlanConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlanConfigDO::getId));
    }

    default List<PlanConfigDO> selectList(PlanConfigExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PlanConfigDO>()
                .eqIfPresent(PlanConfigDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(PlanConfigDO::getDayOfWeek, reqVO.getDayOfWeek())
                .eqIfPresent(PlanConfigDO::getPeriod, reqVO.getPeriod())
                .betweenIfPresent(PlanConfigDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(PlanConfigDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(PlanConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlanConfigDO::getId));
    }

}
