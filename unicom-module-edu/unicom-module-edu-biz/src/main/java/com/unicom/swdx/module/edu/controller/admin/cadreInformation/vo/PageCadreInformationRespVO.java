package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class PageCadreInformationRespVO {

    @ApiModelProperty(value = "干部id")
    private Long id;

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "职级")
    private Integer jobLevel;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "培训次数")
    private Integer traineeCount;

    @ApiModelProperty(value = "政治面貌")
    private Integer politicalIdentity;

    @ApiModelProperty(value = "文化程度")
    private Integer educationalLevel;

    @ApiModelProperty(value = "民族")
    private Integer ethnic;

    @ApiModelProperty(value = "老教务职级")
    private String oldjobLevel;

}
