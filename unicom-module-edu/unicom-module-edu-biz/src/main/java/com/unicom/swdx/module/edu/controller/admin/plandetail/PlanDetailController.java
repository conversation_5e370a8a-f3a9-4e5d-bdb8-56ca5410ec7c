package com.unicom.swdx.module.edu.controller.admin.plandetail;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.plandetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plandetail.PlanDetailDO;
import com.unicom.swdx.module.edu.convert.plandetail.PlanDetailConvert;
import com.unicom.swdx.module.edu.service.plandetail.PlanDetailService;

@Api(tags = "管理后台 - 教学计划详情")
@RestController
@RequestMapping("/edu/plan-detail")
@Validated
public class PlanDetailController {

    @Resource
    private PlanDetailService planDetailService;

    @PostMapping("/create")
    @ApiOperation("创建教学计划详情")
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:create')")
    public CommonResult<Long> createPlanDetail(@Valid @RequestBody PlanDetailCreateReqVO createReqVO) {
        return success(planDetailService.createPlanDetail(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新教学计划详情")
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:update')")
    public CommonResult<Boolean> updatePlanDetail(@Valid @RequestBody PlanDetailUpdateReqVO updateReqVO) {
        planDetailService.updatePlanDetail(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除教学计划详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:delete')")
    public CommonResult<Boolean> deletePlanDetail(@RequestParam("id") Long id) {
        planDetailService.deletePlanDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得教学计划详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:query')")
    public CommonResult<PlanDetailRespVO> getPlanDetail(@RequestParam("id") Long id) {
        PlanDetailDO planDetail = planDetailService.getPlanDetail(id);
        return success(PlanDetailConvert.INSTANCE.convert(planDetail));
    }

    @GetMapping("/list")
    @ApiOperation("获得教学计划详情列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:query')")
    public CommonResult<List<PlanDetailRespVO>> getPlanDetailList(@RequestParam("ids") Collection<Long> ids) {
        List<PlanDetailDO> list = planDetailService.getPlanDetailList(ids);
        return success(PlanDetailConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得教学计划详情分页")
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:query')")
    public CommonResult<PageResult<PlanDetailRespVO>> getPlanDetailPage(@Valid PlanDetailPageReqVO pageVO) {
        PageResult<PlanDetailDO> pageResult = planDetailService.getPlanDetailPage(pageVO);
        return success(PlanDetailConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出教学计划详情 Excel")
    @PreAuthorize("@ss.hasPermission('edu:plan-detail:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanDetailExcel(@Valid PlanDetailExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PlanDetailDO> list = planDetailService.getPlanDetailList(exportReqVO);
        // 导出 Excel
        List<PlanDetailExcelVO> datas = PlanDetailConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "教学计划详情.xls", "数据", PlanDetailExcelVO.class, datas);
    }

}
