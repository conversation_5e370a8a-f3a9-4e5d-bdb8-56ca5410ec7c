package com.unicom.swdx.module.edu.dal.mysql.electiverelease;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseAndClassSelectedInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseAndClassSelectionInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.courseinfo.CourseInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleasePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseSelectedInfoPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseSelectedInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesReqVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 选修课发布信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveReleaseMapper extends BaseMapperX<ElectiveReleaseDO> {

    default PageResult<ElectiveReleaseDO> selectPage(ElectiveReleasePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ElectiveReleaseDO>()
                .likeIfPresent(ElectiveReleaseDO::getName, reqVO.getName())
                .orderByDesc(ElectiveReleaseDO::getId));
    }

    List<ElectiveReleaseDO> selectPageByReqVO(IPage<ElectiveReleaseDO> page, @Param("reqVO") ElectiveReleasePageReqVO pageReqVO);

    /**
     * 获取选修课发布信息列表应选人数信息 - 仅统计已报到学员
     *
     * @param releaseId 只查询指定选修课发布应选人数信息
     * @param classIdList   只查询指定班级应选人数信息
     * @param statusList   只查询指定状态的班级应选人数信息
     * @return 发布-班级 应选人数信息
     */
    List<ReleaseAndClassSelectionInfoDTO> getSelectionNumInfo(@Param("releaseId") Long releaseId,
                                                              @Param("classIdList") List<Long> classIdList,
                                                              @Param("statusList") List<Integer> statusList);

    /**
     * 获取选修课发布信息列表已选人数信息
     *
     * @param releaseId 只查询指定选修课发布已选人数信息
     * @param classIdList   只查询指定班级已选人数信息
     * @return 发布-班级 已选人数信息
     */
    List<ReleaseAndClassSelectedInfoDTO> getSelectedNumInfo(@Param("releaseId") Long releaseId, @Param("classIdList") List<Long> classIdList);

    /**
     * 教务管理-选修课管理-已选人数列表分页查询
     * @param page 分页对象
     * @param reqVO 分页参数
     * @return 分页结果
     */
    List<ElectiveReleaseSelectedInfoRespVO> getSelectedInfoPageByReqVO(IPage<ElectiveReleaseSelectedInfoRespVO> page, @Param("reqVO") ElectiveReleaseSelectedInfoPageReqVO reqVO);

    /**
     * 选修课发布课程关联的课程信息分页查询
     * @param page 分页对象
     * @param reqVO 分页参数
     * @return 课程信息分页数据
     */
    List<ElectiveReleaseCoursesRespVO> getReleaseCoursePage(IPage<ElectiveReleaseCoursesRespVO> page,@Param("reqVO") ElectiveReleaseCoursesReqVO reqVO);

    /**
     * 根据班级ID获得选修课发布信息列表
     *
     * @param classId 班级ID
     * @return 选修课发布信息列表
     */
    List<ElectiveReleaseDO> getElectiveReleaseListByClassId(@Param("classId") Long classId);

    /**
     * 根据学员ID获得待选的选修课发布信息列表
     * @param traineeId 学员ID
     * @param currentDateTime 查询选学时间包裹了当前时间的范围
     * @return 待选的选修课发布信息列表
     */
    List<ElectiveReleaseDO> getUnselectElectiveReleaseAndCoursesListByTraineeId(@Param("traineeId") Long traineeId,
                                                                                @Param("currentDateTime") LocalDateTime currentDateTime);

    /**
     * 获取选修课班级信息列表
     * @param releaseId 选修课发布ID
     * @return 选修课班级信息列表
     */
    List<ClassInfoVO> getSimpleClassesInfoList(Long releaseId);

    /**
     * 获取选修课课程信息列表
     * @param releaseId 选修课发布ID
     * @return 选修课课程信息列表
     */
    List<CourseInfoVO> getSimpleCoursesInfoList(Long releaseId);
}
