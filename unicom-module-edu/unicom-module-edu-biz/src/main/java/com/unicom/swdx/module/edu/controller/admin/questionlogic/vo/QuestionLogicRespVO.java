package com.unicom.swdx.module.edu.controller.admin.questionlogic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 问题逻辑 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionLogicRespVO {

    @Schema(description = "主键", example = "3030")
    @ExcelProperty("主键")
    private Integer id;

    @Schema(description = "逻辑问题id", example = "16879")
    @ExcelProperty("逻辑问题id")
    private Long questionId;

    @Schema(description = "关联问题id", example = "14855")
    @ExcelProperty("关联问题id")
    private Long logicQuestionId;

    /**
     * 对应问卷id
     */
    private Long questionnaireId;
    /**
     * 打分题的分数
     */
    private Integer score;
    /**
     * 选择题的选项
     */
    private Long option;

}
