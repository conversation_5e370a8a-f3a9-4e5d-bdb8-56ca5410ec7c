package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
@Schema(description = "管理后台 - 学员离校报备新增 Request VO")
@Data
public class LeaveReportDetailCreateReqVO {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 离校报备id
     */
    private Long leaveId;
    /**
     * 是否离校
     */
    private Boolean leaveStatus;
    /**
     * 离校时间
     */
    private LocalDateTime leaveTime;
    /**
     * 返校时间
     */
    private LocalDateTime returnTime;
    /**
     * 学员id
     */
    private Long studentId;
    /**
     * 离校日是否校内用餐
     */
    private Boolean leaveDinner;
    /**
     * 返校日是否校内用餐
     */
    private Boolean returnDinner;
    /**
     * 目的地
     */
    private String destination;
    /**
     * 事由
     */
    private String cause;
}
