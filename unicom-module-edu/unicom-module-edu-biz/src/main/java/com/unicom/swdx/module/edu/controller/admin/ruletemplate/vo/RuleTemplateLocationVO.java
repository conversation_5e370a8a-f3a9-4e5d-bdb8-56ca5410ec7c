package com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * EduClassManagement DO
 *
 * <AUTHOR>
 */
@Data
public class RuleTemplateLocationVO{

    /**
     * 地点名称
     */
    @ApiModelProperty(value = "地点名称")
    private String locationName;
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;
    /**
     * 打卡范围
     */
    @ApiModelProperty(value = "打卡范围")
    private Integer range;

}
