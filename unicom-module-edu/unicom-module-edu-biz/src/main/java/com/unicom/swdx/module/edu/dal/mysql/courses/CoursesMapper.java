package com.unicom.swdx.module.edu.dal.mysql.courses;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesRespVO;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordReqVO;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 课程库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CoursesMapper extends BaseMapperX<CoursesDO> {

    List<CoursesRespVO> selectPageByReqVO(IPage<CoursesRespVO> page, @Param("reqVO") CoursesPageReqVO reqVO);

    List<CoursesRespVO> selectListByReqVO(@Param("reqVO") CoursesPageReqVO reqVO);

    List<CoursesRespVO> selectListByIds(@Param("ids") List<Long> ids);

    /**
     * 根据课程名称和课程类型查询课程库 校验重名
     *
     * @param name        课程名称
     * @param coursesType 课程类型
     * @param excludeId   排除的课程库id
     */
    default List<CoursesDO> selectByNameAndType(String name, Integer coursesType, Long excludeId) {
        return selectList(new LambdaQueryWrapperX<CoursesDO>()
                .eqIfPresent(CoursesDO::getName, name)
                .eqIfPresent(CoursesDO::getCoursesType, coursesType)
                .neIfPresent(CoursesDO::getId, excludeId));
    }

    default List<CoursesDO> selectListByCoursesType(Integer coursesType) {
        return selectList(new LambdaQueryWrapperX<CoursesDO>()
                .eqIfPresent(CoursesDO::getCoursesType, coursesType));
    }

    /**
     * 查询课程的授课记录
     *
     * @param page        分页对象
     * @param pageReqVO   查询参数
     * @param currentTime 当前时间
     * @return 授课记录列表
     */
    List<CoursesTeachingRecordRespVO> selectPageForCourseTeachingRecord(IPage<CoursesTeachingRecordRespVO> page,
                                                                        @Param("reqVO") CoursesTeachingRecordReqVO pageReqVO,
                                                                        @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据课程id集合查询课程的授课记录
     *
     * @param pageVO      页面课程查询条件
     * @param currentTime 当前时间
     * @return 课程的授课记录列表
     */
    List<CoursesTeachingRecordRespVO> selectListForTeachingRecordByReqVO(@Param("reqVO") CoursesPageReqVO pageVO,
                                                                         @Param("currentTime") LocalDateTime currentTime);

    default List<CoursesDO> selectListByNames(List<String> names, Integer coursesType){
        if (Objects.isNull(names) || names.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<CoursesDO>()
                .in(CoursesDO::getName, names)
                .eq(CoursesDO::getCoursesType, coursesType));
    }
}
