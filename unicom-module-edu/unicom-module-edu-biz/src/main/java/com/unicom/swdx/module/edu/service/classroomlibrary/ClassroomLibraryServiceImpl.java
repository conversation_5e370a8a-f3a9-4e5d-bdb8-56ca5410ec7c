package com.unicom.swdx.module.edu.service.classroomlibrary;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseClassTimeClassroomReqVO;
import com.unicom.swdx.module.edu.convert.classroomlibrary.ClassroomLibraryConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import com.unicom.swdx.module.edu.dal.mysql.classroomlibrary.ClassroomLibraryMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;


/**
 * EduClassroomLibrary Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ClassroomLibraryServiceImpl extends ServiceImpl<ClassroomLibraryMapper, ClassroomLibraryDO> implements ClassroomLibraryService {

    @Resource
    private ClassroomLibraryMapper classroomLibraryMapper;

    @Override
    public Integer createClassroomLibrary(ClassroomLibraryCreateReqVO createReqVO) {

        // 教室名称唯一性校验
        validateShortName(null, createReqVO.getClassName());

        // 插入
        ClassroomLibraryDO classroomLibrary = ClassroomLibraryConvert.INSTANCE.convert(createReqVO);
        classroomLibraryMapper.insert(classroomLibrary);
        // 返回
        return classroomLibrary.getId();
    }

    @Override
    public void updateClassroomLibrary(ClassroomLibraryUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateClassroomLibraryExists(updateReqVO.getId());

        // 教室名称唯一性校验
        validateShortName(updateReqVO.getId(), updateReqVO.getClassName());

        // 更新
        ClassroomLibraryDO updateObj = ClassroomLibraryConvert.INSTANCE.convert(updateReqVO);
        classroomLibraryMapper.updateById(updateObj);
    }

    @Override
    public void deleteClassroomLibrary(Integer id) {
        // 校验存在
        this.validateClassroomLibraryExists(id);

        // 判断该教室是否被排课  排课之后不允许删除
        Integer count = classroomLibraryMapper.getClassCourseByClassRoomId(id);
        if(count > 0){
            throw exception(CLASSROOM_LIBRARY_CLASS_COURSE_EXISTS);
        }

        // 删除
        classroomLibraryMapper.deleteById(id);
    }

    /**
     * 批量删除EduClassroomLibrary
     *
     * @param classroomLibraryDeleteVO 编号
     */
    @Override
    public void deleteClassroomLibraryBatch(ClassroomLibraryDeleteVO classroomLibraryDeleteVO) {

        String ids = classroomLibraryDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");


        //判断教室是否存在排课情况
        for(String idString : idArray){
            // 转化成 int 类型
            int idInt = Integer.parseInt(idString);
            // 校验存在
            this.validateClassroomLibraryExists(idInt);

            Integer count = classroomLibraryMapper.getClassCourseByClassRoomId(idInt);
            if(count > 0){
                throw exception(CLASSROOM_LIBRARY_CLASS_COURSE_SECTION_EXISTS);
            }
        }

        for (String idStr : idArray) {

            // 转化成 int 类型
            int id = Integer.parseInt(idStr);
            // 删除
            classroomLibraryMapper.deleteById(id);

        }

    }

    private void validateClassroomLibraryExists(Integer id) {
        if (classroomLibraryMapper.selectById(id) == null) {
            throw exception(CLASSROOM_LIBRARY_NOT_EXISTS);
        }
    }

    @Override
    public ClassroomLibraryDO getClassroomLibrary(Integer id) {
        return classroomLibraryMapper.selectById(id);
    }

    @Override
    public PageResult<ClassroomLibraryDO> getClassroomLibraryPage(ClassroomLibraryPageReqVO pageReqVO) {
        return classroomLibraryMapper.selectPage(pageReqVO);
    }

    /**
     * 选修课管理-根据选修课发布上课时间段获取空闲下拉教室数据
     *
     * @param reqVO 上课时间段
     * @return 教室列表
     */
    @Override
    public List<ClassroomLibrarySimpleRespVO> listForElectiveRelease(ElectiveReleaseClassTimeClassroomReqVO reqVO) {
        // 获取上课开始时间、上课结束时间 LocalDateTime
        LocalDateTime classStartTime = LocalDateTime.of(reqVO.getClassDate(), LocalTime.parse(reqVO.getClassStartTimeStr()));
        LocalDateTime classEndTime = LocalDateTime.of(reqVO.getClassDate(), LocalTime.parse(reqVO.getClassEndTimeStr()));
        return classroomLibraryMapper.listForElectiveRelease(classStartTime,
                classEndTime,
                reqVO.getExcludeReleaseId(),
                reqVO.getExcludeClassCourseId());
    }

    /**
     * 校验教室名称唯一
     *
     * @param id
     * @param className
     */
    private void validateShortName(Integer id, String className) {
        ClassroomLibraryDO classObj = classroomLibraryMapper.selectByClassName(className);
        if (classObj == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的客户端
        if (id == null) {
            throw exception(CLASSROOM_LIBRARY_CLASS_NAME_EXISTS);
        }
        if (!classObj.getId().equals(id)) {
            throw exception(CLASSROOM_LIBRARY_CLASS_NAME_EXISTS);
        }
    }

}
