package com.unicom.swdx.module.edu.dal.dataobject.options;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 选项 DO
 *
 * <AUTHOR>
 */
@TableName("pg_options")
@KeySequence("pg_options_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptionsDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 选项类型(字典)
     */
    private String optionsType;
    /**
     * 选项内容
     */
    private String content;
    /**
     * 选项分数
     */
    private BigDecimal score;
    /**
     * 创建部门
     */
    private Long createDept;
    /**
     * 题干主键
     */
    private Long questionId;

}