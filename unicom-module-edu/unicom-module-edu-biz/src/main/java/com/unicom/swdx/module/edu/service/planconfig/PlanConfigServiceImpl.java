package com.unicom.swdx.module.edu.service.planconfig;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.planconfig.PlanConfigConvert;
import com.unicom.swdx.module.edu.dal.mysql.planconfig.PlanConfigMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 教学计划配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlanConfigServiceImpl implements PlanConfigService {

    @Resource
    private PlanConfigMapper planConfigMapper;

    @Override
    public Long createPlanConfig(PlanConfigCreateReqVO createReqVO) {
        // 插入
        PlanConfigDO planConfig = PlanConfigConvert.INSTANCE.convert(createReqVO);
        planConfigMapper.insert(planConfig);
        // 返回
        return planConfig.getId();
    }

    @Override
    public void updatePlanConfig(PlanConfigUpdateReqVO updateReqVO) {
        // 校验存在
        this.validatePlanConfigExists(updateReqVO.getId());
        // 更新
        PlanConfigDO updateObj = PlanConfigConvert.INSTANCE.convert(updateReqVO);
        planConfigMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanConfig(Long id) {
        // 校验存在
        this.validatePlanConfigExists(id);
        // 删除
        planConfigMapper.deleteById(id);
    }

    private void validatePlanConfigExists(Long id) {
        if (planConfigMapper.selectById(id) == null) {
            throw exception(PLAN_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public PlanConfigDO getPlanConfig(Long id) {
        return planConfigMapper.selectById(id);
    }

    @Override
    public List<PlanConfigDO> getPlanConfigList(Collection<Long> ids) {
        return planConfigMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PlanConfigDO> getPlanConfigPage(PlanConfigPageReqVO pageReqVO) {
        return planConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PlanConfigDO> getPlanConfigList(PlanConfigExportReqVO exportReqVO) {
        return planConfigMapper.selectList(exportReqVO);
    }

}
