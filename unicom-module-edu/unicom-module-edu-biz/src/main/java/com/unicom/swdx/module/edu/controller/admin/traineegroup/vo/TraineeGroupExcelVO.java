package com.unicom.swdx.module.edu.controller.admin.traineegroup.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

/**
 * @ClassName: TraineeGroupReqVO
 * @Author: lty
 * @Date: 2024/10/17 17:03
 */
@Data
public class TraineeGroupExcelVO {
    @ExcelProperty(value = "序号")
    private Long index;
    @ExcelProperty(value = "所在小组")
    private String groupName;
    @ExcelProperty(value = "姓名")
    private String name;
    @ExcelProperty(value = "性别")
    private String sex;
    @ExcelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;
    @ExcelProperty(value = "文化程度")
    private String educationalLevel;
    @ExcelProperty(value = "政治面貌")
    private String politicalIdentity;
    @ExcelProperty(value = "学员职务")
    private String position;
    @ExcelProperty(value = "班委职务")
    private String classCommitteeName;
}
