package com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 点名签到信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RollcallSignInRespVO extends RollcallSignInBaseVO {

    @ApiModelProperty(value = "主键", required = true, example = "1")
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true, example = "2020-10-31 09:13:26")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "总人数", example = "100")
    private Integer checkTotal;

    @ApiModelProperty(value = "已打卡人数", example = "50")
    private Integer checkedCount;

    @ApiModelProperty(value = "课程名称", example = "课程1")
    private String courseName;

    @ApiModelProperty(value = "课程Id", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "课程是否调课", example = "0")
    private Boolean isChange;

    @ApiModelProperty(value = "调课是否发生在该大课考勤之后", example = "1")
    private Boolean isAfterChange;

    @ApiModelProperty(value = "上课日期", example = "2024-10-15")
    private String classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    private Integer dayPeriod;

    @ApiModelProperty(value = "上课时段开始时间", example = "00:00")
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课时段结束时间", example = "00:00")
    private String classEndTimeStr;

    /**
     * 上课开始时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classStartTime;

    /**
     * 上课结束时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classEndTime;

}
