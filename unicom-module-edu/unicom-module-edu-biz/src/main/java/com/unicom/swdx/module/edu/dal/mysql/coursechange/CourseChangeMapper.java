package com.unicom.swdx.module.edu.dal.mysql.coursechange;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.coursechange.CourseChangeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调课记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CourseChangeMapper extends BaseMapperX<CourseChangeDO> {

    List<CourseChangeRespVO> selectPageByReqVO(IPage page, @Param("reqVO") CourseChangePageReqVO reqVO);

    List<CourseChangeRespVO> selectInfoByClassCourseId(@Param("classCourseId") Long classCourseId);

    List<WeekTimetableInfoDTO> getWeekTimetable(@Param("reqVO") WeekTimetableReqVO reqVO);

    ClassCourseInfoDTO getClassInfo(@Param("applyId") Long applyId);
}
