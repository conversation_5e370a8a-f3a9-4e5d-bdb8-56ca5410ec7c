package com.unicom.swdx.module.edu.service.evaluationhistory;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageRespVO;

import java.util.List;

/**
 * 历史评价数据 Service 接口
 */
public interface EvaluationHistoryService {

    /**
     * 获得历史评价数据分页
     *
     * @param pageReqVO 分页查询
     * @return 历史评价数据分页
     */
    PageResult<HistoryDataPageRespVO> getHistoryDataPage(HistoryDataPageReqVO pageReqVO);

    /**
     * 获取历史评价数据导出列表
     *
     * @param pageReqVO 分页查询
     * @return 历史评价数据导出列表
     */
    List<HistoryDataExcelVO> getHistoryDataExportList(HistoryDataPageReqVO pageReqVO);
}
