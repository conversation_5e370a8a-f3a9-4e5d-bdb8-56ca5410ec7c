package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserAppletCourseTypeVO extends UserAppletBaseVO {


    /**
     * 教师ID集合
     */
    @ApiModelProperty(value = "课程类型：1专题，2选修课", required = true)
    @NotNull(message = "课程类型不能为空")
    private Integer courseType;


}
