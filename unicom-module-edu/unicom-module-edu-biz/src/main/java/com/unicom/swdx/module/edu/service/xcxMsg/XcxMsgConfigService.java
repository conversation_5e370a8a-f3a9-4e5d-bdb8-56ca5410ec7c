package com.unicom.swdx.module.edu.service.xcxMsg;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigRespVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;

public interface XcxMsgConfigService extends IService<XcxMsgConfigDO> {

    PageResult<XcxMsgConfigRespVO> pageList(XcxMsgConfigPageReqVO reqVO);

    boolean updateConfig(XcxMsgConfigUpdateReqVO reqVO);

}
