package com.unicom.swdx.module.edu.convert.plantemplateconfig;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplateconfig.PlanTemplateConfigDO;

/**
 * 教学计划模版配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanTemplateConfigConvert {

    PlanTemplateConfigConvert INSTANCE = Mappers.getMapper(PlanTemplateConfigConvert.class);

    PlanTemplateConfigDO convert(PlanTemplateConfigCreateReqVO bean);

    PlanTemplateConfigDO convert(PlanTemplateConfigUpdateReqVO bean);

    PlanTemplateConfigRespVO convert(PlanTemplateConfigDO bean);

    List<PlanTemplateConfigRespVO> convertList(List<PlanTemplateConfigDO> list);

    PageResult<PlanTemplateConfigRespVO> convertPage(PageResult<PlanTemplateConfigDO> page);

    List<PlanTemplateConfigExcelVO> convertList02(List<PlanTemplateConfigDO> list);

    List<PlanTemplateConfigDO> convertToDoList(List<PlanTemplateConfigBaseVO> planTemplateConfigBaseVOList);

    List<PlanTemplateConfigBaseVO> convertToBaseVOList(List<PlanTemplateConfigDO> configList);
}
