package com.unicom.swdx.module.edu.dal.dataobject.teacherinformation;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.GenderEnum;
import com.unicom.swdx.framework.common.enums.NationEnum;
import com.unicom.swdx.framework.common.enums.PoliticalOutlookEnum;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 师资部门信息 DO
 *
 * <AUTHOR>
 */
@TableName("edu_teacher_dept")
@KeySequence("edu_teacher_dept_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherDeptDO {

    /**
     * 教师id
     */
    private Long teacherId;

    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 教师部门id
     */
    private Long deptId;

    /**
     * 教师部门名称
     */
    private String deptName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 多租户编号
     */
    private Long tenantId;
}
