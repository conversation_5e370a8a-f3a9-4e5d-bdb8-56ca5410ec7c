package com.unicom.swdx.module.edu.service.classroomlibrary;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseClassTimeClassroomReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;

import javax.validation.Valid;
import java.util.List;

/**
 * EduClassroomLibrary Service 接口
 *
 * <AUTHOR>
 */
public interface ClassroomLibraryService extends IService<ClassroomLibraryDO> {

    /**
     * 创建EduClassroomLibrary
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createClassroomLibrary(@Valid ClassroomLibraryCreateReqVO createReqVO);

    /**
     * 更新EduClassroomLibrary
     *
     * @param updateReqVO 更新信息
     */
    void updateClassroomLibrary(@Valid ClassroomLibraryUpdateReqVO updateReqVO);

    /**
     * 删除EduClassroomLibrary
     *
     * @param id 编号
     */
    void deleteClassroomLibrary(Integer id);

    /**
     * 批量删除EduClassroomLibrary
     *
     * @param classroomLibraryDeleteVO 编号
     */
    void deleteClassroomLibraryBatch(ClassroomLibraryDeleteVO classroomLibraryDeleteVO);

    /**
     * 获得EduClassroomLibrary
     *
     * @param id 编号
     * @return EduClassroomLibrary
     */
    ClassroomLibraryDO getClassroomLibrary(Integer id);

    /**
     * 获得EduClassroomLibrary分页
     *
     * @param pageReqVO 分页查询
     * @return EduClassroomLibrary分页
     */
    PageResult<ClassroomLibraryDO> getClassroomLibraryPage(ClassroomLibraryPageReqVO pageReqVO);

    /**
     * 选修课管理-根据选修课发布上课时间段获取空闲下拉教室数据
     *
     * @param reqVO 上课时间段
     * @return 教室列表
     */
    List<ClassroomLibrarySimpleRespVO> listForElectiveRelease(ElectiveReleaseClassTimeClassroomReqVO reqVO);
}
