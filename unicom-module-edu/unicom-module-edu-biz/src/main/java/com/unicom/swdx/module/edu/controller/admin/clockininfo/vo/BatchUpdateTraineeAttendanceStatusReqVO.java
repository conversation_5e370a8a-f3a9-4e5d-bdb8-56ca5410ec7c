package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("管理后台 - 批量更新学员打卡状态 Request VO")
@Data
public class BatchUpdateTraineeAttendanceStatusReqVO {

    @ApiModelProperty(value = "学员打卡记录ID列表", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "学员打卡记录ID列表不能为空")
    private List<Long> recordIds;

    @ApiModelProperty(value = "学员打卡状态：0-未到 1-正常 2-迟到 3-事假 4-病假 5-五会假", required = true, example = "1")
    @NotNull(message = "学员打卡状态不能为空")
    private Integer status;
}