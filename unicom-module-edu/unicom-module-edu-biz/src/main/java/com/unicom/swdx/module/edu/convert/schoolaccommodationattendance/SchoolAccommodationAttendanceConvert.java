package com.unicom.swdx.module.edu.convert.schoolaccommodationattendance;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;

/**
 * 全校就餐住宿考勤 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SchoolAccommodationAttendanceConvert {

    SchoolAccommodationAttendanceConvert INSTANCE = Mappers.getMapper(SchoolAccommodationAttendanceConvert.class);

    SchoolAccommodationAttendanceDO convert(SchoolAccommodationAttendanceCreateReqVO bean);

    SchoolAccommodationAttendanceDO convert(SchoolAccommodationAttendanceUpdateReqVO bean);

    SchoolAccommodationAttendanceRespVO convert(SchoolAccommodationAttendanceDO bean);

    List<SchoolAccommodationAttendanceRespVO> convertList(List<SchoolAccommodationAttendanceDO> list);

    PageResult<SchoolAccommodationAttendanceRespVO> convertPage(PageResult<SchoolAccommodationAttendanceDO> page);

}
