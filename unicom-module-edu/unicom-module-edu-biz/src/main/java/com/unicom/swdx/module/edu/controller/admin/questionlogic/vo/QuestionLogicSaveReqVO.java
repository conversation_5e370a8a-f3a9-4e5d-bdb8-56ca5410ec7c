package com.unicom.swdx.module.edu.controller.admin.questionlogic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 问题逻辑新增/修改 Request VO")
@Data
public class QuestionLogicSaveReqVO {

    @Schema(description = "主键", example = "3030")
    private Integer id;

    @Schema(description = "逻辑问题id", example = "16879")
    @NotNull(message = "逻辑问题id不能为空")
    private Long questionId;

    @Schema(description = "关联问题id", example = "14855")
    @NotNull(message = "关联问题id不能为空")
    private Long logicQuestionId;

    @Schema(description = "关联问卷id", example = "14855")
    private Long questionnaireId;

    @Schema(description = "打分题的分数", example = "14855")
    private Integer score;

    @Schema(description = "选择题的选项", example = "14855")
    private Long option;

}
