package com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 评估问卷与问题关联 DO
 *
 * <AUTHOR>
 */
@TableName("pg_questionnaire_detail")
@KeySequence("pg_questionnaire_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionnaireDetailDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 问题主键
     */
    private Long questionId;
    /**
     * 试卷主键
     */
    private Long questionnaireId;
    /**
     * 创建部门
     */
    private Long createDept;
    /**
     * 创建人
     */
    // private Long creator;
    /**
     * 0不是1是一票否决
     */
    private Boolean oneBallotVeto;
    /**
     * 0不是1是必选
     */
    private Boolean required;
    /**
     * 一票否决对应的选项
     */
    private Long optionId;

    private Long serialNumber;


}