package com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.math.BigDecimal;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 课程表-教师-授课关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ClassCourseTeacherExcelVO {

    @ExcelProperty("唯一标识符，自增")
    private Long id;

    @ExcelProperty("课程表id")
    private Long classCourseId;

    @ExcelProperty("授课教师id")
    private Long teacherId;

    @ExcelProperty("排序")
    private Long sort;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
