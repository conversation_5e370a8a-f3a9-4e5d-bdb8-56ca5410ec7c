package com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("参训系统 - 站内信 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NotificationMessageRespVO extends NotificationMessageBaseVO{

    @ApiModelProperty(value = "主键，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;
}
