package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 选修课学员选课 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ElectiveTraineeSelectionBaseVO {

    @ApiModelProperty(value = "选修课发布ID", required = true)
    @NotNull(message = "选修课发布ID不能为空")
    private Long releaseId;

    @ApiModelProperty(value = "发布课程ID", required = true)
    @NotNull(message = "选择的发布课程不能为空")
    private Long releaseCourseId;

    @ApiModelProperty(value = "学员ID", required = true)
    @NotNull(message = "学员ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;
}
