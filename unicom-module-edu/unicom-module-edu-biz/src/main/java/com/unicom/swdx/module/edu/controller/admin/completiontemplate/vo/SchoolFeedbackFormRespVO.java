package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

@ApiModel("管理后台 - 结业考核模版设置 Response VO")
@Data
@ToString(callSuper = true)
public class SchoolFeedbackFormRespVO {

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "班主任名称")
    private String classTeacherName;

    @ApiModelProperty(value = "培训时间")
    private String trainingTime;

    /**
     * 办班类型 字典表中的id
     */
    @ApiModelProperty(value = "办班类型")
    private Integer classTypeDictId;

    private List<TraineeInfoVO> traineeInfoList;
    @Data
    public static class TraineeInfoVO{
        @ApiModelProperty(value = "学员id")
        private Long id;
        @ApiModelProperty(value = "学员名字")
        private String name;

        @ApiModelProperty(value = "性别")
        private String sex;

        @ApiModelProperty(value = "民族")
        private Integer ethnic;

        @ApiModelProperty(value = "出生日期")
        @JsonSerialize(using = LocalDateSerializer.class)
        @JsonDeserialize(using = LocalDateDeserializer.class)
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate birthday;

        @ApiModelProperty(value = "政治面貌")
        private Integer politicalIdentity;

        @ApiModelProperty(value = "职务")
        private String position;

        @ApiModelProperty(value = "在校任职")
        private String schoolPosition;
        @ApiModelProperty(value = "单位名称")
        private String unitName;
        @ApiModelProperty(value = "结业日期")
        @JsonSerialize(using = LocalDateSerializer.class)
        @JsonDeserialize(using = LocalDateDeserializer.class)
        @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
        private LocalDate graduateDate;

        @ApiModelProperty(value = "事假")
        private Float business;

        @ApiModelProperty(value = "病假")
        private Float sick;

        @ApiModelProperty(value = "五会假")
        private Float fiveCans;

        @ApiModelProperty(value = "迟到早退")
        private Float lateAndLeaveEarly;

        @ApiModelProperty(value = "旷课")
        private Float absentClass;

        @ApiModelProperty(value = "到课率")
        private String classRate;

        @ApiModelProperty(value = "就餐率")
        private String mealRate;

        @ApiModelProperty(value = "住宿率")
        private String accommodationRate;

        @ApiModelProperty(value = "自学率")
        private String selfLearnRate;

        @ApiModelProperty(value = "学习心得")
        private String learningExperience;

        @ApiModelProperty(value = "理论考试成绩")
        private String theoreticalGrades;

        @ApiModelProperty(value = "结业论文成绩")
        private String graduationThesisScore;

        @ApiModelProperty(value = "综合考评得分")
        private String comprehensiveEvaluationScore;
    }

}
