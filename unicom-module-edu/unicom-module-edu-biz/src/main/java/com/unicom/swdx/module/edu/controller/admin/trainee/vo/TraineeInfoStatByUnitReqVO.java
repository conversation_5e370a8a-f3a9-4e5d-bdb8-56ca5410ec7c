package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: zhouhk
 * @Date: 2024/12/20
 */
@Data
@ApiModel(value = "参训统计筛选条件")
public class TraineeInfoStatByUnitReqVO extends TraineeInfoStatReqVO {

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "所属校区")
    private Integer campus;

    @ApiModelProperty(value = "是否委托班（短班级）")
    private Boolean shortClass;

    @ApiModelProperty("根据单位名称排序，0正序，1倒序，为空不排序")
    private String orderByUnitName;

    @ApiModelProperty("根据年份排序，0正序，1倒序，为空不排序")
    private String orderByYear;

    @ApiModelProperty("根据开班时间排序，0正序，1倒序，为空不排序")
    private String orderByClassOpenTime;

    @ApiModelProperty("根据结业时间排序，0正序，1倒序，为空不排序")
    private String orderByCompletionTime;

    @ApiModelProperty("根据当前的排序整体顺序或降序，0正序，1倒序，为空不排序")
    private String orderByNow;

}
