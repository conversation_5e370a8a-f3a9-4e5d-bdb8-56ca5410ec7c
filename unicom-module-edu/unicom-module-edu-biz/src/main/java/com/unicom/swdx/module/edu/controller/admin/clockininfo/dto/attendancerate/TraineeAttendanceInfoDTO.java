package com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 学员考勤信息DTO
 * @date 2024-11-08
 */
@Data
public class TraineeAttendanceInfoDTO {

    /**
     * 学员ID
     */
    private Long traineeId;

    /**
     * 考勤类型 0-到课 1-就餐 2-住宿
     */
    private Integer type;

    /**
     * 考勤应到人数
     */
    private Integer attendanceExpected;

    /**
     * 考勤实到人数
     */
    private Integer attendanceActual;

    /**
     * 考勤请假人数
     */
    private Integer attendanceLeave;

    /**
     * 考勤率
     */
    private Float rate;
}
