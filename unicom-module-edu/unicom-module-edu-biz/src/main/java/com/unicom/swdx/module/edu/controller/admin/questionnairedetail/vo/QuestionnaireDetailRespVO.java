package com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo;

import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 评估问卷与问题关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionnaireDetailRespVO {

    @Schema(description = "主键ID",  example = "31318")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "问题主键", example = "3908")
    @ExcelProperty("问题主键")
    private Long questionId;

    @Schema(description = "试卷主键", example = "31586")
    @ExcelProperty("试卷主键")
    private Long questionnaireId;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private Long creator;

    @Schema(description = "0不是1是一票否决")
    @ExcelProperty("0不是1是一票否决")
    private Boolean oneBallotVeto;

    @Schema(description = "0不是1是必答题")
    private Boolean required;

    @Schema(description = "一票否决对应的选项id")
    private Long optionId;

}
