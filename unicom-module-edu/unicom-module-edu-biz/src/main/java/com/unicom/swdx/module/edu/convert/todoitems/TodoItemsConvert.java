package com.unicom.swdx.module.edu.convert.todoitems;

import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.todoitems.TodoItemsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 班主任待办事项 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TodoItemsConvert {

    TodoItemsConvert INSTANCE = Mappers.getMapper(TodoItemsConvert.class);


    TodoItemsRespVO convert(TodoItemsDO bean);

    List<TodoItemsRespVO> convertList(List<TodoItemsDO> list);

}
