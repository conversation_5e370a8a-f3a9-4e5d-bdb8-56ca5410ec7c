package com.unicom.swdx.module.edu.utils.word;

import freemarker.template.Configuration;
import freemarker.template.Template;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;


public class DocUtils {

    public static InputStreamResource saveWord(Map<String,Object> dataMap) throws IOException {

        if (dataMap == null) {
            return null;
        }

        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("utf-8");
        configuration.setClassForTemplateLoading(DocUtils.class, "/");

        Template template = configuration.getTemplate("templates/approvalForm.xml");
        InputStreamSource streamSource = createWord(template, dataMap);

        // 创建InputStreamResource
        InputStreamResource resource = new InputStreamResource(streamSource.getInputStream());

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "approvalForm.docx");

        return resource;
    }

//    public static InputStreamResource saveWord(Map<String, Object> dataMap) throws IOException {
//        if (dataMap == null) {
//            return null;
//        }
//
//        try {
//            // FreeMarker 渲染 Flat OPC XML（你的原始逻辑不变）
//            Configuration configuration = new Configuration();
//            configuration.setDefaultEncoding("utf-8");
//            configuration.setClassForTemplateLoading(DocUtils.class, "/");
//
//            Template template = configuration.getTemplate("templates/approvalForm.xml");
//
//            InputStreamSource streamSource = createWord(template, dataMap);
//
//            // ✅ 用 docx4j 8.x 的 load 方法加载 XML 为 Word 文档
//            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(streamSource.getInputStream());
//
//            // 保存为 DOCX 到内存流
//            ByteArrayOutputStream docxOut = new ByteArrayOutputStream();
//            wordMLPackage.save(docxOut);
//
//            return new InputStreamResource(new ByteArrayInputStream(docxOut.toByteArray()));
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new IOException("生成 Word 文件失败", e);
//        }
//    }



    public static InputStreamSource createWord(Template template, Map<String, Object> dataMap) {
        StringWriter out = null;
        Writer writer = null;
        try {
            out = new StringWriter();
            writer = new BufferedWriter(out, 1024);
            template.process(dataMap, writer);
            return new ByteArrayResource(out.toString().getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                writer.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
