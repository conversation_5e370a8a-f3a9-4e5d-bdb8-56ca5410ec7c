package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: TreeNode
 * @Author: lty
 * @Date: 2024/11/21 11:15
 */


public class TraineeTreeBuilder {
    public static class TraineeDO {
        private String moduleName;
        private String columnName;
        private String assessmentName;

        public TraineeDO(String moduleName, String columnName, String assessmentName) {
            this.moduleName = moduleName;
            this.columnName = columnName;
            this.assessmentName = assessmentName;
        }

        public String getModuleName() {
            return moduleName;
        }

        public String getColumnName() {
            return columnName;
        }

        public String getAssessmentName() {
            return assessmentName;
        }
    }

    @Data
    public static class TreeNode {
        private String colName;    // 模块名或列名
        private String prop;       // 属性值
        private String maxScore;       // 属性值
        private String acquisitionMode;  //获取方式
        private String moduleName; // 存储 moduleName
        private String columnName; // 存储 columnName
        private String assessmentName; // 存储 assessmentName
        private String serialNumber; // 存储 assessmentName
        private List<TreeNode> moudleCols = new ArrayList<>();

        public TreeNode(String colName, String prop) {
            this.colName = colName;
            this.prop = prop;
        }

        // Getters, setters, and constructors as needed
        public TreeNode() {
        }

        // Add other methods and constructors as needed
    }


    public static void main(String[] args) {
        // 示例数据
        List<ClassCompletionTemplateDO> list = new ArrayList<>();

        // 构建树形结构
        List<TreeNode> tree = buildTree(list);

        // 使用 Gson 序列化为 JSON
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String jsonOutput = gson.toJson(tree);

        // 输出结果
        System.out.println(jsonOutput);
    }

    public static  List<TreeNode> buildTree(List<ClassCompletionTemplateDO> list) {
        // 按 moduleName 分组，保留顺序
        Map<String, List<ClassCompletionTemplateDO>> moduleGroups = list.stream()
                .collect(Collectors.groupingBy(
                        template -> template.getModuleName() != null ? template.getModuleName() : "",
                        LinkedHashMap::new, // 使用 LinkedHashMap 保留顺序
                        Collectors.toList()
                ));

        return moduleGroups.entrySet().stream()
                .map(moduleEntry -> {
                    String moduleName = moduleEntry.getKey();
                    List<ClassCompletionTemplateDO> moduleItems = moduleEntry.getValue();

                    // 按 columnName 分组，保留顺序
                    Map<String, List<ClassCompletionTemplateDO>> columnGroups = moduleItems.stream()
                            .collect(Collectors.groupingBy(
                                    template -> template.getColumnName() != null ? template.getColumnName() : "",
                                    LinkedHashMap::new, // 使用 LinkedHashMap 保留顺序
                                    Collectors.toList()
                            ));

                    List<TreeNode> columnNodes = columnGroups.entrySet().stream()
                            .map(columnEntry -> {
                                String columnName = columnEntry.getKey();
                                List<ClassCompletionTemplateDO> columnItems = columnEntry.getValue();

                                // 构造 assessment 层节点
                                List<TreeNode> assessmentNodes = columnItems.stream()
                                        .map(template -> {
                                            TreeNode assessmentNode = new TreeNode();
                                            assessmentNode.setAssessmentName(template.getAssessmentName());
                                            assessmentNode.setProp(template.getConversionAnnouncement());
                                            assessmentNode.setSerialNumber(template.getSerialNumber());
                                            assessmentNode.setAcquisitionMode(template.getAcquisitionMode() == null ? "" : template.getAcquisitionMode().toString());
                                            assessmentNode.setMaxScore(template.getMaxScore() == null ? "" : template.getMaxScore().toString());
                                            return assessmentNode;
                                        })
                                        .collect(Collectors.toList());

                                TreeNode columnNode = new TreeNode();
                                columnNode.setColumnName(columnName);
                                columnNode.setMoudleCols(assessmentNodes);
                                return columnNode;
                            })
                            .collect(Collectors.toList());

                    TreeNode moduleNode = new TreeNode();
                    moduleNode.setModuleName(moduleName);
                    moduleNode.setMoudleCols(columnNodes);
                    return moduleNode;
                })
                .collect(Collectors.toList());
    }





    private static String generateProp(String name) {
        return "value_" + name.hashCode(); // 根据名称生成唯一 prop
    }
}

