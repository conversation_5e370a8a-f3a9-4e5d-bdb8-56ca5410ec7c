package com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 点名签到信息列表 Request VO")
@Data
public class RollcallSignInListReqVO {

    @ApiModelProperty(value = "关联的班级表 ID", example = "1")
    @NotNull(message = "班级ID不能为空")
    private Long classId;

    @ApiModelProperty(value = "点名签到信息状态 0 - 进行中(默认) 1-已结束 2-未开始", example = "0")
    @Range(min = 0, max = 2)
    private Integer status;
}
