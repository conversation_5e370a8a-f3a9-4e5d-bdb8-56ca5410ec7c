package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 评估详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaluationDetailPageReqVO extends PageParam {

    @Schema(description = "问卷id", example = "9284")
    private Long questionnaireId;

    @Schema(description = "问题id", example = "13794")
    private Long questionId;

    @Schema(description = "问题类型1打分2单选3简答", example = "2")
    private String questionType;

    @Schema(description = "评卷人id", example = "14762")
    private Long studentId;

    @Schema(description = "打分题得分")
    private Long score;

    @Schema(description = "选择题选项", example = "21307")
    private Long optionId;

    @Schema(description = "简单题内容")
    private String content;

    @Schema(description = "课程id", example = "24082")
    private Long classCourseId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}