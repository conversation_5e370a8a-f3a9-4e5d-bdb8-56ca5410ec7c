package com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 课程表-教师-授课关系 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassCourseTeacherBaseVO {

    @ApiModelProperty(value = "课程表id", required = true)
    @NotNull(message = "课程表id不能为空")
    private Long classCourseId;

    @ApiModelProperty(value = "授课教师id", required = true)
    @NotNull(message = "授课教师id不能为空")
    private Long teacherId;

    @ApiModelProperty(value = "排序")
    private Long sort;
}
