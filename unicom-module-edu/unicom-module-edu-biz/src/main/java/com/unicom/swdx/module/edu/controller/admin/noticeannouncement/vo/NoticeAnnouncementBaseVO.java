package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
* EduNoticeAnnouncement Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class NoticeAnnouncementBaseVO {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "通知公告内容")
    private String content;

    @ApiModelProperty(value = "是否置顶,1-是，0-否")
    private Integer isTop;

    @ApiModelProperty(value = "文件地址,多个用,拼接")
    private String fileUrl;

    @ApiModelProperty(value = "文件名,多个用,拼接")
    private String fileName;

    @ApiModelProperty(value = "是否发布，1-发布，2-存草稿箱")
    private Integer isPublish;

    @ApiModelProperty(value = "状态，1-上架，0-下架")
    private Integer status;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "发布人")
    private String publisher;

    @ApiModelProperty(value = "存草稿箱时间")
    private LocalDateTime draftsTime;

    @ApiModelProperty(value = "置顶时间")
    private LocalDateTime topTime;

    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    @ApiModelProperty(value = "系统模块：1-学员管理系统 ，2-教务管理系统/参训系统")
    private Integer moduleCode;

    @ApiModelProperty(value = "班级id集合")
    private String classIds;

    @ApiModelProperty(value = "发布人id")
    private Long publishId;
}
