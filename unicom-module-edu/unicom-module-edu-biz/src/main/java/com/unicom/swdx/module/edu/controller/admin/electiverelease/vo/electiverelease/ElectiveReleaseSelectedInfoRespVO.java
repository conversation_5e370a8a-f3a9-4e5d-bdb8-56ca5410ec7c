package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 管理后台 - 选修课发布信息已选人数列表 Response VO
 */
@ApiModel("管理后台 - 选修课发布信息已选人数列表 Response VO")
@Data
public class ElectiveReleaseSelectedInfoRespVO {

    @ApiModelProperty(value = "发布ID", example = "1024")
    private Long releaseId;

    @ApiModelProperty(value = "选修课ID", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "选修课名称", example = "选修课")
    private String courseName;

    @ApiModelProperty(value = "授课教师ID", example = "1024")
    private Long teacherId;

    @ApiModelProperty(value = "授课教师", example = "王老师")
    private String teacherName;

    @ApiModelProperty(value = "班次ID", example = "1024")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "班次名称", example = "1班")
    private String className;

    @ApiModelProperty(value = "学员ID", example = "1024")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名", example = "王小明")
    private String traineeName;

    @ApiModelProperty(value = "学员性别", example = "1")
    private String traineeSex;

    @ApiModelProperty(value = "学员职务", example = "职务")
    private String position;
}
