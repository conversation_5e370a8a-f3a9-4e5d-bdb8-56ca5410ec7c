package com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EduTrainFilePageRespVO  {

    /**
     *  自增id
     */
    private Long id;

    /**
     *  标题
     */
    @ApiModelProperty("title")
    private String title;

    /**
     *  文件
     */
    @ApiModelProperty("file")
    private String file;

    /**
     *  用户id
     */
    @ApiModelProperty("userId")
    private Long userId;

    /**
     *  用户名字
     */
    @ApiModelProperty("userName")
    private String userName;

    /**
     *  发布时间
     */
    @ApiModelProperty("publishTime")
    private String publishTime;

    /**
     *  租户id
     */
    @ApiModelProperty("tenantId")
    private Long tenantId;

}
