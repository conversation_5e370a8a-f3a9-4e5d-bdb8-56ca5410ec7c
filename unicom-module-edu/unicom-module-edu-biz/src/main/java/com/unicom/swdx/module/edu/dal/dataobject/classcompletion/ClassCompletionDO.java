package com.unicom.swdx.module.edu.dal.dataobject.classcompletion;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 结业考核模版设置 DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_completion")
@KeySequence("edu_class_completion_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCompletionDO extends TenantBaseDO {

    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 学员id
     */
    private Long traineeId;
    /**
     * 学员名称
     */
    private String traineeName;
    /**
     * 小组名
     */
    private String groupName;
    /**
     * 单位职务
     */
    private String unitPosition;
    /**
     * 班级职务
     */
    private String classPosition;
    /**
     * 班级id
     */
    private Long classId;

    /**
     * 考核名称
     */
    private String assessmentName;

    /**
     * 分数
     */
    private String score;
    /**
     * 考核序列号
     */
    private String serialNumber;

    @TableField(exist = false)
    private Integer acquisitionMode;

    @TableField(exist = false)
    private Integer dataSource;

    @TableField(exist = false)
    private String moduleName;

    @TableField(exist = false)
    private String columnName;

}
