package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 题库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionRespVO {
    @Schema(description = "主键ID",  example = "4884")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "题目类别名称", example = "李四")
    @ExcelProperty("题目类别名称")
    private String fullName;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "父级", example = "2048")
    @ExcelProperty("父级")
    private Long parentId;

    @Schema(description = "类型", example = "0")
    @ExcelProperty("类型")
    private Long type;

    @Schema(description = "题目主键ID",  example = "4884")
    @ExcelProperty("题目主键ID")
    private Long questionId;

    @Schema(description = "题干", example = "0")
    @ExcelProperty("题干")
    private String stem;

    @Schema(description = "标题", example = "0")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "题目类型(字典)", example = "1")
    @ExcelProperty("题目类型(字典)")
    private String questionType;

    @Schema(description = "题目类型(字典)", example = "1")
    @ExcelProperty("题目类型(字典)")
    private String questionScore;

    @Schema(description = "单选题选项", example = "1")
    @ExcelProperty("单选题选项")
    private List<OptionsDO> options;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;
}
