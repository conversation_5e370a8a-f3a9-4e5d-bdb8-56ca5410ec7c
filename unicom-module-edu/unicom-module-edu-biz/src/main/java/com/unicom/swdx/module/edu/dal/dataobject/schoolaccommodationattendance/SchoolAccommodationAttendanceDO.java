package com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 全校就餐住宿考勤 DO
 *
 * <AUTHOR>
 */
@TableName("edu_school_accommodation_attendance")
@KeySequence("edu_school_accommodation_attendance_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchoolAccommodationAttendanceDO extends TenantBaseDO {

    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 早餐考勤，0-开，1-关
     */
    private Integer breakfast;
    /**
     * 午餐考勤，0-开，1-关
     */
    private Integer lunch;
    /**
     * 晚餐考勤，0-开，1-关
     */
    private Integer dinner;
    /**
     * 住宿考勤，0-开，1-关
     */
    private Integer putUp;
    /**
     * 考勤日期
     */
    private LocalDate clockDate;
    /**
     * 是否节假日
     * 0 - 是   1 - 否
     */
    private Integer isHoliday;

}
