package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto.TeacherEvaluationExportIdItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 我的评估分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MyEvaluationPageReqVO extends PageParam {
    @ApiModelProperty(value = "班级ID", example = "1")
    private Long classId;

    @ApiModelProperty(value = "课程名称", example = "课程1")
    private String courseName;

    @ApiModelProperty(value = "所属部门")
    private String deptName;

    @ApiModelProperty(value = "开始日期", example = "2023-01-01")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束日期", example = "2023-01-01")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "教师ID", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "教师ID列表", example = "1")
    private List<Long> teacherIdList;

    @ApiModelProperty(value = "教师姓名", example = "教师1")
    private String teacherName;

    @ApiModelProperty(value = "是否组装教学形式字符串", example = "true")
    private Boolean setEducateForm;

    @ApiModelProperty(value = "是否校验登录权限(默认不校验)", example = "true")
    private Boolean isTeacher;

    @ApiModelProperty(value = "多选id（教师评估导出-（class_course_id和teacherId确定一行）我的评估导出（class_course_id、course_id和teacherId确定一行）")
    private @Valid List<TeacherEvaluationExportIdItemDTO> manyIds;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "排序字段(默认按上课时间) 0-默认授课时间 1-按课程名称 2-按平均分 3-按教师姓名 4-教师所属部门 5-排名分")
    @Range(min = 0, max = 5, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序排列(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

    @ApiModelProperty(value = "课程结束时间")
    private LocalDateTime courseEndTime;

}