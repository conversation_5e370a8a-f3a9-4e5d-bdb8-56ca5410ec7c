package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 业中首页-仪表盘 - 课程评估排名详情 Response VO
 * @date 2024-11-19
 */
@ApiModel("业中首页-仪表盘 - 课程评估排名详情 Response VO")
@Data
public class RankDetailVO {

    @ApiModelProperty(value = "排名", example = "1")
    private Integer rankNumber;

    @ApiModelProperty(value = "部门名称", example = "研发部")
    private String departmentName;

    @ApiModelProperty(value = "部门ID", example = "1")
    private Long deptId;

    @ApiModelProperty(value = "教务教师ID", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "教师名称", example = "张三")
    private String teacherName;

    @ApiModelProperty(value = "课程数量", example = "10")
    private Integer courseCount;

    @ApiModelProperty("课程评分")
    private String avgScore;

    @ApiModelProperty(value = "是否部门授课", example = "true")
    private Boolean isDeptTeaching;
}
