package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("班主任移动端 - 课评查看 评课学员信息 Response VO")
@Data
public class CourseEvaluationTraineeDetailVO {

    @ApiModelProperty("学员ID")
    private Long traineeId;

    @ApiModelProperty("学员姓名")
    private String traineeName;

    @ApiModelProperty("学员手机号")
    private String phone;

    @ApiModelProperty("学员性别")
    private String sex;

    @ApiModelProperty(value = "用户头像", required = true, example = "http://www.iocoder.cn/xx.jpg")
    private String avatar;

}
