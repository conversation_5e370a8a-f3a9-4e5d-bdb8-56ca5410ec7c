package com.unicom.swdx.module.edu.service.users;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.users.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.users.UsersDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.users.UsersMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 用户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UsersServiceImpl implements UsersService {

    private static final Integer USERS_NOT_EXISTS = new Integer( 500);

    @Resource
    private UsersMapper usersMapper;

    @Override
    public Long createUsers(UsersSaveReqVO createReqVO) {
        // 插入
        UsersDO users = BeanUtils.toBean(createReqVO, UsersDO.class);
        usersMapper.insert(users);
        // 返回
        return users.getId();
    }

    @Override
    public void updateUsers(UsersSaveReqVO updateReqVO) {
        // 校验存在
        validateUsersExists(updateReqVO.getId());
        // 更新
        UsersDO updateObj = BeanUtils.toBean(updateReqVO, UsersDO.class);
        usersMapper.updateById(updateObj);
    }

    @Override
    public void deleteUsers(Long id) {
        // 校验存在
        validateUsersExists(id);
        // 删除
        usersMapper.deleteById(id);
    }

    private void validateUsersExists(Long id) {
        if (usersMapper.selectById(id) == null) {
            throw exception(USERS_NOT_EXISTS);
        }
    }

    @Override
    public UsersDO getUsers(Long id) {
        return usersMapper.selectById(id);
    }

    @Override
    public PageResult<UsersDO> getUsersPage(UsersPageReqVO pageReqVO) {
        return usersMapper.selectPage(pageReqVO);
    }

}
