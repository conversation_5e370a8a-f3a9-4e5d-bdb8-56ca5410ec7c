package com.unicom.swdx.module.edu.service.notificationmessage;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

public interface NotificationMessageService {

    Integer createNotificationMessage(@Valid NotificationMessageCreateReqVO createReqVO);

    void updateNotificationMessage(@Valid NotificationMessageUpdateReqVO updateReqVO);

    void deleteNotificationMessage(Integer id);

    void deleteNotificationMessageBatch(NotificationMessageDeleteVO noticeDeleteVO);

    NotificationMessageRespVO getNotificationMessage(Integer id,Integer unit);

    PageResult<NotificationMessageRespVO> getNotificationMessagePage(NotificationMessagePageReqVO pageReqVO);

    void publishBatch(NotificationMessageDeleteVO noticeDeleteVO);

    void getNotificationMessageInfoList(NotificationMessageExportVO reqVO, HttpServletResponse response) throws IOException;


}
