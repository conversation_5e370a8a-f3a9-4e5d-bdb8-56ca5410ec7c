package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 结业考核模版设置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CompletionTemplateRespVO extends CompletionTemplateBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
