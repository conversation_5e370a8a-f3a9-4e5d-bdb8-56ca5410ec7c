package com.unicom.swdx.module.edu.service.leavenotification;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.leavenotification.vo.*;

/**
 * 离校报备 Service 接口
 */
public interface LeaveNotificationService {

    /**
     * 创建离校报备
     *
     * @param createReqVO 创建信息
     * @return 离校报备ID
     */
    Long createLeaveNotification(LeaveNotificationCreateReqVO createReqVO);

    /**
     * 删除离校报备
     *
     * @param id 编号
     */
    void deleteLeaveNotification(Long id);

    /**
     * 获得离校报备详情
     *
     * @param id 编号
     * @return 离校报备详情
     */
    LeaveNotificationDetailRespVO getLeaveNotificationDetail(Long id);

    /**
     * 获得离校报备分页
     *
     * @param pageReqVO 分页查询
     * @return 离校报备分页
     */
    PageResult<LeaveNotificationRespVO> getLeaveNotificationPage(LeaveNotificationPageReqVO pageReqVO);

    /**
     * 切换学员手机号脱敏状态
     *
     * @param traineeId      学员ID
     * @param notificationId 离校报备ID
     */
    void toggleTraineeMobileMask(Long traineeId, Long notificationId);

    /**
     * 批量通知学员
     *
     * @param reqVO 通知信息
     */
    void batchNotifyTrainees(LeaveNotificationBatchNotifyReqVO reqVO);
}