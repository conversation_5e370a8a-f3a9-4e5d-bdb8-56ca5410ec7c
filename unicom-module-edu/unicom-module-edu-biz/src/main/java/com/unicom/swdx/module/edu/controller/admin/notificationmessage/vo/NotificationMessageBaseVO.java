package com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class NotificationMessageBaseVO {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "站内信内容")
    private String content;

    @ApiModelProperty(value = "是否置顶,1-是，0-否")
    private Integer isTop;

    @ApiModelProperty(value = "单位id")
    private List<Integer> units;

    @ApiModelProperty(value = "是否发布，1-发布，2-存草稿箱")
    private Integer isPublish;

    @ApiModelProperty(value = "状态，1-上架，0-下架")
    private Integer status;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "发布人")
    private String publisher;

    @ApiModelProperty(value = "存草稿箱时间")
    private LocalDateTime draftsTime;

    @ApiModelProperty(value = "置顶时间")
    private LocalDateTime topTime;

}
