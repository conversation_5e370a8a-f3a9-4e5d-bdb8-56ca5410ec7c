package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class ExportCadreInformationReqVO {

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "单位id", required = true)
    private Long unitId;

    @ApiModelProperty(value = "导出特定id列表", required = true)
    private List<Long> idList;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;
}
