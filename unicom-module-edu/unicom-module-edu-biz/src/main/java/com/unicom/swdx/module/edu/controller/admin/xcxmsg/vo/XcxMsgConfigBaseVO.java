package com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XcxMsgConfigBaseVO {

    @ApiModelProperty(value = "配置标题")
    private String title;

    @ApiModelProperty(value = "通知内容")
    private String informContent;

    @ApiModelProperty(value = "通知时间")
    private String informTime;

    @ApiModelProperty(value = "提示说明")
    private String remark;

    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Boolean status;

}
