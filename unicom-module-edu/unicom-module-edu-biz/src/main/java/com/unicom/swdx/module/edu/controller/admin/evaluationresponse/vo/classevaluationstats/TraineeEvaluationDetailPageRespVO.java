package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 班次评估统计-学员参评详情分页 Response VO
 * @date 2024-11-19
 */
@ApiModel("部门评估-班次评估统计分页-学员参评详情分页 Response VO")
@Data
public class TraineeEvaluationDetailPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "学员ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名", example = "课程")
    private String traineeName;

    @ApiModelProperty(value = "应评次数", example = "10")
    private Integer expectedCount;

    @ApiModelProperty(value = "已评次数", example = "5")
    private Integer actualCount;

    @ApiModelProperty(value = "未评次数", example = "5")
    private Integer notActualCount;

    @ApiModelProperty(value = "参评率", example = "50.00%")
    private String ratioStr;

    @ApiModelProperty(value = "参评率", example = "50.0001", hidden = true)
    private Float ratio;
}
