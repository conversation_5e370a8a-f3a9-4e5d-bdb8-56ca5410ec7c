package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import lombok.Data;

import java.time.LocalDateTime;
@Data
public class StudentLeaveReportRespVO {
    private Long id;
    /**
     * 离校报备名称
     */
    private String name;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 开始时间
     */
    private LocalDateTime leaveTime;
    /**
     * 结束时间
     */
    private LocalDateTime returnTime;
    /**
     * 填报状态,0已结束，1已申请，2未申请
     */
    private Integer status;

    private Long detailId;

    private LocalDateTime createTime;
}
