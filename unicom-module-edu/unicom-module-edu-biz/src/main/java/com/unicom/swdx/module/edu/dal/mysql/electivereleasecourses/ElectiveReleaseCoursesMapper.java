package com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordReqVO;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordRespVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseCourseSelectedInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 发布课程 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveReleaseCoursesMapper extends BaseMapperX<ElectiveReleaseCoursesDO> {


    List<ElectiveReleaseCoursesDO> selectListByClassTimePeriod(@Param("classStartDateTime") LocalDateTime classStartDateTime,
                                                               @Param("classEndDateTime") LocalDateTime classEndDateTime,
                                                               @Param("excludeId") Long excludeId);

    List<ElectiveReleaseCoursesSubRespVO> selectReleaseCoursesListByReleaseIdList(@Param("releaseIdList") List<Long> releaseIdList);

    default int deleteByReleaseId(Long releaseId) {
        return delete(new LambdaQueryWrapperX<ElectiveReleaseCoursesDO>()
                .eq(ElectiveReleaseCoursesDO::getReleaseId, releaseId));
    }

    default int deleteBatchByReleaseId(List<Long> releaseIdList) {
        return delete(new LambdaQueryWrapperX<ElectiveReleaseCoursesDO>()
                .in(ElectiveReleaseCoursesDO::getReleaseId, releaseIdList));
    }

    /**
     * 根据发布课程id获得课程信息
     *
     * @param releaseCourseId 发布课程id
     * @return 课程信息
     */
    CoursesDO selectCourseByReleaseCourseId(@Param("releaseCourseId") Long releaseCourseId);

    /**
     * 获得选修课发布课程已选人数分页列表信息
     *
     * @param releaseId   发布id
     * @param classIdList 指定班级id列表范围
     * @return 获得选修课发布课程已选人数分页列表信息
     */
    List<ReleaseCourseSelectedInfoDTO> getReleaseCourseSelectedNumInfo(@Param("releaseId") Long releaseId,
                                                                       @Param("classIdList") List<Long> classIdList);

    /**
     * 查询课程的授课记录
     *
     * @param page        分页对象
     * @param pageReqVO   查询参数
     * @param currentTime 当前时间
     * @return 授课记录列表
     */
    List<CoursesTeachingRecordRespVO> selectPageForCourseTeachingRecord(IPage<CoursesTeachingRecordRespVO> page,
                                                                        @Param("reqVO") CoursesTeachingRecordReqVO pageReqVO,
                                                                        @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据课程id集合查询课程的授课记录
     *
     * @param pageVO      页面课程查询条件
     * @param currentTime 当前时间
     * @return 课程的授课记录列表
     */
    List<CoursesTeachingRecordRespVO> selectListForTeachingRecordByReqVO(@Param("reqVO") CoursesPageReqVO pageVO,
                                                                         @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据课程id集合查询发布课程信息
     * @param courseIdList 课程id集合
     * @return 发布课程信息
     */
    default List<ElectiveReleaseCoursesDO> selectCourseByCourseIdList(List<Long> courseIdList){
        if (CollectionUtils.isEmpty(courseIdList)){
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<ElectiveReleaseCoursesDO>()
                .in(ElectiveReleaseCoursesDO::getCourseId, courseIdList));
    }
}
