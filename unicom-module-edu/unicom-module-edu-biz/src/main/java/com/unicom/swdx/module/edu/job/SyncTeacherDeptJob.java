package com.unicom.swdx.module.edu.job;

import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherDeptMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: SyncTeacherDeptJob 根据edu_teacher_information表更新edu_teacher_dept
 * @Author: hz
 * @Date: 2025/04/16
 */
@Service
@Slf4j
public class SyncTeacherDeptJob {

    @Resource
    private TeacherDeptMapper teacherDeptMapper;

    @TenantIgnore
    @XxlJob("SyncTeacherDeptJob")
    public void syncTeacherDept() {
        log.info("SyncTeacherDeptJob start");
        // 全量删除
        teacherDeptMapper.delete(new LambdaQueryWrapperX<>());
        // 同步
        teacherDeptMapper.syncTeacherDeptForTenantId(null);
        // 更新班级状态
        log.info("SyncTeacherDeptJob end");
    }

}
