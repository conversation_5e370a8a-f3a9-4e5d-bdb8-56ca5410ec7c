package com.unicom.swdx.module.edu.controller.admin.leavenotification.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("管理后台 - 离校报备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveNotificationPageReqVO extends PageParam {

    @ApiModelProperty(value = "离校报备名称", example = "第1次离校申请", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "班级ID", example = "1024")
    private Long classId;

    @ApiModelProperty(value = "班级名称", example = "2023级软件开发1班", notes = "模糊匹配")
    private String className;

    @ApiModelProperty(value = "班主任ID", example = "2048")
    private Long headTeacherId;

    @ApiModelProperty(value = "班主任姓名", example = "张老师", notes = "模糊匹配")
    private String headTeacherName;

    @ApiModelProperty(value = "放假时间", notes = "查询条件为指定日期是否落在放假时间段内")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate holidayDate;

    @ApiModelProperty(value = "状态", example = "1", notes = "参见 NotificationStatusEnum 枚举")
    private Integer status;

    @ApiModelProperty(value = "创建时间范围起始", example = "2022-10-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeBegin;

    @ApiModelProperty(value = "创建时间范围结束", example = "2022-10-01 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;
}