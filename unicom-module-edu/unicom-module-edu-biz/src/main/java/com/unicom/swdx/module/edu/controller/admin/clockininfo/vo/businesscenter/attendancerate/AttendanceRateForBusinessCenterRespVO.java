package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 考勤三率响应
 * @date 2024-11-08
 */
@ApiModel("业中首页-仪表盘-考勤三率 Resp VO")
@Data
public class AttendanceRateForBusinessCenterRespVO {

    @ApiModelProperty(value = "考勤已打卡、未打卡、请假详情")
    private List<AttendanceDetailsVO> attendanceDetailsVOList;
}
