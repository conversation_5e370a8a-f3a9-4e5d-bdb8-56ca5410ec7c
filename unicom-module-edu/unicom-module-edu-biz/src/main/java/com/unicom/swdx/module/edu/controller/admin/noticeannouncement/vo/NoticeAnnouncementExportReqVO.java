package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@ApiModel(value = "管理后台 - EduNoticeAnnouncement Excel 导出 Request VO", description = "参数和 NoticeAnnouncementPageReqVO 是一致的")
@Data
public class NoticeAnnouncementExportReqVO {

    @ExcelProperty("标题")
    @ApiModelProperty(value = "标题序号 0")
    private String title;

    @ExcelProperty("状态")
    @ApiModelProperty(value = "状态序号 1")
    private String status;

    @ExcelProperty("发布时间")
    @ApiModelProperty(value = "发布时间序号 2")
    private LocalDate publishTime;

    @ExcelProperty(value = "发布人")
    @ApiModelProperty(value = "发布时间序号 3")
    private String publisher;

    @ExcelProperty(value = "存草稿箱时间")
    @ApiModelProperty(value = "草稿箱序号 4")
    private LocalDate draftsTime;

}
