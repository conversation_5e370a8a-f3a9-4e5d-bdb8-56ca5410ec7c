package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@ApiModel("管理后台 - 结业考核模版设置 Response VO")
@Data
@ToString(callSuper = true)
public class ClassCompletionRespVO {


    @ApiModelProperty(value = "表头节点")
    private List<TraineeTreeBuilder.TreeNode> treeNodes;


//    private ClassCompletionDO traineeInfo;
//    private List<Map<String,Double>> mapScore;


}
