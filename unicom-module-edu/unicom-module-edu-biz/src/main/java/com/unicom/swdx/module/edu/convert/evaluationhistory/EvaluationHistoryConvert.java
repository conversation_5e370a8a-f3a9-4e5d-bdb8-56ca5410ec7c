package com.unicom.swdx.module.edu.convert.evaluationhistory;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationhistory.EvaluationHistoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 历史评价数据 Convert
 */
@Mapper
public interface EvaluationHistoryConvert {

    EvaluationHistoryConvert INSTANCE = Mappers.getMapper(EvaluationHistoryConvert.class);

    HistoryDataPageRespVO convert(EvaluationHistoryDO bean);

    List<HistoryDataPageRespVO> convertList(List<EvaluationHistoryDO> list);

    PageResult<HistoryDataPageRespVO> convertPage(PageResult<EvaluationHistoryDO> page);

    List<HistoryDataExcelVO> convertList02(List<EvaluationHistoryDO> list);
}
