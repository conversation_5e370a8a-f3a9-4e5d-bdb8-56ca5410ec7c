package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - EduSignUpUnit更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SignUpUnitUpdateReqVO extends SignUpUnitBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    @NotNull(message = "主键id，自增不能为空")
    private Integer id;

}
