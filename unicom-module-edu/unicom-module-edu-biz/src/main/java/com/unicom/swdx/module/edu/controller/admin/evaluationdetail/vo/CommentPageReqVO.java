package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 意见详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CommentPageReqVO extends PageParam {

    @Schema(description = "排课id", example = "9284")
    private Long classCourseId;

    @ApiModelProperty(value = "课程ID", example = "1")
    private Long courseId;

//    @Schema(description = "问卷id", example = "9284")
//    private Long questionnaireId;

}
