package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - EduSignUpUnit分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SignUpUnitPageReqVO extends PageParam {

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位分类")
    private Integer unitClassification;

    @ApiModelProperty(value = "单位责任人")
    private String unitChargePeople;

    @ApiModelProperty(value = "负责人电话")
    private String phone;

    @ApiModelProperty(value = "状态，1-启用，2-禁用")
    private Integer status;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "名额人数")
    private Integer capacity;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "班级id")
    private Long classId;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

    private List<Long> idList;

}
