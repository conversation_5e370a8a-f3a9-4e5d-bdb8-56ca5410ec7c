package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 选修课发布课程关联课程详细信息导出excel，包含选课人数 VO
 */
@ApiModel("管理后台 - 选修课发布课程关联课程详细信息导出excel，包含选课人数 VO")
@Data
public class ElectiveReleaseCoursesExcelVO {


    @ExcelProperty(value = "选修课名称")
    private String courseName;

    @ExcelProperty(value = "授课教师")
    private String teacherName;

    @ExcelProperty(value = "选课人数")
    private Long selectedNum;

    @ExcelProperty(value = "教室名称")
    private String classroomName;
}
