package com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 大课考勤、点名签到信息 DO
 *
 * <AUTHOR>
 */
@TableName("edu_rollcall_sign_in")
@KeySequence("edu_rollcall_sign_in_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RollcallSignInDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 点名签到标题
     */
    private String title;
    /**
     * 关联的课表 ID
     */
    private Long classCourseId;
    /**
     * 关联的班级表 ID
     */
    private Long classId;
    /**
     * 0-大课考勤 1-点名签到
     */
    private Integer type;
    /**
     * 打卡开始时间
     */
    private LocalDateTime checkStartTime;
    /**
     * 打卡结束时间
     */
    private LocalDateTime checkEndTime;
    /**
     * 打卡位置的纬度
     */
    private String latitude;
    /**
     * 打卡位置的经度
     */
    private String longitude;
    /**
     * 打卡范围半径
     */
    private BigDecimal radius;
    /**
     * 打卡位置的地址
     */
    private String address;
    /**
     * 是否手动结束考勤
     */
    private Boolean ended;
    /**
     * 状态0-正常 1-撤回状态
     */
    private Integer status;
    /**
     * 系统内部门
     */
    private Long deptId;

}
