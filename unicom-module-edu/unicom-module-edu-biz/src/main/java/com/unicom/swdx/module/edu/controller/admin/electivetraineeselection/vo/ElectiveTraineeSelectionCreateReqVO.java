package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 选修课学员选课创建 Request VO")
@Data
public class ElectiveTraineeSelectionCreateReqVO {

    @ApiModelProperty(value = "选修课发布ID", required = true)
    @NotNull(message = "选修课发布ID不能为空")
    private Long releaseId;

    @ApiModelProperty(value = "发布课程ID", required = true)
    @NotNull(message = "选择的发布课程不能为空")
    private Long releaseCourseId;

}
