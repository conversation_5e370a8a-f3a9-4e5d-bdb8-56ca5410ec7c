package com.unicom.swdx.module.edu.convert.teachercourseinformation;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;

/**
 * 师资-任课信息中间 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TeacherCourseInformationConvert {

    TeacherCourseInformationConvert INSTANCE = Mappers.getMapper(TeacherCourseInformationConvert.class);

    TeacherCourseInformationDO convert(TeacherCourseInformationCreateReqVO bean);

    TeacherCourseInformationDO convert(TeacherCourseInformationUpdateReqVO bean);

    TeacherCourseInformationRespVO convert(TeacherCourseInformationDO bean);

    List<TeacherCourseInformationRespVO> convertList(List<TeacherCourseInformationDO> list);

    PageResult<TeacherCourseInformationRespVO> convertPage(PageResult<TeacherCourseInformationDO> page);

    List<TeacherCourseInformationExcelVO> convertList02(List<TeacherCourseInformationDO> list);

}
