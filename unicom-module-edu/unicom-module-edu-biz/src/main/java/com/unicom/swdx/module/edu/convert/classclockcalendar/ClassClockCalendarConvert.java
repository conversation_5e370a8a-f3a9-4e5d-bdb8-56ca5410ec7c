package com.unicom.swdx.module.edu.convert.classclockcalendar;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;

/**
 * 班级考勤日历 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassClockCalendarConvert {

    ClassClockCalendarConvert INSTANCE = Mappers.getMapper(ClassClockCalendarConvert.class);

    ClassClockCalendarDO convert(ClassClockCalendarCreateReqVO bean);

    ClassClockCalendarDO convert(ClassClockCalendarUpdateReqVO bean);

    ClassClockCalendarRespVO convert(ClassClockCalendarDO bean);

    List<ClassClockCalendarRespVO> convertList(List<ClassClockCalendarDO> list);

    PageResult<ClassClockCalendarRespVO> convertPage(PageResult<ClassClockCalendarDO> page);
}
