package com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 题目类别管理 DO
 *
 * <AUTHOR>
 */
@TableName("pg_question_category_management")
@KeySequence("pg_question_category_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionCategoryManagementDO extends TenantBaseDO {

    public static final Long PARENT_ID_ROOT = 0L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 题目类别名称
     */
    private String fullName;
    /**
     * 创建部门
     */
    private Long createDept;
    /**
     * 父级
     */
    private Long parentId;

    private Boolean builtIn;

}