package com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("参训系统 - 站内信 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NotificationMessageUpdateReqVO extends NotificationMessageBaseVO{

    @ApiModelProperty(value = "主键，自增", required = true)
    @NotNull(message = "主键，自增不能为空")
    private Integer id;

}
