package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDate;


/**
 * @ClassName: TraineeReportExcelVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "参训报名导出VO")
public class TraineeReportExcelVO {

    /**
     * 班次编码
     */
    @ExcelProperty(value = "班次编码")
    private String classNameCode;
    /**
     * 班次名称
     */
    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "报名开始时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate registrationStartTime;

    @ExcelProperty(value = "报名结束时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate registrationEndTime;

    /**
     * 开班日期
     */
    @ExcelProperty(value = "开班日期")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate classOpenTime;


    /**
     * 校区
     */
    @ExcelProperty(value = "校区")
    private String campus;


    /**
     * 名额
     */
    @ExcelProperty(value = "名额")
    private String capacity;


}
