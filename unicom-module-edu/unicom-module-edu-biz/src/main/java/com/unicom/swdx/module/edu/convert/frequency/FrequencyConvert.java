package com.unicom.swdx.module.edu.convert.frequency;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.frequency.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.frequency.FrequencyDO;

/**
 * 使用次数 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FrequencyConvert {

    FrequencyConvert INSTANCE = Mappers.getMapper(FrequencyConvert.class);

    FrequencyDO convert(FrequencyCreateReqVO bean);

    FrequencyDO convert(FrequencyUpdateReqVO bean);

    FrequencyRespVO convert(FrequencyDO bean);

    List<FrequencyRespVO> convertList(List<FrequencyDO> list);

    PageResult<FrequencyRespVO> convertPage(PageResult<FrequencyDO> page);

    List<FrequencyExcelVO> convertList02(List<FrequencyDO> list);

}
