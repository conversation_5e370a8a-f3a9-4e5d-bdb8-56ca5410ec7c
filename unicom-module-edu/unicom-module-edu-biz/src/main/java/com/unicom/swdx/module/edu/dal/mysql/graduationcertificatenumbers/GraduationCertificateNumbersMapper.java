package com.unicom.swdx.module.edu.dal.mysql.graduationcertificatenumbers;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.graduationcertificatenumbers.GraduationCertificateNumbersDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface GraduationCertificateNumbersMapper extends BaseMapperX<GraduationCertificateNumbersDO> {

    /**
     * 学员结业证书编号 分页查询
     *
     * @param reqVO             请求参数
     * @param traineeStatusList 学员状态列表
     * @return 分页结果
     */
    List<GraduationCertificateNumbersPageRespVO> selectPage(Page<GraduationCertificateNumbersPageRespVO> page,
                                                            @Param("reqVO") GraduationCertificateNumbersPageReqVO reqVO,
                                                            @Param("traineeStatusList") List<Integer> traineeStatusList);

    default List<GraduationCertificateNumbersDO> selectListByTraineeIdList(List<Long> traineeIdList) {
        return selectList(new LambdaQueryWrapperX<GraduationCertificateNumbersDO>()
                .in(GraduationCertificateNumbersDO::getTraineeId, traineeIdList));
    }
}
