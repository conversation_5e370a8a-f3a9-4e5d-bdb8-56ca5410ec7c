package com.unicom.swdx.module.edu.utils.numberconverter;

import java.util.*;

/**
 * @ClassName: NumberConverter
 * @Author: lty
 * @Date: 2024/10/22 9:21
 */


public class NumberConverter {

    private static final Map<Character, Integer> chineseToArabicMap = new HashMap<>();
    private static final String[] chineseUnits = {"", "十", "百", "千", "万", "亿"};
    private static final String[] chineseDigits = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

    static {
        chineseToArabicMap.put('零', 0);
        chineseToArabicMap.put('一', 1);
        chineseToArabicMap.put('二', 2);
        chineseToArabicMap.put('三', 3);
        chineseToArabicMap.put('四', 4);
        chineseToArabicMap.put('五', 5);
        chineseToArabicMap.put('六', 6);
        chineseToArabicMap.put('七', 7);
        chineseToArabicMap.put('八', 8);
        chineseToArabicMap.put('九', 9);
    }

    /**
     * 将中文数字转换为阿拉伯数字
     * @param chineseNumber 中文数字字符串
     * @return 对应的阿拉伯数字
     */
    public static int chineseToArabic(String chineseNumber) {

        if ("十".equals(chineseNumber)){
            return 10;
        }

        int result = 0;
        int temp = 0;
        int unit = 1;
        boolean lastIsZero = false;

        for (int i = 0; i < chineseNumber.length(); i++) {
            char c = chineseNumber.charAt(i);
            if (chineseToArabicMap.containsKey(c)) {
                temp = chineseToArabicMap.get(c);
                lastIsZero = temp == 0;
            } else {
                int unitIndex = getUnitIndex(c);
                if (unitIndex == 4) { // 万
                    result += temp;
                    result *= 10000;
                } else if (unitIndex == 5) { // 亿
                    result += temp;
                    result *= 100000000;
                } else {
                    if (temp == 0 && result != 0) {
                        result += temp;
                    }
                    result += temp * Math.pow(10, unitIndex);
                }
                temp = 0;
            }
        }
        return result + temp;
    }

    /**
     * 将阿拉伯数字转换为中文数字
     * @param arabicNumber 阿拉伯数字
     * @return 对应的中文数字字符串
     */
    public static String arabicToChinese(int arabicNumber) {
        if (arabicNumber == 0) {
            return "零";
        }

        StringBuilder result = new StringBuilder();
        int unitIndex = 0;
        boolean lastIsZero = false;

        while (arabicNumber > 0) {
            int digit = arabicNumber % 10;
            if (digit != 0) {
                if (unitIndex >= chineseUnits.length) {
                    // 处理更大的单位
                    result.insert(0, "万");
                    unitIndex = 0;
                }
                result.insert(0, chineseUnits[unitIndex]);
                result.insert(0, chineseDigits[digit]);
                lastIsZero = false;
            } else {
                if (!lastIsZero && result.length() > 0 && result.charAt(0) != '零') {
                    result.insert(0, "零");
                }
                lastIsZero = true;
            }
            arabicNumber /= 10;
            unitIndex++;
        }
        if (result.toString().equals("一十")) {
            result = new StringBuilder("十");
        }

        return result.toString().replaceAll("零+$", "");
    }

    private static int getUnitIndex(char c) {
        for (int i = 0; i < chineseUnits.length; i++) {
            if (chineseUnits[i].equals(String.valueOf(c))) {
                return i;
            }
        }
        return -1;
    }

    public static void main(String[] args) {
        String chineseNumber = "一千二百三十四万五千六百七十八";
        int arabicNumber = chineseToArabic(chineseNumber);
        System.out.println("中文数字 " + chineseNumber + " 转换为阿拉伯数字: " + arabicNumber);

        int number = 12345678;
        String chinese = arabicToChinese(number);
        System.out.println("阿拉伯数字 " + number + " 转换为中文数字: " + chinese);
    }
}
