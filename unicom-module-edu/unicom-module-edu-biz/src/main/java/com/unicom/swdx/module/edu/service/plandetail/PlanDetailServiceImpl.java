package com.unicom.swdx.module.edu.service.plandetail;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.plandetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plandetail.PlanDetailDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.plandetail.PlanDetailConvert;
import com.unicom.swdx.module.edu.dal.mysql.plandetail.PlanDetailMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 教学计划详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlanDetailServiceImpl extends ServiceImpl<PlanDetailMapper, PlanDetailDO> implements PlanDetailService {

    @Resource
    private PlanDetailMapper planDetailMapper;

    @Override
    public Long createPlanDetail(PlanDetailCreateReqVO createReqVO) {
        // 插入
        PlanDetailDO planDetail = PlanDetailConvert.INSTANCE.convert(createReqVO);
        planDetailMapper.insert(planDetail);
        // 返回
        return planDetail.getId();
    }

    @Override
    public void updatePlanDetail(PlanDetailUpdateReqVO updateReqVO) {
        // 校验存在
        this.validatePlanDetailExists(updateReqVO.getId());
        // 更新
        PlanDetailDO updateObj = PlanDetailConvert.INSTANCE.convert(updateReqVO);
        planDetailMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanDetail(Long id) {
        // 校验存在
        this.validatePlanDetailExists(id);
        // 删除
        planDetailMapper.deleteById(id);
    }

    private void validatePlanDetailExists(Long id) {
        if (planDetailMapper.selectById(id) == null) {
            throw exception(PLAN_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public PlanDetailDO getPlanDetail(Long id) {
        return planDetailMapper.selectById(id);
    }

    @Override
    public List<PlanDetailDO> getPlanDetailList(Collection<Long> ids) {
        return planDetailMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PlanDetailDO> getPlanDetailPage(PlanDetailPageReqVO pageReqVO) {
        return planDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PlanDetailDO> getPlanDetailList(PlanDetailExportReqVO exportReqVO) {
        return planDetailMapper.selectList(exportReqVO);
    }

}
