package com.unicom.swdx.module.edu.controller.admin.rollcallrecord.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 学员点名签到记录 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class RollcallRecordBaseVO {

    @ApiModelProperty(value = "点名签到表ID", required = true)
    @NotNull(message = "点名签到表ID不能为空")
    private Long rollcallId;

    @ApiModelProperty(value = "学员ID", required = true)
    @NotNull(message = "学员ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员打卡状态 0-未到 1-已到", required = true)
    @NotNull(message = "学员打卡状态 0-未到 1-已到不能为空")
    private Integer status;

}
