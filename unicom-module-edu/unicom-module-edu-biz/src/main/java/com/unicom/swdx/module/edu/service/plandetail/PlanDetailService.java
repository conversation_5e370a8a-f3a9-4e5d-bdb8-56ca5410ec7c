package com.unicom.swdx.module.edu.service.plandetail;

import java.util.*;
import javax.validation.*;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.plandetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plandetail.PlanDetailDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 教学计划详情 Service 接口
 *
 * <AUTHOR>
 */
public interface PlanDetailService extends IService<PlanDetailDO> {

    /**
     * 创建教学计划详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanDetail(@Valid PlanDetailCreateReqVO createReqVO);

    /**
     * 更新教学计划详情
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanDetail(@Valid PlanDetailUpdateReqVO updateReqVO);

    /**
     * 删除教学计划详情
     *
     * @param id 编号
     */
    void deletePlanDetail(Long id);

    /**
     * 获得教学计划详情
     *
     * @param id 编号
     * @return 教学计划详情
     */
    PlanDetailDO getPlanDetail(Long id);

    /**
     * 获得教学计划详情列表
     *
     * @param ids 编号
     * @return 教学计划详情列表
     */
    List<PlanDetailDO> getPlanDetailList(Collection<Long> ids);

    /**
     * 获得教学计划详情分页
     *
     * @param pageReqVO 分页查询
     * @return 教学计划详情分页
     */
    PageResult<PlanDetailDO> getPlanDetailPage(PlanDetailPageReqVO pageReqVO);

    /**
     * 获得教学计划详情列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 教学计划详情列表
     */
    List<PlanDetailDO> getPlanDetailList(PlanDetailExportReqVO exportReqVO);

}
