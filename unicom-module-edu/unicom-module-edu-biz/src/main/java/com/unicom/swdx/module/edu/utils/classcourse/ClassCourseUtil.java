package com.unicom.swdx.module.edu.utils.classcourse;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourseteacher.ClassCourseTeacherMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: ClassCourseUtil
 * @Author: zhouhk
 * @Date: 2024/01/14 11:14
 */
@Component
public class ClassCourseUtil {

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private EvaluationDetailService evaluationDetailService;

    @Resource
    private ClassCourseTeacherMapper classCourseTeacherMapper;

    /**
     * 根据课表id删除关联的考勤、评课、教师关联数据
     * @param ccIds 课表主键列表
     */
    public void deleteClassCourseLinkInfo(List<Long> ccIds) {
        if(CollectionUtil.isNotEmpty(ccIds)){
            //删除关联的考勤
            LambdaQueryWrapper<ClockInInfoDO> clockInWrapper = new LambdaQueryWrapper<>();
            clockInWrapper.in(ClockInInfoDO::getClassCourseId, ccIds);
            clockInInfoMapper.delete(clockInWrapper);

            //删除关联的评课数据
            evaluationResponseService.deleteByClassCourseId(ccIds);
            evaluationDetailService.deleteByClassCourseId(ccIds);

            //删除关联的课程-授课教师数据
            LambdaQueryWrapper<ClassCourseTeacherDO> classCourseTeacherWrapper = new LambdaQueryWrapper<>();
            classCourseTeacherWrapper.in(ClassCourseTeacherDO::getClassCourseId, ccIds);
            classCourseTeacherMapper.delete(classCourseTeacherWrapper);
        }
    }
}
