package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程评价记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaluationResponsePageReqVO extends PageParam {

    @Schema(description = "评估问卷id", example = "4615")
    private Integer questionnaireId;

    @Schema(description = "评卷人id", example = "27859")
    private Integer studentId;

    @Schema(description = "发卷人id")
    private String issuer;

    @Schema(description = "发卷人部门id", example = "18040")
    private Integer deptId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "班级教师id", example = "9441")
    private String teacherId;

    @Schema(description = "排课id", example = "23817")
    private Integer classCourseId;

    @Schema(description = "班级id", example = "23817")
    private Long classId;

    @Schema(description = "总分")
    private BigDecimal score;

    @Schema(description = "评分等级")
    private String grade;

    @Schema(description = "是否评卷 （0 否， 1是）")
    private Boolean handle;

    @Schema(description = "是否已完成 （0 未完成， 1已完成）")
    private Boolean isDone;

    @Schema(description = "免评价类型（0 必须评价 ，1 请假了不用评）", example = "2")
    private Boolean remarktype;

    @ApiModelProperty(value = "是否部门评课", example = "教师")
    private Boolean department;

}