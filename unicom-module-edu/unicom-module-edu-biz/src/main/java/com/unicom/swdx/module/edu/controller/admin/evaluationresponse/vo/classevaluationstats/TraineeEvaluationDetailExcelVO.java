package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description: 班次评估统计-学员参评详情分页-导出 Excel VO
 * @date 2024-11-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class TraineeEvaluationDetailExcelVO {

    @ExcelProperty(value = "学员姓名")
    private String traineeName;

    @ExcelProperty(value = "应评次数")
    private Integer expectedCount;

    @ExcelProperty(value = "已评次数")
    private Integer actualCount;

    @ExcelProperty(value = "未评次数")
    private Integer notActualCount;

    @ExcelProperty(value = "参评率")
    private String ratioStr;
}
