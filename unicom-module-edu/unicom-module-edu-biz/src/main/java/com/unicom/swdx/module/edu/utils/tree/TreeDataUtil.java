package com.unicom.swdx.module.edu.utils.tree;

import com.unicom.swdx.module.edu.utils.tree.dto.TreeNodeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 字典树的一些相关工具
 * @date 2024-10-14
 */
@Slf4j
public class TreeDataUtil {

    // 分隔符
    public static final String SPLIT_STR = "/";

    // 根节点ID
    public static final Long ROOT_ID = 0L;

    /**
     * 获取当前节点的完整路径表示
     *
     * @param treeNodeDTOList 所有节点列表
     * @param treeNodeDTO     当前节点
     * @return 当前节点的完整路径表示 (祖宗/父节点/当前节点名)
     */
    public static String generateFullPathToCurrent(List<TreeNodeDTO> treeNodeDTOList, TreeNodeDTO treeNodeDTO) {
        return generateFullPathToCurrent(treeNodeDTOList, treeNodeDTO, ROOT_ID, SPLIT_STR);
    }

    /**
     * 获取当前节点的完整路径表示
     *
     * @param treeNodeDTOList 所有节点列表
     * @param treeNodeDTO     当前节点
     * @param rootId          根节点ID
     * @param separator       分隔符
     * @return 当前节点的完整路径表示 (祖宗/父节点/当前节点名)
     */
    public static String generateFullPathToCurrent(List<TreeNodeDTO> treeNodeDTOList, TreeNodeDTO treeNodeDTO, Long rootId, String separator) {
        if (Objects.isNull(treeNodeDTO)){
            return "";
        }
        if (Objects.isNull(treeNodeDTOList) || treeNodeDTOList.isEmpty()){
            return treeNodeDTO.getName();
        }
        TreeNodeDTO currentNode = treeNodeDTO;
        List<String> parentList = new ArrayList<>();
        for (int i = 0; i < treeNodeDTOList.size(); i++) {
            parentList.add(0, currentNode.getName());
            if (rootId.equals(currentNode.getParentId())) {
                break;
            }
            long parentId = currentNode.getParentId();
            List<TreeNodeDTO> findParentNodeList = treeNodeDTOList.stream()
                    .filter(node -> node.getId().equals(parentId)).collect(Collectors.toList());
            if (findParentNodeList.isEmpty()) {
                break;
            } else {
                currentNode = findParentNodeList.get(0);
            }
        }
        // parentList 使用/分隔符生成字符串
        return StringUtils.join(parentList, separator);
    }

    /**
     * 获取节点列表中所有节点的完整路径表示并以id->完整路径表示 的Map返回
     *
     * @param treeNodeDTOList 所有节点列表
     * @return id->完整路径表示 的Map
     */
    public static Map<Long, String> generateNodeIdToFullPathMap(List<TreeNodeDTO> treeNodeDTOList) {
        return generateNodeIdToFullPathMap(treeNodeDTOList, ROOT_ID, SPLIT_STR);
    }


    /**
     * 获取节点列表中所有节点的完整路径表示并以id->完整路径表示 的Map返回
     *
     * @param treeNodeDTOList 所有节点列表
     * @param rootId          根节点ID
     * @param separator       分隔符
     * @return id->完整路径表示 的Map
     */
    public static Map<Long, String> generateNodeIdToFullPathMap(List<TreeNodeDTO> treeNodeDTOList, Long rootId, String separator) {
        if (Objects.isNull(treeNodeDTOList) || treeNodeDTOList.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, String> resultMap = new HashMap<>();
        int maxSize = treeNodeDTOList.size();
        treeNodeDTOList.forEach(treeNodeDTO -> {
            TreeNodeDTO currentNode = treeNodeDTO;
            // 如果resultMap 中已有该节点父节点路径 则直接使用
            List<String> parentLabelList = new ArrayList<>();
            List<TreeNodeDTO> parentNodeList = new ArrayList<>();
            for (int i = 0; i < maxSize; i++) {
                // 如果resultMap 中已有该节点路径 则直接使用
                if (resultMap.containsKey(currentNode.getId())) {
                    parentLabelList.add(0, resultMap.get(currentNode.getId()));
                    parentNodeList.add(0, currentNode);
                    break;
                }else {
                    parentLabelList.add(0, currentNode.getName());
                    parentNodeList.add(0, currentNode);
                    // 如果是根节点 则跳出
                    if (rootId.equals(currentNode.getParentId())){
                        break;
                    }
                    if(currentNode.getParentId()!=null){
                        long parentId = currentNode.getParentId();
                        List<TreeNodeDTO> findParentNodeList = treeNodeDTOList.stream()
                                .filter(node -> node.getId().equals(parentId)).collect(Collectors.toList());
                        if (findParentNodeList.isEmpty()) {
                            break;
                        }
                        currentNode = findParentNodeList.get(0);
                    }
                }

            }
            // 生成该条路径中的所有节点的完整路径
            for (int i = 0; i < parentNodeList.size(); i++) {
                TreeNodeDTO node = parentNodeList.get(i);
                if (resultMap.containsKey(node.getId())) {
                    continue;
                }
                resultMap.put(node.getId(), StringUtils.join(parentLabelList.subList(0, i + 1), separator));
            }
        });
        return resultMap;
    }

    /**
     * 获取节点列表中所有节点的完整路径表示并以完整路径表示->id 的Map返回
     * 完整路径重名则取第一个
     * @param treeNodeDTOList 所有节点列表
     * @return 完整路径表示-> 的Map
     */
    public static Map<String, Long> generateFullPathToIdMap(List<TreeNodeDTO> treeNodeDTOList) {
        return generateFullPathToIdMap(treeNodeDTOList, ROOT_ID, SPLIT_STR);

    }

    /**
     * 获取节点列表中所有节点的完整路径表示并以完整路径表示->id 的Map返回
     * 完整路径重名则取第一个
     * @param treeNodeDTOList 所有节点列表
     * @param rootId          根节点ID
     * @param separator       分隔符
     * @return 完整路径表示-> 的Map
     */
    public static Map<String, Long> generateFullPathToIdMap(List<TreeNodeDTO> treeNodeDTOList, Long rootId, String separator) {
        Map<Long, String> idToFullPathMap = generateNodeIdToFullPathMap(treeNodeDTOList, rootId, separator);
        Map<String, Long> fullPathToIdMap = new HashMap<>();
        for (Map.Entry<Long, String> entry : idToFullPathMap.entrySet()) {
            if (!fullPathToIdMap.containsKey(entry.getValue())) {
                fullPathToIdMap.put(entry.getValue(), entry.getKey());
            }else {
                log.info("节点{}完整路径'{}'已存在，舍弃！", entry.getKey(), entry.getValue());
            }
        }
        return fullPathToIdMap;
    }

    /**
     * 获取节点列表中所有节点的完整路径排序后的列表
     *
     * @param treeNodeDTOList 所有节点列表
     * @return List<String> 所有节点的完整路径排序后结果列表
     */
    public static List<String> generateSortedFullPathList(List<TreeNodeDTO> treeNodeDTOList) {
        return generateSortedFullPathList(treeNodeDTOList, ROOT_ID, SPLIT_STR);
    }

    /**
     * 获取节点列表中所有节点的完整路径排序后的列表
     *
     * @param treeNodeDTOList 所有节点列表
     * @param rootId          根节点ID
     * @param separator       分隔符
     * @return List<String> 所有节点的完整路径排序后结果列表
     */
    public static List<String> generateSortedFullPathList(List<TreeNodeDTO> treeNodeDTOList, Long rootId, String separator) {
        Map<Long, String> longStringMap = generateNodeIdToFullPathMap(treeNodeDTOList, rootId, separator);
        // 获取所有字典标签列表 并排序
        return longStringMap.values().stream().sorted().collect(Collectors.toList());
    }

    public static void main(String[] args) {
        TreeNodeDTO d1 = new TreeNodeDTO();
        d1.setId(1L);
        d1.setParentId(0L);
        d1.setName("一级字典");

        TreeNodeDTO d2 = new TreeNodeDTO();
        d2.setId(2L);
        d2.setParentId(1L);
        d2.setName("二级字典");

        TreeNodeDTO d3 = new TreeNodeDTO();
        d3.setId(3L);
        d3.setParentId(2L);
        d3.setName("三级字典");

        TreeNodeDTO d4 = new TreeNodeDTO();
        d4.setId(4L);
        d4.setParentId(2L);
        d4.setName("三级字典2");

        TreeNodeDTO d5 = new TreeNodeDTO();
        d5.setId(5L);
        d5.setParentId(0L);
        d5.setName("2-1级字典");

        TreeNodeDTO d6 = new TreeNodeDTO();
        d6.setId(6L);
        d6.setParentId(5L);
        d6.setName("2-2级字典");

        TreeNodeDTO d7 = new TreeNodeDTO();
        d7.setId(7L);
        d7.setParentId(6L);
        d7.setName("2-3级字典");


        List<TreeNodeDTO> list = new ArrayList<>();
        list.add(d1);
        list.add(d2);
        list.add(d3);
        list.add(d6);
        list.add(d7);
        list.add(d4);
        list.add(d5);

        System.out.println(generateFullPathToCurrent(list, d1));
        System.out.println(generateFullPathToCurrent(list, d2));
        System.out.println(generateFullPathToCurrent(list, d3));
        System.out.println(generateFullPathToCurrent(list, d4));
        System.out.println(generateFullPathToCurrent(list, d5));
        System.out.println(generateFullPathToCurrent(list, d6));
        System.out.println(generateFullPathToCurrent(list, d7));
        System.out.println("22"+generateFullPathToCurrent(new ArrayList<>(), d7));
        System.out.println("33"+generateNodeIdToFullPathMap(new ArrayList<>()));
        System.out.println(generateNodeIdToFullPathMap(list));
        Map<Integer, Integer> integerHashMap = new HashMap<>();

        System.out.println(integerHashMap.get(1));

    }


}
