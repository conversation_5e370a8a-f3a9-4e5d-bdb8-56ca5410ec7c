package com.unicom.swdx.module.edu.service.courses;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.utils.excel.ExcelImportResultRespVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 课程库 Service 接口
 *
 * <AUTHOR>
 */
public interface CoursesService {

    /**
     * 创建课程库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCourses(@Valid CoursesCreateReqVO createReqVO);

    /**
     * 更新课程库
     *
     * @param updateReqVO 更新信息
     */
    void updateCourses(@Valid CoursesUpdateReqVO updateReqVO);

    /**
     * 删除课程库
     *
     * @param id 编号
     */
    void deleteCourses(Long id);

    /**
     * 导出选修课库模板
     *
     * @param request  请求
     * @param response 响应
     */
    void exportOptionalTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 导出专题库模板
     *
     * @param request  请求
     * @param response 响应
     */
    void exportTopicTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 导出教学活动模板
     * @param request 请求
     * @param response 响应
     */
    void exportActivityTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 获得课程库
     *
     * @param id 编号
     * @return 课程库
     */
    CoursesRespVO getCourses(Long id);

    /**
     * 获得课程库分页
     *
     * @param pageReqVO 分页查询
     * @return 课程库分页
     */
    PageResult<CoursesRespVO> getCoursesPage(CoursesPageReqVO pageReqVO, HttpServletRequest request);

    /**
     * 获得一个专题课的授课记录
     *
     * @param pageReqVO 查询条件
     * @return 授课记录分页
     */
    PageResult<CoursesTeachingRecordRespVO> getPageForTeachingRecord(CoursesTeachingRecordReqVO pageReqVO);

    /**
     * 导出一个专题课的授课记录
     *
     * @param pageReqVO 查询条件
     * @return 授课记录列表
     */
    List<CoursesTeachingRecordTopicExcelVO> getExportTopicTeachingRecordExcel(CoursesTeachingRecordReqVO pageReqVO);

    /**
     * 导出勾选下的多个专题课的授课记录
     *
     * @param pageVO 页面课程查询条件
     * @return 授课记录列表
     */
    List<CoursesTeachingRecordTopicExcelVO> getExportBatchTopicTeachingRecordExcel(CoursesPageReqVO pageVO);

    /**
     * 获得一个选修课的授课记录
     * @param pageVO 查询条件
     * @return 授课记录分页
     */
    PageResult<CoursesTeachingRecordRespVO> getPageForElectiveTeachingRecord(CoursesTeachingRecordReqVO pageVO);

    /**
     * 导出一个选修课的授课记录
     * @param pageVO 查询条件
     * @return 授课记录列表
     */
    List<CoursesTeachingRecordElectiveExcelVO> getExportElectiveTeachingRecordExcel(CoursesTeachingRecordReqVO pageVO);

    /**
     * 导出勾选下的多个专题课的授课记录
     *
     * @param pageVO 页面课程查询条件
     * @return 授课记录列表
     */
    List<CoursesTeachingRecordElectiveExcelVO> getExportBatchElectiveTeachingRecordExcel(CoursesPageReqVO pageVO);
    /**
     * 获得课程库列表
     *
     * @param request 请求
     * @param reqVO   筛选条件
     * @return 课程库列表
     */
    List<CoursesRespVO> getCoursesListByReqVO(HttpServletRequest request, CoursesPageReqVO reqVO);

    /**
     * 获得课程库列表
     *
     * @param ids id集合
     * @return 课程库列表
     */
    List<CoursesDO> getCoursesList(Collection<Long> ids);

    /**
     * 批量删除课程库 多选或条件筛选
     *
     * @param pageReqVO 筛选条件
     */
    void batchDeleteCourses(CoursesPageReqVO pageReqVO);

    /**
     * 导出课程库 Excel
     *
     * @param pageVO 查询条件
     * @return 导出数据
     */
    List<CoursesExcelVO> getExportCoursesExcel(CoursesPageReqVO pageVO, HttpServletRequest request);

    /**
     * 导出教学活动 Excel
     *
     * @param pageVO 查询条件
     * @return 导出数据
     */
    List<CoursesActivityExcelVO> getExportActivityCoursesExcel(CoursesPageReqVO pageVO, HttpServletRequest request);

    /**
     * 导入选修课
     *
     * @param request 请求
     * @param list    导入数据
     * @return 导入结果
     */
    ExcelImportResultRespVO importOptionalCourses(HttpServletRequest request, List<CoursesOptionalImportExcelVO> list) throws InterruptedException, IOException;

    /**
     * 导入专题课
     *
     * @param request 请求
     * @param list    导入数据
     * @return 导入结果
     */
    ExcelImportResultRespVO importTopicCourses(HttpServletRequest request, List<CoursesTopicImportExcelVO> list) throws InterruptedException, IOException;

    /**
     * 导入教学活动
     *
     * @param request 请求
     * @param list    导入数据
     * @return 导入结果
     */
    ExcelImportResultRespVO importActivityCourses(HttpServletRequest request, List<CoursesActivityImportResultExcelVO> list);
}
