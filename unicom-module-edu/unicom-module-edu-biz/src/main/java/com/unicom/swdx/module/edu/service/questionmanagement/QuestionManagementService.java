package com.unicom.swdx.module.edu.service.questionmanagement;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 题目管理 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionManagementService {

    /**
     * 创建题目管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionManagement(@Valid QuestionManagementSaveReqVO createReqVO);

    /**
     * 更新题目管理
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionManagement(@Valid QuestionManagementSaveReqVO updateReqVO);

    /**
     * 删除题目管理
     *
     * @param id 编号
     */
    void deleteQuestionManagement(Long id);

    /**
     * 获得题目管理
     *
     * @param id 编号
     * @return 题目管理
     */
    QuestionManagementDO getQuestionManagement(Long id);

    /**
     * 获得题目管理分页
     *
     * @param pageReqVO 分页查询
     * @return 题目管理分页
     */
    PageResult<QuestionManagementRespVO> getQuestionManagementPage(QuestionManagementPageReqVO pageReqVO);

    void batchDeleteQuestionManagement(List<Long> ids);
}