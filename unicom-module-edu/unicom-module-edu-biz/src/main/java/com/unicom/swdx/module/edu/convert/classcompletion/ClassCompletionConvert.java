package com.unicom.swdx.module.edu.convert.classcompletion;

import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.ClassCompletionInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.SaveClassCompletionInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.SchoolFeedbackFormRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface ClassCompletionConvert {

    ClassCompletionConvert INSTANCE = Mappers.getMapper(ClassCompletionConvert.class);

    List<ClassCompletionInfoRespVO> convertList(List<ClassCompletionDO> traineeInfoList);

    List<ClassCompletionDO> convertInfoList(List<SaveClassCompletionInfoReqVO> reqVOS);

    ClassCompletionDO convert(SaveClassCompletionInfoReqVO reqVO);
    List<SchoolFeedbackFormRespVO.TraineeInfoVO> convertTraineeInfoList(List<TraineeDO> traineeList );
}
