package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 教师授课记录分页 Request VO
 */
@ApiModel(value = "管理后台 - 教师授课记录分页 Request VO", description = "用于查询教师授课记录")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeachingRecordReqVO extends PageParam {

    @ApiModelProperty(value = "教师ID", example = "1", required = true)
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;

    @ApiModelProperty(value = "班次名称", example = "2024春季班")
    private String className;

    @ApiModelProperty(value = "课程名称", example = "Java基础")
    private String courseName;

    @ApiModelProperty(value = "授课开始时间", example = "2022-07-01 00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "授课结束时间", example = "2022-07-01 23:59:59")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "教学方式ID", example = "1")
    private Long educateFormId;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)", example = "[1, 2]")
    private Set<Integer> includeColumnIndexes;
}