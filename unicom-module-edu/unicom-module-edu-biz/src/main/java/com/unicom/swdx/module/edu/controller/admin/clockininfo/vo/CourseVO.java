package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: CourseVO
 * @Author: lty
 * @Date: 2024/11/13 9:46
 */
@Data
public class CourseVO {

    private Long id;
    @ApiModelProperty("课程id")
    private Long classCourseId;

    @ApiModelProperty(value = "类型 0-到课，1-就餐，2-住宿")
    private Integer type;

    @ApiModelProperty(value = "0-早餐，1-午餐，2-晚餐（就餐专属）")
    private Integer mealPeriod;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("学员状态")
    private String traineeStatus;

    @ApiModelProperty(value = "课程时段")
    private Integer period;

    private LocalDateTime createTime;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    private Boolean isCheck;
}
