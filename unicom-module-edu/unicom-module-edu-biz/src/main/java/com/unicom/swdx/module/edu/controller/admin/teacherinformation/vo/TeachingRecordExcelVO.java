package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 教师授课记录Excel导出 VO
 */
@Data
public class TeachingRecordExcelVO {

    @ExcelProperty("序号")
    private Long serialNumber;

    @ExcelProperty("课程名称")
    private String courseName;

    @ExcelProperty("班次名称")
    private String className;

    @ExcelProperty("授课教师")
    private String teacherName;

    @ExcelProperty("授课开始时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    @ExcelProperty("授课结束时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ExcelProperty("授课时间段")
    private String classduration;

    @ExcelProperty("教学方式")
    private String teachmethod;
}