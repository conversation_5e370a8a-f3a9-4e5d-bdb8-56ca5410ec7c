package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 下拉框师资信息
 * @date 2024-10-25
 */
@ApiModel("管理后台 - 下拉框师资信息Resp VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeacherInformationSimpleRespVO {

    @ApiModelProperty(value = "教师唯一标识", required = true)
    private Long id;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;

}
