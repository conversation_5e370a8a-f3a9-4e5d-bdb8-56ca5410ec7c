package com.unicom.swdx.module.edu.service.graduationcertificatenumbers;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.graduationcertificatenumbers.GraduationCertificateNumbersDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.graduationcertificatenumbers.GraduationCertificateNumbersMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.CLASS_MANAGEMENT_NOT_EXISTS;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * <AUTHOR> Zhe
 * @Description: 学员结业证书编号 Service 实现类
 * @date 2024-11-22
 */
@Service
@Validated
public class GraduationCertificateNumbersServiceImpl implements GraduationCertificateNumbersService {

    @Resource
    private GraduationCertificateNumbersMapper graduationCertificateNumbersMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    /**
     * 学员结业证书编号 分页查询
     *
     * @param reqVO 请求参数
     * @return 分页结果
     */
    @Override
    public PageResult<GraduationCertificateNumbersPageRespVO> selectPage(GraduationCertificateNumbersPageReqVO reqVO) {
        Page<GraduationCertificateNumbersPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        // 获取已报名、已报到、已结业的所有学员
        List<Integer> traineeStatusList = Arrays.asList(TraineeStatusEnum.REGISTERED.getStatus(),
                TraineeStatusEnum.REPORTED.getStatus(), TraineeStatusEnum.GRADUATED.getStatus());
        List<GraduationCertificateNumbersPageRespVO> list = graduationCertificateNumbersMapper.selectPage(page, reqVO, traineeStatusList);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(false,
                page.getTotal(),
                reqVO,
                list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 生成班级学员结业证书编号
     *
     * @param classId 班级ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateGraduationCertificateNumbers(Long classId) {
        ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classManagementDO)) {
            throw exception(CLASS_MANAGEMENT_NOT_EXISTS);
        }

        // 根据班级id 获取所有已报名、已报到、已结业的学员 并按分组排序
        // 获取已报名、已报到、已结业的所有学员
        List<Integer> traineeStatusList = Arrays.asList(TraineeStatusEnum.REGISTERED.getStatus(),
                TraineeStatusEnum.REPORTED.getStatus(), TraineeStatusEnum.GRADUATED.getStatus());
        List<AppTraineeGroupRespVO> traineeGroupRespVOList = traineeMapper.selectTraineeGroupListByClassIdAndStatus(classId, null, traineeStatusList);
        if (traineeGroupRespVOList.isEmpty()) {
            return;
        }
        List<Long> traineeIdList = traineeGroupRespVOList.stream().map(AppTraineeGroupRespVO::getId).collect(Collectors.toList());
        // 查询考勤编号表里面已存在的学员的编号记录
        List<GraduationCertificateNumbersDO> existGraduationCertificateNumbersDOList = graduationCertificateNumbersMapper.selectListByTraineeIdList(traineeIdList);
        Map<Long, GraduationCertificateNumbersDO> existTraineeIdToDoMap = existGraduationCertificateNumbersDOList.stream()
                .collect(Collectors.toMap(GraduationCertificateNumbersDO::getTraineeId, item -> item));

        // 需要删除的学员考勤编号
        List<Long> deleteIdList = existGraduationCertificateNumbersDOList.stream().filter(item -> !traineeIdList.contains(item.getTraineeId()))
                .map(GraduationCertificateNumbersDO::getId).collect(Collectors.toList());

        List<GraduationCertificateNumbersDO> batchUpdateList = new ArrayList<>();
        List<GraduationCertificateNumbersDO> batchInsertList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long loginUserId = getLoginUserId();
        for (int i = 0; i < traineeGroupRespVOList.size(); i++) {
            AppTraineeGroupRespVO appTraineeGroupRespVO = traineeGroupRespVOList.get(i);
            // 获取证书编号
            String certificateNumber = classManagementDO.getClassNameCode() + String.format("%02d", i + 1);
            if (existTraineeIdToDoMap.containsKey(appTraineeGroupRespVO.getId())) {
                GraduationCertificateNumbersDO existGraduationCertificateNumbersDO = existTraineeIdToDoMap.get(appTraineeGroupRespVO.getId());
                existGraduationCertificateNumbersDO.setCertificateNumber(certificateNumber);
                existGraduationCertificateNumbersDO.setUpdateTime(now);
                if (Objects.nonNull(loginUserId)) {
                    existGraduationCertificateNumbersDO.setUpdater(String.valueOf(loginUserId));
                }
                batchUpdateList.add(existGraduationCertificateNumbersDO);
            } else {
                GraduationCertificateNumbersDO graduationCertificateNumbersDO = GraduationCertificateNumbersDO.builder()
                        .traineeId(appTraineeGroupRespVO.getId())
                        .certificateNumber(certificateNumber).build();
                batchInsertList.add(graduationCertificateNumbersDO);
            }
        }
        if (!batchInsertList.isEmpty()) {
            graduationCertificateNumbersMapper.insertBatch(batchInsertList);
        }
        if (!batchUpdateList.isEmpty()) {
            graduationCertificateNumbersMapper.updateBatch(batchUpdateList);
        }
        if (!deleteIdList.isEmpty()) {
            graduationCertificateNumbersMapper.deleteBatchIds(deleteIdList);
        }
    }

    public static void main(String[] args) {
        List<Integer> list = Arrays.asList(1, 2, 3, 4);
        System.out.println(list.stream().map(item -> String.format("%02d", item)).collect(Collectors.toList()));
    }
}
