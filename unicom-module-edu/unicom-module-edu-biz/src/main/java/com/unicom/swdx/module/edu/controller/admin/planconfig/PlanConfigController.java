package com.unicom.swdx.module.edu.controller.admin.planconfig;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;
import com.unicom.swdx.module.edu.convert.planconfig.PlanConfigConvert;
import com.unicom.swdx.module.edu.service.planconfig.PlanConfigService;

@Api(tags = "管理后台 - 教学计划配置")
@RestController
@RequestMapping("/edu/plan-config")
@Validated
public class PlanConfigController {

    @Resource
    private PlanConfigService planConfigService;

    @PostMapping("/create")
    @ApiOperation("创建教学计划配置")
    @PreAuthorize("@ss.hasPermission('edu:plan-config:create')")
    public CommonResult<Long> createPlanConfig(@Valid @RequestBody PlanConfigCreateReqVO createReqVO) {
        return success(planConfigService.createPlanConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新教学计划配置")
    @PreAuthorize("@ss.hasPermission('edu:plan-config:update')")
    public CommonResult<Boolean> updatePlanConfig(@Valid @RequestBody PlanConfigUpdateReqVO updateReqVO) {
        planConfigService.updatePlanConfig(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除教学计划配置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-config:delete')")
    public CommonResult<Boolean> deletePlanConfig(@RequestParam("id") Long id) {
        planConfigService.deletePlanConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得教学计划配置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-config:query')")
    public CommonResult<PlanConfigRespVO> getPlanConfig(@RequestParam("id") Long id) {
        PlanConfigDO planConfig = planConfigService.getPlanConfig(id);
        return success(PlanConfigConvert.INSTANCE.convert(planConfig));
    }

    @GetMapping("/list")
    @ApiOperation("获得教学计划配置列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-config:query')")
    public CommonResult<List<PlanConfigRespVO>> getPlanConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<PlanConfigDO> list = planConfigService.getPlanConfigList(ids);
        return success(PlanConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得教学计划配置分页")
    @PreAuthorize("@ss.hasPermission('edu:plan-config:query')")
    public CommonResult<PageResult<PlanConfigRespVO>> getPlanConfigPage(@Valid PlanConfigPageReqVO pageVO) {
        PageResult<PlanConfigDO> pageResult = planConfigService.getPlanConfigPage(pageVO);
        return success(PlanConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出教学计划配置 Excel")
    @PreAuthorize("@ss.hasPermission('edu:plan-config:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanConfigExcel(@Valid PlanConfigExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PlanConfigDO> list = planConfigService.getPlanConfigList(exportReqVO);
        // 导出 Excel
        List<PlanConfigExcelVO> datas = PlanConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "教学计划配置.xls", "数据", PlanConfigExcelVO.class, datas);
    }

}
