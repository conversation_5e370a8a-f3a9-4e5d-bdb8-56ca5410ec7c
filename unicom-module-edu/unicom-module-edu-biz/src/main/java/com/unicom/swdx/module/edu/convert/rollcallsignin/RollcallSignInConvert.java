package com.unicom.swdx.module.edu.convert.rollcallsignin;

import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.dto.RollcallCommonLocationsAddDTO;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInRespVO;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 大课考勤、点名签到信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RollcallSignInConvert {

    RollcallSignInConvert INSTANCE = Mappers.getMapper(RollcallSignInConvert.class);

    RollcallSignInDO convert(RollcallSignInCreateReqVO bean);

    RollcallSignInDO convert(RollcallSignInUpdateReqVO bean);

    RollcallSignInRespVO convert(RollcallSignInDO bean);

    List<RollcallSignInRespVO> convertList(List<RollcallSignInDO> list);

    RollcallCommonLocationsAddDTO convert2(RollcallSignInDO rollCallSignInDO);
}
