package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class UserAppletTeachingMethodsVO {

    @ApiModelProperty(value = "教学方式字典ID", required = true)
    private String EDUCATE_FORM_ID;

    @ApiModelProperty(value = "教学方式名称", required = true)
    private String EDUCATE_FORM_NAME;


    @ApiModelProperty(value = "百分比", required = true)
    private BigDecimal prop;


    @ApiModelProperty(value = "工时", required = true)
    private Integer work_hour;

}
