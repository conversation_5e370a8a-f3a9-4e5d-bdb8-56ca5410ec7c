package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "批量设置分组VO")
public class BatchSetGroupReqVO {

    @ApiModelProperty(value = "分组id")
    private Long groupId;

    @ApiModelProperty(value = "学员id")
    private List<Long> ids;

}
