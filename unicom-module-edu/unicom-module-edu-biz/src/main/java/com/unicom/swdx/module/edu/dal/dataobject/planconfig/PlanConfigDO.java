package com.unicom.swdx.module.edu.dal.dataobject.planconfig;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 教学计划配置 DO
 *
 * <AUTHOR>
 */
@TableName("edu_plan_config")
@KeySequence("edu_plan_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanConfigDO extends BaseDO {

    /**
     * 唯一标识符，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 教学计划ID
     */
    private Long planId;
    /**
     * 星期几（如：1代表周一等）
     */
    private String dayOfWeek;
    /**
     * 时间段（0上午，1下午，2晚上）
     */
    private String period;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

}
