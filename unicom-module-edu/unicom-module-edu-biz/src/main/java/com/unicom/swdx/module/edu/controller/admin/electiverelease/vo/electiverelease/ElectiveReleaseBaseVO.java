package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.*;

/**
 * 选修课发布信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ElectiveReleaseBaseVO {

    @ApiModelProperty(value = "发布名称", required = true, example = "选修课发布")
    @NotNull(message = "发布名称不能为空")
    private String name;

    @ApiModelProperty(value = "选学开始时间", required = true, example = "2021-01-01 00:00:00")
    @NotNull(message = "选学开始时间不能为空")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime selectionStartTime;

    @ApiModelProperty(value = "选学结束时间 hh:mm", required = true, example = "2021-01-01 00:00:00")
    @NotNull(message = "选学结束时间不能为空")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime selectionEndTime;

    @ApiModelProperty(value = "上课日期", example = "2021-01-01")
    // @NotNull(message = "上课日期不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    // @NotNull(message = "午别时间不能为空")
    @Range(min = 0, max = 2, message = "午别时间不存在")
    private Integer dayPeriod;

    @ApiModelProperty(value = "上课时段开始时间", example = "00:00")
    // @NotNull(message = "上课时段开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE)
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课时段结束时间", example = "00:00")
    // @NotNull(message = "上课时段结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE)
    private String classEndTimeStr;

    /**
     * 上课开始时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classStartTime;

    /**
     * 上课结束时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classEndTime;
}
