package com.unicom.swdx.module.edu.controller.admin.classmanagement.excelimporthandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetClassTypeSheetWriteHandler implements SheetWriteHandler {

    @Resource
    private ClassManagementMapper classManagementMapper;

    private Integer num;

    public GetClassTypeSheetWriteHandler(ClassManagementMapper classManagementMapper,Integer num){

        this.classManagementMapper = classManagementMapper;
        this.num = num;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Map<Integer, String[]> mapDropDown = new HashMap<>();

        // 从数据库中获取数据
        List<ClassInfoImportDictLableExcelVO> label = classManagementMapper.getDictTypeByDictLabel(1 , SecurityFrameworkUtils.getTenantId());

        // 初始化 downArray，用于存储动态数据
        String[] downArray = new String[label.size()];

        // 遍历 label 列表，将数据填入 downArray
        for (int i = 0; i < label.size(); i++) {
            downArray[i] = label.get(i).getLabel(); // 假设 getClassName() 是获取类名的方法
        }


//        String[] downArray = {"厅级领导干部进修班", "中青年干部培训班", "处级干部进修班（含处级公务员进修班）",
//                "乡镇党委书记进修班（含向乡镇长班）","师资进修班","科技干部进修班（含科级公务员进修班）", "其它"};

        mapDropDown.put(num, downArray);
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper dvhelper = sheet.getDataValidationHelper();
        for (Map.Entry<Integer, String[]> entry : mapDropDown.entrySet()) {
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10001, entry.getKey(), entry.getKey());
            if (entry.getValue().length > 0) {
                DataValidationConstraint constraint = dvhelper.createExplicitListConstraint(entry.getValue());
                DataValidation dataValidation = dvhelper.createValidation(constraint, addressList);
                dataValidation.setSuppressDropDownArrow(true);
                dataValidation.setShowErrorBox(true);
                dataValidation.createErrorBox("提示", "此值与单元格定义格式不一致");
                dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                sheet.addValidationData(dataValidation);
            }
        }

    }
}
