package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 班次评估统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassEvaluationStatsPageReqVO extends PageParam {

    @ApiModelProperty(value = "班次名称", example = "陈平安")
    private String className;

    @ApiModelProperty(value = "班级状态，0-报名中，1-报名结束，2-开班中，3-已结束，4-未开始")
    private Integer classStatus;

    @ApiModelProperty(value = "校区")
    private Integer campus;

    @ApiModelProperty(value = "开班开始日期", example = "2023-01-01")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private String startTime;

    @ApiModelProperty(value = "开班结束日期", example = "2023-01-01")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private String endTime;

    @ApiModelProperty(value = "是否是班主任(默认是)", example = "true")
    private Boolean isHeadTeacher;

    @ApiModelProperty(value = "多选id（导出时使用）")
    private List<Long> ids;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "排序字段(默认按教师姓名) 0-班次排序（默认） 1-班次编码 2-按班次名称 3-按开班日期 4-按平均参评率 5-按平均分")
    @Range(min = 0, max = 6, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序排列(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

    @ApiModelProperty(value ="系统使用-查询时限制班级查看范围", hidden = true)
    private List<Long> classIdList;
}