package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeacherInformationExcelVO {

    @ExcelProperty("姓名")
    private String name;
    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSON_GENDER)
    private Integer gender;

    @ExcelProperty(value = "联系方式")
    private String contactInformation;

    @ExcelProperty(value = "所属部门")
    private String deptNames;

    @ExcelProperty(value = "所在单位")
    private String workUnit;
}
