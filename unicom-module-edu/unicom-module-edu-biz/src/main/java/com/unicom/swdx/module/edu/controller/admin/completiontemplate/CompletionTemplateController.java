package com.unicom.swdx.module.edu.controller.admin.completiontemplate;

import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExcelVO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.convert.completiontemplate.CompletionTemplateConvert;
import com.unicom.swdx.module.edu.service.completiontemplate.CompletionTemplateService;

@Api(tags = "管理后台 - 结业考核模版设置")
@RestController
@RequestMapping("/edu/completion-template")
@Validated
public class CompletionTemplateController {

    @Resource
    private CompletionTemplateService completionTemplateService;

//    @PostMapping("/create")
//    @ApiOperation("创建结业考核模版设置")
//    @PreAuthorize("@ss.hasPermission('edu:completion-template:create')")
//    public CommonResult<Integer> createCompletionTemplate(@Valid @RequestBody CompletionTemplateCreateReqVO createReqVO) {
//        return success(completionTemplateService.createCompletionTemplate(createReqVO));
//    }

    @PostMapping("/add")
    @ApiOperation("新增模版")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:create')")
    public CommonResult<List<Integer>> addCompletionTemplate(@Valid @RequestBody List<CompletionTemplateCreateReqVO> addReqVO) {
        return success(completionTemplateService.addCompletionTemplate(addReqVO));
    }

//    @PostMapping("/update")
//    @ApiOperation("更新结业考核模版设置")
//    @PreAuthorize("@ss.hasPermission('edu:completion-template:update')")
//    public CommonResult<Boolean> updateCompletionTemplate(@Valid @RequestBody CompletionTemplateUpdateReqVO updateReqVO) {
//        completionTemplateService.updateCompletionTemplate(updateReqVO);
//        return success(true);
//    }

    @PostMapping("/edit")
    @ApiOperation("编辑数据")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:update')")
    public CommonResult<Boolean> updateCompletionTemplateData(@Valid @RequestBody List<CompletionTemplateUpdateReqVO> editReqVO) {
        completionTemplateService.updateCompletionTemplateData(editReqVO);
        return success(true);
    }

//    @PostMapping("/delete")
//    @ApiOperation("删除结业考核模版设置")
//    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
//    @PreAuthorize("@ss.hasPermission('edu:completion-template:delete')")
//    public CommonResult<Boolean> deleteCompletionTemplate(@RequestParam("id") Integer id) {
//        completionTemplateService.deleteCompletionTemplate(id);
//        return success(true);
//    }


    @PostMapping("/deleteByIdCode")
    @ApiOperation("根据模版编码删除模版")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:delete')")
    public CommonResult<Boolean> deleteCompletionTemplateByName(@RequestParam("idCode") String idCode) {
        completionTemplateService.deleteCompletionTemplateByName(idCode);
        return success(true);
    }

    @PostMapping("/deleteByIdCodeBatch")
    @ApiOperation("根据模版编码批量删除模版")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:delete')")
    public CommonResult<Boolean> deleteCompletionTemplateByNameBatch(@RequestParam("idCode") List<String> idCode) {
        completionTemplateService.deleteCompletionTemplateByNameBatch(idCode);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得结业考核模版设置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<CompletionTemplateRespVO> getCompletionTemplate(@RequestParam("id") Integer id) {
        CompletionTemplateDO completionTemplate = completionTemplateService.getCompletionTemplate(id);
        return success(CompletionTemplateConvert.INSTANCE.convert(completionTemplate));
    }

    @GetMapping("/list")
    @ApiOperation("根据模版编码获取数据")
   @ApiImplicitParam(name = "idCode", value = "id编号", required = true, example = "2048", dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<List<CompletionTemplateRespVO>> getCompletionTemplateList(@RequestParam("idCode") String idCode) {
        List<CompletionTemplateDO> list = completionTemplateService.getCompletionTemplateList(idCode);
        return success(CompletionTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得结业考核模版设置分页")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<PageResult<CompletionTemplateRespVO>> getCompletionTemplatePage(HttpServletRequest request,@Valid CompletionTemplatePageReqVO pageVO) {
        PageResult<CompletionTemplateDO> pageResult = completionTemplateService.getCompletionTemplatePage(request,pageVO);
        return success(CompletionTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-all")
    @ApiOperation("获得所有模版数据")
//    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<List<CompletionTemplateDO>> getCompletionTemplatePageList(HttpServletRequest request,@Valid CompletionTemplatePageReqVO pageVO) {
        List<CompletionTemplateDO> list = completionTemplateService.getCompletionTemplatePageList(request,pageVO);
        return success(list);
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出结业考核模版设置 Excel")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:export')")
    public void exportCompletionTemplateExcel(@Valid CompletionTemplateExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<CompletionTempleExcelVO> list = completionTemplateService.getCompletionTemplateListExport(exportReqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "模版列表.xls",
                "数据", CompletionTempleExcelVO.class, list, exportReqVO.getIncludeColumnIndexes());
    }



    @ApiOperation("下载模板")
    @GetMapping("/get-template")
    public void getTemplate(HttpServletRequest request,Integer classId,HttpServletResponse response) throws IOException {
        completionTemplateService.exportExcel(request,classId,0,response);
    }

    @GetMapping("/getClassCompletionTitle")
    @ApiOperation("表头")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<ClassCompletionRespVO> getClassCompletion(Long classId) {
        ClassCompletionRespVO respVO = completionTemplateService.getClassCompletion(classId);
        return success(respVO);
    }


    @GetMapping("/regenerate")
    @ApiOperation("重新生成")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<Boolean> regenerate(Long classId) {
        completionTemplateService.regenerate(classId);
        return success(true);
    }


    @GetMapping("/getClassCompletionInfo")
    @ApiOperation("数据")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<List<ClassCompletionInfoRespVO>> getClassCompletionInfo(HttpServletRequest request,Integer classId) {
        List<ClassCompletionInfoRespVO> list = completionTemplateService.getClassCompletionInfo(request,classId);
        return success(list);
    }

    @ApiOperation("导出")
    @GetMapping("/export")
    public void export(HttpServletRequest request,Integer classId,HttpServletResponse response) throws IOException {
        completionTemplateService.exportExcel(request,classId,1,response);
    }

    @PostMapping("/saveInfo")
    @ApiOperation("保存数据")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<Boolean> saveInfo(@RequestBody List<SaveClassCompletionInfoReqVO> reqVOs) {
        completionTemplateService.saveInfo(reqVOs);
        return success(true);
    }

    @GetMapping("/generate")
    @ApiOperation("生成内置模板")
    @Cacheable(cacheNames = "cache:innertemplate")
    public CommonResult<Boolean> generationTemplate() {
        Boolean result = completionTemplateService.generationTemplate();
        return success(result);
    }

    @GetMapping("/schoolFeedbackForm")
    @ApiOperation("在校反馈表")
    public CommonResult<SchoolFeedbackFormRespVO> schoolFeedbackForm(Long classId) {
        return success(completionTemplateService.schoolFeedbackForm(classId));
    }

    @GetMapping("/clear")
    @ApiOperation("清除缓存")
    @CacheEvict(cacheNames = "cache:innertemplate")
    public CommonResult<Boolean> clearCache() {
        System.out.println("缓存已经清除");
        return success(true);
    }
}
