package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeInfoPageRespVO {

    private Integer index;

    @ApiModelProperty(value = "学员id")
    private String id;

    @ApiModelProperty(value = "学员姓名")
    private String name;

    @ApiModelProperty(value = "学员性别")
    private String sex;

    @ApiModelProperty(value = "学员手机号")
    private String phone;

    @ApiModelProperty(value = "文化程度")
    private Integer educationalLevel;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "职级")
    private Integer jobLevel;

    @ApiModelProperty(value = "政治面貌")
    private Integer politicalIdentity;

    @ApiModelProperty(value = "民族")
    private Integer ethnic;
}
