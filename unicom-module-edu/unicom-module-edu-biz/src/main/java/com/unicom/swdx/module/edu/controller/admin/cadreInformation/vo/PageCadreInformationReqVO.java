package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class PageCadreInformationReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "单位id", required = true)
    private Long unitId;

    private List<Long> idList;
}
