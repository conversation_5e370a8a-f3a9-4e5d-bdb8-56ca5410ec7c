package com.unicom.swdx.module.edu.controller.admin.users.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户信息新增/修改 Request VO")
@Data
public class UsersSaveReqVO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户账号")
    @NotEmpty(message = "用户账号不能为空")
    private String username;

    @Schema(description = "密码")
    @NotEmpty(message = "密码不能为空")
    private String password;

    @Schema(description = "用户昵称")
    @NotEmpty(message = "用户昵称不能为空")
    private String nickname;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "部门ID", example = "9278")
    private Long deptId;

    @Schema(description = "岗位编号数组")
    private String postIds;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "用户性别")
    private Integer sex;

    @Schema(description = "头像地址")
    private String avatar;

    @Schema(description = "帐号状态（0正常 1停用）")
    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    private LocalDateTime loginDate;

}
