package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel(value = "管理后台 - EduNoticeAnnouncement Excel 导出 Request VO", description = "参数和 NoticeAnnouncementPageReqVO 是一致的")
@Data
public class NoticeAnnouncementExportVO {

    @ApiModelProperty(value = "发布人")
    private String publisher;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "通知公告内容")
    private String content;

    @ApiModelProperty(value = "是否置顶,1-是，0-否")
    private Integer isTop;

    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "1-发布，2-存草稿箱")
    @NotNull(message = "isPublish不能为空")
    private Integer isPublish;

    @ApiModelProperty(value = "状态，1-上架，0-下架")
    private Integer status;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "0-升序，1-降序")
    @NotNull(message = "change不能为空")
    private Integer change;

    @ApiModelProperty(value = "1-标题，2-发布时间")
    @NotNull(message = "tag不能为空")
    private Integer tag;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "id列表")
    private List<Long> idList;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "截止时间")
    private String endTime;

    @ApiModelProperty(value = "系统模块：1-学员管理系统 ，2-教务管理系统/参训系统")
    private Integer moduleCode;

    @ApiModelProperty(value = "发布人id")
    private Long publishId;
}
