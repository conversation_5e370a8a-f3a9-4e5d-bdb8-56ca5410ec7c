package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel("业中首页-仪表盘-排名显示 Request VO")
@Data
public class AttendanceDetailsVO {
    @ApiModelProperty(value = "考勤类型 0-到课 1-就餐 2-住宿", example = "0")
    private Integer type;

    @ApiModelProperty(value = "应到人数", example = "1")
    private Integer attendanceExpected;

    @ApiModelProperty(value = "实到人数", example = "1")
    private Integer attendanceActual;

    @ApiModelProperty(value = "未到人数", example = "1")
    private Integer attendanceAbsent;

    @ApiModelProperty(value = "请假人数", example = "1")
    private Integer attendanceLeave;

    @ApiModelProperty(value = "平均到率 （实到/应到）", example = "80.1")
    private String averageRate;
}
