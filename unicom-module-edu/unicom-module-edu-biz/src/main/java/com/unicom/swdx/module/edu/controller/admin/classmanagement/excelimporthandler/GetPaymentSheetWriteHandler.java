package com.unicom.swdx.module.edu.controller.admin.classmanagement.excelimporthandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.HashMap;
import java.util.Map;

public class GetPaymentSheetWriteHandler implements SheetWriteHandler {

    private Integer num;
    public GetPaymentSheetWriteHandler(Integer num){
        this.num = num;
    }
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Map<Integer, String[]> mapDropDown = new HashMap<>();
        String[] downArray = {"是","否"};
        mapDropDown.put(num, downArray);
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper dvhelper = sheet.getDataValidationHelper();
        for (Map.Entry<Integer, String[]> entry : mapDropDown.entrySet()) {
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10001, entry.getKey(), entry.getKey());
            if (entry.getValue().length > 0) {
                DataValidationConstraint constraint = dvhelper.createExplicitListConstraint(entry.getValue());
                DataValidation dataValidation = dvhelper.createValidation(constraint, addressList);
                dataValidation.setSuppressDropDownArrow(true);
                dataValidation.setShowErrorBox(true);
                dataValidation.createErrorBox("提示", "此值与单元格定义格式不一致");
                dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                sheet.addValidationData(dataValidation);
            }
        }

    }
}
