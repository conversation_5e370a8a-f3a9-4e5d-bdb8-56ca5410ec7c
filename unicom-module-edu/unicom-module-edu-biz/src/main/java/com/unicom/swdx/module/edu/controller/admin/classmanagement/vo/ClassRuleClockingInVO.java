package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
public class ClassRuleClockingInVO {

    @ApiModelProperty(value = "到课考勤id")
    private Long attendanceCheck;

    @ApiModelProperty(value = "就餐考勤id")
    private Long mealAttendance;

    @ApiModelProperty(value = "住宿考勤id")
    private Long checkIn;

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

}
