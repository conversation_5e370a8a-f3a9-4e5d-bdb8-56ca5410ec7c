package com.unicom.swdx.module.edu.controller.admin.classcourse;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementRespVO;
import com.unicom.swdx.module.edu.convert.classcourse.ClassCourseConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 班级课程安排")
@RestController
@RequestMapping("/edu/class-course")
@Validated
public class ClassCourseController {

    @Resource
    private ClassCourseService classCourseService;

    @PostMapping("/create")
    @ApiOperation("创建班级课程安排")
    @PreAuthorize("@ss.hasPermission('edu:class-course:create')")
    public CommonResult<Long> createClassCourse(@Valid @RequestBody ClassCourseCreateReqVO createReqVO) {
        return success(classCourseService.createClassCourse(createReqVO));
    }

    @PostMapping("/createBatch")
    @ApiOperation("批量创建班级课程安排")
    @PreAuthorize("@ss.hasPermission('edu:class-course:create')")
    public CommonResult<Boolean> createBatch(@RequestParam("classCourseId") Long classCourseId , @Valid @RequestBody List<ClassCourseCreateReqVO> createReqVOList) {
        classCourseService.createBatch(classCourseId , createReqVOList);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation("更新班级课程安排")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<Boolean> updateClassCourse(@Valid @RequestBody ClassCourseUpdateReqVO updateReqVO) {
        classCourseService.updateClassCourse(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateBatch")
    @ApiOperation("教学计划详情-批量更新或新增")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<Boolean> updateBatch(@RequestBody List<ClassCourseUpdateReqVO> updateReqVOList) {
        classCourseService.updateBatch(updateReqVOList);
        return success(true);
    }

    @PostMapping("/updateTableBatch")
    @ApiOperation("课程表-批量排课")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<Boolean> updateTableBatch(@RequestParam("status") String status, @RequestBody List<ClassCourseUpdateReqVO> updateReqVOList) {
        classCourseService.updateTableBatch(status, updateReqVOList);
        return success(true);
    }

    @PostMapping("/undoPublish")
    @ApiOperation("课程表-撤销发布")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<Boolean> undoPublish(@RequestParam("classId") Long classId) {
        classCourseService.undoPublish(classId);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除班级课程安排")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course:delete')")
    public CommonResult<Boolean> deleteClassCourse(@RequestParam("id") Long id) {
        classCourseService.deleteClassCourse(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得班级课程安排")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<ClassCourseRespVO> getClassCourse(@RequestParam("id") Long id) {
        ClassCourseRespVO respVO = classCourseService.getClassCourse(id);
        return success(respVO);
    }

    @GetMapping("/getStat")
    @ApiOperation("教学形式统计")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<StatVO>> getStat(@Valid ClassCoursePageReqVO pageVO) {
        List<StatVO> statVOList = classCourseService.getStat(pageVO);
        return success(statVOList);
    }

    @GetMapping("/list")
    @ApiOperation("获得班级课程安排列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassCourseRespVO>> getClassCourseList(@RequestParam("ids") Collection<Long> ids) {
        List<ClassCourseDO> list = classCourseService.getClassCourseList(ids);
        return success(ClassCourseConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得班级课程安排分页")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<PageResult<ClassCourseRespVO>> getClassCoursePage(@Valid ClassCoursePageReqVO pageVO) {
        PageResult<ClassCourseDO> pageResult = classCourseService.getClassCoursePage(pageVO);
        return success(ClassCourseConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/pageByClassManagement")
    @ApiOperation("全校课程获得按照班级课程安排分页-用于排课")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassCourseByClassManagementRespVO>> getClassCoursePageByClassManagement(@Valid ClassCoursePageReqVO pageVO) {
        List<ClassCourseByClassManagementRespVO> resultList = classCourseService.getClassCoursePageByClassManagement(pageVO);
        return success(resultList);
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出班级课程安排 Excel")
    @PreAuthorize("@ss.hasPermission('edu:class-course:export')")
    @OperateLog(type = EXPORT)
    public void exportClassCourseExcel(@Valid ClassCourseExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<ClassCourseDO> list = classCourseService.getClassCourseList(exportReqVO);
        // 导出 Excel
        List<ClassCourseExcelVO> datas = ClassCourseConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "班级课程安排.xls", "数据", ClassCourseExcelVO.class, datas);
    }

    @GetMapping("/getVacation")
    @ApiOperation("获取法定节假日")
    public CommonResult<Set<String>> getVacation(@RequestParam("year") Integer year) {
        // todo 常量规范
        Integer month = 0;
        Set<String> result = classCourseService.getVacation(year, month);
        return success(result);
    }

    @GetMapping("/my-class-schedule")
    @ApiOperation("小程序我的课表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ScheduleChartVO>> myClassSchedule(@Valid MyClassScheduleParamsVO myClassScheduleParamsVO) {
        List<ScheduleChartVO> list = classCourseService.getMyClassSchedule(myClassScheduleParamsVO);
        return success(list);
    }

    @GetMapping("/getTimeTable")
    @ApiOperation("班主任获取课表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<ClassTableRespVO> getTimeTable(ClassTimeTableReqVO reqVO) {
        ClassTableRespVO respVO = classCourseService.getTimeTable(reqVO);
        return success(respVO);
    }

    @GetMapping("/export-timeTable")
    @ApiOperation("班主任导出课表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public void exportTimeTable(ClassTimeTableReqVO reqVO, HttpServletResponse response) throws IOException {
        List<ClassTimeTableRespVO> list = classCourseService.getTimeTable(reqVO).getCourse();
        Workbook workbook = classCourseService.exportTimeTable(reqVO, list, response);
        OutputStream out = null;
        try {
            // 设置字符编码的格式
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String formatTime = today.format(formatter);
            String fileName = "name" + formatTime + ".xlsx";
            // 对文件名进行 URL 编码，以便在响应头中传递文件名
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            // 设定输出文件头
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            // 定义输出类型
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败！");
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    @GetMapping("/getTimeTableAll")
    @ApiOperation("获取全校课表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassTableAllRespVO>> getTimeTableAll(ClassTimeTableReqVO reqVO) {
        List<ClassTableAllRespVO> respVO = classCourseService.getTimeTableAll(reqVO);
        return success(respVO);
    }

    @GetMapping("/getOrderClassIdList")
    @ApiOperation("获取全校课表班级次序")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<Long>> getTimeTableAllOrderClassIdList(ClassTimeTableReqVO reqVO) {
        List<Long> timeTableAllOrderClassIdList = classCourseService.getTimeTableAllOrderClassIdList(reqVO);
        return success(timeTableAllOrderClassIdList);
    }

    @PostMapping("/updateOrderClassIdList")
    @ApiOperation("更新全校课表班级次序")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<Boolean> updateTimeTableAllOrderClassIdList(@Valid @RequestBody ClassTimeTableReqUpdateVO classOrder) {
        return success(classCourseService.updateTimeTableAllOrderClassIdList(classOrder.getClassOrder()));
    }

    @GetMapping("/getTimeTableAll-applet")
    @ApiOperation("获取全校课表业中小程序")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    @PermitAll
    public CommonResult<Set<Map.Entry<String, Map<String, List<UserAppletCourseResponseVO.Data.Sql1>>>>> getTimeTableAllApplet(ClassTimeTableReqVO reqVO) {
        Set<Map.Entry<String, Map<String, List<UserAppletCourseResponseVO.Data.Sql1>>>> respVO = classCourseService.getTimeTableAllApplet(reqVO);
        return success(respVO);
    }


    @GetMapping("/getMergedClassList")
    @ApiOperation("获取一个排课的合班授课的班级列表")
    @ApiImplicitParam(name = "classCourseId", value = "排课编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassInfoRespVO>> getMergedClassList(@RequestParam("classCourseId") Long classCourseId){
        List<ClassInfoRespVO> respVOList = classCourseService.getMergedClassList(classCourseId);
        return success(respVOList);
    }

    @PostMapping("/MergedClassListByReqVO")
    @ApiOperation("获取一个排课时间段的合班授课班级列表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassInfoRespVO>> MergedClassListByTime(@RequestBody @Valid ClassMergeReqVO reqVO){
        List<ClassInfoRespVO> respVOList = classCourseService.getMergedClassListByReqVO(reqVO);
        return success(respVOList);
    }

    @GetMapping("/export-timeTableAll")
    @ApiOperation("导出全校课表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public void exportTimeTableAll(ClassTimeTableReqVO reqVO, HttpServletResponse response) throws IOException {
        List<ClassTableAllRespVO> list = classCourseService.getTimeTableAll(reqVO);
        Workbook workbook = classCourseService.exportTimeTableAll(reqVO, list, response);
        OutputStream out = null;
        try {
            // 设置字符编码的格式
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String formatTime = today.format(formatter);
            String fileName = "name" + formatTime + ".xlsx";
            // 对文件名进行 URL 编码，以便在响应头中传递文件名
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            // 设定输出文件头
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            // 定义输出类型
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败！");
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    @PostMapping("/simple-list")
    @ApiOperation("获取发布课表下拉列表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassCourseSimpleRespVO>> getSimpleList(@Valid @RequestBody ClassCourseSimpleReqVO reqVO) {
        List<ClassCourseSimpleRespVO> list = classCourseService.getSimpleList(reqVO);
        return success(list);
    }


    @GetMapping("/class-leader-schedule")
    @ApiOperation("小程序班主任课表")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ScheduleChartVO>> classLeaderSchedule(@Valid MyClassScheduleParamsVO myClassScheduleParamsVO) {
        List<ScheduleChartVO> list = classCourseService.getClassLeaderSchedule(myClassScheduleParamsVO);
        return success(list);
    }

    @PostMapping("/update-clocking-in")
    @ApiOperation("课程考勤开关")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<Boolean> updateClassCourseClockingIn(@Valid @RequestBody ClassCourseClockingInVO clockingInVO) {
        classCourseService.updateClassCourseByClockingIn(clockingInVO);
        return success(true);
    }

    @GetMapping("/getClassTimeByPeriodAndDate")
    @ApiOperation("根据午别和日期获取选修课上课时间段")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dayPeriod", value = "午别", required = true, dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "classDate", value = "上课日期", required = true, dataTypeClass = String.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<String>> getClassTimeByPeriodAndDate(@RequestParam("dayPeriod") Integer dayPeriod,
                                                                  @RequestParam("classDate") String classDate) {
        List<String> list = classCourseService.getClassTimeByPeriodAndDate(dayPeriod, classDate);
        return success(list);
    }


    @GetMapping("/teacherId")
    @ApiOperation("根据传入的userId返回teacherId")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<Long> classLeaderSchedule(@RequestParam("userId") Long userId) {
        Long teacherId = classCourseService.getTeacherIdByUserId(userId);
        return success(teacherId);
    }

    @GetMapping("/getLatestDate")
    @ApiOperation("获得班级课程最后一节课的排课日期，用于调课时快速定位")
    @ApiImplicitParam(name = "classId", value = "班级id", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<String> getLatestDate(@RequestParam("classId") Long classId) {
        return success(classCourseService.getLatestDate(classId));
    }

    @PostMapping("/updateMerge")
    @ApiOperation("修改合班授课绑定关系")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<Boolean> updateClassCourseBinding(@Valid @RequestBody ClassCourseBindingUpdateReqVO classCourseBindingUpdateReqVO) {
        classCourseService.updateClassCourseBinding(classCourseBindingUpdateReqVO);
        return success(true);
    }

    @PostMapping("/mergeInfo")
    @ApiOperation("判断合班授课是否可进行，是否能合班，若不能则将冲突班级返回")
    @PreAuthorize("@ss.hasPermission('edu:class-course:update')")
    public CommonResult<List<ClassCourseBindingInfoRespVO>> getMergeInfo(@Valid @RequestBody List<ClassCourseBindingUpdateReqVO> classCourseBindingUpdateReqVOList) {
        List<ClassCourseBindingInfoRespVO> list = classCourseService.getMergeInfo(classCourseBindingUpdateReqVOList);
        return success(list);
    }

    @GetMapping("/classIdMergeSelect")
    @ApiOperation("获得合班授课时可选的班级信息")
    @PreAuthorize("@ss.hasPermission('edu:class-course:query')")
    public CommonResult<List<ClassManagementRespVO>> getClassIdMergeSelect(@RequestParam("id") Long ClassCourseId) {
        return success(classCourseService.getClassIdMergeSelect(ClassCourseId));
    }

}

