package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - EduClassroomLibrary更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassroomLibraryUpdateReqVO extends ClassroomLibraryBaseVO {

    @ApiModelProperty(value = "主键id,自增", required = true)
    @NotNull(message = "主键id,自增不能为空")
    private Integer id;

}
