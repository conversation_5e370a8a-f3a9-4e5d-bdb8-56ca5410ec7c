package com.unicom.swdx.module.edu.dal.dataobject.traineeleave;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "edu_trainee_leave",autoResultMap = true)
@KeySequence("edu_trainee_leave_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraineeLeaveDO extends TenantBaseDO {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    // 学员用户id
    private Long traineeUserId;

    // 学员id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    // 所在班级id
    private Long classId;

    // 请假类型
    private Integer leaveType;

    // 原因
    private String reason;

    // 开始时间
    private LocalDateTime startTime;

    // 结束时间
    private LocalDateTime endTime;

    // 天数
    private Float days;

    // 标题
    private String title;

    // 附件
    private String accessory;

    // 请假流程的状态
    private Integer status;

    // 发起时间
    private LocalDateTime applyTime;

}
