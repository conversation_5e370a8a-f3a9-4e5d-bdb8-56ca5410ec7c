package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - EduClassroomLibrary更新 Request VO")
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class ClassManagerDeleteVO {

    @ApiModelProperty(value = "批量删除", required = true)
    private String ids;

    @ApiModelProperty(value = "批量发布，1-批量发布，2-批量撤销发布", required = true)
    private Integer isPublish;

}
