package com.unicom.swdx.module.edu.controller.admin.plantemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 教学计划模版更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanTemplateUpdateReqVO extends PlanTemplateBaseVO {

    @ApiModelProperty(value = "唯一标识符，自增", required = true)
    @NotNull(message = "唯一标识符，自增不能为空")
    private Long id;

}
