package com.unicom.swdx.module.edu.dal.mysql.classcompletiontemplate;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.enums.classcompletion.CompletionSaveEnum;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 结业考核模版设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCompletionTemplateMapper extends BaseMapperX<ClassCompletionTemplateDO> {

    default PageResult<ClassCompletionTemplateDO> selectPage(ClassCompletionTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ClassCompletionTemplateDO>()
                .eqIfPresent(ClassCompletionTemplateDO::getSerialNumber, reqVO.getSerialNumber())
                .likeIfPresent(ClassCompletionTemplateDO::getColumnName, reqVO.getColumnName())
                .eqIfPresent(ClassCompletionTemplateDO::getConversionAnnouncement, reqVO.getConversionAnnouncement())
                .eqIfPresent(ClassCompletionTemplateDO::getMaxScore, reqVO.getMaxScore())
                .eqIfPresent(ClassCompletionTemplateDO::getInitialScore, reqVO.getInitialScore())
                .eqIfPresent(ClassCompletionTemplateDO::getAcquisitionMode, reqVO.getAcquisitionMode())
                .eqIfPresent(ClassCompletionTemplateDO::getDataSource, reqVO.getDataSource())
                .likeIfPresent(ClassCompletionTemplateDO::getAssessmentName, reqVO.getAssessmentName())
                .eqIfPresent(ClassCompletionTemplateDO::getCampus, reqVO.getCampus())
                .eqIfPresent(ClassCompletionTemplateDO::getDefaultRule, reqVO.getDefaultRule())
                .likeIfPresent(ClassCompletionTemplateDO::getTemplateName, reqVO.getTemplateName())
                .likeIfPresent(ClassCompletionTemplateDO::getModuleName, reqVO.getModuleName())
                .eqIfPresent(ClassCompletionTemplateDO::getIdCode, reqVO.getIdCode())
                .eqIfPresent(ClassCompletionTemplateDO::getClassId, reqVO.getClassId())
                .orderByDesc(ClassCompletionTemplateDO::getId));
    }

    default List<ClassCompletionTemplateDO> selectList(ClassCompletionTemplateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ClassCompletionTemplateDO>()
                .eqIfPresent(ClassCompletionTemplateDO::getSerialNumber, reqVO.getSerialNumber())
                .likeIfPresent(ClassCompletionTemplateDO::getColumnName, reqVO.getColumnName())
                .eqIfPresent(ClassCompletionTemplateDO::getConversionAnnouncement, reqVO.getConversionAnnouncement())
                .eqIfPresent(ClassCompletionTemplateDO::getMaxScore, reqVO.getMaxScore())
                .eqIfPresent(ClassCompletionTemplateDO::getInitialScore, reqVO.getInitialScore())
                .eqIfPresent(ClassCompletionTemplateDO::getAcquisitionMode, reqVO.getAcquisitionMode())
                .eqIfPresent(ClassCompletionTemplateDO::getDataSource, reqVO.getDataSource())
                .likeIfPresent(ClassCompletionTemplateDO::getAssessmentName, reqVO.getAssessmentName())
                .eqIfPresent(ClassCompletionTemplateDO::getCampus, reqVO.getCampus())
                .eqIfPresent(ClassCompletionTemplateDO::getDefaultRule, reqVO.getDefaultRule())
                .likeIfPresent(ClassCompletionTemplateDO::getTemplateName, reqVO.getTemplateName())
                .likeIfPresent(ClassCompletionTemplateDO::getModuleName, reqVO.getModuleName())
                .eqIfPresent(ClassCompletionTemplateDO::getIdCode, reqVO.getIdCode())
                .eqIfPresent(ClassCompletionTemplateDO::getClassId, reqVO.getClassId())
                .orderByDesc(ClassCompletionTemplateDO::getId));
    }

    Integer selectListByClassId(@Param("classId") Long classId, @Param("save") Integer save);

    void deletedByClassId(@Param("classId") Long classId);

    default List<ClassCompletionTemplateDO> selectByTemplateCode(Long classId, String idCode) {
        LambdaQueryWrapper<ClassCompletionTemplateDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassCompletionTemplateDO::getSave, 1);
        wrapper.eq(ClassCompletionTemplateDO::getClassId, classId);

        // 使用 last 方法自定义排序
        wrapper.last("ORDER BY to_number(regexp_replace(serial_number, '[^0-9]', ''), '99999'), serial_number");

        return selectList(wrapper);
    }

    default List<ClassCompletionTemplateDO> selectTemplateByIdCode(Long classId) {
        LambdaQueryWrapper<ClassCompletionTemplateDO> wrapper = new LambdaQueryWrapper<>();
//
//        wrapper.eq(ClassCompletionTemplateDO::getClassId,classId);
//        wrapper.eq(ClassCompletionTemplateDO::getSave,CompletionSaveEnum.NOT_TEMPORARY.getCode());
//        List<ClassCompletionTemplateDO> templateDOS = selectList(wrapper);
//        if (templateDOS.isEmpty()){
//            return new ArrayList<>();
//        }
//        String idCode = templateDOS.get(0).getIdCode();


        wrapper.eq(ClassCompletionTemplateDO::getClassId,classId);
        wrapper.eq(ClassCompletionTemplateDO::getSave,CompletionSaveEnum.TEMPORARY.getCode());

        return selectList(wrapper);
    }

    default void deleteTemplate(Long classId) {
        LambdaQueryWrapper<ClassCompletionTemplateDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ClassCompletionTemplateDO::getClassId,classId);
        wrapper.eq(ClassCompletionTemplateDO::getSave,CompletionSaveEnum.NOT_TEMPORARY.getCode());

        List<ClassCompletionTemplateDO> list = selectList(wrapper);
        if (!list.isEmpty()){
            this.deleteBatchIds(list.stream().map(ClassCompletionTemplateDO::getId).collect(Collectors.toList()));
        }

    }

    /**
     * 更新暂存模板
     * @param classId 班级id
     */
    default void updateTemplate(Long classId) {
        LambdaQueryWrapper<ClassCompletionTemplateDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ClassCompletionTemplateDO::getClassId,classId);
        wrapper.eq(ClassCompletionTemplateDO::getSave, CompletionSaveEnum.TEMPORARY.getCode());

        List<ClassCompletionTemplateDO> list = selectList(wrapper);

        if (!list.isEmpty()){

            for (ClassCompletionTemplateDO template : list) {
                template.setSave(CompletionSaveEnum.NOT_TEMPORARY.getCode());
            }

            this.updateBatch(list);
        }
    }
    List<Long> selectListByClassIdAndSave(@Param("idCode") String idCode);

}
