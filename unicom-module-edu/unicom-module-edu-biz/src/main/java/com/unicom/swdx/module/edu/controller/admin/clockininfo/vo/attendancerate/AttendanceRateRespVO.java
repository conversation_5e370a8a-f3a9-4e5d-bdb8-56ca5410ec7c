package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 考勤三率响应
 * @date 2024-11-08
 */
@ApiModel("考勤三率 Resp VO")
@Data
public class AttendanceRateRespVO {

    @ApiModelProperty(value = "学生分组考勤信息列表")
    private List<AttendanceRateTraineeInfoVO> traineeInfoList;

    @ApiModelProperty(value = "平均到课率", example = "80.00")
    private String averageClassRate;

    @ApiModelProperty(value = "平均就餐率", example = "80.00")
    private String averageMealRate;

    @ApiModelProperty(value = "平均住宿率", example = "80.00")
    private String averageAccommodationRate;
}
