package com.unicom.swdx.module.edu.dal.dataobject.rollcallcommonlocations;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 大课考勤、点名签到常用信息 DO
 *
 * <AUTHOR>
 */
@TableName("edu_rollcall_common_locations")
@KeySequence("edu_rollcall_common_locations_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RollcallCommonLocationsDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 教师 ID
     */
    private Long teacherId;
    /**
     * 0-大课考勤 1-点名签到
     */
    private Integer type;
    /**
     * 打卡位置的纬度
     */
    private String latitude;
    /**
     * 打卡位置的经度
     */
    private String longitude;
    /**
     * 打卡范围半径
     */
    private BigDecimal radius;
    /**
     * 打卡位置的地址
     */
    private String address;
    /**
     * 系统内部门
     */
    private Long deptId;

}
