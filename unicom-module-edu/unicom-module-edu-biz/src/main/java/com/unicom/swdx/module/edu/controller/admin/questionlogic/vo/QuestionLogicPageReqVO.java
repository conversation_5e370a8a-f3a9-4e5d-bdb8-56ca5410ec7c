package com.unicom.swdx.module.edu.controller.admin.questionlogic.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 问题逻辑分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionLogicPageReqVO extends PageParam {

    @Schema(description = "逻辑问题id", example = "16879")
    private Long questionId;

    @Schema(description = "关联问题id", example = "14855")
    private Long logicQuestionId;

}