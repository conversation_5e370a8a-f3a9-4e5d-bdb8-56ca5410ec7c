package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto.DeptEvaluationExportIdItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 部门评估分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeptEvaluationPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "陈平安")
    private String name;

    @ApiModelProperty(value = "所属部门")
    private String deptName;

    @ApiModelProperty(value = "所属部门ID", example = "1")
    private Long deptId;

    @ApiModelProperty(value = "开始日期", example = "2023-01-01")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束日期", example = "2023-01-01")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "多选id（部门评估统计导出时使用（为deptId））")
    private List<Long> ids;

    @ApiModelProperty(value = "多选id（部门评估导出时使用（deptId和teacherId确定一行））")
    private @Valid List<DeptEvaluationExportIdItemDTO> manyIds;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "排序字段(默认按教师姓名) 0-默认教师姓名 1-按授课次数 2-按班次次数 3-按平均分 4-按部门")
    @Range(min = 0, max = 4, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序排列(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

    @ApiModelProperty(value = "系统-辅助部门权限列表控制", hidden = true)
    private List<Long> deptIdList;
}