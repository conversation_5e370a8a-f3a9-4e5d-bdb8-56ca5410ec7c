package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - EduClassManagement分页 Request VO")
@Data
@ToString(callSuper = true)
public class ClassManagementExportParamsVO {

    @ApiModelProperty(value = "班次编码")
    private String classNameCode;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "办班类型")
    private Integer classTypeDictId;

    @ApiModelProperty(value = "班级属性")
    private String classAttribute;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期")
    private Integer semester;

    @ApiModelProperty(value = "学制")
    private Integer learningSystem;

    @ApiModelProperty(value = "培训对象")
    private String trainingObject;

    @ApiModelProperty(value = "预计人数")
    private Integer peopleNumber;

    @ApiModelProperty(value = "轮次")
    private String turn;

    @ApiModelProperty(value = "校区")
    private Integer campus;

    @ApiModelProperty(value = "报道时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime reportingTime;

    @ApiModelProperty(value = "开班时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime classOpenTime;

    @ApiModelProperty(value = "结业时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime completionTime;

    @ApiModelProperty(value = "报名开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime registrationStartTime;

    @ApiModelProperty(value = "缴费报道，1-是，2-否")
    private Integer paymentReport;

    @ApiModelProperty(value = "考勤评课，1-是，2-否")
    private Integer evaluate;

    @ApiModelProperty(value = "附件")
    private String accessory;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "1-发布中，2-待发布")
    private Integer publish;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "报名结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime registrationEndTime;

    @ApiModelProperty(value = "班主任")
    private Long classTeacherLead;

    @ApiModelProperty(value = "辅导老师")
    private String coachTeacher;

    @ApiModelProperty(value = "0-升序，1-降序")
    @NotNull(message = "change不能为空")
    private Integer change;

    @ApiModelProperty(value = "0-排序，1-班次编码，2-班次名称，3-开班日期")
    @NotNull(message = "tag不能为空")
    private Integer tag;

    @ApiModelProperty(value = "班级状态，0-报名中，1-报名结束，2-开班中，3-已结束")
    private Integer classStatus;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "id列表")
    private List<Long> idList;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "截止时间")
    private String endTime;

    @ApiModelProperty(value = "非调训模块的班次管理查询")
    private Integer isFiltrate;

    @ApiModelProperty(value = "是否班主任管理班级管理，传1-只看当前班主任管理相的班级")
    private String isClassTeacherHead;

    @ApiModelProperty(value = "结业模块，传-1")
    private String complete;
}
