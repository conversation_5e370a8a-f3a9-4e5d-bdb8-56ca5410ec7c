package com.unicom.swdx.module.edu.convert.traineeleave;

import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveProcessDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TraineeLeaveConvert {

    TraineeLeaveConvert INSTANCE = Mappers.getMapper(TraineeLeaveConvert.class);

    TraineeLeaveDO convert(TraineeLeaveBaseVO reqVO);

    Trainee<PERSON>eaveDO convert(TraineeLeaveCreateReqVO reqVO);

    TraineeLeaveRespVO convert(Train<PERSON><PERSON>eaveD<PERSON> leaveDO);

    List<TraineeLeaveProcessVO> convertList(List<TraineeLeaveProcessDO> list);

    List<TraineeLeaveExcelVO> convertExportList(List<TraineeLeaveRespVO> list);

}
