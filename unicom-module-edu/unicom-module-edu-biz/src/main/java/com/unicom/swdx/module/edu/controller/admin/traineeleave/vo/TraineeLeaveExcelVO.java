package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TraineeLeaveExcelVO {
 
    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "请假标题")
    private String title;

    @ExcelProperty(value = "学员姓名")
    private String traineeName;

    @ExcelProperty(value = "班主任")
    private String classTeacherLeadName;

    @ExcelProperty(value = "请假类别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.EDU_TRAINEE_LEAVE_TYPE)
    private Integer leaveType;

    @ExcelProperty(value = "请假天数")
    private Float days;

    @ExcelProperty(value = "请假开始时间")
    private String startTimeStr;

    @ExcelProperty(value = "请假结束时间")
    private String endTimeStr;

    @ExcelProperty(value = "请假事由")
    private String reason;

    @ExcelProperty(value = "附件")
    @ColumnWidth(30)
    private WriteCellData<Void> pictures;
 
    @ExcelIgnore
    private String accessory;

    @ExcelProperty(value = "流程状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.EDU_TRAINEE_LEAVE_STATUS)
    private Integer status;

    @ExcelProperty(value = "提交时间")
    private String applyTimeStr;

}