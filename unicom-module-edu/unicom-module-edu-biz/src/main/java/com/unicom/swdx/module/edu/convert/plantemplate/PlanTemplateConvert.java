package com.unicom.swdx.module.edu.convert.plantemplate;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.plantemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplate.PlanTemplateDO;

/**
 * 教学计划模版 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PlanTemplateConvert {

    PlanTemplateConvert INSTANCE = Mappers.getMapper(PlanTemplateConvert.class);

    PlanTemplateDO convert(PlanTemplateCreateReqVO bean);

    PlanTemplateDO convert(PlanTemplateUpdateReqVO bean);

    PlanTemplateRespVO convert(PlanTemplateDO bean);

    List<PlanTemplateRespVO> convertList(List<PlanTemplateDO> list);

    PageResult<PlanTemplateRespVO> convertPage(PageResult<PlanTemplateDO> page);

    List<PlanTemplateExcelVO> convertList02(List<PlanTemplateDO> list);

}
