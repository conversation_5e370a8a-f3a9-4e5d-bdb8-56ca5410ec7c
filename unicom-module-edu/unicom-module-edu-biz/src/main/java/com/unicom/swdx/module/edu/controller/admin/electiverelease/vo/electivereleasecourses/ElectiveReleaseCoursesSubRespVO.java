package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 选修课发布课程关联 Resp VO
 */
@ApiModel("管理后台 - 选修课发布信息中选修课发布课程关联 Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleaseCoursesSubRespVO extends ElectiveReleaseCoursesSubBaseVO {

    @ApiModelProperty(value = "发布课程主键", example = "1")
    private Long id;

    @ApiModelProperty(value = "选修课发布主键", example = "1")
    private Long releaseId;

    @ApiModelProperty(value = "课程名称", required = true, example = "选修课")
    private String courseName;

    @ApiModelProperty(value = "教师名称", required = true, example = "教师")
    private String teacherName;

    @ApiModelProperty(value = "上课教室名称", required = true, example = "A1")
    private String classroomName;
}
