package com.unicom.swdx.module.edu.service.homepage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.HomePageDataCardRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeLevelAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeUnitAnalysisRespVO;
import com.unicom.swdx.module.edu.convert.trainee.TraineeLevelConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: homePageServiceImpl
 * @Author: lty
 * @Date: 2024/11/15 10:25
 */
@Service
@Validated
@Slf4j
public class HomePageServiceImpl extends ServiceImpl<TraineeMapper, TraineeDO> implements HomePageService {

    @Resource
    private TraineeMapper traineeMapper;
    @Resource
    private ClassManagementService classManagementService;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private ClassManagementMapper classManagementMapper;

    @Override
    public HomePageDataCardRespVO getDataCard() {

        HomePageDataCardRespVO respVO = new HomePageDataCardRespVO();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(TraineeStatusEnum.REPORTED.getStatus());
        statusList.add(TraineeStatusEnum.GRADUATED.getStatus());

        // 获取所有班级已发布并且学员状态为已报到的学员数量
        List<TraineeDO> currentTraineeList = traineeMapper.getTraineeInfoByTraineeStatus(statusList.get(0));
        respVO.setCurrentStudents(currentTraineeList.size());

        // 本年度结业人员
        List<TraineeDO> graduateTraineeList = traineeMapper.getCurrentGraduateTrainee(statusList);
        respVO.setGraduatesThisYear(graduateTraineeList == null ? 0 : graduateTraineeList.size());

        //当前开班班次
        ClassManagementPageReqVO pageVO = new ClassManagementPageReqVO();
        pageVO.setClassStatus(2);
        pageVO.setTag(0);
        pageVO.setChange(1);
        pageVO.setPageSize(99999);

        Page buildPage = MyBatisUtils.buildPage(pageVO);
        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectPageList(buildPage, pageVO);

        respVO.setCurrentClasses(classManagementDOList == null ? 0 : classManagementDOList.size());

        //即将开班
        List<ClassManagementDO> classList = classManagementService.getClassList();
        respVO.setUpcomingClasses(classList == null ? 0 : classList.size());

        //本年度结业班次
        List<ClassManagementDO> complateClassList = classManagementService.getComplateClassList();
        respVO.setGraduatingClassesThisYear(complateClassList == null ? 0 : complateClassList.size());

        return respVO;
    }

    @Override
    public TraineeLevelAnalysisRespVO traineeLevelAnalysis() {

        TraineeLevelAnalysisRespVO respVO = new TraineeLevelAnalysisRespVO();

        //查出字典中所有职级
        List<String> dict = new ArrayList<>();
        dict.add(TraineeDictTypeEnum.JOB_LEVEL.getType());
        List<DictDataRespDTO> levelList = dictDataApi.getByDictTypes(dict).getCheckedData();

        // 转换列表
        List<TraineeLevelAnalysisRespVO.DataVO> levelTypeList = TraineeLevelConvert.INSTANCE.covertList(levelList);

        // 查询已报到学员信息并分组（减少后续多次流操作的性能损耗）
        Map<Integer, Long> traineeCountMap = traineeMapper.getTraineeInfoByTraineeStatus(TraineeStatusEnum.REPORTED.getStatus())
                .stream()
                .filter(item->item.getJobLevel() != null)
                .collect(Collectors.groupingBy(TraineeDO::getJobLevel, Collectors.counting()));

        // 设置每个职级的学员数量，避免空指针问题
        levelTypeList.forEach(level ->
                level.setItemNum(traineeCountMap.getOrDefault(level.getId(), 0L).intValue()));

        // 计算总人数
        int total = levelTypeList.stream()
                .mapToInt(TraineeLevelAnalysisRespVO.DataVO::getItemNum)
                .sum();

        List<TraineeLevelAnalysisRespVO.DataVO> levelTypes=new ArrayList<>();
        TraineeLevelAnalysisRespVO.DataVO hallLevel=new TraineeLevelAnalysisRespVO.DataVO(0,"厅级",0,"0");
        TraineeLevelAnalysisRespVO.DataVO sectionChief=new TraineeLevelAnalysisRespVO.DataVO(0,"处级",0,"0");
        TraineeLevelAnalysisRespVO.DataVO sectionAdministrative=new TraineeLevelAnalysisRespVO.DataVO(0,"科级",0,"0");
        TraineeLevelAnalysisRespVO.DataVO belowLevelScience=new TraineeLevelAnalysisRespVO.DataVO(0,"科级以下",0,"0");


        if (total > 0) {
            BigDecimal totalDecimal = BigDecimal.valueOf(total);
            BigDecimal sumRates = BigDecimal.ZERO;

            for (TraineeLevelAnalysisRespVO.DataVO dataVO : levelTypeList) {
                if(dataVO.getItemName()!=null){
                    if(dataVO.getItemName().equals("正厅")||dataVO.getItemName().equals("一级巡视员")||dataVO.getItemName().equals("二级巡视员")||dataVO.getItemName().equals("副厅")){
                        hallLevel.setItemNum(hallLevel.getItemNum()+ dataVO.getItemNum());
                    }
                    if(dataVO.getItemName().equals("正处")||dataVO.getItemName().equals("一级调研员")||dataVO.getItemName().equals("二级调研员")||dataVO.getItemName().equals("三级调研员")||dataVO.getItemName().equals("四级调研员")||dataVO.getItemName().equals("副处")){
                        sectionChief.setItemNum(sectionChief.getItemNum()+dataVO.getItemNum());
                    }
                    if(dataVO.getItemName().equals("正科")||dataVO.getItemName().equals("一级主任科员")||dataVO.getItemName().equals("二级主任科员")||dataVO.getItemName().equals("三级主任科员")||dataVO.getItemName().equals("四级主任科员")||dataVO.getItemName().equals("副科")||dataVO.getItemName().equals("科员")){
                        sectionAdministrative.setItemNum(sectionAdministrative.getItemNum()+dataVO.getItemNum());
                    }
                    if(dataVO.getItemName().equals("办事员")||dataVO.getItemName().equals("未定职人员")){
                        belowLevelScience.setItemNum(belowLevelScience.getItemNum()+dataVO.getItemNum());
                    }
                }
            }

            levelTypes.add(hallLevel);
            levelTypes.add(sectionChief);
            levelTypes.add(sectionAdministrative);
            levelTypes.add(belowLevelScience);

            levelTypeList=levelTypes;

            for (int i = 0; i < levelTypeList.size(); i++) {
                TraineeLevelAnalysisRespVO.DataVO level = levelTypeList.get(i);
                BigDecimal levelRate;


                if (i == levelTypeList.size() - 1) {
                    // 最后一个比例，避免浮点误差
                    levelRate = BigDecimal.ONE.subtract(sumRates).setScale(2, RoundingMode.HALF_UP);
                } else {
                    levelRate = BigDecimal.valueOf(level.getItemNum())
                            .divide(totalDecimal, 4, RoundingMode.HALF_UP)
                            .setScale(2, RoundingMode.HALF_UP);
                    sumRates = sumRates.add(levelRate);
                }

                // 设置比例，确保数据一致性
                level.setItemRate(levelRate.max(BigDecimal.ZERO).toString());
            }
        } else {
            levelTypes.add(hallLevel);
            levelTypes.add(sectionChief);
            levelTypes.add(sectionAdministrative);
            levelTypes.add(belowLevelScience);

            levelTypeList=levelTypes;
            // 若总人数为 0，所有比例为 0
            levelTypeList.forEach(level -> level.setItemRate("0.00"));
        }

        // 设置响应对象并返回
        respVO.setItemTypeList(levelTypeList);
        respVO.setTotal(total);

        return respVO;

    }



    @Override
    public TraineeLevelAnalysisRespVO traineeEthnicAnalysis() {

        TraineeLevelAnalysisRespVO respVO = new TraineeLevelAnalysisRespVO();

        //查出字典中所有职级
        List<String> dict = new ArrayList<>();
        dict.add("nation");
        List<DictDataRespDTO> levelList = dictDataApi.getByDictTypes(dict).getCheckedData();

        // 转换列表
        // 转换列表
        List<TraineeLevelAnalysisRespVO.DataVO> levelTypeList = TraineeLevelConvert.INSTANCE.covertList(levelList);

        // 查询已报到学员信息并分组（减少后续多次流操作的性能损耗）
        Map<Integer, Long> traineeCountMap = traineeMapper.getTraineeInfoByTraineeStatus(TraineeStatusEnum.REPORTED.getStatus())
                .stream()
                .filter(item-> item.getEthnic()!=null)
                .collect(Collectors.groupingBy(TraineeDO::getEthnic, Collectors.counting()));

        // 设置每个职级的学员数量，避免空指针问题
        levelTypeList.forEach(level ->
                level.setItemNum(traineeCountMap.getOrDefault(level.getId(), 0L).intValue()));

        // 计算总人数
        int total = levelTypeList.stream()
                .mapToInt(TraineeLevelAnalysisRespVO.DataVO::getItemNum)
                .sum();

        if (total > 0) {
            BigDecimal totalDecimal = BigDecimal.valueOf(total);
            BigDecimal sumRates = BigDecimal.ZERO;

            for (int i = 0; i < levelTypeList.size(); i++) {
                TraineeLevelAnalysisRespVO.DataVO level = levelTypeList.get(i);
                BigDecimal levelRate;

                if (i == levelTypeList.size() - 1) {
                    // 最后一个比例，避免浮点误差
                    levelRate = BigDecimal.ONE.subtract(sumRates).setScale(2, RoundingMode.HALF_UP);
                } else {
                    levelRate = BigDecimal.valueOf(level.getItemNum())
                            .divide(totalDecimal, 4, RoundingMode.HALF_UP)
                            .setScale(2, RoundingMode.HALF_UP);
                    sumRates = sumRates.add(levelRate);
                }

                // 设置比例，确保数据一致性
                level.setItemRate(levelRate.max(BigDecimal.ZERO).toString());
            }
        } else {
            // 若总人数为 0，所有比例为 0
            levelTypeList.forEach(level -> level.setItemRate("0.00"));
        }

        List<TraineeLevelAnalysisRespVO.DataVO> topFiveList = levelTypeList.stream()
                .sorted(Comparator.comparing(TraineeLevelAnalysisRespVO.DataVO::getItemNum).reversed())
                .limit(5)
                .collect(Collectors.toList());


        int topFiveTotal = 0;
        BigDecimal topFiveRateSum = BigDecimal.ZERO;

        for (TraineeLevelAnalysisRespVO.DataVO dataVO : topFiveList) {
            topFiveTotal += dataVO.getItemNum();

            topFiveRateSum = topFiveRateSum.add(new BigDecimal(dataVO.getItemRate()));
        }

        TraineeLevelAnalysisRespVO.DataVO vo = new TraineeLevelAnalysisRespVO.DataVO();

        vo.setItemName("其他");
        vo.setItemNum(total - topFiveTotal);

        BigDecimal otherRate = BigDecimal.ONE.subtract(topFiveRateSum);
        vo.setItemRate(otherRate.toString());

        topFiveList.add(vo);

        // 设置响应对象并返回
        respVO.setItemTypeList(topFiveList);
        respVO.setTotal(total);

        return respVO;
    }

    @Override
    public TraineeLevelAnalysisRespVO traineeSexAnalysis() {
        TraineeLevelAnalysisRespVO respVO = new TraineeLevelAnalysisRespVO();

        //初始化性别列表
        List<TraineeLevelAnalysisRespVO.DataVO> sexList = new ArrayList<>();

        TraineeLevelAnalysisRespVO.DataVO male = new TraineeLevelAnalysisRespVO.DataVO();
        male.setItemName("男");
        male.setItemNum(0);
        sexList.add(male);

        TraineeLevelAnalysisRespVO.DataVO female = new TraineeLevelAnalysisRespVO.DataVO();
        female.setItemName("女");
        female.setItemNum(0);
        sexList.add(female);

        Map<String, Long> traineeCountMap = traineeMapper.getTraineeInfoByTraineeStatus(TraineeStatusEnum.REPORTED.getStatus())
                .stream()
                .filter(item->item.getSex() != null)
                .collect(Collectors.groupingBy(TraineeDO::getSex, Collectors.counting()));

        int total = 0;
        for (TraineeLevelAnalysisRespVO.DataVO sex : sexList) {

            if (traineeCountMap.get(sex.getItemName()) != null){
                sex.setItemNum(traineeCountMap.get(sex.getItemName()).intValue());
                total+=traineeCountMap.get(sex.getItemName()).intValue();
            }

        }

        respVO.setItemTypeList(sexList);
        respVO.setTotal(total);
        // 转换列表
        return respVO;
    }

    @Override
    public List<TraineeUnitAnalysisRespVO> traineeUnitAnalysis() {

        //获取所有单位分类
        List<String> dict = new ArrayList<>();
        dict.add("edu_class_unitType");
        List<DictDataRespDTO> unitTypeList = dictDataApi.getByDictTypes(dict).getCheckedData();

        List<TraineeUnitAnalysisRespVO> unitList = TraineeLevelConvert.INSTANCE.covertUnitList(unitTypeList);

        Map<Long, Long> unitCountMap = traineeMapper.getTraineeInfoByTraineeStatus(TraineeStatusEnum.REPORTED.getStatus())
                .stream()
                .filter(item->item.getUnitClassification() != null)
                .collect(Collectors.groupingBy(TraineeDO::getUnitClassification, Collectors.counting()));


        for (TraineeUnitAnalysisRespVO vo : unitList) {

            if (unitCountMap.get(vo.getId()) != null){
                vo.setItemNum(unitCountMap.get(vo.getId()).intValue());
            }

        }


        return unitList;
    }

    @Override
    public List<TraineeUnitAnalysisRespVO> traineeEducationAnalysis() {

        //获取所有学员学历分类
        List<String> dict = new ArrayList<>();
        dict.add(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        List<DictDataRespDTO> eduTypeList = dictDataApi.getByDictTypes(dict).getCheckedData();

        List<TraineeUnitAnalysisRespVO> eduList = TraineeLevelConvert.INSTANCE.covertUnitList(eduTypeList);

        Map<Integer, Long> unitCountMap = traineeMapper.getTraineeInfoByTraineeStatus(TraineeStatusEnum.REPORTED.getStatus())
                .stream()
                .filter(item -> item.getEducationalLevel() != null)
                .collect(Collectors.groupingBy(TraineeDO::getEducationalLevel, Collectors.counting()));

        for (TraineeUnitAnalysisRespVO vo : eduList) {

            if (unitCountMap.get(vo.getId().intValue()) != null){
                vo.setItemNum(unitCountMap.get(vo.getId().intValue()).intValue());
            }

        }

        return eduList;
    }

    @Override
    public List<TraineeUnitAnalysisRespVO> traineeAgeAnalysis() {

        List<TraineeDO> traineeList = traineeMapper.getTraineeInfoByTraineeStatus(TraineeStatusEnum.REPORTED.getStatus());

        return analyzeTraineeAgeGroups(traineeList,10);

    }


    public static List<TraineeUnitAnalysisRespVO> analyzeTraineeAgeGroups(List<TraineeDO> traineeList, int step) {
        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 计算年龄范围
        List<Integer> ages = traineeList.stream()
                .map(TraineeDO::getBirthday)
                .filter(Objects::nonNull)
                .map(birthday -> Period.between(birthday, now).getYears())
                .collect(Collectors.toList());

        if (ages.isEmpty()) {
            return Collections.emptyList(); // 无数据返回空列表
        }

        int minAge = Collections.min(ages);
        int maxAge = Collections.max(ages);

        // 动态生成区间
        List<String> ageRanges = new ArrayList<>();
        ageRanges.add("20-30");
        ageRanges.add("30-40");
        ageRanges.add("40-50");
        ageRanges.add("50-60");
        ageRanges.add("60-70");

//        for (int start = (minAge / step) * step; start <= maxAge; start += step) {
//            ageRanges.add(start + "-" + (start + step));
//        }

        // 初始化统计结果
        Map<String, Integer> ageGroupCount = new LinkedHashMap<>();
        for (String range : ageRanges) {
            ageGroupCount.put(range, 0);
        }

        // 按年龄分组统计
        ages.forEach(age -> {
            for (String range : ageRanges) {
                String[] limits = range.split("-");
                int lower = Integer.parseInt(limits[0]);
                int upper = Integer.parseInt(limits[1]);
                if (age >= lower && age < upper) {
                    ageGroupCount.put(range, ageGroupCount.get(range) + 1);
                    break;
                }
            }
        });

        // 过滤无数据的区间并构造返回结果
        return ageGroupCount.entrySet().stream()
                .filter(entry -> entry.getValue() > 0)
                .map(entry -> {
                    TraineeUnitAnalysisRespVO resp = new TraineeUnitAnalysisRespVO();
                    resp.setItemName(entry.getKey());
                    resp.setItemNum(entry.getValue());
                    return resp;
                })
                .collect(Collectors.toList());
    }
}
