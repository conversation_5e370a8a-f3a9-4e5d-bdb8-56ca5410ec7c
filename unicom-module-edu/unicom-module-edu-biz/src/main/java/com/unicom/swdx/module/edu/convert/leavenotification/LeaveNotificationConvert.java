package com.unicom.swdx.module.edu.convert.leavenotification;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.leavenotification.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.leavenotification.LeaveNotificationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.mapstruct.IterableMapping;
import org.mapstruct.Named;

/**
 * 离校报备 Convert
 */
@Mapper
public interface LeaveNotificationConvert {

    LeaveNotificationConvert INSTANCE = Mappers.getMapper(LeaveNotificationConvert.class);

    LeaveNotificationDO convert(LeaveNotificationCreateReqVO bean);

    @Named("convert")
    LeaveNotificationRespVO convert(LeaveNotificationDO bean);

    LeaveNotificationDetailRespVO convertDetail(LeaveNotificationDO bean);

    @IterableMapping(qualifiedByName = "convert")
    PageResult<LeaveNotificationRespVO> convertPage(PageResult<LeaveNotificationDO> page);

}