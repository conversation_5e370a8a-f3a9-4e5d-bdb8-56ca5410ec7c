package com.unicom.swdx.module.edu.dal.dataobject.plan;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.PlanConfigBaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;


@TableName("edu_plan")
@KeySequence("edu_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanDO extends TenantBaseDO {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 计划名称
     */
    private String name;
    /**
     * 开始日期（yyyyMMdd，如20241029）
     */
    private String beginDate;
    /**
     * 结束日期（yyyyMMdd，如20241029）
     */
    private String endDate;

    /**
     * 常用教室生效日期
     */
    private String effectiveDate;

    /**
     * 常用教室id
     */
    private Long classroomId;

    /**
     * 常用教室名称
     */
    @TableField(exist = false)
    private String classroomName;

    /**
     * 班级id
     */
    private Long classId;

//    /**
//     * 状态：0暂存，1应用
//     */
//    private Boolean status;

    /**
     * 教学计划配置信息
     */
    @TableField(exist = false)
    private List<PlanConfigBaseVO> planConfigBaseVOList;


}
