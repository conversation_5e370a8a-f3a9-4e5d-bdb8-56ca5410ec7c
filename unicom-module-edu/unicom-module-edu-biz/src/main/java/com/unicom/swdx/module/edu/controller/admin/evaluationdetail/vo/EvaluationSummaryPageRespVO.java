package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("评估汇总分页查询 Response VO")
@Data
public class EvaluationSummaryPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    @ExcelIgnore
    private Long serialNumber;

    @ApiModelProperty(value = "部门名称", example = "部门1")
    @ExcelProperty("部门")
    private String deptName;

    @ApiModelProperty(value = "任课老师名称", example = "老师1")
    @ExcelProperty("任课老师")
    private String teacherName;

    @ApiModelProperty(value = "课程名称", example = "课程")
    @ExcelProperty("课程名称")
    private String courseName;

    @ApiModelProperty(value = "学生评价分数", example = "1")
    @ExcelProperty("学生评价分数")
    private Long score;

    @ApiModelProperty(value = "意见反馈", example = "面授")
    @ExcelProperty("意见反馈")
    private String comment;

    @ApiModelProperty(value = "授课班次ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long classId;

    @ApiModelProperty(value = "授课班次", example = "班级")
    @ExcelProperty("班次")
    private String className;

    @ApiModelProperty(value = "排课班次", example = "班级")
    @ExcelIgnore
    private Long classCourseId;

    @ApiModelProperty(value = "评课学员", example = "班级")
    @ExcelIgnore
    private Long studentId;

    @ApiModelProperty(value = "授课教师ID", example = "1")
    @ExcelIgnore
    private String teacherIds;
}
