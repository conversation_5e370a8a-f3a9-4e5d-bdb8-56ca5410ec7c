package com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 结业考核模版设置 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class CompletionTemplateBaseVO {

    @ApiModelProperty(value = "序列号")
    private String serialNumber;

    @ApiModelProperty(value = "二级列名")
    private String columnName;

    @ApiModelProperty(value = "换算公式")
    private String conversionAnnouncement;

    @ApiModelProperty(value = "分值上限")
    private Integer maxScore;

    @ApiModelProperty(value = "初始分")
    private Integer initialScore;

    @ApiModelProperty(value = "获取方式,0-初始值上修改，1-自动获取，2-固定值")
    private Integer acquisitionMode;

    @ApiModelProperty(value = "数据来源，0-事假、1-病假、2-五会假、3-到课率、4-就餐率、5-住宿率、6-评课率、7-迟到次数")
    private Integer dataSource;

    @ApiModelProperty(value = "考核名称")
    private String assessmentName;

    @ApiModelProperty(value = "所属校区")
    @NotNull(message = "所属校区不能为空")
    private Integer campus;

    @ApiModelProperty(value = "默认规则，0-是，1-否")
    @NotNull(message = "默认规则不能为空")
    private Integer defaultRule;

    @ApiModelProperty(value = "模版名称")
    @NotNull(message = "模版名称不能为空")
    private String templateName;

    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    @ApiModelProperty(value = "唯一编码")
    private String idCode;
    @ApiModelProperty(value = "内置模板")
    private Integer builtinTemplate;
}
