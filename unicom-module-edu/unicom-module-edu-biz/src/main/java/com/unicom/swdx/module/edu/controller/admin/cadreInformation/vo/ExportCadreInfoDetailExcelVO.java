package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDate;


/**
 * @ClassName: ExportCadreInfoDetailExcelVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "干部信息详情导出VO")
public class ExportCadreInfoDetailExcelVO {


    /**
     * 班次编码
     */
    @ExcelProperty(value = "班次编码")
    private String classNameCode;

    /**
     * 班次名称
     */
    @ExcelProperty(value = "班次名称")
    private String className;

    /**
     * 班次属性
     */
    @ExcelProperty(value = "班次属性")
    private String classAttribute;

    /**
     * 开班日期
     */
    @ExcelProperty(value = "开班日期")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate classOpenTime;

    /**
     * 结业日期
     */
    @ExcelProperty(value = "结（毕）业日期")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate completionTime;


    /**
     * 年度
     */
    @ExcelProperty(value = "年度")
    private Integer year;

    /**
     * 学期
     */
    @ExcelProperty(value = "学期")
    private String semester;

    /**
     * 班主任
     */
    @ExcelProperty(value = "班主任")
    private String classTeacherLead;


}
