package com.unicom.swdx.module.edu.dal.mysql.teacherinformation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherDeptDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TeacherDeptMapper extends BaseMapperX<TeacherDeptDO> {
    default void deleteByTenantId(Long myTenantId){
        delete(new LambdaQueryWrapper<TeacherDeptDO>()
                .eq(TeacherDeptDO::getTenantId,myTenantId));
    }

    /**
     * 根据教师表同步租户的教师部门信息
     * @param myTenantId 租户ID
     */
    void syncTeacherDeptForTenantId(@Param("tenantId") Long myTenantId);
}
