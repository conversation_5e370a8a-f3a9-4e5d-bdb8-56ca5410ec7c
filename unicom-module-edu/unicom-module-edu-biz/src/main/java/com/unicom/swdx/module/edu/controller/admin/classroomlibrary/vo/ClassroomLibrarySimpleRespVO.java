package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 下拉框教室信息
 * @date 2024-10-25
 */
@ApiModel("管理后台 - 下拉框教室信息Resp VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassroomLibrarySimpleRespVO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "教室名称")
    private String name;

}
