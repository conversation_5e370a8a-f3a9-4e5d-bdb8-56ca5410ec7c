package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 选修课管理-根据上课时间段获取空闲教师下拉数据 Request VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 选修课管理-根据上课时间段获取空闲教师下拉数据 Request VO")
@Data
public class ElectiveReleaseClassTimeTeacherReqVO extends ElectiveReleaseClassTimeClassroomReqVO {

    @ApiModelProperty(value = "选修课id", required = true, example = "1")
    @NotNull(message = "选修课id不能为空")
    private Long courseId;
}
