package com.unicom.swdx.module.edu.service.questionlogic;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.questionlogic.QuestionLogicMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 问题逻辑 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionLogicServiceImpl implements QuestionLogicService {

    @Resource
    private QuestionLogicMapper questionLogicMapper;

    @Override
    public Integer createQuestionLogic(QuestionLogicSaveReqVO createReqVO) {
        // 插入
        QuestionLogicDO questionLogic = BeanUtils.toBean(createReqVO, QuestionLogicDO.class);
        questionLogicMapper.insert(questionLogic);
        // 返回
        return questionLogic.getId();
    }

    @Override
    public void updateQuestionLogic(QuestionLogicSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionLogicExists(updateReqVO.getId());
        // 更新
        QuestionLogicDO updateObj = BeanUtils.toBean(updateReqVO, QuestionLogicDO.class);
        questionLogicMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionLogic(Integer id) {
        // 校验存在
        validateQuestionLogicExists(id);
        // 删除
        questionLogicMapper.deleteById(id);
    }

    private void validateQuestionLogicExists(Integer id) {
        if (questionLogicMapper.selectById(id) == null) {
            throw exception(QUESTION_LOGIC_NOT_EXISTS);
        }
    }

    @Override
    public QuestionLogicDO getQuestionLogic(Integer id) {
        return questionLogicMapper.selectById(id);
    }

    @Override
    public PageResult<QuestionLogicDO> getQuestionLogicPage(QuestionLogicPageReqVO pageReqVO) {
        return questionLogicMapper.selectPage(pageReqVO);
    }

}