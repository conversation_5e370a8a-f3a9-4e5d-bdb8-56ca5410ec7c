package com.unicom.swdx.module.edu.controller.admin.evaluationdetail;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationDetailSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.EvaluationResponseSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleaseclasses.ElectiveReleaseClassesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivetraineeselection.ElectiveTraineeSelectionDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleaseclasses.ElectiveReleaseClassesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses.ElectiveReleaseCoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivetraineeselection.ElectiveTraineeSelectionMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.classcommen.CommenService;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.questionnairemanagement.QuestionnaireManagementService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.checkerframework.checker.units.qual.C;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 选修课下发问卷
 */
@Component
public class ElectiveDistributeJob {

    @Resource
    ElectiveTraineeSelectionMapper electiveTraineeSelectionMapper;

    @Resource
    ElectiveReleaseCoursesMapper electiveReleaseCoursesMapper;

    @Resource
    ElectiveReleaseClassesMapper electiveReleaseClassesMapper;

    @Resource
    TraineeMapper traineeMapper;

    @Resource
    ClassManagementMapper classManagementMapper;

    @Resource
    ClockInInfoMapper clockInInfoMapper;

    @Resource
    CoursesMapper coursesMapper;


    @Resource
    QuestionnaireManagementService questionnaireManagementService;


    @Resource
    EvaluationResponseService evaluationResponseService;

    @Resource
    EvaluationDetailService evaluationDetailService;

    @XxlJob("ElectiveDistributeJob")
    @TenantIgnore
    // @Scheduled(cron = "")
    public void ElectiveDistribute() throws Exception {

        // 查询近半个小时内没有下发问卷且已经结束的课程
        LocalDateTime now = LocalDateTime.now();

        XxlJobHelper.log("开始下发选修课问卷");
        LocalDateTime time = now.minus(1, ChronoUnit.DAYS);
        List<ElectiveTraineeSelectionDO> selectedTrainee = electiveTraineeSelectionMapper.getSelectedTrainee(time, now);
        if(selectedTrainee != null && !selectedTrainee.isEmpty()) {
            // 下发问卷
            selectedTrainee.forEach(selection-> {
                try {
                    selection.setIsDistributed(true);
                    // 获取课程信息
                    ElectiveReleaseCoursesDO electiveReleaseCoursesDO = electiveReleaseCoursesMapper.selectById(selection.getReleaseCourseId());

                    // 获取学员信息
                    TraineeDO trainee = traineeMapper.selectById(selection.getTraineeId());

                    // 获取排课信息
                    LambdaQueryWrapper<ElectiveReleaseClassesDO> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(ElectiveReleaseClassesDO::getClassId, trainee.getClassId());
                    wrapper.eq(ElectiveReleaseClassesDO::getReleaseId, selection.getReleaseId());
                    wrapper.eq(ElectiveReleaseClassesDO::getDeleted, false);
                    List<ElectiveReleaseClassesDO> electiveReleaseClassesDOS = electiveReleaseClassesMapper.selectList(wrapper);

                    ClassManagementDO classManagementDO = classManagementMapper.selectById(electiveReleaseClassesDOS.get(0).getClassId());

                    // 考勤评课的情况下如果没打卡就不发问卷
                    if(classManagementDO.getEvaluate().equals(1)){
                        Integer status = clockInInfoMapper.getTraineeStatus(electiveReleaseClassesDOS.get(0).getClassCourseId(), trainee.getId().toString());
                        if (status == 0 || status == 3) {
                            return;
                        }
                    }

                    // 获取问卷信息
                    CoursesDO course = coursesMapper.selectById(electiveReleaseCoursesDO.getCourseId());
                    Long questionnaireId = questionnaireManagementService.getByEducateForm(course.getEducateFormId(), course.getTenantId());
                    if (questionnaireId == null) {
                        questionnaireId = questionnaireManagementService.getDefaultQuestionnaireId(course.getTenantId());
                    }
                    QuestionnaireManagementRespVO questionnaire = questionnaireManagementService.getQuestionnaireManagement(questionnaireId,null,null);
                    List<QuestionManagementRespVO> questions = questionnaire.getQuestions();

                    EvaluationResponseSaveReqVO responseSaveReqVO = new EvaluationResponseSaveReqVO();
                    responseSaveReqVO.setClassCourseId(electiveReleaseClassesDOS.get(0).getClassCourseId());
                    responseSaveReqVO.setQuestionnaireId(questionnaire.getId());
                    responseSaveReqVO.setStudentId(selection.getTraineeId());
                    responseSaveReqVO.setIssuer(electiveReleaseCoursesDO.getTeacherId().toString());
                    responseSaveReqVO.setTeacherId(electiveReleaseCoursesDO.getTeacherId().toString());
                    responseSaveReqVO.setHandle(false);
                    responseSaveReqVO.setTenantId(course.getTenantId());
                    responseSaveReqVO.setDepartment(false);
                    responseSaveReqVO.setCourseId(course.getId());
                    if (Boolean.TRUE.equals(questionnaire.getTimeTag())) {
                        LocalDateTime expireTime = now.plusDays(questionnaire.getTimeLimit());
                        responseSaveReqVO.setExpireTime(expireTime);
                    }

                    // 下发问卷
                    evaluationResponseService.createEvaluationResponse(responseSaveReqVO);

                    // 下发题目
                    try {
                        questions.forEach(question -> {
                            EvaluationDetailSaveReqVO detailSaveReqVO = new EvaluationDetailSaveReqVO();
                            detailSaveReqVO.setQuestionnaireId(questionnaire.getId());
                            detailSaveReqVO.setQuestionId(question.getId());
                            detailSaveReqVO.setQuestionType(question.getQuestionType());
                            detailSaveReqVO.setStudentId(selection.getTraineeId());
                            detailSaveReqVO.setClassCourseId(electiveReleaseClassesDOS.get(0).getClassCourseId());
                            detailSaveReqVO.setTenantId(course.getTenantId());
                            detailSaveReqVO.setCourseId(course.getId());

                            // 记录每个学员的选项
                            evaluationDetailService.createEvaluationDetail(detailSaveReqVO);
                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    electiveTraineeSelectionMapper.updateById(selection);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            // 下发完成后更新下发状态为已下发
            // classCourseMapper.updateBatch(endCourses);
            XxlJobHelper.log("选修课问卷下发完成");
        }
    }
}
