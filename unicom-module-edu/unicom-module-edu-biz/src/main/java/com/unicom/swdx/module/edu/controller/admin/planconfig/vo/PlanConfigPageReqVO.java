package com.unicom.swdx.module.edu.controller.admin.planconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 教学计划配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanConfigPageReqVO extends PageParam {

    @ApiModelProperty(value = "教学计划ID")
    private Long planId;

    @ApiModelProperty(value = "星期几（如：1代表周一等）")
    private String dayOfWeek;

    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）")
    private String period;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] beginTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] endTime;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
