package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 师资信息 Create VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 师资信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherInformationCreateReqVO extends TeacherInformationBaseVO{
    @ApiModelProperty(value = "关联课程ID列表")
    private List<Long> coursesIds;
}
