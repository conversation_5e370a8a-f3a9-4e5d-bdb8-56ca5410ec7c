package com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 考勤规则模版分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RuleTemplatePageReqVO extends PageParam {

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "0-到课考勤，1-就餐考勤，2-住宿考勤")
    private Integer ruleType;

    @ApiModelProperty(value = "考勤地点，预留")
    private Integer locations;

    @ApiModelProperty(value = "上课前打卡时间")
    private Integer beforeClassTime;

    @ApiModelProperty(value = "上课后打卡时间")
    private Integer afterClassTime;

    @ApiModelProperty(value = "早餐打卡开始时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime breakfastStartTime;

    @ApiModelProperty(value = "早餐打卡结束时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime breakfastEndTime;

    @ApiModelProperty(value = "午餐打卡开始时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime lunchStartTime;

    @ApiModelProperty(value = "午餐打卡结束时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime lunchEndTime;

    @ApiModelProperty(value = "晚餐打卡开始时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime dinnerStartTime;

    @ApiModelProperty(value = "晚餐打卡结束时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime dinnerEndTime;

    @ApiModelProperty(value = "住宿打卡开始时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime putUpStartTime;

    @ApiModelProperty(value = "住宿打卡结束时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalTime putUpEndTime;

    @ApiModelProperty(value = "状态，0-启用，1-禁用")
    private Integer status;

    @ApiModelProperty(value = "校区，字典值")
    private Integer campus;

    @ApiModelProperty(value = "默认规则，0-是，1-否")
    private Integer defaultRule;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}
