package com.unicom.swdx.module.edu.dal.dataobject.signupunit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * EduSignUpUnit DO
 *
 * <AUTHOR>
 */
@TableName("edu_sign_up_unit")
@KeySequence("edu_sign_up_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignUpUnitDO extends TenantBaseDO {

    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 单位名称
     */
    private String unitName;
    /**
     * 单位分类
     */
    private Integer unitClassification;
    /**
     * 单位责任人
     */
    private String unitChargePeople;
    /**
     * 负责人电话
     */
    private String phone;
    /**
     * 状态，1-启用，2-禁用
     */
    private Integer status;
    /**
     * 排序号
     */
    private Integer sort;
    /**
     * 名额人数
     */
    private Integer capacity;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 是否限制
     */
    private Integer isRestrict;
    /**
     * 模版，模版-1，默认-0
     */
    private Integer template;
    /**
     * 单位管理员用户名
     */
    private String username;
    /**
     * 单位管理员用户id
     */
    private Long userId;

    private Long parentId;

    private String officePhone;

}
