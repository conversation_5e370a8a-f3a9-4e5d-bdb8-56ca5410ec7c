package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @Description: 学员考勤信息响应
 * @date 2024-11-08
 */
@ApiModel("学员考勤信息 Resp VO")
@Data
public class AttendanceRateTraineeInfoVO {

    @ApiModelProperty(value = "学员ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "组名", example = "第一组")
    private String groupName;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "性别", example = "男")
    private String sex;

    @ApiModelProperty(value = "学员职务", example = "研究员")
    private String position;

    @ApiModelProperty(value = "班委id", example = "1")
    private Long classCommitteeId;

    @ApiModelProperty(value = "班委职务", example = "班委长")
    private String classCommitteeName;

    @ApiModelProperty(value = "上课应到次数", example = "1")
    private Integer classAttendanceExpected;

    @ApiModelProperty(value = "上课实到次数", example = "1")
    private Integer classAttendanceActual;

    @ApiModelProperty(value = "上课请假次数", example = "1")
    private Integer classAttendanceLeave;

    @ApiModelProperty(value = "到课率", example = "98.5")
    private String classRate;

    @ApiModelProperty(value = "就餐应到次数", example = "1")
    private Integer mealAttendanceExpected;

    @ApiModelProperty(value = "就餐实到次数", example = "1")
    private Integer mealAttendanceActual;

    @ApiModelProperty(value = "就餐请假次数", example = "1")
    private Integer mealAttendanceLeave;

    @ApiModelProperty(value = "就餐率", example = "98.5")
    private String mealRate;

    @ApiModelProperty(value = "住宿应到次数", example = "1")
    private Integer accommodationAttendanceExpected;

    @ApiModelProperty(value = "住宿实到次数", example = "1")
    private Integer accommodationAttendanceActual;

    @ApiModelProperty(value = "住宿请假次数", example = "1")
    private Integer accommodationAttendanceLeave;

    @ApiModelProperty(value = "住宿率", example = "98.5")
    private String accommodationRate;



    public void setRate(String methodName, String value) {
        try {
            Method method = this.getClass().getMethod(methodName, String.class);
            method.invoke(this, value);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException("反射调用 set 方法失败：" + methodName, e);
        }
    }
}
