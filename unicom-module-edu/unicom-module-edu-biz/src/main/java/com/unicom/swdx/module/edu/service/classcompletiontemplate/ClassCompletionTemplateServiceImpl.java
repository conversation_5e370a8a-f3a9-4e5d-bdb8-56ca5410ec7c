package com.unicom.swdx.module.edu.service.classcompletiontemplate;

import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.CompletionTemplateCreateReqVO;
import com.unicom.swdx.module.edu.convert.completiontemplate.CompletionTemplateConvert;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.mysql.classcompletion.ClassCompletionMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.completiontemplate.CompletionTemplateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.classcompletiontemplate.ClassCompletionTemplateConvert;
import com.unicom.swdx.module.edu.dal.mysql.classcompletiontemplate.ClassCompletionTemplateMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 结业考核模版设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ClassCompletionTemplateServiceImpl implements ClassCompletionTemplateService {

    @Resource
    private ClassCompletionTemplateMapper classCompletionTemplateMapper;

    @Resource
    private CompletionTemplateMapper completionTemplateMapper;
    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private ClassCompletionMapper classCompletionMapper;

    @Override
    public List<Long> createClassCompletionTemplate(ClassCompletionTemplateCreateReqVO createReqVO) {


        if(createReqVO.getClassId() != null){
            //先判断该班级是否存在   若不存在  直接插入   若存在  先删再插
            int count = classCompletionTemplateMapper.selectListByClassId(createReqVO.getClassId(), null);

            //判断是否暂存状态
            int countSave = classCompletionTemplateMapper.selectListByClassId(createReqVO.getClassId(), 0);

            if(count > 0){

                if(countSave > 0){
                    // 存在  先删
                    classCompletionTemplateMapper.deletedByClassId(createReqVO.getClassId());


                    // 获取班级所绑定模版编码的所有数据
                    List<CompletionTemplateDO> list = completionTemplateMapper.selectOneByIdCode(createReqVO.getIdCode());

                    // 把模版编码的所有数据存在班级模版表中   并存入班级id

                    // 新建一个数组
                    List<ClassCompletionTemplateDO> completionTemplateList = new ArrayList<>();

                    for (CompletionTemplateDO addReqVO : list) {
                        ClassCompletionTemplateCreateReqVO completionTemplateDO = new ClassCompletionTemplateCreateReqVO();

                        if(addReqVO.getSerialNumber() != null){
                            completionTemplateDO.setSerialNumber(addReqVO.getSerialNumber());
                        }

                        if(addReqVO.getColumnName() != null){
                            completionTemplateDO.setColumnName(addReqVO.getColumnName());
                        }

                        if(addReqVO.getConversionAnnouncement() != null){
                            completionTemplateDO.setConversionAnnouncement(addReqVO.getConversionAnnouncement());
                        }

                        if(addReqVO.getMaxScore() != null){
                            completionTemplateDO.setMaxScore(addReqVO.getMaxScore());
                        }

                        if(addReqVO.getInitialScore() != null){
                            completionTemplateDO.setInitialScore(addReqVO.getInitialScore());
                        }

                        if(addReqVO.getAcquisitionMode() != null){
                            completionTemplateDO.setAcquisitionMode(addReqVO.getAcquisitionMode());
                        }

                        if(addReqVO.getDataSource() != null){
                            completionTemplateDO.setDataSource(addReqVO.getDataSource());
                        }

                        if(addReqVO.getAssessmentName() != null){
                            completionTemplateDO.setAssessmentName(addReqVO.getAssessmentName());
                        }

                        if(addReqVO.getCampus() != null){
                            completionTemplateDO.setCampus(addReqVO.getCampus());
                        }

                        if(addReqVO.getDefaultRule() != null) {
                            completionTemplateDO.setDefaultRule(addReqVO.getDefaultRule());
                        }

                        if(addReqVO.getTemplateName() != null){
                            completionTemplateDO.setTemplateName(addReqVO.getTemplateName());
                        }

                        if(addReqVO.getModuleName() != null){
                            completionTemplateDO.setModuleName(addReqVO.getModuleName());
                        }

                        if(addReqVO.getIdCode() != null){
                            completionTemplateDO.setIdCode(addReqVO.getIdCode());
                        }

                        if(createReqVO.getClassId() != null){
                            completionTemplateDO.setClassId(createReqVO.getClassId());
                        }

                        completionTemplateDO.setSave(0);

                        // 插入
                        ClassCompletionTemplateDO classCompletionTemplate = ClassCompletionTemplateConvert.INSTANCE.convert(completionTemplateDO);
                        completionTemplateList.add(classCompletionTemplate);
                    }

                    if(!completionTemplateList.isEmpty()){
                        // 批量插入
                        classCompletionTemplateMapper.insertBatch(completionTemplateList);
                    }


                    List<Long> ids = new ArrayList<>();
                    for(ClassCompletionTemplateDO classCompletionTemplateDO : completionTemplateList ){
                        ids.add(classCompletionTemplateDO.getId());
                    }

                    // 返回
                    return ids;
                }else{
                    //不存在  直接插入
                    // 获取班级所绑定模版编码的所有数据
                    List<CompletionTemplateDO> list = completionTemplateMapper.selectOneByIdCode(createReqVO.getIdCode());

                    // 把模版编码的所有数据存在班级模版表中   并存入班级id

                    // 新建一个数组
                    List<ClassCompletionTemplateDO> completionTemplateList = new ArrayList<>();

                    for (CompletionTemplateDO addReqVO : list) {
                        ClassCompletionTemplateCreateReqVO completionTemplateDO = new ClassCompletionTemplateCreateReqVO();

                        if(addReqVO.getSerialNumber() != null){
                            completionTemplateDO.setSerialNumber(addReqVO.getSerialNumber());
                        }

                        if(addReqVO.getColumnName() != null){
                            completionTemplateDO.setColumnName(addReqVO.getColumnName());
                        }

                        if(addReqVO.getConversionAnnouncement() != null){
                            completionTemplateDO.setConversionAnnouncement(addReqVO.getConversionAnnouncement());
                        }

                        if(addReqVO.getMaxScore() != null){
                            completionTemplateDO.setMaxScore(addReqVO.getMaxScore());
                        }

                        if(addReqVO.getInitialScore() != null){
                            completionTemplateDO.setInitialScore(addReqVO.getInitialScore());
                        }

                        if(addReqVO.getAcquisitionMode() != null){
                            completionTemplateDO.setAcquisitionMode(addReqVO.getAcquisitionMode());
                        }

                        if(addReqVO.getDataSource() != null){
                            completionTemplateDO.setDataSource(addReqVO.getDataSource());
                        }

                        if(addReqVO.getAssessmentName() != null){
                            completionTemplateDO.setAssessmentName(addReqVO.getAssessmentName());
                        }

                        if(addReqVO.getCampus() != null){
                            completionTemplateDO.setCampus(addReqVO.getCampus());
                        }

                        if(addReqVO.getDefaultRule() != null) {
                            completionTemplateDO.setDefaultRule(addReqVO.getDefaultRule());
                        }

                        if(addReqVO.getTemplateName() != null){
                            completionTemplateDO.setTemplateName(addReqVO.getTemplateName());
                        }

                        if(addReqVO.getModuleName() != null){
                            completionTemplateDO.setModuleName(addReqVO.getModuleName());
                        }

                        if(addReqVO.getIdCode() != null){
                            completionTemplateDO.setIdCode(addReqVO.getIdCode());
                        }

                        if(createReqVO.getClassId() != null){
                            completionTemplateDO.setClassId(createReqVO.getClassId());
                        }

                        completionTemplateDO.setSave(0);

                        // 插入
                        ClassCompletionTemplateDO classCompletionTemplate = ClassCompletionTemplateConvert.INSTANCE.convert(completionTemplateDO);
                        completionTemplateList.add(classCompletionTemplate);
                    }

                    if(!completionTemplateList.isEmpty()){
                        // 批量插入
                        classCompletionTemplateMapper.insertBatch(completionTemplateList);
                    }


                    List<Long> ids = new ArrayList<>();
                    for(ClassCompletionTemplateDO classCompletionTemplateDO : completionTemplateList ){
                        ids.add(classCompletionTemplateDO.getId());
                    }

                    // 返回
                    return ids;

                }


            }else{
                //不存在  直接插入
                // 获取班级所绑定模版编码的所有数据
                List<CompletionTemplateDO> list = completionTemplateMapper.selectOneByIdCode(createReqVO.getIdCode());

                // 把模版编码的所有数据存在班级模版表中   并存入班级id

                // 新建一个数组
                List<ClassCompletionTemplateDO> completionTemplateList = new ArrayList<>();

                for (CompletionTemplateDO addReqVO : list) {
                    ClassCompletionTemplateCreateReqVO completionTemplateDO = new ClassCompletionTemplateCreateReqVO();

                    if(addReqVO.getSerialNumber() != null){
                        completionTemplateDO.setSerialNumber(addReqVO.getSerialNumber());
                    }

                    if(addReqVO.getColumnName() != null){
                        completionTemplateDO.setColumnName(addReqVO.getColumnName());
                    }

                    if(addReqVO.getConversionAnnouncement() != null){
                        completionTemplateDO.setConversionAnnouncement(addReqVO.getConversionAnnouncement());
                    }

                    if(addReqVO.getMaxScore() != null){
                        completionTemplateDO.setMaxScore(addReqVO.getMaxScore());
                    }

                    if(addReqVO.getInitialScore() != null){
                        completionTemplateDO.setInitialScore(addReqVO.getInitialScore());
                    }

                    if(addReqVO.getAcquisitionMode() != null){
                        completionTemplateDO.setAcquisitionMode(addReqVO.getAcquisitionMode());
                    }

                    if(addReqVO.getDataSource() != null){
                        completionTemplateDO.setDataSource(addReqVO.getDataSource());
                    }

                    if(addReqVO.getAssessmentName() != null){
                        completionTemplateDO.setAssessmentName(addReqVO.getAssessmentName());
                    }

                    if(addReqVO.getCampus() != null){
                        completionTemplateDO.setCampus(addReqVO.getCampus());
                    }

                    if(addReqVO.getDefaultRule() != null) {
                        completionTemplateDO.setDefaultRule(addReqVO.getDefaultRule());
                    }

                    if(addReqVO.getTemplateName() != null){
                        completionTemplateDO.setTemplateName(addReqVO.getTemplateName());
                    }

                    if(addReqVO.getModuleName() != null){
                        completionTemplateDO.setModuleName(addReqVO.getModuleName());
                    }

                    if(addReqVO.getIdCode() != null){
                        completionTemplateDO.setIdCode(addReqVO.getIdCode());
                    }

                    if(createReqVO.getClassId() != null){
                        completionTemplateDO.setClassId(createReqVO.getClassId());
                    }

                    completionTemplateDO.setSave(1);

                    // 插入
                    ClassCompletionTemplateDO classCompletionTemplate = ClassCompletionTemplateConvert.INSTANCE.convert(completionTemplateDO);
                    completionTemplateList.add(classCompletionTemplate);
                }

                if(!completionTemplateList.isEmpty()){
                    // 批量插入
                    classCompletionTemplateMapper.insertBatch(completionTemplateList);
                }


                List<Long> ids = new ArrayList<>();
                for(ClassCompletionTemplateDO classCompletionTemplateDO : completionTemplateList ){
                    ids.add(classCompletionTemplateDO.getId());
                }

                // 返回
                return ids;
            }

        }else{

            String idCode = createReqVO.getIdCode();

            //根据idCode找到所有 暂存的班级id
            List<Long> listId = classCompletionTemplateMapper.selectListByClassIdAndSave(idCode);

            //编辑 删除所有暂存班级的数据
            for(Long id : listId){
                classCompletionTemplateMapper.deletedByClassId(id);
            }


            // 获取班级所绑定模版编码的所有数据
            List<CompletionTemplateDO> list = completionTemplateMapper.selectOneByIdCode(createReqVO.getIdCode());


            //没有就插如

            //重新插入暂存数据
            for(Long id : listId) {


                // 把模版编码的所有数据存在班级模版表中   并存入班级id

                // 新建一个数组
                List<ClassCompletionTemplateDO> completionTemplateList = new ArrayList<>();

                for (CompletionTemplateDO addReqVO : list) {
                    ClassCompletionTemplateCreateReqVO completionTemplateDO = new ClassCompletionTemplateCreateReqVO();

                    if(addReqVO.getSerialNumber() != null){
                        completionTemplateDO.setSerialNumber(addReqVO.getSerialNumber());
                    }

                    if(addReqVO.getColumnName() != null){
                        completionTemplateDO.setColumnName(addReqVO.getColumnName());
                    }

                    if(addReqVO.getConversionAnnouncement() != null){
                        completionTemplateDO.setConversionAnnouncement(addReqVO.getConversionAnnouncement());
                    }

                    if(addReqVO.getMaxScore() != null){
                        completionTemplateDO.setMaxScore(addReqVO.getMaxScore());
                    }

                    if(addReqVO.getInitialScore() != null){
                        completionTemplateDO.setInitialScore(addReqVO.getInitialScore());
                    }

                    if(addReqVO.getAcquisitionMode() != null){
                        completionTemplateDO.setAcquisitionMode(addReqVO.getAcquisitionMode());
                    }

                    if(addReqVO.getDataSource() != null){
                        completionTemplateDO.setDataSource(addReqVO.getDataSource());
                    }

                    if(addReqVO.getAssessmentName() != null){
                        completionTemplateDO.setAssessmentName(addReqVO.getAssessmentName());
                    }

                    if(addReqVO.getCampus() != null){
                        completionTemplateDO.setCampus(addReqVO.getCampus());
                    }

                    if(addReqVO.getDefaultRule() != null) {
                        completionTemplateDO.setDefaultRule(addReqVO.getDefaultRule());
                    }

                    if(addReqVO.getTemplateName() != null){
                        completionTemplateDO.setTemplateName(addReqVO.getTemplateName());
                    }

                    if(addReqVO.getModuleName() != null){
                        completionTemplateDO.setModuleName(addReqVO.getModuleName());
                    }

                    if(addReqVO.getIdCode() != null){
                        completionTemplateDO.setIdCode(addReqVO.getIdCode());
                    }


                    completionTemplateDO.setClassId(id);


                    completionTemplateDO.setSave(0);

                    // 插入
                    ClassCompletionTemplateDO classCompletionTemplate = ClassCompletionTemplateConvert.INSTANCE.convert(completionTemplateDO);
                    completionTemplateList.add(classCompletionTemplate);
                }

                if(!completionTemplateList.isEmpty()){
                    // 批量插入
                    classCompletionTemplateMapper.insertBatch(completionTemplateList);
                }

            }

            return null;
        }


    }

    @Override
    public void updateClassCompletionTemplate(ClassCompletionTemplateUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateClassCompletionTemplateExists(updateReqVO.getId());
        // 更新
        ClassCompletionTemplateDO updateObj = ClassCompletionTemplateConvert.INSTANCE.convert(updateReqVO);
        classCompletionTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteClassCompletionTemplate(Long id) {
        // 校验存在
        this.validateClassCompletionTemplateExists(id);
        // 删除
        classCompletionTemplateMapper.deleteById(id);
    }

    private void validateClassCompletionTemplateExists(Long id) {
        if (classCompletionTemplateMapper.selectById(id) == null) {
            throw exception(CLASS_COMPLETION_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public ClassCompletionTemplateDO getClassCompletionTemplate(Integer id) {
        return classCompletionTemplateMapper.selectById(id);
    }

    @Override
    public List<ClassCompletionTemplateDO> getClassCompletionTemplateList(Collection<Integer> ids) {
        return classCompletionTemplateMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ClassCompletionTemplateDO> getClassCompletionTemplatePage(ClassCompletionTemplatePageReqVO pageReqVO) {
        return classCompletionTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ClassCompletionTemplateDO> getClassCompletionTemplateList(ClassCompletionTemplateExportReqVO exportReqVO) {
        return classCompletionTemplateMapper.selectList(exportReqVO);
    }

    public void deleteCompletionInfo(ClassCompletionTemplateCreateReqVO reqVO) {
        if (reqVO.getDefaultRule() == 1){
            return;
        }

        //根据idCode和campus获取班级
        List<Long> ids = classManagementMapper.getClassByCampus(reqVO.getCampus());

        if (ids.isEmpty()){
            return;
        }

        //删除班级id所有数据
        classCompletionMapper.deleteBatchClassIds(ids);


    }
}
