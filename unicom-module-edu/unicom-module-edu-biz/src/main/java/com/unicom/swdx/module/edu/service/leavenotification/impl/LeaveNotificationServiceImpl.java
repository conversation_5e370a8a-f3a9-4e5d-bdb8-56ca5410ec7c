package com.unicom.swdx.module.edu.service.leavenotification.impl;

import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.leavenotification.vo.*;
import com.unicom.swdx.module.edu.convert.leavenotification.LeaveNotificationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.leavenotification.LeaveNotificationDO;
import com.unicom.swdx.module.edu.dal.dataobject.leavenotification.LeaveNotificationTraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.leavenotification.LeaveNotificationMapper;
import com.unicom.swdx.module.edu.dal.mysql.leavenotification.LeaveNotificationTraineeMapper;
import com.unicom.swdx.module.edu.enums.LeaveNotificationFillStatusEnum;
import com.unicom.swdx.module.edu.enums.NotificationStatusEnum;
import com.unicom.swdx.module.edu.service.leavenotification.LeaveNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 离校报备 Service 实现类
 */
@Service
@Validated
@Slf4j
public class LeaveNotificationServiceImpl implements LeaveNotificationService {

    @Resource
    private LeaveNotificationMapper leaveNotificationMapper;

    @Resource
    private LeaveNotificationTraineeMapper leaveNotificationTraineeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLeaveNotification(LeaveNotificationCreateReqVO createReqVO) {
        // 校验班级状态，确保只能为开班中的班级创建离校报备
        validateClassStatus(createReqVO.getClassId());

        // 生成离校报备名称：第N次离校申请
        String name = generateLeaveNotificationName(createReqVO.getClassId());

        // 插入离校报备
        LeaveNotificationDO leaveNotification = LeaveNotificationConvert.INSTANCE.convert(createReqVO);
        leaveNotification.setName(name);
        leaveNotification.setStatus(NotificationStatusEnum.IN_PROGRESS.getStatus());
        leaveNotificationMapper.insert(leaveNotification);

        // 初始化学员填报记录
        initTraineeRecords(leaveNotification.getId(), leaveNotification.getClassId());

        return leaveNotification.getId();
    }

    @Override
    public void deleteLeaveNotification(Long id) {
        // 校验存在
        validateLeaveNotificationExists(id);

        // 删除离校报备及相关的学员填报记录
        leaveNotificationMapper.deleteById(id);
        // 删除相关学员记录的逻辑，这里需要批量删除
        // leaveNotificationTraineeMapper.deleteByNotificationId(id);
    }

    @Override
    public LeaveNotificationDetailRespVO getLeaveNotificationDetail(Long id) {
        // 获取离校报备记录
        LeaveNotificationDO leaveNotification = leaveNotificationMapper.selectById(id);
        if (leaveNotification == null) {
            throw ServiceExceptionUtil.exception(LEAVE_NOTIFICATION_NOT_EXISTS);
        }

        // 转换为详情VO
        LeaveNotificationDetailRespVO detailVO = LeaveNotificationConvert.INSTANCE.convertDetail(leaveNotification);

        // 获取学员分类数据
        // 1. 未填写学员
        List<LeaveNotificationTraineeDO> notFilledTrainees = leaveNotificationTraineeMapper
                .selectListByNotificationIdAndStatus(
                        id, LeaveNotificationFillStatusEnum.NOT_FILLED.getStatus());
        detailVO.setNotFilledTrainees(convertToTraineeItems(notFilledTrainees));

        // 2. 离校学员
        List<LeaveNotificationTraineeDO> leavingTrainees = leaveNotificationTraineeMapper
                .selectListByNotificationIdAndStatus(
                        id, LeaveNotificationFillStatusEnum.LEAVING.getStatus());
        detailVO.setLeavingTrainees(convertToTraineeItems(leavingTrainees));

        // 3. 不离校学员
        List<LeaveNotificationTraineeDO> stayingTrainees = leaveNotificationTraineeMapper
                .selectListByNotificationIdAndStatus(
                        id, LeaveNotificationFillStatusEnum.STAYING.getStatus());
        detailVO.setStayingTrainees(convertToTraineeItems(stayingTrainees));

        return detailVO;
    }

    @Override
    public PageResult<LeaveNotificationRespVO> getLeaveNotificationPage(LeaveNotificationPageReqVO pageReqVO) {
        // 如果没有指定班级，获取默认班级
        if (pageReqVO.getClassId() == null && pageReqVO.getClassName() == null) {
            // 获取开班中的班级，如无则获取最近结束的班级
            // 这里需要调用班级相关服务获取班级信息
            // Long defaultClassId = classService.getDefaultClassId();
            // pageReqVO.setClassId(defaultClassId);
        }

        PageResult<LeaveNotificationDO> pageResult = leaveNotificationMapper.selectPage(pageReqVO);

        // 转换结果，填充学员统计信息
        List<LeaveNotificationRespVO> respVOs = new ArrayList<>(pageResult.getList().size());
        for (LeaveNotificationDO notification : pageResult.getList()) {
            LeaveNotificationRespVO respVO = LeaveNotificationConvert.INSTANCE.convert(notification);

            // 填充统计数据
            respVO.setNotFilledCount(Math.toIntExact(leaveNotificationTraineeMapper.countByNotificationIdAndStatus(
                    notification.getId(), LeaveNotificationFillStatusEnum.NOT_FILLED.getStatus())));
            respVO.setLeavingCount(Math.toIntExact(leaveNotificationTraineeMapper.countByNotificationIdAndStatus(
                    notification.getId(), LeaveNotificationFillStatusEnum.LEAVING.getStatus())));
            respVO.setStayingCount(Math.toIntExact(leaveNotificationTraineeMapper.countByNotificationIdAndStatus(
                    notification.getId(), LeaveNotificationFillStatusEnum.STAYING.getStatus())));

            respVOs.add(respVO);
        }

        return new PageResult<>(respVOs, pageResult.getTotal());
    }

    @Override
    public void toggleTraineeMobileMask(Long traineeId, Long notificationId) {
        // 获取学员填报记录
        LeaveNotificationTraineeDO trainee = leaveNotificationTraineeMapper.selectByNotificationIdAndTraineeId(
                notificationId, traineeId);
        if (trainee == null) {
            throw ServiceExceptionUtil.exception(LEAVE_NOTIFICATION_TRAINEE_NOT_EXISTS);
        }

        // 切换脱敏状态
        trainee.setMobileMasked(!trainee.getMobileMasked());
        leaveNotificationTraineeMapper.updateById(trainee);
    }

    @Override
    public void batchNotifyTrainees(LeaveNotificationBatchNotifyReqVO reqVO) {
        // 校验离校报备存在
        validateLeaveNotificationExists(reqVO.getNotificationId());

        // 遍历学员ID列表，发送通知
        for (Long traineeId : reqVO.getTraineeIds()) {
            LeaveNotificationTraineeDO trainee = leaveNotificationTraineeMapper.selectByNotificationIdAndTraineeId(
                    reqVO.getNotificationId(), traineeId);

            if (trainee != null && LeaveNotificationFillStatusEnum.NOT_FILLED.getStatus().equals(trainee.getStatus())) {
                // 发送通知逻辑
                sendNotification(traineeId, reqVO.getNotificationId());
            }
        }
    }

    /**
     * 校验离校报备是否存在
     *
     * @param id 离校报备ID
     */
    private void validateLeaveNotificationExists(Long id) {
        if (leaveNotificationMapper.selectById(id) == null) {
            throw ServiceExceptionUtil.exception(LEAVE_NOTIFICATION_NOT_EXISTS);
        }
    }

    /**
     * 校验班级状态，确保只能为开班中的班级创建离校报备
     *
     * @param classId 班级ID
     */
    private void validateClassStatus(Long classId) {
        // TODO: 调用班级服务，校验班级状态
        // 示例:
        // ClassDO classDO = classMapper.selectById(classId);
        // if (classDO == null ||
        // !ClassStatusEnum.IN_PROGRESS.getStatus().equals(classDO.getStatus())) {
        // throw ServiceExceptionUtil.exception(CLASS_NOT_IN_PROGRESS);
        // }
    }

    /**
     * 生成离校报备名称
     *
     * @param classId 班级ID
     * @return 离校报备名称
     */
    private String generateLeaveNotificationName(Long classId) {
        // 查询当前班级已有的离校报备次数
        int count = leaveNotificationMapper.selectCount(
                LeaveNotificationDO::getClassId, classId).intValue();
        // 返回第N次离校申请
        return String.format("第%d次离校申请", count + 1);
    }

    /**
     * 初始化学员填报记录
     *
     * @param notificationId 离校报备ID
     * @param classId        班级ID
     */
    private void initTraineeRecords(Long notificationId, Long classId) {
        // TODO: 调用班级服务，获取班级学员列表
        // 示例:
        // List<TraineeDO> trainees = traineeMapper.selectListByClassId(classId);
        // 批量插入学员填报记录
        // List<LeaveNotificationTraineeDO> traineeRecords = new ArrayList<>();
        // for (TraineeDO trainee : trainees) {
        // LeaveNotificationTraineeDO record = new LeaveNotificationTraineeDO();
        // record.setNotificationId(notificationId);
        // record.setTraineeId(trainee.getId());
        // record.setTraineeName(trainee.getName());
        // record.setTraineeMobile(trainee.getMobile());
        // record.setMobileMasked(true);
        // record.setStatus(LeaveNotificationFillStatusEnum.NOT_FILLED.getStatus());
        // traineeRecords.add(record);
        // }
        // leaveNotificationTraineeMapper.insertBatch(traineeRecords);
    }

    /**
     * 发送通知
     *
     * @param traineeId      学员ID
     * @param notificationId 离校报备ID
     */
    private void sendNotification(Long traineeId, Long notificationId) {
        // TODO: 调用消息服务发送通知
        // 示例:
        // messageService.sendMessage(traineeId, "离校报备提醒", "请尽快完成离校报备填写");
    }

    /**
     * 将学员DO列表转换为前端展示的TraineeItem列表
     *
     * @param trainees 学员DO列表
     * @return 学员Item列表
     */
    private List<LeaveNotificationDetailRespVO.TraineeItem> convertToTraineeItems(
            List<LeaveNotificationTraineeDO> trainees) {
        return trainees.stream().map(trainee -> {
            LeaveNotificationDetailRespVO.TraineeItem item = new LeaveNotificationDetailRespVO.TraineeItem();
            item.setId(trainee.getTraineeId());
            item.setName(trainee.getTraineeName());
            item.setMobile(
                    trainee.getMobileMasked() ? maskMobile(trainee.getTraineeMobile()) : trainee.getTraineeMobile());
            item.setMobileMasked(trainee.getMobileMasked());
            item.setStatus(trainee.getStatus());
            item.setFillTime(trainee.getFillTime());
            item.setLeaveReason(trainee.getLeaveReason());
            item.setLeaveAddress(trainee.getLeaveAddress());
            item.setTransportation(trainee.getTransportation());
            item.setExpectedReturnTime(trainee.getExpectedReturnTime());
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 手机号脱敏
     *
     * @param mobile 原手机号
     * @return 脱敏后的手机号
     */
    private String maskMobile(String mobile) {
        if (mobile == null || mobile.length() != 11) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }
}
