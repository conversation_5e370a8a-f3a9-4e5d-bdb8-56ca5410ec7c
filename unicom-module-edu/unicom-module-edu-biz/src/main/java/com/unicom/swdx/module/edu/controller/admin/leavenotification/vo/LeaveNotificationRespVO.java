package com.unicom.swdx.module.edu.controller.admin.leavenotification.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 离校报备 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveNotificationRespVO extends LeaveNotificationBaseVO {

    @ApiModelProperty(value = "ID", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人", example = "芋道")
    private String creator;

    @ApiModelProperty(value = "未填写学员数量", required = true, example = "10")
    private Integer notFilledCount;

    @ApiModelProperty(value = "离校学员数量", required = true, example = "20")
    private Integer leavingCount;

    @ApiModelProperty(value = "不离校学员数量", required = true, example = "5")
    private Integer stayingCount;
}
