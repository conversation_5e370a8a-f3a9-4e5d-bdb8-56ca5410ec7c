package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString(callSuper = true)
public class CourseChangeBaseVO{

    @ApiModelProperty(value = "班次id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "班次名")
    private String className;

    @ApiModelProperty(value = "调课原因")
    private String changeReason;

}
