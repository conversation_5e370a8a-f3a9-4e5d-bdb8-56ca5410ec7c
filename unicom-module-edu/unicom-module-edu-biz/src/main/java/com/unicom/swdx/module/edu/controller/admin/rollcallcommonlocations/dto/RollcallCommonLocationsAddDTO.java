package com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.dto;

import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo.RollcallCommonLocationsBaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 大课考勤、点名签到常用地点新增 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RollcallCommonLocationsAddDTO extends RollcallCommonLocationsBaseVO {

}
