package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免信息导入有问题
public class CadreInfoImportExcelVO {

    @ExcelProperty(value = "序号（黄色为必填项，灰\n色为非必填项）")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String index;

    @ExcelProperty(value = "学员姓名")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String name;

    @ExcelProperty(value = "学员手机号")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String phone;

    @ExcelProperty(value = "学员身份证")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String cardNo;

    @ExcelIgnore
    private String sex;

    @ExcelIgnore
    private String birth;

    @ExcelProperty(value = "学员所在单位")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String unitName;


    @ExcelProperty(value = "文化程度")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String educationalLevelName;


    @ExcelProperty(value = "学员民族")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String ethnicName;

    @ExcelProperty(value = "学员职务")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String position;

    @ExcelProperty(value = "职级")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String jobLevelName;

    @ExcelProperty(value = "政治面貌")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String politicalIdentityName;


    @ExcelProperty(value = "毕业院校")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String graduationSchool;

    @ExcelProperty(value = "学员备注")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String remark;

}

