package com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 班级考勤日历 DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_clock_calendar")
@KeySequence("edu_class_clock_calendar_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassClockCalendarDO extends TenantBaseDO {

    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 早餐考勤，0-开，1-关
     */
    private Integer breakfast;
    /**
     * 午餐考勤，0-开，1-关
     */
    private Integer lunch;
    /**
     * 晚餐考勤，0-开，1-关
     */
    private Integer dinner;
    /**
     * 住宿考勤，0-开，1-关
     */
    private Integer putUp;
    /**
     * 考勤日期
     */
    private LocalDate clockDate;
    /**
     * 是否节假日，0-是，1-否
     */
    private Integer isHoliday;
    /**
     * 班级id
     */
    private Long classId;

}
