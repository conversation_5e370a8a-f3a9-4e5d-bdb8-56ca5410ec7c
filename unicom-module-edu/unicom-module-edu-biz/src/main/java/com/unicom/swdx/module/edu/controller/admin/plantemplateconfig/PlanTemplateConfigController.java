package com.unicom.swdx.module.edu.controller.admin.plantemplateconfig;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplateconfig.PlanTemplateConfigDO;
import com.unicom.swdx.module.edu.convert.plantemplateconfig.PlanTemplateConfigConvert;
import com.unicom.swdx.module.edu.service.plantemplateconfig.PlanTemplateConfigService;

@Api(tags = "管理后台 - 教学计划模版配置")
@RestController
@RequestMapping("/edu/plan-template-config")
@Validated
public class PlanTemplateConfigController {

    @Resource
    private PlanTemplateConfigService planTemplateConfigService;

    @PostMapping("/create")
    @ApiOperation("创建教学计划模版配置")
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:create')")
    public CommonResult<Long> createPlanTemplateConfig(@Valid @RequestBody PlanTemplateConfigCreateReqVO createReqVO) {
        return success(planTemplateConfigService.createPlanTemplateConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新教学计划模版配置")
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:update')")
    public CommonResult<Boolean> updatePlanTemplateConfig(@Valid @RequestBody PlanTemplateConfigUpdateReqVO updateReqVO) {
        planTemplateConfigService.updatePlanTemplateConfig(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除教学计划模版配置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:delete')")
    public CommonResult<Boolean> deletePlanTemplateConfig(@RequestParam("id") Long id) {
        planTemplateConfigService.deletePlanTemplateConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得教学计划模版配置")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:query')")
    public CommonResult<PlanTemplateConfigRespVO> getPlanTemplateConfig(@RequestParam("id") Long id) {
        PlanTemplateConfigDO planTemplateConfig = planTemplateConfigService.getPlanTemplateConfig(id);
        return success(PlanTemplateConfigConvert.INSTANCE.convert(planTemplateConfig));
    }

    @GetMapping("/list")
    @ApiOperation("获得教学计划模版配置列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:query')")
    public CommonResult<List<PlanTemplateConfigRespVO>> getPlanTemplateConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<PlanTemplateConfigDO> list = planTemplateConfigService.getPlanTemplateConfigList(ids);
        return success(PlanTemplateConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得教学计划模版配置分页")
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:query')")
    public CommonResult<PageResult<PlanTemplateConfigRespVO>> getPlanTemplateConfigPage(@Valid PlanTemplateConfigPageReqVO pageVO) {
        PageResult<PlanTemplateConfigDO> pageResult = planTemplateConfigService.getPlanTemplateConfigPage(pageVO);
        return success(PlanTemplateConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出教学计划模版配置 Excel")
    @PreAuthorize("@ss.hasPermission('edu:plan-template-config:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanTemplateConfigExcel(@Valid PlanTemplateConfigExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PlanTemplateConfigDO> list = planTemplateConfigService.getPlanTemplateConfigList(exportReqVO);
        // 导出 Excel
        List<PlanTemplateConfigExcelVO> datas = PlanTemplateConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "教学计划模版配置.xls", "数据", PlanTemplateConfigExcelVO.class, datas);
    }

}
