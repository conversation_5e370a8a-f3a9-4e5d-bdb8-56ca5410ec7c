package com.unicom.swdx.module.edu.controller.admin.evaluationresponse;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.security.core.LoginUser;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationStatsExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata.HistoryDataPageRespVO;
import com.unicom.swdx.module.edu.convert.evaluation.EvaluationResponseConvert;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.edu.service.evaluationhistory.EvaluationHistoryService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.system.api.businesscenter.BusinessCenterApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

@Api(tags = "管理后台 - 课程评价记录")
@RestController
@RequestMapping("/edu/evaluation-response")
@Validated
@Slf4j
public class EvaluationResponseController {

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private ClassManagementService classManagementService;

    @Resource
    private BusinessCenterApi businessCenterApi;

    @Resource
    private EvaluationHistoryService evaluationHistoryService;

    @PostMapping("/myEvaluation/page")
    @ApiOperation("我的评估-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<MyEvaluationPageRespVO>> getMyEvaluationPage(@Valid @RequestBody MyEvaluationPageReqVO reqVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setManyIds(null);
        PageResult<MyEvaluationPageRespVO> pageResult = evaluationResponseService.getMyEvaluationPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/myEvaluation/export")
    @ApiOperation("我的评估-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportMyEvaluationExcel(@Valid @RequestBody MyEvaluationPageReqVO reqVO,
                                        HttpServletResponse response) throws IOException {
        List<MyEvaluationExcelVO> list = evaluationResponseService.getExportMyEvaluationExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "我的评估.xlsx",
                "数据", MyEvaluationExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/myEvaluation/detail")
    @ApiOperation("我的评估-评分详情")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<MyEvaluationDetailPageRespVO>> getMyEvaluationDetail(@Valid @RequestBody MyEvaluationDetailPageReqVO reqVO) {
        PageResult<MyEvaluationDetailPageRespVO> pageResult = evaluationResponseService.getMyEvaluationDetail(reqVO);
        return success(pageResult);
    }

    @PostMapping("/teacherEvaluation/list")
    @ApiOperation("教师移动端-课评查看old")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<List<TeacherEvaluationResponseVO>> getTeacherEvaluationList(@Valid @RequestBody EvaluationResponsePageReqVO reqVO) {
        List<TeacherEvaluationResponseVO> resultList = evaluationResponseService.getTeacherEvaluationList(reqVO);
        return success(resultList);
    }

    @GetMapping("/getCourseEvaluationForApp")
    @ApiOperation("教师移动端-课评查看")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classId", value = "班级id", example = "1", required = true, dataTypeClass = Long.class),
            @ApiImplicitParam(name = "isDone", value = "是否完成评课（0 未完成， 1已完成）", example = "0", required = true, dataTypeClass = Boolean.class)
    })
    public CommonResult<List<CourseEvaluationResponseVO>> getCourseEvaluationForApp(@RequestParam(value = "classId") Long classId,
                                                                                    @RequestParam(value = "isDone") Boolean isDone) {
        List<CourseEvaluationResponseVO> resultList = evaluationResponseService.getCourseEvaluationForApp(classId, isDone);
        return success(resultList);
    }

    @GetMapping("/getCourseEvaluationTraineeDetailForApp")
    @ApiOperation("教师移动端-课评查看-选修课、专题课查看学员评估详情")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classCourseId", value = "排课id", example = "1", required = true, dataTypeClass = Long.class),
            @ApiImplicitParam(name = "courseId", value = "课程id (不传null则 表示选修课查看详情)", example = "1", required = false, dataTypeClass = Long.class),
            @ApiImplicitParam(name = "isDone", value = "是否完成评课（0 未完成， 1已完成）", example = "1", required = true, dataTypeClass = Boolean.class)
    })
    public CommonResult<List<CourseEvaluationTraineeDetailVO>> getCourseEvaluationTraineeDetailForApp(@RequestParam(value = "classCourseId") Long classCourseId,
                                                                                   @RequestParam(value = "courseId", required = false) Long courseId,
                                                                                   @RequestParam(value = "isDone") Boolean isDone) {
        List<CourseEvaluationTraineeDetailVO> resultList = evaluationResponseService.getCourseEvaluationTraineeDetailForApp(classCourseId, courseId, isDone);
        return success(resultList);
    }

    @GetMapping("/getClassUnfinished")
    @Operation(summary = "获得评估列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<Long> countClassUnfinished(@RequestParam(value = "classId", required = false) Long classId) {
        Long unfinished = evaluationResponseService.countClassUnfinished(classId);
        return success(unfinished);
    }

    @PostMapping("/myEvaluation/exportByDetail")
    @ApiOperation("课评详情(我的评估)-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportMyEvaluationExcelByDetail(@Valid @RequestBody MyEvaluationPageReqVO reqVO,
                                                HttpServletResponse response) throws IOException {
        List<MyEvaluationDetailExcelVO> list = evaluationResponseService.getExportMyEvaluationExcelByDetail(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "课评详情.xlsx",
                "数据", MyEvaluationDetailExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/teacherEvaluation/page")
    @ApiOperation("教师评估-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<TeacherEvaluationPageRespVO>> getTeacherEvaluationPage(@Valid @RequestBody MyEvaluationPageReqVO reqVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setManyIds(null);
        // 全部教师权限
        reqVO.setIsTeacher(false);
        PageResult<TeacherEvaluationPageRespVO> pageResult = evaluationResponseService.getTeacherEvaluationPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/teacherEvaluation/export")
    @ApiOperation("教师评估-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportTeacherEvaluationExcel(@Valid @RequestBody MyEvaluationPageReqVO reqVO,
                                             HttpServletResponse response) throws IOException {
        List<TeacherEvaluationExcelVO> list = evaluationResponseService.getExportTeacherEvaluationExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "教师评估.xlsx",
                "数据", TeacherEvaluationExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/allPage")
    @ApiOperation("分页查询所有学员的详情")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<AllEvaluationPageRespVO>> getAllEvaluationPage(@Valid @RequestBody AllEvaluationPageReqVO reqVO) {
        PageResult<AllEvaluationPageRespVO> pageResult = evaluationResponseService.getAllEvaluationPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/distributeUnhandled")
    @ApiOperation("给未评人员重新下发问卷")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<Boolean> distributeUnhandled(@RequestBody DistributeVO reqVO) {
        evaluationResponseService.distribute(reqVO);
        return success(true);
    }

    @PostMapping("/revoke")
    @ApiOperation("撤回")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<Boolean> revoke(@RequestBody DistributeVO reqVO) {
        evaluationResponseService.revoke(reqVO);
        return success(true);
    }

    @PostMapping("/deptEvaluation/page")
    @ApiOperation("部门评估-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<DeptEvaluationPageRespVO>> getDeptEvaluationPage(@Valid @RequestBody DeptEvaluationPageReqVO reqVO,
                                                                                    HttpServletRequest request) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setIds(null);
        reqVO.setManyIds(null);
        // 设置部门权限
        reqVO.setDeptIdList(getUserDeptPerminssion(request));
        PageResult<DeptEvaluationPageRespVO> pageResult = evaluationResponseService.getDeptEvaluationPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/deptEvaluation/export")
    @ApiOperation("部门评估-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportDeptEvaluationExcel(@Valid @RequestBody DeptEvaluationPageReqVO reqVO,
                                          HttpServletResponse response,
                                          HttpServletRequest request) throws IOException {
        // 设置部门权限
        reqVO.setDeptIdList(getUserDeptPerminssion(request));
        List<DeptEvaluationExcelVO> list = evaluationResponseService.getExportDeptEvaluationExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "部门评估.xlsx",
                "数据", DeptEvaluationExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/classEvaluationForBusinessCenter")
    @ApiOperation("课程评估-业中首页-仪表盘")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    @TenantIgnore
    @PermitAll
    public CommonResult<ClassEvaluationForBusinessCenterRespVO> classEvaluationForBusinessCenter(@Valid @RequestBody ClassEvaluationForBusinessCenterReqVO reqVO) {
        ClassEvaluationForBusinessCenterRespVO result = evaluationResponseService.classEvaluationForBusinessCenter(reqVO);
        return success(result);
    }

    @PostMapping("/teacherEvaluationStats/page")
    @ApiOperation("教师评估统计-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<DeptEvaluationPageRespVO>> getTeacherEvaluationStatsPage(@Valid @RequestBody DeptEvaluationPageReqVO reqVO,
                                                                                    HttpServletRequest request) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setIds(null);
        reqVO.setManyIds(null);
        PageResult<DeptEvaluationPageRespVO> pageResult = evaluationResponseService.getDeptEvaluationPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/teacherEvaluationStats/export")
    @ApiOperation("教师评估统计-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportTeacherEvaluationStatsExcel(@Valid @RequestBody DeptEvaluationPageReqVO reqVO,
                                          HttpServletResponse response,
                                          HttpServletRequest request) throws IOException {
        List<DeptEvaluationExcelVO> list = evaluationResponseService.getExportDeptEvaluationExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "教师评估统计.xlsx",
                "数据", DeptEvaluationExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/classEvaluationStats/page")
    @ApiOperation("班次评估统计-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<ClassEvaluationStatsPageRespVO>> getClassEvaluationStatsPage(@Valid @RequestBody ClassEvaluationStatsPageReqVO reqVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setIds(null);
        // 班主任设置班级查看范围
        if (Boolean.TRUE.equals(reqVO.getIsHeadTeacher())) {
            reqVO.setClassIdList(classManagementService.getLoginUserClassMasterLimitClassList());
        }
        PageResult<ClassEvaluationStatsPageRespVO> pageResult = evaluationResponseService.getClassEvaluationStatsPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/classEvaluationStats/export")
    @ApiOperation("班次评估统计-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportClassEvaluationStatsExcel(@Valid @RequestBody ClassEvaluationStatsPageReqVO reqVO,
                                                HttpServletResponse response) throws IOException {
        // 班主任设置班级查看范围
        if (Boolean.TRUE.equals(reqVO.getIsHeadTeacher())) {
            reqVO.setClassIdList(classManagementService.getLoginUserClassMasterLimitClassList());
        }
        List<ClassEvaluationStatsExcelVO> list = evaluationResponseService.getExportClassEvaluationStatsExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "班次评估统计.xlsx",
                "数据", ClassEvaluationStatsExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/classEvaluationStats/traineeEvaluationDetailPage")
    @ApiOperation("班次评估统计-学员参评情况-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<TraineeEvaluationDetailPageRespVO>> getTraineeEvaluationDetailPage(@Valid @RequestBody TraineeEvaluationDetailPageReqVO reqVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setIds(null);
        PageResult<TraineeEvaluationDetailPageRespVO> pageResult = evaluationResponseService.getTraineeEvaluationDetailPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/classEvaluationStats/traineeEvaluationDetailExport")
    @ApiOperation("班次评估统计-学员参评情况-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportTraineeEvaluationDetailExcel(@Valid @RequestBody TraineeEvaluationDetailPageReqVO reqVO,
                                                   HttpServletResponse response) throws IOException {
        // 导出 Excel 班次名称+参评详情.xlsx
        evaluationResponseService.exportTraineeEvaluationDetailExcel(reqVO, response);
    }

    @PostMapping("/classEvaluationStats/traineeEvaluatedAndUnEvaluatedDetailPage")
    @ApiOperation("班次评估统计-学员参评情况-学员已评、未评详情-分页")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO>> getTraineeEvaluatedAndUnEvaluatedDetailPage(@Valid @RequestBody TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setIds(null);
        PageResult<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> pageResult = evaluationResponseService.getTraineeEvaluatedAndUnEvaluatedDetailPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/classEvaluationStats/traineeEvaluatedAndUnEvaluatedDetailExport")
    @ApiOperation("班次评估统计-学员已评、未评详情-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportTraineeEvaluatedAndUnEvaluatedDetailExcel(@Valid @RequestBody TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO,
                                                                HttpServletResponse response) throws IOException {
        // 导出 Excel 学员名称+已评评详情.xls
        evaluationResponseService.exportTraineeEvaluatedAndUnEvaluatedDetailExcel(reqVO, response);
    }

    @PostMapping("/classEvaluationStats/courseTraineeEvaluationDetailPage")
    @ApiOperation("班次评估统计-课评详情-学员详情-分页")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<CourseTraineeEvaluationDetailPageRespVO>> getCourseTraineeEvaluationDetailPage(@Valid @RequestBody MyEvaluationDetailPageReqVO reqVO) {
        PageResult<CourseTraineeEvaluationDetailPageRespVO> pageResult = evaluationResponseService.getCourseTraineeEvaluationDetailPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/deptEvaluationStats/page")
    @ApiOperation("部门评估统计-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<DeptEvaluationPageRespVO>> getDeptEvaluationStatsPage(@Valid @RequestBody DeptEvaluationPageReqVO reqVO,
                                                                                         HttpServletRequest request) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        reqVO.setIds(null);
        reqVO.setManyIds(null);
        PageResult<DeptEvaluationPageRespVO> pageResult = evaluationResponseService.getDeptEvaluationStatsPage(reqVO);
        return success(pageResult);
    }

    @PostMapping("/deptEvaluationStats/export")
    @ApiOperation("部门评估统计-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportDeptEvaluationStatsExcel(@Valid @RequestBody DeptEvaluationPageReqVO reqVO,
                                               HttpServletResponse response,
                                               HttpServletRequest request) throws IOException {
        List<DeptEvaluationStatsExcelVO> list = evaluationResponseService.getExportDeptEvaluationStatsExcel(reqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "部门评估统计.xlsx",
                "数据", DeptEvaluationStatsExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/historyData/page")
    @ApiOperation("历史评价数据-分页查询")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:query')")
    public CommonResult<PageResult<HistoryDataPageRespVO>> getHistoryDataPage(
            @Valid @RequestBody HistoryDataPageReqVO reqVO) {
        // 屏蔽用于导出查询条件中的id集合，避免影响分页查询
        PageResult<HistoryDataPageRespVO> pageResult = evaluationHistoryService.getHistoryDataPage(reqVO);

        // 添加序号字段
        List<HistoryDataPageRespVO> list = pageResult.getList();
        int startIndex = (reqVO.getPageNo() - 1) * reqVO.getPageSize() + 1;
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(startIndex + i);
        }

        return success(pageResult);
    }

    @PostMapping("/historyData/export")
    @ApiOperation("历史评价数据-导出")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-response:export')")
    public void exportHistoryDataExcel(@Valid @RequestBody HistoryDataPageReqVO reqVO,
            HttpServletResponse response) throws IOException {
        // 使用分页请求参数但不限制数量，保持查询条件一致
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE); // 导出全部数据

        List<HistoryDataExcelVO> list = evaluationHistoryService.getHistoryDataExportList(reqVO);

        // 添加序号字段，与分页查询保持一致的序号起始规则
        int startIndex = 1; // 导出从1开始
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(startIndex + i);
        }

        // 导出 Excel（支持动态列筛选）
        // 列顺序：1课程名称 2教学形式 3授课时间 4部门 5师资来源 6教师姓名 7班次 8学员数量 9平均分 10排名分
        ExcelUtils.writeByIncludeColumnIndexes(response, "历史评价数据.xlsx",
                "数据", HistoryDataExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    private String getTokenFromRequestHead(HttpServletRequest request) {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        // 如果token以"Bearer "开头，则提取实际的token值
        if (token != null && token.startsWith("Bearer ")) {
            // 去除"Bearer "部分
            token = token.substring(7);
        }
        log.info("获得请求头Authorization:{}", token);
        return token;
    }

    /**
     * 从业中获取登录用户部门权限
     *
     * @param request 请求
     * @return 部门id集合
     */
    private List<Long> getUserDeptPerminssion(HttpServletRequest request) {
        List<Long> deptIdList = new ArrayList<>();
        // 获取登录用户部门权限（多部门）
        String token = this.getTokenFromRequestHead(request);
        LoginUser loginUser = getLoginUser();
        if (Objects.nonNull(loginUser)) {
            deptIdList = businessCenterApi.getUserDeptPermissionByUserId(loginUser.getTenantId(), null, loginUser.getId(), token).getCheckedData();
        }
        return deptIdList;
    }
}
