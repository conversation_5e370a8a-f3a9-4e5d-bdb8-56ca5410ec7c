package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@ApiModel(description = "周课表查询参数")
public class WeekTimetableReqVO {

    @ApiModelProperty(value = "班级id",required = true)
    private Long classId;

    @ApiModelProperty(value = "开始日期",required = true)
    private String dateBeg;

    @ApiModelProperty(value = "结束日期",required = true)
    private String dateEnd;

    @ApiModelProperty(value = "教学计划id",required = false)
    private Long planId;
}
