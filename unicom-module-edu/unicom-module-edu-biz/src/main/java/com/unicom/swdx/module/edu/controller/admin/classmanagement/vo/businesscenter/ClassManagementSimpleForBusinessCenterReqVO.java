package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
* 业中首页-仪表盘- 获取班级下钻 req VO
*/
@Data
@ApiModel("业中首页-仪表盘- 获取班级下钻 req VO")
public class ClassManagementSimpleForBusinessCenterReqVO {

    @ApiModelProperty(value = "年度", example = "1")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期 不填全部", example = "1")
    private Integer classTerm;

    @ApiModelProperty(value = "开始时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-11-08")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "租户ID", example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

}
