package com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@ApiModel(value = "参训系统 - 站内信 Excel 导出 Request VO", description = "参数和 NotificationMessagePageReqVO 是一致的")
@Data
public class NotificationMessageExportVO {

    @ApiModelProperty(value = "单位id")
    private Integer unit;

    @ApiModelProperty(value = "已读未读，1-未读，2-已读")
    private Integer isRead;

    @ApiModelProperty(value = "发布人")
    private String publisher;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "通知公告内容")
    private String content;

    @ApiModelProperty(value = "是否置顶,1-是，0-否")
    private Integer isTop;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "1-发布，2-存草稿箱")
    @NotNull(message = "isPublish不能为空")
    private Integer isPublish;

    @ApiModelProperty(value = "状态，1-上架，0-下架")
    private Integer status;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "0-升序，1-降序")
    @NotNull(message = "change不能为空")
    private Integer change;

    @ApiModelProperty(value = "1-标题，2-发布时间，3-存草稿箱时间")
    @NotNull(message = "tag不能为空")
    private Integer tag;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "截止时间")
    private String endTime;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "id列表")
    private List<Long> idList;
}
