package com.unicom.swdx.module.edu.dal.dataobject.electiverelease;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 选修课发布信息 DO
 *
 * <AUTHOR>
 */
@TableName("edu_elective_release")
@KeySequence("edu_elective_release_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElectiveReleaseDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 发布名称
     */
    private String name;
    /**
     * 选学开始时间
     */
    private LocalDateTime selectionStartTime;
    /**
     * 选学结束时间
     */
    private LocalDateTime selectionEndTime;
    /**
     * 上课日期
     */
    private LocalDate classDate;
    /**
     * 午别时间 0-上午 1-下午 2-晚上
     */
    private Integer dayPeriod;
    /**
     * 上课时段开始时间
     */
    private LocalDateTime classStartTime;
    /**
     * 上课时段结束时间
     */
    private LocalDateTime classEndTime;
    /**
     * 系统内部门
     */
    private Long deptId;

}
