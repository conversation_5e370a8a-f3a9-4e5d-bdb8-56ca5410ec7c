package com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "服务通知配置 response VO")
public class XcxMsgConfigRespVO extends XcxMsgConfigBaseVO{

    @ApiModelProperty(value = "配置id")
    private Long id;

    @ApiModelProperty(value = "隶属模块：任课教师授课提醒-1，住宿打卡提醒-2，教学评估提醒-3")
    private Integer tag;

}
