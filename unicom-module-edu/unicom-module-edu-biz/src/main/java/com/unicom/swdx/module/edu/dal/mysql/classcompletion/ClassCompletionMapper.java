package com.unicom.swdx.module.edu.dal.mysql.classcompletion;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.TraineeScoreVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 结业考核模版设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCompletionMapper extends BaseMapperX<ClassCompletionDO> {

    /**
     * 根据班级id获取结业信息
     * @param classId 班级id
     */
    default List<ClassCompletionDO> listByClassId(Long classId) {
        LambdaQueryWrapper<ClassCompletionDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ClassCompletionDO::getClassId, classId);
//        wrapper.orderByAsc(ClassCompletionDO::getSerialNumber);
        wrapper.last("ORDER BY (REGEXP_REPLACE(serial_number, '[^0-9]', ''))::INTEGER, serial_number");
        return selectList(wrapper);
    }

    List<ClassCompletionDO> getInfoByClassId(@Param("classId") Integer classId);

    /**
     * 根据班级id删除所有信息
     * @param ids
     */
    default void deleteBatchClassIds(List<Long> ids) {
        LambdaQueryWrapper<ClassCompletionDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ClassCompletionDO::getClassId,ids);

        List<Long> idList = selectList(wrapper).stream().map(ClassCompletionDO::getId).collect(Collectors.toList());

        if(CollUtil.isEmpty(idList)){
            return;
        }

        this.deleteBatchIds(idList);

    }

    List<ClassCompletionDO> getDefaultRuleInfoByClassId(@Param("classDO") ClassManagementDO classDO);

    List<ClassCompletionDO> getTemplateByIdCode(@Param("idCode") String idCode);

    List<Map<String, String>> getScoreBySerials(@Param("serialList") List<String> serialList, @Param("classId") Long classId,@Param("idCode") String idCode);
    List<TraineeScoreVO> getScoreList(@Param("classId") Long classId, @Param("idCode") String idCode);
}
