package com.unicom.swdx.module.edu.controller.admin.plandetail.vo;

import lombok.*;

import java.time.LocalDateTime;

import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 教学计划详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanDetailPageReqVO extends PageParam {

    @ApiModelProperty(value = "教学计划id")
    private Long planId;

    @ApiModelProperty(value = "日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] date;

    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）")
    private String period;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] beginTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

}
