package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@ApiModel("管理后台 - 课程更改 Request VO")
@Data
@ToString(callSuper = true)
public class CourseChangeTimeReqVO {

    @ApiModelProperty(value = "班次ID")
    private Long classId;

}
