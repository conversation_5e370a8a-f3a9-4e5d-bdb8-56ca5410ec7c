package com.unicom.swdx.module.edu.service.training;

import cn.hutool.core.collection.CollectionUtil;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @ClassName: TraineeDictConvertUtil
 * @Author: lty
 * @Date: 2024/10/24 8:53
 */
@Service
public class TraineeDictConvertService {
    @Resource
    private TraineeMapper traineeMapper;

    public Map<String, Map<String, Long>> getDictDateMap(@Param("type") String type){
        return traineeMapper.getDictDateMap(type);
    }



    public Map<Long, Map<String, String>> getDictDateMapById(@Param("type") String type){
        return traineeMapper.getDictDateMapById(type);
    }
    public Map<String, Map<String, String>> getDictDateMapById1(@Param("type") String type){
        return traineeMapper.getDictDateMapById1(type);
    }



    public Map<Long, Map<String, String>> getDictDateMapByIdandTenantId(@Param("type") String type ,Long tenantId){


        //找不到的话 使用省委党校的
        Map<Long, Map<String, String>> temple=   traineeMapper.getDictDateMapByIdandTenant(type ,tenantId);

        if(CollectionUtil.isEmpty(temple)){
            temple=   traineeMapper.getDictDateMapByIdandTenant(type ,25L);
        }

        return temple;
    }
    public Map<String, Map<String, String>> getDictDateMapByIdandTenantId1(@Param("type") String type ,Long tenantId){

        //找不到的话 使用省委党校的
        Map<String, Map<String, String>> temple=   traineeMapper.getDictDateMapByIdandTenant1(type ,tenantId);

        if(CollectionUtil.isEmpty(temple)){
            temple=   traineeMapper.getDictDateMapByIdandTenant1(type ,25L);
        }

        return temple;

    }
}
