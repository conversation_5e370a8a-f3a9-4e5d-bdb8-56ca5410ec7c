package com.unicom.swdx.module.edu.service.plantemplateconfig;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplateconfig.PlanTemplateConfigDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 教学计划模版配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PlanTemplateConfigService {

    /**
     * 创建教学计划模版配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanTemplateConfig(@Valid PlanTemplateConfigCreateReqVO createReqVO);

    /**
     * 更新教学计划模版配置
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanTemplateConfig(@Valid PlanTemplateConfigUpdateReqVO updateReqVO);

    /**
     * 删除教学计划模版配置
     *
     * @param id 编号
     */
    void deletePlanTemplateConfig(Long id);

    /**
     * 获得教学计划模版配置
     *
     * @param id 编号
     * @return 教学计划模版配置
     */
    PlanTemplateConfigDO getPlanTemplateConfig(Long id);

    /**
     * 获得教学计划模版配置列表
     *
     * @param ids 编号
     * @return 教学计划模版配置列表
     */
    List<PlanTemplateConfigDO> getPlanTemplateConfigList(Collection<Long> ids);

    /**
     * 获得教学计划模版配置分页
     *
     * @param pageReqVO 分页查询
     * @return 教学计划模版配置分页
     */
    PageResult<PlanTemplateConfigDO> getPlanTemplateConfigPage(PlanTemplateConfigPageReqVO pageReqVO);

    /**
     * 获得教学计划模版配置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 教学计划模版配置列表
     */
    List<PlanTemplateConfigDO> getPlanTemplateConfigList(PlanTemplateConfigExportReqVO exportReqVO);

}
