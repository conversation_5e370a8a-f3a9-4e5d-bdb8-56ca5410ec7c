package com.unicom.swdx.module.edu.controller.admin.frequency.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 使用次数 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class FrequencyExcelVO {

    @ExcelProperty("序号")
    private Long id;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("使用次数")
    private Long numberOfTimes;

    @ExcelProperty("创建时间")
    private Date createTime;

}
