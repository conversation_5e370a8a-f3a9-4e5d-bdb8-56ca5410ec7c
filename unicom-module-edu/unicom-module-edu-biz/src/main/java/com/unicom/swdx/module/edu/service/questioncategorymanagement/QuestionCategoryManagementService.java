package com.unicom.swdx.module.edu.service.questioncategorymanagement;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 题目类别管理 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionCategoryManagementService {

    /**
     * 创建题目类别管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionCategoryManagement(@Valid QuestionCategoryManagementSaveReqVO createReqVO);

    /**
     * 更新题目类别管理
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionCategoryManagement(@Valid QuestionCategoryManagementSaveReqVO updateReqVO);

    /**
     * 删除题目类别管理
     *
     * @param id 编号
     */
    void deleteQuestionCategoryManagement(Long id);

    /**
     * 获得题目类别管理
     *
     * @param id 编号
     * @return 题目类别管理
     */
    QuestionCategoryManagementDO getQuestionCategoryManagement(Long id);

    /**
     * 获得题目类别管理列表
     *
     * @param listReqVO 查询条件
     * @return 题目类别管理列表
     */
    List<QuestionCategoryManagementDO> getQuestionCategoryManagementList(QuestionCategoryManagementListReqVO listReqVO);

}