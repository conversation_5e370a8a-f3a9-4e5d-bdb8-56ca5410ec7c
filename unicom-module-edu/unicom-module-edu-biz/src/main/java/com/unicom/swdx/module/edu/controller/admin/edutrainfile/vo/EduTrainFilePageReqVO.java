package com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class EduTrainFilePageReqVO extends PageParam {

    private String title;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

}
