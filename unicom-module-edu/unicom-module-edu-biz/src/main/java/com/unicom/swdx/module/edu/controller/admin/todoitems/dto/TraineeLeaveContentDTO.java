package com.unicom.swdx.module.edu.controller.admin.todoitems.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 班主任待办事项 请假申请内容详情
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraineeLeaveContentDTO {

    /**
     * 请假标题
     */
    private String leaveTitle;
    /**
     * 请假天数
     */
    private Float leaveDay;

    /**
     * 请假类型
     */
    private String leaveType;
    /**
     * 请假状态
     */
    private Integer leaveStatus;



    /**
     * 该类转json字符串
     */
    public String toJsonString() {
        return "{\"leaveTitle\":" + leaveTitle
                + ",\"leaveDay\":" + leaveDay
                + ",\"leaveType\":" + leaveType
                + ",\"leaveStatus\":" + leaveStatus
                + "}";
    }
}
