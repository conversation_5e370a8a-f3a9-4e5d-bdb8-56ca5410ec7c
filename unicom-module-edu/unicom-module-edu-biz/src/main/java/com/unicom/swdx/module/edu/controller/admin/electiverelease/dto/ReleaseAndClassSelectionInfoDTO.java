package com.unicom.swdx.module.edu.controller.admin.electiverelease.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 课程发布信息-班级 的应选人数信息
 * @date 2024-10-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReleaseAndClassSelectionInfoDTO {

    /**
     * 课程发布信息id
     */
    private Long releaseId;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 应选人数
     */
    private Integer selectionNum;

}
