package com.unicom.swdx.module.edu.dal.dataobject.coursechange;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 调课记录实体类
 *
 * @Author: youxiaoyan
 */
@TableName(value = "edu_course_change",autoResultMap = true)
@KeySequence("edu_course_change_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseChangeDO extends TenantBaseDO {

    /**
     * 调课主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 调课时间
     */
    private LocalDateTime changeTime;

    /**
     * 调课类型
     */
    private Integer changeType;

    /**
     * 调课人id
     */
    private Long changeUserId;

    /**
     * 调课人姓名
     */
    private String changeUserName;

    /**
     * 调课理由
     */
    private String changeReason;

    /**
     * 调课前课程信息
     */
    private String classInfoBefore;

    /**
     * 调课后课程信息
     */
    private String classInfoAfter;

    /**
     * 课表id
     */
    private Long classCourseId;
}
