package com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 结业考核模版设置更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassCompletionTemplateUpdateReqVO extends ClassCompletionTemplateBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    @NotNull(message = "主键id，自增不能为空")
    private Long id;

}
