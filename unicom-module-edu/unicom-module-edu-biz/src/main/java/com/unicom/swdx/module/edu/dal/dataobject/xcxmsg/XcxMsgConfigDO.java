package com.unicom.swdx.module.edu.dal.dataobject.xcxmsg;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

@TableName(value = "edu_xcxmsg_config",autoResultMap = true)
@KeySequence("edu_xcxmsg_config_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XcxMsgConfigDO  extends TenantBaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    //配置标题
    private String title;

    //通知内容
    private String informContent;

    //通知时间
    private String informTime;

    //提示说明
    private String remark;

    //状态 0禁用 1启用
    private Boolean status;

    //隶属模块：隶属模块：任课教师授课提醒-1，住宿打卡提醒-2，教学评估提醒-3
    private Integer tag;

}
