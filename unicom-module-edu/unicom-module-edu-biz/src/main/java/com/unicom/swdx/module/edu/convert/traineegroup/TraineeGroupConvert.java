package com.unicom.swdx.module.edu.convert.traineegroup;

import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupExcelVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * EduClassroomLibrary Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TraineeGroupConvert {

    TraineeGroupConvert INSTANCE = Mappers.getMapper(TraineeGroupConvert.class);

    List<TraineeGroupExcelVO> convertList(List<TraineeGroupRespVO> tmpList);
}
