package com.unicom.swdx.module.edu.convert.coursechange;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangeExcelVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.coursechange.CourseChangeDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CourseChangeConvert {

    CourseChangeConvert INSTANCE = Mappers.getMapper(CourseChangeConvert.class);

    List<CourseChangeExcelVO> convertExportList(List<CourseChangeDO> list);

}
