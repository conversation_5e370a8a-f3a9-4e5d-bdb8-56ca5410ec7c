package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 首页班级列表VO")
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class ClassHaveCourseVO {

    @ApiModelProperty(value = "班级主键")
    private Long id;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "开班日期")
    private String classOpenTime;

    @ApiModelProperty(value = "结业日期")
    private String completionTime;

}
