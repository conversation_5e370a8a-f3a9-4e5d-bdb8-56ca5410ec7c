package com.unicom.swdx.module.edu.controller.admin.classroomlibrary;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseClassTimeClassroomReqVO;
import com.unicom.swdx.module.edu.convert.classroomlibrary.ClassroomLibraryConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import com.unicom.swdx.module.edu.dal.mysql.classroomlibrary.ClassroomLibraryMapper;
import com.unicom.swdx.module.edu.service.classroomlibrary.ClassroomLibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;


@Api(tags = "管理后台 - 教室库管理")
@RestController
@RequestMapping("/edu/classroom-library")
@Validated
public class ClassroomLibraryController {

    @Resource
    private ClassroomLibraryService classroomLibraryService;
    @Resource
    private ClassroomLibraryMapper classroomLibraryMapper;

    @PostMapping("/create")
    @ApiOperation("创建EduClassroomLibrary")
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:create')")
    public CommonResult<Integer> createClassroomLibrary(@Valid @RequestBody ClassroomLibraryCreateReqVO createReqVO) {
        return success(classroomLibraryService.createClassroomLibrary(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新EduClassroomLibrary")
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:update')")
    public CommonResult<Boolean> updateClassroomLibrary(@Valid @RequestBody ClassroomLibraryUpdateReqVO updateReqVO) {
        classroomLibraryService.updateClassroomLibrary(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除EduClassroomLibrary")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:delete')")
    public CommonResult<Boolean> deleteClassroomLibrary(@RequestParam("id") Integer id) {
        classroomLibraryService.deleteClassroomLibrary(id);
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除EduClassroomLibrary")
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:delete')")
    public CommonResult<Boolean> deleteClassroomLibraryBatch(@Valid @RequestBody ClassroomLibraryDeleteVO classroomLibraryDeleteVO) {
        classroomLibraryService.deleteClassroomLibraryBatch(classroomLibraryDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得EduClassroomLibrary")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:query')")
    public CommonResult<ClassroomLibraryRespVO> getClassroomLibrary(@RequestParam("id") Integer id) {
        ClassroomLibraryDO classroomLibrary = classroomLibraryService.getClassroomLibrary(id);
        return success(ClassroomLibraryConvert.INSTANCE.convert(classroomLibrary));
    }

    @GetMapping("/page")
    @ApiOperation("获得EduClassroomLibrary分页")
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:query')")
    public CommonResult<PageResult<ClassroomLibraryRespVO>> getClassroomLibraryPage(@Valid ClassroomLibraryPageReqVO pageVO) {
        PageResult<ClassroomLibraryDO> pageResult = classroomLibraryService.getClassroomLibraryPage(pageVO);
        return success(ClassroomLibraryConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/list-for-elective-release")
    @ApiOperation("选修课管理-根据选修课发布上课时间段获取空闲下拉教室数据")
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:query')")
    public CommonResult<List<ClassroomLibrarySimpleRespVO>> listForElectiveRelease(@Valid @RequestBody ElectiveReleaseClassTimeClassroomReqVO reqVO) {
        List<ClassroomLibrarySimpleRespVO> teacherInformationSimpleRespVOList = classroomLibraryService.listForElectiveRelease(reqVO);
        return success(teacherInformationSimpleRespVOList);
    }


    @PostMapping("/export")
    @ApiOperation("导出教室库")
    @PreAuthorize("@ss.hasPermission('edu:classroom-library:export')")
    public void exportClassroomLibrary(HttpServletResponse response,
                                       @RequestBody ClassroomLibraryParasVO params) throws IOException {

        // 获取 IDs 和选择的列
        String ids = params.getIds();
        String selectedColumns = params.getSelectedColumns();

        // 过滤模糊查询的特殊字符
        // 替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(params.getClassName())) {
            params.setClassName(params.getClassName().replaceAll("([%_])", "\\\\$1"));
        }

        if (StringUtils.isNotBlank(params.getBuildingName())) {
            params.setBuildingName(params.getBuildingName().replaceAll("([%_])", "\\\\$1"));
        }

        // 如果 selectedColumns 为空，导出所有
        //    if (selectedColumns == null || selectedColumns.trim().isEmpty()) {
        // 默认列
        //      selectedColumns = "className,campusName,buildingName,capacity";
        //  }

        List<ClassroomLibraryExcelVO> classroomLibrarys = new ArrayList<>();

        if (ids != null && !ids.isEmpty()) {
            // 遍历 ID 数组
            for (String id : ids.split(",")) {
                ClassroomLibraryExcelVO classroomLibrary = classroomLibraryMapper.getClassroomLibraryDataById(id);
                if (classroomLibrary != null) {
                    classroomLibrarys.add(classroomLibrary);
                }
            }
        } else {
            // 如果没有传递 IDs，导出所有教室库数据
            List<ClassroomLibraryExcelVO> allClassroomLibrarys = classroomLibraryMapper.getClassroomLibraryDataAll(params);
            classroomLibrarys.addAll(allClassroomLibrarys);
        }

        // 根据 selectedColumns 过滤导出列
        List<List<String>> head = new ArrayList<>();
        List<List<Object>> data = new ArrayList<>();

        // 构建表头
        if (selectedColumns.contains("className")) {
            head.add(Collections.singletonList("教室名称"));
        }
        if (selectedColumns.contains("buildingName")) {
            head.add(Collections.singletonList("所在建筑"));
        }
        if (selectedColumns.contains("campusName")) {
            head.add(Collections.singletonList("所在校区"));
        }
        if (selectedColumns.contains("capacity")) {
            head.add(Collections.singletonList("容纳人数"));
        }

        // 构建数据
        for (ClassroomLibraryExcelVO library : classroomLibrarys) {
            List<Object> rowData = new ArrayList<>();

            // 根据 selectedColumns 过滤导出列
            if (selectedColumns.contains("className")) {
                rowData.add(library.getClassName());
            }
            if (selectedColumns.contains("buildingName")) {
                rowData.add(library.getBuildingName());
            }
            if (selectedColumns.contains("campusName")) {
                rowData.add(library.getCampusName());
            }
            if (selectedColumns.contains("capacity")) {
                rowData.add(library.getCapacity());
            }

            data.add(rowData);
        }

        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("教室库信息", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 使用 EasyExcel 导出
        EasyExcel.write(response.getOutputStream())
                .head(head)
                .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())// 基于 column 长度，自动适配。最大 255 宽度
                .sheet("教室库信息列表")
                .doWrite(data);
    }

}
