package com.unicom.swdx.module.edu.controller.admin.plantemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 教学计划模版 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanTemplateRespVO extends PlanTemplateBaseVO {

    @ApiModelProperty(value = "唯一标识符，自增", required = true)
    private Long id;

}
