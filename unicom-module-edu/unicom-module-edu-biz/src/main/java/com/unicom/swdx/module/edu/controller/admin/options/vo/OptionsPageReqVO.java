package com.unicom.swdx.module.edu.controller.admin.options.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 选项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OptionsPageReqVO extends PageParam {

    @Schema(description = "选项类型(字典)", example = "1")
    private String optionsType;

    @Schema(description = "选项内容")
    private String content;

    @Schema(description = "选项分数")
    private BigDecimal score;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "更新人")
    private Long updater;

    @Schema(description = "删除标志")
    private Integer deleted;

    @Schema(description = "题干主键", example = "27397")
    private Long questionId;

}