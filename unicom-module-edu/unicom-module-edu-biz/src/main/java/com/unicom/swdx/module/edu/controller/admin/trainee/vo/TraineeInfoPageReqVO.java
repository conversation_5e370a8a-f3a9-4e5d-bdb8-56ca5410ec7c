package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("管理后台 - TraineeInfoPageReqVO Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraineeInfoPageReqVO extends PageParam {

    @ApiModelProperty(value = "单位id")
    @NotNull(message = "单位id不能为空")
    private Long unitId;

    @ApiModelProperty(value = "班级id")
    @NotNull(message = "班级id不能为空")
    private Long classId;

    @ApiModelProperty("学员姓名或手机号")
    private String nameOrPhone;

    @ApiModelProperty("学员状态")
    private Integer status;

    private List<Integer> includeColumnIndexes;

}
