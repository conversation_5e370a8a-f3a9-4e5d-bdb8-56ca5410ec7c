package com.unicom.swdx.module.edu.controller.admin.classmanagement.excelimporthandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Arrays;
import java.util.List;

/**
 * 学制单位下拉框处理器
 */
public class GetLearningSystemUnitSheetWriteHandler implements SheetWriteHandler {

    // 学制单位字段在Excel中的列索引（从0开始）
    private final int columnIndex;

    /**
     * 构造函数
     * 
     * @param columnIndex 学制单位字段在Excel中的列索引（从0开始）
     */
    public GetLearningSystemUnitSheetWriteHandler(int columnIndex) {
        this.columnIndex = columnIndex;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 这里不需要实现
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 创建下拉框
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();

        // 设置下拉框的范围，从第2行（数据行，表头是第1行）开始，到第100行，列索引由构造函数传入
        CellRangeAddressList addressList = new CellRangeAddressList(1, 100, columnIndex, columnIndex);

        // 定义下拉框选项
        List<String> options = Arrays.asList("天", "周", "月");

        // 创建下拉框约束
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options.toArray(new String[0]));

        // 创建下拉框
        DataValidation dataValidation = helper.createValidation(constraint, addressList);

        // 配置下拉框
        dataValidation.setShowErrorBox(true);
        dataValidation.setSuppressDropDownArrow(true);

        // 添加下拉框到工作表
        sheet.addValidationData(dataValidation);
    }
}