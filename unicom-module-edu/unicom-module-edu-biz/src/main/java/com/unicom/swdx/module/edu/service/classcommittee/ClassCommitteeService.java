package com.unicom.swdx.module.edu.service.classcommittee;


import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcommittee.vo.ClassCommitteeRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcommittee.ClassCommitteeDO;

import java.util.List;
import java.util.Map;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ClassCommitteeService extends IService<ClassCommitteeDO> {

    Long addClassCommittee(ClassCommitteeReqVO reqVO);

    Integer editClassCommittee(ClassCommitteeReqVO reqVO);

    Boolean deleteClassCommittee(Long id);

    List<Map<String, String>> getClassCommittee(Long classId);

    List<ClassCommitteeRespVO> getClassCommitteeList(Long classId, String name);
}
