package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "班主任-移动端-批量补卡 ReqVO")
@Data
public class AppBatchUpdateToCheckInReqVO {

    @ApiModelProperty(value = "补卡记录id列表", required = true, example = "[1,2]")
    @NotEmpty(message = "补卡记录id列表不能为空")
    private List<Long> recordIdList;

    @ApiModelProperty(value = "补卡状态 1-正常 2-迟到 3-事假 4-病假 5-五会假", required = true, example = "1")
    @NotNull(message = "补卡状态不能为空")
    @Range(min = 1, max = 5, message = "补卡状态不存在")
    private Integer status;
}
