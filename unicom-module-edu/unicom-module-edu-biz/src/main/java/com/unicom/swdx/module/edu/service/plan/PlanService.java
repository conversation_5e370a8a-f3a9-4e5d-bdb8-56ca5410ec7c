package com.unicom.swdx.module.edu.service.plan;

import java.util.*;
import javax.validation.*;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.plan.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.framework.common.pojo.PageResult;


public interface PlanService extends IService<PlanDO> {

    /**
     * 创建教学计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long  createPlan(@Valid PlanCreateReqVO createReqVO);

    /**
     * 更新教学计划
     *
     * @param updateReqVO 更新信息
     */
    void updatePlan(@Valid PlanUpdateReqVO updateReqVO);

    /**
     * 删除教学计划
     *
     * @param id 编号
     */
    void deletePlan(Long id);

    /**
     * 获得教学计划
     *
     * @param id 编号
     * @return 教学计划
     */
    PlanDO getPlan(Long id);

    /**
     * 获得教学计划列表
     *
     * @param ids 编号
     * @return 教学计划列表
     */
    List<PlanDO> getPlanList(Collection<Long> ids);

    /**
     * 获得教学计划分页
     *
     * @param pageReqVO 分页查询
     * @return 教学计划分页
     */
    PageResult<PlanDO> getPlanPage(PlanPageReqVO pageReqVO);

    /**
     * 获得教学计划列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 教学计划列表
     */
    List<PlanDO> getPlanList(PlanExportReqVO exportReqVO);

    void deleteByIds(List<Long> ids);

//    PlanDO getPlanByClassId(Long classId);

    /**
     * 教学计划生成课表
     * @param id 教学计划id
     */
    void updateStatus(Long id);
}
