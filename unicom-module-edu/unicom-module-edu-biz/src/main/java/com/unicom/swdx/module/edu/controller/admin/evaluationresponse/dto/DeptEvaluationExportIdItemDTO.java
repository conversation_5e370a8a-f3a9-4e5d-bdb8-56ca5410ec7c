package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description: 部门评估-导出 指定导出时 需要指定部门id和教师id两个字段确定一行
 * @date 2024-11-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeptEvaluationExportIdItemDTO {

    @ApiModelProperty(value = "所属部门ID", example = "1")
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    @ApiModelProperty(value = "教师ID", example = "1")
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;
}
