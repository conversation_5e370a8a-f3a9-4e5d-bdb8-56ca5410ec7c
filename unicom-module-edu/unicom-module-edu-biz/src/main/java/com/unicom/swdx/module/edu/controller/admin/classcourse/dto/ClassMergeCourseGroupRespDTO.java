package com.unicom.swdx.module.edu.controller.admin.classcourse.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 有班级名称字典的分组课程安排 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassMergeCourseGroupRespDTO extends ClassCourseClassNameRespDTO {

    /**
     * 合班授课分组序号
     */
    private Integer groupNum;

}
