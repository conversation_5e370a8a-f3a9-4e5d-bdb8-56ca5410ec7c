package com.unicom.swdx.module.edu.dal.mysql.questionnairedetail;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.*;

/**
 * 评估问卷与问题关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionnaireDetailMapper extends BaseMapperX<QuestionnaireDetailDO> {

    default PageResult<QuestionnaireDetailDO> selectPage(QuestionnaireDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionnaireDetailDO>()
                .eqIfPresent(QuestionnaireDetailDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(QuestionnaireDetailDO::getQuestionnaireId, reqVO.getQuestionnaireId())
                .eqIfPresent(QuestionnaireDetailDO::getCreateDept, reqVO.getCreateDept())
                .betweenIfPresent(QuestionnaireDetailDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(QuestionnaireDetailDO::getCreator, reqVO.getCreator())
                .eqIfPresent(QuestionnaireDetailDO::getOneBallotVeto, reqVO.getOneBallotVeto())
                .orderByDesc(QuestionnaireDetailDO::getId));
    }

    default List<QuestionnaireDetailDO> selectByQuestionnaireId(Long id) {
        return selectList(new LambdaQueryWrapperX<QuestionnaireDetailDO>()
                .eq(QuestionnaireDetailDO::getQuestionnaireId, id)
                .eq(QuestionnaireDetailDO::getDeleted, false));
    }

    default void deleteByQuestionnaireId(Long id) {
        delete(new LambdaQueryWrapperX<QuestionnaireDetailDO>()
                .eq(QuestionnaireDetailDO::getQuestionnaireId, id));
    }

    default void batchDeleteByQuestionnaireId(List<Long> ids) {
        delete(new LambdaQueryWrapperX<QuestionnaireDetailDO>()
                .in(QuestionnaireDetailDO::getQuestionnaireId, ids));
    }
}