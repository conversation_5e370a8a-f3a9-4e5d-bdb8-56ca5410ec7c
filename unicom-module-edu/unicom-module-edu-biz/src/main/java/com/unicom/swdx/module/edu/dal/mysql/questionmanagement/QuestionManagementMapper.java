package com.unicom.swdx.module.edu.dal.mysql.questionmanagement;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 题目管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionManagementMapper extends BaseMapperX<QuestionManagementDO> {

    default PageResult<QuestionManagementDO> selectPage(QuestionManagementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionManagementDO>()
                .eqIfPresent(QuestionManagementDO::getStem, reqVO.getStem())
                .eqIfPresent(QuestionManagementDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(QuestionManagementDO::getScore, reqVO.getScore())
                .eqIfPresent(QuestionManagementDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(QuestionManagementDO::getDescription, reqVO.getDescription())
                .eqIfPresent(QuestionManagementDO::getCreateDept, reqVO.getCreateDept())
                .betweenIfPresent(QuestionManagementDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(QuestionManagementDO::getCreator, reqVO.getCreator())
                .eqIfPresent(QuestionManagementDO::getOneBallotVetoResult, reqVO.getOneBallotVetoResult())
                .eqIfPresent(QuestionManagementDO::getIsLogic, reqVO.getIsLogic())
                .orderByDesc(QuestionManagementDO::getId));
    }

    void moveToDefaultCategory(@Param("defaultId") Long defaultId, @Param("currentId") Long currentId);

    List<QuestionManagementRespVO> selectPageByPageVO(IPage<QuestionManagementRespVO> page, @Param("reqVO") QuestionManagementPageReqVO pageReqVO);

    List<QuestionRespVO> selectQuestionList();

    List<Long> selectBuiltInQuestionId(@Param("categoryId") Long builtInCategoryId);
}