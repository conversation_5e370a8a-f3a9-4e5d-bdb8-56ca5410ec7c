package com.unicom.swdx.module.edu.controller.admin.leavenotification.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("管理后台 - 离校报备批量通知 Request VO")
@Data
public class LeaveNotificationBatchNotifyReqVO {

    @ApiModelProperty(value = "离校报备ID", required = true, example = "1024")
    @NotNull(message = "离校报备ID不能为空")
    private Long notificationId;

    @ApiModelProperty(value = "学员ID列表", required = true, example = "[1024, 2048]")
    @NotEmpty(message = "学员ID列表不能为空")
    private List<Long> traineeIds;

}