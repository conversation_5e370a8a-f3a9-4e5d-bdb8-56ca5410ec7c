package com.unicom.swdx.module.edu.dal.dataobject.leavereport;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName("edu_leave_report_detail")
@KeySequence("edu_leave_report_detail_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeaveReportDetailDO extends TenantBaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 离校报备id
     */
    private Long leaveId;
    /**
     * 是否离校
     */
    private Boolean leaveStatus;
    /**
     * 离校时间
     */
    private LocalDateTime leaveTime;
    /**
     * 返校时间
     */
    private LocalDateTime returnTime;
    /**
     * 学员id
     */
    private Long studentId;
    /**
     * 离校日是否校内用餐
     */
    private Boolean leaveDinner;
    /**
     * 返校日是否校内用餐
     */
    private Boolean returnDinner;
    /**
     * 目的地
     */
    private String destination;
    /**
     * 事由
     */
    private String cause;
}
