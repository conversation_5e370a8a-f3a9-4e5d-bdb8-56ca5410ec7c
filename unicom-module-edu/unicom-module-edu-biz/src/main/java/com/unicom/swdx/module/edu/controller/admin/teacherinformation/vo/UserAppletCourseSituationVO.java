package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class UserAppletCourseSituationVO {


    @ApiModelProperty(value = "课程ID", required = true)
    private String courseId;

    @ApiModelProperty(value = "课程名称", required = true)
    private String courseName;

    @ApiModelProperty(value = "班次ID", required = true)
    private String classId;

    @ApiModelProperty(value = "班次名称", required = true)
    private String className;

    @ApiModelProperty(value = "学年", required = true, example = "2023")
    private String year;

    @ApiModelProperty(value = "学期（1 上学期 2 下学期）", required = true, example = "1")
    private String term;

    @ApiModelProperty(value = "上课日期", required = true)
    private String jxjhSjrq;

    @ApiModelProperty(value = "上课时间段", required = true)
    private String jxjhSjd;

    @ApiModelProperty(value = "末次评估时间", required = true)
    private String lastUpdateTime;

    @ApiModelProperty(value = "师资ID", required = true)
    private String szId;

    @ApiModelProperty(value = "辅助师资ID", required = false)
    private String fszId;

    @ApiModelProperty(value = "均分（修正后）", required = true, example = "88.50")
    private BigDecimal avgScoreR;

    @ApiModelProperty(value = "总评估人数", required = true, example = "100")
    private int totalStu;

    @ApiModelProperty(value = "已评估", required = true)
    private int evaled;

    @ApiModelProperty(value = "排名", required = false)
    private String rn;

}
