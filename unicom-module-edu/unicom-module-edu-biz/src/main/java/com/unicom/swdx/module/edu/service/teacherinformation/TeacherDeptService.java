package com.unicom.swdx.module.edu.service.teacherinformation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherDeptDO;
import org.springframework.transaction.annotation.Transactional;

public interface TeacherDeptService extends IService<TeacherDeptDO> {

    @Transactional(rollbackFor = Exception.class)
    void syncTeacherDept(Long tenantId);
}
