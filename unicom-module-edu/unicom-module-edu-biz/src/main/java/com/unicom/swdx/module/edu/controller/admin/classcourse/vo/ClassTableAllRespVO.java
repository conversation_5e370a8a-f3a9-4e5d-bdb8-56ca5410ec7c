package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClassTableAllRespVO {

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    Long classId;
    @ApiModelProperty(value = "班级名称")
    String className;
    @ApiModelProperty(value = "班级学员人数")
    Long traineeNum;
    @ApiModelProperty(value = "班级对应课表")
    List<ClassTimeTableRespVO> course = new ArrayList<>();
}
