package com.unicom.swdx.module.edu.service.rollcallcommonlocations;

import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.dto.RollcallCommonLocationsAddDTO;
import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo.RollcallCommonLocationsRespVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 大课考勤、点名签到常用地点 Service 接口
 *
 * <AUTHOR>
 */
public interface RollcallCommonLocationsService {

    /**
     * 获得一个教师大课考勤或者点名签到的常用地点
     *
     * @param type 0 大课考勤 1 点名签到
     * @return 常用地点列表
     */
    List<RollcallCommonLocationsRespVO> getListForTeacher(Integer type);

    /**
     * 删除一个常用地点
     * @param id 常用地点id
     */
    void deleteOne(Long id);

    /**
     * 添加一个常用地点
     * @param dto 常用地点信息
     */
    void addOne(RollcallCommonLocationsAddDTO dto);

    /**
     * 获得一个教师最近一次选择的半径范围
     * @param type 0 大课考勤 1 点名签到
     * @return 半径
     */
    BigDecimal getLastRadius(Integer type);
}
