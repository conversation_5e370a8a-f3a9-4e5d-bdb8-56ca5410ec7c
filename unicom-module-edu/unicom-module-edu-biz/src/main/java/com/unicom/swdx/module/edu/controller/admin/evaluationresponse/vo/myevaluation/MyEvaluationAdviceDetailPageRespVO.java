package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 我的评估-意见详情 分页查询 Response VO
 * @date 2024-11-19
 */
@ApiModel("我的评估-意见详情 分页查询 Response VO")
@Data
public class MyEvaluationAdviceDetailPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "内容", example = "不错")
    private String content;
}
