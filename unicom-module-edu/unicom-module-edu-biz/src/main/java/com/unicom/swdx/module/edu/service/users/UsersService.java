package com.unicom.swdx.module.edu.service.users;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.users.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.users.UsersDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface UsersService {

    /**
     * 创建用户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUsers(@Valid UsersSaveReqVO createReqVO);

    /**
     * 更新用户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateUsers(@Valid UsersSaveReqVO updateReqVO);

    /**
     * 删除用户信息
     *
     * @param id 编号
     */
    void deleteUsers(Long id);

    /**
     * 获得用户信息
     *
     * @param id 编号
     * @return 用户信息
     */
    UsersDO getUsers(Long id);

    /**
     * 获得用户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户信息分页
     */
    PageResult<UsersDO> getUsersPage(UsersPageReqVO pageReqVO);

}