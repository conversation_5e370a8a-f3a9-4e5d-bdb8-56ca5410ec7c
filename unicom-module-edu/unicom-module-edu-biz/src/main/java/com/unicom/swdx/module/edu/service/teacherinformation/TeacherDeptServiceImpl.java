package com.unicom.swdx.module.edu.service.teacherinformation;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherDeptDO;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

@Slf4j
@Service
public class TeacherDeptServiceImpl extends ServiceImpl<TeacherDeptMapper, TeacherDeptDO> implements TeacherDeptService {
    @Resource
    private TeacherDeptMapper teacherDeptMapper;

    /**
     * 同步教师部门信息至edu_teacher_information
     *
     * @param tenantId 租户id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncTeacherDept(Long tenantId) {
        Long myTenantId = tenantId == null ? getTenantId() : tenantId;
        log.info("同步教师部门信息 - tenantId：" + myTenantId);
        if (myTenantId == null) {
            log.info("同步教师部门信息失败，未指定租户");
            return;
        }
        // 先删除再同步，防止重复插入数据
        teacherDeptMapper.deleteByTenantId(myTenantId);
        teacherDeptMapper.syncTeacherDeptForTenantId(myTenantId);
    }


}
