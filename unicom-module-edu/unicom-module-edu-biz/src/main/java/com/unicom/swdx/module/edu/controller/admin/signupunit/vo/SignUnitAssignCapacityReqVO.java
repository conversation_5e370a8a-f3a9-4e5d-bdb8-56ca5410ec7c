package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: SignUnitAssignCapacityReqVO
 * @Author: lty
 * @Date: 2024/11/7 9:27
 */
@Data
public class SignUnitAssignCapacityReqVO {

    @ApiModelProperty("班级id")
    private Long classId;

    @ApiModelProperty("单位信息列表")
    private List<AssignCapacityVO> list;

    @Data
    public static class AssignCapacityVO{
        @ApiModelProperty(value = "单位id")
        private Integer id;

        @ApiModelProperty(value = "单位名称")
        private String unitName;

        @ApiModelProperty(value = "名额数")
        private Integer capacity;

        @ApiModelProperty(value = "是否限制 1限制 0不限制")
        private Integer isRestrict;

        @ApiModelProperty(value = "模板  1是模板 0不是模板")
        private Integer template;

        @ApiModelProperty(value = "单位分类")
        private Integer unitClassification;

        @ApiModelProperty(value = "负责人电话")
        private String phone;

        @ApiModelProperty(value = "单位负责人")
        private String unitChargePeople;
    }

}
