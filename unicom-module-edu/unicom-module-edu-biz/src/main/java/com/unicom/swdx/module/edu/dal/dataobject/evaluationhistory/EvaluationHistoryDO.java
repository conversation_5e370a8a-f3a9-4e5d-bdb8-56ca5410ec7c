package com.unicom.swdx.module.edu.dal.dataobject.evaluationhistory;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * 历史评价数据 DO
 */
@TableName("edu_evaluation_history")
@KeySequence("edu_evaluation_history_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class EvaluationHistoryDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private String id;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 授课时间
     */
    private LocalDateTime sksj;

    /**
     * 授课时段
     */
    private String sksd;

    /**
     * 课程ID
     */
    private String kcId;

    /**
     * 课程名称
     */
    private String kcName;

    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 部门名称
     */
    private String orgName;

    /**
     * 教学形式
     */
    private String jxxs;

    /**
     * 总人数
     */
    private Integer totalNum;

    /**
     * 已评人数
     */
    private Integer ypNum;

    /**
     * 评课率
     */
    private BigDecimal pkRate;

    /**
     * 平均分
     */
    private BigDecimal score;

    /**
     * 排名分
     */
    private BigDecimal rankScore;

    /**
     * 分数排名
     */
    private Integer scoreRank;

    /**
     * 师资来源
     */
    private String teacherSource;

    /**
     * 教师ID
     */
    private String teacherId;

    /**
     * 班级ID
     */
    private String classId;
}
