package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 课程库授课记录 Resp VO")
@Data
public class CoursesTeachingRecordRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "排课id", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "是否部门授课", example = "true")
    private Boolean department;

    @ApiModelProperty(value = "课程id", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "课程")
    private String courseName;

    @ApiModelProperty(value = "班次id", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "班次名称", example = "五班")
    private String className;

    @ApiModelProperty(value = "授课教师id", example = "1")
    private String teacherIds;

    @ApiModelProperty(value = "授课教师姓名", example = "李四")
    private String teacherNames;

    @ApiModelProperty(value = "授课时间", example = "2021-01-01 上午 00:00 至 00:00")
    private String classDuration;

    @ApiModelProperty(value = "授课开始时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classStartTime;

    @ApiModelProperty(value = "授课结束时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classEndTime;

    @ApiModelProperty(value = "授课日期", example = "2021-01-01")
    private String classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    private Integer dayPeriod;

}
