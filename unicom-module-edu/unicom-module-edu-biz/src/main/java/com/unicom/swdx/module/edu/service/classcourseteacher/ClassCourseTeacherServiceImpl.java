package com.unicom.swdx.module.edu.service.classcourseteacher;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.classcourseteacher.ClassCourseTeacherConvert;
import com.unicom.swdx.module.edu.dal.mysql.classcourseteacher.ClassCourseTeacherMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 课程表-教师-授课关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ClassCourseTeacherServiceImpl implements ClassCourseTeacherService {

    @Resource
    private ClassCourseTeacherMapper classCourseTeacherMapper;

    @Override
    public Long createClassCourseTeacher(ClassCourseTeacherCreateReqVO createReqVO) {
        // 插入
        ClassCourseTeacherDO classCourseTeacher = ClassCourseTeacherConvert.INSTANCE.convert(createReqVO);
        classCourseTeacherMapper.insert(classCourseTeacher);
        // 返回
        return classCourseTeacher.getId();
    }

    @Override
    public void updateClassCourseTeacher(ClassCourseTeacherUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateClassCourseTeacherExists(updateReqVO.getId());
        // 更新
        ClassCourseTeacherDO updateObj = ClassCourseTeacherConvert.INSTANCE.convert(updateReqVO);
        classCourseTeacherMapper.updateById(updateObj);
    }

    @Override
    public void deleteClassCourseTeacher(Long id) {
        // 校验存在
        this.validateClassCourseTeacherExists(id);
        // 删除
        classCourseTeacherMapper.deleteById(id);
    }

    private void validateClassCourseTeacherExists(Long id) {
        if (classCourseTeacherMapper.selectById(id) == null) {
            throw exception(CLASS_COURSE_TEACHER_NOT_EXISTS);
        }
    }

    @Override
    public ClassCourseTeacherDO getClassCourseTeacher(Long id) {
        return classCourseTeacherMapper.selectById(id);
    }

    @Override
    public List<ClassCourseTeacherDO> getClassCourseTeacherList(Collection<Long> ids) {
        return classCourseTeacherMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ClassCourseTeacherDO> getClassCourseTeacherPage(ClassCourseTeacherPageReqVO pageReqVO) {
        return classCourseTeacherMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ClassCourseTeacherDO> getClassCourseTeacherList(ClassCourseTeacherExportReqVO exportReqVO) {
        return classCourseTeacherMapper.selectList(exportReqVO);
    }

}
