package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 选修课发布信息(不包括选课人数信息) Excel VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class ElectiveReleaseSimpleExcelVO {

    @ExcelProperty(value = "选修课发布名称")
    private String name;

    @ExcelProperty(value = "选学时间")
    private String selectionDuration;

    @ExcelProperty(value = "上课时间")
    private LocalDate classDate;

    @ExcelProperty(value = "上课时间段")
    private String classDuration;

}
