package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 选修课管理-根据上课时间段获取空闲教室下拉数据 Request VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 选修课管理-根据上课时间段获取空闲教室下拉数据 Request VO")
@Data
public class ElectiveReleaseClassTimeClassroomReqVO {


    @ApiModelProperty(value = "上课日期", required = true, example = "2021-01-01")
    @NotNull(message = "上课日期不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate classDate;

    @ApiModelProperty(value = "上课时段开始时间", required = true, example = "00:00")
    @NotNull(message = "上课时段开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE)
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课时段结束时间", required = true, example = "00:00")
    @NotNull(message = "上课时段结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE)
    private String classEndTimeStr;

    @ApiModelProperty(value = "排除的选修课发布id(用于更新)", example = "1")
    private Long excludeReleaseId;

    @ApiModelProperty(value = "排除的日程安排id(用于更新)", example = "1")
    private Long excludeClassCourseId;
}
