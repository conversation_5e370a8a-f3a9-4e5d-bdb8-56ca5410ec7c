package com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 发布课程的已选课学员信息 Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveCourseTraineeSelectedRespVO extends ElectiveTraineeSelectionBaseVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "学员姓名")
    private String name;

    @ApiModelProperty(value = "学员性别")
    private String sex;

    @ApiModelProperty(value = "学员身份证号")
    private String cardNo;

    @ApiModelProperty(value = "学员手机号")
    private String phone;

    @ApiModelProperty(value = "学员所在单位")
    private String unitName;

    @ApiModelProperty(value = "文化程度字典ID")
    private Long educationalLevel;

    @ApiModelProperty(value = "文化程度")
    private String educationalLevelStr;

    @ApiModelProperty(value = "学员职务")
    private String position;

    @ApiModelProperty(value = "政治面貌字典ID")
    private Long politicalIdentity;

    @ApiModelProperty(value = "政治面貌")
    private String politicalIdentityStr;

    @ApiModelProperty(value = "选课时间")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime createTime;
}
