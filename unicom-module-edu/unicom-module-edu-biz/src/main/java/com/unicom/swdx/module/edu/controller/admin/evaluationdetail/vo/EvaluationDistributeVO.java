package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 评估问卷下发VO")
@Data
@ToString(callSuper = true)
public class EvaluationDistributeVO {

    @ApiModelProperty(value = "补发学员id")
    private Long studentId;

    @ApiModelProperty(value = "补发问卷id")
    private Long questionnaireId;

    @ApiModelProperty(value = "补发课程id")
    private Long classCourseId;
}
