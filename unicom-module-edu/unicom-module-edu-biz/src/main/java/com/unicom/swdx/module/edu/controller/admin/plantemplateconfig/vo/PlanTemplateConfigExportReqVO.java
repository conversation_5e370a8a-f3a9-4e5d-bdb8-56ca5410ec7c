package com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel(value = "管理后台 - 教学计划模版配置 Excel 导出 Request VO", description = "参数和 PlanTemplateConfigPageReqVO 是一致的")
@Data
public class PlanTemplateConfigExportReqVO {

    @ApiModelProperty(value = "教学计划模版ID")
    private Long templateId;

    @ApiModelProperty(value = "星期几（如：1代表周一等）")
    private String dayOfWeek;

    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）")
    private String period;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] beginTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] endTime;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
