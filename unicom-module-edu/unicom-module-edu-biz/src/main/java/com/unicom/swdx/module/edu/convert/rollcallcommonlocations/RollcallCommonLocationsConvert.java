package com.unicom.swdx.module.edu.convert.rollcallcommonlocations;

import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.dto.RollcallCommonLocationsAddDTO;
import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo.RollcallCommonLocationsRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallcommonlocations.RollcallCommonLocationsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 大课考勤、点名签到常用地点 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RollcallCommonLocationsConvert {

    RollcallCommonLocationsConvert INSTANCE = Mappers.getMapper(RollcallCommonLocationsConvert.class);

    List<RollcallCommonLocationsRespVO> convert(List<RollcallCommonLocationsDO> list);

    RollcallCommonLocationsDO convert2(RollcallCommonLocationsAddDTO dto);
}
