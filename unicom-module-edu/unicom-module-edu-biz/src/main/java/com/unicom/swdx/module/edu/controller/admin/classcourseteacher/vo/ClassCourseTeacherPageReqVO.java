package com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 课程表-教师-授课关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassCourseTeacherPageReqVO extends PageParam {

    @ApiModelProperty(value = "课程表id")
    private Long classCourseId;

    @ApiModelProperty(value = "授课教师id")
    private Long teacherId;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
