package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * EduClassroomLibrary Excel VO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ClassroomLibraryExcelVO {

    @ExcelIgnore
    private Integer id;

    @ExcelProperty("教室名称")
    private String className;

    @ExcelProperty("所在校区")
    private String campusName;

    @ExcelProperty("所在建筑")
    private String buildingName;

    @ExcelProperty("容纳人数")
    private Integer capacity;

}
