package com.unicom.swdx.module.edu.service.classcompletiontemplate;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 结业考核模版设置 Service 接口
 *
 * <AUTHOR>
 */
public interface ClassCompletionTemplateService {

    /**
     * 创建结业考核模版设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    List<Long> createClassCompletionTemplate(@Valid ClassCompletionTemplateCreateReqVO createReqVO);

    /**
     * 更新结业考核模版设置
     *
     * @param updateReqVO 更新信息
     */
    void updateClassCompletionTemplate(@Valid ClassCompletionTemplateUpdateReqVO updateReqVO);

    /**
     * 删除结业考核模版设置
     *
     * @param id 编号
     */
    void deleteClassCompletionTemplate(Long id);

    /**
     * 获得结业考核模版设置
     *
     * @param id 编号
     * @return 结业考核模版设置
     */
    ClassCompletionTemplateDO getClassCompletionTemplate(Integer id);

    /**
     * 获得结业考核模版设置列表
     *
     * @param ids 编号
     * @return 结业考核模版设置列表
     */
    List<ClassCompletionTemplateDO> getClassCompletionTemplateList(Collection<Integer> ids);

    /**
     * 获得结业考核模版设置分页
     *
     * @param pageReqVO 分页查询
     * @return 结业考核模版设置分页
     */
    PageResult<ClassCompletionTemplateDO> getClassCompletionTemplatePage(ClassCompletionTemplatePageReqVO pageReqVO);

    /**
     * 获得结业考核模版设置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 结业考核模版设置列表
     */
    List<ClassCompletionTemplateDO> getClassCompletionTemplateList(ClassCompletionTemplateExportReqVO exportReqVO);

}
