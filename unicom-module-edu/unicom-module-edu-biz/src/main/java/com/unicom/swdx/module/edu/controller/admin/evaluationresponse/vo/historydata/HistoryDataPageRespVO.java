package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.math.BigDecimal;

@ApiModel("管理后台 - 历史评价数据分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class HistoryDataPageRespVO {

    @ApiModelProperty(value = "序号", required = true)
    private Integer serialNumber;

    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "课程名称", required = true)
    private String kcName;

    @ApiModelProperty(value = "教学形式", required = true)
    private String jxxs;

    @ApiModelProperty(value = "授课时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime sksj;

    @ApiModelProperty(value = "部门", required = true)
    private String orgName;

    @ApiModelProperty(value = "师资来源", required = true)
    private String teacherSource;

    @ApiModelProperty(value = "教师姓名", required = true)
    private String teacherName;

    @ApiModelProperty(value = "教师ID")
    private String teacherId;

    @ApiModelProperty(value = "班次名称", required = true)
    private String className;

    @ApiModelProperty(value = "班级ID")
    private String classId;

    @ApiModelProperty(value = "学员数量", required = true)
    private Integer totalNum;

    @ApiModelProperty(value = "已评人数")
    private Integer ypNum;

    @ApiModelProperty(value = "评课率")
    private BigDecimal pkRate;

    @ApiModelProperty(value = "平均分", required = true)
    private BigDecimal score;

    @ApiModelProperty(value = "排名分", required = true)
    private BigDecimal rankScore;

    @ApiModelProperty(value = "分数排名")
    private Integer scoreRank;

    @ApiModelProperty(value = "授课时段")
    private String sksd;
}
