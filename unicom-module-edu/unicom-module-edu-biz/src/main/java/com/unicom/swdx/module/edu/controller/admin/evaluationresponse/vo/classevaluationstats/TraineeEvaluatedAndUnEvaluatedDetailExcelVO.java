package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description: 班次评估统计-学员已评、未评详情-导出 Excel VO
 * @date 2024-11-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class TraineeEvaluatedAndUnEvaluatedDetailExcelVO {

    @ExcelProperty(value = "课程名称")
    private String courseName;

    @ExcelProperty(value = "上课时间")
    private String classDuration;

    @ExcelProperty(value = "授课教师")
    private String teacherName;
}
