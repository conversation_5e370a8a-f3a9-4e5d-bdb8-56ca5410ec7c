package com.unicom.swdx.module.edu.constant;

/**
 * @ClassName: CompletionTemplateConstant
 * @Author: lty
 * @Date: 2025/3/10 17:38
 */
public class CompletionTemplateConstant {

    public static final String BUSINESS_DEDUCTION = "事假扣分";
    public static final String SICK_DEDUCTION = "病假扣分";
    public static final String FIVE_CANS_LEAVE = "五会假";
    public static final String LATE_EARLY_DEDUCTION = "迟到早退课堂违规扣分";
    public static final String ABSENT_CLASS = "旷课";

    public static final String MEAL_RATE = "就餐率";
    public static final String ACCOMMODATION_RATE = "住宿率";
    public static final String CLASS_RATE = "到课率";

    public static String JSON_DATA = "[\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A0\",\n" +
            "    \"assessmentName\": \"考试成绩 (原分)\",\n" +
            "    \"columnName\": \"  10分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A1\",\n" +
            "    \"assessmentName\": \"学习心得 (原分)\",\n" +
            "    \"columnName\": \"10分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A2\",\n" +
            "    \"assessmentName\": \"事假扣分\",\n" +
            "    \"columnName\": \"学习考勤共计20分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 0\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A3\",\n" +
            "    \"assessmentName\": \"病假扣分\",\n" +
            "    \"columnName\": \"学习考勤共计20分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 1\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A4\",\n" +
            "    \"assessmentName\": \"五会假\",\n" +
            "    \"columnName\": \"学习考勤共计20分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 2\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A5\",\n" +
            "    \"assessmentName\": \"迟到早退课堂违规扣分\",\n" +
            "    \"columnName\": \"学习考勤共计20分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 0,\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A6\",\n" +
            "    \"assessmentName\": \"旷课\",\n" +
            "    \"columnName\": \"学习考勤共计20分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 0,\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A7\",\n" +
            "    \"assessmentName\": \"到课率\",\n" +
            "    \"columnName\": \"学习考勤共计20分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 6\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A8\",\n" +
            "    \"assessmentName\": \"自学率\",\n" +
            "    \"columnName\": \"/ \",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 100,\n" +
            "    \"acquisitionMode\": 0,\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A9\",\n" +
            "    \"assessmentName\": \"学习笔记\",\n" +
            "    \"columnName\": \"5分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 5,\n" +
            "    \"acquisitionMode\": 0,\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A10\",\n" +
            "    \"assessmentName\": \"教学评估\",\n" +
            "    \"columnName\": \"  5分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 9\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"理论学习50分\",\n" +
            "    \"serialNumber\": \"A11\",\n" +
            "    \"assessmentName\": \"小计得分\",\n" +
            "    \"columnName\": \"/\",\n" +
            "    \"conversionAnnouncement\": \"A0*0.1+A1*0.1+20-A2-A3-A5-A6+A9+A10*0.05\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B0\",\n" +
            "    \"assessmentName\": \"政治纪律\",\n" +
            "    \"columnName\": \"  10分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 10,\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B1\",\n" +
            "    \"assessmentName\": \"组织纪律\",\n" +
            "    \"columnName\": \"10分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 10,\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B2\",\n" +
            "    \"assessmentName\": \"廉洁纪律\",\n" +
            "    \"columnName\": \"10分 \",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": 10,\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B3\",\n" +
            "    \"assessmentName\": \"就餐率\",\n" +
            "    \"columnName\": \"  10分      \",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 7\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B4\",\n" +
            "    \"assessmentName\": \"住宿率\",\n" +
            "    \"columnName\": \"10分   \",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": 1,\n" +
            "    \"dataSource\": 8\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B5\",\n" +
            "    \"assessmentName\": \"其他扣分\",\n" +
            "    \"columnName\": \"/  \",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"党性锻炼50分\",\n" +
            "    \"serialNumber\": \"B6\",\n" +
            "    \"assessmentName\": \"小计得分\",\n" +
            "    \"columnName\": \"/\",\n" +
            "    \"conversionAnnouncement\": \"B0+B1+B2+B3+B4-B5\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"加分项\",\n" +
            "    \"serialNumber\": \"C0\",\n" +
            "    \"assessmentName\": \"发表文章\",\n" +
            "    \"columnName\": \"1-3分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": 3,\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"加分项\",\n" +
            "    \"serialNumber\": \"C1\",\n" +
            "    \"assessmentName\": \"竞赛获奖\",\n" +
            "    \"columnName\": \"1-2分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": 2,\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"加分项\",\n" +
            "    \"serialNumber\": \"C2\",\n" +
            "    \"assessmentName\": \"奉献精神\",\n" +
            "    \"columnName\": \" 1-2分\",\n" +
            "    \"conversionAnnouncement\": \"\",\n" +
            "    \"maxScore\": 2,\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"moduleName\": \"加分项\",\n" +
            "    \"serialNumber\": \"C3\",\n" +
            "    \"assessmentName\": \"小计得分\",\n" +
            "    \"columnName\": \"/\",\n" +
            "    \"conversionAnnouncement\": \"C0+C1+C2\",\n" +
            "    \"maxScore\": \"\",\n" +
            "    \"initialScore\": \"\",\n" +
            "    \"acquisitionMode\": \"\",\n" +
            "    \"dataSource\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"idCode\": null,\n" +
            "    \"templateName\": \"百分制量化模板\",\n" +
            "    \"defaultRule\": 1,\n" +
            "    \"serialNumber\": \"ST0\",\n" +
            "    \"columnName\": \"量化得分\",\n" +
            "    \"conversionAnnouncement\": \"A11+B6+C3\"\n" +
            "  }\n" +
            "]";
}
