package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@ApiModel("班级课程安排下拉列表 Req VO")
@Data
public class ClassCourseSimpleReqVO {


    @ApiModelProperty(value = "班级id", example = "1")
    private Long classId;

    @ApiModelProperty(value = "上课开始日期", required = true, example = "2020-01-01")
    @NotNull(message = "上课开始日期不能为空")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @ApiModelProperty(value = "上课结束日期", required = true, example = "2020-01-02")
    @NotNull(message = "上课结束日期不能为空")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @ApiModelProperty(value = "是否开启考勤", example = "1")
    private Boolean isCheck;
}
