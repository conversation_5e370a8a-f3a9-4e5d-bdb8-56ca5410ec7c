package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 班级学员考勤详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassAttendanceDetailPageReqVO extends PageParam {

    @ApiModelProperty(value = "班级ID", required = true, example = "1024")
    private Long classId;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "考勤状态", example = "1")
    private Integer status;

    @ApiModelProperty(value = "考勤日期", example = "2024-07-01")
    private String attendanceDate;

    @ApiModelProperty(value = "排课表ID", example = "1024")
    private Long classCourseId;

    @ApiModelProperty(value = "考勤类型：0-到课，1-就餐，2-住宿", example = "0")
    private Integer type;

    @ApiModelProperty(value = "就餐时段：0-早餐，1-午餐，2-晚餐", example = "0")
    private Integer mealPeriod;

    @ApiModelProperty(value = "打卡类型：0-未打卡，1-已打卡，2-迟到，3-请假", example = "0")
    private Integer clockInType;
}