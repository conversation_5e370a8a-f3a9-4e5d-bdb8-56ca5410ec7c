package com.unicom.swdx.module.edu.controller.admin.leavereport;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportDetailCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportDetailListReqVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportDetailRespVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.StudentLeaveReportRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDetailDO;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.leavereport.LeaveReportDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 学员离校报备管理")
@RestController
@RequestMapping("/edu/leave-report-detail")
@Validated
public class LeaveReportDetailController {

    @Resource
    private LeaveReportDetailService leaveReportDetailService;

    @Resource
    private TraineeMapper traineeMapper;

    @PostMapping("/create")
    @Operation(summary = "学员填报离校报备")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:create')")
    public CommonResult<Long> createLeaveReportDetail(@Valid @RequestBody LeaveReportDetailCreateReqVO createReqVO) {
        return success(leaveReportDetailService.createLeaveReportDetail(createReqVO));
    }

//    @PostMapping("/update")
//    @Operation(summary = "更新题目类别管理")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:update')")
//    public CommonResult<Boolean> updateLeaveReportDetail(@Valid @RequestBody LeaveReportDetailSaveReqVO updateReqVO) {
//        leaveReportDetailService.updateLeaveReportDetail(updateReqVO);
//        return success(true);
//    }
//
//    @PostMapping("/delete")
//    @Operation(summary = "删除题目类别管理")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:delete')")
//    public CommonResult<Boolean> deleteLeaveReportDetail(@RequestParam("id") Long id) {
//        leaveReportDetailService.deleteLeaveReportDetail(id);
//        return success(true);
//    }

    @GetMapping("/get")
    @Operation(summary = "获得已填报的离校报备详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<LeaveReportDetailRespVO> getLeaveReportDetail(@RequestParam("detailId") Long detailId) {
        LeaveReportDetailDO LeaveReportDetail = leaveReportDetailService.getLeaveReportDetail(detailId);
        return success(BeanUtils.toBean(LeaveReportDetail, LeaveReportDetailRespVO.class));
    }

    @GetMapping("/getLeaveList")
    @Operation(summary = "获得班主任发起的离校报备列表")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<StudentLeaveReportRespVO>> getLeaveReportDetailList(@RequestParam("classId") Long classId,
                                                                                 @RequestParam(value = "studentId", required = false) Long studentId) {
        if (studentId == null) {
            Long loginUserId = getLoginUserId();
            studentId = traineeMapper.getTraineeId(loginUserId, classId);
        }
        return success(leaveReportDetailService.getLeaveReportDetailList(classId, studentId));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出题目类别管理 Excel")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:export')")
//
//    public void exportLeaveReportDetailExcel(@Valid LeaveReportDetailListReqVO listReqVO,
//                                                      HttpServletResponse response) throws IOException {
//        List<LeaveReportDetailDO> list = leaveReportDetailService.getLeaveReportDetailList(listReqVO);
//        // 导出 Excel
//        ExcelUtils.write(response, "题目类别管理.xls", "数据", LeaveReportDetailRespVO.class,
//                BeanUtils.toBean(list, LeaveReportDetailRespVO.class));
//    }
}
