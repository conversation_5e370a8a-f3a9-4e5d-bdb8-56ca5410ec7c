package com.unicom.swdx.module.edu.convert.completiontemplate;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;

/**
 * 结业考核模版设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CompletionTemplateConvert {

    CompletionTemplateConvert INSTANCE = Mappers.getMapper(CompletionTemplateConvert.class);

    CompletionTemplateDO convert(CompletionTemplateCreateReqVO bean);

    CompletionTemplateDO convert(CompletionTemplateUpdateReqVO bean);

    CompletionTemplateRespVO convert(CompletionTemplateDO bean);

    List<CompletionTemplateRespVO> convertList(List<CompletionTemplateDO> list);

    PageResult<CompletionTemplateRespVO> convertPage(PageResult<CompletionTemplateDO> page);

    List<CompletionTemplateExcelVO> convertList02(List<CompletionTemplateDO> list);

}
