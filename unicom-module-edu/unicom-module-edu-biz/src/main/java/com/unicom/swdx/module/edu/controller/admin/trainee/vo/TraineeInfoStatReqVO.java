package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: zhouhk
 * @Date: 2024/12/20
 */
@Data
@ApiModel(value = "参训统计筛选条件")
public class TraineeInfoStatReqVO extends PageParam {

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty("学员姓名或手机号")
    private String nameOrPhone;

    @ApiModelProperty("根据班级名称排序，0正序，1倒序，为空不排序")
    private String orderByClassName;

    @ApiModelProperty("根据报到时间排序，0正序，1倒序，为空不排序")
    private String orderByReportTime;

    @ApiModelProperty("导出时需要导出的列字段名称")
    private List<String> exportPropertyNameList;


}
