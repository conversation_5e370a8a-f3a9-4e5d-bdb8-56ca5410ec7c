package com.unicom.swdx.module.edu.service.planconfig;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 教学计划配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PlanConfigService {

    /**
     * 创建教学计划配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanConfig(@Valid PlanConfigCreateReqVO createReqVO);

    /**
     * 更新教学计划配置
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanConfig(@Valid PlanConfigUpdateReqVO updateReqVO);

    /**
     * 删除教学计划配置
     *
     * @param id 编号
     */
    void deletePlanConfig(Long id);

    /**
     * 获得教学计划配置
     *
     * @param id 编号
     * @return 教学计划配置
     */
    PlanConfigDO getPlanConfig(Long id);

    /**
     * 获得教学计划配置列表
     *
     * @param ids 编号
     * @return 教学计划配置列表
     */
    List<PlanConfigDO> getPlanConfigList(Collection<Long> ids);

    /**
     * 获得教学计划配置分页
     *
     * @param pageReqVO 分页查询
     * @return 教学计划配置分页
     */
    PageResult<PlanConfigDO> getPlanConfigPage(PlanConfigPageReqVO pageReqVO);

    /**
     * 获得教学计划配置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 教学计划配置列表
     */
    List<PlanConfigDO> getPlanConfigList(PlanConfigExportReqVO exportReqVO);

}
