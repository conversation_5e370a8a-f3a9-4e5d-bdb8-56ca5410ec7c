package com.unicom.swdx.module.edu.controller.admin.plantemplate;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.plantemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplate.PlanTemplateDO;
import com.unicom.swdx.module.edu.convert.plantemplate.PlanTemplateConvert;
import com.unicom.swdx.module.edu.service.plantemplate.PlanTemplateService;

/**
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 教学计划模版")
@RestController
@RequestMapping("/edu/plan-template")
@Validated
public class PlanTemplateController {

    @Resource
    private PlanTemplateService planTemplateService;

    @PostMapping("/create")
    @ApiOperation("创建教学计划模版")
    @PreAuthorize("@ss.hasPermission('edu:plan-template:create')")
    public CommonResult<Long> createPlanTemplate(@Valid @RequestBody PlanTemplateCreateReqVO createReqVO) {
        return success(planTemplateService.createPlanTemplate(createReqVO));
    }

    @GetMapping("/listByCreator")
    @ApiOperation("创建者获取教学计划模版列表")
    @PreAuthorize("@ss.hasPermission('edu:plan-template:query')")
    public CommonResult<List<PlanTemplateRespVO>> getPlanTemplateListByCreator() {
        List<PlanTemplateRespVO> list = planTemplateService.getPlanTemplateListByCreator();
        return success(list);
    }

    @PostMapping("/update")
    @ApiOperation("更新教学计划模版")
    @PreAuthorize("@ss.hasPermission('edu:plan-template:update')")
    public CommonResult<Boolean> updatePlanTemplate(@Valid @RequestBody PlanTemplateUpdateReqVO updateReqVO) {
        planTemplateService.updatePlanTemplate(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除教学计划模版")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-template:delete')")
    public CommonResult<Boolean> deletePlanTemplate(@RequestParam("id") Long id) {
        planTemplateService.deletePlanTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得教学计划模版")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-template:query')")
    public CommonResult<PlanTemplateRespVO> getPlanTemplate(@RequestParam("id") Long id) {
        PlanTemplateDO planTemplate = planTemplateService.getPlanTemplate(id);
        return success(PlanTemplateConvert.INSTANCE.convert(planTemplate));
    }

    @GetMapping("/list")
    @ApiOperation("获得教学计划模版列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:plan-template:query')")
    public CommonResult<List<PlanTemplateRespVO>> getPlanTemplateList(@RequestParam("ids") Collection<Long> ids) {
        List<PlanTemplateDO> list = planTemplateService.getPlanTemplateList(ids);
        return success(PlanTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得教学计划模版分页")
    @PreAuthorize("@ss.hasPermission('edu:plan-template:query')")
    public CommonResult<PageResult<PlanTemplateRespVO>> getPlanTemplatePage(@Valid PlanTemplatePageReqVO pageVO) {
        PageResult<PlanTemplateDO> pageResult = planTemplateService.getPlanTemplatePage(pageVO);
        return success(PlanTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出教学计划模版 Excel")
    @PreAuthorize("@ss.hasPermission('edu:plan-template:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanTemplateExcel(@Valid PlanTemplateExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PlanTemplateDO> list = planTemplateService.getPlanTemplateList(exportReqVO);
        // 导出 Excel
        List<PlanTemplateExcelVO> datas = PlanTemplateConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "教学计划模版.xls", "数据", PlanTemplateExcelVO.class, datas);
    }

}
