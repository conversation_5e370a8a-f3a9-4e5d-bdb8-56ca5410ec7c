package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 课程库更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CoursesUpdateReqVO extends CoursesBaseVO {

    @ApiModelProperty(value = "课程主键", required = true)
    @NotNull(message = "课程主键不能为空")
    private Long id;

}
