package com.unicom.swdx.module.edu.dal.dataobject.traineegroup;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: TraineeDO
 * @Author: lty
 * @Date: 2024/10/9 11:40
 */
@TableName(value = "edu_trainee_group")
@KeySequence("edu_trainee_group_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraineeGroupDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 小组名称
     */
    private String groupName;

    /**
     * 排序
     */
    private Integer sort;


    /**
     * 租户id
     */
    private Long tenantId;

    private Long classId;

}
