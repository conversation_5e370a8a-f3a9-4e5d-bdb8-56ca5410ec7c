package com.unicom.swdx.module.edu.service.classcompletion;

import com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.mysql.classcompletion.ClassCompletionMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结业考核模版设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ClassCompletionServiceImpl implements ClassCompletionService {

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private ClassCompletionMapper classCompletionMapper;



    @Override
    public void addTraineeInfo(Integer classId) {
        ClassManagementDO classDO = classManagementMapper.selectById(classId);
        if (classDO == null) {
            return;
        }
        //查出班级学员信息
        List<ClassCompletionDO> list = classCompletionMapper.getInfoByClassId(classId);

        classCompletionMapper.insertBatch(list);

    }
}
