package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
* 班级课程安排 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ScheduleChartVO {

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "存储数据列表")
    private List<MyClassScheduleVO> schedules;



}
