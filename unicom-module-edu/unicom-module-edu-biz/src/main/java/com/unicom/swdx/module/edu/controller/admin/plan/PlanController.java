package com.unicom.swdx.module.edu.controller.admin.plan;

import com.unicom.swdx.module.edu.utils.fuzzyquery.EscapeSelectUtil;
import dm.jdbc.util.StringUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.plan.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.convert.plan.PlanConvert;
import com.unicom.swdx.module.edu.service.plan.PlanService;

/**
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 教学计划")
@RestController
@RequestMapping("/edu/plan")
@Validated
public class PlanController {

    @Resource
    private PlanService planService;

    @PostMapping("/create")
    @ApiOperation("创建教学计划")
    @PreAuthorize("@ss.hasPermission('edu:plan:create')")
    public CommonResult<Long> createPlan(@Valid @RequestBody PlanCreateReqVO createReqVO) {
        return success(planService.createPlan(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新教学计划")
    @PreAuthorize("@ss.hasPermission('edu:plan:update')")
    public CommonResult<Boolean> updatePlan(@Valid @RequestBody PlanUpdateReqVO updateReqVO) {
        planService.updatePlan(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateStatus")
    @ApiOperation("更新教学计划")
    @PreAuthorize("@ss.hasPermission('edu:plan:update')")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Long id) {
        planService.updateStatus(id);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除教学计划")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan:delete')")
    public CommonResult<Boolean> deletePlan(@RequestParam("id") Long id) {
        planService.deletePlan(id);
        return success(true);
    }

    @PostMapping("/deleteByIds")
    @ApiOperation("批量删除教学计划")
    @ApiImplicitParam(name = "ids", value = "批量删除编号列表", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan:delete')")
    public CommonResult<Boolean> deleteByIds(@RequestParam("ids") List<Long> ids) {
        planService.deleteByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得教学计划")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:plan:query')")
    public CommonResult<PlanRespVO> getPlan(@RequestParam("id") Long id) {
        PlanDO plan = planService.getPlan(id);
        return success(PlanConvert.INSTANCE.convert(plan));
    }

    @GetMapping("/list")
    @ApiOperation("获得教学计划列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:plan:query')")
    public CommonResult<List<PlanRespVO>> getPlanList(@RequestParam("ids") Collection<Long> ids) {
        List<PlanDO> list = planService.getPlanList(ids);
        return success(PlanConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得教学计划分页")
    @PreAuthorize("@ss.hasPermission('edu:plan:query')")
    public CommonResult<PageResult<PlanRespVO>> getPlanPage(@Valid PlanPageReqVO pageVO) {
        //处理模糊查询的逃逸字符
        if(StringUtil.isNotEmpty(pageVO.getName())){
            pageVO.setName(EscapeSelectUtil.escapeChar(pageVO.getName()));
        }
        PageResult<PlanDO> pageResult = planService.getPlanPage(pageVO);
        return success(PlanConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出教学计划 Excel")
    @PreAuthorize("@ss.hasPermission('edu:plan:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanExcel(@Valid PlanExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PlanDO> list = planService.getPlanList(exportReqVO);
        // 导出 Excel
        List<PlanExcelVO> datas = PlanConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "教学计划.xls", "数据", PlanExcelVO.class, datas);
    }

}
