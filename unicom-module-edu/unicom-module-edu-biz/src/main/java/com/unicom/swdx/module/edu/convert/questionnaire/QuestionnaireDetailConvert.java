package com.unicom.swdx.module.edu.convert.questionnaire;

import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO;
import com.unicom.swdx.module.edu.convert.options.OptionsConvert;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface QuestionnaireDetailConvert {
    QuestionnaireDetailConvert Instance = Mappers.getMapper(QuestionnaireDetailConvert.class);

    List<QuestionnaireDetailDO> convertList(List<QuestionnaireDetailSaveReqVO> questionnaireDetails);

    QuestionnaireManagementRespVO convertDO(QuestionnaireManagementDO questionnaireManagementDO);
}
