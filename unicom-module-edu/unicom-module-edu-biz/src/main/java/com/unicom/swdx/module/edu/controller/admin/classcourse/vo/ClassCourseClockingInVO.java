package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ClassCourseClockingInVO{

    @ApiModelProperty(value = "是否到课考勤，true-开，false-关", required = true)
    private Boolean isCheck;

    @ApiModelProperty(value = "课程表 id", required = true)
    private Long id;

}
