package com.unicom.swdx.module.edu.dal.mysql.ruletemplate;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateLocationDO;
import com.unicom.swdx.module.edu.enums.clockininfo.TypeEnum;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 考勤规则模版 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RuleTemplateMapper extends BaseMapperX<RuleTemplateDO> {


    public static Cache<String, List<RuleTemplateLocationDO> > resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(1024)   // 设定最大容量
                    .expireAfterWrite(5L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    default List<RuleTemplateLocationDO> getRuleTemplateLocationDOlist(Long id){

        List<RuleTemplateLocationDO> ruleTemplateLocationDOS = null;

        try {
            ruleTemplateLocationDOS = resultCache.get("selectListByUserId" + id, () -> {
                return selectListTemplateLocation(id);
            });
        } catch (ExecutionException e) {
            ruleTemplateLocationDOS =selectListTemplateLocation(id);
        }

        return ruleTemplateLocationDOS;
    }




    default PageResult<RuleTemplateDO> selectPage(RuleTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RuleTemplateDO>()
                .likeIfPresent(RuleTemplateDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(RuleTemplateDO::getRuleType, reqVO.getRuleType())
                .eqIfPresent(RuleTemplateDO::getLocations, reqVO.getLocations())
                .eqIfPresent(RuleTemplateDO::getStatus, reqVO.getStatus())
                .eqIfPresent(RuleTemplateDO::getCampus, reqVO.getCampus())
                .eqIfPresent(RuleTemplateDO::getDefaultRule, reqVO.getDefaultRule())
                .orderByDesc(RuleTemplateDO::getId));
    }

    default List<RuleTemplateDO> selectList(RuleTemplateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RuleTemplateDO>()
                .likeIfPresent(RuleTemplateDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(RuleTemplateDO::getRuleType, reqVO.getRuleType())
                .eqIfPresent(RuleTemplateDO::getLocations, reqVO.getLocations())
                .eqIfPresent(RuleTemplateDO::getStatus, reqVO.getStatus())
                .eqIfPresent(RuleTemplateDO::getCampus, reqVO.getCampus())
                .eqIfPresent(RuleTemplateDO::getDefaultRule, reqVO.getDefaultRule())
                .orderByDesc(RuleTemplateDO::getId));
    }

    default RuleTemplateDO selectByNameAndType(String name , Integer ruleType){
        LambdaQueryWrapper<RuleTemplateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleTemplateDO::getRuleName, name)
                .eq(RuleTemplateDO::getRuleType, ruleType);
        return selectOne(queryWrapper);
    }

    /**
     * 规则模版插入地点信息
     * @param reqVO
     */
    void insertLocalInfo(@Param("reqVO") RuleTemplateLocationDO reqVO);

    /**
     * 返回校区默认规则的数量
     * @param campusId
     * @return
     */
    Integer getCampusDefaultRule(@Param("campusId") Integer campusId, @Param("ruleType") Integer ruleType);

    /**
     * @param campusId
     * @return
     */
    RuleTemplateDO getCampusDefaultRules(@Param("campusId") Integer campusId, @Param("ruleType") Integer ruleType);

    /**
     * @param ruleTemplateId
     * @return
     */
    void deletedRuleLocation(@Param("ruleTemplateId") Long ruleTemplateId);

    /**
     * @param id
     * @return
     */
    List<RuleTemplateLocationDO> selectListTemplateLocation(@Param("id") Long id);

    /**
     * @param id
     * @return
     */
    Integer getClassClockCount(@Param("id") Long id);
    /**
     * 班次新增时  拿默认规则
     * @param campusId
     * @return
     */
    List<RuleTemplateDO> getClassDefaultRule(@Param("campusId") Integer campusId);
    /**
     * 班次新增时  拿默认规则 一条
     * @param campusId
     * @return
     */
    RuleTemplateDO getClassDefaultRuleSingle(@Param("campusId") Integer campusId, @Param("type") Integer type);
    /**
     *
     * @param ruleType
     * @return
     */
    List<RuleTemplateVO> selectListByType(@Param("ruleType") Long ruleType);


}
