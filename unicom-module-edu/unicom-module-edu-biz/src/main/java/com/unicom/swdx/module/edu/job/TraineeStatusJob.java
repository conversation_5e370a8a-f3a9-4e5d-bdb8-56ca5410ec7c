package com.unicom.swdx.module.edu.job;

import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: TraineeStatusJob
 * @Author: lty
 * @Date: 2024/10/21 20:51
 */
@Service
@Slf4j
public class TraineeStatusJob {

    @Resource
    private TraineeService traineeService;

    @Resource
    private ClassManagementMapper classManagementMapper;


//    @Scheduled(cron = "0 5 0 * * ?")
    @TenantIgnore
    @XxlJob("UpdateTraineeStatusJob")
    public void updateTraineeStatus() {
        log.info("updateTraineeStatus start");

        //获取当前所有为结业的班级
        List<Long> classIdList = classManagementMapper.getAllClass();

        // 更新学员状态
        List<TraineeDO> list = traineeService.getAllTraineeByClassIds(classIdList);

        for (TraineeDO traineeDO : list) {
            // 更新学员状态
            traineeDO.setStatus(TraineeStatusEnum.GRADUATED.getStatus());
            traineeDO.setGraduateDate(LocalDate.now().minusDays(1));
        }
        traineeService.updateBatchById(list);
        // 更新班级状态
        log.info("updateTraineeStatus end");
    }

//    @Scheduled(fixedRate = 300000) // 300000 毫秒 = 5 分钟
    @TenantIgnore
    @XxlJob("UpdateUserSystemIdJob")
    public void updateUserSystemId() {

        log.info("同步创建异常学员账号信息到业中 - 开始定时任务");

        try {
            traineeService.updateUserSystemId();
        }catch (Exception e){
            log.error("同步创建异常学员账号信息到业中 - 定时任务执行失败！原因：{}",e.getMessage(), e);
            return;
        }

        log.info("同步创建异常学员账号信息到业中 - 定时任务执行成功！");

    }

}
