package com.unicom.swdx.module.edu.controller.admin.planconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 教学计划配置更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanConfigUpdateReqVO extends PlanConfigBaseVO {

    @ApiModelProperty(value = "唯一标识符，自增", required = true)
    @NotNull(message = "唯一标识符，自增不能为空")
    private Long id;

}
