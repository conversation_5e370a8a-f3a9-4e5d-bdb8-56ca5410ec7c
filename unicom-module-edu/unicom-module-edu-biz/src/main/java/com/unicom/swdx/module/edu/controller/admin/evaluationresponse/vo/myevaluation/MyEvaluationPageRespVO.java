package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 我的评估-分页查询 Response VO
 * @date 2024-11-19
 */
@ApiModel("我的评估-分页查询 Response VO")
@Data
public class MyEvaluationPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "排课id", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "课程id", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "课程")
    private String courseName;

    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课、3-教学活动)", example = "1")
    private Integer courseType;

    @ApiModelProperty(value = "教学形式字典ID", example = "1")
    private Long educateFormId;

    @ApiModelProperty(value = "教学形式", example = "面授")
    private String educateForm;

    @ApiModelProperty(value = "上课日期", example = "2021-01-01")
    private String classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    private Integer dayPeriod;

    @ApiModelProperty(value = "上课开始时间", example = "09:00")
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课结束时间", example = "11:00")
    private String classEndTimeStr;

    @ApiModelProperty(value = "授课时间", example = "2021-01-01 上午 00:00-00:00")
    private String classDuration;

    @ApiModelProperty(value = "授课班次ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "授课班次", example = "班级")
    private String className;

    @ApiModelProperty(value = "授课教师ID", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "授课教师", example = "张三")
    private String teacherName;

    @ApiModelProperty(value = "所属部门", example = "公共管理部,测试部")
    private String deptNames;

    @ApiModelProperty(value = "是否部门授课", example = "false")
    private Boolean department;

    @ApiModelProperty(value = "应评人数", example = "10")
    private Integer expectedCount;

    @ApiModelProperty(value = "参评人数", example = "5")
    private Integer actualCount;

    @ApiModelProperty(value = "参评率(% 两位小数)", example = "50.00")
    private String ratioStr;

    @ApiModelProperty(value = "平均分(两位小数)", example = "90.00")
    private String averageScoreStr;

    @ApiModelProperty(value = "班次排名", example = "1/21")
    private String rankStr;

    @ApiModelProperty(value = "排名分(两位小数)", example = "82.00")
    private String rankScoreStr;

    @ApiModelProperty(value = "排名分", example = "82.00")
    private Float rankScore;

    @ApiModelProperty(hidden = true)
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classStartTime;

    @ApiModelProperty(hidden = true)
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classEndTime;

    @ApiModelProperty(value = "平均分", example = "90.452", hidden = true)
    private Float averageScore;

    @ApiModelProperty(value = "参评率", example = "50.0001", hidden = true)
    private Float ratio;
}
