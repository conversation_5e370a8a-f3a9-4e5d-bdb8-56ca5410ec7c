package com.unicom.swdx.module.edu.convert.clockininfo;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.MyClassScheduleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;

/**
 * 考勤签到 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClockInInfoConvert {

    ClockInInfoConvert INSTANCE = Mappers.getMapper(ClockInInfoConvert.class);

    ClockInInfoDO convert(ClockInInfoCreateReqVO bean);

    ClockInInfoDO convert(ClockInInfoUpdateReqVO bean);

    ClockInInfoRespVO convert(ClockInInfoDO bean);

    List<ClockInInfoRespVO> convertList(List<ClockInInfoDO> list);

    PageResult<ClockInInfoRespVO> convertPage(PageResult<ClockInInfoDO> page);

    List<AppClockInInfoRespVO> convertToAppRespVoList(List<MyClassScheduleVO> myClassScheduleVOList);
}
