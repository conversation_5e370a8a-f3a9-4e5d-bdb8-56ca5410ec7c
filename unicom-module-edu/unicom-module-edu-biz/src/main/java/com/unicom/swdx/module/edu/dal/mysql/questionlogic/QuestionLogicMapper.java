package com.unicom.swdx.module.edu.dal.mysql.questionlogic;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.*;

/**
 * 问题逻辑 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionLogicMapper extends BaseMapperX<QuestionLogicDO> {

    default PageResult<QuestionLogicDO> selectPage(QuestionLogicPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionLogicDO>()
                .eqIfPresent(QuestionLogicDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(QuestionLogicDO::getLogicQuestionId, reqVO.getLogicQuestionId())
                .orderByDesc(QuestionLogicDO::getId));
    }

    default List<QuestionLogicDO> selectListByQuestionnaireId(Long id) {
        return selectList(new LambdaQueryWrapperX<QuestionLogicDO>()
                .eq(QuestionLogicDO::getQuestionnaireId, id)
                .eq(QuestionLogicDO::getDeleted, false));
    }

    default void deleteByQuestionnaireId(Long id) {
        delete(new LambdaQueryWrapperX<QuestionLogicDO>()
                .eq(QuestionLogicDO::getQuestionnaireId, id));
    }

    default void batchDeleteByQuestionnaireId(List<Long> ids) {
        delete(new LambdaQueryWrapperX<QuestionLogicDO>()
                .in(QuestionLogicDO::getQuestionnaireId, ids));
    }
}