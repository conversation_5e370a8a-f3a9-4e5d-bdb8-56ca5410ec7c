package com.unicom.swdx.module.edu.convert.questionnaire;

import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicSaveReqVO;
import com.unicom.swdx.module.edu.convert.options.OptionsConvert;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface QuestionLogicConvert {
    QuestionLogicConvert Instance = Mappers.getMapper(QuestionLogicConvert.class);

    List<QuestionLogicDO> convertList(List<QuestionLogicSaveReqVO> questionLogic);

    List<QuestionLogicRespVO> convertList01(List<QuestionLogicDO> questionLogic);
}
