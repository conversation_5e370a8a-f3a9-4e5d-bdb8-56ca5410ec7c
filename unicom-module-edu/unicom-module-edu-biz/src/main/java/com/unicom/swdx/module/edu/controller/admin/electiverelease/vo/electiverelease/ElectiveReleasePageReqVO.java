package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 选修课发布信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleasePageReqVO extends PageParam {

    @ApiModelProperty(value = "发布名称", example = "选修课发布")
    private String name;

    @ApiModelProperty(value = "是否是班主任-限定该班主任班级的发布信息", example = "false-非班主任(默认) true-班主任")
    private Boolean isClassMaster;

    @ApiModelProperty(hidden = true)
    private List<Long> classIdList;

    @ApiModelProperty(value = "是否返回选课人数信息(默认不返回) false-不返回 true-返回")
    private Boolean returnSelectionNumInfo;

    @ApiModelProperty(value = "多选id（删除导出时使用）")
    private List<Long> ids;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "排序字段(默认按上课时间) 0-按上课时间 1-按上课日期 2-按创建时间")
    @Range(min = 0, max = 2, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序排列(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;
}
