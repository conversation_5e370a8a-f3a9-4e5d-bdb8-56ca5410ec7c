package com.unicom.swdx.module.edu.controller.admin.trainee.excelimporthandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetNationSheetWriteHandler implements SheetWriteHandler {

    @Resource
    private TraineeMapper traineeMapper;

    public GetNationSheetWriteHandler(TraineeMapper traineeMapper){

        this.traineeMapper = traineeMapper;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Map<Integer, String[]> mapDropDown = new HashMap<>();
        //获取字典表中的下来label
        List<ClassInfoImportDictLableExcelVO> label = traineeMapper.getDictTypeByDictLabel(3);

        // 初始化 downArray，用于存储动态数据
        String[] downArray = new String[label.size()];

        // 遍历 label 列表，将数据填入 downArray
        for (int i = 0; i < label.size(); i++) {
            downArray[i] = label.get(i).getLabel(); // 假设 getClassName() 是获取类名的方法
        }
//        String[] downArray = {"校本部"};
        mapDropDown.put(6, downArray);
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper dvhelper = sheet.getDataValidationHelper();
        for (Map.Entry<Integer, String[]> entry : mapDropDown.entrySet()) {
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10001, entry.getKey(), entry.getKey());
            if (entry.getValue().length > 0) {
                DataValidationConstraint constraint = dvhelper.createExplicitListConstraint(entry.getValue());
                DataValidation dataValidation = dvhelper.createValidation(constraint, addressList);
                dataValidation.setSuppressDropDownArrow(true);
                dataValidation.setShowErrorBox(true);
                dataValidation.createErrorBox("提示", "此值与单元格定义格式不一致");
                dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                sheet.addValidationData(dataValidation);
            }
        }

    }
}
