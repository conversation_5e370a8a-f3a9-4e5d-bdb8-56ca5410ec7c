package com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 教学计划模版配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanTemplateConfigRespVO extends PlanTemplateConfigBaseVO {

    @ApiModelProperty(value = "唯一标识符，自增", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
