package com.unicom.swdx.module.edu.service.classcommen;


import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.enums.userrole.JwUserRoleTypeEnum;

import java.util.List;
import java.util.Map;

public interface CommenService {

    /**
     * 获取全部学生 - 请假学生
     * @param classcourseid
     * @param classid
     * @return
     */

    List<TraineeDO> getUsersByClasscourseid(Long classcourseid, Long classid);


    /**
     * 获取全部学生
     * @param classcourseid
     * @param classid
     * @return
     */
    List<TraineeDO> getAllUsersByClasscourseid(Long classcourseid, Long classid);

    /**
     * 获取全部学生 - 请假学生、未签到学生
     * @param classcourseid
     * @param classid
     * @return
     */
    List<TraineeDO> getCheckedUserByClassCourseId(Long classcourseid, Long classid);

    /**
     * 教职工分配学员和小程序入口
     * @param userIds 业中用户id列表
     * @return 小程序用户id列表
     */
    void addTeacherRole(List<Long> userIds);

    /**
     * 获取用户在教务的基本角色 (教师、学员、调训单位管理员)
     * （由于未使用user_role表，通过查相关表实现获取用户角色的公共实现）
     * （随着角色增加、查询的sql越多）
     * @param userId 用户id
     * @return 角色列表 {@link JwUserRoleTypeEnum}
     */
    List<Integer> getJwUserRoleByUserId(Long userId);

    /**
     * 获取多个用户在教务的基本角色 (教师、学员、调训单位管理员)
     * （由于未使用user_role表，通过查相关表实现获取用户角色的公共实现）
     * （随着角色增加、查询的sql越多）
     * @param userIds 用户id列表
     * @return Map<userId, 角色列表> {@link JwUserRoleTypeEnum}
     */
    Map<Long, List<Integer>> getJwUserRoleByUserIdList(List<Long> userIds);
}
