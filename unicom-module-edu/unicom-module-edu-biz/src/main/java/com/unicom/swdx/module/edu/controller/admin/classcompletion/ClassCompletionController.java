package com.unicom.swdx.module.edu.controller.admin.classcompletion;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.ClassCompletionInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.ClassCompletionRespVO;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.SaveClassCompletionInfoReqVO;
import com.unicom.swdx.module.edu.service.completiontemplate.CompletionTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 结业考核模版设置")
@RestController
@RequestMapping("/edu/completion")
@Validated
public class ClassCompletionController {

    @Resource
    private CompletionTemplateService completionTemplateService;


    @ApiOperation("下载模板")
    @GetMapping("/get-template")
    public void getTemplate(HttpServletRequest request,Integer classId,HttpServletResponse response) throws IOException {
        completionTemplateService.exportExcel(request,classId,0,response);
    }

    @GetMapping("/getClassCompletionTitle")
    @ApiOperation("表头")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<ClassCompletionRespVO> getClassCompletion(Long classId) {
        ClassCompletionRespVO respVO = completionTemplateService.getClassCompletion(classId);
        return success(respVO);
    }


    @GetMapping("/regenerate")
    @ApiOperation("重新生成")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<Boolean> regenerate(Long classId) {
        completionTemplateService.regenerate(classId);
        return success(true);
    }


    @GetMapping("/getClassCompletionInfo")
    @ApiOperation("数据")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<List<ClassCompletionInfoRespVO>> getClassCompletionInfo(HttpServletRequest request, Integer classId) {
        List<ClassCompletionInfoRespVO> list = completionTemplateService.getClassCompletionInfo(request,classId);
        return success(list);
    }

    @ApiOperation("导出")
    @GetMapping("/export")
    public void export(HttpServletRequest request,Integer classId,HttpServletResponse response) throws IOException {
        completionTemplateService.exportExcel(request,classId,1,response);
    }

    @PostMapping("/saveInfo")
    @ApiOperation("保存数据")
    @PreAuthorize("@ss.hasPermission('edu:completion-template:query')")
    public CommonResult<Boolean> saveInfo(@RequestBody List<SaveClassCompletionInfoReqVO> reqVOs) {
        completionTemplateService.saveInfo(reqVOs);
        return success(true);
    }


}
