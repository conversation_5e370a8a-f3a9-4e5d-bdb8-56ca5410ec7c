package com.unicom.swdx.module.edu.controller.admin.noticeannouncement;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExcelVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagerDeleteVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.*;
import com.unicom.swdx.module.edu.convert.noticeannouncement.NoticeAnnouncementConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import com.unicom.swdx.module.edu.dal.mysql.noticeannouncement.NoticeAnnouncementMapper;
import com.unicom.swdx.module.edu.service.noticeannouncement.NoticeAnnouncementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 通知公告")
@RestController
@RequestMapping("/edu/notice-announcement")
@Validated
public class NoticeAnnouncementController {

    @Resource
    private NoticeAnnouncementService noticeAnnouncementService;

    @Resource
    private NoticeAnnouncementMapper noticeAnnouncementMapper;

    @PostMapping("/create")
    @ApiOperation("新增")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:create')")
    public CommonResult<Integer> createNoticeAnnouncement(@Valid @RequestBody NoticeAnnouncementCreateReqVO createReqVO) {
        return success(noticeAnnouncementService.createNoticeAnnouncement(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("修改")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:update')")
    public CommonResult<Boolean> updateNoticeAnnouncement(@Valid @RequestBody NoticeAnnouncementUpdateReqVO updateReqVO) {
        noticeAnnouncementService.updateNoticeAnnouncement(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:delete')")
    public CommonResult<Boolean> deleteNoticeAnnouncement(@RequestParam("id") Integer id) {
        noticeAnnouncementService.deleteNoticeAnnouncement(id);
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:delete')")
    public CommonResult<Boolean> deleteNoticeAnnouncementBatch(@Valid @RequestBody NoticeDeleteVO noticeDeleteVO) {
        noticeAnnouncementService.deleteNoticeAnnouncementBatch(noticeDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("查看详细内容")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:query')")
    public CommonResult<List<NoticeAnnouncementUrlVO>> getNoticeAnnouncement(@RequestParam("id") Integer id) {
        List<NoticeAnnouncementUrlVO> noticeAnnouncementUrlVOs = noticeAnnouncementService.getNoticeAnnouncement(id);

        return success(noticeAnnouncementUrlVOs);
    }

    @GetMapping("/list")
    @ApiOperation("获得EduNoticeAnnouncement列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:query')")
    public CommonResult<List<NoticeAnnouncementRespVO>> getNoticeAnnouncementList(@RequestParam("ids") Collection<Integer> ids) {
        List<NoticeAnnouncementDO> list = noticeAnnouncementService.getNoticeAnnouncementList(ids);
        return success(NoticeAnnouncementConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:query')")
    public CommonResult<PageResult<NoticeAnnouncementRespVO>> getNoticeAnnouncementPage(@Valid NoticeAnnouncementPageReqVO pageVO) {
        PageResult<NoticeAnnouncementRespVO> pageResult = noticeAnnouncementService.getNoticeAnnouncementPage(pageVO);
        return success(pageResult);
    }

    @PostMapping("/isTop")
    @ApiOperation("是否置顶")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:update')")
    public CommonResult<Boolean> updateIsTop(@RequestParam("id") Integer id, @RequestParam("isTop") Integer isTop) {
        if(isTop == 1){
            noticeAnnouncementMapper.isTopNoticeAnnouncement(id, isTop, LocalDateTime.now());
        }else{
            noticeAnnouncementMapper.notIsTopNoticeAnnouncement(id, isTop);
        }

        return success(true);
    }

    @PostMapping("/status")
    @ApiOperation("上下架")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:update')")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Integer id, @RequestParam("status") Integer status) {
        noticeAnnouncementMapper.isUpOrDownNoticeAnnouncement(id, status);
        return success(true);
    }

    @PostMapping("/publish-batch")
    @ApiOperation("批量发布")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:update')")
    public CommonResult<Boolean> updatePublishBatch(@Valid @RequestBody NoticeDeleteVO noticeDeleteVO) {
        noticeAnnouncementService.publishBatch(noticeDeleteVO);
        return success(true);
    }

    @GetMapping("/export-excel")
    @ApiOperation("通知公告导出")
    @PreAuthorize("@ss.hasPermission('edu:notice-announcement:export')")
    public void exportNoticeAnnouncementExcel(NoticeAnnouncementExportVO exportReqVO,
                                              HttpServletResponse response) throws IOException {
        noticeAnnouncementService.getNoticeAnnouncementInfoList(exportReqVO,response);
    }

    @GetMapping("/selectClassManagementList")
    @ApiOperation("查询班级")
    public HashMap<Integer, List<ClassManagementDO>> selectClassManagementList(ClassManagementPageReqVO reqVO){
        return  noticeAnnouncementService.selectClassManagementList(reqVO);
    }


}
