package com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 班级考勤日历 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassClockCalendarRespVO extends ClassClockCalendarBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
