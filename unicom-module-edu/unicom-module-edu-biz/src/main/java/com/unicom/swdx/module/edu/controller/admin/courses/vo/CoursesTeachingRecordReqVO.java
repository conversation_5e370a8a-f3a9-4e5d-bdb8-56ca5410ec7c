package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 课程库授课记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CoursesTeachingRecordReqVO extends PageParam {
    @ApiModelProperty(value = "课程ID", required = true,example = "1")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @ApiModelProperty(value = "班次名称", example = "五班")
    private String className;

    @ApiModelProperty(value = "授课教师姓名", example = "李四")
    private String teacherName;

    @ApiModelProperty(value = "授课开始时间", example = "2021-01-01 00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime classStartTime;

    @ApiModelProperty(value = "授课结束时间", example = "2021-01-01 00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime classEndTime;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)", example = "[1, 2]")
    private Set<Integer> includeColumnIndexes;

    @ApiModelProperty(value = "排序字段(默认按授课时间) 0-按授课时间 1-按班次名称")
    @Range(min = 0, max = 1, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;
}
