package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@ApiModel(value = "参训系统导出", description = "参数和 NoticeAnnouncementPageReqVO 是一致的")
@Data
public class NoticeAnnouncementExportModule2ReqVO {

    @ExcelProperty("标题")
    @ApiModelProperty(value = "标题序号 0")
    private String title;

    @ExcelProperty("附件")
    @ApiModelProperty(value = "附件序号 1")
    private String fileName;

    @ExcelProperty(value = "发布用户")
    @ApiModelProperty(value = "发布时间序号 2")
    private String publisher;

    @ExcelProperty("发布时间")
    @ApiModelProperty(value = "发布时间序号 3")
    private LocalDate publishTime;

}
