package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 课程互换 Request VO")
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CourseExchangeReqVO extends CourseChangeBaseVO{

    @ApiModelProperty(value = "申请排班的单元格ID")
    private Long applyId;

    @ApiModelProperty(value = "交换排班的单元格ID")
    private Long exchangeId;

}
