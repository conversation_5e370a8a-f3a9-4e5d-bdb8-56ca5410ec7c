package com.unicom.swdx.module.edu.service.noticeannouncement;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.*;
import com.unicom.swdx.module.edu.convert.noticeannouncement.NoticeAnnouncementConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.noticeannouncement.NoticeAnnouncementMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.NOTICE_ANNOUNCEMENT_NOT_EXISTS;


/**
 * EduNoticeAnnouncement Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NoticeAnnouncementServiceImpl implements NoticeAnnouncementService {

    @Resource
    private NoticeAnnouncementMapper noticeAnnouncementMapper;

    @Resource
    private ClassManagementService classManagementService;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Override
    public Integer createNoticeAnnouncement(NoticeAnnouncementCreateReqVO createReqVO) {

        //如果直接发版  状态上架
        if(createReqVO.getIsPublish() == 1){
            createReqVO.setStatus(1);
            createReqVO.setPublishTime(LocalDateTime.now());
        }else{
            //存草稿箱
            createReqVO.setDraftsTime(LocalDateTime.now());
        }

        //置顶 需要插入置顶时间
        if(createReqVO.getIsTop() == 1){
            createReqVO.setTopTime(LocalDateTime.now());
        }

        // 插入
        NoticeAnnouncementDO noticeAnnouncement = NoticeAnnouncementConvert.INSTANCE.convert(createReqVO);
        noticeAnnouncementMapper.insert(noticeAnnouncement);

        //插入附件
        if(StringUtils.isNotBlank(createReqVO.getFileUrl())){

            String[] dataArray = createReqVO.getFileUrl().split(",");
            String[] dataArrayName = createReqVO.getFileName().split(",");
            String[] dataFileSize = createReqVO.getFileSize().split(",");


            // 确保两个数组的长度相同，以避免数组越界
            if (dataArray.length == dataArrayName.length) {
                for (int i = 0; i < dataArray.length; i++) {
                    String data = dataArray[i].trim();
                    String fileName = dataArrayName[i].trim();
                    String fileSize = dataFileSize[i].trim();

                    NoticeAnnouncementUrlDO noticeAnnouncementUrlDO = new NoticeAnnouncementUrlDO();
                    noticeAnnouncementUrlDO.setNoticeId(noticeAnnouncement.getId());
                    noticeAnnouncementUrlDO.setUrl(data);
                    // 设置文件名
                    noticeAnnouncementUrlDO.setFileName(fileName);
                    noticeAnnouncementUrlDO.setFileSize(fileSize);

                    // 插入到数据库中
                    noticeAnnouncementMapper.setNoticeAnnouncementUrl(noticeAnnouncementUrlDO);
                }
            } else {
                // 处理长度不一致的情况，可以抛出异常或者记录日志
                throw new IllegalArgumentException("文件 URL 和文件名长度不一致");
            }
        }

        resultCachenotice.invalidateAll();


        // 返回
        return noticeAnnouncement.getId();
    }

    @Override
    public void updateNoticeAnnouncement(NoticeAnnouncementUpdateReqVO updateReqVO) {

        // 校验存在
        this.validateNoticeAnnouncementExists(updateReqVO.getId());

        //如果直接发版  状态上架
        if(updateReqVO.getIsPublish() == 1){
            updateReqVO.setStatus(1);
            updateReqVO.setPublishTime(LocalDateTime.now());
        }else{
            //存草稿箱
            updateReqVO.setDraftsTime(LocalDateTime.now());
        }

        //置顶 需要插入置顶时间
        if(updateReqVO.getIsTop() == 1){
            updateReqVO.setTopTime(LocalDateTime.now());
        }

        //获取更新值得  url  与  文件名称
        //后插入附件
        if(StringUtils.isNotBlank(updateReqVO.getFileUrl())){

            // 先删除
            noticeAnnouncementMapper.deleteNoticeAnnouncementUrl(updateReqVO.getId());

            String[] dataArray = updateReqVO.getFileUrl().split(",");
            String[] dataArrayName = updateReqVO.getFileName().split(",");
            String[] dataFileSize = updateReqVO.getFileSize().split(",");

            // 确保两个数组的长度相同，以避免数组越界
            if (dataArray.length == dataArrayName.length) {
                for (int i = 0; i < dataArray.length; i++) {
                    String data = dataArray[i].trim();
                    String fileName = dataArrayName[i].trim();
                    String fileSize = dataFileSize[i].trim();


                    NoticeAnnouncementUrlDO noticeAnnouncementUrlDO = new NoticeAnnouncementUrlDO();
                    noticeAnnouncementUrlDO.setNoticeId(updateReqVO.getId());
                    noticeAnnouncementUrlDO.setUrl(data);
                    // 设置文件名
                    noticeAnnouncementUrlDO.setFileName(fileName);
                    // 设置文件大小
                    noticeAnnouncementUrlDO.setFileSize(fileSize);
                    // 插入到数据库中
                    noticeAnnouncementMapper.setNoticeAnnouncementUrl(noticeAnnouncementUrlDO);
                }
            } else {
                // 处理长度不一致的情况，可以抛出异常或者记录日志
                throw new IllegalArgumentException("文件 URL 和文件名长度不一致");
            }
        }


        // 更新
        NoticeAnnouncementDO updateObj = NoticeAnnouncementConvert.INSTANCE.convert(updateReqVO);
        noticeAnnouncementMapper.updateById(updateObj);

        resultCachenotice.invalidateAll();
    }

    @Override
    public void deleteNoticeAnnouncement(Integer id) {
        // 校验存在
        this.validateNoticeAnnouncementExists(id);
        // 先删除 url 表中的id
        noticeAnnouncementMapper.deleteNoticeAnnouncementUrl(id);
        // 删除
        noticeAnnouncementMapper.deleteById(id);

        resultCachenotice.invalidateAll();
    }

    /**
     * 批量删除EduNoticeAnnouncement
     *
     * @param noticeDeleteVO
     */
    @Override
    public void deleteNoticeAnnouncementBatch(NoticeDeleteVO noticeDeleteVO) {
        String ids = noticeDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");

        for (String idStr : idArray){

            //转化成 int 类型
            int id = Integer.parseInt(idStr);
            // 校验存在
            this.validateNoticeAnnouncementExists(id);
            // 先删除 url 表中的id
            noticeAnnouncementMapper.deleteNoticeAnnouncementUrl(id);
            // 删除
            noticeAnnouncementMapper.deleteById(id);
        }

        resultCachenotice.invalidateAll();
    }

    private void validateNoticeAnnouncementExists(Integer id) {
        if (noticeAnnouncementMapper.selectById(id) == null) {
            throw exception(NOTICE_ANNOUNCEMENT_NOT_EXISTS);
        }
    }


    public List<NoticeAnnouncementUrlVO> getNoticeAnnouncement(Integer id) {
        List<NoticeAnnouncementUrlDO> noticeAnnouncementUrlDOS = noticeAnnouncementMapper.selectByOneId(id);
        List<NoticeAnnouncementUrlVO> noticeAnnouncementUrlVOs = NoticeAnnouncementConvert.INSTANCE.doConvertUrlVO(noticeAnnouncementUrlDOS);

        List<String> classIds = noticeAnnouncementUrlDOS.stream().filter(Objects::nonNull).map(NoticeAnnouncementUrlDO::getClassIds).collect(Collectors.toList());
        HashSet<Long> ids=new HashSet<>();

        //获取数组返回来的所有classId，一次性从数据库查询
        for (String classId : classIds) {
            if(classId!=null){
                String[] numbers = classId.split(",");
                for (String number : numbers) {
                    if(number!=null){
                        ids.add(Long.valueOf(number));
                    }
                }
            }
        }

        //根据班级id获取班级名字，并用,隔开
        if(CollectionUtil.isNotEmpty(ids)) {
            List<ClassManagementDO> classManagementDOS = classManagementMapper.selectBatchIds(ids);


            for (NoticeAnnouncementUrlVO noticeAnnouncementUrlVO : noticeAnnouncementUrlVOs) {
                if (noticeAnnouncementUrlVO.getClassIds() != null) {
                    List<Long> idList = new ArrayList<>();
                    String[] numbers = noticeAnnouncementUrlVO.getClassIds().split(",");
                    for (String number : numbers) {
                        idList.add(Long.valueOf(number));
                    }
                    List<ClassManagementDO> classList = classManagementDOS.stream().filter(o -> idList.contains(o.getId())).sorted(Comparator.comparingInt(c -> idList.indexOf(c.getId()))).collect(Collectors.toList());
                    String classNames = classList.stream().map(ClassManagementDO::getClassName).collect(Collectors.joining(","));
                    noticeAnnouncementUrlVO.setClassNames(classNames);
                }
            }

        }


        return noticeAnnouncementUrlVOs;
    }

    @Override
    public List<NoticeAnnouncementDO> getNoticeAnnouncementList(Collection<Integer> ids) {
        return noticeAnnouncementMapper.selectBatchIds(ids);
    }



    //缓存一份字典 访问太高
    public static Cache<NoticeAnnouncementPageReqVO, PageResult<NoticeAnnouncementRespVO> > resultCachenotice=
            CacheBuilder.newBuilder()
                    .initialCapacity(1024) // 初始容量
                    .maximumSize(4096)   // 设定最大容量
                    .expireAfterWrite(5L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    @Override
    public PageResult<NoticeAnnouncementRespVO> getNoticeAnnouncementPage(NoticeAnnouncementPageReqVO pageReqVO) {

        PageResult<NoticeAnnouncementRespVO>  listtemp =null;
        try {
            listtemp = resultCachenotice.get(pageReqVO, () -> {
                //替换掉特殊符号  %  _
                if (StringUtils.isNotBlank(pageReqVO.getTitle())){
                    pageReqVO.setTitle(pageReqVO.getTitle().replaceAll("([%_])", "\\\\$1"));
                }
                if (StringUtils.isNotBlank(pageReqVO.getPublisher())){
                    pageReqVO.setPublisher(pageReqVO.getPublisher().replaceAll("([%_])", "\\\\$1"));
                }

                Long classId=null;

                if(pageReqVO.getTraineeId()!=null) {
                    TraineeDO traineeDO = traineeMapper.selectById(pageReqVO.getTraineeId());
                    if(traineeDO!=null&&traineeDO.getClassId()!=null){
                        classId=traineeDO.getClassId();
                    }
                }

                Page buildPage = MyBatisUtils.buildPage(pageReqVO);

                List<NoticeAnnouncementDO> noticeAnnouncementDOList = noticeAnnouncementMapper.selectPageList(buildPage, pageReqVO,classId);

                //每条数据增加附件
                List<NoticeAnnouncementRespVO> listResult = new ArrayList<>();

                if (noticeAnnouncementDOList.isEmpty()){
                    return new PageResult<>(listResult, buildPage.getTotal());
                }
                
                List<Integer> noticeAnnouncementIds = noticeAnnouncementDOList.stream().map(NoticeAnnouncementDO::getId).collect(Collectors.toList());

                List<NoticeAnnouncementUrlDO> noticeAnnouncementUrlList = noticeAnnouncementMapper.selectByOneIds(noticeAnnouncementIds);

                Map<Integer, List<NoticeAnnouncementUrlDO>> noticeAnnouncementMap = noticeAnnouncementUrlList.stream()
                        .filter(item->item.getNoticeId()!=null)
                        .collect(Collectors.groupingBy(NoticeAnnouncementUrlDO::getNoticeId));
                for(NoticeAnnouncementDO noticeAnnouncementDO:noticeAnnouncementDOList){
//                        List<NoticeAnnouncementUrlDO> list = noticeAnnouncementMapper.selectByOneId(noticeAnnouncementDO.getId());
                    List<NoticeAnnouncementUrlDO> list = noticeAnnouncementMap.getOrDefault(noticeAnnouncementDO.getId(), new ArrayList<>());
                    List<String> fileName = new ArrayList<>();
                    List<String> fileUrl = new ArrayList<>();
                    for(NoticeAnnouncementUrlDO noticeAnnouncementUrlDO : list){
                        fileName.add(noticeAnnouncementUrlDO.getFileName());
                        fileUrl.add(noticeAnnouncementUrlDO.getUrl());
                    }
                    NoticeAnnouncementRespVO result = NoticeAnnouncementConvert.INSTANCE.convert(noticeAnnouncementDO);
                    result.setFileName(fileName.stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
                    result.setFileUrl(fileUrl.stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
                    listResult.add(result);
                }

//                if(pageReqVO.getTraineeId()!=null){
//                    TraineeDO traineeDO = traineeMapper.selectById(pageReqVO.getTraineeId());
//                    if(traineeDO!=null&&traineeDO.getClassId()!=null){
//                        LambdaQueryWrapper<NoticeAnnouncementDO> lqw=new LambdaQueryWrapper<>();
//                        lqw.like(NoticeAnnouncementDO::getClassIds,traineeDO.getClassId());
//                        List<NoticeAnnouncementDO> noticeAnnouncementDOS = noticeAnnouncementMapper.selectList(lqw);
//                        List<NoticeAnnouncementRespVO> noticeAnnouncementRespVOS = NoticeAnnouncementConvert.INSTANCE.convertList(noticeAnnouncementDOS);
//                        if(CollectionUtil.isNotEmpty(listResult)){
//                            listResult.addAll(noticeAnnouncementRespVOS);
//                        }
//                    }
//                }


//                if(pageReqVO.getClassTeacherLead()!=null){
//                    LambdaQueryWrapper<TeacherInformationDO> lqw=new LambdaQueryWrapper<>();
//                    lqw.eq(TeacherInformationDO::getSystemId,pageReqVO.getClassTeacherLead());
//                    TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectOne(lqw);
//                    if(teacherInformationDO!=null){
//                        List<Long> ids =  classManagementMapper.getClassIdListByTeacherId(teacherInformationDO.getId());
////                        LambdaQueryWrapper<NoticeAnnouncementDO> lambdaQueryWrapper=new LambdaQueryWrapper<>();
////
////                        if(ids!=null){
////                            lambdaQueryWrapper.and(wrapper -> {
////                                // 遍历ids，给每个classId添加LIKE查询条件
////                                ids.forEach(classId -> wrapper.or().like(NoticeAnnouncementDO::getClassIds, classId ));
////                            });
//
//                        if(CollectionUtil.isNotEmpty(ids)){
//                            List<NoticeAnnouncementDO> noticeAnnouncementDOS = noticeAnnouncementMapper.selectByClassIds(ids);
//                            noticeAnnouncementDOS= noticeAnnouncementDOS.stream().distinct().collect(Collectors.toList());
//                            List<NoticeAnnouncementRespVO> noticeAnnouncementRespVOS = NoticeAnnouncementConvert.INSTANCE.convertList(noticeAnnouncementDOS);
//                            if(CollectionUtil.isNotEmpty(listResult)){
//                                listResult.addAll(noticeAnnouncementRespVOS);
//                            }
//                        }
//                        }
//
//                    }



                PageResult<NoticeAnnouncementRespVO> pageList = new PageResult<>(listResult, buildPage.getTotal());


                return pageList;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return listtemp;




    }

    /**
     * 批量发布
     *
     * @param noticeDeleteVO
     */
    @Override
    public void publishBatch(NoticeDeleteVO noticeDeleteVO) {
        String ids = noticeDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");

        for (String idStr : idArray) {

            //转化成 int 类型
            int id = Integer.parseInt(idStr);
            // 校验存在
            this.validateNoticeAnnouncementExists(id);
            // 发布
            noticeAnnouncementMapper.updatePublishById(id,LocalDateTime.now());

        }
        resultCachenotice.invalidateAll();
    }

    /**
     * 获得EduClassManagement列表, 用于 Excel 导出
     *
     * @param reqVO
     * @return
     */
    @Override
    public void getNoticeAnnouncementInfoList(NoticeAnnouncementExportVO reqVO, HttpServletResponse response) throws IOException{


        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(reqVO.getTitle())){
            reqVO.setTitle(reqVO.getTitle().replaceAll("([%_])", "\\\\$1"));
        }
        if (StringUtils.isNotBlank(reqVO.getPublisher())){
            reqVO.setPublisher(reqVO.getPublisher().replaceAll("([%_])", "\\\\$1"));
        }

        List<NoticeAnnouncementDO> noticeAnnouncementDOList = noticeAnnouncementMapper.selectListInfo(reqVO);
        //学员管理系统通知公告导出
        if(reqVO.getModuleCode() == 1){

            List<NoticeAnnouncementExportReqVO> excelVOList = new ArrayList<>();
            for(NoticeAnnouncementDO dataList : noticeAnnouncementDOList){

                NoticeAnnouncementExportReqVO list = new NoticeAnnouncementExportReqVO();

                if(dataList.getTitle() != null){
                    list.setTitle(dataList.getTitle());
                }

                if(dataList.getStatus() != null){
                    if(dataList.getStatus() == 1){
                        list.setStatus("上架");
                    }else{
                        list.setStatus("下架");
                    }
                }

                if(dataList.getPublishTime() != null && !dataList.getPublishTime().equals("")){
                    list.setPublishTime(dataList.getPublishTime().toLocalDate());
                }

                if(dataList.getPublisher() != null){
                    list.setPublisher(dataList.getPublisher());
                }

                if (dataList.getDraftsTime() != null && !dataList.getDraftsTime().equals("")) {
                    list.setDraftsTime(dataList.getDraftsTime().toLocalDate());
                }

                excelVOList.add(list);
            }

            // 导出 Excel
            ExcelUtils.writeByIncludeColumnIndexes(response, "通知公告信息列表.xls",
                    "数据", NoticeAnnouncementExportReqVO.class, excelVOList, reqVO.getIncludeColumnIndexes());
        }else if(reqVO.getModuleCode() == 2) {

            //每条数据增加附件
            List<NoticeAnnouncementRespVO> listResult = new ArrayList<>();
            for(NoticeAnnouncementDO noticeAnnouncementDO:noticeAnnouncementDOList){
                List<NoticeAnnouncementUrlDO> list = noticeAnnouncementMapper.selectByOneId(noticeAnnouncementDO.getId());
                List<String> fileName = new ArrayList<>();
                List<String> fileUrl = new ArrayList<>();
                for(NoticeAnnouncementUrlDO noticeAnnouncementUrlDO : list){
                    fileName.add(noticeAnnouncementUrlDO.getFileName());
                    fileUrl.add(noticeAnnouncementUrlDO.getUrl());
                }
                NoticeAnnouncementRespVO result = NoticeAnnouncementConvert.INSTANCE.convert(noticeAnnouncementDO);
                result.setFileName(fileName.stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
                result.setFileUrl(fileUrl.stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
                listResult.add(result);
            }

            //参训系统通知公告导出
            List<NoticeAnnouncementExportModule2ReqVO> excelVOList = new ArrayList<>();
            for (NoticeAnnouncementRespVO dataList : listResult) {

                NoticeAnnouncementExportModule2ReqVO list = new NoticeAnnouncementExportModule2ReqVO();

                if (dataList.getTitle() != null) {
                    list.setTitle(dataList.getTitle());
                }

                if(StringUtils.isNotEmpty(dataList.getFileName())){
                    list.setFileName(dataList.getFileName());
                }

                if (dataList.getPublishTime() != null && !dataList.getPublishTime().equals("")) {
                    list.setPublishTime(dataList.getPublishTime().toLocalDate());
                }

                if (dataList.getPublisher() != null) {
                    list.setPublisher(dataList.getPublisher());
                }

                excelVOList.add(list);
            }

            // 导出 Excel
            ExcelUtils.writeByIncludeColumnIndexes(response, "通知公告信息列表.xls",
                    "数据", NoticeAnnouncementExportModule2ReqVO.class, excelVOList, reqVO.getIncludeColumnIndexes());

        }

    }

    @Override
    public  HashMap<Integer, List<ClassManagementDO>> selectClassManagementList(ClassManagementPageReqVO reqVO) {

        if(reqVO.getClassTeacherLead()!=null){
            LambdaQueryWrapper<TeacherInformationDO> lqw=new LambdaQueryWrapper<>();
            lqw.eq(TeacherInformationDO::getSystemId,reqVO.getClassTeacherLead());
            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectOne(lqw);
            if(teacherInformationDO!=null){
                reqVO.setClassTeacherLead(teacherInformationDO.getId());
            }
        }

        //报名中
//        ClassManagementPageReqVO reqVO=new ClassManagementPageReqVO();
        reqVO.setTag(0);
        reqVO.setChange(1);
        reqVO.setClassStatus(0);
        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectClassManagementList(reqVO);

        //报名结束
        ClassManagementPageReqVO reqVO1=new ClassManagementPageReqVO();
        reqVO.setTag(0);
        reqVO.setChange(1);
        reqVO.setClassStatus(1);
        List<ClassManagementDO> classManagementDOList1 = classManagementMapper.selectClassManagementList(reqVO);

        //开班中
        ClassManagementPageReqVO reqVO2=new ClassManagementPageReqVO();
        reqVO.setTag(0);
        reqVO.setChange(1);
        reqVO.setClassStatus(2);
        List<ClassManagementDO> classManagementDOList2 = classManagementMapper.selectClassManagementList(reqVO);

        HashMap<Integer, List<ClassManagementDO>> hashMap=new HashMap<>();

        hashMap.put(0,classManagementDOList);
        hashMap.put(1,classManagementDOList1);
        hashMap.put(2,classManagementDOList2);

        return hashMap;

    }

}
