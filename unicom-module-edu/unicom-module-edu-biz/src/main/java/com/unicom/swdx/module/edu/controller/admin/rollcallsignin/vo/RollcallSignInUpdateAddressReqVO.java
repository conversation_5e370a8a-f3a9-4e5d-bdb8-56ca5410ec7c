package com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("管理后台 - 大课考勤、点名签到位置信息更新 Request VO")
@Data
public class RollcallSignInUpdateAddressReqVO {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private Long id;

    @ApiModelProperty(value = "打卡位置的纬度", required = true, example = "10.2552")
    @NotNull(message = "打卡位置的纬度不能为空")
    private String latitude;

    @ApiModelProperty(value = "打卡位置的经度", required = true, example = "20.25552")
    @NotNull(message = "打卡位置的经度不能为空")
    private String longitude;

    @ApiModelProperty(value = "打卡位置的地址", example = "中国")
    @NotNull(message = "打卡位置的地址不能为空")
    private String address;

    @ApiModelProperty(value = "打卡范围半径", required = true, example = "50.50")
    @NotNull(message = "打卡范围半径不能为空")
    private BigDecimal radius;

}
