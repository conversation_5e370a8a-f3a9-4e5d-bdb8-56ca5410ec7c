package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 考勤签到更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClockInInfoUpdateReqVO extends ClockInInfoBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    @NotNull(message = "主键id，自增不能为空")
    private Integer id;

}
