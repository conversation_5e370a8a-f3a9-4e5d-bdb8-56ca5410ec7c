package com.unicom.swdx.module.edu.dal.mysql.leavereport;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LeaveReportDetailMapper extends BaseMapper<LeaveReportDetailDO> {
    List<LeaveReportDetailDO> selectByStudentId(@Param("studentId") Long studentId);
}
