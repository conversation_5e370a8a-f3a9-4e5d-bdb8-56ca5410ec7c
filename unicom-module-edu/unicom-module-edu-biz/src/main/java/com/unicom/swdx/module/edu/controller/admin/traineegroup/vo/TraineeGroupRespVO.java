package com.unicom.swdx.module.edu.controller.admin.traineegroup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * @ClassName: TraineeGroupReqVO
 * @Author: lty
 * @Date: 2024/10/17 17:03
 */
@Data
public class TraineeGroupRespVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty(value = "序号")
    private Long index;
    @ApiModelProperty(value = "组内排序")
    private Integer groupSort;
    @ApiModelProperty(value = "组名id")
    private String groupId;
    @ApiModelProperty(value = "组名")
    private String groupName;
    @ApiModelProperty(value = "学员姓名")
    private String name;
    @ApiModelProperty(value = "性别")
    private String sex;
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;
    @ApiModelProperty(value = "文化程度")
    private Integer educationalLevel;
    @ApiModelProperty(value = "政治面貌")
    private Integer politicalIdentity;
    @ApiModelProperty(value = "学员职务")
    private String position;

    @ApiModelProperty(value = "班委职务id")
    private String classCommitteeId;

    @ApiModelProperty(value = "班委职务")
    private String classCommitteeName;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long preId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long nextId;
}
