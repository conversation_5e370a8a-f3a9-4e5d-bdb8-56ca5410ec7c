package com.unicom.swdx.module.edu.controller.admin.plan.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

import javax.validation.constraints.NotNull;


@Data
public class PlanExcelVO {

    @ExcelProperty("唯一标识")
    private Long id;

    @ExcelProperty("计划名称")
    private String name;

    @ExcelProperty("开始日期")
    private String beginDate;

    @ExcelProperty("结束日期")
    private String endDate;

//    @ExcelProperty("时间段（0上午，1下午，2晚上）")
//    private String period;
//
//    @ExcelProperty("开始时间")
//    private LocalDateTime beginTime;
//
//    @ExcelProperty("结束时间")
//    private LocalDateTime endTime;

    @ExcelProperty("常用教室id")
    private Long classroomId;

    @ExcelProperty("班级id")
    private Long classId;

    //todo 替换为String转换
    @ExcelProperty("状态：0暂存，1应用")
    private Boolean status;
}
