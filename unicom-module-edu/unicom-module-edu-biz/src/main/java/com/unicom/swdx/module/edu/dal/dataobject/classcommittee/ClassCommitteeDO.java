package com.unicom.swdx.module.edu.dal.dataobject.classcommittee;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * @ClassName: TraineeDO
 * @Author: lty
 * @Date: 2024/10/9 11:40
 */
@TableName(value = "edu_class_committee")
@KeySequence("edu_class_committee_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCommitteeDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long classId;

    private String classCommitteeName;
    private Boolean nameShow;
    private Integer tenantId;

}
