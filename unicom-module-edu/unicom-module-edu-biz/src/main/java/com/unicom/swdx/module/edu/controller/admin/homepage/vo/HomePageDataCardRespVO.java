package com.unicom.swdx.module.edu.controller.admin.homepage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: HomePageDataCardRespVO
 * @Author: lty
 * @Date: 2024/11/15 10:21
 */
@Data
public class HomePageDataCardRespVO {

    // 当前在校学员
    @ApiModelProperty(value = "当前在校学员")
    private Integer currentStudents;

    // 本年度结业人员
    @ApiModelProperty(value = "本年度结业人员")
    private Integer graduatesThisYear;

    // 当前开班班次
    @ApiModelProperty(value = "当前开班班次")
    private Integer currentClasses;

    // 即将开班班次
    @ApiModelProperty(value = "即将开班班次")
    private Integer upcomingClasses;

    // 本年度结业班次
    @ApiModelProperty(value = "本年度结业班次")
    private Integer graduatingClassesThisYear;
}
