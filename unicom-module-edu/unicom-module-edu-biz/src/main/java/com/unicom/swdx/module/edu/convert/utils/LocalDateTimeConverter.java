package com.unicom.swdx.module.edu.convert.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class LocalDateTimeConverter implements Converter<LocalDateTime> {
    private static  final String PATTERN_YYYY_MM = "yyyy-MM";
    private static  final String PATTERN_YYYY_MM_DD = "yyyy-MM-dd";


    @Override
    public Class<LocalDateTime> supportJavaTypeKey() {
        return LocalDateTime.class;
    }

    /**
     * easyExcel导入数据类型转换
     * @param cellData
     * @param contentProperty
     * @param globalConfiguration
     * @return
     * @throws Exception
     */
    @Override
    public LocalDateTime convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        BigDecimal numberValue = cellData.getNumberValue();
        if (StrUtil.isBlank(value) && numberValue == null) {
            return null;
        }
        if (StrUtil.isBlank(value)) {
            long second = numberValue.multiply(new BigDecimal("86400")).longValue();
            Instant instant = Instant.ofEpochSecond(second-2209190400L);
            return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        }
        if(Objects.equals(value.length(),7)){
            value = value+"-01";
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(PATTERN_YYYY_MM_DD);
        LocalDate parse = LocalDate.parse(value, dateTimeFormatter);
        return LocalDateTime.of(parse, LocalTime.MIN);
    }

    /**
     * easyExcel导出数据类型转换
     * @param context
     * @return
     * @throws Exception
     */
    @Override
    public WriteCellData<String> convertToExcelData(WriteConverterContext<LocalDateTime> context) throws Exception {
        LocalDateTime localDateTime = context.getValue();
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return new WriteCellData<>(localDateTime.toLocalDate().toString());
    }

}
