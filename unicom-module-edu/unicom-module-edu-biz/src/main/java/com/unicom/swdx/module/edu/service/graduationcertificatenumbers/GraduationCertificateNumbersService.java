package com.unicom.swdx.module.edu.service.graduationcertificatenumbers;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageRespVO;

/**
 * <AUTHOR>
 * @Description: 学员结业证书编号 Service 接口
 * @date 2024-11-22
 */
public interface GraduationCertificateNumbersService {

    /**
     * 学员结业证书编号 分页查询
     *
     * @param reqVO 请求参数
     * @return 分页结果
     */
    PageResult<GraduationCertificateNumbersPageRespVO> selectPage(GraduationCertificateNumbersPageReqVO reqVO);

    /**
     * 生成班级学员结业证书编号
     *
     * @param classId 班级ID
     */
    void generateGraduationCertificateNumbers(Long classId);
}
