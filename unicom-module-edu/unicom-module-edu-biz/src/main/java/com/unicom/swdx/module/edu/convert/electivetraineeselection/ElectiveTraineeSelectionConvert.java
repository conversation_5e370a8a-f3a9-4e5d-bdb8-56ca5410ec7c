package com.unicom.swdx.module.edu.convert.electivetraineeselection;

import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.ElectiveCourseTraineeSelectedExcelVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.ElectiveCourseTraineeSelectedRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 选修课学员选课 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveTraineeSelectionConvert {

    ElectiveTraineeSelectionConvert INSTANCE = Mappers.getMapper(ElectiveTraineeSelectionConvert.class);

    List<ElectiveCourseTraineeSelectedExcelVO> convertList(List<ElectiveCourseTraineeSelectedRespVO> list);
}
