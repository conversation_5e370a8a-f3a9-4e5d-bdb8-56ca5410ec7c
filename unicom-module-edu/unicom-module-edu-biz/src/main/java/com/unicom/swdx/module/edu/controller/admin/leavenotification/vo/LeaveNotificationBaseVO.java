package com.unicom.swdx.module.edu.controller.admin.leavenotification.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 离校报备 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class LeaveNotificationBaseVO {

    @ApiModelProperty(value = "离校报备名称", required = true, example = "第1次离校申请")
    private String name;

    @ApiModelProperty(value = "班级ID", required = true, example = "1024")
    private Long classId;

    @ApiModelProperty(value = "班级名称", required = true, example = "2023级软件开发1班")
    private String className;

    @ApiModelProperty(value = "班主任ID", required = false, example = "2048")
    private Long headTeacherId;

    @ApiModelProperty(value = "班主任姓名", required = false, example = "张老师")
    private String headTeacherName;

    @ApiModelProperty(value = "放假开始时间", required = true)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "放假结束时间", required = true)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "参见 NotificationStatusEnum 枚举")
    private Integer status;

    @ApiModelProperty(value = "备注", example = "五一假期离校报备")
    private String remark;
}