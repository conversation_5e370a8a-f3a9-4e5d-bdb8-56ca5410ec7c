package com.unicom.swdx.module.edu.service.clockininfo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 考勤签到 Service 接口
 *
 * <AUTHOR>
 */
public interface ClockInInfoService {

    /**
     * 创建考勤签到
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createClockInInfo(@Valid ClockInInfoCreateReqVO createReqVO);

    /**
     * 更新考勤签到
     *
     * @param updateReqVO 更新信息
     */
    void updateClockInInfo(@Valid ClockInInfoUpdateReqVO updateReqVO);

    /**
     * 删除考勤签到
     *
     * @param id 编号
     */
    void deleteClockInInfo(Integer id);

    /**
     * 获得考勤签到
     *
     * @param id 编号
     * @return 考勤签到
     */
    ClockInInfoDO getClockInInfo(Integer id);

    /**
     * 获得考勤签到列表
     *
     * @param ids 编号
     * @return 考勤签到列表
     */
    List<ClockInInfoDO> getClockInInfoList(Collection<Integer> ids);

    /**
     * 获得考勤签到分页
     *
     * @param pageReqVO 分页查询
     * @return 考勤签到分页
     */
    PageResult<ClockInInfoReturnVO> getClockInInfoPage(HttpServletRequest request, ClockInInfoPageReqVO pageReqVO);


    MyClockInInfoRespVO getMyClockInInfoList(MyClockInInfoReqVO reqVO);

    /**
     * 考勤三率
     *
     * @param reqVO 请求参数
     * @return 考勤三率
     */
    AttendanceRateRespVO getListForAttendanceThreeRate(AttendanceRateReqVO reqVO);

    /**
     * 业中调用获取考勤三率
     *
     * @param reqVO 请求对象，包含请求参数
     * @return AttendanceRateForYZRespVO 响应数据
     */
    AttendanceRateForBusinessCenterRespVO getListForAttendanceThreeRateForBusinessCenter(AttendanceRateForBusinessCenterReqVO reqVO);

    /**
     * 获取租户是否开启考勤保护
     * @return 考勤保护是否开启 true 开启，false 未开启
     */
    Boolean isEnableAttendanceProtection(Long tenantId);

    /**
     * 获取学生上课未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AttendanceTraineeClassInfoRespVO> getClassTraineeNotArrivedAndLeaveInfo(AttendanceNotArrivedAndLeaveInfoReqVO reqVO);

    /**
     * 获取学生就餐未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AttendanceTraineeMealInfoRespVO> getMealTraineeNotArrivedAndLeaveInfo(AttendanceNotArrivedAndLeaveInfoReqVO reqVO);

    /**
     * 获取学生住宿未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AttendanceTraineeAccommodationInfoRespVO> getAccommodationTraineeNotArrivedAndLeaveInfo(AttendanceNotArrivedAndLeaveInfoReqVO reqVO);

    /**
     * 获取班级某天考勤详情
     *
     * @param date    日期
     * @param classId 班级id
     * @return 班级某天考勤详情
     */
    List<AppAttendanceDetailsRespVO> getDetailsForAppTeacher(String date, Long classId);

    /**
     * 获取一月班级考勤异常的日期列表
     *
     * @param date    年月 yy-mm
     * @param classId 班级id
     * @return 班级考勤异常的日期列表
     */
    List<String> getAbnormalAttendanceDateList(String date, Long classId);

    /**
     * 获取考勤项学员考勤具体情况列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AppTraineeAttendanceDetailsRespVO> getTraineeAttendanceDetails(AppTraineeAttendanceDetailsReqVO reqVO);

    /**
     * 生成学员的每日待打卡记录
     */
    void generateRecords();

    /**
     * 学员移动端-日常考勤
     *
     * @param type
     * @return 当前最近签到打卡信息
     */
    AppClockInInfoRespVO getAppClockInfo(Integer type);

    boolean isTimeOverlap(LocalDateTime beginTime1, LocalDateTime endTime1,
                  LocalDateTime beginTime2, LocalDateTime endTime2);

    /**
     * 学员状态分页
     *
     * @param pageReqVO 分页查询
     * @return 考勤签到分页
     */
    PageResult<ClockInInfoStudentStatusVO> getStudentClockingStatusPage(HttpServletRequest request,ClockInInfoPageReqVO pageReqVO);

    /**
     * 获得列表, 用于 Excel 导出
     *
     * @param reqVO
     * @return
     */
    List<ClockInfoExcelVO> getClockInfoList(HttpServletRequest request,ClockInInfoExcelExportReqVO reqVO);

    /**
     * 学员移动端-大课考勤、点名签到
     *
     * @param type
     * @return
     */
    AppClockInInfoRespVO getRollCallInfo(Integer type);

    /**
     * 移动端-签到
     * @param id
     * @return
     */
    Boolean checkInById(Integer id , Boolean isLate);

    /**
     * 移动端-大课考勤
     * @param id
     * @return
     */
    Boolean checkInLectureById(Integer id , Boolean isLate);

    /**
     * 移动端-点名签到
     * @param id
     * @return
     */
    Boolean checkInRollCallById(Integer id);

    /**
     * 更新学员打卡状态
     * @param recordId 学员打卡记录id
     * @param status 打卡状态
     */
    void updateTraineeAttendanceStatus(Long recordId, Integer status);

    /**
     * 批量更新学员打卡状态
     * 
     * @param recordIds 学员打卡记录id列表
     * @param status    打卡状态
     */
    void batchUpdateTraineeAttendanceStatus(List<Long> recordIds, Integer status);

    List<MyErrorClockInInfoRespVO> getMyErrorClockInInfoList(MyErrorClockInInfoReqVO reqVO);

    /**
     * 批量学员补卡
     * @param reqVO 补卡参数
     */
    void batchUpdateToCheckIn(AppBatchUpdateToCheckInReqVO reqVO);

    void modifyLeaveClockIn(TraineeLeaveDO leaveInfo);

    List<TraineeAttendanceInfoDTO> getLateInfo(AttendanceRateReqVO reqVO);

    Boolean checkInByIdAndMobile(Integer id);


    void deleteClockInInfoByTraineeId(Long traineeId);

    void modifyLeaveCancel(TraineeLeaveDO leaveInfo);

    void deleteClockInInfoByTraineeIds(List<Long> ids);

    /**
     * 获得班级学员考勤详情分页
     *
     * @param pageReqVO 分页查询参数
     * @return 班级学员考勤详情分页
     */
    PageResult<ClassAttendanceDetailRespVO> getClassAttendanceDetailsPage(ClassAttendanceDetailPageReqVO pageReqVO);
}
