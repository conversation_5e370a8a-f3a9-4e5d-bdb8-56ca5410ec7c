package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class RegistrationInfoExcelVO {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "单位名称")
    @ExcelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "名额数")
    @ExcelProperty(value = "名额数")
    private Integer capacity;

    @ApiModelProperty(value = "实际报名人数")
    @ExcelProperty(value = "实际报名人数")
    private Integer actualPeopleNumber;

    @ApiModelProperty(value = "是否限制报名")
    @ExcelProperty(value = "是否限制报名")
    private String isLimit;
}
