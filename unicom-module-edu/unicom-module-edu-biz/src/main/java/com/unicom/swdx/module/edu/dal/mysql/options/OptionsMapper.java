package com.unicom.swdx.module.edu.dal.mysql.options;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.options.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

/**
 * 选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OptionsMapper extends BaseMapperX<OptionsDO> {

    default PageResult<OptionsDO> selectPage(OptionsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OptionsDO>()
                .eqIfPresent(OptionsDO::getOptionsType, reqVO.getOptionsType())
                .eqIfPresent(OptionsDO::getContent, reqVO.getContent())
                .eqIfPresent(OptionsDO::getScore, reqVO.getScore())
                .eqIfPresent(OptionsDO::getCreateDept, reqVO.getCreateDept())
                .betweenIfPresent(OptionsDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(OptionsDO::getCreator, reqVO.getCreator())
                .eqIfPresent(OptionsDO::getUpdater, reqVO.getUpdater())
                .eqIfPresent(OptionsDO::getDeleted, reqVO.getDeleted())
                .eqIfPresent(OptionsDO::getQuestionId, reqVO.getQuestionId())
                .orderByDesc(OptionsDO::getId));
    }

    default List<OptionsDO> selectListByQuestionId(Long id) {
        return selectList(new LambdaQueryWrapperX<OptionsDO>()
                .eq(OptionsDO::getQuestionId, id));
    }

    default void deleteByQuestionId(Long id) {
        delete(new LambdaQueryWrapperX<OptionsDO>()
                .eq(OptionsDO::getQuestionId, id));
    }

    default List<OptionsDO> selectListsByQuestionIds(List<Long> choices) {
        if (choices.isEmpty()) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<OptionsDO>()
                .in(OptionsDO::getQuestionId, choices));
    }

    default void batchDeleteByQuestionId(List<Long> ids) {
        if (ids.isEmpty()){
            return;
        }
        delete(new LambdaQueryWrapperX<OptionsDO>()
                .in(OptionsDO::getQuestionId, ids));
    }

    List<Long> selectOptionId(@Param("questionId") Long questionId);
}