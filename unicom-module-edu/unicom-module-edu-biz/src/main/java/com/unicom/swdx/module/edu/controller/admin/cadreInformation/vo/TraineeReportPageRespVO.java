package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@ApiModel("管理后台 - EduClassManagement Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraineeReportPageRespVO extends ClassManagementBaseVO {

    @ApiModelProperty(value = "主键id,自增")
    private Integer id;

    @ApiModelProperty(value = "名额")
    private String capacity;
}
