package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免人事信息导入有问题

public class ClassInfoSourceImportExcelVO {

    @ExcelProperty("序号（黄色为必填项，灰色为非必填项）")
    private String iSort;

    @ExcelProperty("班次名称")
    @HeadStyle(fillForegroundColor = 13)
    private String className;

    @ExcelProperty("班次来源")
    @HeadStyle(fillForegroundColor = 13)
    private String classSource;

    @ExcelProperty("办班类型")
    @HeadStyle(fillForegroundColor = 13)
    private String classTypeDictId;

    @ExcelProperty("班次属性")
    private String classAttribute;

    @ExcelProperty("年度")
    @HeadStyle(fillForegroundColor = 13)
    private String year;

    @ExcelProperty("学期")
    @HeadStyle(fillForegroundColor = 13)
    private String semester;

    @ExcelProperty("学制")
    private String learningSystem;

    @ExcelProperty("学制单位")
    private String learningSystemUnit;

    @ExcelProperty("培训对象")
    private String trainingObject;

    @ExcelProperty("预计人数")
    private String peopleNumber;

    @ExcelProperty("轮次")
    private String turn;

    @ExcelProperty("校区")
    @HeadStyle(fillForegroundColor = 13)
    private String campus;

    @ExcelProperty(value = "报到日期")
    @HeadStyle(fillForegroundColor = 13)
    private String reportingTime;

    @ExcelProperty(value = "开班日期")
    @HeadStyle(fillForegroundColor = 13)
    private String classOpenTime;

    @ExcelProperty(value = "结业日期")
    @HeadStyle(fillForegroundColor = 13)
    private String completionTime;

    @ExcelProperty(value = "报名开始日期")
    @HeadStyle(fillForegroundColor = 13)
    private String registrationStartTime;

    @ExcelProperty(value = "报名结束日期")
    @HeadStyle(fillForegroundColor = 13)
    private String registrationEndTime;

    @ApiModelProperty(value = "缴费报道，1-是，2-否")
    @ExcelProperty("缴费报道")
    @HeadStyle(fillForegroundColor = 13)
    private String paymentReport;

    @ApiModelProperty(value = "考勤评课，1-是，2-否")
    @ExcelProperty("考勤评课")
    @HeadStyle(fillForegroundColor = 13)
    private String evaluate;

    @ExcelProperty("排序号")
    private String sort;

    @ExcelProperty("备注")
    private String remark;

}
