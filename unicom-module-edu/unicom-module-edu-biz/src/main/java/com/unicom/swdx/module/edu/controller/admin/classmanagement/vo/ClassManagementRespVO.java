package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

@ApiModel("管理后台 - EduClassManagement Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassManagementRespVO extends ClassManagementBaseVO {

    @ApiModelProperty(value = "主键id,自增", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "唯一编码")
    private String idCode;

    private String idCodeName;

    @ApiModelProperty(value = "办班类型名称")
    private String classTypeDictName;

    /**
     * 班次来源
     */
    @ApiModelProperty(value = "班次来源")
    private Integer classSource;
}
