package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 学员课程未到、请假详情 Resp VO
 * @date 2024-11-08
 */
@ApiModel("学员未到、请假详情 Resp VO")
@Data
public class AttendanceTraineeClassInfoRespVO {

    @ApiModelProperty(value = "学员ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "上课日期", example = "2024-11-08")
    private String classDate;

    @ApiModelProperty(value = "午别 0-上午 1-下午 2-晚上", example = "1")
    private Integer period;

    @ApiModelProperty(value = "上课开始时间", example = "09:00")
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课结束时间", example = "11:00")
    private String classEndTimeStr;

    @ApiModelProperty(value = "课程ID", example = "1")
    private Long courseId;

    @ApiModelProperty(hidden = true)
    private LocalDateTime classStartTime;

    @ApiModelProperty(hidden = true)
    private LocalDateTime classEndTime;

}
