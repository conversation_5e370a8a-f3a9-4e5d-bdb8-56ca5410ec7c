package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 选修课发布课程关联课程详细信息，包含选课人数 Resp VO
 */
@ApiModel("管理后台 - 选修课发布课程关联课程详细信息，包含选课人数 Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleaseCoursesRespVO extends ElectiveReleaseCoursesSubRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "发布ID", example = "1")
    private Long releaseId;

    @ApiModelProperty(value = "选课人数", example = "1")
    private Long selectedNum;
}
