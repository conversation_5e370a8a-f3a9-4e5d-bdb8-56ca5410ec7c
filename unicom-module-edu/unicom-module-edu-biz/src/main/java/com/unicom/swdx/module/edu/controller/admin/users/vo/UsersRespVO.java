package com.unicom.swdx.module.edu.controller.admin.users.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UsersRespVO {

    @Schema(description = "用户ID")
    @ExcelProperty("用户ID")
    private Long id;

    @Schema(description = "用户账号")
    @ExcelProperty("用户账号")
    private String username;

    @Schema(description = "密码")
    @ExcelProperty("密码")
    private String password;

    @Schema(description = "用户昵称")
    @ExcelProperty("用户昵称")
    private String nickname;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "部门ID", example = "9278")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "岗位编号数组")
    @ExcelProperty("岗位编号数组")
    private String postIds;

    @Schema(description = "用户邮箱")
    @ExcelProperty("用户邮箱")
    private String email;

    @Schema(description = "手机号码")
    @ExcelProperty("手机号码")
    private String mobile;

    @Schema(description = "用户性别")
    @ExcelProperty("用户性别")
    private Integer sex;

    @Schema(description = "头像地址")
    @ExcelProperty("头像地址")
    private String avatar;

    @Schema(description = "帐号状态（0正常 1停用）")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "最后登录IP")
    @ExcelProperty("最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    @ExcelProperty("最后登录时间")
    private LocalDateTime loginDate;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
