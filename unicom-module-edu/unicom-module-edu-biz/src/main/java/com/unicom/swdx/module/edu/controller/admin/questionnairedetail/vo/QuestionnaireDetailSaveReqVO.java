package com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 评估问卷与问题关联新增/修改 Request VO")
@Data
public class QuestionnaireDetailSaveReqVO {

    @Schema(description = "主键ID",  example = "31318")
    private Long id;

    @Schema(description = "问题在问卷中的序号",  example = "31318")
    private Long serialNumber;

    @Schema(description = "问题主键", example = "3908")
    private Long questionId;

    @Schema(description = "试卷主键", example = "31586")
    private Long questionnaireId;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "0不是1是一票否决")
    private Boolean oneBallotVeto;

    @Schema(description = "0不是1是必答题")
    private Boolean required;

    @Schema(description = "一票否决对应的选项id")
    private Long optionId;

}
