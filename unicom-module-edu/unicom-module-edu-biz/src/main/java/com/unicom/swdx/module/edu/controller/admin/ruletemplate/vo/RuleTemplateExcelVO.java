package com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 考勤规则模版 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class RuleTemplateExcelVO {

    @ExcelProperty("主键id，自增")
    private Integer id;

    @ExcelProperty("规则名称")
    private String ruleName;

    @ExcelProperty("0-到课考勤，1-就餐考勤，2-住宿考勤")
    private Integer ruleType;

    @ExcelProperty("考勤地点，预留")
    private Integer locations;

    @ExcelProperty("上课前打卡时间")
    private Integer beforeClassTime;

    @ExcelProperty("上课后打卡时间")
    private Integer afterClassTime;

    @ExcelProperty("早餐打卡开始时间")
    private LocalTime breakfastStartTime;

    @ExcelProperty("早餐打卡结束时间")
    private LocalTime breakfastEndTime;

    @ExcelProperty("午餐打卡开始时间")
    private LocalTime lunchStartTime;

    @ExcelProperty("午餐打卡结束时间")
    private LocalTime lunchEndTime;

    @ExcelProperty("晚餐打卡开始时间")
    private LocalTime dinnerStartTime;

    @ExcelProperty("晚餐打卡结束时间")
    private LocalTime dinnerEndTime;

    @ExcelProperty("住宿打卡开始时间")
    private LocalTime putUpStartTime;

    @ExcelProperty("住宿打卡结束时间")
    private LocalTime putUpEndTime;

    @ExcelProperty("状态，0-启用，1-禁用")
    private Integer status;

    @ExcelProperty("校区，字典值")
    private Integer campus;

    @ExcelProperty("默认规则，0-是，1-否")
    private Integer defaultRule;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
