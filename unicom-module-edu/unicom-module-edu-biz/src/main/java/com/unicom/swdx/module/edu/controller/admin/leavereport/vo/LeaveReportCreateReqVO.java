package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
@Schema(description = "管理后台 - 离校报备新增 Request VO")
@Data
public class LeaveReportCreateReqVO {
    @Schema(description = "主键ID",  example = "4884")
    private Long id;
    /**
     * 离校报备名称
     */
    @Schema(description = "离校报备名称",  example = "第一次离校申请")
    private String name;
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDateTime endTime;
    /**
     * 班级id
     */
    @Schema(description = "班级id",  example = "4884")
    private Long classId;
    /**
     * 填报状态 0已结束，1进行中
     */
    @Schema(description = "填报状态 0已结束，1进行中",  example = "1")
    private Integer status;
    /**
     * 教师id
     */
    @Schema(description = "教师id",  example = "4884")
    private Long teacherId;

}
