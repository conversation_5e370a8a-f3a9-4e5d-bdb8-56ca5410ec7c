package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 教师授课记录 Response VO")
@Data
public class TeachingRecordRespVO {

    @ApiModelProperty(value = "序号")
    private Long serialNumber;

    @ApiModelProperty(value = "排课id")
    private Long classCourseId;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "Java基础")
    private String courseName;

    @ApiModelProperty(value = "班次id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "班次名称", example = "2024春季班")
    private String className;

    @ApiModelProperty(value = "授课教师姓名", example = "张三")
    private String teacherName;

    @ApiModelProperty(value = "授课开始时间")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "授课结束时间")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "授课时间段", example = "2025-07-17 上午 10:22 ~ 11:23")
    private String classduration;

    @ApiModelProperty(value = "教学方式", example = "面授")
    private String teachmethod;

    @ApiModelProperty(value = "总记录数，用于分页", hidden = true)
    private Long total;
}
