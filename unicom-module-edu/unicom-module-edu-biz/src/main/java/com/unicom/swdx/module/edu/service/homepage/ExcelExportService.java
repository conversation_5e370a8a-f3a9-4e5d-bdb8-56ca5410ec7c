package com.unicom.swdx.module.edu.service.homepage;

import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExcelExportService {

    // 从数据库查询动态表头
    public static List<Map<String, Map<String, List<String>>>> getDynamicHeaders() {
        List<Map<String, Map<String, List<String>>>> headers;

        List<CompletionTemplateDO> list = new ArrayList<>();

        headers = convertToHeaders(list);

//        // 构建理论学习表头
//        Map<String, Map<String, List<String>>> theoryStudy = new HashMap<>();
//        theoryStudy.put("理论学习50分", new HashMap<String, List<String>>() {{
//            put("学习考勤20", Arrays.asList("考试成绩"));
//            put("学习评价", Arrays.asList("到课率", "学习笔记"));
//        }});
//
//        // 构建党性锻炼表头
//        Map<String, Map<String, List<String>>> partyStudy = new HashMap<>();
//        partyStudy.put("党性锻炼50分", new HashMap<String, List<String>>() {{
//            put("考勤纪律", Arrays.asList("考勤记录", "组织纪律"));
//            put("廉洁纪律", Arrays.asList("廉洁行为", "道德规范"));
//        }});
//
//        // 构建加分项表头
//        Map<String, Map<String, List<String>>> bonusItems = new HashMap<>();
//        bonusItems.put("加分项", new HashMap<String, List<String>>() {{
//            put("奖励记录", Arrays.asList("发表文章", "竞赛获奖"));
//        }});
//
//        // 构建另一个加分项表头
//        Map<String, Map<String, List<String>>> bonusItems1 = new HashMap<>();
//        bonusItems1.put("加分项1", new HashMap<String, List<String>>() {{
//            put("奖励记录1", Arrays.asList("发表文章1", "竞赛获奖1"));
//        }});
//
//        // 将所有动态表头加入列表
//        headers.add(theoryStudy);
//        headers.add(partyStudy);
//        headers.add(bonusItems);
//        headers.add(bonusItems1);

        return headers;
    }

    public static void main(String[] args) {
        getDynamicHeaders();
    }

    public static List<Map<String, Map<String, List<String>>>> convertToHeaders(List<CompletionTemplateDO> list) {
        // 使用 Stream API 进行分组和转换
        return list.stream()
                .collect(Collectors.groupingBy(CompletionTemplateDO::getModuleName))
                .entrySet().stream()
                .map(entry -> {
                    String moduleName = entry.getKey();
                    Map<String, List<String>> columnMap = new HashMap<>();
                    entry.getValue().forEach(trainee -> {
                        columnMap.computeIfAbsent(trainee.getColumnName(), k -> new ArrayList<>())
                                .add(trainee.getAssessmentName());
                    });
                    Map<String, Map<String, List<String>>> moduleMap = new HashMap<>();
                    moduleMap.put(moduleName, columnMap);
                    return moduleMap;
                })
                .collect(Collectors.toList());
    }


    // 从数据库查询学生信息
    public List<Map<String, Object>> getStudentData() {
        List<TraineeDO> list = new ArrayList<>();
        list.add(new TraineeDO());
        List<Map<String, Object>> students = new ArrayList<>();

        for (TraineeDO traineeDO : list) {
            Map<String, Object> student = new HashMap<>();
            student.put("name", traineeDO.getName());
            student.put("position", traineeDO.getPosition());
            student.put("committee", traineeDO.getPosition());
            students.add(student);
        }


//        for (int i = 1; i <= 5; i++) {
//            Map<String, Object> student = new HashMap<>();
//            student.put("studentName", "邓文娟" + i);
//            student.put("position", "研究生班干部");
//            student.put("committee", "-");
//            student.put("scores", Arrays.asList(1.0, 2.0)); // 对应动态表头的分数
//            students.add(student);
//        }
        return students;
    }

    // 导出 Excel
    public void exportExcel(HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("成绩单");

        // 构建表头
        List<String> fixedHeaders = Arrays.asList("学生姓名", "单位职务", "班内职务");
        List<Map<String, Map<String, List<String>>>> dynamicHeaders = getDynamicHeaders();
        List<String> lastHeaders = Arrays.asList("获奖情况", "量化得分");

        buildHeader(sheet, fixedHeaders, dynamicHeaders, lastHeaders);

        // 填充学生数据
        List<Map<String, Object>> students = getStudentData();
        fillStudentData(sheet, students, fixedHeaders.size());

        // 设置响应头并写入数据
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=成绩单.xlsx");
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // 构建表头
    // 构建表头逻辑
    private void buildHeader(Sheet sheet, List<String> fixedHeaders, List<Map<String, Map<String, List<String>>>> dynamicHeaders, List<String> lastHeaders) {
        Row row1 = sheet.createRow(0); // 第一行表头
        Row row2 = sheet.createRow(1); // 第二行表头
        Row row3 = sheet.createRow(2); // 第三行表头

        // 固定列
        int colIndex = 0;
        for (String header : fixedHeaders) {
            Cell cell = row1.createCell(colIndex);
            cell.setCellValue(header);
            sheet.addMergedRegion(new CellRangeAddress(0, 2, colIndex, colIndex)); // 固定列合并三行
            colIndex++;
        }

        // 动态列
        for (Map<String, Map<String, List<String>>> mainGroup : dynamicHeaders) {
            for (Map.Entry<String, Map<String, List<String>>> groupEntry : mainGroup.entrySet()) {
                String mainHeader = groupEntry.getKey(); // 主标题
                Map<String, List<String>> subGroup = groupEntry.getValue();

                int groupStart = colIndex; // 当前组的起始列

                for (Map.Entry<String, List<String>> subEntry : subGroup.entrySet()) {
                    String subHeader = subEntry.getKey(); // 次级标题
                    List<String> childrenHeaders = subEntry.getValue();

                    int subGroupStart = colIndex; // 次级标题的起始列

                    for (String childHeader : childrenHeaders) {
                        Cell childCell = row3.createCell(colIndex);
                        childCell.setCellValue(childHeader);
                        colIndex++;
                    }

                    // 合并次级标题单元格
                    if (childrenHeaders.size() > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(1, 1, subGroupStart, colIndex - 1));
                    }
                    Cell subCell = row2.createCell(subGroupStart);
                    subCell.setCellValue(subHeader);
                }

                // 合并主标题单元格
                if (colIndex - groupStart > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, groupStart, colIndex - 1));
                }
                Cell mainCell = row1.createCell(groupStart);
                mainCell.setCellValue(mainHeader);
            }
        }

        // 最后列
        for (String header : lastHeaders) {
            Cell cell = row1.createCell(colIndex);
            cell.setCellValue(header);
            sheet.addMergedRegion(new CellRangeAddress(0, 2, colIndex, colIndex)); // 合并三行
            colIndex++;
        }
    }


    // 填充学生数据
    private void fillStudentData(Sheet sheet, List<Map<String, Object>> students, int fixedColumnSize) {
        int rowNum = 3; // 从第四行开始填充数据
        for (Map<String, Object> student : students) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;

            // 填充固定列
            row.createCell(colNum++).setCellValue((String) student.get("name"));
            row.createCell(colNum++).setCellValue((String) student.get("unitPosition"));
            row.createCell(colNum++).setCellValue((String) student.get("classService"));

            // 填充动态列
//            List<Double> scores = (List<Double>) student.get("scores");
//            for (Double score : scores) {
//                row.createCell(colNum++).setCellValue(score);
//            }

            // 填充最后列
//            row.createCell(colNum++).setCellValue("无"); // 假设无获奖情况
//            row.createCell(colNum++).setCellValue(0);   // 假设量化得分为0
        }
    }
}


