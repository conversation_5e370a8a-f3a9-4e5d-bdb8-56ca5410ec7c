package com.unicom.swdx.module.edu.controller.admin.signupunit.excelimporthandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetSignTypeWriteHandler implements SheetWriteHandler {

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    public GetSignTypeWriteHandler(SignUpUnitMapper signUpUnitMapper){

        this.signUpUnitMapper = signUpUnitMapper;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Map<Integer, String[]> mapDropDown = new HashMap<>();
        // 获取字典表中的下拉label
        List<ClassInfoImportDictLableExcelVO> label = signUpUnitMapper.getUnitType();

        // 初始化 downArray，用于存储动态数据
        String[] downArray = new String[label.size()];
        for (int i = 0; i < label.size(); i++) {
            downArray[i] = label.get(i).getLabel();
        }
        mapDropDown.put(2, downArray); // 假设下拉在第二列 (列索引1)

        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper dvhelper = sheet.getDataValidationHelper();
        for (Map.Entry<Integer, String[]> entry : mapDropDown.entrySet()) {
            CellRangeAddressList addressList = new CellRangeAddressList(1, 10001, entry.getKey(), entry.getKey());
            if (entry.getValue().length > 0) {
                DataValidationConstraint constraint = dvhelper.createExplicitListConstraint(entry.getValue());
                DataValidation dataValidation = dvhelper.createValidation(constraint, addressList);
                dataValidation.setSuppressDropDownArrow(true);
                dataValidation.setShowErrorBox(true);
                dataValidation.createErrorBox("提示", "此值与单元格定义格式不一致");
                dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                sheet.addValidationData(dataValidation);
            }
        }

        // 添加注释到指定单元格 (例如，B2单元格)
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        CreationHelper factory = workbook.getCreationHelper();
        Drawing<?> drawing = sheet.createDrawingPatriarch();

        for (int i = 0; i < 1; i++) {
            Row row = sheet.getRow(i); // 第二行，索引为1
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.createCell(1); // 第二列，索引为1

            // 创建注释
            addComment(factory, cell, row, drawing, workbook);
        }

    }

    private static void addComment(CreationHelper factory, Cell cell, Row row, Drawing<?> drawing, Workbook workbook) {
        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(cell.getColumnIndex());
        anchor.setCol2(cell.getColumnIndex() + 3);
        anchor.setRow1(row.getRowNum());
        anchor.setRow2(row.getRowNum() + 3);

        Comment comment = drawing.createCellComment(anchor);
        comment.setString(factory.createRichTextString("填写说明:\n填写内容只能为下拉中数据，其他数据将导致导入失败"));
        cell.setCellComment(comment);

        // 设置单元格样式 (可选)
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
//        cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cell.setCellStyle(cellStyle);
    }

}
