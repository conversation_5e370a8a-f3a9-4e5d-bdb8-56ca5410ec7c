package com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 考勤规则模版 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RuleTemplateRespVO extends RuleTemplateBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
