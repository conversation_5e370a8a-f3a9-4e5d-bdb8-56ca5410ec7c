package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LeaveReportDetailRespVO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 离校报备id
     */
    private Long leaveId;
    /**
     * 是否离校
     */
    private Boolean leaveStatus;
    /**
     * 离校时间
     */
    private LocalDateTime leaveTime;
    /**
     * 返校时间
     */
    private LocalDateTime returnTime;
    /**
     * 学员id
     */
    private Long studentId;
    /**
     * 离校日是否校内用餐
     */
    private Boolean leaveDinner;
    /**
     * 返校日是否校内用餐
     */
    private Boolean returnDinner;
    /**
     * 目的地
     */
    private String destination;
    /**
     * 事由
     */
    private String cause;
}
