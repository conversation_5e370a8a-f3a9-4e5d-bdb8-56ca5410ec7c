package com.unicom.swdx.module.edu.dal.mysql.classcourseorder;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.ClassCourseTeacherExportReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.ClassCourseTeacherPageReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseorder.ClassCourseOrderDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 课程表-教师-授课关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCourseOrderMapper extends BaseMapperX<ClassCourseOrderDO> {


}
