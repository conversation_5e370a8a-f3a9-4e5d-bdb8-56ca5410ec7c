package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ApiModel(value = "调课页某周课表返回对象")
@Data
public class WeekTimetableRespVO {

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "当日课程-上午-下午-晚上")
    private Map<String, List<CourseInfo>> courses;


    @Data
    public static class CourseInfo {

        @ApiModelProperty(value = "该单元格是否发布，1-未发布，0-已发布，只展示已发布的课程，未发布的课程不展示")
        private Boolean isTemporary;

        @ApiModelProperty(value = "是否合班授课 true - 是，false - 否")
        private Boolean isMerge;

        @ApiModelProperty(value = "是否显示合班授课标签 true - 是，false - 否")
        private Boolean isShowMergeTag;

        @ApiModelProperty(value = "课表单元格id")
        private Long id;

        @ApiModelProperty(value = "课程id")
        private Long courseId;

        @ApiModelProperty(value = "课程名称")
        private String courseName;

        @ApiModelProperty(value = "课程类型")
        private String courseType;

        @ApiModelProperty(value = "教学形式字典id")
        private Long educateFormId;

        @ApiModelProperty(value = "活动类型字典id")
        private Long activityType;

        @ApiModelProperty(value = "上课时间段，格式HH:mm")
        private String time;

        @ApiModelProperty(value = "教师id")
        private Long teacherId;

        @ApiModelProperty(value = "教师")
        private String teacher;

        @ApiModelProperty(value = "教室id")
        private Long classroomId;

        @ApiModelProperty(value = "教室")
        private String classroom;

    }
}
