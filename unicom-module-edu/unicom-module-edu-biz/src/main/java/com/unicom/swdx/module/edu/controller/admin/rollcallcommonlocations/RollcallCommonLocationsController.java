package com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo.RollcallCommonLocationsRespVO;
import com.unicom.swdx.module.edu.service.rollcallcommonlocations.RollcallCommonLocationsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 大课考勤、点名签到常用地点")
@RestController
@RequestMapping("/edu/rollcall-common-locations")
@Validated
public class RollcallCommonLocationsController {

    @Resource
    private RollcallCommonLocationsService rollcallCommonLocationsService;

    @GetMapping("/listForTeacher")
    @ApiOperation("获得一个教师大课考勤或点名签到的常用地点")
    @ApiImplicitParam(name = "type", value = "0-大课考勤 1-点名签到", required = true, example = "0", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:rollcall-common-locations:query')")
    public CommonResult<List<RollcallCommonLocationsRespVO>> getListForTeacher(@RequestParam("type") Integer type) {
        List<RollcallCommonLocationsRespVO> list = rollcallCommonLocationsService.getListForTeacher(type);
        return success(list);
    }

    @GetMapping("/getLastRadius")
    @ApiOperation("获得一个教师最近一次选择的半径范围")
    @ApiImplicitParam(name = "type", value = "0-大课考勤 1-点名签到", required = true, example = "0", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:rollcall-common-locations:query')")
    public CommonResult<BigDecimal> getLastRadius(@RequestParam("type") Integer type) {
        BigDecimal lastRadius = rollcallCommonLocationsService.getLastRadius(type);
        return success(lastRadius);
    }


    @PostMapping("/delete")
    @ApiOperation("删除一个常用地点")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:rollcall-common-locations:delete')")
    public CommonResult<Boolean> deleteOne(@RequestParam("id") Long id) {
        rollcallCommonLocationsService.deleteOne(id);
        return success(true);
    }


}
