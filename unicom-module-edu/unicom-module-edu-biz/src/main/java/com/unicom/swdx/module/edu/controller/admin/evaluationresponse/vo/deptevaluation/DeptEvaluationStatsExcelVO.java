package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description: 部门评估统计-导出 Excel VO
 * @date 2024-11-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class DeptEvaluationStatsExcelVO {

    @ExcelProperty(value = "部门")
    private String deptName;

    @ExcelProperty(value = "授课次数")
    private Integer teachingCount;

    @ExcelProperty(value = "平均分")
    private String averageScoreStr;

    @ExcelProperty(value = "平均排名分")
    private String averageRankScoreStr;
}
