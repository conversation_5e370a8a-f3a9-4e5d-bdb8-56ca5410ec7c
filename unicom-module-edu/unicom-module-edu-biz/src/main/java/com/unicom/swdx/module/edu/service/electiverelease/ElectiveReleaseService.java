package com.unicom.swdx.module.edu.service.electiverelease;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.courseinfo.CourseInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesReqVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesRespVO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 选修课发布信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ElectiveReleaseService {

    /**
     * 创建选修课发布信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createElectiveRelease(@Valid ElectiveReleaseCreateReqVO createReqVO);

    /**
     * 删除选修课发布信息
     *
     * @param id 编号
     */
    void deleteElectiveRelease(Long id);

    /**
     * 获得选修课发布信息
     *
     * @param id 编号
     * @return 选修课发布信息
     */
    ElectiveReleaseGetRespVO getElectiveRelease(Long id);

    /**
     * 获得选修课发布信息分页
     *
     * @param pageReqVO 分页查询
     * @return 选修课发布信息分页
     */
    PageResult<ElectiveReleasePageRespVO> getElectiveReleasePage(ElectiveReleasePageReqVO pageReqVO);

    /**
     * 导出选修课发布信息 Excel
     *
     * @param reqVO 同分页查询参数
     * @return 选修课发布信息 Excel列表信息
     */
    List<ElectiveReleaseExcelVO> getExportElectiveReleaseExcel(ElectiveReleasePageReqVO reqVO);

    /**
     * 批量删除选修课发布信息（支持勾选和条件）
     *
     * @param reqVO 批量删除选修课发布信息
     */
    void batchDeleteElectiveRelease(ElectiveReleasePageReqVO reqVO);

    /**
     * 选修课发布已选人数具体信息分页列表
     *
     * @param pageVO 分页查询参数
     * @return 选修课发布已选人数具体信息分页列表
     */
    PageResult<ElectiveReleaseSelectedInfoRespVO> getSelectedInfoPage(ElectiveReleaseSelectedInfoPageReqVO pageVO);

    /**
     * 选修课发布已选人数具体信息列表 excel信息
     *
     * @param reqVO 分页查询参数
     * @return 选修课发布已选人数具体信息列表 excel信息
     */
    List<ElectiveReleaseSelectedInfoExcelVO> getExportSelectedInfoList(ElectiveReleaseSelectedInfoPageReqVO reqVO);

    /**
     * 查看选修课发布信息里面的选修课程信息分页
     *
     * @param reqVO 分页查询参数
     * @return 选修课发布信息里面的选修课程信息分页
     */
    PageResult<ElectiveReleaseCoursesRespVO> getReleaseCoursePage(ElectiveReleaseCoursesReqVO reqVO);

    /**
     * 导出选修课发布信息里面的选修课程信息分页
     *
     * @param reqVO    分页查询参数
     * @param response 请求响应
     */
    void exportReleaseCourseExcel(ElectiveReleaseCoursesReqVO reqVO, HttpServletResponse response) throws IOException;

    /**
     * 获得选修课发布信息列表（包含选课人数信息）
     *
     * @param classId 班级ID
     * @return 选修课发布信息列表（包含选课人数信息）
     */
    List<ElectiveReleasePageRespVO> getElectiveReleaseListByClassId(Long classId);

    /**
     * 根据学员ID获得选修课发布信息列表（每个选修课发布信息包含课程信息）
     *
     * @param status 0-待选的选修课发布 1-已选的选修课发布
     * @return 选修课发布信息列表
     */
    List<AppElectiveReleaseTraineeRespVO> getElectiveReleaseAndCoursesListByTraineeIdAndStatus(Integer status);

    /**
     * 获得选修课发布的班级下拉信息
     *
     * @param releaseId 发布ID
     * @return 班级下拉信息
     */
    List<ClassInfoVO> getSimpleClassesInfoList(Long releaseId);

    /**
     * 获得选修课发布的课程下拉信息
     *
     * @param releaseId 发布ID
     * @return 课程下拉信息
     */
    List<CourseInfoVO> getSimpleCoursesInfoList(Long releaseId);
}
