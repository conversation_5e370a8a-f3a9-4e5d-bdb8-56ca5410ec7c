package com.unicom.swdx.module.edu.controller.admin.classroomlibrary.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EduClassroomLibrary Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ClassroomLibraryParasVO {


    @ApiModelProperty(value = "勾选id，选中导出，不传导出所有")
    private String ids;

    @ApiModelProperty(value = "导出所选的列，不传导出所有列")
    private String selectedColumns;

    @ApiModelProperty(value = "教室名称")
    private String className;

    @ApiModelProperty(value = "所在建筑")
    private String buildingName;

    @ApiModelProperty(value = "0-升序，1-降序")
    private Integer change;

}
