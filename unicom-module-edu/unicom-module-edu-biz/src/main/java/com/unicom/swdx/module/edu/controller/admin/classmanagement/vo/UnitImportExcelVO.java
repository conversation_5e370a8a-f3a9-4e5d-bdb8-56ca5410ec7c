package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免人事信息导入有问题

public class UnitImportExcelVO {

    @ExcelProperty("单位名称")
    @HeadStyle(fillForegroundColor = 13)
    @ColumnWidth(22) // 设置列宽为22字符宽度
    private String unitName;

    @ExcelProperty("名额数")
    @HeadStyle(fillForegroundColor = 13)
    @ColumnWidth(22) // 设置列宽为22字符宽度
    private String number;

}
