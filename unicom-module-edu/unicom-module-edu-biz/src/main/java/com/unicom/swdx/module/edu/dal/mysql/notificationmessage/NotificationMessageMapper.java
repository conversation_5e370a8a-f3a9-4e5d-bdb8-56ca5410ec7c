package com.unicom.swdx.module.edu.dal.mysql.notificationmessage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementExportVO;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.NoticeAnnouncementPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessageExportVO;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessagePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessageRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageDO;
import com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageUnitDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface NotificationMessageMapper extends BaseMapperX<NotificationMessageDO> {

    void setNotificationMessageUnit(@Param("reqVO") NotificationMessageUnitDO notificationMessageUnitDO);

    void deleteNotificationMessageUnit(@Param("id") Integer id);

    void setUnitRead(@Param("id") Integer id,@Param("unit") Integer unit);

    void setUnitDisplay(@Param("ids") String ids,@Param("unit") Integer unit);

    List<Integer> getUnitsByNoticeId(@Param("id") Integer id);

    List<NotificationMessageDO> selectPageList(IPage<NotificationMessageDO> buildPage, @Param("pageReqVO") NotificationMessagePageReqVO pageReqVO);

    /**
     * 置顶 1- 置顶  2-非置顶
     * @param id
     */
    void isTopNotificationMessage(@Param("id") Integer id, @Param("isTop") Integer isTop, @Param("localDateTime") LocalDateTime localDateTime);

    /**
     * 非置顶
     * @param id
     */
    void notIsTopNotificationMessage(@Param("id") Integer id, @Param("isTop") Integer isTop);

    /**
     * 上下架 1-上架  2 -下架
     * @param id
     */
    void isUpOrDownNotificationMessage(@Param("id") Integer id, @Param("status") Integer status);

    void updatePublishById(@Param("id") Integer id, @Param("localDateTime") LocalDateTime localDateTime);

    List<NotificationMessageDO> selectListInfo(@Param("pageReqVO") NotificationMessageExportVO pageReqVO);

}
