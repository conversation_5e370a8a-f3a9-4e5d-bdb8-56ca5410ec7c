package com.unicom.swdx.module.edu.controller.admin.rollcallrecord;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.service.rollcallrecord.RollcallRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 学员点名签到记录")
@RestController
@RequestMapping("/edu/rollcall-record")
@Validated
public class RollcallRecordController {

    @Resource
    private RollcallRecordService rollcallRecordService;

    @GetMapping("/rollCallSignInTraineeInfoList")
    @ApiOperation("查看点名签到详情-未签到、已签到人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "点名签到ID", required = true, example = "1"),
            @ApiImplicitParam(name = "status", value = "筛选学员签到状态 0-未签到 1-已签到", required = true, example = "1")
    })
    @PreAuthorize("@ss.hasPermission('edu:rollcall-record:query')")
    public CommonResult<List<AppTraineeGroupRespVO>> getRollcallSignInTraineeInfo(@RequestParam("id") Long id,
                                                                                  @RequestParam("status") Integer status) {
        List<AppTraineeGroupRespVO> list = rollcallRecordService.getRollcallSignInTraineeInfo(id, status);
        return success(list);
    }

    @GetMapping("/lectureAttendanceTraineeInfoList")
    @ApiOperation("查看大课考勤-未签到、已签到人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "点名签到ID", required = true, example = "1"),
            @ApiImplicitParam(name = "status", value = "筛选学员签到状态 0-未签到 1-已签到", required = true, example = "1")
    })
    @PreAuthorize("@ss.hasPermission('edu:rollcall-record:query')")
    public CommonResult<List<AppTraineeGroupRespVO>> getLectureAttendanceTraineeInfo(@RequestParam("id") Long id,
                                                                                  @RequestParam("status") Integer status) {
        List<AppTraineeGroupRespVO> list = rollcallRecordService.getLectureAttendanceTraineeInfo(id, status);
        return success(list);
    }

}
