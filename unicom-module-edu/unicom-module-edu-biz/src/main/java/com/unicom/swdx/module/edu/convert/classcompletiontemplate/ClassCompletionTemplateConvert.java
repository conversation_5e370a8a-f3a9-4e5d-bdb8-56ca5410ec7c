package com.unicom.swdx.module.edu.convert.classcompletiontemplate;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;

/**
 * 结业考核模版设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCompletionTemplateConvert {

    ClassCompletionTemplateConvert INSTANCE = Mappers.getMapper(ClassCompletionTemplateConvert.class);

    ClassCompletionTemplateDO convert(ClassCompletionTemplateCreateReqVO bean);

    ClassCompletionTemplateDO convert(ClassCompletionTemplateUpdateReqVO bean);

    ClassCompletionTemplateRespVO convert(ClassCompletionTemplateDO bean);

    List<ClassCompletionTemplateRespVO> convertList(List<ClassCompletionTemplateDO> list);

    PageResult<ClassCompletionTemplateRespVO> convertPage(PageResult<ClassCompletionTemplateDO> page);

    List<ClassCompletionTemplateExcelVO> convertList02(List<ClassCompletionTemplateDO> list);

}
