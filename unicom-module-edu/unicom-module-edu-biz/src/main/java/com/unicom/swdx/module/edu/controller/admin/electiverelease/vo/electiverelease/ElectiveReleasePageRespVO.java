package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 选修课发布信息(包含选课信息) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleasePageRespVO extends ElectiveReleasePageSimpleRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "应选人数", example = "10")
    private Integer selectionNum;

    @ApiModelProperty(value = "已选人数", example = "5")
    private Integer selectedNum;
}
