package com.unicom.swdx.module.edu.utils.fuzzyquery;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName: EscapeSelectUtil
 * @Author: ZHK
 * @Date: 2024/7/17 10:35
 * @Description: TODO
 */
public class EscapeSelectUtil {
    /**
     * mysql的模糊查询时特殊字符转义(条件查询%或者_查询所有问题)
     */
    private EscapeSelectUtil() {
        throw new IllegalStateException("Utility class");
    }
    public static String escapeChar(String string){
        if(StringUtils.isNotBlank(string)){
            string = string.replace("_", "\\_");
            string = string.replace("%", "\\%");
        }
        return string.trim() ;
    }
}
