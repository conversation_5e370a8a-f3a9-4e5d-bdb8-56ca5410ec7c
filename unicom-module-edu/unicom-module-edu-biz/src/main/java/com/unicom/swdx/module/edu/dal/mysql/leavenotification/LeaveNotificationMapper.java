package com.unicom.swdx.module.edu.dal.mysql.leavenotification;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.leavenotification.vo.LeaveNotificationPageReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.leavenotification.LeaveNotificationDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalTime;

@Mapper
public interface LeaveNotificationMapper extends BaseMapperX<LeaveNotificationDO> {

        default PageResult<LeaveNotificationDO> selectPage(LeaveNotificationPageReqVO reqVO) {
                return selectPage(reqVO, new LambdaQueryWrapperX<LeaveNotificationDO>()
                                .likeIfPresent(LeaveNotificationDO::getName, reqVO.getName())
                                .eqIfPresent(LeaveNotificationDO::getClassId, reqVO.getClassId())
                                .likeIfPresent(LeaveNotificationDO::getClassName, reqVO.getClassName())
                                .eqIfPresent(LeaveNotificationDO::getHeadTeacherId, reqVO.getHeadTeacherId())
                                .likeIfPresent(LeaveNotificationDO::getHeadTeacherName, reqVO.getHeadTeacherName())
                                .eqIfPresent(LeaveNotificationDO::getStatus, reqVO.getStatus())
                                .betweenIfPresent(LeaveNotificationDO::getCreateTime, reqVO.getCreateTimeBegin(),
                                                reqVO.getCreateTimeEnd())
                                .apply(reqVO.getHolidayDate() != null,
                                                "start_time <= ? AND end_time >= ?",
                                                reqVO.getHolidayDate().atTime(LocalTime.MAX),
                                                reqVO.getHolidayDate().atTime(LocalTime.MIN))
                                .orderByDesc(LeaveNotificationDO::getCreateTime));
        }
}
