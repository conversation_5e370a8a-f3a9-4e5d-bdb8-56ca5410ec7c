package com.unicom.swdx.module.edu.controller.admin.todoitems.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 班主任待办事项 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TodoItemsRespVO extends TodoItemsBaseVO {

    @ApiModelProperty(value = "待办事项ID", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "学员请假id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long leaveId;
}
