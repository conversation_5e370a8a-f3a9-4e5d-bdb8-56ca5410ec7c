package com.unicom.swdx.module.edu.controller.admin.notificationmessage;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import com.unicom.swdx.module.edu.dal.mysql.notificationmessage.NotificationMessageMapper;
import com.unicom.swdx.module.edu.service.notificationmessage.NotificationMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "参训系统 - 站内信")
@RestController
@RequestMapping("/edu/notification-message")
@Validated
public class NotificationMessageController {

    @Resource
    private NotificationMessageService notificationMessageService;

    @Resource
    private NotificationMessageMapper notificationMessageMapper;

    @PostMapping("/create")
    @ApiOperation("新增")
    public CommonResult<Integer> createNotificationMessage(@Valid @RequestBody NotificationMessageCreateReqVO createReqVO) {
        return success(notificationMessageService.createNotificationMessage(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("修改")
    public CommonResult<Boolean> updateNotificationMessage(@Valid @RequestBody NotificationMessageUpdateReqVO updateReqVO) {
        notificationMessageService.updateNotificationMessage(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    public CommonResult<Boolean> deleteNotificationMessage(@RequestParam("id") Integer id) {
        notificationMessageService.deleteNotificationMessage(id);
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除")
    public CommonResult<Boolean> deleteNotificationMessageBatch(@Valid @RequestBody NotificationMessageDeleteVO noticeDeleteVO) {
        notificationMessageService.deleteNotificationMessageBatch(noticeDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("查看详细内容")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    public CommonResult<NotificationMessageRespVO> getNotificationMessage(@RequestParam("id") Integer id,
                                                                          @RequestParam(value = "unit",required = false)Integer unit) {
        NotificationMessageRespVO result = notificationMessageService.getNotificationMessage(id,unit);
        return success(result);
    }

    @GetMapping("/page")
    @ApiOperation("获得分页")
    public CommonResult<PageResult<NotificationMessageRespVO>> getNotificationMessagePage(@Valid NotificationMessagePageReqVO pageVO) {
        PageResult<NotificationMessageRespVO> pageResult = notificationMessageService.getNotificationMessagePage(pageVO);
        return success(pageResult);
    }

    @PostMapping("/display")
    @ApiOperation("是否展示")
    public CommonResult<Boolean> display(@RequestParam("ids") String ids, @RequestParam("unit") Integer unit) {
        notificationMessageMapper.setUnitDisplay(ids, unit);
        return success(true);
    }

    @PostMapping("/isTop")
    @ApiOperation("是否置顶")
    public CommonResult<Boolean> updateIsTop(@RequestParam("id") Integer id, @RequestParam("isTop") Integer isTop) {
        if(isTop == 1){
            notificationMessageMapper.isTopNotificationMessage(id, isTop, LocalDateTime.now());
        }else{
            notificationMessageMapper.notIsTopNotificationMessage(id, isTop);
        }

        return success(true);
    }

    @PostMapping("/status")
    @ApiOperation("上下架")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Integer id, @RequestParam("status") Integer status) {
        notificationMessageMapper.isUpOrDownNotificationMessage(id, status);
        return success(true);
    }

    @PostMapping("/publish-batch")
    @ApiOperation("批量发布")
    public CommonResult<Boolean> updatePublishBatch(@Valid @RequestBody NotificationMessageDeleteVO noticeDeleteVO) {
        notificationMessageService.publishBatch(noticeDeleteVO);
        return success(true);
    }

    @GetMapping("/export-excel")
    @ApiOperation("通知公告导出")
    public void exportNotificationMessageExcel(NotificationMessageExportVO exportReqVO,
                                              HttpServletResponse response) throws IOException {
        notificationMessageService.getNotificationMessageInfoList(exportReqVO,response);
    }

}
