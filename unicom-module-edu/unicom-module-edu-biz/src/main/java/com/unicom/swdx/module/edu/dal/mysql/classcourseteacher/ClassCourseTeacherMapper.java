package com.unicom.swdx.module.edu.dal.mysql.classcourseteacher;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.*;

/**
 * 课程表-教师-授课关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassCourseTeacherMapper extends BaseMapperX<ClassCourseTeacherDO> {

    default PageResult<ClassCourseTeacherDO> selectPage(ClassCourseTeacherPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ClassCourseTeacherDO>()
                .eqIfPresent(ClassCourseTeacherDO::getClassCourseId, reqVO.getClassCourseId())
                .eqIfPresent(ClassCourseTeacherDO::getTeacherId, reqVO.getTeacherId())
                .betweenIfPresent(ClassCourseTeacherDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ClassCourseTeacherDO::getId));
    }

    default List<ClassCourseTeacherDO> selectList(ClassCourseTeacherExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ClassCourseTeacherDO>()
                .eqIfPresent(ClassCourseTeacherDO::getClassCourseId, reqVO.getClassCourseId())
                .eqIfPresent(ClassCourseTeacherDO::getTeacherId, reqVO.getTeacherId())
                .betweenIfPresent(ClassCourseTeacherDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ClassCourseTeacherDO::getId));
    }

}
