package com.unicom.swdx.module.edu.service.notificationmessage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.*;
import com.unicom.swdx.module.edu.convert.noticeannouncement.NoticeAnnouncementConvert;
import com.unicom.swdx.module.edu.convert.notificationmessage.NotificationMessageConvert;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementDO;
import com.unicom.swdx.module.edu.dal.dataobject.noticeannouncement.NoticeAnnouncementUrlDO;
import com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageDO;
import com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageUnitDO;
import com.unicom.swdx.module.edu.dal.mysql.notificationmessage.NotificationMessageMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.NOTICE_ANNOUNCEMENT_NOT_EXISTS;

@Service
@Validated
public class NotificationMessageServiceImpl implements NotificationMessageService{

    @Resource
    private NotificationMessageMapper notificationMessageMapper;

    @Override
    public Integer createNotificationMessage(NotificationMessageCreateReqVO createReqVO) {

        //如果直接发版  状态上架
        if(createReqVO.getIsPublish() == 1){
            createReqVO.setStatus(1);
            createReqVO.setPublishTime(LocalDateTime.now());
        }else{
            //存草稿箱
            createReqVO.setDraftsTime(LocalDateTime.now());
        }

        //置顶 需要插入置顶时间
        if(createReqVO.getIsTop() == 1){
            createReqVO.setTopTime(LocalDateTime.now());
        }

        // 插入
        NotificationMessageDO notificationMessage = NotificationMessageConvert.INSTANCE.convert(createReqVO);
        notificationMessageMapper.insert(notificationMessage);

        //插入查看单位
        if(!createReqVO.getUnits().isEmpty()){
            for(Integer unit : createReqVO.getUnits()){
                NotificationMessageUnitDO notificationMessageUnitDO = new NotificationMessageUnitDO();
                notificationMessageUnitDO.setUnitId(unit);
                notificationMessageUnitDO.setNoticeId(notificationMessage.getId());
                notificationMessageUnitDO.setIsRead(1);
                notificationMessageUnitDO.setDisplayStatus(1);
                notificationMessageMapper.setNotificationMessageUnit(notificationMessageUnitDO);
            }
        }

        // 返回
        return notificationMessage.getId();
    }

    @Override
    public void updateNotificationMessage(NotificationMessageUpdateReqVO updateReqVO) {

        // 校验存在
        this.validateNotificationMessageExists(updateReqVO.getId());

        //如果直接发版  状态上架
        if(updateReqVO.getIsPublish() == 1){
            updateReqVO.setStatus(1);
            updateReqVO.setPublishTime(LocalDateTime.now());
        }else{
            //存草稿箱
            updateReqVO.setDraftsTime(LocalDateTime.now());
        }

        //置顶 需要插入置顶时间
        if(updateReqVO.getIsTop() == 1){
            updateReqVO.setTopTime(LocalDateTime.now());
        }

        //更新查看单位
        if(!updateReqVO.getUnits().isEmpty()){

            // 先删除
            notificationMessageMapper.deleteNotificationMessageUnit(updateReqVO.getId());

            for(Integer unit : updateReqVO.getUnits()){
                NotificationMessageUnitDO notificationMessageUnitDO = new NotificationMessageUnitDO();
                notificationMessageUnitDO.setUnitId(unit);
                notificationMessageUnitDO.setNoticeId(updateReqVO.getId());
                notificationMessageUnitDO.setIsRead(1);
                notificationMessageUnitDO.setDisplayStatus(1);
                notificationMessageMapper.setNotificationMessageUnit(notificationMessageUnitDO);
            }
        }


        // 更新
        NotificationMessageDO updateObj = NotificationMessageConvert.INSTANCE.convert(updateReqVO);
        notificationMessageMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotificationMessage(Integer id) {
        // 校验存在
        this.validateNotificationMessageExists(id);
        // 先删除 url 表中的id
        notificationMessageMapper.deleteNotificationMessageUnit(id);
        // 删除
        notificationMessageMapper.deleteById(id);
    }

    @Override
    public void deleteNotificationMessageBatch(NotificationMessageDeleteVO noticeDeleteVO) {
        String ids = noticeDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");

        for (String idStr : idArray){

            //转化成 int 类型
            int id = Integer.parseInt(idStr);
            // 校验存在
            this.validateNotificationMessageExists(id);
            // 先删除 url 表中的id
            notificationMessageMapper.deleteNotificationMessageUnit(id);
            // 删除
            notificationMessageMapper.deleteById(id);
        }
    }

    @Override
    public NotificationMessageRespVO getNotificationMessage(Integer id, Integer unit) {
        this.validateNotificationMessageExists(id);

        if(unit != null){
            notificationMessageMapper.setUnitRead(id,unit);
            notificationMessageMapper.setUnitDisplay(id.toString(),unit);
        }

        NotificationMessageDO notificationMessageDO = notificationMessageMapper.selectById(id);
        NotificationMessageRespVO respVO = NotificationMessageConvert.INSTANCE.convert(notificationMessageDO);
        respVO.setUnits(notificationMessageMapper.getUnitsByNoticeId(id));

        return respVO;
    }

    @Override
    public PageResult<NotificationMessageRespVO> getNotificationMessagePage(NotificationMessagePageReqVO pageReqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getTitle())){
            pageReqVO.setTitle(pageReqVO.getTitle().replaceAll("([%_])", "\\\\$1"));
        }
        if (StringUtils.isNotBlank(pageReqVO.getPublisher())){
            pageReqVO.setPublisher(pageReqVO.getPublisher().replaceAll("([%_])", "\\\\$1"));
        }

        Page buildPage = MyBatisUtils.buildPage(pageReqVO);

        List<NotificationMessageDO> notificationMessageDOS = notificationMessageMapper.selectPageList(buildPage, pageReqVO);

        PageResult<NotificationMessageDO> pageList = new PageResult<>(notificationMessageDOS, buildPage.getTotal());

        return NotificationMessageConvert.INSTANCE.convertPage(pageList);
    }

    @Override
    public void publishBatch(NotificationMessageDeleteVO noticeDeleteVO) {
        String ids = noticeDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");

        for (String idStr : idArray) {

            //转化成 int 类型
            int id = Integer.parseInt(idStr);
            // 校验存在
            this.validateNotificationMessageExists(id);
            // 发布
            notificationMessageMapper.updatePublishById(id,LocalDateTime.now());

        }
    }

    @Override
    public void getNotificationMessageInfoList(NotificationMessageExportVO reqVO, HttpServletResponse response) throws IOException {


        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(reqVO.getTitle())){
            reqVO.setTitle(reqVO.getTitle().replaceAll("([%_])", "\\\\$1"));
        }
        if (StringUtils.isNotBlank(reqVO.getPublisher())){
            reqVO.setPublisher(reqVO.getPublisher().replaceAll("([%_])", "\\\\$1"));
        }

        List<NotificationMessageDO> notificationMessageDOS = notificationMessageMapper.selectListInfo(reqVO);


        List<NotificationMessageExportReqVO> excelVOList = new ArrayList<>();
        for(NotificationMessageDO dataList : notificationMessageDOS){
            NotificationMessageExportReqVO list = new NotificationMessageExportReqVO();
            if(dataList.getTitle() != null){
                list.setTitle(dataList.getTitle());
            }
            if(dataList.getStatus() != null){
                if(dataList.getStatus() == 1){
                    list.setStatus("上架");
                }else{
                    list.setStatus("下架");
                }
            }
            if(dataList.getPublishTime() != null && !dataList.getPublishTime().equals("")){
                list.setPublishTime(dataList.getPublishTime());
            }
            if(dataList.getPublisher() != null){
                list.setPublisher(dataList.getPublisher());
            }
            if (dataList.getDraftsTime() != null && !dataList.getDraftsTime().equals("")) {
                list.setDraftsTime(dataList.getDraftsTime());
            }
            excelVOList.add(list);
        }
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "站内信.xls",
                    "数据", NotificationMessageExportReqVO.class, excelVOList, reqVO.getIncludeColumnIndexes());

    }

    private void validateNotificationMessageExists(Integer id) {
        if (notificationMessageMapper.selectById(id) == null) {
            throw exception(NOTICE_ANNOUNCEMENT_NOT_EXISTS);
        }
    }
}
