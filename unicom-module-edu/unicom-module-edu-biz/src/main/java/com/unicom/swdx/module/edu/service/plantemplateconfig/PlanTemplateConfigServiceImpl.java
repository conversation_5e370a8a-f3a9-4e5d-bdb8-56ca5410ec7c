package com.unicom.swdx.module.edu.service.plantemplateconfig;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.plantemplateconfig.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plantemplateconfig.PlanTemplateConfigDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.plantemplateconfig.PlanTemplateConfigConvert;
import com.unicom.swdx.module.edu.dal.mysql.plantemplateconfig.PlanTemplateConfigMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 教学计划模版配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlanTemplateConfigServiceImpl implements PlanTemplateConfigService {

    @Resource
    private PlanTemplateConfigMapper planTemplateConfigMapper;

    @Override
    public Long createPlanTemplateConfig(PlanTemplateConfigCreateReqVO createReqVO) {
        // 插入
        PlanTemplateConfigDO planTemplateConfig = PlanTemplateConfigConvert.INSTANCE.convert(createReqVO);
        planTemplateConfigMapper.insert(planTemplateConfig);
        // 返回
        return planTemplateConfig.getId();
    }

    @Override
    public void updatePlanTemplateConfig(PlanTemplateConfigUpdateReqVO updateReqVO) {
        // 校验存在
        this.validatePlanTemplateConfigExists(updateReqVO.getId());
        // 更新
        PlanTemplateConfigDO updateObj = PlanTemplateConfigConvert.INSTANCE.convert(updateReqVO);
        planTemplateConfigMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanTemplateConfig(Long id) {
        // 校验存在
        this.validatePlanTemplateConfigExists(id);
        // 删除
        planTemplateConfigMapper.deleteById(id);
    }

    private void validatePlanTemplateConfigExists(Long id) {
        if (planTemplateConfigMapper.selectById(id) == null) {
            throw exception(PLAN_TEMPLATE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public PlanTemplateConfigDO getPlanTemplateConfig(Long id) {
        return planTemplateConfigMapper.selectById(id);
    }

    @Override
    public List<PlanTemplateConfigDO> getPlanTemplateConfigList(Collection<Long> ids) {
        return planTemplateConfigMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PlanTemplateConfigDO> getPlanTemplateConfigPage(PlanTemplateConfigPageReqVO pageReqVO) {
        return planTemplateConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PlanTemplateConfigDO> getPlanTemplateConfigList(PlanTemplateConfigExportReqVO exportReqVO) {
        return planTemplateConfigMapper.selectList(exportReqVO);
    }

}
