package com.unicom.swdx.module.edu.service.xcxMsg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigRespVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigUpdateReqVO;
import com.unicom.swdx.module.edu.convert.xcxmsg.XcxMsgConvert;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;
import com.unicom.swdx.module.edu.dal.mysql.xcxmsg.XcxMsgConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class XcxMsgConfigServiceImpl extends ServiceImpl<XcxMsgConfigMapper, XcxMsgConfigDO> implements XcxMsgConfigService{

    @Resource
    private XcxMsgConfigMapper configMapper;

    @Override
    public PageResult<XcxMsgConfigRespVO> pageList(XcxMsgConfigPageReqVO reqVO) {
        IPage<XcxMsgConfigRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<XcxMsgConfigRespVO> list = configMapper.selectPageByReqVO(page, reqVO);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public boolean updateConfig(XcxMsgConfigUpdateReqVO reqVO) {
        XcxMsgConfigDO toUpdate = XcxMsgConvert.INSTANCE.convert(reqVO);
        return this.updateById(toUpdate);
    }

}
