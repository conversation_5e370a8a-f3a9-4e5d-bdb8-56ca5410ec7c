package com.unicom.swdx.module.edu.controller.admin.classcommittee.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * @ClassName: TraineeDO
 * @Author: lty
 * @Date: 2024/10/9 11:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCommitteeRespVO extends BaseDO {

    /**
     * 主键
     */
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    private String classCommitteeName;
    private Boolean nameShow;
    private Integer tenantId;

    private Integer index;
}
