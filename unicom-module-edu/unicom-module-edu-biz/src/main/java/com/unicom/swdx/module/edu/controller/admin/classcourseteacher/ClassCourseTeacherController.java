package com.unicom.swdx.module.edu.controller.admin.classcourseteacher;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.classcourseteacher.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.module.edu.convert.classcourseteacher.ClassCourseTeacherConvert;
import com.unicom.swdx.module.edu.service.classcourseteacher.ClassCourseTeacherService;

@Api(tags = "管理后台 - 课程表-教师-授课关系")
@RestController
@RequestMapping("/edu/class-course-teacher")
@Validated
public class ClassCourseTeacherController {

    @Resource
    private ClassCourseTeacherService classCourseTeacherService;

    @PostMapping("/create")
    @ApiOperation("创建课程表-教师-授课关系")
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:create')")
    public CommonResult<Long> createClassCourseTeacher(@Valid @RequestBody ClassCourseTeacherCreateReqVO createReqVO) {
        return success(classCourseTeacherService.createClassCourseTeacher(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新课程表-教师-授课关系")
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:update')")
    public CommonResult<Boolean> updateClassCourseTeacher(@Valid @RequestBody ClassCourseTeacherUpdateReqVO updateReqVO) {
        classCourseTeacherService.updateClassCourseTeacher(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除课程表-教师-授课关系")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:delete')")
    public CommonResult<Boolean> deleteClassCourseTeacher(@RequestParam("id") Long id) {
        classCourseTeacherService.deleteClassCourseTeacher(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得课程表-教师-授课关系")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:query')")
    public CommonResult<ClassCourseTeacherRespVO> getClassCourseTeacher(@RequestParam("id") Long id) {
        ClassCourseTeacherDO classCourseTeacher = classCourseTeacherService.getClassCourseTeacher(id);
        return success(ClassCourseTeacherConvert.INSTANCE.convert(classCourseTeacher));
    }

    @GetMapping("/list")
    @ApiOperation("获得课程表-教师-授课关系列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:query')")
    public CommonResult<List<ClassCourseTeacherRespVO>> getClassCourseTeacherList(@RequestParam("ids") Collection<Long> ids) {
        List<ClassCourseTeacherDO> list = classCourseTeacherService.getClassCourseTeacherList(ids);
        return success(ClassCourseTeacherConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得课程表-教师-授课关系分页")
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:query')")
    public CommonResult<PageResult<ClassCourseTeacherRespVO>> getClassCourseTeacherPage(@Valid ClassCourseTeacherPageReqVO pageVO) {
        PageResult<ClassCourseTeacherDO> pageResult = classCourseTeacherService.getClassCourseTeacherPage(pageVO);
        return success(ClassCourseTeacherConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出课程表-教师-授课关系 Excel")
    @PreAuthorize("@ss.hasPermission('edu:class-course-teacher:export')")
    @OperateLog(type = EXPORT)
    public void exportClassCourseTeacherExcel(@Valid ClassCourseTeacherExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ClassCourseTeacherDO> list = classCourseTeacherService.getClassCourseTeacherList(exportReqVO);
        // 导出 Excel
        List<ClassCourseTeacherExcelVO> datas = ClassCourseTeacherConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "课程表-教师-授课关系.xls", "数据", ClassCourseTeacherExcelVO.class, datas);
    }

}
