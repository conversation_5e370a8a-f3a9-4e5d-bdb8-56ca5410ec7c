package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("管理后台 - 历史评价数据分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HistoryDataPageReqVO extends PageParam {

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "教学形式")
    private String teachingForm;

    @ApiModelProperty(value = "授课开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "授课结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "部门名称")
    private String orgName;

    @ApiModelProperty(value = "教师姓名")
    private String teacherName;

    @ApiModelProperty(value = "师资来源")
    private String teacherSource;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "ID集合", hidden = true)
    private List<String> ids; // 已修改为String类型，与数据库中的varchar(64)类型匹配

    @ApiModelProperty(value = "列包含集合")
    private List<Integer> includeColumnIndexes;
}
