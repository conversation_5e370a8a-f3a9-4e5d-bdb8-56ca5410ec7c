package com.unicom.swdx.module.edu.convert.notificationmessage;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessageCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessageRespVO;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessageUpdateReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.notificationmessage.NotificationMessageDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface NotificationMessageConvert {

    NotificationMessageConvert INSTANCE = Mappers.getMapper(NotificationMessageConvert.class);

    NotificationMessageDO convert(NotificationMessageCreateReqVO bean);

    NotificationMessageDO convert(NotificationMessageUpdateReqVO bean);

    NotificationMessageRespVO convert(NotificationMessageDO bean);

    List<NotificationMessageRespVO> convertList(List<NotificationMessageDO> list);

    PageResult<NotificationMessageRespVO> convertPage(PageResult<NotificationMessageDO> page);
}
