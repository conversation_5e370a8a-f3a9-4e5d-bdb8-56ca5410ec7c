package com.unicom.swdx.module.edu.dal.mysql.traineegroup;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.traineegroup.TraineeGroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TraineeGroupMapper extends BaseMapperX<TraineeGroupDO> {


    default Integer getMaxSort(Long classId) {
        LambdaQueryWrapper<TraineeGroupDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeGroupDO::getClassId, classId);
        List<TraineeGroupDO> list = this.selectList(wrapper);

        if (!list.isEmpty()){
            return list.stream().map(TraineeGroupDO::getSort).max(Integer::compareTo).get();
        }else {
            return 0;
        }

    }

    default TraineeGroupDO getGroupBySort(Integer sort, Long classId) {
        LambdaQueryWrapper<TraineeGroupDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeGroupDO::getSort, sort);
        wrapper.eq(TraineeGroupDO::getClassId, classId);

        return this.selectOne(wrapper);
    }

    Page<TraineeGroupRespVO> selectGroupPage(@Param("objectPage") Page<Object> objectPage, @Param("reqVO") TraineeGroupReqVO reqVO);

    List<TraineeGroupRespVO> selectGroupList(@Param("reqVO") TraineeGroupReqVO reqVO);
}
