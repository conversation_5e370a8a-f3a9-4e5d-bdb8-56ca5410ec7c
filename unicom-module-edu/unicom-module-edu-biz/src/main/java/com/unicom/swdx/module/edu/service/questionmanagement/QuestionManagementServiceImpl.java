package com.unicom.swdx.module.edu.service.questionmanagement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesRespVO;
import com.unicom.swdx.module.edu.controller.admin.options.vo.OptionsSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO;
import com.unicom.swdx.module.edu.convert.options.OptionsConvert;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import com.unicom.swdx.module.edu.dal.mysql.options.OptionsMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.questionmanagement.QuestionManagementMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * 题目管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionManagementServiceImpl implements QuestionManagementService {

    @Resource
    private QuestionManagementMapper questionManagementMapper;

    @Resource
    private OptionsMapper optionsMapper;

    @Override
    public Long createQuestionManagement(QuestionManagementSaveReqVO createReqVO) {
        // 插入
        QuestionManagementDO questionManagement = BeanUtils.toBean(createReqVO, QuestionManagementDO.class);
        questionManagementMapper.insert(questionManagement);
        // 单选题增加选项
        if (questionManagement.getQuestionType().equals("2")) {
            List<OptionsDO> options = OptionsConvert.Instance.convertList(createReqVO.getOptions());
            options.forEach(option -> option.setQuestionId(questionManagement.getId()));
            options.forEach(option -> option.setOptionsType("0"));
            optionsMapper.insertBatch(options);
        }
        // 返回
        return questionManagement.getId();
    }

    @Override
    public void updateQuestionManagement(QuestionManagementSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionManagementExists(updateReqVO.getId());
        // 如果是选择题，还要更新选项
        if (updateReqVO.getQuestionType().equals("2")) {
            List<OptionsDO> existOptions = optionsMapper.selectListByQuestionId(updateReqVO.getId());
            List<Long> existOptionIds = existOptions.stream().map(OptionsDO::getId).collect(Collectors.toList());
            List<OptionsDO> updateOptions = OptionsConvert.Instance.convertList(updateReqVO.getOptions());
            List<Long> updateOptionIds = updateOptions.stream().map(OptionsDO::getId).collect(Collectors.toList());

            // 获取要删除的选项ids
            List<Long> toBeDeletedIds = existOptionIds.stream()
                    .filter(id -> !updateOptionIds.contains(id))
                    .collect(Collectors.toList());
            if (!toBeDeletedIds.isEmpty()) {
                optionsMapper.deleteBatchIds(toBeDeletedIds);
            }
            // 获取要新增的选项
            List<OptionsDO> toBeInserted = updateOptions.stream()
                    .filter(optionsDO -> optionsDO.getId() == null)
                    .collect(Collectors.toList());
            toBeInserted.forEach(option -> option.setQuestionId(updateReqVO.getId()));
            toBeInserted.forEach(option -> option.setOptionsType("0"));
            optionsMapper.insertBatch(toBeInserted);
        }
        // 更新
        QuestionManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionManagementDO.class);
        questionManagementMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionManagement(Long id) {
        // 校验存在
        String questionType =  validateQuestionManagementExists(id);
        // 删除
        questionManagementMapper.deleteById(id);

        if (questionType.equals("2")) {
            optionsMapper.deleteByQuestionId(id);
        }
    }

    private String validateQuestionManagementExists(Long id) {
        if (questionManagementMapper.selectById(id) == null) {
            throw exception(QUESTION_MANAGEMENT_NOT_EXISTS);
        } else {
            return questionManagementMapper.selectById(id).getQuestionType();
        }
    }

    @Override
    public QuestionManagementDO getQuestionManagement(Long id) {
        QuestionManagementDO questionManagementDO = questionManagementMapper.selectById(id);

        if (questionManagementDO.getQuestionType().equals("2")) {
            List<OptionsDO> options = optionsMapper.selectListByQuestionId(id);

            questionManagementDO.setOptions(options);
        }
        return questionManagementDO;
    }

    @Override
    public PageResult<QuestionManagementRespVO> getQuestionManagementPage(QuestionManagementPageReqVO pageReqVO) {
        IPage<QuestionManagementRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        List<QuestionManagementRespVO> pageResult = questionManagementMapper.selectPageByPageVO(page, pageReqVO);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(null ,
                page.getTotal(),
                pageReqVO,
                pageResult.size());
        for (int i = 0; i < pageResult.size(); i++) {
            pageResult.get(i).setSerialNumber(serialNumberList.get(i));
        }
        if(pageReqVO.getIsSerialDesc() == null || pageReqVO.getIsSerialDesc() == false) {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionManagementRespVO::getSerialNumber)
            ).collect(Collectors.toList());
        } else {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionManagementRespVO::getSerialNumber).reversed()
            ).collect(Collectors.toList());
        }
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public void batchDeleteQuestionManagement(List<Long> ids) {
        // 批量删除
        questionManagementMapper.deleteBatchIds(ids);
        optionsMapper.batchDeleteByQuestionId(ids);
    }
}