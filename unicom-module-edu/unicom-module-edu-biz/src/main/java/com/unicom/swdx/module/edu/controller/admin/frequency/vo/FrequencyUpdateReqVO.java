package com.unicom.swdx.module.edu.controller.admin.frequency.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("管理后台 - 使用次数更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FrequencyUpdateReqVO extends FrequencyBaseVO {

    @ApiModelProperty(value = "序号", required = true)
    @NotNull(message = "序号不能为空")
    private Long id;

}
