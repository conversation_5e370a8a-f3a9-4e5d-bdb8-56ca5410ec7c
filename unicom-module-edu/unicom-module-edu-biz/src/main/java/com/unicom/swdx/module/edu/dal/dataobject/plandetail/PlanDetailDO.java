package com.unicom.swdx.module.edu.dal.dataobject.plandetail;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 教学计划详情 DO
 *
 * <AUTHOR>
 */
@TableName("edu_plan_detail")
@KeySequence("edu_plan_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanDetailDO extends TenantBaseDO {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 教学计划id
     */
    private Long planId;
    /**
     * 日期
     */
    private String date;
    /**
     * 时间段（0上午，1下午，2晚上）
     */
    private String period;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

}
