package com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * @ClassName: TraineeLeavePageReqVO
 * @Author: youxiaoyan
 * @Date: 2024/11/11 15:22
 */
@Data
@ApiModel(value = "请假管理分页返回VO")
public class XcxMsgConfigPageReqVO extends PageParam {

    @ApiModelProperty(value = "隶属模块：任课教师授课提醒-1，住宿打卡提醒-2，教学评估提醒-3")
    private String tag;

    @ApiModelProperty(value = "标题")
    private String title;

}
