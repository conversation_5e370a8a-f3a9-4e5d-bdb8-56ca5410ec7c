package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 评估详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EvaluationDetailRespVO {

    @Schema(description = "主键", example = "18792")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "问卷id", example = "9284")
    @ExcelProperty("问卷id")
    private Long questionnaireId;

    @Schema(description = "问题id", example = "13794")
    @ExcelProperty("问题id")
    private Long questionId;

    @Schema(description = "问题类型1打分2单选3简答", example = "2")
    @ExcelProperty("问题类型1打分2单选3简答")
    private String questionType;

    @Schema(description = "评卷人id", example = "14762")
    @ExcelProperty("评卷人id")
    private Long studentId;

    @Schema(description = "打分题得分")
    @ExcelProperty("打分题得分")
    private Long score;

    @Schema(description = "选择题选项", example = "21307")
    @ExcelProperty("选择题选项")
    private Long optionId;

    @Schema(description = "简单题内容")
    @ExcelProperty("简单题内容")
    private String content;

    @Schema(description = "排课id", example = "24082")
    @ExcelProperty("排课id")
    private Long classCourseId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}