package com.unicom.swdx.module.edu.controller.admin.planconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.math.BigDecimal;
import io.swagger.annotations.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 教学计划配置 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PlanConfigExcelVO {

    @ExcelProperty("唯一标识符，自增")
    private Long id;

    @ExcelProperty("教学计划ID")
    private Long planId;

    @ExcelProperty("星期几（如：1代表周一等）")
    private String dayOfWeek;

    @ExcelProperty("时间段（0上午，1下午，2晚上）")
    private String period;

    @ExcelProperty("开始时间")
    private LocalDateTime beginTime;

    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
