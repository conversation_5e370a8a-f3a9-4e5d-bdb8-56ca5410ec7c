package com.unicom.swdx.module.edu.utils.upload;



import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.unicom.swdx.module.infra.api.file.FileApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: FileApi文件上传工具类
 */
@Component
@Slf4j
public class UploadFileUtil {
    private static FileApi fileApi;
    @Autowired
    public void init(FileApi fileApi) {
        UploadFileUtil.fileApi = fileApi;
    }

    /**
     * 上传excel至服务器
     *
     * @param data     excel数据对象列表
     * @param fileName 文件名
     * @param sheetName sheet名
     * @return url链接
     */
    public static String UploadExcel(List<?> data, String fileName, String sheetName) {
        if (data.isEmpty()) {
            return null;
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream, data.get(0).getClass()).build();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        excelWriter.write(data, writeSheet);
        excelWriter.finish();
        String url = null;
        try {
            // 测试服务器上传Excel到云
            url = fileApi.createFile(fileName, null, byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            log.error(e.toString());
        }
        if (StringUtils.isNotBlank(url)) {
            log.info("文件上传成功");
        } else {
            log.info("文件上传失败！");
        }
        return url;
    }

}
