package com.unicom.swdx.module.edu.dal.dataobject.plantemplate;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 教学计划模版 DO
 *
 * <AUTHOR>
 */
@TableName("edu_plan_template")
@KeySequence("edu_plan_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanTemplateDO extends BaseDO {

    /**
     * 唯一标识符，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 模版名称
     */
    private String name;

}
