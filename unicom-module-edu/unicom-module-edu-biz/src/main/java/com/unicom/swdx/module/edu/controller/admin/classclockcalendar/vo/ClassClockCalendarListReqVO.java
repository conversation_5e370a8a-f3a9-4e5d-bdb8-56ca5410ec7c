package com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo;

import lombok.*;

import java.time.LocalDate;

import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("管理后台 - 班级考勤日历分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassClockCalendarListReqVO extends PageParam {

    @ApiModelProperty(value = "考勤日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate clockDate;

    @ApiModelProperty(value = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @ApiModelProperty(value = "截止日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @ApiModelProperty(value = "班次id")
    private Long classId;

}
