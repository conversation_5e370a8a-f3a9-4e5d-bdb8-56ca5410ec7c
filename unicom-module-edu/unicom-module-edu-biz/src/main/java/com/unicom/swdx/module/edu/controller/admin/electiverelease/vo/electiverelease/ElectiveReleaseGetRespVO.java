package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel("管理后台 - 选修课发布信息单个回显 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElectiveReleaseGetRespVO extends ElectiveReleaseBaseVO {

    @ApiModelProperty(value = "选修课发布信息主键", example = "1024")
    private Long id;

    @ApiModelProperty(value = "回显班级信息")
    private List<ClassInfoVO> classInfoList;

    @ApiModelProperty(value = "选修课信息列表，包含课程名称、教师名称、教室ID、名称", required = true)
    @NotEmpty(message = "请选择课程")
    private @Valid List<ElectiveReleaseCoursesSubRespVO> coursesList;

}
