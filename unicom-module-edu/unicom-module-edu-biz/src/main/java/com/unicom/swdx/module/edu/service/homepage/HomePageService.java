package com.unicom.swdx.module.edu.service.homepage;


import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.HomePageDataCardRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeEthnicAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeLevelAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeUnitAnalysisRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;

import java.util.List;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface HomePageService extends IService<TraineeDO> {

    HomePageDataCardRespVO getDataCard();

    TraineeLevelAnalysisRespVO traineeLevelAnalysis();

    TraineeLevelAnalysisRespVO traineeEthnicAnalysis();

    TraineeLevelAnalysisRespVO traineeSexAnalysis();

    List<TraineeUnitAnalysisRespVO> traineeUnitAnalysis();

    List<TraineeUnitAnalysisRespVO> traineeEducationAnalysis();

    List<TraineeUnitAnalysisRespVO> traineeAgeAnalysis();
}
