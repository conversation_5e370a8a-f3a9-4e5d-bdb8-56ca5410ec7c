package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ApiModel(value = "学员请假 base VO")
public class TraineeLeaveMyReqVO {

    @ApiModelProperty(value = "班次id")
    private Long classId;

    @ApiModelProperty(value = "提交时间-开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime applyTimeStart;

    @ApiModelProperty(value = "提交时间-截至日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime applyTimeEnd;

    @ApiModelProperty(value = "流程状态：1撤回，2待审批，3审批中，4已通过，5已拒绝")
    private Integer status;

    @ApiModelProperty(value = "请假类别，对应字典")
    private Integer leaveType;

    @ApiModelProperty(value = "可不传")
    private Long traineeUserId;

    @ApiModelProperty(value = "不用传")
    private Long traineeId;

}
