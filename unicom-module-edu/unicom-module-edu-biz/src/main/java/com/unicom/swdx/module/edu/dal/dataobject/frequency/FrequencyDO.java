package com.unicom.swdx.module.edu.dal.dataobject.frequency;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 使用次数 DO
 *
 * <AUTHOR>
 */
@TableName("edu_frequency")
@KeySequence("edu_frequency_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrequencyDO extends BaseDO {

    /**
     * 序号
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 使用次数
     */
    private Long numberOfTimes;

}
