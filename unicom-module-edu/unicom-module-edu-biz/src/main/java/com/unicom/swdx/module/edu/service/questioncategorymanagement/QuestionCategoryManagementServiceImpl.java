package com.unicom.swdx.module.edu.service.questioncategorymanagement;

import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementSaveReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.module.edu.dal.mysql.options.OptionsMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionmanagement.QuestionManagementMapper;
import com.unicom.swdx.module.edu.utils.tree.TreeDataUtil;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;


import java.math.BigDecimal;
import java.util.*;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;

import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.questioncategorymanagement.QuestionCategoryManagementMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 题目类别管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionCategoryManagementServiceImpl implements QuestionCategoryManagementService {

    @Resource
    private QuestionCategoryManagementMapper questionCategoryManagementMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private QuestionManagementMapper  questionManagementMapper;

    @Resource
    private OptionsMapper optionsMapper;

    @Override
    public Long createQuestionCategoryManagement(QuestionCategoryManagementSaveReqVO createReqVO) {
        // 校验父级的有效性
        validateParentQuestionCategoryManagement(null, createReqVO.getParentId());
        // 校验题目类别名称的唯一性
        validateQuestionCategoryManagementFullNameUnique(null, createReqVO.getParentId(), createReqVO.getFullName());

        // 插入
        QuestionCategoryManagementDO questionCategoryManagement = BeanUtils.toBean(createReqVO, QuestionCategoryManagementDO.class);
        questionCategoryManagementMapper.insert(questionCategoryManagement);
        // 返回
        return questionCategoryManagement.getId();
    }

    @Override
    public void updateQuestionCategoryManagement(QuestionCategoryManagementSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionCategoryManagementExists(updateReqVO.getId());
        // 校验父级的有效性
        validateParentQuestionCategoryManagement(updateReqVO.getId(), updateReqVO.getParentId());
        // 校验题目类别名称的唯一性
        validateQuestionCategoryManagementFullNameUnique(updateReqVO.getId(), updateReqVO.getParentId(), updateReqVO.getFullName());

        // 更新
        QuestionCategoryManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionCategoryManagementDO.class);
        questionCategoryManagementMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionCategoryManagement(Long id) {
        // 校验存在
        validateQuestionCategoryManagementExists(id);

        Long defaultId = questionCategoryManagementMapper.selectDefault();
        if (id.equals(defaultId)) {
            throw exception(DEFAULT_QUESTION_CATEGORY_CANT_DELETE);
        }

        // 将所属的评估项挪到默认类别下
        QuestionCategoryManagementDO defaultCategory = questionCategoryManagementMapper.getDefaultCategory();
        questionManagementMapper.moveToDefaultCategory(defaultCategory.getId(), id);
        // 删除
        questionCategoryManagementMapper.deleteById(id);
    }

    private void validateQuestionCategoryManagementExists(Long id) {
        if (questionCategoryManagementMapper.selectById(id) == null) {
            throw exception(QUESTION_CATEGORY_MANAGEMENT_NOT_EXISTS);
        }
    }

    private void validateParentQuestionCategoryManagement(Long id, Long parentId) {
        if (parentId == null || QuestionCategoryManagementDO.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父题目类别管理
        if (Objects.equals(id, parentId)) {
            throw exception(QUESTION_CATEGORY_MANAGEMENT_PARENT_ERROR);
        }
        // 2. 父题目类别管理不存在
        QuestionCategoryManagementDO parentQuestionCategoryManagement = questionCategoryManagementMapper.selectById(parentId);
        if (parentQuestionCategoryManagement == null) {
            throw exception(QUESTION_CATEGORY_MANAGEMENT_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父题目类别管理，如果父题目类别管理是自己的子题目类别管理，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentId = parentQuestionCategoryManagement.getParentId();
            if (Objects.equals(id, parentId)) {
                throw exception(QUESTION_CATEGORY_MANAGEMENT_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父题目类别管理
            if (parentId == null || QuestionCategoryManagementDO.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentQuestionCategoryManagement = questionCategoryManagementMapper.selectById(parentId);
            if (parentQuestionCategoryManagement == null) {
                break;
            }
        }
    }

    private void validateQuestionCategoryManagementFullNameUnique(Long id, Long parentId, String fullName) {
        QuestionCategoryManagementDO questionCategoryManagement = questionCategoryManagementMapper.selectByParentIdAndFullName(parentId, fullName);
        if (questionCategoryManagement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的题目类别管理
        if (id == null) {
            throw exception(QUESTION_CATEGORY_MANAGEMENT_FULL_NAME_DUPLICATE);
        }
        if (!Objects.equals(questionCategoryManagement.getId(), id)) {
            throw exception(QUESTION_CATEGORY_MANAGEMENT_FULL_NAME_DUPLICATE);
        }
    }

    @Override
    public QuestionCategoryManagementDO getQuestionCategoryManagement(Long id) {
        return questionCategoryManagementMapper.selectById(id);
    }

    @Override
    public List<QuestionCategoryManagementDO> getQuestionCategoryManagementList(QuestionCategoryManagementListReqVO listReqVO) {
        LambdaQueryWrapperX<QuestionCategoryManagementDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(QuestionCategoryManagementDO::getBuiltIn, true);
        Long count = questionCategoryManagementMapper.selectCount(wrapperX);
        // 没有节点默认创建个根结点
        if (count == 0) {
            AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(getLoginUserId()).getCheckedData();
            QuestionCategoryManagementDO createDO = new QuestionCategoryManagementDO();
            createDO.setCreateDept(adminUserRespDTO.getDeptId());
            createDO.setFullName("内置题库");
            createDO.setParentId(0L);
            createDO.setBuiltIn(true);
            questionCategoryManagementMapper.insert(createDO);
            // 新增内置问题
            QuestionManagementDO question0 = new QuestionManagementDO()
                    .setStem("<p>政治纪律为一票否决项目，请对评价结果不合格的课程详细说明理由</p>")
                    .setQuestionType("3")
                    .setScore(new BigDecimal("0.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("政治纪律不合格理由");
            questionManagementMapper.insert(question0);
            QuestionManagementDO question1 = new QuestionManagementDO()
                    .setStem("<p>坚持党校姓党，坚持党性原则，自觉服从服务于党的政治路线，严守党的政治纪律和政治规矩，引导学员深刻领悟“两个确立”的决定性意义，增强“四个意识”、坚定“四个自信”、做到“两个维护”。</p>")
                    .setQuestionType("2")
                    .setScore(new BigDecimal("0.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("政治纪律");
            questionManagementMapper.insert(question1);
            List<OptionsDO> options = new ArrayList<>();
            options.add(new OptionsDO()
                    .setQuestionId(question1.getId())
                    .setOptionsType("0")
                    .setContent("合格")
                    .setScore(new BigDecimal("0.00")));
            options.add(new OptionsDO()
                    .setQuestionId(question1.getId())
                    .setOptionsType("0")
                    .setContent("不合格")
                    .setScore(new BigDecimal("0.00")));
            optionsMapper.insertBatch(options);
            QuestionManagementDO question2 = new QuestionManagementDO()
                    .setStem("<p>您认为本堂课有哪些不足的地方</p>")
                    .setQuestionType("3")
                    .setScore(new BigDecimal("0.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("不足建议");
            questionManagementMapper.insert(question2);
            QuestionManagementDO question3 = new QuestionManagementDO()
                    .setStem("<p>您认为本堂课有哪些亮点</p>")
                    .setQuestionType("3")
                    .setScore(new BigDecimal("0.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("亮点建议");
            questionManagementMapper.insert(question3);
            QuestionManagementDO question4 = new QuestionManagementDO()
                    .setStem("<p><strong style=\"font-size: 14px;\">教学态度：</strong><span style=\"font-size: 14px;\">态度认真，治学严谨，精神饱满，准备充分。</span></p><p><strong style=\"font-size: 14px;\">学术水平：</strong><span style=\"font-size: 14px;\">坚持用学术讲政治，学理框架清晰，观点明确，论述充分，有国际视野和历史参照，有理论深度。</span></p><p><strong style=\"font-size: 14px;\">问题导向：</strong><span style=\"font-size: 14px;\">问题研判精准，联系实际紧密，有解决问题的对策建议，具有较强的针对性和指导性。</span></p><p><strong style=\"font-size: 14px;\">资料信息：</strong><span style=\"font-size: 14px;\">信息内容丰富，数据详实，案例典型，更新及时。</span></p><p><strong style=\"font-size: 14px;\">讲课能力：</strong><span style=\"font-size: 14px;\">表达清晰，有较好的课堂驾驭能力，富有感染力。讲课提纲和课件规范，与授课内容一致。</span></p><p><strong style=\"font-size: 14px;\">教学效果：</strong><span style=\"font-size: 14px;\">重点突出，观点鲜明，分析透彻，印象深刻。</span></p><p><span style=\"font-size: 14px;\">\uFEFF（备注：百分制打分，其中90-100分为优秀，80-90分为良好，60-80分为一般，60分以下为差）</span></p>")
                    .setQuestionType("1")
                    .setScore(new BigDecimal("100.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("讲授式评价题");
            questionManagementMapper.insert(question4);
            QuestionManagementDO question5 = new QuestionManagementDO()
                    .setStem("<p><strong style=\"font-size: 14px;\">教学态度：</strong><span style=\"font-size: 14px;\">态度认真，治学严谨，精神饱满，准备充分。</span></p><p><strong style=\"font-size: 14px;\">案例选择：</strong><span style=\"font-size: 14px;\">政治方向正确，具有典型意义，内容真实完整，理论联系实际。能充分挖掘学员的学习潜能，提升分析与解决实际问题能力。</span></p><p><strong style=\"font-size: 14px;\">组织引导：</strong><span style=\"font-size: 14px;\">突出问题导向，组织有序，互动深入，引\uFEFF导学员围绕主题充分讨论，形成有价值的意见建议。</span></p><p><strong style=\"font-size: 14px;\">学术水平：</strong><span style=\"font-size: 14px;\">坚持用学术讲政治，学理框架清晰，观点明确，论述充分，有国际视野和历史参照，有理论深度。</span></p><p><strong style=\"font-size: 14px;\">总结点评：</strong><span style=\"font-size: 14px;\">能准确归纳和提炼学员观点，并提出富有启发性的见解，引发思考，给人启迪。</span></p><p><span class=\"ql-cursor\">\uFEFF</span>（备注：百分制打分，其中90-100分为优秀，80-90分为良好，60-80分为一般，60分以下为差）</p>")
                    .setQuestionType("1")
                    .setScore(new BigDecimal("100.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("互动式课堂评价");
            questionManagementMapper.insert(question5);
            QuestionManagementDO question6 = new QuestionManagementDO()
                    .setStem("<p><strong>教学点选择：</strong>现场教学点具有典型性，教学主题有现实针对性。</p><p><strong>教学组织：</strong>组织周详，环节合理，引导学员紧扣主题交流分享。</p><p><strong>讲解（课程）质量：</strong>普通话标准，表达准确，流畅自然；情感真实动人，有感染力。</p><p><strong>教学效果：</strong>教学形式新颖，教学特色鲜明，能感染人、教育人、启迪人。</p><p><br></p>")
                    .setQuestionType("1")
                    .setScore(new BigDecimal("100.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("党性教育现场评价");
            questionManagementMapper.insert(question6);
            QuestionManagementDO question7 = new QuestionManagementDO()
                    .setStem("<p><strong>师资选择：</strong>紧紧围绕“国之大者”“省之大计”，精准聚焦学员需求，能显著提升干部教育培训供给质量。</p><p><strong>教学态度：</strong>态度认真，治学严谨，精神饱满，准备充分。</p><p><strong>学术水平：</strong>坚持用学术讲政治，学理框架清晰，观点明确，论述充分，有国际视野和历史参照，有理论深度。</p><p><strong>问题导向：</strong>问题研判精准，联系实际紧密，有解决问题的对策建议，具有较强的针对性和指导性。</p><p><strong>资料信息：</strong>信息内容丰富，数据详实，案例典型，更新及时。</p><p><strong>教学效果：</strong>能与党校师资课程有效互补，重点突出，观点鲜明，分析透彻，印象深刻。</p><p>（备注：百分制打分，其中90-&nbsp;100分为优秀，80-90分为良好，60-80分为一般，60分以下为差）</p>")
                    .setQuestionType("1")
                    .setScore(new BigDecimal("100.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("外请报告评价");
            questionManagementMapper.insert(question7);
            QuestionManagementDO question8 = new QuestionManagementDO()
                    .setStem("<p><strong>教学组织：</strong>组织周详，环节合理，引导学员紧扣主题交流分享。</p><p><strong>学术水平：</strong>坚持用学术讲政治，学理框架清晰，观点明确，论述充分，有国际视野和历史参照，有理论深度。</p><p><strong>问题导向：</strong>问题研判精准，联系实际紧密，有解决问题的对策建议，具有较强的针对性和指导性。</p><p><strong>教学方案：</strong>专题研究式教学方案紧扣主题，目标明确，内容详实，可操作性强。</p><p><strong>教学效果：</strong>重点突出，观点鲜明，分析透彻，对学员有较强的理论提升和实践指导作用，能形成高质量的研究成果。</p><p><br></p>")
                    .setQuestionType("1")
                    .setScore(new BigDecimal("100.00"))
                    .setCategoryId(createDO.getId())
                    .setIsLogic((short) 0)
                    .setTitle("专题研究式评价");
            questionManagementMapper.insert(question8);
        }
        return questionCategoryManagementMapper.selectList(listReqVO);
    }

}
