package com.unicom.swdx.module.edu.dal.redis.edu;

import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.json.JsonUtils;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.module.edu.dal.redis.RedisKeyConstants.TRAINEE_LOGIN_TYPE;

/**
 * 学员移动端学员选择班级登录缓存 {@link TraineeDO}的 RedisDAO
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class TraineeLoginTypeRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public TraineeDO get(Long userId) {
        TraineeDO traineeDO = null;
        try{
            String redisKey = formatKey(userId);
            traineeDO = JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), TraineeDO.class);
        }catch (Exception e){
            log.info("获取学员登录类型缓存失败，userId:{}", userId, e);
        }
        return traineeDO;
    }

    public void set(Long userId, TraineeDO traineeDO) {
        try{
            String redisKey = formatKey(userId);
            // 清理多余字段，避免缓存
            traineeDO.setUpdater(null).setUpdateTime(null).setCreateTime(null).setCreator(null).setDeleted(null);
            stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(traineeDO), TRAINEE_LOGIN_TYPE.getTimeout());
        }catch (Exception e){
            log.info("设置学员登录类型缓存失败，userId:{}", userId, e);
        }

    }

    public void delete(Long userId) {
        String redisKey = formatKey(userId);
        stringRedisTemplate.delete(redisKey);
    }

    public void deleteList(Collection<Long> userIds) {
        List<String> redisKeys = CollectionUtils.convertList(userIds, TraineeLoginTypeRedisDAO::formatKey);
        stringRedisTemplate.delete(redisKeys);
    }

    private static String formatKey(Long userId) {
        return String.format(TRAINEE_LOGIN_TYPE.getKeyTemplate(), userId);
    }

}
