package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 班级课程安排更新 Request VO")
@Data
@ToString(callSuper = true)
public class ClassCourseBindingUpdateReqVO  {

    @ApiModelProperty(value = "唯一标识，课表格子的主键id", required = true)
    @NotNull(message = "唯一标识不能为空")
    private Long id;

    @ApiModelProperty(value = "针对更新绑定关系的班级id", required = true)
    @NotNull(message = "班级id不能为空")
    private Long classId;

    @ApiModelProperty(value = "针对更新绑定关系的班级id", required = true)
    @NotNull(message = "请选择绑定操作为新增/删除，true新增，false删除")
    private Boolean addBinding;

}
