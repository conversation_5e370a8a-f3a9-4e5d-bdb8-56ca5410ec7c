package com.unicom.swdx.module.edu.controller.admin.noticeannouncement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

@ApiModel("管理后台 - EduNoticeAnnouncement Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NoticeAnnouncementRespVO extends NoticeAnnouncementBaseVO {

    @ApiModelProperty(value = "主键，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
