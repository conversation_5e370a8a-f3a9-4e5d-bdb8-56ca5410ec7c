package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 课程评价记录新增/修改 Request VO")
@Data
public class EvaluationResponseSaveReqVO {

    @Schema(description = "评估结果主键",  example = "15291")
    private Long id;

    @Schema(description = "评估问卷id", example = "4615")
    private Long questionnaireId;

    @Schema(description = "评卷人id", example = "27859")
    private Long studentId;

    @Schema(description = "发卷人id")
    private String issuer;

    @Schema(description = "发卷人部门id", example = "18040")
    private Long deptId;

    @Schema(description = "班级教师id", example = "9441")
    private String teacherId;

    @Schema(description = "排课id", example = "23817")
    private Long classCourseId;

    @Schema(description = "租户id", example = "23817")
    private Long tenantId;

    @Schema(description = "总分")
    private BigDecimal score;

    @Schema(description = "评分等级")
    private String grade;

    @Schema(description = "是否评卷 （0 否， 1是）")
    private Boolean handle;

    @Schema(description = "免评价类型（0 必须评价 ，1 请假了不用评）", example = "2")
    private Boolean remarktype;

    @Schema(description = "问卷过期时间", example = "2")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "是否部门评课", example = "教师")
    private Boolean department;

    @Schema(description = "课程id", example = "23817")
    private Long courseId;
}
