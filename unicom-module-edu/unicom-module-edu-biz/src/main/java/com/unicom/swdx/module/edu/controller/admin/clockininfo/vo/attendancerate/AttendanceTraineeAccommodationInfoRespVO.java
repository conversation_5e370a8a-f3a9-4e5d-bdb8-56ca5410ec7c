package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> <PERSON>
 * @Description: 学员住宿未到、请假详情 Resp VO
 * @date 2024-11-08
 */
@ApiModel("学员未到、请假详情 Resp VO")
@Data
public class AttendanceTraineeAccommodationInfoRespVO {

    @ApiModelProperty(value = "学员ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "时间", example = "2024-11-08")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate time;
}
