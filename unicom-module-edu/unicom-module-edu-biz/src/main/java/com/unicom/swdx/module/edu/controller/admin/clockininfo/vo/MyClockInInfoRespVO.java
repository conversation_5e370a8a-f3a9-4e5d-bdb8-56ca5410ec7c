package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName: MyClockInInfoReqVO
 * @Author: lty
 * @Date: 2024/11/6 16:18
 */
@Data
@ToString(callSuper = true)
public class MyClockInInfoRespVO {

    @ApiModelProperty(value = "早餐")
    private List<CourseVO> breakfast;

    @ApiModelProperty(value = "午餐")
    private List<CourseVO> lunch;

    @ApiModelProperty(value = "晚餐")
    private List<CourseVO> dinner;

    @ApiModelProperty(value = "住宿")
    private List<CourseVO> sleep;

    @ApiModelProperty(value = "上午课程")
    private List<CourseVO> morningCourse;

    @ApiModelProperty(value = "下午课程")
    private List<CourseVO> afternoonCourse;

    @ApiModelProperty(value = "晚上课程")
    private List<CourseVO> eveningCourse;

}
