package com.unicom.swdx.module.edu.dal.dataobject.edutrainfile;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
 * @since 2024-12-26 15:13:46
 */
@Data
@TableName("edu_train_file")
@KeySequence("oa_central_task_record_seq")
@ApiModel(value = "EduTrainFile", description = " ")
public class EduTrainFileDO extends BaseDO implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     *  自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *  标题
     */
    @ApiModelProperty("title")
    private String title;

    /**
     *  文件
     */
    @ApiModelProperty("file")
    private String file;

    /**
     *  用户id
     */
    @ApiModelProperty("userId")
    private Long userId;

    /**
     *  用户名字
     */
    @ApiModelProperty("userName")
    private String userName;

    /**
     *  发布时间
     */
    @ApiModelProperty("publishTime")
    private String publishTime;

    /**
     *  租户id
     */
    @ApiModelProperty("tenantId")
    private Long tenantId;

}
