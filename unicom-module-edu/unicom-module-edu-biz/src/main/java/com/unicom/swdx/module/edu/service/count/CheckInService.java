package com.unicom.swdx.module.edu.service.count;

import cn.hutool.core.util.ObjUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangeReqVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangeRespVO;
import com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseExchangeReqVO;

import java.util.Date;
import java.util.Map;

/**
 * 考勤汇总 Service 接口
 *
 * <AUTHOR>
 */
public interface CheckInService {


    /**
     * 获取学员时间段的课表信息
     *
     * @param id 学员id 学员绑定到单个班级
     * @param begintimme endTime 统计的开始结束时间
     * @param id 学员id 学员绑定到单个班级
     * @return 调课记录分页
     */
//    Object getStudentCheckIn(Long  id , Date begintimme , Date endTime);


    /**
     * 获取学员到课率
     *
     * @param id 学员id 学员绑定到单个班级
     * @param begintimme endTime 统计的开始结束时间
     * @param id 学员id 学员绑定到单个班级
     * @return 调课记录分页
     */
    Object getStudentCheckIn(Long  id , Date begintimme , Date endTime);


    void exchange(CourseExchangeReqVO reqVO);

    void change(CourseChangeReqVO reqVO);
}
