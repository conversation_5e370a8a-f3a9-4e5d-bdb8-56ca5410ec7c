package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description: 课评详情-导出 Excel VO
 * @date 2024-11-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class MyEvaluationDetailExcelVO {

    @ExcelProperty(value = "课程名称")
    private String courseName;

    @ExcelProperty(value = "教学形式")
    private String educateForm;

    @ExcelProperty(value = "课程时间")
    private String classDuration;

    @ExcelProperty(value = "授课教师")
    private String teacherName;

    @ExcelProperty(value = "所属部门")
    private String deptNames;

    @ExcelProperty(value = "参评率（%）")
    private String ratioStr;

    @ExcelProperty(value = "平均分")
    private String averageScoreStr;

    @ExcelProperty(value = "排名分")
    private String rankScoreStr;
}
