package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 考勤签到 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClockInInfoRespVO extends ClockInInfoBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

}
