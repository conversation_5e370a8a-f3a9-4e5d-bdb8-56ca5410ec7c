package com.unicom.swdx.module.edu.controller.admin.leavenotification;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.leavenotification.vo.*;
import com.unicom.swdx.module.edu.service.leavenotification.LeaveNotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 离校报备")
@RestController
@RequestMapping("/edu/leave-notification")
@Validated
public class LeaveNotificationController {

    @Resource
    private LeaveNotificationService leaveNotificationService;

    @PostMapping("/create")
    @ApiOperation("创建离校报备")
    @PreAuthorize("@ss.hasPermission('edu:leave-notification:create')")
    public CommonResult<Long> createLeaveNotification(@Valid @RequestBody LeaveNotificationCreateReqVO createReqVO) {
        return success(leaveNotificationService.createLeaveNotification(createReqVO));
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除离校报备")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:leave-notification:delete')")
    public CommonResult<Boolean> deleteLeaveNotification(@RequestParam("id") Long id) {
        leaveNotificationService.deleteLeaveNotification(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得离校报备详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:leave-notification:query')")
    public CommonResult<LeaveNotificationDetailRespVO> getLeaveNotification(@RequestParam("id") Long id) {
        return success(leaveNotificationService.getLeaveNotificationDetail(id));
    }

    @GetMapping("/page")
    @ApiOperation("获得离校报备分页")
    @PreAuthorize("@ss.hasPermission('edu:leave-notification:query')")
    public CommonResult<PageResult<LeaveNotificationRespVO>> getLeaveNotificationPage(
            @Valid LeaveNotificationPageReqVO pageVO) {
        PageResult<LeaveNotificationRespVO> pageResult = leaveNotificationService.getLeaveNotificationPage(pageVO);
        return success(pageResult);
    }

    @PutMapping("/toggle-mobile-mask")
    @ApiOperation("切换学员手机号脱敏状态")
    @ApiImplicitParam(name = "id", value = "学员ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:leave-notification:update')")
    public CommonResult<Boolean> toggleMobileMask(@RequestParam("id") Long id,
            @RequestParam("notificationId") Long notificationId) {
        leaveNotificationService.toggleTraineeMobileMask(id, notificationId);
        return success(true);
    }

    @PostMapping("/batch-notify")
    @ApiOperation("批量通知学员")
    @PreAuthorize("@ss.hasPermission('edu:leave-notification:notify')")
    public CommonResult<Boolean> batchNotifyTrainees(@Valid @RequestBody LeaveNotificationBatchNotifyReqVO reqVO) {
        leaveNotificationService.batchNotifyTrainees(reqVO);
        return success(true);
    }
}
