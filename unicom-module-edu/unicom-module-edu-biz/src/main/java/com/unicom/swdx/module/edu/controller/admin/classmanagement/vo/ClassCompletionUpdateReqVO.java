package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 班级选择结业模版")
@Data
@ToString(callSuper = true)
public class ClassCompletionUpdateReqVO {

    @ApiModelProperty(value = "班级id")
//    @NotNull(message = "班级id不能为空")
    private Long classId;

    @ApiModelProperty(value = "模版编码")
//    @NotNull(message = "模版编码不能为空")
    private String idCode;
}
