package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("管理后台 - 选修课发布信息（不包含选课信息） Response VO")
@Data
public class ElectiveReleasePageSimpleRespVO {

    @ApiModelProperty(value = "选修课发布主键", example = "1024")
    private Long id;

    @ApiModelProperty(value = "发布名称", required = true, example = "选修课发布")
    private String name;

    @ApiModelProperty(value = "选学时间", example = "2021-01-01 00:00:00 至 2021-01-01 00:00:00")
    private String selectionDuration;

    @ApiModelProperty(value = "上课日期", required = true, example = "2021-01-01")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate classDate;

    @ApiModelProperty(value = "上课开始时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classStartTime;

    @ApiModelProperty(value = "上课结束时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classEndTime;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    private Integer dayPeriod;

    @ApiModelProperty(value = "上课时段", example = "上午00:00 - 00:00")
    private String classDuration;

    @ApiModelProperty(value = "创建时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime createTime;
}
