package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
* 考勤签到 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClockInInfoReturnVO {

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "应到人数")
    private Long traineeCount;

    @ApiModelProperty(value = "上课日期")
    private String courseDate;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "到课类型，0-上午，1-下午，2晚上")
    private String coursePeriod;

    @ApiModelProperty(value = "实到人数")
    private Long attendanceCount;

    @ApiModelProperty(value = "迟到人数")
    private Long latecomersCount;

    @ApiModelProperty(value = "请假人数")
    private Long vacateCount;

    @ApiModelProperty(value = "未到人数")
    private Long noShowCount;

    @ApiModelProperty(value = "到课率")
    private Double ratio;

    @ApiModelProperty(value = "排课表id")
    private Long courseId;

    @ApiModelProperty(value = "住宿与就餐日期")
    private Date clockDate;

    @ApiModelProperty(value = "就餐类型，0-早餐，1-中餐，2-晚餐")
    private Integer mealPeriod;



}
