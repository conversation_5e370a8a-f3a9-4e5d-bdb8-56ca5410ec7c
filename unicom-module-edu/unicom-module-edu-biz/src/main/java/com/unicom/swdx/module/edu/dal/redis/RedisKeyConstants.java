package com.unicom.swdx.module.edu.dal.redis;

import com.unicom.swdx.framework.redis.core.RedisKeyDefine;

import java.time.Duration;

import static com.unicom.swdx.framework.redis.core.RedisKeyDefine.KeyTypeEnum.STRING;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    RedisKeyDefine TRAINEE_LOGIN_TYPE = new RedisKeyDefine("学员小程序登录确定学员班级缓存",
            "trainee_login_type:%s", // 参数为 state
            STRING, String.class, Duration.ofMinutes(10)); // 值为 state
}
