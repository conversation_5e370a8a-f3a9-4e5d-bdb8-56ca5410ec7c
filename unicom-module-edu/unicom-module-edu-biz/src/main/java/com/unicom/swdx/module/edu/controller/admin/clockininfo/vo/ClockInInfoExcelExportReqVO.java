package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 考勤签到分页 Request VO")
@Data
@ToString(callSuper = true)
public class ClockInInfoExcelExportReqVO {

    @ApiModelProperty(value = "班级id")
    private Long classId;

    @ApiModelProperty(value = "学员id")
    private Long traineeId;

    @ApiModelProperty(value = "排课表id")
    private Long classCourseId;

    @ApiModelProperty(value = "0-到课，1-就餐，2-住宿")
    private Integer type;

    @ApiModelProperty(value = "0-早餐，1-午餐，2-晚餐（就餐专属）")
    private Integer mealPeriod;

    @ApiModelProperty(value = "0-未打卡，1-已打卡，2-迟到，3-请假")
    private Integer traineeStatus;

    @ApiModelProperty(value = "大课考勤id")
    private Long largeAttendanceId;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "类型, 0-到课，1-就餐，2-住宿")
    @NotNull(message = "类型不能为空")
    private Integer clockInType;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @ApiModelProperty(value = "学员姓名")
    private String name;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

}
