package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description: 学员就餐未到、请假详情 Resp VO
 * @date 2024-11-08
 */
@ApiModel("学员未到、请假详情 Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AttendanceTraineeMealInfoRespVO extends AttendanceTraineeAccommodationInfoRespVO {

    @ApiModelProperty(value = "就餐时间段 0-早餐 1-午餐 2-晚餐", example = "1")
    private Integer mealPeriod;
}
