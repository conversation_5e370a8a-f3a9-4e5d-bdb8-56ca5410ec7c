package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("管理后台 - 选修课发布的班级范围分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassManagementElectiveReleasePageReqVO extends PageParam {

    @ApiModelProperty(value = "班次名称", example = "一班")
    private String className;

    @ApiModelProperty(value = "开始时间", example = "2021-01-01")
    private String startTime;

    @ApiModelProperty(value = "截止时间", example = "2021-01-02")
    private String endTime;

    @ApiModelProperty(value = "班级状态，0-报名中，1-报名结束，2-开班中，3-已结束，4-未开始", example = "0")
    @Range(min = 0, max = 4, message = "班级状态不存在")
    private Integer classStatus;

    @ApiModelProperty(value = "上课日期", required = true, example = "2021-01-01")
    @NotNull(message = "上课日期不能为空")
    private String classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", required = true, example = "1")
    @NotNull(message = "午别时间不能为空")
    @Range(min = 0, max = 2, message = "午别时间不存在")
    private Integer dayPeriod;

    @ApiModelProperty(value = "上课时段开始时间", example = "00:00")
    @NotNull(message = "上课时段开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE)
    private String classStartTimeStr;

    @ApiModelProperty(value = "上课时段结束时间", example = "00:00")
    @NotNull(message = "上课时段结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_HOUR_MINUTE)
    private String classEndTimeStr;

    /**
     * 上课开始时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classStartTime;

    /**
     * 上课结束时间LocalDateTime 辅助字段
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime classEndTime;

}
