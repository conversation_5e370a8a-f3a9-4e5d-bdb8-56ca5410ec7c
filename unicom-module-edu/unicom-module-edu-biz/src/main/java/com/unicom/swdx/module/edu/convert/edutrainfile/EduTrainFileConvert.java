package com.unicom.swdx.module.edu.convert.edutrainfile;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.*;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFileExcelVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageCreateVO;
import com.unicom.swdx.module.edu.controller.admin.edutrainfile.vo.EduTrainFilePageRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.edutrainfile.EduTrainFileDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 课程库 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface EduTrainFileConvert {

    EduTrainFileConvert INSTANCE = Mappers.getMapper(EduTrainFileConvert.class);

    List<EduTrainFilePageRespVO> convertList(List<EduTrainFileDO> eduTrainFileDOList);

    EduTrainFileDO convert(EduTrainFilePageCreateVO eduTrainFilePageCreateVO);

    List<EduTrainFileExcelVO> fileDOConvertFileExcelVO(List<EduTrainFileDO> eduTrainFileDOList);
}
