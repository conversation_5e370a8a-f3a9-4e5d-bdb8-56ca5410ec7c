package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: TraineeLeaveCreateReqVO
 * @Author: youxiaoyan
 * @Date: 2024/11/5
 */
@Data
@ApiModel(value = "学员发起请假 request VO")
public class TraineeLeaveCreateReqVO extends TraineeLeaveBaseVO {

    @ApiModelProperty(value = "请假id，从草稿提交时需要传入，否则不传")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

}
