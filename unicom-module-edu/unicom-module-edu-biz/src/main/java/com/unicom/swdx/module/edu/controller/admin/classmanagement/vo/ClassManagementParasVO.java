package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * EduClassroomLibrary Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ClassManagementParasVO {


    @ApiModelProperty(value = "勾选id，选中导出，不传导出所有")
    private String ids;

    @ApiModelProperty(value = "导出所选的列，不传导出所有列")
    private String selectedColumns;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期")
    private Integer semester;

    @ApiModelProperty(value = "校区")
    private String campus;

    @ApiModelProperty(value = "0-升序，1-降序")
    @NotNull(message = "change不能为空")
    private Integer change;

    @ApiModelProperty(value = "排序标签，0-排序，1-班次编码，2-班次名称，3-开班日期")
    @NotNull(message = "tag不能为空")
    private Integer tag;

    @ApiModelProperty(value = "班级状态，0-报名中，1-报名结束，2-开班中，3-已结束")
    private Integer classStatus;

}
