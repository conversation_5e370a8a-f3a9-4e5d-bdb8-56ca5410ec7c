package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("学院移动端-教学评估 Response VO")
@Data
public class EvaluationRespVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "课程名称", example = "课程")
    private String courseName;

    @ApiModelProperty(value = "上课日期", example = "2021-01-01")
    private String classTime;

    @ApiModelProperty(value = "授课教师ID", example = "1")
    private List<Long> teacherId;

    @ApiModelProperty(value = "授课教师ID,String", example = "1")
    private String teacherIds;

    @ApiModelProperty(value = "是否部门授课", example = "1")
    private Boolean department;

    @ApiModelProperty(value = "问卷ID", example = "1")
    private Long questionnaireId;

    @ApiModelProperty(value = "问卷得分", example = "98")
    private Long score;

    @ApiModelProperty(value = "排课ID", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "授课教师名称", example = "教师")
    private String teacherName;

    @ApiModelProperty(value = "是否已评卷 0 否， 1是")
    private Boolean handle;

    @ApiModelProperty(value = "是否已过期 0 否， 1是")
    private Boolean expired;

    @ApiModelProperty(value = "问卷过期时间", example = "2021-01-01 11:11:11")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "课程开始时间", example = "2021-01-01 11:11:11")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "是否可撤回")
    private Boolean revocable;

    @ApiModelProperty(value = "是否撤回过")
    private Boolean revoked;
}
