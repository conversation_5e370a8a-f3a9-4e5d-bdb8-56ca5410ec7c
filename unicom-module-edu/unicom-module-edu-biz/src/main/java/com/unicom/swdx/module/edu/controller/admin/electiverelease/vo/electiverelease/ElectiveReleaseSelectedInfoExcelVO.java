package com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 管理后台 - 选修课发布信息已选人数列表分页 导出 VO
 */
@ApiModel("管理后台 - 选修课发布信息已选人数列表 导出 VO")
@Data
public class ElectiveReleaseSelectedInfoExcelVO {

    @ExcelProperty(value = "选修课名称")
    private String courseName;

    @ExcelProperty(value = "授课教师")
    private String teacherName;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "姓名")
    private String traineeName;

    @ExcelProperty(value = "性别")
    private String traineeSex;

    @ExcelProperty(value = "职务")
    private String position;

}
