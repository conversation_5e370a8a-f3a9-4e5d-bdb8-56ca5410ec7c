package com.unicom.swdx.module.edu.convert.electiverelease;

import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.dto.ElectiveTraineeSelectedCoursesAndReleaseDTO;
import com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 选修课发布信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveReleaseConvert {

    ElectiveReleaseConvert INSTANCE = Mappers.getMapper(ElectiveReleaseConvert.class);

    ElectiveReleaseDO convert(ElectiveReleaseCreateReqVO bean);

    ElectiveReleaseGetRespVO convert02(ElectiveReleaseDO electiveReleaseDO);

    List<ElectiveReleaseExcelVO> convertList(List<ElectiveReleasePageRespVO> respVOList);

    List<ElectiveReleaseSelectedInfoExcelVO> convertList02(List<ElectiveReleaseSelectedInfoRespVO> list);

    List<ElectiveReleaseSimpleExcelVO> convertList03(List<ElectiveReleaseExcelVO> list);

    List<ElectiveReleasePageRespVO> convertList04(List<ElectiveReleasePageSimpleRespVO> electiveReleasePageSimpleRespVOS);

    List<AppElectiveReleaseTraineeRespVO> convertList05(List<ElectiveReleasePageSimpleRespVO> electiveReleasePageSimpleRespVOS);

    ElectiveReleaseCoursesSubRespVO convert03(ElectiveTraineeSelectedCoursesAndReleaseDTO dto);
}
