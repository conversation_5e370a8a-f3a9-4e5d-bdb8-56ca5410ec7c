package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.enums.GenderEnum;
import com.unicom.swdx.framework.common.enums.NationEnum;
import com.unicom.swdx.framework.common.enums.PoliticalOutlookEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserAppletBaseVO {

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期，格式：yyyy-MM-dd", required = true)
    @NotNull(message = "开始日期不能为空")
    private LocalDate dateBegin;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "结束日期，格式：yyyy-MM-dd", required = true)
    @NotNull(message = "结束日期不能为空")
        private LocalDate dateEnd;

    /**
     * 教师ID集合
     */
    @ApiModelProperty(value = "教师ID集合", required = true)
    @NotNull(message = "教师ID集合不能为空")
    private List<Long> teacherIdList;

    /**
     * 教师ID集合
     */
    @ApiModelProperty(value = "课程类型：1专题，2选修课", required = true)
    @NotNull(message = "课程类型不能为空")
    private Integer courseType;


}
