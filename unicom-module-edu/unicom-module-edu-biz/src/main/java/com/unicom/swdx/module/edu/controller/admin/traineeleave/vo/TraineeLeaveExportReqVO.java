package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("请假管理 - 导出 Request VO")
@Data
@ToString(callSuper = true)
public class TraineeLeaveExportReqVO extends TraineeLeaveReqVO{

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

//    @ApiModelProperty(value = "勾选id")
//    private List<Long> idList;

}
