package com.unicom.swdx.module.edu.controller.admin.classclockcalendar;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.*;
import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.module.edu.convert.classclockcalendar.ClassClockCalendarConvert;
import com.unicom.swdx.module.edu.service.classclockcalendar.ClassClockCalendarService;

@Api(tags = "管理后台 - 班级考勤日历")
@RestController
@RequestMapping("/edu/class-clock-calendar")
@Validated
public class ClassClockCalendarController {

    @Resource
    private ClassClockCalendarService classClockCalendarService;

//    @PostMapping("/create")
//    @ApiOperation("创建班级考勤日历")
//    @PreAuthorize("@ss.hasPermission('edu:class-clock-calendar:create')")
//    public CommonResult<Integer> createClassClockCalendar(@Valid @RequestBody ClassClockCalendarCreateReqVO createReqVO) {
//        return success(classClockCalendarService.createClassClockCalendar(createReqVO));
//    }

    @PostMapping("/update")
    @ApiOperation("单个更新")
    @PreAuthorize("@ss.hasPermission('edu:class-clock-calendar:update')")
    public CommonResult<Boolean> updateClassClockCalendar(@Valid @RequestBody ClassClockCalendarUpdateReqVO updateReqVO) {
        classClockCalendarService.updateClassClockCalendar(updateReqVO);
        return success(true);
    }

    @PostMapping("/batch-update")
    @ApiOperation("批量更新")
    @PreAuthorize("@ss.hasPermission('edu:class-clock-calendar:update')")
    public CommonResult<Boolean> updateClassClockCalendarBatch(@Valid @RequestBody ClassClockCalendarParamsVO updateReqVO) {
        classClockCalendarService.updateClassClockCalendarBatch(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得班级考勤日历")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-clock-calendar:query')")
    public CommonResult<ClassClockCalendarRespVO> getClassClockCalendar(@RequestParam("id") Integer id) {
        ClassClockCalendarDO classClockCalendar = classClockCalendarService.getClassClockCalendar(id);
        return success(ClassClockCalendarConvert.INSTANCE.convert(classClockCalendar));
    }

//    @GetMapping("/list")
//    @ApiOperation("获得班级考勤日历列表")
//    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
//    @PreAuthorize("@ss.hasPermission('edu:class-clock-calendar:query')")
//    public CommonResult<List<ClassClockCalendarRespVO>> getClassClockCalendarList(@RequestParam("ids") Collection<Integer> ids) {
//        List<ClassClockCalendarDO> list = classClockCalendarService.getClassClockCalendarList(ids);
//        return success(ClassClockCalendarConvert.INSTANCE.convertList(list));
//    }

    @GetMapping("/range-list")
    @ApiOperation("根据班级id获取班级考勤日历")
    @PreAuthorize("@ss.hasPermission('edu:class-clock-calendar:query')")
    public CommonResult<List<ClassClockCalendarRespVO>> getClassClockCalendarPage(@Valid ClassClockCalendarListReqVO pageVO) {
        List<ClassClockCalendarDO> listResult = classClockCalendarService.getClassClockCalendarPage(pageVO);
        return success(ClassClockCalendarConvert.INSTANCE.convertList(listResult));
    }


}
