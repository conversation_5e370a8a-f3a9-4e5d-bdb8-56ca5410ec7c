package com.unicom.swdx.module.edu.dal.mysql.schoolaccommodationattendance;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 全校就餐住宿考勤 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchoolAccommodationAttendanceMapper extends BaseMapperX<SchoolAccommodationAttendanceDO> {


    /**
     *  查询每月的日历考勤数据数据
     * @param reqVO
     * @return
     */
    List<SchoolAccommodationAttendanceDO> selectDateRangeList(@Param("reqVO") SchoolAccommodationAttendancePageReqVO reqVO);


}
