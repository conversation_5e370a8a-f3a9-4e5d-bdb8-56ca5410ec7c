package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * @ClassName: ExportCadreInfoExcelVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "干部信息导出VO")
public class ExportCadreInfoExcelVO {
    @ExcelProperty(value = "序号")
    private Integer index;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "性别")
    private String sex;

    @ExcelProperty(value = "手机号码")
    private String phone;

    @ExcelProperty(value = "职级")
    private String jobLevel;

    @ExcelProperty(value = "工作单位")
    private String unitName;

    @ExcelProperty(value = "职务")
    private String position;

    @ExcelProperty(value = "培训次数")
    private Integer traineeCount;

    @ExcelProperty(value = "政治面貌")
    private String politicalIdentity;

    @ExcelProperty(value = "文化程度")
    private String educationalLevel;

    @ExcelProperty(value = "民族")
    private String ethnic;

    @ExcelIgnore
    private String oldjobLevel;


}
