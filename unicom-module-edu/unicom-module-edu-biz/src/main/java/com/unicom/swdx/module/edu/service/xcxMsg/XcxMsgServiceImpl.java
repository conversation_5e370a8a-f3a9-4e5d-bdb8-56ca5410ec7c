package com.unicom.swdx.module.edu.service.xcxMsg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxGroupMsg;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgSendReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.dal.mysql.xcxmsg.XcxMsgConfigMapper;
import com.unicom.swdx.module.edu.enums.xcxmsg.XcxMsgSendType;
import dm.jdbc.b.e;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.unicom.swdx.module.edu.enums.xcxmsg.XcxMsgSendType.*;

@Service
@Slf4j
public class XcxMsgServiceImpl implements XcxMsgService{

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private XcxMsgConfigMapper configMapper;

    @Resource
    private RedissonClient redissonClient;

    @Value("${unicom.mid-base-uri}")
    private String MID_BASE_URI;

    private final String sendXcxGroupMsgUri = "/xcx-api/system/xcxMsg/sendPermanentToGroup";

    private final String redirectBaseUri = "pages-home/subMessage/index";

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    private DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");


    @Override
    public boolean sendSingleXcxMsg(XcxGroupMsg xcxMsg) {
        return false;
    }

    @Override
    @Async
    public void sendBatchXcxMsg(XcxMsgSendReqVO reqVO) {

        if(CollUtil.isEmpty(reqVO.getTraineeIds())){
            return ;
        }

        // 把traineeIds变成业中的userIds
        List<Long> userIds = traineeMapper.selectUserSystemIdList(reqVO.getTraineeIds());
        if(userIds.isEmpty()){
            return ;
        }

        XcxGroupMsg msg = new XcxGroupMsg();
        msg.setUserIds(userIds);
        XcxMsgSendType type = XcxMsgSendType.getByCode(reqVO.getInformType());
        if(type == null){
            return ;
        }
        if(Objects.equals(reqVO.getInformType(), BJ_KQ.getCode()) ){
            msg.setInformContent(reqVO.getInformItem()+"-签到提醒");
        }else {
            msg.setInformContent(type.getDesc());
        }

        if(StrUtil.isNotEmpty(reqVO.getInformPeriod())){
            msg.setInformTime(reqVO.getInformPeriod());
        }else {
            msg.setInformTime(formatter.format(LocalDateTime.now()));
        }

        msg.setRemark(type.getRemark());

        if(Objects.equals(reqVO.getInformType(), BJ_KQ.getCode())
            || Objects.equals(reqVO.getInformType(), LEAVE_FIN.getCode())
                || Objects.equals(reqVO.getInformType(), LEAVE_REPORT.getCode())
            || Objects.equals(reqVO.getInformType(), KC_PJ.getCode())){
            msg.setPageUri(redirectBaseUri+type.getPath()+"&next_query="+reqVO.getRedirectParam());
        } else {
            msg.setPageUri(redirectBaseUri+type.getPath());
        }
        log.info("--------小程序服务通知按键发送--------"+msg);
        try {
            HttpResponse execute = HttpUtil.createPost(MID_BASE_URI + sendXcxGroupMsgUri)
                    .body(JSONUtil.toJsonStr(msg))
                    .execute();
            System.out.println(execute.body());
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public void sendBatchXcxMsg(XcxGroupMsg msg) {

        if(CollUtil.isEmpty(msg.getUserIds())){
            return ;
        }

        String lockKey = "Lock:WxXcxMsg:"+msg.getPageUri()+":"+msg.getInformTime();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock(1, TimeUnit.MINUTES);
            log.info("--------小程序服务通知定时发送--------"+msg);
            HttpResponse response = HttpUtil.createPost(MID_BASE_URI + sendXcxGroupMsgUri)
                    .body(JSONUtil.toJsonStr(msg))
                    .execute();
            log.info("--------小程序服务通知定时发送返回--------"+response.toString());
        }catch (Exception e){
            log.error("发送小程序服务通知失败，参数为：{}", JSONUtil.toJsonStr(msg), e);
        } finally {
            lock.unlock();
        }

    }

}
