package com.unicom.swdx.module.edu.service.questionlogic;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 问题逻辑 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionLogicService {

    /**
     * 创建问题逻辑
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createQuestionLogic(@Valid QuestionLogicSaveReqVO createReqVO);

    /**
     * 更新问题逻辑
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionLogic(@Valid QuestionLogicSaveReqVO updateReqVO);

    /**
     * 删除问题逻辑
     *
     * @param id 编号
     */
    void deleteQuestionLogic(Integer id);

    /**
     * 获得问题逻辑
     *
     * @param id 编号
     * @return 问题逻辑
     */
    QuestionLogicDO getQuestionLogic(Integer id);

    /**
     * 获得问题逻辑分页
     *
     * @param pageReqVO 分页查询
     * @return 问题逻辑分页
     */
    PageResult<QuestionLogicDO> getQuestionLogicPage(QuestionLogicPageReqVO pageReqVO);

}