package com.unicom.swdx.module.edu.utils.yezhong;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Slf4j
public class YezhongUtil {

    /**
     * 从请求头中获取token
     *
     * @return token
     */
    public static String getTokenFromRequestHead() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        // 如果token以"Bearer "开头，则提取实际的token值
        if (token != null && token.startsWith("Bearer ")) {
            // 去除"Bearer "部分
            token = token.substring(7);
        }
        log.info("获得请求头Authorization:{}", token);
        return token;
    }

}
