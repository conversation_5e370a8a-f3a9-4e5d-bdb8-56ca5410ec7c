package com.unicom.swdx.module.edu.controller.admin.questionlogic;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;



import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import com.unicom.swdx.module.edu.service.questionlogic.QuestionLogicService;

@Tag(name = "管理后台 - 问题逻辑")
@RestController
@RequestMapping("/edu/question-logic")
@Validated
public class QuestionLogicController {

    @Resource
    private QuestionLogicService questionLogicService;

    @PostMapping("/create")
    @Operation(summary = "创建问题逻辑")
    @PreAuthorize("@ss.hasPermission('edu:question-logic:create')")
    public CommonResult<Integer> createQuestionLogic(@Valid @RequestBody QuestionLogicSaveReqVO createReqVO) {
        return success(questionLogicService.createQuestionLogic(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新问题逻辑")
    @PreAuthorize("@ss.hasPermission('edu:question-logic:update')")
    public CommonResult<Boolean> updateQuestionLogic(@Valid @RequestBody QuestionLogicSaveReqVO updateReqVO) {
        questionLogicService.updateQuestionLogic(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除问题逻辑")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-logic:delete')")
    public CommonResult<Boolean> deleteQuestionLogic(@RequestParam("id") Integer id) {
        questionLogicService.deleteQuestionLogic(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得问题逻辑")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-logic:query')")
    public CommonResult<QuestionLogicRespVO> getQuestionLogic(@RequestParam("id") Integer id) {
        QuestionLogicDO questionLogic = questionLogicService.getQuestionLogic(id);
        return success(BeanUtils.toBean(questionLogic, QuestionLogicRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得问题逻辑分页")
    @PreAuthorize("@ss.hasPermission('edu:question-logic:query')")
    public CommonResult<PageResult<QuestionLogicRespVO>> getQuestionLogicPage(@Valid QuestionLogicPageReqVO pageReqVO) {
        PageResult<QuestionLogicDO> pageResult = questionLogicService.getQuestionLogicPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionLogicRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出问题逻辑 Excel")
    @PreAuthorize("@ss.hasPermission('edu:question-logic:export')")
    public void exportQuestionLogicExcel(@Valid QuestionLogicPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionLogicDO> list = questionLogicService.getQuestionLogicPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "问题逻辑.xls", "数据", QuestionLogicRespVO.class,
                        BeanUtils.toBean(list, QuestionLogicRespVO.class));
    }

}
