package com.unicom.swdx.module.edu.service.options;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.options.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 选项 Service 接口
 *
 * <AUTHOR>
 */
public interface OptionsService {

    /**
     * 创建选项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOptions(@Valid OptionsSaveReqVO createReqVO);

    /**
     * 更新选项
     *
     * @param updateReqVO 更新信息
     */
    void updateOptions(@Valid OptionsSaveReqVO updateReqVO);

    /**
     * 删除选项
     *
     * @param id 编号
     */
    void deleteOptions(Long id);

    /**
     * 获得选项
     *
     * @param id 编号
     * @return 选项
     */
    OptionsDO getOptions(Long id);

    /**
     * 获得选项分页
     *
     * @param pageReqVO 分页查询
     * @return 选项分页
     */
    PageResult<OptionsDO> getOptionsPage(OptionsPageReqVO pageReqVO);

}