<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getLectureAttendanceTraineeInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO">
        select
            ecii.trainee_id id,
            etg.group_name groupName,
            et."name" "name",
            et.sex,
            et.phone,
            et."position",
            et.class_committee_id,
            ecc.class_committee_name
        from edu_clock_in_info ecii
                 left join edu_trainee et on ecii.trainee_id = et.id and et.deleted = 0
                 left join edu_class_committee ecc on ecc.id  = et.class_committee_id and ecc.deleted = 0
                 left join edu_trainee_group etg on et.group_id = etg.id and etg.deleted = 0
        where ecii.deleted = 0
          and ecii.class_id = #{classId}
          and ecii.class_course_id = #{classCourseId}
          and ecii."type" = 0
          <if test="status == null or status == 0">
              and ecii.trainee_status != 1
          </if>
          <if test="status == 1">
              and ecii.trainee_status = 1
          </if>
        order by etg.sort, et.group_sort,et.id desc
    </select>

    <select id="getTraineeAttendanceInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateReqVO">
        SELECT
            e.trainee_id,
            e."type",
            COUNT(1) AS attendanceExpected,
            COUNT(e.trainee_status IN (1, 2) or NULL) AS attendanceActual,
            COUNT(e.trainee_status = 3 or NULL) AS attendanceLeave,
            <choose>
                <when test="ruleType == 0">
                    attendanceActual/COALESCE(NULLIF(attendanceExpected - attendanceLeave, 0), 1)
                </when>
                <otherwise>
                    -- 处理默认情况的逻辑
                    attendanceActual/COALESCE(NULLIF(attendanceExpected, 0), 1)
                </otherwise>
            </choose>
            as rate
        from
            edu_clock_in_info e
        where
            e.deleted = 0
            and e.class_id = #{reqVO.classId}
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
        GROUP BY
        e.trainee_id, e."type"
    </select>

    <select id="getTraineeClassAttendanceInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateReqVO">
        SELECT
            e.trainee_id,
            COUNT(1) AS attendanceExpected,
            COUNT(e.trainee_status IN (1, 2) or NULL) AS attendanceActual,
            COUNT(e.trainee_status = 3 or NULL) AS attendanceLeave,
        <choose>
            <when test="ruleType == 0">
                attendanceActual/COALESCE(NULLIF(attendanceExpected - attendanceLeave, 0), 1)
            </when>
            <otherwise>
                -- 处理默认情况的逻辑
                attendanceActual/COALESCE(NULLIF(attendanceExpected, 0), 1)
            </otherwise>
        </choose>
        as rate
        FROM
            edu_clock_in_info e
        WHERE
            e."type" = 0
            AND e.deleted = 0
            and e.class_id = #{reqVO.classId}
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
        GROUP BY
            e.trainee_id
    </select>

    <select id="getTraineeMealAttendanceInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateReqVO">
        SELECT
        e.trainee_id,
            COUNT(1) AS attendanceExpected,
            COUNT(e.trainee_status IN (1, 2) or NULL) AS attendanceActual,
            COUNT(e.trainee_status = 3 or NULL) AS attendanceLeave,
        <choose>
            <when test="ruleType == 0">
                attendanceActual/COALESCE(NULLIF(attendanceExpected - attendanceLeave, 0), 1)
            </when>
            <otherwise>
                -- 处理默认情况的逻辑
                attendanceActual/COALESCE(NULLIF(attendanceExpected, 0), 1)
            </otherwise>
        </choose>
        as rate
        from
            edu_clock_in_info e
        where
            e."type" = 1
            AND e.deleted = 0
            and e.class_id = #{reqVO.classId}
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
        GROUP BY
        e.trainee_id
    </select>

    <select id="getTraineeAccommodationAttendanceInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateReqVO">
        SELECT
            e.trainee_id,
            COUNT(1) AS attendanceExpected,
            COUNT(e.trainee_status IN (1, 2) or NULL) AS attendanceActual,
            COUNT(e.trainee_status = 3 or NULL) AS attendanceLeave,
            <choose>
                <when test="ruleType == 0">
                    attendanceActual/COALESCE(NULLIF(attendanceExpected - attendanceLeave, 0), 1)
                </when>
                <otherwise>
                    -- 处理默认情况的逻辑
                    attendanceActual/COALESCE(NULLIF(attendanceExpected, 0), 1)
                </otherwise>
            </choose>
            as rate
        from
            edu_clock_in_info e
        where
            e."type" = 2
            AND e.deleted = 0
            and e.class_id = #{reqVO.classId}
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
        GROUP BY
        e.trainee_id
    </select>
    <select id="getClassTraineeNotArrivedAndLeaveInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceTraineeClassInfoRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceNotArrivedAndLeaveInfoReqVO">
        SELECT
            e.trainee_id,
            et."name" traineeName,
            ecc."date" classDate,
            ecc.period period,
            ecc.begin_time classStartTime,
            ecc.end_time classEndTime,
            ecc.course_id
        FROM
            edu_clock_in_info e
                JOIN edu_class_course ecc ON ecc.id = e.class_course_id AND ecc.deleted = 0
                join edu_trainee et on e.trainee_id = et.id and e.deleted = 0
        WHERE
            e."type" = 0
          AND e.deleted = 0
          <if test="reqVO.type == null or reqVO.type == 0">
              AND e.trainee_status = 0
          </if>
          <if test="reqVO.type == 1">
              AND e.trainee_status = 3
          </if>
          <if test="reqVO.startTime != null">
              AND CAST(ecc."date" AS date) &gt;= #{reqVO.startTime}
          </if>
          <if test="reqVO.endTime != null">
              AND CAST(ecc."date" AS date) &lt;= #{reqVO.endTime}
          </if>
          and e.trainee_id = #{reqVO.traineeId}
        order by ecc."date", ecc.begin_time
    </select>

    <select id="getMealTraineeNotArrivedAndLeaveInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceTraineeMealInfoRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceNotArrivedAndLeaveInfoReqVO">
        SELECT
            e.trainee_id,
            et."name" traineeName,
            e.meal_period,
            e."date" time
        FROM
            edu_clock_in_info e
            join edu_trainee et on e.trainee_id = et.id and e.deleted = 0
        WHERE
            e."type" = 1
            AND e.deleted = 0
            <if test="reqVO.type == null or reqVO.type == 0">
                AND e.trainee_status = 0
            </if>
            <if test="reqVO.type == 1">
                AND e.trainee_status = 3
            </if>
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
            and e.trainee_id = #{reqVO.traineeId}
        order by e."date", e.meal_period
    </select>
    <select id="getAccommodationTraineeNotArrivedAndLeaveInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceTraineeAccommodationInfoRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceNotArrivedAndLeaveInfoReqVO">
        SELECT
            e.trainee_id,
            et."name" traineeName,
            e."date" time
        FROM
            edu_clock_in_info e
            join edu_trainee et on e.trainee_id = et.id and e.deleted = 0
        WHERE
            e."type" = 2
            AND e.deleted = 0
            <if test="reqVO.type == null or reqVO.type == 0">
                AND e.trainee_status = 0
            </if>
            <if test="reqVO.type == 1">
                AND e.trainee_status = 3
            </if>
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
            and e.trainee_id = #{reqVO.traineeId}
        order by e."date"
    </select>

    <select id="selectPageClockInList" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoReturnVO">
        SELECT
        *
        FROM
        (
        SELECT
        ecii."class_id",
        COUNT(DISTINCT ecii."trainee_id") AS trainee_count,
        -- 迟到人数
        COUNT(CASE WHEN ecii."trainee_status" = 2 THEN 1 END) AS latecomers_count,
        --- 请假人数
        COUNT(CASE WHEN ecii."trainee_status" = 3 THEN 1 END) AS vacate_count,
        -- 未到人数
        COUNT(CASE WHEN ecii."trainee_status" = 0 THEN 1 END) AS no_show_count,
        -- 实到人数
        COUNT(CASE WHEN ecii."trainee_status" = 1 THEN 1 END) + COUNT(CASE WHEN ecii."trainee_status" = 2 THEN 1 END) AS attendance_count,
        ecc."date" AS course_date,
        ec."name" AS course_name,
        ecc."begin_time" AS course_begin_time,
        ecc."period" AS course_period,
        ecc."id" AS course_id
        FROM
        "edu_clock_in_info" ecii
        LEFT JOIN "edu_class_course" ecc ON
        ecii."class_course_id" = ecc."id"
        AND ecc."deleted" = '0'
        LEFT JOIN "edu_courses" ec ON
        ec."id" = ecc."course_id"
        AND ec."deleted" = '0'
        WHERE
        ecii."type" = #{reqVO.type}
        and
        ecii."deleted" = '0'
        <if test="reqVO.endTime != null">
            and DATE(ecc."begin_time") &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.startTime != null">
            and DATE(ecc."begin_time") >= #{reqVO.startTime}
        </if>
        <if test="reqVO.classId != null">
            and ecii."class_id" = #{reqVO.classId}
        </if>
        GROUP BY
        ecii."class_id",
        ecc."id",
        ecc."date",
        ec."name",
        ecc."begin_time",
        ecc."period",
        ecc."id"
        ORDER BY
        ecii."class_id",
        ecc."begin_time"
        ) AS t
        ORDER BY
        t.course_begin_time
    </select>

    <select id="selectPageListMealAttendance" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoReturnVO">
        SELECT
        *
        FROM
        (

        SELECT
        ecii."class_id",
        COUNT(DISTINCT ecii."trainee_id") AS trainee_count,
        --- 请假人数
        COUNT(CASE WHEN ecii."trainee_status" = 3 THEN 1 END) AS vacate_count,
        -- 未到人数
        COUNT(CASE WHEN ecii."trainee_status" = 0 THEN 1 END) AS no_show_count,
        -- 实到人数
        COUNT(CASE WHEN ecii."trainee_status" = 1 THEN 1 END) AS attendance_count,
        ecii ."meal_period" ,
        DATE(ecii."create_time") AS clock_date
        FROM
        "edu_clock_in_info" ecii
        WHERE
        ecii."type" = #{reqVO.type}
        and
        ecii."deleted" = '0'
        <if test="reqVO.classId != null">
            and ecii."class_id" = #{reqVO.classId}
        </if>
        <if test="reqVO.endTime != null">
            and DATE(ecii."create_time") &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.startTime != null">
            and DATE(ecii."create_time") >= #{reqVO.startTime}
        </if>
        GROUP BY
        ecii."class_id",
        ecii ."meal_period" ,
        DATE(ecii."create_time")
        ORDER BY
        ecii."class_id",
        ecii ."meal_period"
        ) AS t
        ORDER BY
        t.clock_date,
        t.meal_period
    </select>
    <select id="selectPageListCheckIn" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoReturnVO">
        SELECT
        *
        FROM
        (
        SELECT
        ecii."class_id",
        COUNT(DISTINCT ecii."trainee_id") AS trainee_count,
        --- 请假人数
        COUNT(CASE WHEN ecii."trainee_status" = 3 THEN 1 END) AS vacate_count,
        -- 未到人数
        COUNT(CASE WHEN ecii."trainee_status" = 0 THEN 1 END) AS no_show_count,
        -- 实到人数
        COUNT(CASE WHEN ecii."trainee_status" = 1 THEN 1 END) AS attendance_count,
        DATE(ecii."create_time") AS clock_date
        FROM
        "edu_clock_in_info" ecii
        WHERE
        ecii."type" = #{reqVO.type}
        and
        ecii."deleted" = '0'
        <if test="reqVO.endTime != null">
            and DATE(ecii."create_time") &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.startTime != null">
            and DATE(ecii."create_time") >= #{reqVO.startTime}
        </if>
        <if test="reqVO.classId != null">
            and ecii."class_id" = #{reqVO.classId}
        </if>
        GROUP BY
        ecii."class_id",
        DATE(ecii."create_time")
        ORDER BY
        ecii."class_id"
        ) AS t
        ORDER BY
        t.clock_date
    </select>
    <select id="getStudentClockingStatusPage" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoStudentStatusVO">
        SELECT
        ecii."id" as recordId,
        ecii."class_id",
        ecii."trainee_id",
        ecii."class_course_id",
        ecii."type",
        ecii."meal_period",
        ecii."trainee_status",
        ecii."clocking_time",
        et."name",
        ecii."leave_type",
        (SELECT COUNT(DISTINCT t."trainee_id") FROM "edu_clock_in_info" t WHERE t."class_course_id" = ecii."class_course_id" AND t."deleted" = '0') AS "trainee_count"
        FROM
        "edu_clock_in_info" ecii LEFT JOIN  "edu_trainee" et ON ecii."trainee_id" = et."id" AND et."deleted" = '0'
        WHERE
        ecii."deleted" = '0'
        <if test="reqVO.classId != null">
            AND ecii ."class_id" = #{reqVO.classId}
        </if>
        <if test="reqVO.name != null and reqVO.name !=''">
            AND et."name" LIKE CONCAT('%', #{reqVO.name}, '%')
        </if>
        <if test="reqVO.clockInType != null">
            <if test="reqVO.clockInType == 0">
                <if test="reqVO.traineeStatus == 1">
                    AND (ecii."trainee_status" = '1' or ecii."trainee_status" = '2')
                </if>
                <if test="reqVO.traineeStatus != 1">
                    AND ecii."trainee_status" = #{reqVO.traineeStatus}
                </if>
            </if>
            <if test="reqVO.clockInType == 1">
                AND ecii."trainee_status" = #{reqVO.traineeStatus}
            </if>
            <if test="reqVO.clockInType == 2">
                AND ecii."trainee_status" = #{reqVO.traineeStatus}
            </if>
        </if>
        <if test="reqVO.classCourseId != null">
            AND ecii ."class_course_id" = #{reqVO.classCourseId}
        </if>
        <if test="reqVO.type != null">
            AND ecii ."type" = #{reqVO.type}
        </if>
        <if test="reqVO.clockDate != null">
            AND DATE(ecii."create_time") = #{reqVO.clockDate}
        </if>
        <if test="reqVO.mealPeriod != null">
            AND ecii."meal_period" = #{reqVO.mealPeriod}
        </if>
        ORDER BY
        <if test="reqVO.clockInType != null">
            <if test="reqVO.clockInType == 0">
                <if test="reqVO.traineeStatus == 1 or reqVO.traineeStatus == 2">
                    ecii."clocking_time" desc
                </if>
                <if test="reqVO.traineeStatus == 0 or reqVO.traineeStatus == 3">
                    et."name" asc
                </if>
            </if>
            <if test="reqVO.clockInType == 1">
                <if test="reqVO.traineeStatus == 1 ">
                    ecii."clocking_time" desc
                </if>
                <if test="reqVO.traineeStatus != 1 ">
                    et."name" asc
                </if>
            </if>
            <if test="reqVO.clockInType == 2">
                <if test="reqVO.traineeStatus == 1 ">
                    ecii."clocking_time" desc
                </if>
                <if test="reqVO.traineeStatus != 1 ">
                    et."name" asc
                </if>
            </if>
        </if>
    </select>
    <select id="selectListByTraineeId"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.CourseVO">
        select
        ecii.id,
        ecii.class_course_id ,
        ecii."type" ,
        ecii.meal_period ,
        ecii.trainee_status ,
        ec."name",
        ecc."period" ,ecc.begin_time
        from
        edu_clock_in_info ecii
        left join edu_class_course ecc on
        ecii.class_course_id = ecc.id
        left join edu_courses ec on ecc.course_id = ec.id
        where
            ecii.deleted = 0
        <if test="reqVO.traineeId != null">
            and ecii.trainee_id = #{reqVO.traineeId}
        </if>
        <if test="reqVO.date != null and reqVO.date != ''">
            and ecii.create_time >= concat(#{reqVO.date},' 00:00:00')
            and ecii.create_time &lt;= concat(#{reqVO.date},' 23:59:59')
        </if>
    </select>
    <select id="selectListByTraineeId1"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.CourseVO">
        select
        ecii.id,
        ecii.class_course_id ,
        ecii."type" ,
        ecii.meal_period ,
        ecii.trainee_status ,
        ec."name",
        ecc."period" ,ecc.begin_time
        from
        edu_clock_in_info ecii
        left join edu_class_course ecc on
        ecii.class_course_id = ecc.id
        left join edu_courses ec on ecc.course_id = ec.id
        where
        ecii.deleted = 0
          and ecii.class_course_id is not null
        <if test="reqVO.traineeId != null">
            and ecii.trainee_id = #{reqVO.traineeId}
        </if>
        <if test="reqVO.date != null and reqVO.date != ''">
            and ecii.date >= concat(#{reqVO.date},' 00:00:00')
            and ecii.date &lt;= concat(#{reqVO.date},' 23:59:59')
        </if>
    </select>
    <select id="selectClockInList" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoReturnVO">
        SELECT
        *
        FROM
        (
        SELECT
        ecii."class_id",
        COUNT(DISTINCT ecii."trainee_id") AS trainee_count,
        -- 迟到人数
        COUNT(CASE WHEN ecii."trainee_status" = 2 THEN 1 END) AS latecomers_count,
        --- 请假人数
        COUNT(CASE WHEN ecii."trainee_status" = 3 THEN 1 END) AS vacate_count,
        -- 未到人数
        COUNT(CASE WHEN ecii."trainee_status" = 0 THEN 1 END) AS no_show_count,
        -- 实到人数
        COUNT(CASE WHEN ecii."trainee_status" = 1 THEN 1 END) + COUNT(CASE WHEN ecii."trainee_status" = 2 THEN 1 END) AS attendance_count,
        ecc."date" AS course_date,
        ec."name" AS course_name,
        ecc."begin_time" AS course_begin_time,
        ecc."period" AS course_period,
        ecc."id" AS course_id
        FROM
        "edu_clock_in_info" ecii
        LEFT JOIN "edu_class_course" ecc ON
        ecii."class_course_id" = ecc."id"
        AND ecc."deleted" = '0'
        LEFT JOIN "edu_courses" ec ON
        ec."id" = ecc."course_id"
        AND ec."deleted" = '0'
        WHERE
        ecii."type" = #{reqVO.type}
        and
        ecii."deleted" = '0'
        <if test="reqVO.endTime != null">
            and DATE(ecc."begin_time") &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.startTime != null">
            and DATE(ecc."begin_time") >= #{reqVO.startTime}
        </if>
        <if test="reqVO.classId != null">
            and ecii."class_id" = #{reqVO.classId}
        </if>
        GROUP BY
        ecii."class_id",
        ecc."id",
        ecc."date",
        ec."name",
        ecc."begin_time",
        ecc."period",
        ecc."id"
        ORDER BY
        ecii."class_id",
        ecc."begin_time"
        ) AS t
        ORDER BY
        t.course_begin_time
    </select>
    <select id="selectListMealAttendance" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoReturnVO">
        SELECT
        *
        FROM
        (
        SELECT
        ecii."class_id",
        COUNT(DISTINCT ecii."trainee_id") AS trainee_count,
        --- 请假人数
        COUNT(CASE WHEN ecii."trainee_status" = 3 THEN 1 END) AS vacate_count,
        -- 未到人数
        COUNT(CASE WHEN ecii."trainee_status" = 0 THEN 1 END) AS no_show_count,
        -- 实到人数
        COUNT(CASE WHEN ecii."trainee_status" = 1 THEN 1 END) AS attendance_count,
        ecii ."meal_period" ,
        DATE(ecii."create_time") AS clock_date
        FROM
        "edu_clock_in_info" ecii
        WHERE
        ecii."type" = #{reqVO.type}
        and
        ecii."deleted" = '0'
        <if test="reqVO.classId != null">
            and ecii."class_id" = #{reqVO.classId}
        </if>
        <if test="reqVO.endTime != null">
            and DATE(ecii."create_time") &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.startTime != null">
            and DATE(ecii."create_time") >= #{reqVO.startTime}
        </if>
        GROUP BY
        ecii."class_id",
        ecii ."meal_period" ,
        DATE(ecii."create_time")
        ORDER BY
        ecii."class_id",
        ecii ."meal_period"
        ) AS t
        ORDER BY
        t.clock_date,
        t.meal_period
    </select>
    <select id="selectListCheckIn" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoReturnVO">
        SELECT
        *
        FROM
        (
        SELECT
        ecii."class_id",
        COUNT(DISTINCT ecii."trainee_id") AS trainee_count,
        --- 请假人数
        COUNT(CASE WHEN ecii."trainee_status" = 3 THEN 1 END) AS vacate_count,
        -- 未到人数
        COUNT(CASE WHEN ecii."trainee_status" = 0 THEN 1 END) AS no_show_count,
        -- 实到人数
        COUNT(CASE WHEN ecii."trainee_status" = 1 THEN 1 END) AS attendance_count,
        DATE(ecii."create_time") AS clock_date
        FROM
        "edu_clock_in_info" ecii
        WHERE
        ecii."type" = #{reqVO.type}
        and
        ecii."deleted" = '0'
        <if test="reqVO.endTime != null">
            and DATE(ecii."create_time") &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.startTime != null">
            and DATE(ecii."create_time") >= #{reqVO.startTime}
        </if>
        <if test="reqVO.classId != null">
            and ecii."class_id" = #{reqVO.classId}
        </if>
        GROUP BY
        ecii."class_id",
        DATE(ecii."create_time")
        ORDER BY
        ecii."class_id"
        ) AS t
        ORDER BY
        t.clock_date
    </select>
    <select id="getAbnormalAttendanceDateList" resultType="java.time.LocalDate">
        select
            ecii."date"
        from edu_clock_in_info ecii
        where ecii.deleted = 0
            and ecii.trainee_status = 0
            and ecii."date" &gt;= #{monthStart}
            and ecii."date" &lt; #{monthEnd}
            and ecii.class_id = #{classId}
        group by ecii."date"
    </select>

    <select id="selectAttendanceRateForYZ"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceDetailsVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterReqVO">
        SELECT
            e."type",
            count(1) as attendanceExpected,
            count(e.trainee_status IN (1, 2) or null) AS attendanceActual,
            count(e.trainee_status = 0 or null) as attendanceAbsent,
            count(e.trainee_status = 3 or null) AS attendanceLeave
        from
            edu_clock_in_info e
        join edu_class_management ecm on ecm.id = e.class_id
                                             and ecm.deleted = 0
        where
            e.deleted = 0
            <if test="reqVO.classTerm != null">
                and ecm.semester = #{reqVO.classTerm}
            </if>
            <if test="reqVO.classId != null">
                and e.class_id = #{reqVO.classId}
            </if>
            <if test="reqVO.startTime != null">
                AND e."date" &gt;= #{reqVO.startTime}
            </if>
            <if test="reqVO.endTime != null">
                AND e."date" &lt;= #{reqVO.endTime}
            </if>
            <if test="reqVO.tenantId != null">
                and e.tenant_id = #{reqVO.tenantId}
            </if>
            and (e.check_end_time is null or
            e.check_end_time &lt;= #{currentTime})
        GROUP BY
        e."type"
    </select>

    <select id="getClassDetails"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.AppAttendanceDetailsRespVO">
        SELECT
            ecc.id classCourseId,
            ecc.period coursePeriod,
            ecc.begin_time classStartTime,
            count(1) as attendanceExpected,
            count(ecii.trainee_status IN (1, 2) or null) AS attendanceActual,
            count(ecii.trainee_status = 0 or null) as attendanceAbsent,
            count(ecii.trainee_status = 3 or null) AS attendanceLeave
        from edu_class_course ecc
            join edu_clock_in_info ecii on ecc.id = ecii.class_course_id and ecii.deleted = 0
        where ecc.deleted = 0
          and ecii.class_id  = #{classId}
          and ecii."type" = 0
          and ecii."date" = #{date}
        group by ecc.id
        order by ecc.begin_time
    </select>

    <select id="getMealDetails"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.AppAttendanceDetailsRespVO">
        SELECT
            ecii.meal_period mealPeriod,
            count(1) as attendanceExpected,
            count(ecii.trainee_status IN (1, 2) or null) AS attendanceActual,
            count(ecii.trainee_status = 0 or null) as attendanceAbsent,
            count(ecii.trainee_status = 3 or null) AS attendanceLeave
        from edu_clock_in_info ecii
        where ecii.deleted = 0
          and ecii.class_id  = #{classId}
          and ecii."type" = 1
          and ecii."date" = #{date}
        group by ecii.meal_period
        order by ecii.meal_period
    </select>

    <select id="getAccommodationDetails"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.AppAttendanceDetailsRespVO">
        SELECT
            ecii."date",
            count(1) as attendanceExpected,
            count(ecii.trainee_status IN (1, 2) or null) AS attendanceActual,
            count(ecii.trainee_status = 0 or null) as attendanceAbsent,
            count(ecii.trainee_status = 3 or null) AS attendanceLeave
        from edu_clock_in_info ecii
        where ecii.deleted = 0
          and ecii.class_id  = #{classId}
          and ecii."type" = 2
          and ecii."date" = #{date}
        group by ecii."date"
        order by ecii."date"
    </select>

    <select id="getTraineeAttendanceDetails"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.AppTraineeAttendanceDetailsRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.AppTraineeAttendanceDetailsReqVO">
        select
            distinct ecii.id recordId,
            ecii.class_id classId,
            ecii.trainee_id traineeId,
            ecii.class_course_id ,
            ecii."type" type,
            ecii.meal_period mealPeriod,
            ecii.trainee_status traineeStatus,
            ecii.clocking_time clockingTime,
            ecii."date" ,
            ecii.leave_type leaveType,
            etg.group_name groupName ,
            et."name" traineeName,
            et.sex,
            et.phone,
            et."position",
            et.class_committee_id,
            ecc.class_committee_name classCommitteeName,
            etg.sort,
            et.group_sort,
            et.photo avatar
            <if test="reqVO.traineeStatus == 3 ">
               ,etl.status leaveStatus
            </if>
        from edu_clock_in_info ecii
             join edu_trainee et on et.id = ecii.trainee_id and et.deleted = 0
             left join edu_class_committee ecc on ecc.id  = et.class_committee_id and ecc.deleted = 0
             left join edu_trainee_group etg on et.group_id = etg.id and etg.deleted = 0
             <if test="reqVO.traineeStatus == 3 ">
                 left join edu_trainee_leave etl on etl.trainee_id = ecii.trainee_id and etl.deleted = 0
             </if>
        where ecii.deleted = 0
          and ecii."type" = #{reqVO.type}
          and ecii."date" = #{reqVO.date}
          and ecii.class_id = #{reqVO.classId}
          <if test="reqVO.traineeStatus != null">
            and ecii.trainee_status = #{reqVO.traineeStatus}
         </if>
          <if test="reqVO.traineeStatus == 3 and reqVO.leaveStatus != null">
            and etl.status = #{reqVO.leaveStatus}
         </if>
         <if test="reqVO.mealPeriod != null">
            and ecii.meal_period = #{reqVO.mealPeriod}
         </if>
         <if test="reqVO.classCourseId != null">
            and ecii.class_course_id = #{reqVO.classCourseId}
         </if>
        order by etg.sort, et.group_sort
    </select>
    <select id="selectErrorClock"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.CourseVO">
        select
        ecii.id,
        ecii.class_course_id ,
        ecii."type" ,
        ecii.meal_period ,
        ecii.trainee_status ,
        ec."name",
        ecc."period" ,
        ecc.begin_time,
        ecii.create_time
        from
        edu_clock_in_info ecii
        left join edu_class_course ecc on
        ecii.class_course_id = ecc.id
        left join edu_courses ec on ecc.course_id = ec.id
        where
        ecii.deleted = 0
        and ecii.trainee_status in (0,2)
        <if test="reqVO.traineeId != null">
            and ecii.trainee_id = #{reqVO.traineeId}
        </if>
        <if test="reqVO.dateBeg != null and reqVO.dateBeg != ''">
            and ecii.create_time >= concat(#{reqVO.dateBeg},' 00:00:00')
        </if>
        <if test="reqVO.dateEnd != null and reqVO.dateEnd != ''">
            and ecii.create_time &lt;= concat(#{reqVO.dateEnd},' 23:59:59')
        </if>
    </select>
    <select id="selectListByTraineeIdAndLeaveTime"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO">
        select
            id,
            class_id,
            trainee_id,
            class_course_id,
            "type",
            meal_period,
            trainee_status,
            leave_type
        from edu_clock_in_info
        where deleted = 0
          and trainee_id = #{traineeId}
          and check_begin_time &gt;= #{beginTime}
          and check_begin_time &lt;= #{endTime}
    </select>
    <select id="getAttendanceCheckTime"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.AttendanceCheckTime">
        select before_class_time as beforeClassMinute,
               after_class_time as afterClassMinute
        FROM edu_class_clock_in ecci
        LEFT JOIN edu_rule_template ert on ecci.attendance_check = ert.id
        where ecci.deleted = 0 and ert.deleted = 0 and ecci.class_id = #{classId}
    </select>
    <select id="getTraineeStatus" resultType="java.lang.Integer">
        select eci.trainee_status from edu_clock_in_info eci where eci.deleted = false and eci.trainee_id = #{traineeId} and eci.class_course_id = #{classCourseId}
    </select>
    <select id="getLateInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO">
        SELECT
        e.trainee_id,
        e."type",
        COUNT(1) AS attendanceExpected,
        COUNT(e.trainee_status IN (2) or NULL) AS attendanceActual,
        COUNT(e.trainee_status = 3 or NULL) AS attendanceLeave,
        <choose>
            <when test="ruleType == 0">
                attendanceActual/COALESCE(NULLIF(attendanceExpected - attendanceLeave, 0), 1)
            </when>
            <otherwise>
                -- 处理默认情况的逻辑
                attendanceActual/COALESCE(NULLIF(attendanceExpected, 0), 1)
            </otherwise>
        </choose>
        as rate
        from
        edu_clock_in_info e
        where
        e.deleted = 0
        and e.class_id = #{reqVO.classId}
        <if test="reqVO.startTime != null">
            AND e."date" &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            AND e."date" &lt;= #{reqVO.endTime}
        </if>
        GROUP BY
        e.trainee_id, e."type"
    </select>
    <select id="selectUnchecked" resultType="java.lang.Long">
        select trainee_id
        from edu_clock_in_info
        where deleted = 0
          and class_course_id = #{classCourseId}
          and type = 0
          and trainee_status = 0
    </select>
    <select id="selectChecked" resultType="java.lang.Long" parameterType="java.lang.Long">
        select trainee_id
        from edu_clock_in_info
        where deleted = 0
          and class_course_id = #{classCourseId}
          and type = 0
          and (trainee_status = 1 or trainee_status = 2)
    </select>
    <select id="isEnableAttendanceProtection" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select enable_attendance_protection
        from system_tenant
        where id = #{tenantId}
        and deleted = 0
    </select>

    <select id="getClassAttendanceDetailsPage" resultType="com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClassAttendanceDetailRespVO">
        SELECT
            ecii.id,
            ecii.trainee_id,
            et."name" as traineeName,
            ecii.trainee_status as status,
            ecii.clocking_time as attendanceTime,
            ecm.id as classId,
            ecm.class_name as className
        FROM
            edu_clock_in_info ecii
        JOIN
            edu_trainee et ON ecii.trainee_id = et.id AND et.deleted = 0
        JOIN
            edu_class_management ecm ON ecii.class_id = ecm.id AND ecm.deleted = 0
        WHERE
            ecii.deleted = 0
            <if test="pageVO.classId != null">
                AND ecii.class_id = #{pageVO.classId}
            </if>
            <if test="pageVO.traineeName != null and pageVO.traineeName != ''">
                AND et."name" LIKE CONCAT('%', #{pageVO.traineeName}, '%')
            </if>
            <if test="pageVO.status != null and pageVO.clockInType == null">
                AND ecii.trainee_status = #{pageVO.status}
            </if>
            <if test="pageVO.attendanceDate != null and pageVO.attendanceDate != ''">
                AND CAST(ecii."date" AS VARCHAR) = #{pageVO.attendanceDate}
            </if>
            <if test="pageVO.classCourseId != null">
                AND ecii.class_course_id = #{pageVO.classCourseId}
            </if>
            <if test="pageVO.type != null">
                AND ecii.type = #{pageVO.type}
            </if>
            <if test="pageVO.mealPeriod != null">
                AND ecii.meal_period = #{pageVO.mealPeriod}
            </if>
            <if test="pageVO.clockInType != null">
                <if test="pageVO.clockInType == 0">
                    <if test="pageVO.status != null">
                        <if test="pageVO.status == 1">
                            AND (ecii.trainee_status = 1 OR ecii.trainee_status = 2)
                        </if>
                        <if test="pageVO.status != 1">
                            AND ecii.trainee_status = #{pageVO.status}
                        </if>
                    </if>
                </if>
                <if test="pageVO.clockInType == 1">
                    <if test="pageVO.status != null">
                        AND ecii.trainee_status = #{pageVO.status}
                    </if>
                </if>
                <if test="pageVO.clockInType == 2">
                    <if test="pageVO.status != null">
                        AND ecii.trainee_status = #{pageVO.status}
                    </if>
                </if>
            </if>
        ORDER BY
            ecii.clocking_time DESC
    </select>
</mapper>
