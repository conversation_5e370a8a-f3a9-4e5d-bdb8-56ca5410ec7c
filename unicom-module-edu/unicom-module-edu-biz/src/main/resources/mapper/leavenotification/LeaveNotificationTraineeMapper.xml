<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.leavenotification.LeaveNotificationTraineeMapper">

    <resultMap id="BaseResultMap" type="com.unicom.swdx.module.edu.dal.dataobject.leavenotification.LeaveNotificationTraineeDO">
        <id column="id" property="id"/>
        <result column="notification_id" property="notificationId"/>
        <result column="trainee_id" property="traineeId"/>
        <result column="trainee_name" property="traineeName"/>
        <result column="trainee_mobile" property="traineeMobile"/>
        <result column="mobile_masked" property="mobileMasked"/>
        <result column="status" property="status"/>
        <result column="fill_time" property="fillTime"/>
        <result column="leave_reason" property="leaveReason"/>
        <result column="leave_address" property="leaveAddress"/>
        <result column="transportation" property="transportation"/>
        <result column="expected_return_time" property="expectedReturnTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

</mapper> 