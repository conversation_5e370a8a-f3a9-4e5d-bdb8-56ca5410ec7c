server:
  port: 48097
spring:
  profiles:
    active: @package.environment@
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务
  autoconfigure:
    exclude:
      # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean
  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

# 工作流 Flowable 配置
flowable:
  async-executor-activate: false
  # 1. false: 默认值，Flowable 启动时，对比数据库表中保存的版本，如果不匹配。将抛出异常
  # 2. true: 启动时会对数据库中所有表进行更新操作，如果表存在，不做处理，反之，自动创建表
  # 3. create_drop: 启动时自动创建表，关闭时自动删除表
  # 4. drop_create: 启动时，删除旧表，再创建新表
  database-schema-update: false # 设置为 false，可通过 https://github.com/flowable/flowable-sql 初始化
  #  db-history-used: true # flowable6 默认 true 生成信息表，无需手动设置
  #  check-process-definitions: false # 设置为 false，禁用 /resources/processes 自动部署 BPMN XML 流程
  history-level: full # full：保存历史数据的最高级别，可保存全部流程相关细节，包括流程流转各节点参数

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    # 本地环境开启mybatis-plus的sql打印
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  type-aliases-package: ${unicom.info.base-package}.dal.dataobject

--- #################### RPC 远程调用相关配置 ####################
dubbo:
  scan:
    base-packages: ${unicom.info.base-package}.api # 指定 Dubbo 服务实现类的扫描基准包
  protocol:
    name: dubbo # 协议名称
    port: -1 # 协议端口，-1 表示自增端口，从 20880 开始
  registry:
    address: spring-cloud://localhost # 设置使用 Spring Cloud 注册中心

--- #################### 定时任务相关配置 ####################

xxl:
  job:
    executor:
      appname: ${spring.application.name} # 执行器 AppName
      logpath: ${user.home}/logs/xxl-job/${spring.application.name} # 执行器运行日志文件存储磁盘路径
    accessToken: default_token # 执行器通讯TOKEN

--- #################### sk相关配置 ####################

unicom:
  info:
    version: 1.0.0
    base-package: com.unicom.swdx.module.edu
  web:
    admin-ui:
      url: http://localhost:48080/admin-api # 管理后台 UI 的地址
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${unicom.info.version}
    base-package: ${unicom.info.base-package}
  error-code: # 错误码相关配置项
    constants-class-list:
      - com.unicom.swdx.module.edu.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    enable: true
    ignore-tables:
      - bpm_process_instance_ext
      - bpm_task_ext
      - system_user_role
      - oa_central_task_operation
      - system_user_post
      - hr_personnel_basic_information
      - hr_personnel_position
      - system_user_dept
      - system_dict_data
      - edu_holiday
      - edu_low_permission
      - system_tenant
      - edu_evaluation_history  # 历史评价数据表，跨租户共享
debug: false
