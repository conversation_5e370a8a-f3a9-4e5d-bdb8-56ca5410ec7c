package com.unicom.swdx.module.bpm.enums.definition;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum AffairHandleTaskNameCodeEnum {


    SHOOTING_COMPETITION_DISTRIBUTE("Activity_1y4tska","首席审批员分发","督办","分发中"),
    SHOOTING_COMPETITION_CHECK_1("Activity_1utbgjp","竞技体育与科技处初审","督办","审批中"),
    SHOOTING_COMPETITION_CHECK_2("Activity_0dnbo3s","竞技体育与科技处复核","督办","审批中"),
    SHOOTING_COMPETITION_HANDLE("Activity_02ec047", "分管领导审批","提醒","审批中"),
    SHOOTING_COMPETITION_NOTICE("Activity_1aa1vqo","办理","督办","办理中"),
    SHOOTING_COMPETITION_FEEDBACK("Activity_1w6xfg7","首席审批员反馈","督办","反馈中"), // 从事射击竞技体育运动单位审批


    TEMP_OCCUPY_FACILITY_DISTRIBUTE("Activity_0jfp33s","首席审批员分发","督办","分发中"),
    TEMP_OCCUPY_FACILITY_CHECK_1("Activity_172g1yt","体育经济处初审","督办","审批中"),
    TEMP_OCCUPY_FACILITY_CHECK_2("Activity_0wkqq67","体育经济处复核","督办","审批中"),
    TEMP_OCCUPY_FACILITY_APPROVE("Activity_0x7khyw","分管领导审批","提醒","审批中"),
    TEMP_OCCUPY_FACILITY_DELIBERATE("Activity_1r16v2d","党局组审议","督办","审批中"),
    TEMP_OCCUPY_FACILITY_HANDLE("Activity_1dy1gae", "办理","督办","办理中"),
    TEMP_OCCUPY_FACILITY_FEEDBACK("Activity_1gkjp0e","首席审批员反馈","督办","反馈中"),// 临时占用公共体育场（馆）设施审批



    FIRST_LEVEL_ATHLETE_DISTRIBUTE("Activity_1xhzx50","首席审批员分发","督办","分发中"),
    FIRST_LEVEL_ATHLETE_CHECK_1("Activity_0e1ncld", "竞技体育与科技处初审","督办","审批中"),
    FIRST_LEVEL_ATHLETE_CHECK_2("Activity_1mrid8w", "竞技体育与科技处复核","督办","审批中"),
    FIRST_LEVEL_ATHLETE_APPROVE("Activity_1vlk4f5", "分管领导审批","提醒","审批中"),
    FIRST_LEVEL_ATHLETE_HANDLE("Activity_1qdj6vw", "办理","督办","办理中"),
    FIRST_LEVEL_ATHLETE_FEEDBACK("Activity_0zrj3oq","首席审批员反馈","督办","反馈中"),//一级运动员等级称号授予



    LEVEL_SPORTS_INSTRUCTOR_DISTRIBUTE("Activity_0nb420o","首席审批员分发","督办","分发中"),
    LEVEL_SPORTS_INSTRUCTOR_CHECK_1("Activity_1ht8gta", "群众体育处初审","督办","审批中"),
    LEVEL_SPORTS_INSTRUCTOR_CHECK_2("Activity_0obawxi", "群众体育处复核","督办","审批中"),
    LEVEL_SPORTS_INSTRUCTOR_SIGN("Activity_0m65x2w", "分管领导签发","提醒","审批中"),
    LEVEL_SPORTS_INSTRUCTOR_HANDLE("Activity_1j0hlg2", "办理","督办","办理中"),
    LEVEL_SPORTS_INSTRUCTOR_FEEDBACK("Activity_0cz1ifn","首席审批员反馈","督办","反馈中"),//一级社会体育指导员技术等级称号授予



    TALENT_REVERVE_BASE_DISTRIBUTE("Activity_1u82py8","首席审批员分发","督办","分发中"),
    TALENT_REVERVE_BASE_CHECK("Activity_1bguupq", "青少年体育处初审","督办","审批中"),
    TALENT_REVERVE_BASE_APPROVE("Activity_0pa5vz4", "局领导审批","提醒","审批中"),
    TALENT_REVERVE_BASE_HANDLE("Activity_0e0yyfg", "办理","督办","办理中"),
    TALENT_REVERVE_BASE_FEEDBACK("Activity_0odthue","首席审批员反馈","督办","反馈中"),//创建省高水平体育后备人才基地


    //SCHOOLS_APPROVAL_NAMING_HANDLE("Activity_0wrr8k6", "办理"),  // 省级体育传统项目学校的审定和命名

    SCHOOLS_APPROVAL_NAMING_DISTRIBUTE("Activity_1tlgekb","首席审批员分发","督办","分发中"),
    SCHOOLS_APPROVAL_NAMING_CHECK_1("Activity_0l0ohjx", "青少处承办人初审","督办","审批中"),
    SCHOOLS_APPROVAL_NAMING_CHECK_2("Activity_0i4wol7", "专家组审批","督办","审批中"),
    SCHOOLS_APPROVAL_NAMING_HANDLE("Activity_0wrr8k6", "办理","督办","办理中"),
    SCHOOLS_APPROVAL_NAMING_FEEDBACK("Activity_18vae9c","首席审批员反馈","督办","反馈中"),
    SCHOOLS_APPROVAL_NAMING_PUBLICITY("Activity_0wsavig", "公示","督办","办理中"),  // 省级体育传统项目学校的审定和命名



    NATIONAL_FITNESS_REWARD_DISTRIBUTE("Activity_1h4uz56","首席审批员分发","督办","分发中"),
    NATIONAL_FITNESS_REWARD_CHECK("Activity_194u4ay", "初审","督办","审批中"),
    NATIONAL_FITNESS_REWARD_APPROVE("Activity_1pl2yg5", "复核","督办","审批中"),
    NATIONAL_FITNESS_REWARD_HANDLE("Activity_0p9pdbr", "审批","督办","审批中"),
    NATIONAL_FITNESS_REWARD_HANDLE2("Activity_023knov", "办理","督办","办理中"),
    NATIONAL_FITNESS_REWARD_FEEDBACK("Activity_0uri7c3","首席审批员反馈","督办","反馈中"),// 全民健身事业发展奖励


    NATIONAL_SPORTS_REWARD_DISTRIBUTE("Activity_0ego1qt","首席审批员分发","督办","分发中"),
    NATIONAL_SPORTS_REWARD_CHECK("Activity_077eqzl", "初审","督办","审批中"),
    NATIONAL_SPORTS_REWARD_APPROVE("Activity_1k2advm", "复核","督办","审批中"),
    NATIONAL_SPORTS_REWARD_HANDLE("Activity_0s9rx5n", "审批","督办","审批中"),
    NATIONAL_SPORTS_REWARD_HANDLE2("Activity_0abgft9", "办理","督办","办理中"),
    NATIONAL_SPORTS_REWARD_FEEDBACK("Activity_1ym975a","首席审批员反馈","督办","反馈中"),//全国体育事业发展奖励


    NON_ENTERPRISE_REGISTER_DISTRIBUTE("Activity_0whnlvl","首席审批员分发","督办","分发中"),
    NON_ENTERPRISE_REGISTER_CHECK("Activity_0kbxzj4", "体育经济处初审","督办","审批中"),
    NON_ENTERPRISE_REGISTER_APPROVE("Activity_00mxc52", "局领导审批","提醒","审批中"),
    NON_ENTERPRISE_REGISTER_HANDLE("Activity_1bzt6we", "办理","督办","办理中"),
    NON_ENTERPRISE_REGISTER_FEEDBACK("Activity_1tazfdl","首席审批员反馈","督办","反馈中");// 体育类民办非企业单位申请登记审查


    private final String code;
    private final String name;
    private final String superviseType;
    private final String statusName;


    public static List<String> getAllHandleTaskCode(){
        List<String> res = new ArrayList<>();
        for (AffairHandleTaskNameCodeEnum e:AffairHandleTaskNameCodeEnum.values()){
            res.add(e.getCode());
        }
        return res;
    }

    /**
     * 根据code获取枚举类
     *
     * */
    public static AffairHandleTaskNameCodeEnum getByCode(String code) {
        return ArrayUtil.firstMatch(affairHandleTaskNameCodeEnum -> affairHandleTaskNameCodeEnum.getCode().equals(code),
                values());
    }

    public static List<String> getHandleSet()
    {
        return ArrayUtil.map(ArrayUtil.filter(values(),v -> v.getStatusName().equals("办理中")),a -> a.getCode());
    }

    public static List<String> getCheckSet()
    {
        return ArrayUtil.map(ArrayUtil.filter(values(),v -> v.getStatusName().equals("审批中")),a -> a.getCode());
    }

    public static List<String> getDistributeSet()
    {
        return ArrayUtil.map(ArrayUtil.filter(values(),v -> v.getStatusName().equals("分发中")),a -> a.getCode());
    }

    public static List<String> getFeedbackSet()
    {
        return ArrayUtil.map(ArrayUtil.filter(values(),v -> v.getStatusName().equals("反馈中")),a -> a.getCode());
    }
}
