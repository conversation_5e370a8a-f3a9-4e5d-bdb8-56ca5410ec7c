package com.unicom.swdx.module.bpm.enums.definition;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 顺序流程枚举
 */
@Getter
@AllArgsConstructor
public enum BpmSeqTaskEnum {
    WEB_LEADER_SIGN_SEQ("Activity_0q1apjf", "分管领导签发","签发"),//顺序
    WEB_MAIN_SIGN_SEQ("Activity_0o8pmjv", "主要领导签发","签发"), // 顺序
    REQ_PROPOSE_1("Activity_0fcj4ni", "顺序拟办","顺序拟办"),
    REQ_INSTRUCT_1("Activity_1mtqwvk", "顺序批示","顺序批示"),
    MEETING_APPLY_LEADER_APPROVE_1("Activity_105xntq", "领导顺序审批","顺序审批");
    /**
     * 节点编号
     */
    private final String code;

    /**
     * 节点全称
     */
    private final String name;

    /**
     * 节点简称
     */
    private final String taskShortName;

    public static boolean contains(String key) {
       return Arrays.stream(BpmSeqTaskEnum.values()).map(BpmSeqTaskEnum::getCode).collect(Collectors.toList()).contains(key);
    }
}
