package com.unicom.swdx.module.bpm.enums.definition.appQuery;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模版sql include名称枚举
 */
@Getter
@AllArgsConstructor
public enum SqlInclusionEnum {
    RECEIVING_INCLUSION("receiving","收文 SQL include",true),
    SENDING_INCLUSION("sending","发文 SQL include",true),
    AFFAIR_HANDLE_INCLUSION( "affairHandle", "政务办理 SQL include",true),
    APPROVAL_CENTER_INCLUSION( "approvalCenter", "审批中心 SQL include",true),
    KEY_WORK_INCLUSION("keyWork","重点工作 SQL include",true),
    PEOPLE_INTERACTION_INCLUSION("peopleInteraction","政民互动 SQL include",true);


    /**
     * sql include模板标签名称
     */
    final String inclusionName;

    /**
     * 描述
     */
    final String description;

    /**
     * 默认的激活状态
     */
    final Boolean isActive;

    /**
     * 根据sqlName获得对应的枚举类
     * @param name 模版sql约束名
     * @return SqlTemplateNemConstants
     */
    public static SqlInclusionEnum getByName(String name){
        for (SqlInclusionEnum e: SqlInclusionEnum.values()) {
            if (e.inclusionName.equals(name)) {
                return e;
            }
            else if ("affair-sending-flow".equals(name)){
                return SENDING_INCLUSION;
            }
            else if ("affair-receiving-flow".equals(name)){
                return RECEIVING_INCLUSION;
            }
        }
        return null;
    }

    /**
     * 根据sqlName获得对应的枚举类
     * @param typeTagEnum 审批类型
     * @return SqlTemplateNemConstants
     */
    public static SqlInclusionEnum getByApprovalTypeTag(ApprovalTypeTagEnum typeTagEnum){
        switch (typeTagEnum){
            case AFFAIR_HANDLE: return AFFAIR_HANDLE_INCLUSION;
            case COMPREHENSIVE_AFFAIRS: return APPROVAL_CENTER_INCLUSION;
            case SENDING: return SENDING_INCLUSION;
            case RECEIVING: return RECEIVING_INCLUSION;
            case KEY_WORK: return KEY_WORK_INCLUSION;
            case PEOPLE_INTERACTION: return PEOPLE_INTERACTION_INCLUSION;
            default:{
                return null;
            }
        }
    }
}
