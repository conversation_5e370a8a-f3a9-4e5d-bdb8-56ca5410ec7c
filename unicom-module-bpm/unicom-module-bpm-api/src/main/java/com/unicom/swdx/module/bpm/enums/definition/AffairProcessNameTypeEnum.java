package com.unicom.swdx.module.bpm.enums.definition;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/15  13:47
 */
@Getter
@AllArgsConstructor
public enum AffairProcessNameTypeEnum {
    REQ_INSTRUNCT("affair_approval_req_instruct", "请示呈批","comprehensiveAffairs"),

    MEETING_RESERVE("affair_approval_meeting_reservation", "会议室预定","comprehensiveAffairs"),

    METTING_APPLY("affair_approval_meeting_application", "会议申请","comprehensiveAffairs"),

    VEHICLE("affair_approval_vehicle", "用车申请","comprehensiveAffairs"),

    SEAL("affair_approval_seal", "用章申请","comprehensiveAffairs"),

    MEAL("affair_approval_meal", "接待申请","comprehensiveAffairs"),

    OFFICE_SUPPLIES("affair_approval_office_supplies", "办公用品申领","comprehensiveAffairs"),

    CONTRACT_1("affair_approval_contract_1", "合同签审表","comprehensiveAffairs"),

    CONTRACT_2("affair_approval_contract_2", "合同签审表","comprehensiveAffairs"),

    DOCUMENTS("affair_approval_documents", "采购文件合法性审查","comprehensiveAffairs"),

    ABROAD("affair_approval_abroad", "公务出国审批","comprehensiveAffairs"),

    LEAVE("affair_approval_leave", "请假审批","comprehensiveAffairs"),

    WEB_POST("affair_approval_web_post", "网站发文","comprehensiveAffairs");

    private final String type;
    private final String desc;

    //别名
    private final String subject;

    public static AffairProcessNameTypeEnum getNameByType(String type) {
        return ArrayUtil.firstMatch(affairProcessNameTypeEnum -> affairProcessNameTypeEnum.getType().equals(type),
                values());
    }

    public static void main(String[] args) {
        getNameByType("affair_approval_leave");
        System.out.println(getNameByType("da"));
    }

    //返回综合事务的processKeys
    public static List<String> getComprehensiveAffairs(){
        List<String> res = new ArrayList<>();
        for (AffairProcessNameTypeEnum e:AffairProcessNameTypeEnum.values()){
            if (e.subject.equals("comprehensiveAffairs")){
                res.add(e.getType());
            }
        }
        return res;
    }

    public static Boolean isApprovalProcess(String processKey){
        for (AffairProcessNameTypeEnum e:AffairProcessNameTypeEnum.values()){
            if (e.type.equals(processKey)){
                return true;
            }
        }
        return false;
    }
}
