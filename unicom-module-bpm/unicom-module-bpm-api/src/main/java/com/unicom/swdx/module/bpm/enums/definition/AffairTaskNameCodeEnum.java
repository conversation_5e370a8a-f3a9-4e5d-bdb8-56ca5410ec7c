package com.unicom.swdx.module.bpm.enums.definition;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/31  11:10
 */
@Getter
@AllArgsConstructor
public enum AffairTaskNameCodeEnum {
    WEB_CHECK("Activity_0ufva29", "发稿部门主要负责人核稿","核稿"),  // 流程标识

    WEB_APPROVE("Activity_09mzi6z", "办公室审稿","审稿"),
    WEB_APPROVE_MAIN("Activity_08qpku8", "局办公室主任审批","审批"),
    WEB_HEAD_SIGN("Activity_0odq8et", "局办公室主任签发","签发"),

    WEB_LEADER_SIGN("Activity_140zivc", "分管领导签发","签发"),//会签

    WEB_LEADER_SIGN_SEQ("Activity_0q1apjf", "分管领导签发","签发"),//顺序
    WEB_MAIN_SIGN("Activity_1unwjd3", "主要领导签发","签发"),  // 会签
    WEB_MAIN_SIGN_SEQ("Activity_0o8pmjv", "主要领导签发","签发"), // 顺序

    WEB_MAIN_F("Activity_01a7g9r", "政策法规处审批","审批"),

    REQ_PROPOSE("Activity_0kbmzvw", "办公室主任拟办","办公室主任拟办"),    //旧的
    //REQ_INSTRUCT("Activity_1kejq02", "局领导批示"),    //旧的
    REQ_PROPOSE_1("Activity_0fcj4ni", "顺序拟办","顺序拟办"),
    REQ_PROPOSE_2("Activity_1tremt0", "会签拟办","会签拟办"),
    REQ_PROPOSE_SUM("Activity_1lx71id", "办公室主任汇总拟办意见","办公室主任汇总拟办意见"),
    REQ_INSTRUCT_1("Activity_1mtqwvk", "顺序批示","顺序批示"),
    REQ_INSTRUCT_2("Activity_09omecf", "会签批示","会签批示"),
    REQ_INSTRUCT_SUM("Activity_0hb65d9", "办公室主任汇总批示意见","办公室主任汇总批示意见"),



    MEETING_RESERVE_OFFICE_APPROVE("Activity_1xehuue","局办公室审批","审批"),

    MEETING_APPLY_OFFICE_APPROVE("Activity_0na770j", "办公室审批","审批"),
    MEETING_APPLY_APPLICANT_APPROVE("Activity_0x3vsfn", "申请人审批", "申请人审批"),
    MEETING_APPLY_LEADER_APPROVE_1("Activity_105xntq", "领导顺序审批","顺序审批"),
    MEETING_APPLY_LEADER_APPROVE_2("Activity_12fgfj8", "领导会签审批","会签审批"),
    //MEETING_APPLY_LEADER_APPROVE("Activity_1pzkmqz", "分管领导审批"),

    SEAL_APPROVE("Activity_0c1dak5", "用章审批","审批"),

    VEHICLE_OFFICE_APPROVE("Activity_1lf7pb2","局办公室审批", "审批"),
    VEHICLE_LEADER_APPROVE("Activity_0yx94kv","局分管负责人审批", "审批"),
    VEHICLE_MAIN_APPROVE("Activity_02hqvhi","局主要负责人审批", "审批"),
    //合同签审表1-经济处审批
    CONTRACT_1_ECON_APPROVE("Activity_0q8r449","经济处审批", "审批"),
    //合同签审表1-法规处审批
    CONTRACT_1_LAW_DEPT_APPROVE("Activity_0rbtoef","法规处审批", "审批"),
    //合同签审表1-分管领导审批
    CONTRACT_1_BRANCH_LEADER("Activity_139f05y","分管领导审批", "审批"),
    //合同签审表1-主要领导审批
    CONTRACT_1_MAIN_LEADER("Activity_0fdwbkk","主要领导审批", "审批"),
    //合同签审表2-经济处审批
    CONTRACT_2_ECON_APPROVE("Activity_1l79fzz","经济处审批", "审批"),
    //合同签审表2-分管领导审批
    CONTRACT_2_BRANCH_LEADER("Activity_12higev","分管领导审批", "审批"),
    //合同签审表2-主要领导审批
    CONTRACT_2_MAIN_LEADER("Activity_0f4wyna","主要领导审批", "审批");

    /**
     * 节点编号
     */
    private final String code;

    /**
     * 节点全称
     */
    private final String name;

    /**
     * 节点简称
     */
    private final String taskShortName;

    public static AffairTaskNameCodeEnum getEnumByCode(String taskCode) {
        return ArrayUtil.firstMatch(AffairTaskNameCodeEnum -> AffairTaskNameCodeEnum.getCode().equals(taskCode),
                values());
    }
}
