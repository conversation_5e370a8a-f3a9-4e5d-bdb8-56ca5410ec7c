package com.unicom.swdx.module.bpm.enums.definition.appQuery;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 审批流程类型筛选 标签
 */
@Getter
@AllArgsConstructor
public enum ApproveStatusEnum {

    ALL(null,"","全部"),
    APPROVING("1","approving","审批中"),
    HANDLING("2","handling","办理中"),
    APPROVED("3","approved","已同意"),
    REJECTED("4","reject","已驳回"),
    REVOKED("5","revoke","已撤销"),
    END("6","end","已结束");

    /**
     * 审批类型筛选标签
     */
    final String status;

    /**
     * 别名
     */
    final String alias;

    /**
     * 描述
     */
    final String description;

    /**
     * 根据页面请求传来的审批状态值获取审批状态enum
     * @param status 请求体的审批状态值
     * @return SqlTemplateNemConstants
     */
    public static ApproveStatusEnum getByReqStatus(String status){
        if (Objects.isNull(status) || status.length() == 0 || status.equals("0")){
            return ALL;
        }
        for (ApproveStatusEnum e: ApproveStatusEnum.values()) {
            if (ALL.equals(e)){
                continue;
            }
            if (e.status.equals(status) || e.alias.equals(status)) {
                return e;
            }
        }
        return null;
    }
}
