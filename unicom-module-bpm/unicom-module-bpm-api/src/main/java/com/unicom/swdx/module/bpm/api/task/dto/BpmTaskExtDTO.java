package com.unicom.swdx.module.bpm.api.task.dto;

import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/23  10:59
 */
@Data
@ApiModel("工作流任务扩展相关--请求参数")
public class BpmTaskExtDTO {
    /**
     * 编号，自增
     */
    private Long id;

    /**
     * 任务的审批人
     *
     * 冗余 Task 的 assignee 属性
     */
    private Long assigneeUserId;
    /**
     * 任务的名字
     *
     * 冗余 Task 的 name 属性，为了筛选
     */
    private String name;
    /**
     * 任务的编号
     *
     * 关联 Task 的 id 属性
     */
    private String taskId;
    /**
     * 任务的标识
     *
     */
    private String taskDefKey;
    /**
     * 任务的结果
     *
     * 枚举 {@link BpmProcessInstanceResultEnum}
     */
    private Integer result;
    /**
     * 审批建议
     */
    private String reason;
    /**
     * 任务的结束时间
     *
     * 冗余 HistoricTaskInstance 的 endTime  属性
     */
    private LocalDateTime endTime;

    /**
     * 流程实例的编号
     *
     * 关联 ProcessInstance 的 id 属性
     */
    private String processInstanceId;
    /**
     * 流程定义的编号
     *
     * 关联 ProcessDefinition 的 id 属性
     */
    private String processDefinitionId;

    /**
     * 办理日志参数
     *
     * 办理任务节点产生的日志信息
     */
    private String logParameters;
    /**
     * 参数map
     *
     * 办理任务节点产生的日志信息
     */
    private Map<String,Object> paramsMap;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 任务类型
     */
    private Integer taskType;

    private String imageUrl;

}
