package com.unicom.swdx.module.bpm.enums.definition;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SubSystemSecrectEnum {
    OA("OA","qZCT+Ix8"),
    J<PERSON>("JW","6R6HvLr!");

    //流程发起子系统
    private final String source;
    //凭证
    private final String secret;

    /**
     * 根据source查询secret
     * @param source
     * @return secret
     */
    public static String getSecretBySource(String source) {

        return ArrayUtil.firstMatch(subSystemSecrectEnum -> subSystemSecrectEnum.getSource().equals(source), values())
                .getSecret();
    }

}
