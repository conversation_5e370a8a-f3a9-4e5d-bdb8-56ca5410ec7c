package com.unicom.swdx.module.bpm.api.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.Map;

@ApiModel("管理后台 - 通过流程任务的 Request VO")
@Data
public class BpmTaskApproveReqDTO {

    @ApiModelProperty(value = "任务编号", required = true, example = "1024")
    @NotEmpty(message = "任务编号不能为空")
    private String id;

    @ApiModelProperty(value = "审批意见", example = "不错不错！")
    private String reason;

    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    @ApiModelProperty(value = "移动端签字")
    private String handSignature;

    /**
     * 审批过程中参数信息
     */
    @ApiModelProperty(value = "审批参数")
    private Map<String, Object> variables;

    @ApiModelProperty(value = "办理日志参数")
    private Map<String, Object> paramsMap;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    private LocalDateTime endTime;


}
