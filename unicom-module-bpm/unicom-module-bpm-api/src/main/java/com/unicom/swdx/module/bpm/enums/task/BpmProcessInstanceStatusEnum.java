package com.unicom.swdx.module.bpm.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmProcessInstanceStatusEnum {

    RUNNING(1, "审批中"),
    FINISH(2, "结束"),
    CANCEL(3,"已撤销"),
    REJECT(4,"驳回");


    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;

}
