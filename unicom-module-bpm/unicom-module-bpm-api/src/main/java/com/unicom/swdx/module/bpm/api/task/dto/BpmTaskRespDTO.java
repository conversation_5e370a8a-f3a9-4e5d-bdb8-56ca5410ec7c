package com.unicom.swdx.module.bpm.api.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("审批中心 办理日志 Response VO")
@Data
@ToString(callSuper = true)
public class BpmTaskRespDTO {
    @ApiModelProperty(value = "任务编号", example = "1024")
    private String id;

    @ApiModelProperty(value = "任务名字", example = "sk")
    private String name;

    @ApiModelProperty(value = "任务定义的标识", example = "user-001")
    private String definitionKey;

    @ApiModelProperty(value = "接收时间", required = true)
    private LocalDateTime claimTime;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "激活状态", example = "1", notes = "参见 SuspensionState 枚举")
    private Integer suspensionState;

    /**
     * 审核的用户信息
     */
    @ApiModelProperty(value = "审核的用户信息", required = true)
    private UserDTO assigneeUser;

    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "持续时间", example = "1000")
    private Long durationInMillis;

    @ApiModelProperty(value = "任务结果", notes = "参见 bpm_process_instance_result", example = "2")
    private Integer result;
    @ApiModelProperty(value = "审批建议", example = "不请假了！")
    private String reason;

    @ApiModelProperty(value = "驳回撤回状态", example = "1")
    private String revokeStatus;

    /**
     * 所属流程实例
     */
    @ApiModelProperty(value = "所属流程实例")
    private ProcessInstanceDTO processInstance;

    @ApiModelProperty(value = "流程分类", notes = "参见 bpm_model_category 数据字典", example = "1")
    private String category;

}
