package com.unicom.swdx.module.bpm.enums.definition;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/04/04  09:10
 */
@Getter
@AllArgsConstructor
public enum AffairHandleStartTaskCodeEnum {

    SHOOTING_COMPETITION_START("Activity_0k1yt84", "接受申请"),  // 从事射击竞技体育运动单位审批
    TEMP_OCCUPY_FACILITY_START("Activity_125daes", "接受申请"), // 临时占用公共体育场（馆）设施审批
    FIRST_LEVEL_ATHLETE_START("Activity_03fgp9e", "接受申请"), //一级运动员等级称号授予
    LEVEL_SPORTS_INSTRUCTOR_START("Activity_0vp49ge", "接受申请"), //一级社会体育指导员技术等级称号授予
    TALENT_REVERVE_BASE_START("Activity_1ceszai", "接受申请"), //创建省高水平体育后备人才基地
    SCHOOLS_APPROVAL_NAMING_START("Activity_1c6v9la", "接受申请"),  // 省级体育传统项目学校的审定和命名
    NATIONAL_FITNESS_REWARD_START("Activity_09u18hc", "接受申请"), // 全民健身事业发展奖励
    NATIONAL_SPORTS_REWARD_START("Activity_1wlpnv8", "接受申请"), //全国体育事业发展奖励
    NON_ENTERPRISE_REGISTER_START("Activity_0maps8u", "接受申请"); // 体育类民办非企业单位申请登记审查


    private final String code;
    private final String name;
}
