package com.unicom.swdx.module.bpm.api.task;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 流程任务实例 Service 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 任务")
public interface BpmTaskServiceApi {

    String PREFIX = ApiConstants.PREFIX + "/task";


    /**
     * 通过任务
     *
     * @param userId 用户编号
     * @param reqVO  通过请求
     */
    @PostMapping(PREFIX + "/approveTask")
    @ApiOperation("通过任务")
    CommonResult<Boolean> approveTask(@RequestParam("userId")Long userId, @RequestBody BpmTaskApproveReqDTO reqVO);

    /**
     * 查询是否是顺序审批的最后一个
     * @param reqVO 通过请求
     * @return
     */
    @PostMapping(PREFIX + "/isLast")
    @ApiOperation("查询是否是顺序审批的最后一个")
    Boolean isLast(@RequestBody BpmTaskApproveReqDTO reqVO);

    @PostMapping(PREFIX + "/isSequenceLast")
    @ApiOperation("查询是否是顺序审批的最后一个")
    CommonResult<Map<String,Object>> isSequenceLast(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "/getTime")
    @ApiOperation("查询是否是顺序审批的最后一个")
    CommonResult<LocalDateTime> getTime(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 不通过任务
     *
     * @param userId 用户编号
     * @param reqVO  不通过请求
     */
    @PostMapping(PREFIX + "/rejectTask")
    @ApiOperation("不通过任务")
    CommonResult<Boolean>  rejectTask(@RequestParam("userId") Long userId, @RequestBody BpmTaskRejectReqDTO reqVO);

    /**
     * 将流程任务分配给指定用户
     *
     * @param userId 用户编号
     * @param reqVO  分配请求
     */
    @PostMapping(PREFIX + "/updateTaskAssignee")
    @ApiOperation("转派任务")
    CommonResult<Boolean>  updateTaskAssignee(@RequestParam("userId")  Long userId,@RequestBody BpmTaskUpdateAssigneeReqDTO reqVO);

    /**
     * 将某任务分配给指定用户
     * @param reqVO  分配请求
     */
    @PostMapping(PREFIX + "/changeTaskAssignee")
    @ApiOperation("指派任务")
    CommonResult<Boolean>  changeTaskAssignee(@RequestBody BpmTaskUpdateAssigneeReqDTO reqVO);


    /**
     * 驳回任务
     *
     * @param bpmTaskVO
     */
    @PostMapping(PREFIX + "/taskReject")
    @ApiOperation("驳回任务")
    CommonResult<Boolean>  taskReject(@RequestBody BpmTaskDTO bpmTaskVO);

    /**
     * 撤回流程
     * @param bpmTaskVO
     */
    @PostMapping(PREFIX + "/revokeProcess")
    @ApiOperation("撤回流程")
    CommonResult<Boolean>  revokeProcess(@RequestBody BpmTaskDTO bpmTaskVO);


    /**
     * 查询正在执行的任务审批人员
     * @param processInstanceId
     */
    @GetMapping(PREFIX + "/getTaskExt")
    @ApiOperation("查询正在执行的任务审批人员")
    CommonResult<List<AdminUserRespDTO>> getTaskExt(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 查询正在执行的任务
     * @param processInstanceId
     */
    @GetMapping(PREFIX + "/getDetailTask")
    @ApiOperation("任务历史")
    CommonResult<BpmTaskExtDTO> getDetailTask(@RequestParam("processInstanceId") String processInstanceId,@RequestParam("assigneeUserId") Long assigneeUserId);

    @GetMapping(PREFIX + "/getAllTask")
    @ApiOperation("所有任务")
    CommonResult<BpmTaskExtDTO> getAllTask(@RequestParam("processInstanceId") String processInstanceId,
                                           @RequestParam("assigneeUserId") Long assigneeUserId,
                                           @RequestParam("result") String result);
    /**
     * 查询正在执行的任务
     * @param processInstanceId
     */
    @GetMapping(PREFIX + "/getTaskDefKey")
    @ApiOperation("查询正在执行的任务定义")
    CommonResult<String> getTaskKeyByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    /**
     * 查询当前任务的下一步办理人
     */
    @PostMapping (PREFIX + "/getTaskNexApproval")
    @ApiOperation("查询下一步办理人")
    CommonResult<Map<String, Object>> getTaskNextApproval(@RequestBody Map<String,String> reqMap);

    @PostMapping (PREFIX + "/getTaskNexApprovals")
    @ApiOperation("查询下一步办理人们")
    CommonResult<Map<String, Object>> getTaskNextApprovals(@RequestParam("userId") Long userId,
                                                           @RequestParam(value = "taskId",required = false) String taskId,
                                                           @RequestParam(value = "nextTaskName",required = false) String nextTaskName,
                                                           @RequestParam(value = "deptId",required = false) Long deptId);

    /**
     * 更新正在执行的任务督办提醒状态
     * @param taskId
     */
    @GetMapping(PREFIX + "/updateTaskSuperviseStatus")
    @ApiOperation("任务督办提醒状态")
    CommonResult<Boolean> updateTaskSuperviseStatus(@RequestParam("taskId") String taskId,@RequestParam("superviseStatus") String superviseStatus);

    @GetMapping(PREFIX + "/getTaskListByProcessInstanceId")
    @ApiOperation("获得办理日志")
    List<BpmTaskRespDTO> getTaskListByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "/getTaskLogByProcessInstanceId")
    @ApiOperation("获得办理日志简化")
    List<Map<String,Object>> getTaskLogByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/restartProcess")
    @ApiOperation("重新提交审批")
    CommonResult<Boolean> restartProcess(@RequestBody BpmRestartDTO bpmRestartDTO);

    @GetMapping(PREFIX + "/getNextTaskName")
    @ApiOperation("获得下一任务节点的名称")
    String getNextTaskName(@RequestParam("taskId")String taskId);

    @PostMapping(PREFIX + "/saveCustomTaskExt")
    @ApiOperation("保存流程引擎外的日志记录")
    void saveCustomTaskExt(@RequestBody BpmTaskExtDTO bpmTaskExtDTO);

    @PostMapping(PREFIX + "/saveBatchCustomTaskExt")
    @ApiOperation("批量保存流程引擎外的日志记录")
    void saveBatchCustomTaskExt(@RequestBody List<BpmTaskExtDTO> records);

    @GetMapping(PREFIX + "/getNeededTaskInfo")
    @ApiOperation("获得详情页面所需的任务信息")
    Map<String, String> getNeededTaskInfo(@RequestParam(value = "category")String category,
                                          @RequestParam(value = "loginUserId")Long loginUserId,
                                          @RequestParam(value = "processInstanceId")String processInstanceId,
                                          @RequestParam(value = "isReceived", required = false)Boolean isReceived,
                                          @RequestParam(value = "isDealt", required = false)Boolean isDealt);

    @GetMapping(PREFIX + "/superviseTask")
    @ApiOperation("催办任务")
    CommonResult<List<Long>> superviseTask(@RequestParam(value = "loginUserId")Long loginUserId,
                             @RequestParam(value = "processInstanceId")String processInstanceId);
}
