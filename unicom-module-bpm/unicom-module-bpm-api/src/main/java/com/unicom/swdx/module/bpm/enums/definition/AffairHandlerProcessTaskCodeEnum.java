package com.unicom.swdx.module.bpm.enums.definition;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum AffairHandlerProcessTaskCodeEnum {

    REGISTER_D("Activity_0whnlvl", "首席审批员分发","6"),  // 体育类民办非企业单位申请登记审查
    REGISTER_F("Activity_0kbxzj4", "体育经济处","1"),  // 体育类民办非企业单位申请登记审查
    REGISTER_T("Activity_00mxc52", "局领导审批","3"), // 体育类民办非企业单位申请登记审查
    REGISTER_FB("Activity_1tazfdl", "首席审批员反馈","7"),  // 体育类民办非企业单位申请登记审查

    REWARD_D("Activity_0ego1qt", "首席审批员分发","6"), //全国体育事业发展奖励
    REWARD_F("Activity_077eqzl", "人力资源社会保障部门、体育行政部门审核","1"), //全国体育事业发展奖励
    REWARD_T("Activity_1k2advm", "省评选工作领导小组审批、公示、上报","4"), //全国体育事业发展奖励
    REWARD_H("Activity_0s9rx5n", "审批","2"), //全国体育事业发展奖励
    REWARD_FB("Activity_1ym975a", "首席审批员反馈","7"), //全国体育事业发展奖励

    FITNESS_D("Activity_1h4uz56", "首席审批员分发","6"), //全民健身事业发展奖励
    FITNESS_F("Activity_194u4ay", "人力资源社会保障部门、体育行政部门审核","1"), //全民健身事业发展奖励
    FITNESS_T("Activity_1pl2yg5", "省评选工作领导小组审批、公示、上报","4"), //全民健身事业发展奖励
    FITNESS_H("Activity_0p9pdbr", "审批","2"), //全民健身事业发展奖励
    FITNESS_FB("Activity_0uri7c3", "首席审批员反馈","7"), //全民健身事业发展奖励

    NAMEING_D("Activity_1tlgekb", "首席审批员分发","6"), //省级体育传统项目学校的审定和命名
    NAMEING_F("Activity_0l0ohjx", "青少处承办人初审","1"), //省级体育传统项目学校的审定和命名
    NAMEING_T("Activity_0i4wol7", "专家组评审","4"), //省级体育传统项目学校的审定和命名
    NAMEING_FB("Activity_18vae9c", "首席审批员反馈","7"), //省级体育传统项目学校的审定和命名

    BASE_D("Activity_1u82py8", "首席审批员分发","6"), //创建省高水平体育后备人才基地
    BASE_F("Activity_1bguupq", "青少年体育处初审","1"), //创建省高水平体育后备人才基地
    BASE_T("Activity_0pa5vz4", "局领导审批","3"), //创建省高水平体育后备人才基地
    BASE_FB("Activity_0odthue", "首席审批员反馈","7"), //创建省高水平体育后备人才基地

    INSTRUCT_D("Activity_0nb420o", "首席审批员分发","6"), //一级社会体育指导员技术等级称号授予
    INSTRUCT_F("Activity_1ht8gta", "群众体育处审核1","1"), //一级社会体育指导员技术等级称号授予
    INSTRUCT_T("Activity_0obawxi", "群众体育处审核2","2"), //一级社会体育指导员技术等级称号授予
    INSTRUCT_E("Activity_0m65x2w", "分管领导签发","3"), //一级社会体育指导员技术等级称号授予
    INSTRUCT_FB("Activity_0cz1ifn", "首席审批员反馈","7"), //一级社会体育指导员技术等级称号授予

    ATHLET_D("Activity_1xhzx50", "首席审批员分发","6"), //一级运动员等级称号授予
    ATHLET_F("Activity_0e1ncld", "竞技体育与科技处审核1","1"), //一级运动员等级称号授予
    ATHLET_T("Activity_1mrid8w", "竞技体育与科技处审核2","2"), //一级运动员等级称号授予
    ATHLET_E("Activity_1vlk4f5", "分管领导审批","3"), //一级运动员等级称号授予
    ATHLET_FB("Activity_0zrj3oq", "首席审批员反馈","7"), //一级运动员等级称号授予

    FACILITY_D("Activity_0jfp33s", "首席审批员分发","6"), //临时占用公共体育场（馆）设施审批
    FACILITY_F("Activity_172g1yt", "体育经济处审核1","1"), //临时占用公共体育场（馆）设施审批
    FACILITY_T("Activity_0wkqq67", "体育经济处审核2","2"), //临时占用公共体育场（馆）设施审批
    FACILITY_E("Activity_0x7khyw", "分管领导审批","3"), //临时占用公共体育场（馆）设施审批
    FACILITY_A("Activity_1r16v2d", "分管领导审批","5"), //临时占用公共体育场（馆）设施审批
    FACILITY_FB("Activity_1gkjp0e", "首席审批员反馈","7"), //临时占用公共体育场（馆）设施审批

    COMPTITION_D("Activity_1y4tska", "首席审批员分发","6"), //从事射击竞技体育运动单位审批
    COMPTITION_F("Activity_1utbgjp", "竞技体育与科技处审核1","1"), //从事射击竞技体育运动单位审批
    COMPTITION_T("Activity_0dnbo3s", "竞技体育与科技处审核2","2"), //从事射击竞技体育运动单位审批
    COMPTITION_E("Activity_02ec047", "分管领导签批，印制通知文书","3"), //从事射击竞技体育运动单位审批
    COMPTITION_FB("Activity_1w6xfg7", "首席审批员反馈","7"), //从事射击竞技体育运动单位审批
;
    private final String code;
    private final String name;
    //别名
    private final String type;

    public static List<String> getAllHandleTaskCode(){
        List<String> res = new ArrayList<>();
        for (AffairHandleTaskNameCodeEnum e:AffairHandleTaskNameCodeEnum.values()){
            res.add(e.getCode());
        }
        return res;
    }

    //
    public static List<String> getTypeHandleTaskCode(String type){
        List<String> res = new ArrayList<>();
        for (AffairHandlerProcessTaskCodeEnum e: AffairHandlerProcessTaskCodeEnum.values()){
            if (e.getType().equals(type)){
                res.add(e.getCode());
            }
        }
        return res;
    }
}
