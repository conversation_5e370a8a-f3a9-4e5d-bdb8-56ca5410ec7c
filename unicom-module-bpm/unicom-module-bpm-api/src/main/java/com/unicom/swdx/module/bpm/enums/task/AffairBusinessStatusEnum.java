package com.unicom.swdx.module.bpm.enums.task;


import cn.hutool.core.util.ArrayUtil;
import lombok.Getter;

@Getter
public enum AffairBusinessStatusEnum {

    /**
     * 说明
     */
    REJECT(0, "不予办理"),
    CHECKING(1, "审批中"),
    HANDLING(3, "办理中"),
    SUCCESS(2, "已办结"),

    INCOMPLETE(4,"补正材料"),
    CANCEL(5,"已撤销"),

    DISTRIBUTE(6,"分发中"),

    FEEDBACK(7,"反馈中");


    /**
     * 类型
     */
    private final Integer type;

    /**
     * 说明
     */
    private final String remark;

    AffairBusinessStatusEnum(Integer type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public String getRemark() {
        return remark;
    }

    public static Integer getTypeByRemark(String remark)
    {
        return  ArrayUtil.firstMatch(a -> a.getRemark().equals(remark),
                values()).getType();
    }
}
