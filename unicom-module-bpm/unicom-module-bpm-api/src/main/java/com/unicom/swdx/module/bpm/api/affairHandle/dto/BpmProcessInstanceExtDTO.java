package com.unicom.swdx.module.bpm.api.affairHandle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@ApiModel("办事评价 - 政务办理 Response DTO")
@Data
public class BpmProcessInstanceExtDTO {

    @ApiModelProperty(value = "流程实例的编号")
    private String id;

    @ApiModelProperty(value = "申请单位/人")
    private Long startUserId;

    @ApiModelProperty(value = "流程名称")
    private String name;

    @ApiModelProperty(value = "流程实例的编号")
    private String processKey;

    @ApiModelProperty(value = "流程实例的编号")
    private String processInstanceId;

    @ApiModelProperty(value = "流程定义的编号")
    private String processDefinitionId;

    @ApiModelProperty(value = "流程分类", notes = "参见 bpm_model_category 数据字典")
    private String category;

    @ApiModelProperty(value = "流程实例的状态", notes = "参见 bpm_process_instance_status")
    private Integer status;

    @ApiModelProperty(value = "流程实例的结果", notes = "参见 bpm_process_instance_result")
    private Integer result;

    @ApiModelProperty(value = "提交时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "提交的表单值")
    private Map<String, Object> formVariables;

    @ApiModelProperty(value = "政务办理流程实例code")
    private String handleInstanceCode;

}