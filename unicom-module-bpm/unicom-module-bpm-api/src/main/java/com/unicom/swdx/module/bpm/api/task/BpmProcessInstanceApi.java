package com.unicom.swdx.module.bpm.api.task;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceUpdateReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * 流程实例 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 流程实例")
public interface BpmProcessInstanceApi {

    String PREFIX = ApiConstants.PREFIX + "/process";
    /**
     * 创建流程实例（提供给内部）
     *
     * @param userId 用户编号
     * @param reqDTO 创建信息
     * @return 实例的编号
     */
    @PostMapping(PREFIX + "/createProcessInstance")
    @ApiOperation("创建流程实例")
    CommonResult<String> createProcessInstance(@RequestParam("userId") Long userId, @RequestBody BpmProcessInstanceCreateReqDTO reqDTO);

    @GetMapping(PREFIX + "/getProcessInstanceInfo")
    @ApiOperation("获取流程实例信息")
    CommonResult<BpmProcessInstanceRespDTO> getProcessInstanceInfo(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/skipFirstTask")
    @ApiOperation("跳过第一个任务节点")
    CommonResult<Boolean> skipFirstTask(@RequestParam("proinstanceId") String proinstanceId,
                                        @RequestParam(value = "time", required = false) LocalDateTime time);

    @PostMapping(PREFIX + "/updateProcessInstance")
    @ApiOperation("更新流程实例表单数据")
    CommonResult<Boolean> updateProcessInstance(@RequestBody BpmProcessInstanceUpdateReqDTO reqDTO);

    @PostMapping(PREFIX + "/updateProcessInstanceParam")
    @ApiOperation("更新流程实例表单参数")
    CommonResult<Boolean> updateProcessInstanceParam(@RequestBody BpmProcessInstanceUpdateReqDTO reqDTO);

    @GetMapping(PREFIX + "/getProcessInstanceStatus")
    @ApiOperation("获得流程实例参数")
    CommonResult<Map<String,Object>> getProcessInstanceStatus(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "/delProcessInstance")
    @ApiOperation("删除流程实例")
    CommonResult<Boolean> delProcessInstance(@RequestParam("processInstanceId") String processInstanceId);


    @GetMapping(PREFIX + "/updateProcessInstanceFlowflag")
    @ApiOperation("更新状态标识")
    CommonResult<Boolean> updateProcessInstanceFlowflag(@RequestParam("processInstanceId") String processInstanceId,@RequestParam("flowFlag") String flowFlag);

    @PostMapping(PREFIX + "/getProcessInstanceFormVariable")
    @ApiOperation("更新流程实例表单数据")
    CommonResult<Map<String,Object>> getProcessInstanceFormVariableMap(@RequestBody Set<String> ids);

    @GetMapping(PREFIX + "/getNextApprovalByTaskKey")
    @ApiOperation("获取用户组审批人员")
    CommonResult<Set<Long>> getNextApprovalByTaskKey(@RequestParam("taskKey")String taskKey);

    @GetMapping(PREFIX + "getStartUserId")
    @ApiOperation("获取流程发起人")
    CommonResult<Long> getStartUserId(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "cancel")
    @ApiOperation("撤销流程")
    CommonResult<String> cancelProcessInstance(@RequestParam("loginUserId") Long loginUserId, @RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "endProcess")
    @ApiOperation("结束流程")
    CommonResult<String> endProcess(@RequestParam("loginUserId") Long loginUserId, @RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "endVacationProcess")
    @ApiOperation("结束流程")
    CommonResult<String> endVacationProcess(@RequestParam("loginUserId") Long loginUserId, @RequestParam("processInstanceId") String processInstanceId);


    @GetMapping(PREFIX + "removeProcess")
    @ApiOperation("删除流程")
    CommonResult<String> removeProcess(@RequestParam("loginUserId") Long loginUserId, @RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "getTodoTaskTotal")
    @ApiOperation("获取待办数量")
    Long getTodoTaskTotal(@RequestParam("loginUserId") Long loginUserId);

    @GetMapping(PREFIX + "getTaskTotal")
    @ApiOperation("中心工作")
    Long getTaskTotal(@RequestParam("loginUserId") Long loginUserId);

}
