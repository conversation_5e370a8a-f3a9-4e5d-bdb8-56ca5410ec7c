package com.unicom.swdx.module.bpm.api.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@ApiModel("管理后台 - 流程实例的 Response DTO")
@Data
public class BpmProcessInstanceRespDTO {
    @ApiModelProperty(value = "流程实例的编号", required = true, example = "1024")
    private String id;

    @ApiModelProperty(value = "流程名称", required = true, example = "sk")
    private String name;

    @ApiModelProperty(value = "流程分类", required = true, notes = "参见 bpm_model_category 数据字典", example = "1")
    private String category;

    @ApiModelProperty(value = "流程实例的状态", required = true, notes = "参见 bpm_process_instance_status", example = "1")
    private Integer status;

    @ApiModelProperty(value = "流程实例的结果", required = true, notes = "参见 bpm_process_instance_result", example = "2")
    private Integer result;

    @ApiModelProperty(value = "提交时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "提交的表单值", required = true)
    private Map<String, Object> formVariables;

    @ApiModelProperty(value = "业务的唯一标识", example = "1", notes = "例如说，请假申请的编号")
    private String businessKey;

    @ApiModelProperty(value = "任务编号", example = "1", notes = "当前正在执行的任务编号")
    private String taskDefKey;

    @ApiModelProperty(value = "任务ID", example = "1", notes = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "流程类型编号", example = "1", notes = "流程类型编号")
    private String processKey;

    /**
     * 发起流程的用户
     */
    @ApiModelProperty(value = "发起人")
    private UserDTO startUser;

//    @ApiModelProperty(value = "政务办理流程参数", example = "1", notes = "政务办理流程参数")
//    private String paramCondition;

//    @ApiModelProperty(value = "事项类型")
//    private String processType;

}
