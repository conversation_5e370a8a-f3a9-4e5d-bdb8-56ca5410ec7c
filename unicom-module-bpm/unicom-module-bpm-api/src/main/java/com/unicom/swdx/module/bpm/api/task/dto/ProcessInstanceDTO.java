package com.unicom.swdx.module.bpm.api.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("流程实例简要信息")
public class ProcessInstanceDTO {
    @ApiModelProperty(value = "流程实例编号", required = true, example = "1024")
    private String id;

    @ApiModelProperty(value = "流程实例名称", required = true, example = "sk")
    private String name;

    @ApiModelProperty(value = "发起人的用户编号", required = true, example = "1024")
    private Long startUserId;

    @ApiModelProperty(value = "发起人的用户昵称", required = true, example = "芋艿")
    private String startUserNickname;

    @ApiModelProperty(value = "流程定义的编号", required = true, example = "2048")
    private String processDefinitionId;
}
