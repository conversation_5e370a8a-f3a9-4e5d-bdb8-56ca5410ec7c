package com.unicom.swdx.module.bpm.api.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("用户信息")
@Data
public class UserDTO {

    @ApiModelProperty(value = "用户编号", example = "1")
    private Long id;
    @ApiModelProperty(value = "用户昵称", example = "芋艿")
    private String nickname;

    @ApiModelProperty(value = "部门编号", example = "1")
    private Long deptId;
    @ApiModelProperty(value = "部门名称", example = "研发部")
    private String deptName;

}