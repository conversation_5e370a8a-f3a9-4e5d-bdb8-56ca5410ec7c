package com.unicom.swdx.module.bpm.enums.definition.appQuery;


import com.unicom.swdx.module.bpm.enums.definition.AffairProcessNameTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 审批流程类型筛选 标签
 */
@Getter
@AllArgsConstructor
public enum ApprovalTypeTagEnum {

    AFFAIR_HANDLE("affairHandling","政务办理"),
    COMPREHENSIVE_AFFAIRS("comprehensiveAffairs","综合事务"),
    KEY_WORK("keyWork","重点工作"),
    SENDING("affair-sending-flow","发文"),
    RECEIVING("affair-receiving-flow","收文"),
    PEOPLE_INTERACTION("peopleInteraction","政民互动");
    /**
     * 审批类型筛选标签
     */
    final String tagName;

    /**
     * 描述
     */
    final String description;

    /**
     * 根据sqlName获得对应的枚举类
     * @param tagName 模版sql约束名
     * @return SqlTemplateNemConstants
     */
    public static ApprovalTypeTagEnum getByTagName(String tagName){
        if ( Objects.nonNull(AffairProcessNameTypeEnum.getNameByType( tagName )) ){
            return COMPREHENSIVE_AFFAIRS;
        }
        for (ApprovalTypeTagEnum e: ApprovalTypeTagEnum.values()) {
            if (e.tagName.equals(tagName)) {
                return e;
            }
        }
        return null;
    }
}
