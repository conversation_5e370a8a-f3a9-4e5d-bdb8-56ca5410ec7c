package com.unicom.swdx.module.bpm.enums.definition;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 政务子系统政务办理
 * @date 2023/4/4  13:47
 */
@Getter
@AllArgsConstructor
public enum AffairHandleProcessNameTypeEnum {
    SHOOTING_COMPETITION("affair_handle_shooting_competition", "从事射击竞技体育运动单位审批","permit"),

    TEMP_OCCUPY_FACILITY("affair_handle_temp_occupy_facility", "临时占用公共体育场（馆）设施审批","permit"),

    FIRST_LEVEL_ATHLETE("affair_handle_first_level_athlete", "一级运动员等级称号授予","confirmation"),

    LEVEL_SPORTS_INSTRUCTOR("affair_handle_first_level_sports_instructor", "一级社会体育指导员技术等级称号授予","confirmation"),

    TALENT_REVERVE_BASE("affair_handle_high_level_talent_reserve_base", "创建省高水平体育后备人才基地","confirmation"),

    SCHOOLS_APPROVAL_NAMING("affair_handle_schools_approval_naming", "省级体育传统项目学校的审定和命名","confirmation"),

    NATIONAL_FITNESS_REWARD("affair_handle_national_fitness_reward", "对在发展全民健身事业中做出突出贡献的组织和个人，按照国家有关规定给予奖励","award"),

    NATIONAL_SPORTS_REWARD("affair_handle_national_sports_reward", "对全国体育事业做出突出贡献的组织和个人，按照国家有关规定给予奖励","award"),

    NON_ENTERPRISE_REGISTER("affair_handle_private_non_enterprise_register", "体育类民办非企业单位申请登记审查","other");

    private final String type;
    private final String desc;

    //别名
    private final String subject;

    /**
     * 根据type查询
     * @param type
     * @return
     */
    public static AffairHandleProcessNameTypeEnum getNameByType(String type) {
        return ArrayUtil.firstMatch(affairProcessNameTypeEnum -> affairProcessNameTypeEnum.getType().equals(type),
                values());
    }

    /**
     * 根据name查询
     * @param name
     * @return
     */
    public static AffairHandleProcessNameTypeEnum getTypeByName(String name) {
        return ArrayUtil.firstMatch(affairProcessNameTypeEnum -> affairProcessNameTypeEnum.getDesc().equals(name),
                values());
    }

    public static AffairHandleProcessNameTypeEnum getSubjectByType(String type) {
        return ArrayUtil.firstMatch(affairProcessNameTypeEnum -> affairProcessNameTypeEnum.getType().equals(type),
                values());
    }

    //返回综合事务的processKeys
    public static List<String> getComprehensiveAffairs(String type){
        List<String> res = new ArrayList<>();
        for (AffairHandleProcessNameTypeEnum e: AffairHandleProcessNameTypeEnum.values()){
            if (e.subject.equals(type)){
                res.add(e.getType());
            }
        }
        return res;
    }

    //根据类型获得type集合
    public static List<String> getTypesBySubject(String subject) {
        return Arrays.stream(values()).filter(e -> e.getSubject().equals(subject)).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());
    }
}
