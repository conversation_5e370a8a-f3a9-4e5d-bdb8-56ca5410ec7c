package com.unicom.swdx.module.bpm.api.affairHandle;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.affairHandle.dto.BpmProcessInstanceCreateDTO;
import com.unicom.swdx.module.bpm.api.affairHandle.dto.BpmProcessInstanceExtDTO;
import com.unicom.swdx.module.bpm.api.task.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 政务办理")
public interface AffairHandleApi {

    String PREFIX = ApiConstants.PREFIX + "/affair-handle";


    @PostMapping(PREFIX + "/create-process")
    @ApiOperation("发起流程")
    @PermitAll
    CommonResult<String> createProcess(@RequestParam("userId") Long userId,@RequestBody BpmProcessInstanceCreateDTO CreateReqVO);

    @GetMapping("/get-detail-by-instance-code")
    @ApiOperation("获取流程实例详情")
    BpmProcessInstanceExtDTO getDetailByInstanceCode(@RequestParam("instanceCode") String instanceCode);

    @GetMapping(PREFIX + "/get-handle-Count")
    @ApiOperation("政务办理查询数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataTypeClass = Long.class),
    })
    CommonResult<Map<String,Object>> getHandleCount(@RequestParam("userId") Long userId);



    @PostMapping(PREFIX + "/create-ineraction-process")
    @ApiOperation("发起政民互动流程")
    @PermitAll
    CommonResult<String> createIneractionProcess(@RequestParam("userId") Long userId, @RequestBody BpmProcessInstanceCreateDTO CreateReqVO);
}
