package com.unicom.swdx.module.bpm.enums.definition;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/02/14  09:10
 */
@Getter
@AllArgsConstructor
public enum AffairStartTaskCodeEnum {
    LEAVE_START("Activity_1j916mn", "发起审批"),  // 流程标识
    ABROAD_START("Activity_1sgycmg", "发起审批"), // 出国公务
    DOCUMENT_START("Activity_0dn4j2s", "发起审批"),
    SUPPLIES_START("Activity_0jow1m3", "发起审批"),
    MEAL_START("Activity_1cjcnlb", "发起审批"),
    SEAL_START("Activity_1yopry2", "发起审批"),  // 用章
    VEHICLE_START("Activity_0hh3h30", "发起审批"), // 用车
    MEETING_RESERVETION_START("Activity_015e9nm", "发起审批"), //会议室预定
    MEETING_RESERVETION_APPLICTION("Activity_16a7twx", "发起审批"), // 会议申请
    CONTRACT_2_START("Activity_11n4g26","发起审批"), // 合同2
    CONTRACT_1_START("Activity_12psbvf", "发起审批"), // 合同1
    WEB_START("Activity_0rjg62p", "发起审批"), // 网站发文
    REQ_INSTRUNCT("Activity_0l79q8d", "发起审批"); //请示呈批





    private final String code;
    private final String name;

    public static boolean isStartTaskByCode(String taskDefKey){
        for (AffairStartTaskCodeEnum e: AffairStartTaskCodeEnum.values()){
            if (e.getCode().equals(taskDefKey)){
                return true;
            }
        }
        return false;
    }
}
