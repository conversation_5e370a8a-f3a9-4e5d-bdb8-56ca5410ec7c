//package com.unicom.swdx.module.bpm.api.affairProcess;
//
//import com.unicom.swdx.framework.common.pojo.CommonResult;
//import com.unicom.swdx.module.bpm.api.task.ApiConstants;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
//@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
//@Api(tags = "RPC 服务 - 审批中心")
//public interface AffairProcessApi {
//
//    String PREFIX = ApiConstants.PREFIX + "/affair-process";
//
//    @GetMapping(PREFIX + "todo-count")
//    @ApiOperation("获取 Todo 待审批个数")
//    CommonResult<Integer> getTodoCount(@RequestParam("userId") Long userId);
//
//}
