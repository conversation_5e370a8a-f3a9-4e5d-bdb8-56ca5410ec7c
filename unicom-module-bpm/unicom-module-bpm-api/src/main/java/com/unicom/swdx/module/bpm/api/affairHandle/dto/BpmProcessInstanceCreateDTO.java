package com.unicom.swdx.module.bpm.api.affairHandle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@ApiModel("管理后台 - 流程实例的创建 Request VO")
@Data
public class BpmProcessInstanceCreateDTO {

    @ApiModelProperty(value = "流程定义的编号", example = "1024")
    private String processDefinitionId;

    @ApiModelProperty(value = "流程实例编号", example = "1024")
    private String processInstanceId;

    @ApiModelProperty(value = "流程定义", required = true, example = "1024")
    @NotEmpty(message = "流程定义不能为空")
    private String processDefinitionKey;

    @ApiModelProperty(value = "变量实例")
    private Map<String, Object> variables;

    @ApiModelProperty(value = "政务办理流程实例ID", example = "1024")
    private String handleInstanceId;

    @ApiModelProperty(value = "政务办理流程实例编号", example = "1024")
    private String handleInstanceCode;
}
