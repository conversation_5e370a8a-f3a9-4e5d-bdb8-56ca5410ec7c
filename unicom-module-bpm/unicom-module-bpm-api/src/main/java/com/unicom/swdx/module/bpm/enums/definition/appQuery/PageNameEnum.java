package com.unicom.swdx.module.bpm.enums.definition.appQuery;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求页面名称 枚举
 */
@Getter
@AllArgsConstructor
public enum PageNameEnum {

    TODO_PAGE("todo","待办页面"),
    DONE_PAGE("done","已办页面"),
    MY_LAUNCH_PAGE("myLaunch","我发起页面");

    /**
     * sql include模板名称
     */
    final String pageName;

    /**
     * 描述
     */
    final String description;
}
