package com.unicom.swdx.module.bpm.controller.admin.oa.vo;

import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskTodoPageItemRespVO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@ApiModel("管理后台 - 流程任务的 Done 已完成的分页项 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmOATaskTodoPageItemRespVO extends BpmTaskTodoPageItemRespVO {

    @ApiModelProperty(value = "催办:1表示催办", required = true, example = "1")
    private String superviseStatus;

    @ApiModelProperty(value = "当前节点", required = true)
    private String point;

    @ApiModelProperty(value = "操作人", required = true, notes = "参见 bpm_process_instance_result", example = "2")
    private String operator;
    @ApiModelProperty(value = "操作人部门", required = true, example = "不请假了！")
    private String operatorDept;

    @ApiModelProperty(value = "状态", required = true, example = "1")
    private Integer status;

    private List<Map<String,Object>> info;

}
