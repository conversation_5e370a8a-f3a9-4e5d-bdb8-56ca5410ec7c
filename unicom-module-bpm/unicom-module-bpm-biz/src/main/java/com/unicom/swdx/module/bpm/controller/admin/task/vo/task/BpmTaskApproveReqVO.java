package com.unicom.swdx.module.bpm.controller.admin.task.vo.task;



import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess.FileAttachmentCreateReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@ApiModel("管理后台 - 通过流程任务 VO")
@Data
public class BpmTaskApproveReqVO {

    @ApiModelProperty(value = "任务编号")
    private String id;

    @ApiModelProperty(value = "审批意见", required = true, example = "不错不错！")
//    @NotEmpty(message = "审批意见不能为空")
    private String reason;
    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID",required = true)
    private String processInstanceId ;

    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String taskDefKey ;

    /**
     * 审批过程中参数信息
     */
    @ApiModelProperty(value = "审批参数")
    private Map<String, Object> variables;

    /**
     * 审批过程中需要保存的日志信息
     */
    @ApiModelProperty(value = "审批过程中需要保存的日志信息")
    private Map<String, Object> paramsMap;

    /**
     * 审批过程中需要保存的日志信息
     */
    @ApiModelProperty(value = "审批过程中需要保存的日志信息-Json形式保存")
    private String logParameters;

    @ApiModelProperty(value = "图片base64")
    private String imageUrl;

    @ApiModelProperty(value = "附件数据")
    List<FileAttachmentCreateReqVO> fileAttachments;

    private LocalDateTime endTime;

}
