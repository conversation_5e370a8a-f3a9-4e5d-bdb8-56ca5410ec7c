package com.unicom.swdx.module.bpm.controller.admin.task.vo.task;

import com.unicom.swdx.module.bpm.api.task.dto.UserDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 流程任务的 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskRespVO extends BpmTaskDonePageItemRespVO {

    @ApiModelProperty(value = "任务定义的标识", required = true, example = "user-001")
    private String definitionKey;

    /**
     * 审核的用户信息
     */
    @ApiModelProperty("审核的用户信息")
    private UserDTO assigneeUser;

    /**
     * 用来存移动端手写签字等其他任务过程中产生的文件
     */
    @ApiModelProperty(value = "用来存移动端手写签字等其他任务过程中产生的文件")
    private String imageUrl;

}
