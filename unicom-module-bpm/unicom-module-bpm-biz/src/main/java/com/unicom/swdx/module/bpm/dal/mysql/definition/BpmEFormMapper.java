package com.unicom.swdx.module.bpm.dal.mysql.definition;


import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormPageReqVO;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmEFormDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.QueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 动态表单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmEFormMapper extends BaseMapperX<BpmEFormDO> {

    default PageResult<BpmEFormDO> selectPage(BpmEFormPageReqVO reqVO) {
        return selectPage(reqVO, new QueryWrapperX<BpmEFormDO>()
                .likeIfPresent("name", reqVO.getName())
                .orderByDesc("id"));
    }

    @Select("select count(1) from ACT_RE_MODEL where META_INFO_ like concat('%,\"formId\":',#{id},',%')")
    long selectModelCountById(Long id);
}
