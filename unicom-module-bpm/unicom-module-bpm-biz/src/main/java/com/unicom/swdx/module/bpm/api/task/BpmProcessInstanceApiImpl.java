package com.unicom.swdx.module.bpm.api.task;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceUpdateReqDTO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskTodoPageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskTodoPageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCancelReqVO;
import com.unicom.swdx.module.bpm.dal.mysql.task.BpmProcessInstanceExtMapper;
import com.unicom.swdx.module.bpm.enums.definition.SubSystemSecrectEnum;
import com.unicom.swdx.module.bpm.service.oa.BpmOATaskService;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

/**
 * Flowable 流程实例 Api 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class BpmProcessInstanceApiImpl implements BpmProcessInstanceApi {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private BpmOATaskService taskService;

    @Resource
    private BpmProcessInstanceExtMapper bpmProcessInstanceExtMapper;

    @Override
    public CommonResult<BpmProcessInstanceRespDTO> getProcessInstanceInfo(String processInstanceId) {
        BpmProcessInstanceRespDTO resp = processInstanceService.getProcessInstanceResp(processInstanceId);
        return success(resp);
    }

    @Override
    public CommonResult<String> createProcessInstance(Long userId, BpmProcessInstanceCreateReqDTO reqDTO) {
        return success(processInstanceService.createProcessInstance(userId, reqDTO));
    }

    @Override
    public CommonResult<Boolean> skipFirstTask(String processInstanceId, LocalDateTime time) {
        return success(processInstanceService.skipFirstTask(processInstanceId, time));
//        processInstanceService.updateProcessInstanceExtFlowFlag(proinstanceId, null);
//        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateProcessInstance(BpmProcessInstanceUpdateReqDTO reqDTO) {
        processInstanceService.updateProcessInstance(reqDTO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateProcessInstanceParam(BpmProcessInstanceUpdateReqDTO reqDTO) {
        processInstanceService.updateProcessInstanceParam(reqDTO);
        return success(true);
    }

    @Override
    public CommonResult<Map<String,Object>> getProcessInstanceStatus(String processInstanceId) {
        return success(processInstanceService.getProcessInstanceStatus(processInstanceId));
    }

    @Override
    public CommonResult<Boolean> delProcessInstance(String processInstanceId) {
        return success(processInstanceService.delProcessInstance(processInstanceId));
    }

    @Override
    public CommonResult<Boolean> updateProcessInstanceFlowflag(String processInstanceId, String flowFlag) {
        return success(processInstanceService.updateProcessInstanceFlowflag(processInstanceId, flowFlag));
    }

    @Override
    public CommonResult<Map<String, Object>> getProcessInstanceFormVariableMap(Set<String> ids) {
        if (ids.isEmpty())
            return success(new HashMap<>());
        Map<String,Object> formVariableMap = processInstanceService.getProcessInstanceFormVariableMap((Set<String>) ids);
        return success(formVariableMap);
    }

    @Override
    public CommonResult<Set<Long>> getNextApprovalByTaskKey(String taskKey) {
        Set<Long> ids = processInstanceService.getNextApprovalByTaskKey(taskKey);
        return success(ids);
    }

    @Override
    public CommonResult<Long> getStartUserId(String processInstanceId) {
        Long startUserId = processInstanceService.getProcessInstanceVO(processInstanceId).getStartUser().getId();
        return success(startUserId);
    }

    @Override
    public CommonResult<String> cancelProcessInstance(Long loginUserId, String processInstanceId) {
        String itemId = processInstanceService.cancelProcessInstance(loginUserId,
                new BpmProcessInstanceCancelReqVO().setProcessInstanceId(processInstanceId).setReason("发起人自行撤销"));
        return success(itemId);
    }

    @Override
    public CommonResult<String> endProcess(Long userId, String processInstanceId) {
        String itemId = processInstanceService.endProcessInstance(userId, processInstanceId);
        return success(itemId);
    }

    @Override
    public CommonResult<String> endVacationProcess(Long userId, String processInstanceId) {
        String itemId = processInstanceService.endVacationProcessInstance(userId, processInstanceId);
        return success(itemId);
    }

    @Override
    public CommonResult<String> removeProcess(Long userId, String processInstanceId) {
        String itemId = processInstanceService.removeProcessInstance(userId, processInstanceId);
        return success(itemId);
    }

    @Override
    public Long getTodoTaskTotal(Long loginUserId) {
        BpmOATaskTodoPageReqVO  pageVO = new BpmOATaskTodoPageReqVO();
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        PageResult<BpmOATaskTodoPageItemRespVO> page=  taskService.getTodoTaskPage(loginUserId, pageVO);
        return page.getTotal();
    }

    @Override
    public Long getTaskTotal(Long loginUserId) {
        Long total = bpmProcessInstanceExtMapper.getTaskTotal(loginUserId);
        return total;
    }
}
