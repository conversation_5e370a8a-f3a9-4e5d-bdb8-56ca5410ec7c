package com.unicom.swdx.module.bpm.controller.app.dto.querycombined;

import com.unicom.swdx.module.bpm.enums.definition.AffairHandleProcessNameTypeEnum;
import com.unicom.swdx.module.bpm.enums.definition.AffairHandleStartTaskCodeEnum;
import com.unicom.swdx.module.bpm.enums.definition.AffairProcessNameTypeEnum;
import com.unicom.swdx.module.bpm.enums.definition.AffairStartTaskCodeEnum;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.ApprovalTypeTagEnum;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.SqlInclusionEnum;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程的sql模版层面的约束
 * 收文、发文、重点工作、审批中心、政务办理
 */

@Data
public class CombinedSqlInclusion {
    /**
     * 模版名称
     */
    private SqlInclusionEnum includedEnum;

    /**
     * 是否启用该sql模版
     */
    private boolean isActive;

    /**
     * 模版的processKey约束
     */
    private List<String> inProcessKey;

    /**
     * 模版的任务节点约束
     * in :存在于
     * 区分审批中、办理中等状态的节点
     */
    private List<String> inTaskDefKey;

    /**
     * 模版的任务节点约束
     * not in :不存在
     * 例如一些流程的开始节点不需要被查询，就放在这里
     */
    private List<String> notInTaskDefKey;

    /**
     * 该sql include下需要的额外的条件
     * 暂时没有用上
     */
    private Map<String,Object> optionalCondition;

    public CombinedSqlInclusion(SqlInclusionEnum includedEnum){
        //模版枚举
        this.includedEnum = includedEnum;
        //默认启用
        this.isActive = includedEnum.getIsActive();
        //配置各sql模版的processKey约束
        this.inProcessKey = initInProcessKeyByEnum( includedEnum );
        //配置各sql模版的查询时不需要查询的节点
        this.notInTaskDefKey = initNotInTaskDefKey( includedEnum );
    }

    /**
     * 根据枚举，设置每个include下涉及的processKey
     * @param includedNameEnum include enum
     * @return processKey List
     */
    private static List<String> initInProcessKeyByEnum(SqlInclusionEnum includedNameEnum) {
        List<String> returnList = new ArrayList<>();

        if (Objects.isNull(includedNameEnum)){
            return Collections.emptyList();
        }
        switch (includedNameEnum){
            case RECEIVING_INCLUSION:{
                returnList.add(ApprovalTypeTagEnum.RECEIVING.getTagName());
                break;
            }
            case SENDING_INCLUSION:{
                returnList.add(ApprovalTypeTagEnum.SENDING.getTagName());
                break;
            }
            case KEY_WORK_INCLUSION:{
                returnList.add(ApprovalTypeTagEnum.KEY_WORK.getTagName());
                break;
            }
            case APPROVAL_CENTER_INCLUSION:{
                for (AffairProcessNameTypeEnum affairProcessNameTypeEnum : AffairProcessNameTypeEnum.values()) {
                    returnList.add(affairProcessNameTypeEnum.getType());
                }
                break;
            }
            case AFFAIR_HANDLE_INCLUSION:{
                for (AffairHandleProcessNameTypeEnum handleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()){
                    returnList.add(handleProcessNameTypeEnum.getType());
                }
                break;
            }
            case PEOPLE_INTERACTION_INCLUSION:{
                returnList.add(ApprovalTypeTagEnum.PEOPLE_INTERACTION.getTagName());
                break;
            }
            default:{
                returnList = Collections.emptyList();
            }
        }
        return returnList;
    }


    private static List<String> initNotInTaskDefKey( SqlInclusionEnum includedNameEnum ){
        if (Objects.isNull(includedNameEnum)){
            return null;
        }
        switch ( includedNameEnum ) {
            case AFFAIR_HANDLE_INCLUSION:{
                return Arrays.stream(AffairHandleStartTaskCodeEnum.values()).map(AffairHandleStartTaskCodeEnum::getCode).collect(Collectors.toList());
            }
            case APPROVAL_CENTER_INCLUSION:{
                return Arrays.stream(AffairStartTaskCodeEnum.values()).map(AffairStartTaskCodeEnum::getCode).collect(Collectors.toList());
            }
            default:{
                return null;
            }
        }
    }

    /**
     * 创建所有的include
     * @return map
     */
    public static Map<String,CombinedSqlInclusion> createMapIncludeAll(){
        Map<String,CombinedSqlInclusion> includeAllMap = new HashMap<>();
        Arrays.stream(SqlInclusionEnum.values()).forEach( inclusion -> {
            includeAllMap.put( inclusion.getInclusionName(), new CombinedSqlInclusion( inclusion ) );
        });
        return includeAllMap;
    }
}
