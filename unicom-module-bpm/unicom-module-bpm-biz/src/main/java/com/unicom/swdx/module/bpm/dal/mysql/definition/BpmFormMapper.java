package com.unicom.swdx.module.bpm.dal.mysql.definition;


import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.form.BpmFormPageReqVO;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmFormDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.QueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 动态表单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmFormMapper extends BaseMapperX<BpmFormDO> {

    default PageResult<BpmFormDO> selectPage(BpmFormPageReqVO reqVO) {
        return selectPage(reqVO, new QueryWrapperX<BpmFormDO>()
                .likeIfPresent("name", reqVO.getName())
                .orderByDesc("id"));
    }

    @Select("select count(1) from ACT_RE_MODEL where META_INFO_ like concat('%,\"formId\":',#{id},',%')")
    long selectModelCountById(Long id);

    default List<BpmFormDO> selectListByNameLike(String name) {
        return selectList(new LambdaQueryWrapperX<BpmFormDO>()
                .eq(BpmFormDO::getStatus,0)
                .likeIfPresent(BpmFormDO::getName,name)
        );
    }

}
