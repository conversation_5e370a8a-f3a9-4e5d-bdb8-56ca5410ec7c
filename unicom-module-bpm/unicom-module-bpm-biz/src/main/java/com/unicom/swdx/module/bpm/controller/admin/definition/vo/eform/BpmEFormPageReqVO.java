package com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 动态电子表单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmEFormPageReqVO extends PageParam {

    @ApiModelProperty(value = "表单名称", example = "sk")
    private String name;

}
