package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AffairHandlePCReportSummaryVO {
    @ApiModelProperty(value = "办理总数")
    private Integer totalHandle;

    @ApiModelProperty(value = "好评数")
    private Integer favourableComment;

    @ApiModelProperty(value = "督办数")
    private Integer supervision;

    @ApiModelProperty(value = "逾期数")
    private Integer overdue;

    @ApiModelProperty(value = "非常满意")
    private Integer verySatisfied;


    @ApiModelProperty(value = "满意")
    private Integer satisfied;


    @ApiModelProperty(value = "基本满意")
    private Integer basicallySatisfied;


    @ApiModelProperty(value = "不满意")
    private Integer dissatisfied;


    @ApiModelProperty(value = "非常不满意")
    private Integer veryDissatisfied;


    @ApiModelProperty(value = "按满意度总数")
    private Integer totalSatisfaction;

    @ApiModelProperty(value = "行政许可")
    private Integer permit;


    @ApiModelProperty(value = "行政确认")
    private Integer confirmation;


    @ApiModelProperty(value = "行政奖励")
    private Integer award;


    @ApiModelProperty(value = "其他行政权力")
    private Integer other;


    @ApiModelProperty(value = "按业务类型总数")
    private Integer totalProcessType;

    public AffairHandlePCReportSummaryVO()
    {
        this.totalHandle = 0;
        this.favourableComment = 0;
        this.supervision = 0;
        this.overdue = 0;
        this.verySatisfied = 0;
        this.satisfied = 0;
        this.basicallySatisfied = 0;
        this.dissatisfied = 0;
        this.veryDissatisfied = 0;
        this.totalSatisfaction = 0;
        this.permit = 0;
        this.award = 0;
        this.other = 0;
        this.totalProcessType = 0;
    }
}
