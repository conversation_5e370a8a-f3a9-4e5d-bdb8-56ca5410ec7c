package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("政务子系统app 搜索 分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSearchPageReqVO extends PageParam {

    /**
     * APP查询还是网页查询？
     */
    @ApiModelProperty(value = "Web/APP查询标识 web:网页查询 app:app端查询")
    private String clientTag;
    @ApiModelProperty(value = "关键词",required = true)
    private String keyWords;

    @ApiModelProperty(value = "范围:(不填/all：搜索全部)、(myLaunch：我发起)、(done：已办理)",notes = "(不填/all：搜索全部)、(myLaunch：我发起)、(done：已办理)")
    private String scope;


}
