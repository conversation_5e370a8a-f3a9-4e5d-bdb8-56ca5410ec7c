package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel("政务子系统 - 政务办理报表PC导出 Response VO")
@Data
public class AffairHandlePCReportExcelVO {

    @ExcelIgnore
    private Long deptId;

    @ExcelProperty(value = "处室")
    private String deptName;


    @ExcelProperty(value = "办理总数")
    private Integer totalHandle;


    @ExcelProperty(value = "督办件数")
    private Integer supervision;

    @ExcelProperty(value = "逾期件数")
    private Integer overdue;


    @ExcelProperty(value = "行政许可")
    private Integer permit;


    @ExcelProperty(value = "行政确认")
    private Integer confirmation;


    @ExcelProperty(value = "行政奖励")
    private Integer award;


    @ExcelProperty(value = "其他行政权力")
    private Integer other;

    @ExcelProperty(value = "非常满意")
    private Integer verySatisfied;


    @ExcelProperty(value = "满意")
    private Integer satisfied;


    @ExcelProperty(value = "基本满意")
    private Integer basicallySatisfied;


    @ExcelProperty(value = "不满意")
    private Integer dissatisfied;


    @ExcelProperty(value = "非常不满意")
    private Integer veryDissatisfied;


    public AffairHandlePCReportExcelVO()
    {
        this.setTotalHandle(0);
        this.setSupervision(0);
        this.setOverdue(0);
        this.setPermit(0);
        this.setConfirmation(0);
        this.setAward(0);
        this.setOther(0);
        this.setVerySatisfied(0);
        this.setSatisfied(0);
        this.setBasicallySatisfied(0);
        this.setDissatisfied(0);
        this.setVeryDissatisfied(0);


    }
}
