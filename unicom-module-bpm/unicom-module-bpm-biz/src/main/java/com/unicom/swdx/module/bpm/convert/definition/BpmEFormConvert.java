package com.unicom.swdx.module.bpm.convert.definition;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormCreateReqVO;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormRespVO;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormSimpleRespVO;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormUpdateReqVO;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmEFormDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 动态表单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmEFormConvert {

    BpmEFormConvert INSTANCE = Mappers.getMapper(BpmEFormConvert.class);

    BpmEFormDO convert(BpmEFormCreateReqVO bean);

    BpmEFormDO convert(BpmEFormUpdateReqVO bean);

    BpmEFormRespVO convert(BpmEFormDO bean);

    List<BpmEFormSimpleRespVO> convertList2(List<BpmEFormDO> list);

    PageResult<BpmEFormRespVO> convertPage(PageResult<BpmEFormDO> page);

}
