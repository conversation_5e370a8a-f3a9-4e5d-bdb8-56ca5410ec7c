package com.unicom.swdx.module.bpm.convert.task;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.number.NumberUtils;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskExtDTO;
import com.unicom.swdx.module.bpm.api.task.dto.ProcessInstanceDTO;
import com.unicom.swdx.module.bpm.api.task.dto.UserDTO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskDonePageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskTodoPageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskDonePageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskTodoPageItemRespVO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.unicom.swdx.module.bpm.service.message.dto.BpmMessageSendWhenTaskCreatedReqDTO;
import com.unicom.swdx.module.bpm.service.oa.BpmOATaskService;
import com.unicom.swdx.module.bpm.service.oa.BpmOATaskServiceImpl;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Bpm 任务 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmTaskConvert {

    BpmTaskConvert INSTANCE = Mappers.getMapper(BpmTaskConvert.class);


    /**
     * 复制对象
     *
     * @param source 源 要复制的对象
     * @param target 目标 复制到此对象
     * @param <T>
     *
     * @return
     */
    public static <T> T copy(Object source, Class<T> target) {
        if (source == null || target == null) {
            return null;
        }
        try {
            T newInstance = target.newInstance();
            BeanUtils.copyProperties(source, newInstance);
            return newInstance;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    default <T, K> List<K> copyList(List<T> source, Class<K> target) {
        if (null == source || source.isEmpty()) {
            return Collections.emptyList();
        }
        return source.stream().map(e -> copy(e, target)).collect(Collectors.toList());
    }

    default List<BpmTaskTodoPageItemRespVO> convertList1(List<Task> tasks,
        Map<String, ProcessInstance> processInstanceMap, Map<Long, AdminUserRespDTO> userMap,
         Map<String, BpmProcessInstanceExtDO> processInstanceExtDOMap) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmTaskTodoPageItemRespVO respVO = convert1(task);
            ProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
            BpmProcessInstanceExtDO processInstanceExtDO = processInstanceExtDOMap.get(task.getProcessInstanceId());
            if (processInstance != null) {
                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(processInstance.getStartUserId()));
                respVO.setProcessInstance(convert(processInstance, startUser));
            }
            if(processInstanceExtDO != null){
                respVO.setCategory(processInstanceExtDO.getCategory());
            }
            respVO.setCreateTime(task.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            return respVO;
        });
    }

    @Mapping(source = "suspended", target = "suspensionState", qualifiedByName = "convertSuspendedToSuspensionState")
    BpmTaskTodoPageItemRespVO convert1(Task bean);

    default List<BpmOATaskDonePageItemRespVO> convertList5(List<HistoricTaskInstance> tasks,
                                                           Map<String, BpmTaskExtDO> bpmTaskExtDOMap, Map<String, HistoricProcessInstance> historicProcessInstanceMap,
                                                           Map<Long, AdminUserRespDTO> userMap,
                                                           Map<String, BpmProcessInstanceExtDO> ProcessInstanceExtDOMap) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmOATaskDonePageItemRespVO respVO = convert5(task);
            BpmTaskExtDO taskExtDO = bpmTaskExtDOMap.get(task.getId());
            BpmProcessInstanceExtDO ProcessInstanceExtDO = ProcessInstanceExtDOMap.get(task.getProcessInstanceId());
            copyTo1(taskExtDO, respVO);
            HistoricProcessInstance processInstance = historicProcessInstanceMap.get(task.getProcessInstanceId());
            if (processInstance != null) {
                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(processInstance.getStartUserId()));
                respVO.setProcessInstance(convert(processInstance, startUser));
            }

            if(ProcessInstanceExtDO != null){
                respVO.setCategory(ProcessInstanceExtDO.getCategory());
                respVO.setStatus(ProcessInstanceExtDO.getStatus());
            }
            else {
                return null;
            }

            respVO.setCreateTime(ProcessInstanceExtDO.getCreateTime());

            return respVO;
        });
    }

    BpmOATaskDonePageItemRespVO convert5(HistoricTaskInstance bean);

    @Mapping(target = "id", ignore = true)
    void copyTo1(BpmTaskExtDO from, @MappingTarget BpmOATaskDonePageItemRespVO to);

    @Mapping(source = "suspended", target = "suspensionState", qualifiedByName = "convertSuspendedToSuspensionState")
    BpmOATaskTodoPageItemRespVO convert4(Task bean);

    default List<BpmOATaskTodoPageItemRespVO> convertList4(List<Task> tasks,
                                                           Map<String, ProcessInstance> processInstanceMap, Map<Long, AdminUserRespDTO> userMap,
                                                           Map<String, BpmProcessInstanceExtDO> processInstanceExtDOMap) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmOATaskTodoPageItemRespVO respVO = convert4(task);
            ProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
            BpmProcessInstanceExtDO processInstanceExtDO = processInstanceExtDOMap.get(task.getProcessInstanceId());

            if (processInstance != null) {
                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(processInstance.getStartUserId()));
                respVO.setProcessInstance(convert(processInstance, startUser));
            }

            if(processInstanceExtDO != null){
                respVO.setCategory(processInstanceExtDO.getCategory());
                respVO.setStatus(processInstanceExtDO.getStatus());
            }
            else {
                return null;
            }

            assert processInstance != null;
            respVO.setCreateTime(LocalDateTime.ofInstant(processInstance.getStartTime().toInstant(),ZoneId.systemDefault()));
            return respVO;
        });
    }

    @Named("convertSuspendedToSuspensionState")
    default Integer convertSuspendedToSuspensionState(boolean suspended) {
        return suspended ? SuspensionState.SUSPENDED.getStateCode() : SuspensionState.ACTIVE.getStateCode();
    }

    default List<BpmTaskDonePageItemRespVO> convertList2(List<HistoricTaskInstance> tasks,
        Map<String, BpmTaskExtDO> bpmTaskExtDOMap, Map<String, HistoricProcessInstance> historicProcessInstanceMap,
        Map<Long, AdminUserRespDTO> userMap,
        Map<String, BpmProcessInstanceExtDO> ProcessInstanceExtDOMap) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmTaskDonePageItemRespVO respVO = convert2(task);
            BpmTaskExtDO taskExtDO = bpmTaskExtDOMap.get(task.getId());
            BpmProcessInstanceExtDO ProcessInstanceExtDO = ProcessInstanceExtDOMap.get(task.getProcessInstanceId());
            copyTo(taskExtDO, respVO);
            HistoricProcessInstance processInstance = historicProcessInstanceMap.get(task.getProcessInstanceId());
            if (processInstance != null) {
                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(processInstance.getStartUserId()));
                respVO.setProcessInstance(convert(processInstance, startUser));
            }
            respVO.setCategory(ProcessInstanceExtDO.getCategory());
            return respVO;
        });
    }

    BpmTaskDonePageItemRespVO convert2(HistoricTaskInstance bean);

    @Mappings({@Mapping(source = "processInstance.id", target = "id"),
        @Mapping(source = "processInstance.name", target = "name"),
        @Mapping(source = "processInstance.startUserId", target = "startUserId"),
        @Mapping(source = "processInstance.processDefinitionId", target = "processDefinitionId"),
        @Mapping(source = "startUser.nickname", target = "startUserNickname")})
    ProcessInstanceDTO convert(ProcessInstance processInstance, AdminUserRespDTO startUser);

    default List<BpmTaskRespVO> convertList3(List<HistoricTaskInstance> tasks,
        Map<String, BpmTaskExtDO> bpmTaskExtDOMap, HistoricProcessInstance processInstance,
        Map<Long, AdminUserRespDTO> userMap, Map<Long, DeptRespDTO> deptMap) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmTaskRespVO respVO = convert3(task);
            BpmTaskExtDO taskExtDO = bpmTaskExtDOMap.get(task.getId());
            copyTo(taskExtDO, respVO);
            respVO.setImageUrl(taskExtDO.getImageUrl());
            if (processInstance != null) {
                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(processInstance.getStartUserId()));
                respVO.setProcessInstance(convert(processInstance, startUser));
            }
            AdminUserRespDTO assignUser = userMap.get(NumberUtils.parseLong(task.getAssignee()));
            if (assignUser != null) {
                respVO.setAssigneeUser(convert3(assignUser));
                DeptRespDTO dept = deptMap.get(assignUser.getDeptId());
                if (dept != null) {
                    respVO.getAssigneeUser().setDeptName(dept.getName());
                }
            }
            return respVO;
        });
    }

    @Mapping(source = "taskDefinitionKey", target = "definitionKey")
    BpmTaskRespVO convert3(HistoricTaskInstance bean);

    UserDTO convert3(AdminUserRespDTO bean);

    @Mapping(target = "id", ignore = true)
    void copyTo(BpmTaskExtDO from, @MappingTarget BpmTaskDonePageItemRespVO to);

    @Mappings({@Mapping(source = "processInstance.id", target = "id"),
        @Mapping(source = "processInstance.name", target = "name"),
        @Mapping(source = "processInstance.startUserId", target = "startUserId"),
        @Mapping(source = "processInstance.processDefinitionId", target = "processDefinitionId"),
        @Mapping(source = "startUser.nickname", target = "startUserNickname")})
    ProcessInstanceDTO convert(HistoricProcessInstance processInstance,
        AdminUserRespDTO startUser);

    default BpmTaskExtDO convert2TaskExt(Task task) {
        BpmTaskExtDO taskExtDO = new BpmTaskExtDO().setTaskId(task.getId())
                .setTaskDefKey(task.getTaskDefinitionKey())
            .setAssigneeUserId(NumberUtils.parseLong(task.getAssignee())).setName(task.getName())
            .setProcessDefinitionId(task.getProcessDefinitionId()).setProcessInstanceId(task.getProcessInstanceId());
        taskExtDO.setCreateTime(LocalDateTimeUtil.of(task.getCreateTime()));
        return taskExtDO;
    }

    BpmTaskExtDO convertToTaskExtDO(BpmTaskExtDTO taskExtDTO);

    default BpmMessageSendWhenTaskCreatedReqDTO convert(ProcessInstance processInstance, AdminUserRespDTO startUser,
        Task task) {
        BpmMessageSendWhenTaskCreatedReqDTO reqDTO = new BpmMessageSendWhenTaskCreatedReqDTO();
        reqDTO.setProcessInstanceId(processInstance.getProcessInstanceId())
            .setProcessInstanceName(processInstance.getName()).setStartUserId(startUser.getId())
            .setStartUserNickname(startUser.getNickname()).setTaskId(task.getId()).setTaskName(task.getName())
            .setAssigneeUserId(NumberUtils.parseLong(task.getAssignee()));
        return reqDTO;
    }

}
