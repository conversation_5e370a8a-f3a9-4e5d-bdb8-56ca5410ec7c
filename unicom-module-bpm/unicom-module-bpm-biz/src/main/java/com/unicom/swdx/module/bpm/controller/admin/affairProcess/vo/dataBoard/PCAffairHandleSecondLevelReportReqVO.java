package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;


import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("政务子系统 - 政务办理PC二级报表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PCAffairHandleSecondLevelReportReqVO extends PageParam {

    @ApiModelProperty(value = "办结日期-开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date startDate;

    @ApiModelProperty(value = "办结日期-结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date endDate;

    @ApiModelProperty(value = "处室ID")
    private Long deptId;
    @ApiModelProperty(value = "路由列")
    @NotNull
    private String routeColumn;
}
