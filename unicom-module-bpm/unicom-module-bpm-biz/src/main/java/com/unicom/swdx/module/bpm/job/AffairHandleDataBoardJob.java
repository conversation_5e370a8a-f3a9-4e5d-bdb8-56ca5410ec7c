package com.unicom.swdx.module.bpm.job;

import com.unicom.swdx.framework.tenant.core.job.TenantJob;
import com.unicom.swdx.module.bpm.service.dataBoard.AffairHandleDataBoardServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Component
@TenantJob
public class AffairHandleDataBoardJob {


    @Resource
    AffairHandleDataBoardServiceImpl affairHandleDataBoardService;

    @XxlJob("affairHandleDataBoard")
    public void affairHandleDataBoard()
    {
        LocalDate endDate = LocalDate.now().plusDays(-1);
        XxlJobHelper.log("PC政务办理办理报表：正在对日期：{} 办结的事项进行报表统计",endDate);
        affairHandleDataBoardService.saveCountResultOnTheDay(endDate);
    }
}
