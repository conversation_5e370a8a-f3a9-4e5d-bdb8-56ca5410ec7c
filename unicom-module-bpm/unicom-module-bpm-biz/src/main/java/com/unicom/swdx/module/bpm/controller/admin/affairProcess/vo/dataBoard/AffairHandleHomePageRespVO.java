package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("政务子系统 - 政务办理报表首页 Response VO")
@Data
public class AffairHandleHomePageRespVO {

    @ApiModelProperty(value = "处室ID")
    private Long deptId;

    @ApiModelProperty(value = "处室名称")
    private String deptName;


    @ApiModelProperty(value = "办理总数")
    private Integer totalHandle;

    @ApiModelProperty(value = "好评数")
    private Integer favourableComment;

    @ApiModelProperty(value = "差评数")
    private Integer negativeComment;

    @ApiModelProperty(value = "督办数")
    private Integer supervision;

    @ApiModelProperty(value = "逾期数")
    private Integer overdue;

    @ApiModelProperty(value = "行政许可")
    private Integer permit;

    @ApiModelProperty(value = "行政确认")
    private Integer confirmation;


    @ApiModelProperty(value = "行政奖励")
    private Integer award;


    @ApiModelProperty(value = "其他行政权力")
    private Integer other;

}
