package com.unicom.swdx.module.bpm.service.definition;

import cn.hutool.core.lang.Assert;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.json.JsonUtils;
import com.unicom.swdx.framework.common.util.validation.ValidationUtils;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormCreateReqVO;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormPageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.BpmEFormUpdateReqVO;
import com.unicom.swdx.module.bpm.convert.definition.BpmEFormConvert;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmEFormDO;
import com.unicom.swdx.module.bpm.dal.mysql.definition.BpmEFormMapper;
import com.unicom.swdx.module.bpm.enums.ErrorCodeConstants;
import com.unicom.swdx.module.bpm.service.definition.dto.BpmFormFieldRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.*;

/**
 * 动态表单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BpmEFormServiceImpl implements BpmEFormService {

    @Resource
    private BpmEFormMapper formMapper;

    @Override
    public Long createForm(BpmEFormCreateReqVO createReqVO) {
        this.checkFields(createReqVO.getFields());
        // 插入
        BpmEFormDO form = BpmEFormConvert.INSTANCE.convert(createReqVO);
        formMapper.insert(form);
        // 返回
        return form.getId();
    }

    @Override
    public void updateForm(BpmEFormUpdateReqVO updateReqVO) {
        this.checkFields(updateReqVO.getFields());
        // 校验存在
        this.validateFormExists(updateReqVO.getId());
        // 更新
        BpmEFormDO updateObj = BpmEFormConvert.INSTANCE.convert(updateReqVO);
        formMapper.updateById(updateObj);
    }

    @Override
    public void deleteForm(Long id) {
        // 校验存在
        this.validateFormExists(id);
        //// 校验表单是否被流程模型引用
        //this.validateFormUsed(id);
        // 删除
        formMapper.deleteById(id);
    }

    //private void validateFormUsed(Long id) {
    //    if (formMapper.selectModelCountById(id) > 0) {
    //        throw exception(FORM_USED);
    //    }
    //}

    private void validateFormExists(Long id) {
        if (formMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.FORM_NOT_EXISTS);
        }
    }

    @Override
    public BpmEFormDO getForm(Long id) {
        return formMapper.selectById(id);
    }

    @Override
    public List<BpmEFormDO> getFormList() {
        return formMapper.selectList(BpmEFormDO::getStatus,0);
    }

    @Override
    public List<BpmEFormDO> getFormList(Collection<Long> ids) {
        return formMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BpmEFormDO> getFormPage(BpmEFormPageReqVO pageReqVO) {
        return formMapper.selectPage(pageReqVO);
    }


//   @Override
//   public BpmFormDO checkFormConfig(String configStr) {
//       BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(configStr, BpmModelMetaInfoRespDTO.class);
//       if (metaInfo == null || metaInfo.getFormType() == null) {
//           throw exception(MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG);
//       }
//       // 校验表单存在
//       if (Objects.equals(metaInfo.getFormType(), BpmModelFormTypeEnum.NORMAL.getType())) {
//           BpmFormDO form = getForm(metaInfo.getFormId());
//           if (form == null) {
//               throw exception(FORM_NOT_EXISTS);
//           }
//           return form;
//       }
//       return null;
//   }

    private void checkKeyNCName(String key) {
        if (!ValidationUtils.isXmlNCName(key)) {
            throw exception(MODEL_KEY_VALID);
        }
    }
    /**
     * 校验 Field，避免 field 重复
     *
     * @param fields field 数组
     */
    private void checkFields(List<String> fields) {
        Map<String, String> fieldMap = new HashMap<>(); // key 是 vModel，value 是 label
        for (String field : fields) {
            BpmFormFieldRespDTO fieldDTO = JsonUtils.parseObject(field, BpmFormFieldRespDTO.class);
            Assert.notNull(fieldDTO);
            String oldLabel = fieldMap.put(fieldDTO.getVModel(), fieldDTO.getLabel());
            // 如果不存在，则直接返回
            if (oldLabel == null) {
                continue;
            }
            // 如果存在，则报错
            throw exception(ErrorCodeConstants.FORM_FIELD_REPEAT, oldLabel, fieldDTO.getLabel(), fieldDTO.getVModel());
        }
    }

}
