package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AffairHandlePCReportRespVO {
    @ApiModelProperty(value = "处室ID", required = true)
    private Long deptId;

    @ApiModelProperty(value = "处室名称")
    private String deptName;


    @ApiModelProperty(value = "办理总数")
    private Integer totalHandle;


    @ApiModelProperty(value = "督办数")
    private Integer supervision;

    @ApiModelProperty(value = "逾期数")
    private Integer overdue;


    @ApiModelProperty(value = "非常满意")
    private Integer verySatisfied;


    @ApiModelProperty(value = "满意")
    private Integer satisfied;


    @ApiModelProperty(value = "基本满意")
    private Integer basicallySatisfied;


    @ApiModelProperty(value = "不满意")
    private Integer dissatisfied;


    @ApiModelProperty(value = "非常不满意")
    private Integer veryDissatisfied;


    //@ApiModelProperty(value = "按满意度总数")
    //private Integer totalSatisfaction;

    @ApiModelProperty(value = "行政许可")
    private Integer permit;


    @ApiModelProperty(value = "行政确认")
    private Integer confirmation;


    @ApiModelProperty(value = "行政奖励")
    private Integer award;


    @ApiModelProperty(value = "其他行政权力")
    private Integer other;


    @ApiModelProperty(value = "评分")
    private Float grade;

    public AffairHandlePCReportRespVO()
    {
        this.setTotalHandle(0);
        this.setSupervision(0);
        this.setOverdue(0);
        this.setPermit(0);
        this.setConfirmation(0);
        this.setAward(0);
        this.setOther(0);
        this.setVerySatisfied(0);
        this.setSatisfied(0);
        this.setBasicallySatisfied(0);
        this.setDissatisfied(0);
        this.setVeryDissatisfied(0);


    }
}
