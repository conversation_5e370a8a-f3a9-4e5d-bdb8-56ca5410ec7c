package com.unicom.swdx.module.bpm.controller.admin.oa.vo;

import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstancePageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel("管理后台 - 流程任务的 我发起 的分页项 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmOAProcessInstancePageItemRespVO extends BpmProcessInstancePageItemRespVO {

    private List<BpmTaskRespVO> infos;
}
