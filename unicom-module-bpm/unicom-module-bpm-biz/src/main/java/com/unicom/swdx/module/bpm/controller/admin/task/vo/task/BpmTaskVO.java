package com.unicom.swdx.module.bpm.controller.admin.task.vo.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/13  10:55
 */
@Data
@ApiModel("工作流任务相关--请求参数")
public class BpmTaskVO {

    @ApiModelProperty("任务Id")
    private String taskId;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID",required = true)
    private String processInstanceId ;

    @ApiModelProperty("用户Id")
    private String userId;

    @ApiModelProperty("任务意见")
    private String comment;

    @ApiModelProperty("流程实例Id")
    private String instanceId;

    @ApiModelProperty("节点")
    private String targetKey;

    @ApiModelProperty("流程变量信息")
    private Map<String, Object> values;

    //前端一直发的都是variables.不好改,所以BpmTaskVO新建一个variables
    @ApiModelProperty("流程变量信息2")
    private Map<String, Object> variables;

    @ApiModelProperty("审批人")
    private String assignee;

    @ApiModelProperty("候选人")
    private List<String> candidateUsers;

    @ApiModelProperty("审批组")
    private List<String> candidateGroups;

    @ApiModelProperty("任务节点")
    private String taskDefKey;



}
