package com.unicom.swdx.module.bpm.controller.admin.external.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("办公OA - 审批中心 下一任务审批人 Request VO")
public class BpmSubmitApprovalsReqVO {
    @ApiModelProperty("当前处理人的用户id")
    private Long userId;
    @ApiModelProperty(value = "当前任务Id",required = true)
    private String taskId;
    @ApiModelProperty("选择的下一任务审批人列表")
    private List<Long> userIds;
    @ApiModelProperty(value = "审批方式 1顺序 2会签 3或签",required = true)
    private String chargeLeaderSeq;
    @ApiModelProperty("按需保存流程节点中需要记录的日志")
    private String logParameters;
}
