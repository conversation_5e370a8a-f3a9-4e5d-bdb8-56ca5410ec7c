package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import com.unicom.swdx.framework.common.pojo.PageResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AffairHandleAppReportSummaryVO {


    @ApiModelProperty(value = "报表列表")
    PageResult<AffairHandleSecondLevelReportBaseVO> reportList;

    @ApiModelProperty(value = "总数")
    Integer total;

    public AffairHandleAppReportSummaryVO()
    {
        this.reportList = new PageResult<>();
    }
}
