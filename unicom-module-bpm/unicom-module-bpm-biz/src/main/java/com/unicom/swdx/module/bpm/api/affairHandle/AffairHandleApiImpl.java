//package com.unicom.swdx.module.bpm.api.affairHandle;
//
//import cn.hutool.core.bean.BeanUtil;
//import com.unicom.swdx.framework.common.pojo.CommonResult;
//import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
//import com.unicom.swdx.module.bpm.api.affairHandle.dto.BpmProcessInstanceCreateDTO;
//import com.unicom.swdx.module.bpm.api.affairHandle.dto.BpmProcessInstanceExtDTO;
//import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCreateReqVO;
//import com.unicom.swdx.module.bpm.convert.task.BpmProcessInstanceConvert;
//import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
//import com.unicom.swdx.module.bpm.dal.mysql.task.BpmProcessInstanceExtMapper;
//import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
//import com.unicom.swdx.module.bpm.service.definition.BpmProcessDefinitionService;
//import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
//import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.flowable.engine.repository.ProcessDefinition;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
//import java.util.Map;
//
//import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
//import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
//import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
//import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;
//
//@RestController // 提供 RESTful API 接口，给 Feign 调用
//@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
//@Validated
//public class AffairHandleApiImpl implements AffairHandleApi {
//
//    @Resource
//    private BpmProcessDefinitionService bpmProcessDefinitionService;
//    @Resource
//    private BpmProcessInstanceService processInstanceService;
//    @Resource
//    private BpmTaskService taskService;
//    @Resource
//    private BpmProcessInstanceExtMapper processInstanceExtMapper;
//    @Resource
//    private BpmProcessDefinitionService processDefinitionService;
//
//    @Override
//    public CommonResult<String> createProcess(Long userId,BpmProcessInstanceCreateDTO dto) {
//        ProcessDefinition processDefinition = bpmProcessDefinitionService.getActiveProcessDefinition(dto.getProcessDefinitionKey());
//        if (processDefinition == null) {
//            return error(400, "流程实例未找到");
//        }
//        dto.setProcessDefinitionId(processDefinition.getId());
//        BpmProcessInstanceCreateReqVO CreateReqVO = BpmProcessInstanceConvert.INSTANCE.convert0(dto);
//
//        // 发起流程
//        CreateReqVO.setProcessDefinitionId(processDefinition.getId());
//        String instanceId = processInstanceService.createProcessInstance(userId,CreateReqVO);
//        //保存发起日志
//        taskService.saveCreateHandleLog1(userId,CreateReqVO,instanceId);
////        taskService.saveCreateHandleLog1(getLoginUserId(),CreateReqVO,instanceId);
//
//        // 跳过第一个申请节点
//        processInstanceService.skipFirstTask(instanceId);
//        //保存发起日志
//        taskService.saveCreateHandleLog(userId,CreateReqVO,instanceId);
////        taskService.saveCreateHandleLog(getLoginUserId(),CreateReqVO,instanceId);
//        //推送第一个节点的待办
//        taskService.pushNewTodoToMidOffice(instanceId,dto.getProcessDefinitionKey(), BpmProcessInstanceResultEnum.PROCESS.getResult());
//        // 更新bpm表记录
//        processInstanceExtMapper.updateProcessInstanceExtHandleInstanceCode(instanceId, dto.getHandleInstanceCode());
//        return success(instanceId);
//    }
//
//    @Override
//    public BpmProcessInstanceExtDTO getDetailByInstanceCode(String instanceCode) {
//        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectOne(new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
//                .eq(BpmProcessInstanceExtDO::getHandleInstanceCode, instanceCode)
//                .eq(BpmProcessInstanceExtDO::getDeleted, 0)
//                .orderByDesc(BpmProcessInstanceExtDO::getCreateTime)
//                .last("limit 1"));
//        if (bpmProcessInstanceExtDO != null) {
//            BpmProcessInstanceExtDTO bpmProcessInstanceExtDTO = new BpmProcessInstanceExtDTO();
//            BeanUtil.copyProperties(bpmProcessInstanceExtDO, bpmProcessInstanceExtDTO);
//            return bpmProcessInstanceExtDTO;
//        }
//        return null;
//    }
//
//    @Override
//    public CommonResult<Map<String, Object>> getHandleCount(Long userId) {
//        Map<String,Object> handleTaskCount = taskService.getHandleCount(userId);
//        return success(handleTaskCount);
//    }
//
//    @Override
//    public CommonResult<String> createIneractionProcess(Long userId, BpmProcessInstanceCreateDTO dto) {
//        ProcessDefinition processDefinition = bpmProcessDefinitionService.getActiveProcessDefinition(dto.getProcessDefinitionKey());
//        if (processDefinition == null) {
//            return error(400, "流程实例未找到");
//        }
//        BpmProcessInstanceCreateReqVO bpmProcessInstanceCreateReqVO = new BpmProcessInstanceCreateReqVO();
//        BeanUtil.copyProperties(dto, bpmProcessInstanceCreateReqVO);
//        bpmProcessInstanceCreateReqVO.setProcessDefinitionId(processDefinition.getId());
//        String processInstanceId = processInstanceService.createProcessInstance(userId, bpmProcessInstanceCreateReqVO);
//        //保存发起日志
//        taskService.saveCreateHandleLog(userId, bpmProcessInstanceCreateReqVO, processInstanceId);
//        return success(processInstanceId);
//    }
//}
