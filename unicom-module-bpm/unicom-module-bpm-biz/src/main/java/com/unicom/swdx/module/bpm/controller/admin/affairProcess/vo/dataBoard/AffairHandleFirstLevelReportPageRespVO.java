package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ApiModel("获得一级报表统计页面 Response VO")
@Data
@ToString(callSuper = true)
public class AffairHandleFirstLevelReportPageRespVO {
    @ApiModelProperty(value = "各个处室统计明细")
    List<AffairHandleReportRespVO> statisticalDetails;

    @ApiModelProperty(value = "总计")
    AffairHandleFirstLevelReportSummaryVO summary;

    @ApiModelProperty(value = "是否局领导")
    Boolean isBureauLeaderRole;

    public AffairHandleFirstLevelReportPageRespVO(){
        this.statisticalDetails  = new ArrayList<>(Collections.emptyList());
        this.summary = new AffairHandleFirstLevelReportSummaryVO();
    }
}
