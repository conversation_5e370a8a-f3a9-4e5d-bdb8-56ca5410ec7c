package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
@ApiModel(value = "政务子系统 - 政务办理PC二级报表 Excel 导出 Request VO", description = "参数和 PCAffairHandleSecondLevelReportRespVO 是一致的")
@Data
public class PCAffairHandleSecondLevelReportListReqVO {
    @ApiModelProperty(value = "办结日期-开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date startDate;

    @ApiModelProperty(value = "办结日期-结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date endDate;

    @ApiModelProperty(value = "处室ID")
    private Long deptId;
    @ApiModelProperty(value = "路由列")
    @NotNull
    private String routeColumn;


    public static String getNameByRoute(String routeColumn)
    {
        switch (routeColumn)
        {
            case "totalHandle":
            {
                return "办理总数";
            }
            case "supervision":
            {
                return "督办件数";
            }
            case "overdue":
            {
                return "逾期件数";
            }
            case "verySatisfied":
            {
                return "非常满意";
            }
            case "satisfied":
            {
                return "满意";
            }
            case "basicallySatisfied":
            {
                return "基本满意";
            }
            case "dissatisfied":
            {
                return "不满意";
            }
            case "veryDissatisfied":
            {
                return "非常不满意";
            }
            case "award":
            {
                return "行政奖励";
            }
            case "confirmation":
            {
                return "行政确认";
            }
            case "permit":
            {
                return "行政许可";
            }
            case "other":
            {
                return "其他行政权力";
            }
            case "grade":
            {
                return "评分";
            }
            default:{
                return "";
            }
        }


    }
}
