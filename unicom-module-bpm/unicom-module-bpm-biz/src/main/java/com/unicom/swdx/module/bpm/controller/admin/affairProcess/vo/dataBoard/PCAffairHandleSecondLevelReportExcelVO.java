package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import static com.unicom.swdx.module.bpm.enums.DictTypeConstants.AFFAIR_BUSINESS_TYPE;
import static com.unicom.swdx.module.bpm.enums.DictTypeConstants.AFFAIR_SERVICE_EVALUATION_SATISFACTION_DEGREE;


@ApiModel("政务子系统 - 二级政务办理报表PC导出 Response VO")
@Data
public class PCAffairHandleSecondLevelReportExcelVO {



    @ExcelProperty(value = "服务事项")
    private String name;

    @ExcelProperty(value = "事项类型",converter = DictConvert.class)
    @DictFormat(AFFAIR_BUSINESS_TYPE)
    private String processType;

    @ExcelProperty(value = "申请单位/人")
    private String applyEnterpriseName;

    @ExcelProperty(value = "申请日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String applyTime;

    @ExcelProperty(value = "办结日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String handleTime;


    @ExcelProperty(value = "督办次数")
    private Integer superviseCount;

    @ExcelProperty(value = "状态")
    private String status;

    @ExcelProperty(value = "满意度",converter = DictConvert.class)
    @DictFormat(AFFAIR_SERVICE_EVALUATION_SATISFACTION_DEGREE)
    private Integer satisfactionDegree;

    @ExcelIgnore
    private Float grade;
}
