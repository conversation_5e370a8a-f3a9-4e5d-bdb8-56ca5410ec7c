package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("政务子系统 - 审批中心的 待审批/已审批/我发起 的分页项 Response VO")
@Data
public class BpmTaskPageItemRespVO {

    @ApiModelProperty(value = "任务编号", required = true, example = "1024")
    private String taskId;

    @ApiModelProperty(value = "任务名字", required = true, example = "sk")
    private String taskName;

    @ApiModelProperty(value = "流程实例编号", required = true, example = "1024")
    private String processInstanceId;

    @ApiModelProperty(value = "流程实例名称", required = true, example = "sk")
    private String processInstanceName;

    @ApiModelProperty(value = "流程实例标识", required = true, example = "sk")
    private String processInstanceKey;

    @ApiModelProperty(value = "流程发起人的用户编号", required = true, example = "1024")
    private Long startUserId;

    @ApiModelProperty(value = "流程发起人的用户昵称", required = true, example = "芋艿")
    private String startUserNickname;

    @ApiModelProperty(value = "任务审批人的用户编号", required = true, example = "1024")
    private String assigneeUserId;

    @ApiModelProperty(value = "任务审批人的用户昵称", required = true, example = "芋艿")
    private String assigneeUserNickname;

    @ApiModelProperty(value = "该节点的任务审批人的用户编号列表", required = true)
    private List<Long> assigneeUserIds;

    @ApiModelProperty(value = "该节点的任务审批人的用户编号列表", required = true)
    private List<String> assigneeUserNickNames;

    @ApiModelProperty(value = "该节点的任务审批人的组织列表", required = true)
    private List<String> assigneeUserDeptNames;

    @ApiModelProperty(value = "任务审批人的所属组织", required = true)
    private String assigneeUserDeptName;

    @ApiModelProperty(value = "任务结果", required = true)
    private Integer taskResult;

    @ApiModelProperty(value = "流程结果", required = true)
    private Integer processInstanceResult;

    @ApiModelProperty(value = "流程状态标识 1 撤回  2 是驳回 3 补正材料 4 不予办理")
    private String flowFlag;

    @ApiModelProperty(value = "流程发起时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime processInstanceStartTime;

    @ApiModelProperty(value = "任务创建时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime taskStartTime;

    @ApiModelProperty(value = "任务结束时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime taskEndTime;

    @ApiModelProperty(value = "任务节点")
    private String taskDefKey;

    @ApiModelProperty(value = "督办/提醒状态")
    private String superviseStatus;

    @ApiModelProperty(value = "摘要信息")
    private Map<String,Object> approvalVariables;

    @ApiModelProperty(value = "事项类型")
    private String processType;

    @ApiModelProperty(value = "申请单位/人")
    private String applyEnterpriseName;

    @ApiModelProperty(value = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "会议取消标识  1 取消")
    private String meetingCancelFlag;

    @ApiModelProperty(value = "查询结果类型")
    private String queryCategory;
}
