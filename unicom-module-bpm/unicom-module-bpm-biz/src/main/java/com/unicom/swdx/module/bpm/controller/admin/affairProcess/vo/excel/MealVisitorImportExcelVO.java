package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import jodd.util.StringUtil;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import javax.validation.constraints.NotBlank;

/**
 * 接待申请-来访人员信息表 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
@HeadRowHeight(80)
public class MealVisitorImportExcelVO {

    public static final String notice = "填写须知： \n" +
            "1、不能增删模版已有内容\n" +
            "2、性别：下拉框，下拉框选项为男、女\n" +
            "3、联系电话：11位整数\n" +
            "4、姓名不能重复，相同时则提示人员重复";
    @ExcelProperty(value = {notice,"姓名\n（必填）"}, index = 0)
    @NotBlank(message = "姓名不能为空")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty(value = {notice,"用户性别\n（必选）"}, index = 1)
    @NotBlank(message = "性别不能为空")
    private String sex;

    @ExcelProperty(value = {notice,"联系电话\n（必填）"}, index = 2)
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    @ExcelProperty(value = {notice,"身份证号"}, index = 3)
    @ContentStyle(dataFormat = 49)
    private String identity;

    @ExcelProperty(value = {notice,"所属单位\n（必填）"}, index = 4)
    @NotBlank(message = "所属单位不能为空")
    private String organ;

    @ExcelProperty(value = {notice,"职位"}, index = 5)
    private String work;

    public static int getHeadHeight(){
        return (StringUtil.count(notice,"\n")+2)*15;
    }

    public static HorizontalCellStyleStrategy getMyCellStyle(){
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        // 颜色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 10);
        // 字体
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setWrapped(true);

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置内容靠中对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现


        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    public static SheetWriteHandler getSelectListSheetWriteHandler(){
        return new SheetWriteHandler() {
            @Override
            public void afterSheetCreate(SheetWriteHandlerContext context) {
                Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
                Sheet sheet = context.getWriteSheetHolder().getSheet();
                System.out.println(context.getWriteSheetHolder().getSheetNo()+"写入成功");

                // 区间设置 第一列第一行和第二行的数据。由于第一行是头，所以第一、二行的数据实际上是第二三行
                //设置性别下拉框
                try {
                    //设置下拉选项
                    Integer sexIndex = MealVisitorImportExcelVO.class.getDeclaredField("sex").getDeclaredAnnotation(ExcelProperty.class).index();
                    CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(2, 10001, sexIndex, sexIndex);
                    this.getClass().getDeclaredFields();
                    DataValidationHelper helper = context.getWriteSheetHolder().getSheet().getDataValidationHelper();
                    DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[] {"男", "女"});
                    //设置约束
                    DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
                    dataValidation.setSuppressDropDownArrow(true);
                    dataValidation.setShowErrorBox(true);
                    dataValidation.createErrorBox("提示", "此值与单元格定义格式不一致");
                    dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                    sheet.addValidationData(dataValidation);
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException(e);
                }
            }
        };
    }


}
