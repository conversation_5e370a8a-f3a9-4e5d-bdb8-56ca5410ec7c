package com.unicom.swdx.module.bpm.dal.mysql.affairProcess;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface AffairHandleDataBoardMapper {


    /**
     * 根据类型查询政务办理计数
     *
     * @param reqVO
     * @return 该类型政务办理计数
     */
    Integer selectHandleCountByType(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptIds") List<Long> deptIds);

    /**
     * 根据满意度查询政务办理计数
     *
     * @param reqVO
     * @return 该满意度政务办理计数
     */
    Integer selectHandleCountBySatisfactionDegree(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("satisfactionDegree") Integer satisfactionDegree, @Param("deptIds") List<Long> deptIds);

    /**
     * 根据督办逾期查询政务办理计数
     *
     * @param reqVO
     * @return 该督办逾期政务办理计数
     */
    Integer selectHandleCountBySupervision(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("superviseStatus") Integer superviseStatus, @Param("deptIds") List<Long> deptIds);

    /**
     * 获取所有政务办理相关部门
     *
     * @param reqVO
     * @return 所有相关部门
     */
    List<Long> selectHandleDeptList(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptIds") List<Long> deptIds);

    /**
     * 根据部门获取统计信息
     *
     * @param reqVO
     * @return 统计信息
     */
    AffairHandleReportRespVO selectHandleCountsByDept(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptId") Long deptId);

    /**
     * 根据部门获取二级报表统计信息
     *
     * @param reqVO
     * @return 二级报表统计信息
     */
    List<AffairHandleSecondLevelReportBaseVO> selectReportList(IPage page, @Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptIds") List<Long> deptIds);

    Float selectHandleCountGrade(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptIds") List<Long> deptIds);

    AffairHandleEvaluationRespVO getEvaluation(@Param("processInstanceId") String processInstanceId);

    List<Map> getPCPageCountByType(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptIds") List<Long> deptIds);

    List<Map> getPCPageCountBySatisfactionDegree(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("satisfactionDegree") Integer satisfactionDegree, @Param("deptIds") List<Long> deptIds);

    List<Map> getPCPageCountBySupervision(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("superviseStatus") Integer superviseStatus, @Param("deptIds") List<Long> deptIds);

    List<Map> getPCPageCountByGrade(@Param("reqVO") AffairHandleCountReqVO reqVO, @Param("typeSet") List<String> typeSet, @Param("deptIds") List<Long> deptIds);

    List<PCAffairHandleSecondLevelReportPageRespVO> selectPCSecondHandleCount(IPage page, @Param("reqVO") PCAffairHandleSecondLevelReportReqVO reqVO, @Param("typeSet") List<String> typeSet);

    List<PCAffairHandleSecondLevelReportPageRespVO> selectPCSecondHandleCount2(@Param("reqVO") PCAffairHandleSecondLevelReportListReqVO reqVO,@Param("typeSet")  List<String> typeSet);

    List<Long> selectTodayEndDeptIds(@Param("endTime") LocalDate endTime,@Param("typeSet")  List<String> typeSet);

    List<Map<String,Object>> selectEvaluation(@Param("evaluationTime") LocalDate evaluationTime);

    List<Long> selectProcessDeptIds(@Param("processInstanceId") String processInstanceId);

    List<AffairHandlePCReportRespVO> analyseReport(@Param("reqVO") AffairHandleCountReqVO reqVO,@Param("deptIds") List<Long> deptIds);

    List<AffairHandlePCReportExcelVO> analyseReportExport(@Param("reqVO") AffairHandleCountReqVO reqVO,@Param("deptIds") List<Long> deptIds);
}
