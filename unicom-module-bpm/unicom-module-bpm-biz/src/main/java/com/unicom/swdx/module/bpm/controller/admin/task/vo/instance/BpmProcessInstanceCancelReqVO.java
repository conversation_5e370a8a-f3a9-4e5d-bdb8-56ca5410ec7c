package com.unicom.swdx.module.bpm.controller.admin.task.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel("管理后台 - 流程实例的取消 Request VO")
@Data
public class BpmProcessInstanceCancelReqVO {

    @ApiModelProperty(value = "流程实例的编号", required = true, example = "1024")
    @NotEmpty(message = "流程实例的编号不能为空")
    private String processInstanceId;

    @ApiModelProperty(value = "取消原因,不需要记录原因则不传", example = "不请假了！")
    private String reason;

    @ApiModelProperty(value = "取消人的用户id", required = true, example = "1024")
    private Long userId;
}
