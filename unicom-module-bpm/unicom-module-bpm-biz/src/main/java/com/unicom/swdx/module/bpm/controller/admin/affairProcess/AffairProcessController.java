package com.unicom.swdx.module.bpm.controller.admin.affairProcess;

//import com.unicom.tyj.module.affair.api.ApprovalDraftApi;
//import com.unicom.tyj.module.affair.api.approval.meetingattendee.MeetingAttendeeApi;
//import com.unicom.tyj.module.affair.api.approval.meetingattendee.dto.MeetingAttendeeRespDTO;
//import com.unicom.tyj.module.affair.api.approval.meetingreserve.MeetingReserveApi;
//import com.unicom.tyj.module.affair.enums.AffairProcessTypeEnum;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
        import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

        import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.error;


@Api(tags = "政务子系统 - 审批中心")
@RestController
@RequestMapping("/bpm/affair-process")
@Validated
@Slf4j
public class AffairProcessController {

//    @Resource
//    private BpmTaskService taskService;
//
//    @Resource
//    private BpmProcessInstanceService processInstanceService;
//
//    @Resource
//    private BpmTaskService bpmTaskService;
//
//    @Resource
//    private BpmProcessDefinitionService bpmProcessDefinitionService;
//
//    @Resource
//    private ApprovalDraftApi approvalDraftApi;
//
//    @Resource
//    private MeetingReserveApi meetingReserveApi;
//
//    @Resource
//    private MeetingAttendeeApi meetingAttendeeApi;
//
//    @Resource
//    private BpmOALeaveService bpmOALeaveService;
//
//    @Resource
//    private FileApi fileApi;
//
//    @Resource
//    private AdminUserApi adminUserApi;
//
//    @GetMapping("todo-page")
//    @ApiOperation("获取 Todo 待审批分页")
////    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> getTodoTaskPage(@Valid BpmTaskPageReqVO pageReqVO) {
//        //审批状态拦截：待办页面，已同意/结束为0
//        if ("3".equals(pageReqVO.getApprovalStatus()) || "approved".equals(pageReqVO.getApprovalStatus())
//            || "6".equals(pageReqVO.getApprovalStatus()) || "end".equals(pageReqVO.getApprovalStatus())){
//            return success(new PageResult<>(Collections.emptyList(),0L));
//        }
//        PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getTodoAffairTaskPage(getLoginUserId(), pageReqVO);
//        return success(affairTaskPage);
//    }
//
//    @GetMapping("done-page")
//    @ApiOperation("获取 Done 已审批分页")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> getDoneTaskPage(@Valid BpmTaskPageReqVO pageReqVO) {
//        PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getDoneAffairTaskPage(getLoginUserId(), pageReqVO);
//        return success(affairTaskPage);
//    }
//
//
//    @GetMapping("start-page")
//    @ApiOperation("获取 我发起的流程分页")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> getStartTaskPage(BpmTaskPageReqVO pageReqVO) {
//        if ("3".equals(pageReqVO.getApprovalStatus()) || "approved".equals(pageReqVO.getApprovalStatus())){
//            pageReqVO.setApprovalStatus("end");//todo 我发起页面不需要已同意筛选，已同意暂时按照已结束去查
//        }
//        PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getStartAffairTaskPage(getLoginUserId(), pageReqVO);
//        return success(affairTaskPage);
//    }
//
//    //通过
//    @PostMapping("/create-process")
//    @ApiOperation("发起流程")
//    @PreAuthorize("@ss.hasPermission('bpm:task:create')")
//    public CommonResult<Boolean> createProcess(@Valid @RequestBody BpmProcessInstanceCreateReqVO bpmProcessInstanceCreateReqVO) {
//        ProcessDefinition processDefinition = bpmProcessDefinitionService.getActiveProcessDefinition(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey());
//        if (processDefinition == null) {
//            return error(400, "流程实例未找到");
//        }
//        Map<String, Object> map = bpmProcessInstanceCreateReqVO.getVariables();
//        if ( map != null && !Objects.isNull(map.get("id"))) {
//            Boolean result = approvalDraftApi.updateDraftStatus(Long.parseLong(map.get("id").toString())).getCheckedData();
//            if (!result) {
//                return success(false);
//            }
//        }
//        //网站发文的正文必填校验
//        if (AffairProcessTypeEnum.WEB_POST.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())) {
//            if (Objects.isNull(map.get("fileAttachments")) || StringUtils.isEmpty(map.get("fileAttachments").toString())) {
//                throw exception(FLOW_MAIN_BODY_NOT_EXISTS);
//            }
//            log.info(">>>>>>>>>>>>>>>>>>>>>>>网站发文附件处理");
//            JSONArray jsonArray = JSONArray.parseArray(map.get("fileAttachments").toString());
//            JSONArray array = new JSONArray();
//            Boolean flag = false;
//            for (int i =0;i<jsonArray.size();i++) {
//                JSONObject jsonObject = jsonArray.getJSONObject(i);
//                if ("main".equals(jsonObject.getString("bussiType"))) {
//                    JSONObject object = new JSONObject();
//                    String filePath = jsonObject.getString("originName");
//                    String path = fileApi.getFileUrl(filePath).getCheckedData();
//                    if (StringUtils.isEmpty(path)) {
//                        throw exception(FLOW_MAIN_BODY_NOT_EXISTS);
//                    }
//                    FileUpateReqDTO fileUpateReqDTO = new FileUpateReqDTO();
//                    fileUpateReqDTO.setName(jsonObject.getString("fileName"));
//                    fileUpateReqDTO.setPath(filePath);
//                    fileApi.uploadUpdateUploadFileName(fileUpateReqDTO);
//                    flag = true;
//                    object.put("originName", jsonObject.getString("originName"));
//                    object.put("filePath", path);
//                    object.put("fileName", jsonObject.getString("fileName"));
//                    object.put("fileType", jsonObject.getString("fileType"));
//                    object.put("bussiType", jsonObject.getString("bussiType"));
//                    array.add(object);
//                } else {
//                    array.add(jsonObject);
//                }
//            }
//            if (!flag) {
//                throw exception(FLOW_MAIN_BODY_NOT_EXISTS);
//            }
//            map.put("fileAttachments",array);
//        }
//        if (AffairProcessTypeEnum.METTING_RESERVATION.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())
//                || AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())) {
////            if (Objects.isNull(map.get("meetingRooms")) || StringUtils.isEmpty(map.get("meetingRooms").toString())) {
////                throw exception(MEETING_BODY_NOT_EXISTS);
////            }
//            if (!Objects.isNull(map.get("meetingRooms"))) {
//                log.info(">>>>>>>>>>>>>>>>>>>>>>>会议起止时间处理");
//
//                Object meetingRoomsObject = map.get("meetingRooms");
//                String meetingRoomsString = JSON.toJSONString(meetingRoomsObject);
//                JSONArray jsonArray = JSONArray.parseArray(meetingRoomsString);
//                List<LocalDateTime> startTimes = new ArrayList<>();
//                List<LocalDateTime> endTimes = new ArrayList<>();
//                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
//                for (int i = 0; i < jsonArray.size(); i++) {
//                    JSONObject meetingObj = jsonArray.getJSONObject(i);
//                    String date = meetingObj.getString("date");
//                    JSONArray timeRangeArray = meetingObj.getJSONArray("timeRange");
//
//                    for (int j = 0; j < timeRangeArray.size(); j++) {
//                        String timeRange = timeRangeArray.getString(j);
//                        String[] times = timeRange.split("-");
//                        startTimes.add(LocalDateTime.parse(date + " " + times[0], dateTimeFormatter));
//                        endTimes.add(LocalDateTime.parse(date + " " + times[1], dateTimeFormatter));
//                    }
//                }
//
//                startTimes.sort(Comparator.naturalOrder());
//                endTimes.sort(Comparator.naturalOrder());
//
//                LocalDateTime startDate = startTimes.get(0);
//                LocalDateTime endDate = endTimes.get(endTimes.size() - 1);
//                JSONArray times = new JSONArray();
//                times.add(startDate.format(dateTimeFormatter));
//                times.add(endDate.format(dateTimeFormatter));
//
//                map.put("time", times);
//                bpmProcessInstanceCreateReqVO.setVariables(map);
//            }
//            if (map.containsKey("approvals")) {
//                Integer approvals1 = (Integer) map.get("approvals");
//                List<Integer> approvals = Arrays.asList(approvals1);
//                map.put("approvals", approvals);
//                bpmProcessInstanceCreateReqVO.setVariables(map);
//            }
//        }
//
//        if (AffairProcessTypeEnum.VEHICLE_APPPLY.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())) {
//            List<Integer> participants = (List<Integer>)bpmProcessInstanceCreateReqVO.getVariables().get("participants");
//            List<Long> lds = participants.stream().map(a->Long.valueOf(a)).collect(Collectors.toList());
//            List<AdminUserRespDTO> adminUserRespDTO = adminUserApi.getUsers(lds).getCheckedData();
//            List<String> names = adminUserRespDTO.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
//            bpmProcessInstanceCreateReqVO.getVariables().put("participantsName",String.join(",", names));
//        }
//
//        bpmProcessInstanceCreateReqVO.setProcessDefinitionId(processDefinition.getId());
//        String instanceId = processInstanceService.createProcessInstance(getLoginUserId(),bpmProcessInstanceCreateReqVO);
//        // 保存发起日志
//        taskService.saveCreateHandleLog(getLoginUserId(),bpmProcessInstanceCreateReqVO,instanceId);
//        // 跳过第一个申请节点
//        processInstanceService.skipFirstTask(instanceId);
//        //保存发起日志
//        taskService.saveCreateHandleLog(getLoginUserId(),bpmProcessInstanceCreateReqVO,instanceId);
//        Integer todoType;
//        // 会议室申请、预定流程
////        if (AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())
////            || AffairProcessTypeEnum.METTING_RESERVATION.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())) {
////            MeetingReserveBaseDTO meetingReserveBaseDTO = new MeetingReserveBaseDTO();
////            meetingReserveBaseDTO.setProcessInstanceId(instanceId);
////            meetingReserveBaseDTO.setVariables(bpmProcessInstanceCreateReqVO.getVariables());
////            meetingReserveBaseDTO.setProcessDefinitionKey(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey());
////            if (StringUtils.isNotEmpty(bpmProcessInstanceCreateReqVO.getProcessInstanceId())) {
////                meetingReserveApi.updateMeetingReserve(meetingReserveBaseDTO);
////            } else {
////                meetingReserveApi.createMeetingReserve(meetingReserveBaseDTO);
////            }
////        }
//        if (AffairProcessTypeEnum.LEAVE.getProcessType().equals(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey())) {
//            bpmOALeaveService.saveVaribale(bpmProcessInstanceCreateReqVO.getVariables(),instanceId);
//        }
//
//        //推送待办到当前办理人的工作台
////        taskService.pushTodoStatus(TodoType.AFFAIR.getId(), AffairProcessNameTypeEnum.getNameByType(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey()).getDesc(),
////                SubSystemEnum.GOVERNMENT.getId(),instanceId,bpmProcessInstanceCreateReqVO.getProcessDefinitionKey(),BpmProcessInstanceResultEnum.PROCESS.getResult());
//        //创建新待办
//        taskService.pushNewTodoToMidOffice(instanceId,bpmProcessInstanceCreateReqVO.getProcessDefinitionKey(),BpmProcessInstanceResultEnum.PROCESS.getResult());
//        return success(true);
//    }
//
//    //通过
//    @PostMapping("/approveTask")
//    @ApiOperation("通过任务")
//    @PreAuthorize("@ss.hasPermission('bpm:task:create')")
//    public CommonResult<Boolean> approveTask(@Valid @RequestBody  BpmTaskApproveReqVO bpmTaskApproveReqVO) {
//        String taskId = taskService.getTaskIdByInstanceId(getLoginUserId(), bpmTaskApproveReqVO.getProcessInstanceId());
//        if (StringUtils.isEmpty(taskId)) {
//            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
//        }
//        if (StringUtils.isEmpty(bpmTaskApproveReqVO.getTaskDefKey())) {
//            bpmTaskApproveReqVO.setTaskDefKey(taskService.getTaskDefByInstanceId(getLoginUserId(), bpmTaskApproveReqVO.getProcessInstanceId()));
//        }
//        Map<String, Object> map = bpmTaskApproveReqVO.getVariables();
//        if (!Objects.isNull(map.get("processInstanceId"))) {
//            if (!Objects.isNull(map.get("id"))) {
//                Boolean result = approvalDraftApi.updateDraftStatus(Long.parseLong(map.get("id").toString())).getCheckedData();
//            }
//            BpmProcessInstanceUpdateReqDTO reqDTO =  new BpmProcessInstanceUpdateReqDTO();
//            reqDTO.setProcessInstanceId(bpmTaskApproveReqVO.getProcessInstanceId());
//            reqDTO.setVariables(bpmTaskApproveReqVO.getVariables());
//            processInstanceService.updateProcessInstance(reqDTO);
//        }
//        bpmTaskApproveReqVO.setId(taskId);
//        // 流程审批参数处理
//        processInstanceService.dealProcessApprovals(bpmTaskApproveReqVO);
//        //同意本次任务之前，查询正在执行的任务,通过数量判断是否是并行任务
//        List<BpmTaskExtDO> taskExtDOS = bpmTaskService.getDetailTasks(bpmTaskApproveReqVO.getProcessInstanceId());
//        //当前待办更新为已办
//        taskService.pushDoneDTOtoMidOffice(bpmTaskApproveReqVO.getProcessInstanceId(),null,BpmProcessInstanceResultEnum.APPROVE.getResult());
//        // 审批通过
//        taskService.approveTask(getLoginUserId(),bpmTaskApproveReqVO);
//
//        //审批通过后的处理，主要用于判断是不是网站发文主要领导签发的特殊驳回，返回值为是否改变了FlowFlag
//        Boolean isChangeFlowFlag = processInstanceService.afterApproveTaskDeal(bpmTaskApproveReqVO);
//
//
//        //同意本次任务之后，创建新的待办:如果本次任务通过之前时的任务数量大于1，说明是并行任务，并行任务则在最后一个任务执行时再创建新待办
//        if (!(taskExtDOS.size() > 1)){
//            taskService.pushNewTodoToMidOffice(bpmTaskApproveReqVO.getProcessInstanceId(),null, BpmProcessInstanceResultEnum.APPROVE.getResult());
//        }
//        // 添加审批意见
//
//        taskService.approveTaskAndComment(getLoginUserId(),bpmTaskApproveReqVO);
//        // 审批日志
//        taskService.saveApproveHandleLog(getLoginUserId(),bpmTaskApproveReqVO,taskExtDOS.get(0));
//        // 更新待办
////        taskService.pushTodoStatus(TodoType.AFFAIR.getId(), null, SubSystemEnum.GOVERNMENT.getId(), bpmTaskApproveReqVO.getProcessInstanceId(),null, BpmProcessInstanceResultEnum.APPROVE.getResult());
//        if (AffairStartTaskCodeEnum.isStartTaskByCode(bpmTaskApproveReqVO.getTaskDefKey())){
//            processInstanceService.updateProcessInstanceExtFlowFlagAndLaunchSortTime(bpmTaskApproveReqVO.getProcessInstanceId(), null);
//            BpmTaskVO reqVO = new BpmTaskVO();
//            reqVO.setInstanceId(bpmTaskApproveReqVO.getProcessInstanceId());
//            taskService.revokeTaskAndComment(reqVO);
//        } else {
//            //是否被网站发文的特殊驳回修改过flowFlag,修改过就不覆盖了
//            if(!isChangeFlowFlag)
//            {
//                processInstanceService.updateProcessInstanceExtFlowFlag(bpmTaskApproveReqVO.getProcessInstanceId(), null);
//            }
//        }
//        // 图片签名
//        if (StringUtils.isNotEmpty(bpmTaskApproveReqVO.getImageUrl())) {
//            BpmTaskExtDO bpmTaskExtDO = new BpmTaskExtDO();
//            bpmTaskExtDO.setImageUrl(bpmTaskApproveReqVO.getImageUrl());
//            bpmTaskExtDO.setTaskId(taskId);
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//        }
//
//        return success(true);
//    }
//
//    //转派
//    @PostMapping("/updateTaskAssignee")
//    @ApiOperation("转派任务")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<Boolean> updateTaskAssignee(@Valid  @RequestBody BpmTaskUpdateAssigneeReqVO reqVO) {
//        String taskId = taskService.getTaskIdByInstanceId(getLoginUserId(), reqVO.getProcessInstanceId());
//        if (StringUtils.isEmpty(taskId)) {
//            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
//        }
//        reqVO.setId(taskId);
//        //更新当前待办
//        taskService.pushDoneDTOtoMidOffice(reqVO.getProcessInstanceId(),null,BpmProcessInstanceResultEnum.REASSIGNMENT.getResult());
//        taskService.updateTaskAssignee(getLoginUserId(), reqVO);
//        //创建新待办
//        taskService.pushNewTodoToMidOffice(reqVO.getProcessInstanceId(),null,BpmProcessInstanceResultEnum.REASSIGNMENT.getResult());
//        taskService.saveReassignmentHandleLog(getLoginUserId(),reqVO);
//        //更新待办
////        taskService.pushTodoStatus(TodoType.AFFAIR.getId(), null, SubSystemEnum.GOVERNMENT.getId(), reqVO.getProcessInstanceId(),null,BpmProcessInstanceResultEnum.REASSIGNMENT.getResult());
//        return success(true);
//    }
//    //不同意
//    @PostMapping("/taskReject")
//    @ApiOperation("驳回任务")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<Boolean> taskReject(@Valid  @RequestBody BpmTaskVO reqVO) {
//        //由于前端不好改
//        //网站发文主要领导签发驳回转通过接口
//        if(reqVO.getTaskDefKey().equals(AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode()) || reqVO.getTaskDefKey().equals(AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getCode()))
//        {
//            BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
//            //BpmTaskVO转BpmTaskApproveReqVO
//            bpmTaskApproveReqVO.setTaskDefKey(reqVO.getTaskDefKey());
//            bpmTaskApproveReqVO.setReason(reqVO.getComment());
//            //前端一直发的都是variables.不好改,所以BpmTaskVO新建一个variables
//            bpmTaskApproveReqVO.setVariables(reqVO.getVariables());
//            bpmTaskApproveReqVO.setId(reqVO.getTaskId());
//            bpmTaskApproveReqVO.setProcessInstanceId(reqVO.getProcessInstanceId());
//            return approveTask(bpmTaskApproveReqVO);
//        }
//
//        String taskId = taskService.getTaskIdByInstanceId(getLoginUserId(), reqVO.getProcessInstanceId());
//        if (StringUtils.isEmpty(taskId)) {
//            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
//        }
//        reqVO.setTaskId(taskId);
//        //更新当前待办
//        taskService.pushDoneDTOtoMidOffice(reqVO.getProcessInstanceId(),null,BpmProcessInstanceResultEnum.BACK.getResult());
//        bpmTaskService.taskReject(reqVO);
//        //创建新待办
//        taskService.pushNewTodoToMidOffice(reqVO.getProcessInstanceId(), null, BpmProcessInstanceResultEnum.BACK.getResult());
//        // 驳回审批意见
////        taskService.rejectTaskAndComment(reqVO);
//        //更新次任务的办理日志和驳回状态
//        taskService.saveRejectHandleLog(getLoginUserId(),reqVO);
////        taskService.pushTodoStatus(TodoType.AFFAIR.getId(), null, SubSystemEnum.GOVERNMENT.getId(), reqVO.getProcessInstanceId(),null,BpmProcessInstanceResultEnum.BACK.getResult());
//
//        processInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getProcessInstanceId(), "2");
//        return success(true);
//    }
//    //撤回
//    @PostMapping("/revokeProcess")
//    @ApiOperation("撤回任务")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<Boolean> revokeProcess(@Valid  @RequestBody BpmTaskVO reqVO) {
//        reqVO.setUserId(String.valueOf(getLoginUserId()));
//        //  获取当前正在执行的任务
//        List<BpmTaskExtDO> bpmTaskExtDOS = taskService.getDetailTasks(reqVO.getInstanceId());
//        BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(reqVO.getInstanceId());
//        if (processInstanceVO != null) {
//            if (AffairProcessTypeEnum.METTING_RESERVATION.getProcessType().equals(processInstanceVO.getProcessKey())
//                    || AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(processInstanceVO.getProcessKey())) {
//                bpmTaskService.deleteRedisMeetingRoomsTimeRange(processInstanceVO.getFormVariables());
//            }
//        }
//        //更新当前待办
//        taskService.pushDoneDTOtoMidOffice(reqVO.getInstanceId(), null,BpmProcessInstanceResultEnum.WITHDRAW.getResult());
//        bpmTaskService.revokeProcess(reqVO);
//        //创建新待办
////        taskService.pushNewTodoToMidOffice(reqVO.getInstanceId(),null, BpmProcessInstanceResultEnum.WITHDRAW.getResult());
//        // 撤销办理日志
//        taskService.saveRevokeHandleLog(bpmTaskExtDOS);
//        // 撤销审批意见
////        taskService.revokeTaskAndComment(reqVO);
////        taskService.pushTodoStatus(TodoType.AFFAIR.getId(), null, SubSystemEnum.GOVERNMENT.getId(), reqVO.getInstanceId(),null,BpmProcessInstanceResultEnum.WITHDRAW.getResult());
//        processInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getInstanceId(), "1");
//        bpmTaskExtDOS.forEach(bpmTaskExtDO -> bpmTaskExtDO.setRevokeStatus("1"));
//        taskService.batchUpdate(bpmTaskExtDOS);
//
//        return success(true);
//    }
//
//    @PostMapping("/meetingCancel")
//    @ApiOperation("取消会议")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<Boolean> meetingCancel(@RequestParam("processInstanceId") String processInstanceId) {
//        //  获取当前正在执行的任务
//        BpmProcessInstanceRespVO bpmProcessInstanceRespVO = processInstanceService.getProcessInstanceVO(processInstanceId);
//        Boolean result = false;
//        if (bpmProcessInstanceRespVO.getStatus() == null) {
//            return success(result);
//        }
//        if ((bpmProcessInstanceRespVO.getStatus().equals(2))
//                && ((AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(bpmProcessInstanceRespVO.getProcessKey())))
//                || AffairProcessTypeEnum.METTING_RESERVATION.getProcessType().equals(bpmProcessInstanceRespVO.getProcessKey())) {
//            meetingReserveApi.cancelMeeting(processInstanceId);
//            result = bpmTaskService.meetingCancel(processInstanceId);
//        }
//        return success(result);
//    }
//
//
//    //获取流程实例详情
//    @GetMapping("/getDetail")
//    @ApiOperation("获取流程实例详情")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<BpmProcessInstanceRespVO> getDetail(@RequestParam("processInstanceId") String processInstanceId) {
//        BpmProcessInstanceRespVO bpmProcessInstanceRespVO = processInstanceService.getProcessInstanceVO(processInstanceId);
//        List<BpmTaskExtDO> bpmTaskExtDOS = taskService.getDetailTasks(processInstanceId);
//        if (CollUtil.isNotEmpty(bpmTaskExtDOS)) {
//            List<BpmTaskExtDO> doList = bpmTaskExtDOS.stream().filter(bpmTaskExtDO -> getLoginUserId().equals(bpmTaskExtDO.getAssigneeUserId())).collect(Collectors.toList());
//            if (CollUtil.isNotEmpty(doList)) {
//                bpmProcessInstanceRespVO.setTaskDefKey(doList.get(0).getTaskDefKey());
//                bpmProcessInstanceRespVO.setTaskId(doList.get(0).getTaskId());
//            }
//        }
//        if (AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(bpmProcessInstanceRespVO.getProcessKey())
//                || AffairProcessTypeEnum.METTING_RESERVATION.getProcessType().equals(bpmProcessInstanceRespVO.getProcessKey())) {
//            List<MeetingAttendeeRespDTO> meetingAttendees = meetingAttendeeApi.getMeetingAttendee(processInstanceId);
//            if (meetingAttendees != null) {
//                Map<String, Object> formVariables = bpmProcessInstanceRespVO.getFormVariables();
//                formVariables.put("meetingAttendees", meetingAttendees);
//                bpmProcessInstanceRespVO.setFormVariables(formVariables);
//            }
//        }
//        if (AffairProcessTypeEnum.WEB_POST.getProcessType().equals(bpmProcessInstanceRespVO.getProcessKey())) {
//            // 修改
//            Map<String, Object> map = bpmProcessInstanceRespVO.getFormVariables();
//            if (!(map.get("fileAttachments") instanceof ArrayList)) {
//                map.put("fileAttachments", JSONArray.parseArray(map.get("fileAttachments").toString()));
//                bpmProcessInstanceRespVO.setFormVariables(map);
//            }
//        }
//        // 当前审批人
//        if (!CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
//            List<Long> userIds = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toList());
//            bpmProcessInstanceRespVO.setCurrentApprovalUserIds(userIds);
//        }
//
//        return success(bpmProcessInstanceRespVO);
//    }
//
//    //获取流程实例详情
//    @GetMapping("/getNextApproval")
//    @ApiOperation("获取下一节点审批人")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "taskId", value = "任务ID", required = false, dataTypeClass = String.class),
//            @ApiImplicitParam(name = "type", value = "1 是 正向， 2 是驳回, 3 是顺序", required = true, dataTypeClass = String.class),
//            @ApiImplicitParam(name = "proDefinitionKey", value = "流程模型标识类型", required = true, dataTypeClass = String.class)
//    })
//    public CommonResult<Map<String,Object>> getNextApproval(@RequestParam(value = "taskId", required = false) String taskId,
//                                                            @RequestParam("type") String type,
//                                                            @RequestParam(value = "condition", required = false) String condition,
//                                                            @RequestParam("proDefinitionKey") String proDefinitionKey) {
//        Map<String,Object> result = processInstanceService.getNextApproval(taskId,type,proDefinitionKey,condition);
//        return success(result);
//    }
//
//    @PostMapping("/delete")
//    @ApiOperation(value = "取消流程实例")
//    @ApiImplicitParam(name = "processInstanceId", value = "1024", required = true, dataTypeClass = String.class)
//    @PreAuthorize("@ss.hasPermission('bpm:task:deleted')")
//    public CommonResult<Boolean> cancelProcessInstance(@RequestParam("processInstanceId") String processInstanceId) {
//        BpmProcessInstanceCancelReqVO bpmProcessInstanceCancelReqVO = new BpmProcessInstanceCancelReqVO();
//        bpmProcessInstanceCancelReqVO.setId(processInstanceId);
//        bpmProcessInstanceCancelReqVO.setReason("deletd by " + getLoginUserId());
//        processInstanceService.cancelProcessInstance(getLoginUserId(), bpmProcessInstanceCancelReqVO);
//        BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(processInstanceId);
//        if (processInstanceVO != null) {
//            meetingReserveApi.deleteMeetingReserve(processInstanceId);
//            if (AffairProcessTypeEnum.METTING_RESERVATION.getProcessType().equals(processInstanceVO.getProcessKey())
//                || AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(processInstanceVO.getProcessKey())) {
//                bpmTaskService.deleteRedisMeetingRoomsTimeRange(processInstanceVO.getFormVariables());
//            }
//        }
//        if (processInstanceVO.getFormVariables() != null && !Objects.isNull(processInstanceVO.getFormVariables().get("id"))
//                && StringUtils.isNotEmpty(processInstanceVO.getFormVariables().get("id").toString())) {
//            approvalDraftApi.deleteDraftApproval(Long.valueOf(processInstanceVO.getFormVariables().get("id").toString()));
//        }
//        if (AffairProcessTypeEnum.LEAVE.getProcessType().equals(processInstanceVO.getProcessKey())) {
//            bpmOALeaveService.updateResult(processInstanceId,BpmProcessInstanceResultEnum.CANCEL.getResult());
//        }
//        //更新当前待办
//        taskService.pushDoneDTOtoMidOffice(processInstanceId,null,BpmProcessInstanceResultEnum.CANCEL.getResult());
//        return success(true);
//    }
//
//    @GetMapping("/getHandleLog")
//    @ApiOperation("获取流程办理日志")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<List<BpmProcessInstanceHandleLogRespVo>> getHandleLog(@RequestParam("processInstanceId") String processInstanceId) {
//        List<BpmProcessInstanceHandleLogRespVo> respVoList = bpmTaskService.getHandleLog(processInstanceId);
//        return success(respVoList);
//    }
//
//    @GetMapping("/isFormFiledUnique")
//    @ApiOperation("检验formVariables字段唯一性")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "processKey", value = "流程定义Key", required = true, dataTypeClass = String.class),
//            @ApiImplicitParam(name = "filed", value = "form字段名称", required = true, example = "name", dataTypeClass = String.class),
//            @ApiImplicitParam(name = "formValue", value = "字段值", required = true, example = "John", dataTypeClass = String.class),
//            @ApiImplicitParam(name = "dataType", value = "字段数据类型：一般为String", required = true, example = "String", dataTypeClass = String.class)
//    })
//    public CommonResult<Boolean> isFormFiledUnique(@RequestParam("processKey") String processKey,@RequestParam("filed") String filed,@RequestParam("formValue") String formValue,@RequestParam("dataType") String dataType) {
//
//        return success(bpmTaskService.isFormFiledUnique(processKey,filed,formValue,dataType));
//    }
//
//    @GetMapping("/calculate-leavel-time")
//    @ApiOperation("请假时间是否有重叠")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "startTime", value = "开始日期", example = "2022-1-1", required = true, dataTypeClass = String.class),
//            @ApiImplicitParam(name = "endTime", value = "结束日期", example = "2022-1-30", required = true, dataTypeClass = String.class),
//    })
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<Boolean> calculateLeavelTime(@RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-mm-dd") String startTime,
//                                                     @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-mm-dd") String endTime,
//                                                     @RequestParam(value = "processInstanceId", required = false)  String processInstanceId) {
//        boolean res = bpmOALeaveService.getMutilData(startTime,endTime,processInstanceId);
//        return success(res);
//    }
//
//    @GetMapping("/getTodoCount")
//    @ApiOperation("app查询待办数量")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataTypeClass = Long.class),
//    })
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<Map<String,Long>> getTodoCountForApp(@RequestParam("userId") Long userId) {
//        Map<String,Long> todoAffairTaskCount = bpmTaskService.getTodoAffairTaskCountForAPP(getLoginUserId());
//        return success(todoAffairTaskCount);
//    }
//
//
//    @GetMapping("/appSearchPage")
//    @ApiOperation("app搜索页面")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> appSearchPage(@Valid AppSearchPageReqVO pageReqVO) {
//
//        BpmTaskPageReqVO bpmTaskPageReqVO = new BpmTaskPageReqVO();
//        bpmTaskPageReqVO.setPageNo(pageReqVO.getPageNo());
//        bpmTaskPageReqVO.setPageSize(pageReqVO.getPageSize());
//        bpmTaskPageReqVO.setAppSearchKeywords(pageReqVO.getKeyWords());
//        bpmTaskPageReqVO.setClientTag(pageReqVO.getClientTag());
//
//        if (pageReqVO.getScope() == null || pageReqVO.getScope().equals("all")){
//            PageResult<BpmTaskPageItemRespVO> queryTaskPage = taskService.appSearchPage(getLoginUserId(), pageReqVO);
//            return success(queryTaskPage);
//        }
//        else if (pageReqVO.getScope().equals("myLaunch")){
//            PageResult<BpmTaskPageItemRespVO> queryTaskPage = taskService.getStartAffairTaskPage(getLoginUserId(), bpmTaskPageReqVO);
//            return success(queryTaskPage);
//        }
//        else if (pageReqVO.getScope().equals("done")){
//            PageResult<BpmTaskPageItemRespVO> queryTaskPage = taskService.getDoneAffairTaskPage(getLoginUserId(), bpmTaskPageReqVO);
//            return success(queryTaskPage);
//        }
//        return success(null);
//    }
//
//    @GetMapping("/get-mealVisitor-import-template")
//    @ApiOperation("获得接待申请-来访人员信息表模版")
//    @OperateLog(enable = false)
//    public void mealImportTemplate(HttpServletResponse response) throws IOException {
//        // 输出
//        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
//        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("来访人员信息表导入模版", "UTF-8"));
//        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
//        // 输出 Excel
//        try {
//            EasyExcel.write(response.getOutputStream(), MealVisitorImportExcelVO.class)
//                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
//                    .registerWriteHandler(MealVisitorImportExcelVO.getMyCellStyle())
//                    .registerWriteHandler(MealVisitorImportExcelVO.getSelectListSheetWriteHandler())
//                    .sheet("来访人员信息").doWrite(Collections.emptyList());
//            System.out.println(MealVisitorImportExcelVO.getMyCellStyle());
//        } catch (IOException e) {
//            response.setContentType("application/json;charset=UTF-8");
//            throw exception(EXPORT_FAILED);
//        }
//    }
//
//    @PostMapping("/import-mealVisitorInfo")
//    @ApiOperation("导入来访人员信息")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
//            @ApiImplicitParam(name = "updateSupport", value = "是否支持更新，默认为 false", example = "true", dataTypeClass = Boolean.class)
//    })
////    @PreAuthorize("@ss.hasPermission('bpm:task:import')")
//    public CommonResult<List<MealVisitorImportExcelVO>> importMealVisitorInfoExcel(@RequestParam("file") MultipartFile file,
//                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
//        List<MealVisitorImportExcelVO> list = EasyExcel.read(file.getInputStream(),MealVisitorImportExcelVO.class,null)
//                                                        .autoCloseStream(false)  // 不要自动关闭，交给 Servlet 自己处理
//                                                        .doReadAllSync();
//        ExcelValidator.valid(list,1);
//        Set<String> visitorNames = list.stream().map(MealVisitorImportExcelVO::getName).collect(Collectors.toSet());
//        if (visitorNames.size() < list.size()){
////            return error(null,"人员姓名重复");
//            throw exception(REPEAT_VISITOR_NAME_IN_EXCEL);
//        }
//        return success(list);
//    }
//
//    @GetMapping("/getMyLastAttendeeInfos")
//    @ApiOperation("获得上一次参会人信息")
//    @ApiImplicitParam(name = "processKey", value = "流程定义Key", required = true, dataTypeClass = String.class)
//    public CommonResult<Map<String, Object>> getMyLastMeetingAttendeesAndObservers(@RequestParam("processKey") String processKey) {
//        Map<String, Object> map = processInstanceService.getLastVariableMapByStartUserIdAndProcessKey(getLoginUserId(), processKey);
//        return success(map);
//    }

}
