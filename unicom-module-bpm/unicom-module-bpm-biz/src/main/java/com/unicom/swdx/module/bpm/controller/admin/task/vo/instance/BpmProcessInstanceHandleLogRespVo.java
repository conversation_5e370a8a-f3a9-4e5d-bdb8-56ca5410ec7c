package com.unicom.swdx.module.bpm.controller.admin.task.vo.instance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BpmProcessInstanceHandleLogRespVo {
    @ApiModelProperty(value = "任务名称", required = true, example = "")
    private String name;
    @ApiModelProperty(value = "任务节点状态 1、进行中、2、通过、3、驳回", required = true, example = "")
    private Integer result;
    @ApiModelProperty(value = "节点日志参数", required = true, example = "")
    private String logParameters;
    @ApiModelProperty(value = "任务简称", required = false, example = "")
    private String taskShortName;

}
