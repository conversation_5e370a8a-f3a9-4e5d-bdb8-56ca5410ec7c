package com.unicom.swdx.module.bpm.controller.admin.task.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@ApiModel("管理后台 - 流程实例的创建 Request VO")
@Data
public class BpmProcessInstanceCreateCtrlReqVO {

    @ApiModelProperty(value = "流程定义的编号", example = "1024")
    private String processDefinitionId;

    @ApiModelProperty(value = "流程实例编号", example = "1024")
    private String processInstanceId;

    @ApiModelProperty(value = "流程定义", example = "1024")
    private String processDefinitionKey;

    @ApiModelProperty(value = "变量实例")
    private Map<String, Object> variables;

    private String source = "OA";

}
