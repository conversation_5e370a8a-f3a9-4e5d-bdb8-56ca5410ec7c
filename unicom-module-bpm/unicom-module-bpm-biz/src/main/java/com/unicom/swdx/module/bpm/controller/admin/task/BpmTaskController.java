package com.unicom.swdx.module.bpm.controller.admin.task;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.*;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.*;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Api(tags = "管理后台 - 流程任务实例")
@RestController
@RequestMapping("/bpm/task")
@Validated
public class BpmTaskController {

    @Resource
    private BpmTaskService taskService;

    @Resource
    private BpmTaskService bpmTaskService;

    @Resource
    private BpmProcessInstanceService bpmProcessInstanceService;

    @GetMapping("todo-page")
    @ApiOperation("获取 Todo 待办任务分页")
    public CommonResult<PageResult<BpmTaskTodoPageItemRespVO>> getTodoTaskPage(@Valid BpmTaskTodoPageReqVO pageVO) {
        return success(taskService.getTodoTaskPage(getLoginUserId(), pageVO));
    }

    @GetMapping("done-page")
    @ApiOperation("获取 Done 已办任务分页")
    public CommonResult<PageResult<BpmTaskDonePageItemRespVO>> getDoneTaskPage(@Valid BpmTaskDonePageReqVO pageVO) {
        PageResult<BpmTaskDonePageItemRespVO> res = taskService.getDoneTaskPageWithFlowFlag(getLoginUserId(), pageVO);
        res.getList().forEach( x -> {
            if ( StringUtils.isNotEmpty(x.getRevokeStatus()) ) {
                if ( "2".equals(x.getRevokeStatus()) ){
                    x.setResult(BpmProcessInstanceResultEnum.BACK.getResult());
                }
                else if ( "1".equals(x.getRevokeStatus()) ) {
                    x.setResult(BpmProcessInstanceResultEnum.WITHDRAW.getResult());
                }
            }
        });

        return success(res);
    }

    @GetMapping("/list-by-process-instance-id")
    @ApiOperation(value = "获得指定流程实例的任务列表", notes = "包括完成的、未完成的")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
    public CommonResult<List<BpmTaskRespVO>> getTaskListByProcessInstanceId(
        @RequestParam("processInstanceId") String processInstanceId) {
        return success(taskService.getTaskListByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/get-bpmn-node-progress")
    @ApiOperation(value = "获得指定流程经过筛选的任务列表", notes = "用户bpmn展示，包括完成的、未完成的")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
    public CommonResult<List<BpmTaskRespVO>> getBpmnNodeProgressByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        return success(taskService.getTaskListByProcessInstanceIdWithFlowStatus(processInstanceId));
    }

    @PostMapping("/approve")
    @ApiOperation("通过任务")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> approveTask(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        taskService.approveTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/reject")
    @ApiOperation("不通过任务")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> rejectTask(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {
        taskService.rejectTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/update-assignee")
    @ApiOperation(value = "更新任务的负责人", notes = "用于【流程详情】的【转派】按钮")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> updateTaskAssignee(@Valid @RequestBody BpmTaskUpdateAssigneeReqVO reqVO) {
        taskService.updateTaskAssignee(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/rejectBack")
    @ApiOperation(value = "驳回任务",notes = "用于【流程详情】的【驳回】按钮，id必填")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> rejectBackTask(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {
        //进行驳回
        BpmTaskVO bpmTaskVO = new BpmTaskVO();
        bpmTaskVO.setTaskId(reqVO.getId());
        bpmTaskVO.setInstanceId(reqVO.getProcessInstanceId());
        bpmTaskVO.setComment(reqVO.getReason());
        bpmTaskVO.setUserId(String.valueOf(getLoginUserId()));
        bpmTaskService.taskReject(bpmTaskVO);
        //更新本次任务驳回意见
        BpmTaskExtDO bpmTaskExtDO = new BpmTaskExtDO();
        bpmTaskExtDO.setTaskId(reqVO.getId());
        bpmTaskExtDO.setReason(reqVO.getReason());
        bpmTaskExtDO.setRevokeStatus("2");
        bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
        //流程状态置为驳回状态
        bpmProcessInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getProcessInstanceId(), "2");
        return success(true);
    }

    @PostMapping("/revokeProcess")
    @ApiOperation(value = "撤回流程",notes = "用于【流程详情】的【退回】按钮，processInstanceId必填")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> revokeProcess(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {
        BpmTaskVO bpmTaskVO = new BpmTaskVO();
        bpmTaskVO.setTaskId(reqVO.getId());
        bpmTaskVO.setInstanceId(reqVO.getProcessInstanceId());
        bpmTaskVO.setComment(reqVO.getReason());
        bpmTaskVO.setUserId(String.valueOf(getLoginUserId()));
        bpmTaskService.revokeProcess(bpmTaskVO);

        //流程状态置为撤回状态
        bpmProcessInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getProcessInstanceId(), "1");
        return success(true);
    }

}
