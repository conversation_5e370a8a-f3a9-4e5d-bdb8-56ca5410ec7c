package com.unicom.swdx.module.bpm.listener;

import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskExtDTO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEvent;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.oa.api.OutReportApi;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.dto.OutReportDTO;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Component
@Slf4j
public class OAOutReportResultDeal {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private ReceiveApi receiveApi;

    @Resource
    private OutReportApi outReportApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private PostApi postApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Async
    @SneakyThrows
    public void deal(BpmProcessInstanceResultEvent event) {
        Thread.sleep(2000);

        if(Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())){
            String id = event.getId();
            log.info("开始抄送外出报告流程，id = {}",id);

            BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(id);
            OutReportDTO outReportDTO = outReportApi.getItemId(id);
            // 改变外出报告表的结果为已完成
            outReportApi.updateResultById(outReportDTO.getId(), BpmProcessInstanceResultEnum.APPROVE.getResult());

            ReceiveDTO receiveDTO = new ReceiveDTO();
            receiveDTO.setProcessInstanceId(id);
            receiveDTO.setCategory(processInstanceVO.getCategory());
            receiveDTO.setApplyTime(processInstanceVO.getCreateTime());
            receiveDTO.setItemId(outReportDTO.getId());
            receiveDTO.setProcessInstanceId(processInstanceVO.getId());
            Map<String, Object> formVariables = processInstanceVO.getFormVariables();
            Integer postType  = Integer.parseInt(formVariables.get("postType").toString());
            List<Long> userIds = null;
            String taskName = "";
            if (postType == 1 || postType == 2) {
                // 抄送给办公室
                taskName = "办公室备案";
                // 办公室外出报告备案负责人（设置一个专门的postType
                userIds = postApi.getUserByPost("oa-office-charge-out",outReportDTO.getTenantId());
//            List<AdminUserRespDTO> users = userApi.getUsersByDeptName("办公室", lectureDTO.getTenantId()).getCheckedData();
//            userIds = users.stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
            } else if (postType == 3 || postType == 4) {
                // 人事部
                taskName = "人事部备案";
                // 人事部外出报告备案负责人
                userIds = postApi.getUserByPost("oa-hr-charge-out",outReportDTO.getTenantId());
//            List<AdminUserRespDTO> users = userApi.getUsersByDeptName("教务部", lectureDTO.getTenantId()).getCheckedData();
//            userIds = users.stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
            }
            receiveDTO.setUserIds(userIds);
            receiveDTO.setPromoterUserId(processInstanceVO.getStartUser().getId());
            receiveApi.save(receiveDTO);

            // 记录抄送日志到taskExt表
            BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
            bpmTaskExtDTO.setName(taskName);
            bpmTaskExtDTO.setTaskDefKey("copyOutReportInfoToOfficeOrHr");
            bpmTaskExtDTO.setTaskId("copyOutReportInfoToOfficeOrHr");
            bpmTaskExtDTO.setResult(2);
            bpmTaskExtDTO.setProcessInstanceId(id);
            bpmTaskExtDTO.setProcessDefinitionId(id);
            LocalDateTime now = LocalDateTime.now();
            bpmTaskExtDTO.setCreateTime(now);
            bpmTaskExtDTO.setEndTime(now);
            bpmTaskExtDTO.setTaskType(4);
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> userDeptMap = new HashMap<>();
            userIds.forEach(userId -> {
                AdminUserRespDTO user = userApi.getUser(userId).getCheckedData();
                String nickname = "";
                String deptName = "";
                if(Objects.nonNull(user)){
                    nickname = user.getNickname();
                    DeptRespDTO dept = deptApi.getDept(user.getDeptId()).getCheckedData();
                    if(Objects.nonNull(dept)){
                        deptName = dept.getName();
                    }
                    userDeptMap.put(nickname,deptName);
                }
            });
            map.put("copyTo", userDeptMap);
            bpmTaskExtDTO.setParamsMap(map);
            bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);

            //添加外出报告时间到日程

            Map<String, LocalDate> dateMap  = outReportApi.getDateById(outReportDTO.getId());
            ScheduleDto scheduleDto = new ScheduleDto();
            //设置发起人id
            scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
            //设置发起时间dateMap.get("startMap")
            LocalDate startDate =dateMap.get("startDate");
            LocalDate endDate =dateMap.get("endDate");
            scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            //传流程id赋值到
            scheduleDto.setProcessInstanceId(id);
            //传类型传12为外出报告
            scheduleDto.setType(12);
            scheduleServiceApi.createScheduleOther(scheduleDto);
        }

    }

}
