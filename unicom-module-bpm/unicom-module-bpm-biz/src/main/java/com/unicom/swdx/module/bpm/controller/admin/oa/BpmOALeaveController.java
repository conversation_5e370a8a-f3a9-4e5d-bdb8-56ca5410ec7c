package com.unicom.swdx.module.bpm.controller.admin.oa;

import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOALeaveCreateReqVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOALeavePageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOALeaveRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.unicom.swdx.module.bpm.convert.oa.BpmOALeaveConvert;
import com.unicom.swdx.module.bpm.dal.dataobject.oa.BpmOALeaveDO;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEvent;
import com.unicom.swdx.module.bpm.service.oa.BpmOALeaveService;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.oa.api.*;
import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.api.dto.LectureDTO;
import com.unicom.swdx.module.oa.api.dto.OutReportDTO;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * OA 请假申请 Controller，用于演示自己存储数据，接入工作流的例子
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Api(tags = "管理后台 - OA 请假申请")
@RestController
@RequestMapping("/bpm/oa/leave")
@Validated
public class BpmOALeaveController {

    @Resource
    private BpmOALeaveService leaveService;

    //同名api更改
    @Resource
    private ScheduleServiceApi scheduleServiceApi;
    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private LectureApi lectureApi;

    @Resource
    private OutReportApi outReportApi;

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private LeaveApi leaveApi;

    @Resource
    SummaryApi summaryApi;

    @Resource
    private ReceiveApi receiveApi;

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('bpm:oa-leave:create')")
    @ApiOperation("创建请求申请")
    public CommonResult<Long> createLeave(@Valid @RequestBody BpmOALeaveCreateReqVO createReqVO) {
        return success(leaveService.createLeave(getLoginUserId(), createReqVO));
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('bpm:oa-leave:query')")
    @ApiOperation("获得请假申请")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<BpmOALeaveRespVO> getLeave(@RequestParam("id") Long id) {
        BpmOALeaveDO leave = leaveService.getLeave(id);
        return success(BpmOALeaveConvert.INSTANCE.convert(leave));
    }

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('bpm:oa-leave:query')")
    @ApiOperation("获得请假申请分页")
    public CommonResult<PageResult<BpmOALeaveRespVO>> getLeavePage(@Valid BpmOALeavePageReqVO pageVO) {
        PageResult<BpmOALeaveDO> pageResult = leaveService.getLeavePage(getLoginUserId(), pageVO);
        return success(BpmOALeaveConvert.INSTANCE.convertPage(pageResult));
    }
    @GetMapping("/test")
    //@PreAuthorize("@ss.hasPermission('bpm:oa-leave:query')")
    @ApiOperation("获得外出报告")
    public void getTest(@RequestParam("id") String id,
                        @RequestParam("result") Integer result
                        ) {
        BpmProcessInstanceResultEvent event = new BpmProcessInstanceResultEvent(new Object());
        event.setId(id);
        event.setResult(result);
        if (Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())) {
            id = event.getId();

            BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(id);
            OutReportDTO outReportDTO = outReportApi.getItemId(id);


            //添加外出报告时间到日程

            Map<String, LocalDate> dateMap = outReportApi.getDateById(outReportDTO.getId());
            ScheduleDto scheduleDto = new ScheduleDto();
            //设置发起人id
            scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
            //设置发起时间dateMap.get("startMap")
            LocalDate startDate = dateMap.get("startDate");
            LocalDate endDate = dateMap.get("endDate");
            scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            //传流程id赋值到
            scheduleDto.setProcessInstanceId(id);
            //传类型传1为外出报告
            scheduleDto.setType(12);
            scheduleServiceApi.createScheduleOther(scheduleDto);
        }
    }
    @GetMapping("/test_bg")
    //@PreAuthorize("@ss.hasPermission('bpm:oa-leave:query')")
    @ApiOperation("获得外出讲学")
    public void getTestBG(@RequestParam("id") String id,
                        @RequestParam("result") Integer result
    ) {
        BpmProcessInstanceResultEvent event = new BpmProcessInstanceResultEvent(new Object());
        event.setId(id);
        event.setResult(result);
        if (Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())) {
            id = event.getId();

            BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(id);
            ReceiveDTO receiveDTO = new ReceiveDTO();
            receiveDTO.setProcessInstanceId(id);
            receiveDTO.setCategory(processInstanceVO.getCategory());
            receiveDTO.setApplyTime(processInstanceVO.getCreateTime());
            LectureDTO lectureDTO = lectureApi.getItemId(id);

//            lectureApi.getItemId(id);
            //添加外出报告时间到日程
            Map<String, LocalDate> dateMap  = lectureApi.getDateById(lectureDTO.getId());
            ScheduleDto scheduleDto = new ScheduleDto();
            //设置发起人id
            scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
            //设置发起时间dateMap.get("startMap")
            LocalDate startDate =dateMap.get("startDate");
            LocalDate endDate =dateMap.get("endDate");
            scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            //传流程id赋值到
            scheduleDto.setProcessInstanceId(id);
            //传类型传11为外出讲学
            scheduleDto.setType(11);
            scheduleServiceApi.createScheduleOther(scheduleDto);
        }
    }

    @GetMapping("/test_qj")
    //@PreAuthorize("@ss.hasPermission('bpm:oa-leave:query')")
    @ApiOperation("获得请假审批")
    public void getTestQJ(@RequestParam("id") String id,
                          @RequestParam("result") Integer result
    ) {
        BpmProcessInstanceResultEvent event = new BpmProcessInstanceResultEvent(new Object());
        event.setId(id);
        event.setResult(result);
        if (Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())) {
            id = event.getId();

            BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(id);
            LeaveDTO leaveDTO = leaveApi.getItemId(id);

            //添加外出报告时间到日程
            Map<String, LocalDate> dateMap  = leaveApi.getDateById(leaveDTO.getId());
            ScheduleDto scheduleDto = new ScheduleDto();
            //设置发起人id
            scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
            //设置发起时间dateMap.get("startMap")
            LocalDate startDate =dateMap.get("startDate");
            LocalDate endDate =dateMap.get("endDate");
            scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            //传流程id赋值到
            scheduleDto.setProcessInstanceId(id);
            //传类型传10为请假审批
            scheduleDto.setType(10);
            scheduleServiceApi.createScheduleOther(scheduleDto);
        }
    }

    @GetMapping("/test_yzgzhz")
    //@PreAuthorize("@ss.hasPermission('bpm:oa-leave:query')")
    @ApiOperation("获得一周工作安排汇总")
    public void getTestYzgzhz(@RequestParam("id") String id,
                          @RequestParam("result") Integer result
    ) {
        BpmProcessInstanceResultEvent event = new BpmProcessInstanceResultEvent(new Object());
        event.setId(id);
        event.setResult(result);
        if (Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())) {
            id = event.getId();

            //id为pid
            summaryApi.getItemId(id);
            //userIds是抄送人列表
            List<Long> personIds =  summaryApi.getUsersById(id);
            //添加一周报告汇总时间到日程
            Map<String, LocalDate> dateMap  = summaryApi.getDateById(id);
            ScheduleDto scheduleDto = new ScheduleDto();
            //设置发起人id
//            scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
            //设置发起时间dateMap.get("startMap")
            LocalDate startDate =dateMap.get("startDate");
            LocalDate endDate =dateMap.get("endDate");
            Integer year = summaryApi.getYear(id);
            Integer week = summaryApi.getWeek(id);
            scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            //传流程id赋值到
            scheduleDto.setProcessInstanceId(id);
            //传类型传14为一周安排汇总
            scheduleDto.setType(14);
            scheduleDto.setWeek(week);
            scheduleDto.setYear(year);
            scheduleDto.setUserIds(personIds);
//            scheduleServiceApi.createSchedulesOther(scheduleDto);


            String message =  String.format("假期变更通知\n您申请的假期变更已完成，假期开始时间：【%s】，假期结束时间：【%s】"
                        ,  startDate, endDate);
                            //待办事项办添加
            ReceiveDTO receiveDTO = new ReceiveDTO();
                //设置内容
            receiveDTO.setCategory(message);

            //参与人和抄送人
            receiveDTO.setUserIds(personIds);
            //流程id
            receiveDTO.setProcessInstanceId(id);
                //
            //应该把发起人和时间加上
            //
//            receiveDTO.setApplyTime(bpmProcessInstanceRespDTO.getCreateTime());
//        receiveApi.save(receiveDTO);
            receiveApi.saveWorkSummary(receiveDTO);
        }
    }

}
