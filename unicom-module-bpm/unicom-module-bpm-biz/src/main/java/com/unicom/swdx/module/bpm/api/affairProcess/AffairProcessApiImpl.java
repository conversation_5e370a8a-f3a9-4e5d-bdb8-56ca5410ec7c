//package com.unicom.swdx.module.bpm.api.affairProcess;
//
//import com.unicom.swdx.framework.common.pojo.CommonResult;
////import com.unicom.tyj.module.affair.api.ApprovalDraftApi;
//import com.unicom.swdx.module.bpm.service.definition.BpmProcessDefinitionService;
//import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
//import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
//import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;
//
//@RestController // 提供 RESTful API 接口，给 Feign 调用
//@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
//@Validated
//public class AffairProcessApiImpl implements AffairProcessApi{
//
//    @Resource
//    private BpmTaskService bpmTaskService;
//    @Resource
//    private BpmProcessDefinitionService bpmProcessDefinitionService;
////    @Resource
////    private ApprovalDraftApi approvalDraftApi;
//    @Override
//    public CommonResult<Integer> getTodoCount(Long userId) {
//        Integer todoAffairTaskCount = bpmTaskService.getTodoAffairTaskCount(userId);
//        return success(todoAffairTaskCount);
//    }
//
//
//}
