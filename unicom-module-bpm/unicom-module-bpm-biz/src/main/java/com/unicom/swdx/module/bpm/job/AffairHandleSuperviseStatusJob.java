//package com.unicom.tyj.module.bpm.job;
//
//import com.unicom.tyj.framework.common.exception.ServiceException;
//import com.unicom.tyj.framework.common.pojo.CommonResult;
//import com.unicom.tyj.framework.common.util.collection.CollectionUtils;
//import com.unicom.tyj.framework.tenant.core.job.TenantJob;
//import com.unicom.tyj.module.affair.api.general.notification.AppNotificationApi;
//import com.unicom.tyj.module.affair.api.general.notification.dto.AppNotificationCreateDTO;
//import com.unicom.tyj.module.affair.api.supervise.SuperviseConfApi;
//import com.unicom.tyj.module.affair.api.supervise.dto.SuperviseConfDTO;
//import com.unicom.tyj.module.affair.enums.AppNoticeTypeEnum;
//import com.unicom.tyj.module.affair.enums.DocRemindType;
//import com.unicom.tyj.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
//import com.unicom.tyj.module.bpm.dal.dataobject.task.BpmTaskExtDO;
//import com.unicom.tyj.module.bpm.dal.mysql.task.BpmProcessInstanceExtMapper;
//import com.unicom.tyj.module.bpm.enums.definition.AffairHandleProcessNameTypeEnum;
//import com.unicom.tyj.module.bpm.enums.definition.AffairHandleTaskNameCodeEnum;
//import com.unicom.tyj.module.bpm.enums.definition.AffairProcessNameTypeEnum;
//import com.unicom.tyj.module.bpm.job.po.SuperviseTodoTaskPO;
//import com.unicom.tyj.module.bpm.service.task.BpmTaskService;
//import com.unicom.tyj.module.system.api.common.CalendarWnlApi;
//import com.unicom.tyj.module.system.api.common.dto.CalendarWnlRespDTO;
//import com.unicom.tyj.module.system.api.sms.SmsSendApi;
//import com.unicom.tyj.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
//import com.unicom.tyj.module.system.api.user.AdminUserApi;
//import com.unicom.tyj.module.system.api.user.dto.AdminUserRespDTO;
//import com.xxl.job.core.context.XxlJobHelper;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import org.mapstruct.ap.internal.model.assignment.UpdateWrapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.text.SimpleDateFormat;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.ZoneId;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//
//import static com.unicom.tyj.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.unicom.tyj.module.system.enums.ErrorCodeConstants.MASSAGE_TIMES_OVER_10;
//import static com.unicom.tyj.module.system.enums.ErrorCodeConstants.SMS_SEND_MOBILE_NOT_EXISTS;
//
//@Component
//@TenantJob
//public class AffairHandleSuperviseStatusJob {
//
//
//    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//    private static String AFFAIR_HANDLE = "affair_handle";
//
//    @Resource
//    private BpmTaskService bpmTaskService;
//
//    @Resource
//    private SuperviseConfApi superviseConfApi;
//
//    @Resource
//    private AdminUserApi adminUserApi;
//
//    @Autowired
//    private SmsSendApi smsSendApi;
//
//    @Resource
//    private BpmProcessInstanceExtMapper processInstanceExtMapper;
//
//    @Resource
//    private AppNotificationApi appNotificationApi;
//
//    @Resource
//    private CalendarWnlApi calendarWnlApi;
//
//    /**
//     * 判断督办、提醒和逾期
//     */
//    @XxlJob("AffairHandleSuperviseJob")
//    public void affairTaskSuperviseStatus() {
//
//        List<SuperviseTodoTaskPO> superviseTodoTasks = bpmTaskService.getSuperviseTodoTasks();
//        List<ServiceException> serviceExceptions = new ArrayList<>();
//
//        superviseTodoTasks.forEach(superviseTodoTaskPO -> {
//            if (superviseTodoTaskPO == null) {
//                return;
//            }
//            BpmTaskExtDO bpmTaskExtDO = bpmTaskService.getDetailTaskByTaskId(superviseTodoTaskPO.getTaskId());
//            if (bpmTaskExtDO == null) {
//                XxlJobHelper.log("taskId为" + bpmTaskExtDO.getTaskId() + "的bpmTaskExtDO不存在");
//                XxlJobHelper.log("------------------------------------------------------------------------------");
//                return;
//            }
//
//            SuperviseConfDTO superviseConfDTO = null;
//
//            AffairHandleTaskNameCodeEnum affairHandleTaskCodeEnum = AffairHandleTaskNameCodeEnum.getByCode(superviseTodoTaskPO.getTaskDefKey());
//            if (affairHandleTaskCodeEnum != null) {
//                superviseConfDTO = superviseConfApi.getSuperviseConfByTaskDefKey(affairHandleTaskCodeEnum.getCode());
//                XxlJobHelper.log(superviseTodoTaskPO.getProcessInstanceName());
//                XxlJobHelper.log(superviseTodoTaskPO.getTaskDefKey());
//                XxlJobHelper.log(superviseTodoTaskPO.getTaskName());
//            }
//
//            /*ApprovalTaskCodeEnum[] values = ApprovalTaskCodeEnum.values();
//            for (int i = 2; i < values.length; i++) {
//                if (values[i].getTaskDefKey().equals(superviseTodoTaskPO.getTaskDefKey())) {
//                    superviseConfDTO = superviseConfApi.getSuperviseConfByTaskDefKey(values[i].getTaskDefKey()); //根据流程类型获得督办配置
//                    XxlJobHelper.log(superviseTodoTaskPO.getProcessInstanceName());
//                    XxlJobHelper.log(superviseTodoTaskPO.getTaskDefKey());
//                    XxlJobHelper.log(superviseTodoTaskPO.getTaskName());
//                    break;
//                }
//            }*/
//            if (superviseConfDTO == null) {
//                XxlJobHelper.log("相应的督办配置superviseConfDTO不存在");
//                //if (bpmTaskExtDO.getSuperviseStatus() != null) {
//                //    bpmTaskExtDO.setSuperviseStatus(DocRemindType.NULL.getRemindType());
//                //    bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//                //    XxlJobHelper.log("supervise_status状态不匹配，将supervise_status修改为null");
//                //}
//                XxlJobHelper.log("------------------------------------------------------------------------------");
//                return;
//            }
//
//            //  获得任务开始的时间
//            LocalDateTime taskStartTime = superviseTodoTaskPO.getTaskStartTime();
//            //  获得任务开始的当天0点时间
//            LocalDateTime ofDay = taskStartTime.with(LocalTime.MIN);
//            Date taskStartTimeDate = Date.from(ofDay.atZone(ZoneId.systemDefault()).toInstant());
//            //  获得逾期日期
//            Integer limitDays = superviseConfDTO.getLimitDays();
//            Calendar limitDaysCal = Calendar.getInstance();
//            limitDaysCal.setTime(taskStartTimeDate);
//            limitDaysCal = skipHolidayAdd(limitDaysCal,limitDays);
//            //limitDaysCal.add(Calendar.DATE, +limitDays);
//            //  获得提醒日期
//            Integer remindTime = Integer.parseInt(superviseConfDTO.getRemindTime());
//            Calendar remindTimeCal = Calendar.getInstance();
//            remindTimeCal.setTime(limitDaysCal.getTime());
//            remindTimeCal.add(Calendar.DATE,-remindTime);
//
//            XxlJobHelper.log("创建日期：" + simpleDateFormat.format(taskStartTimeDate));
//            XxlJobHelper.log("提醒时限：" + remindTime);
//            XxlJobHelper.log("提醒日期：" + simpleDateFormat.format(remindTimeCal.getTime()));
//            XxlJobHelper.log("办理时限：" + limitDays);
//            XxlJobHelper.log("逾期日期：" + simpleDateFormat.format(limitDaysCal.getTime()));
//
//            Calendar nowCal = Calendar.getInstance();
//
//
//            if (nowCal.compareTo(remindTimeCal) > 0 && nowCal.compareTo(limitDaysCal) < 0) {        //  督办、提醒
//                if (AffairHandleTaskNameCodeEnum.getByCode(superviseTodoTaskPO.getTaskDefKey()).getSuperviseType().equals("提醒")) {
//                    //  提醒状态
//                    ServiceException se = changeToRemind(bpmTaskExtDO, superviseTodoTaskPO);
//                    if (!Objects.isNull(se)) {
//                        serviceExceptions.add(se);
//                    }
//                } else {
//                    //  督办状态
//                    ServiceException se = changeToSupervise(bpmTaskExtDO, superviseTodoTaskPO);
//                    if (!Objects.isNull(se)) {
//                        serviceExceptions.add(se);
//                    }
//                }
////                sendMessage(bpmTaskExtDO, "dubantixing");
////                sendSMS(superviseTodoTaskPO, "dubantixing");
//            } else if (nowCal.compareTo(limitDaysCal) > 0) {        //  逾期
//                if (AffairHandleTaskNameCodeEnum.getByCode(superviseTodoTaskPO.getTaskDefKey()).getSuperviseType().equals("提醒")) {
//                    //  提醒+逾期状态
//                    ServiceException se = changeToRemindAndExceed(bpmTaskExtDO, superviseTodoTaskPO);
//                    if (!Objects.isNull(se)) {
//                        serviceExceptions.add(se);
//                    }
//                } else {
//                    //  督办+逾期状态
//                    ServiceException se = changeToSuperviseAndExceed(bpmTaskExtDO, superviseTodoTaskPO);
//                    if (!Objects.isNull(se)) {
//                        serviceExceptions.add(se);
//                    }
//                }
////                sendMessage(bpmTaskExtDO, "yuqitixing");
////                sendSMS(superviseTodoTaskPO, "yuqitixing");
//            } else {
//                //changeToNull(bpmTaskExtDO);
//            }
//        });
//
//        if (!CollectionUtils.isAnyEmpty(serviceExceptions)) {
//            throw serviceExceptions.get(0);
//        }
//    }
//
//    private ServiceException sendSMS(SuperviseTodoTaskPO superviseTodoTaskPO, String contentText) {
//        ServiceException serviceException = null;
//        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(superviseTodoTaskPO.getAssigneeUserId()).getCheckedData();
//        if (adminUserRespDTO != null) {
//            SmsSendSingleToUserReqDTO smsSendSingleToUserReqDTO = new SmsSendSingleToUserReqDTO();
//            smsSendSingleToUserReqDTO.setMobile(adminUserRespDTO.getMobile());
//            smsSendSingleToUserReqDTO.setTemplateCode(contentText); // TODO 督办提醒
//            smsSendSingleToUserReqDTO.setUserId(adminUserRespDTO.getId());
//            Map<String, Object> content = new HashMap<>();
//            //content.put("arg0","待审批");
//            //content.put("arg1", superviseTodoTaskPO.getProcessInstanceName().substring(6));
//            String processInstanceName = AffairHandleProcessNameTypeEnum.getNameByType(superviseTodoTaskPO.getProcessInstanceKey()).getDesc();
//            content.put("arg1", processInstanceName);
//            smsSendSingleToUserReqDTO.setTemplateParams(content);
//            try {
//                smsSendApi.sendSingleSmsToAdmin(smsSendSingleToUserReqDTO).getCheckedData();
//            } catch (ServiceException e) {
//                if (SMS_SEND_MOBILE_NOT_EXISTS.getCode().equals(e.getCode()) && SMS_SEND_MOBILE_NOT_EXISTS.getMsg().equals(e.getMessage())) {
//                    serviceException = exception(SMS_SEND_MOBILE_NOT_EXISTS);
//                    XxlJobHelper.log("ID为{}的用户的手机号码不存在", adminUserRespDTO.getId());
//                } else if (MASSAGE_TIMES_OVER_10.getCode().equals(e.getCode()) && MASSAGE_TIMES_OVER_10.getMsg().equals(e.getMessage())) {
//                    serviceException = exception(MASSAGE_TIMES_OVER_10);
//                    XxlJobHelper.log("ID为{}的用户的手机号码短信发送次数超过限制，手机号码：{}", adminUserRespDTO.getId(), adminUserRespDTO.getMobile());
//                }
//            }
//            XxlJobHelper.log("短信发送成功, 流程实例ID：{}, 流程实例名称：{}, 发送人员：{}", superviseTodoTaskPO.getProcessInstanceId(), superviseTodoTaskPO.getProcessInstanceName(), superviseTodoTaskPO.getAssigneeUserId());
//            try {
//                TimeUnit.MILLISECONDS.sleep(500);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        } else {
//            XxlJobHelper.log("人员数据为空：流程实例ID：{}", superviseTodoTaskPO.getProcessInstanceId());
//        }
//        return serviceException;
//    }
//
//    private ServiceException changeToRemind(BpmTaskExtDO bpmTaskExtDO, SuperviseTodoTaskPO superviseTodoTaskPO) {
//        ServiceException serviceException = null;
//        if (bpmTaskExtDO.getSuperviseStatus() == null || !DocRemindType.REMIND.getRemindType().equals(bpmTaskExtDO.getSuperviseStatus())) {
//            bpmTaskExtDO.setSuperviseStatus(DocRemindType.REMIND.getRemindType());
//            bpmTaskExtDO.setRemindCount(bpmTaskExtDO.getRemindCount()+1);
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//            XxlJobHelper.log("已到提醒日期，supervise_status修改为提醒状态1");
//            sendMessage(bpmTaskExtDO, "dubantixing");
//            ServiceException exception = sendSMS(superviseTodoTaskPO, "dubantixing");
//            if (!Objects.isNull(exception)) {
//                serviceException = exception;
//            }
//        } else {
//            XxlJobHelper.log("已到提醒日期，supervise_status已经为提醒状态1，不修改");
//        }
//        return serviceException;
//    }
//
//    private ServiceException changeToSupervise(BpmTaskExtDO bpmTaskExtDO, SuperviseTodoTaskPO superviseTodoTaskPO) {
//        ServiceException serviceException = null;
//        if (bpmTaskExtDO.getSuperviseStatus() == null || !DocRemindType.SUPERVISE.getRemindType().equals(bpmTaskExtDO.getSuperviseStatus())) {
//            bpmTaskExtDO.setSuperviseStatus(DocRemindType.SUPERVISE.getRemindType());
//            bpmTaskExtDO.setSuperviseCount(bpmTaskExtDO.getSuperviseCount()+1);
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//            XxlJobHelper.log("已到督办日期，supervise_status修改为督办状态2");
//            sendMessage(bpmTaskExtDO, "dubantixing");
//            ServiceException exception = sendSMS(superviseTodoTaskPO, "dubantixing");
//            if (!Objects.isNull(exception)) {
//                serviceException = exception;
//            }
//        } else {
//            XxlJobHelper.log("已到督办日期，supervise_status已经为督办状态2，不修改");
//        }
//        return serviceException;
//    }
//
//    private void changeToExceed(BpmTaskExtDO bpmTaskExtDO) {
//        if (bpmTaskExtDO.getSuperviseStatus() == null || !DocRemindType.EXCEED.getRemindType().equals(bpmTaskExtDO.getSuperviseStatus())) {
//            bpmTaskExtDO.setSuperviseStatus(DocRemindType.EXCEED.getRemindType());
//            bpmTaskExtDO.setOverdueCount(bpmTaskExtDO.getOverdueCount()+1);
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//            XxlJobHelper.log("已到逾期日期，supervise_status修改为逾期状态3");
//        } else {
//            XxlJobHelper.log("已到逾期日期，supervise_status已经为逾期状态3，不修改");
//        }
//    }
//
//    private ServiceException changeToRemindAndExceed(BpmTaskExtDO bpmTaskExtDO, SuperviseTodoTaskPO superviseTodoTaskPO) {
//        ServiceException serviceException = null;
//        if (bpmTaskExtDO.getSuperviseStatus() == null || !DocRemindType.REMIND_EXCEED.getRemindType().equals(bpmTaskExtDO.getSuperviseStatus())) {
//            bpmTaskExtDO.setSuperviseStatus(DocRemindType.REMIND_EXCEED.getRemindType());
//            bpmTaskExtDO.setRemindCount(bpmTaskExtDO.getRemindCount()+1);
//            bpmTaskExtDO.setOverdueCount(bpmTaskExtDO.getOverdueCount()+1);
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//            XxlJobHelper.log("已到逾期日期，supervise_status修改为提醒+逾期状态13");
//            sendMessage(bpmTaskExtDO, "yuqitixing");
//            ServiceException exception = sendSMS(superviseTodoTaskPO, "yuqitixing");
//            if (!Objects.isNull(exception)) {
//                serviceException = exception;
//            }
//
//        } else {
//            XxlJobHelper.log("已到逾期日期，supervise_status已经为提醒+逾期状态13，不修改");
//        }
//        return serviceException;
//    }
//
//    private ServiceException changeToSuperviseAndExceed(BpmTaskExtDO bpmTaskExtDO, SuperviseTodoTaskPO superviseTodoTaskPO) {
//        ServiceException serviceException = null;
//        if (bpmTaskExtDO.getSuperviseStatus() == null || !DocRemindType.SUPERVISE_EXCEED.getRemindType().equals(bpmTaskExtDO.getSuperviseStatus())) {
//            bpmTaskExtDO.setSuperviseStatus(DocRemindType.SUPERVISE_EXCEED.getRemindType());
//            bpmTaskExtDO.setSuperviseCount(bpmTaskExtDO.getSuperviseCount()+1);
//            bpmTaskExtDO.setOverdueCount(bpmTaskExtDO.getOverdueCount()+1);
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//            XxlJobHelper.log("已到逾期日期，supervise_status修改为督办+逾期状态23");
//            sendMessage(bpmTaskExtDO, "yuqitixing");
//            ServiceException exception = sendSMS(superviseTodoTaskPO, "yuqitixing");
//            if (!Objects.isNull(exception)) {
//                serviceException = exception;
//            }
//        } else {
//            XxlJobHelper.log("已到逾期日期，supervise_status已经为督办+逾期状态23，不修改");
//        }
//        return serviceException;
//    }
//
//    private void changeToNull(BpmTaskExtDO bpmTaskExtDO) {
//        if (bpmTaskExtDO.getSuperviseStatus() != null) {
//            //bpmTaskExtDO.setSuperviseStatus(DocRemindType.NULL.getRemindType());
//            //bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//            bpmTaskService.setSuperviseStatusToNull(bpmTaskExtDO.getId());
//            XxlJobHelper.log("未到提醒、督办和逾期日期，supervise_status状态不匹配，修改为null");
//        } else {
//            XxlJobHelper.log("未到提醒、督办和逾期日期，supervise_status状态为null");
//        }
//    }
//
//    private void sendMessage(BpmTaskExtDO bpmTaskExtDO, String contentText) {
//        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(bpmTaskExtDO.getProcessInstanceId());
//        if (bpmProcessInstanceExtDO != null) {
//
//            AppNotificationCreateDTO appNotificationCreateDTO = new AppNotificationCreateDTO();
//            appNotificationCreateDTO.setBizId(bpmProcessInstanceExtDO.getProcessInstanceId());
//            appNotificationCreateDTO.setBizType(bpmProcessInstanceExtDO.getProcessKey());
//            appNotificationCreateDTO.setBizTaskDefKey(bpmTaskExtDO.getTaskDefKey());
//            Set<Long> targetUserIds = Collections.singleton(bpmTaskExtDO.getAssigneeUserId());
//            if (targetUserIds == null) {
//                XxlJobHelper.log("流程实例对应办理人为空：{}", bpmTaskExtDO.getProcessInstanceId());
//                return;
//            }
//            appNotificationCreateDTO.setTargetUserIds(targetUserIds);
//            //appNotificationCreateDTO.setTitle(superviseTodoTaskPO.getTaskName());
//
//            if ("dubantixing".equals(contentText)) {
//                appNotificationCreateDTO.setNoticeTypeEnum(AppNoticeTypeEnum.SUPERVISE_NOTICE);
//                CommonResult<Boolean> createNoticeRes = appNotificationApi.createUnread(appNotificationCreateDTO);
//                if (createNoticeRes.isError()) {
//                    XxlJobHelper.log(createNoticeRes.getMsg());
//                } else {
//                    XxlJobHelper.log("app督办提醒通知发送成功, 流程实例ID：{}, 流程实例名称：{}, 发送人员：{}", bpmProcessInstanceExtDO.getProcessInstanceId(), bpmProcessInstanceExtDO.getName(), targetUserIds);
//                }
//            } else if ("yuqitixing".equals(contentText)) {
//                appNotificationCreateDTO.setNoticeTypeEnum(AppNoticeTypeEnum.OVERDUE_NOTICE);
//                CommonResult<Boolean> createNoticeRes = appNotificationApi.createUnread(appNotificationCreateDTO);
//                if (createNoticeRes.isError()) {
//                    XxlJobHelper.log(createNoticeRes.getMsg());
//                } else {
//                    XxlJobHelper.log("app逾期通知发送成功, 流程实例ID：{}, 流程实例名称：{}, 发送人员：{}", bpmProcessInstanceExtDO.getProcessInstanceId(), bpmProcessInstanceExtDO.getName(), targetUserIds);
//                }
//            }
//
//        } else {
//            XxlJobHelper.log("流程实例数据为空：{}", bpmTaskExtDO.getProcessInstanceId());
//        }
//    }
//
//    public Calendar skipHolidayAdd(Calendar cal, int addDays) {
//        if (addDays == 0) {
//            return cal;
//        }
//
//        int realAddDays = addDays;
//
//        LocalDate tempDate = cal.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//        LocalDate targetDate = tempDate.plusDays(addDays);
//
//        while (addDays > 0) {
//            addDays = 0;
//
//            List<CalendarWnlRespDTO> calList = calendarWnlApi.getCalendarInRange(tempDate, targetDate).getCheckedData();
//            if (!calList.isEmpty()) {
//                //  stream流count方法
//                addDays = (int) calList.stream()
//                        .filter(calendarWnlRespDTO -> calendarWnlRespDTO.getHolidayType() == 1 || calendarWnlRespDTO.getHolidayType() == 2)
//                        .count();
//            }
//            if (addDays == 0) {
//                break;
//            }
//
//            tempDate = targetDate.plusDays(1);
//            targetDate = targetDate.plusDays(addDays);
//
//            realAddDays += addDays;
//        }
//
//        while (addDays < 0) {
//            addDays = 0;
//
//            List<CalendarWnlRespDTO> calList = calendarWnlApi.getCalendarInRange(targetDate, tempDate).getCheckedData();
//            if (!calList.isEmpty()) {
//                //  stream流count方法
//                addDays = (int) calList.stream()
//                        .filter(calendarWnlRespDTO -> calendarWnlRespDTO.getHolidayType() == 1 || calendarWnlRespDTO.getHolidayType() == 2)
//                        .count();
//            }
//            if (addDays == 0) {
//                break;
//            }
//
//            addDays = -addDays;
//            tempDate = targetDate.plusDays(-1);
//            targetDate = targetDate.plusDays(addDays);
//
//            realAddDays += addDays;
//        }
//
//        cal.add(Calendar.DATE, realAddDays);
//        return cal;
//    }
//
//
//}
//
