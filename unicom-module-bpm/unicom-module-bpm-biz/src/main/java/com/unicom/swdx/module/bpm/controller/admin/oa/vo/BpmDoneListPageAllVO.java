package com.unicom.swdx.module.bpm.controller.admin.oa.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BpmDoneListPageAllVO {
    @ApiModelProperty(value = "待办理事项", required = true)
    private String task;

    @ApiModelProperty(value = "事项发起人", required = true)
    private String name;

    @ApiModelProperty(value = "部门", required = true)
    private String deptName;

    @ApiModelProperty(value = "发起时间", required = true)
    private String initiationTime;

//    @ApiModelProperty(value = "事项来源", required = true)
//    private String source;
    @ApiModelProperty(value = "流程实例的编号", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "类别", required = true)
    private String category;

    @ApiModelProperty(value = "状态", required = true)
    private String status;

    @ApiModelProperty(value = "年度标签", required = true)
    private String yeartag;

    @ApiModelProperty(value = "年度标签", required = true)
    private String shandle;

    @ApiModelProperty(value = "更新时间", required = true)
    private String updateTime;

}
