package com.unicom.swdx.module.bpm.framework.rpc.config;

import com.unicom.swdx.module.bpm.api.affairHandle.AffairHandleApi;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.oa.api.LeaveApi;
import com.unicom.swdx.module.oa.api.LectureApi;
import com.unicom.swdx.module.oa.api.OutReportApi;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.*;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.todo.TodoItemServiceApi;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.permission.RoleApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {RoleApi.class, DeptApi.class, PostApi.class, AdminUserApi.class, SmsSendApi.class, DictDataApi.class, TodoItemServiceApi.class,
        FileApi.class, AffairHandleApi.class, ReceiveApi.class, LectureApi.class, VacationDutyApi.class, LeaveApi.class, OutReportApi.class, ScheduleApi.class, SummaryApi.class,
        ScheduleServiceApi.class})
public class RpcConfiguration {
}
