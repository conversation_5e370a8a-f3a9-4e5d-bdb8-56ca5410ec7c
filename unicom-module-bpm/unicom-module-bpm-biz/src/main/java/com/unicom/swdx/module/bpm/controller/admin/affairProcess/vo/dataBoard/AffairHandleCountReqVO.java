package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("获取政务办理计数 Request VO")
@Data
@ToString(callSuper = true)
public class AffairHandleCountReqVO  extends PageParam {
    @ApiModelProperty(value = "处室名称")
    private String deptName;

    @ApiModelProperty(value = "处室ID")
    private Long deptId;

    @ApiModelProperty(value = "办结日期-开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date startDate;

    @ApiModelProperty(value = "办结日期-结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date endDate;


    @ApiModelProperty(value = "排序列,参考对应进入二级报表的跳转名称")
    private String orderColumn;

    @ApiModelProperty(value = "排序类型 asc升序 desc降序")
    private String orderType;
}
