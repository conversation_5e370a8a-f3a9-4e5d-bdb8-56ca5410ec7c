package com.unicom.swdx.module.bpm.listener;

import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEvent;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEventListener;
import com.unicom.swdx.module.oa.enums.ProcessDefinitionKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class OALectureResultListener extends BpmProcessInstanceResultEventListener {

    @Resource
    @Lazy
    private OALectureResultDeal deal;

    @Override
    protected String getProcessDefinitionKey() {
        return ProcessDefinitionKeyConstants.LECTURE_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceResultEvent event) {
        deal.deal(event);
    }


}
