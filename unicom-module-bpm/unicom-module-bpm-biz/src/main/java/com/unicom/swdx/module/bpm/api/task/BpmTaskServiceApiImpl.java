package com.unicom.swdx.module.bpm.api.task;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.*;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.apache.dubbo.config.annotation.DubboService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

/**
 * <AUTHOR>
 * @date 2022/12/16  19:25
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class BpmTaskServiceApiImpl implements BpmTaskServiceApi {

    @Resource
    private BpmTaskService bpmTaskService;

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Override
    public CommonResult<Boolean> approveTask(Long userId, BpmTaskApproveReqDTO reqVO) {
        BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
        BeanUtils.copyProperties(reqVO, bpmTaskApproveReqVO);
        bpmTaskApproveReqVO.setImageUrl(reqVO.getHandSignature());
        if(Objects.isNull(reqVO.getTaskType())){
            bpmTaskApproveReqVO.setTaskType(2);
        }
        boolean res = bpmTaskService.approveTask(userId, bpmTaskApproveReqVO);
        //更新流程状态标识：null正常
        processInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getProcessInstanceId(), null);
        return success(res);
    }

    @Override
    public CommonResult<Boolean> rejectTask(Long userId, BpmTaskRejectReqDTO reqVO) {
        BpmTaskRejectReqVO bpmTaskRejectReqVO = new BpmTaskRejectReqVO();
        BeanUtils.copyProperties(reqVO, bpmTaskRejectReqVO);
        bpmTaskRejectReqVO.setImageUrl(reqVO.getHandSignature());
        boolean result = bpmTaskService.rejectTask(userId, bpmTaskRejectReqVO);
        //更新流程状态标识：null正常, 1撤回, 2驳回
        processInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getProcessInstanceId(), "2");
        return success(result);
    }

    @Override
    public Boolean isLast(BpmTaskApproveReqDTO reqVO) {
        return bpmTaskService.isLast(reqVO.getId(), reqVO.getVariables());
    }

    @Override
    public CommonResult<Map<String,Object>> isSequenceLast(String processInstanceId) {
        return success(bpmTaskService.isSequenceLast(processInstanceId));
    }

    @Override
    public CommonResult<LocalDateTime> getTime(String processInstanceId) {
        return success(bpmTaskService.getTime(processInstanceId));
    }

    @Override
    public CommonResult<Boolean> updateTaskAssignee(Long userId, BpmTaskUpdateAssigneeReqDTO reqVO) {
        BpmTaskUpdateAssigneeReqVO bpmTaskUpdateAssigneeReqVO = new BpmTaskUpdateAssigneeReqVO();
        BeanUtils.copyProperties(reqVO, bpmTaskUpdateAssigneeReqVO);
        bpmTaskService.updateTaskAssignee(userId, bpmTaskUpdateAssigneeReqVO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> changeTaskAssignee(BpmTaskUpdateAssigneeReqDTO reqVO) {
        BpmTaskUpdateAssigneeReqVO bpmTaskUpdateAssigneeReqVO = new BpmTaskUpdateAssigneeReqVO();
        BeanUtils.copyProperties(reqVO, bpmTaskUpdateAssigneeReqVO);
        bpmTaskService.changeTaskAssignee(bpmTaskUpdateAssigneeReqVO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> taskReject(BpmTaskDTO bpmTaskDTO) {
        BpmTaskVO bpmTaskVO = new BpmTaskVO();
        BeanUtils.copyProperties(bpmTaskDTO, bpmTaskVO);
        bpmTaskService.taskReject(bpmTaskVO);
        //更新本次任务为驳回状态
        BpmTaskExtDO bpmTaskExtDO = new BpmTaskExtDO();
        bpmTaskExtDO.setTaskId(bpmTaskDTO.getTaskId());
        bpmTaskExtDO.setRevokeStatus("2");
        bpmTaskExtDO.setReason(bpmTaskDTO.getComment());
        bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
        //更新流程状态标识：2驳回
        processInstanceService.updateProcessInstanceExtFlowFlag(bpmTaskDTO.getInstanceId(), "2");
        return success(true);
    }

    @Override
    public CommonResult<Boolean> revokeProcess(BpmTaskDTO bpmTaskDTO) {

        BpmTaskVO bpmTaskVO = new BpmTaskVO();
        BeanUtils.copyProperties(bpmTaskDTO, bpmTaskVO);
        bpmTaskService.revokeProcess(bpmTaskVO);
        BpmTaskExtDO bpmTaskExtDO = new BpmTaskExtDO();
        bpmTaskExtDO.setTaskId(bpmTaskDTO.getTaskId());
        bpmTaskExtDO.setRevokeStatus("1");
        bpmTaskExtDO.setReason(bpmTaskDTO.getComment());
        bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
        //更新流程状态标识：1撤回
        processInstanceService.updateProcessInstanceExtFlowFlag(bpmTaskDTO.getInstanceId(), "1");
        return success(true);
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getTaskExt(String processInstanceId) {
        return success(bpmTaskService.getTasksAssigneeByProcessInstanceId(processInstanceId));
    }

    @Override
    public CommonResult<BpmTaskExtDTO> getDetailTask(String processInstanceId, Long assigneeUserId) {
        return success(bpmTaskService.getDetailTask(assigneeUserId, processInstanceId));
    }

    @Override
    public CommonResult<BpmTaskExtDTO> getAllTask(String processInstanceId, Long assigneeUserId, String result) {
        return success(bpmTaskService.getAllTask(assigneeUserId, processInstanceId, result));
    }

    @Override
    public CommonResult<String> getTaskKeyByProcessInstanceId(String processInstanceId) {
        return success(bpmTaskService.getTaskKeyByProcessInstanceId(processInstanceId));
    }

    @Override
    public CommonResult<Map<String, Object>> getTaskNextApproval(Map<String, String> reqMap) {
        String taskId = reqMap.get("taskId");
        String type = reqMap.get("type");
        String proDefinitionKey = reqMap.get("proDefinitionKey");
        String condition = reqMap.get("condition");
        return success(bpmTaskService.getNextTaskApproval(taskId, type, proDefinitionKey, condition));
    }

    @Override
    public CommonResult<Map<String, Object>> getTaskNextApprovals(Long loginUserId, String taskId, String nextTaskName, Long deptId) {
//        Long loginUserId = Long.valueOf(reqMap.get("loginUserId").toString());
//        String taskId = reqMap.get("taskId").toString();
        return success(bpmTaskService.getNextTaskApprovals(loginUserId, taskId, nextTaskName, deptId));
    }

    @Override
    public CommonResult<Boolean> updateTaskSuperviseStatus(String taskId, String superviseStatus) {
        return success(bpmTaskService.updateTaskSuperviseStatus(taskId, superviseStatus));
    }

    @Override
    public List<BpmTaskRespDTO> getTaskListByProcessInstanceId(String processInstanceId) {
        List<BpmTaskRespVO> bpmTaskList = bpmTaskService.getTaskListByProcessInstanceId(processInstanceId);
        List<BpmTaskRespDTO> bpmTaskRespList = new ArrayList<>();
        for (BpmTaskRespVO taskRespVO : bpmTaskList) {
            BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
            BeanUtils.copyProperties(taskRespVO, bpmTaskRespDTO);
            bpmTaskRespList.add(bpmTaskRespDTO);

        }
        return bpmTaskRespList;
    }

    @Override
    public List<Map<String, Object>> getTaskLogByProcessInstanceId(String processInstanceId){
        return bpmTaskService.getTaskLogByProcInsId(processInstanceId);
    }

    @Override
    public CommonResult<Boolean> restartProcess(BpmRestartDTO bpmRestartDTO) {
        Map<String, Object> variables = new HashMap<>();
        if(CollUtil.isNotEmpty(bpmRestartDTO.getVariables())){
            variables.putAll(bpmRestartDTO.getVariables());
        }
        variables.put("unapproved", null);
        variables.put("taskType", null);
        variables.put("waitToRejectToStarter", null);
        variables.put("completed", null);
        variables.put("flag", null);
        BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
        bpmTaskApproveReqVO.setVariables(variables);
        bpmTaskApproveReqVO.setId(this.getTaskListByProcessInstanceId(bpmRestartDTO.getProcessInstanceId())
                .stream().filter(t -> t.getResult() == 1).findFirst().orElse(new BpmTaskRespDTO()).getId());
        bpmTaskApproveReqVO.setProcessInstanceId(bpmRestartDTO.getProcessInstanceId());
        bpmTaskApproveReqVO.setTaskType(0);
        bpmTaskApproveReqVO.setEndTime(bpmRestartDTO.getTime());
        // 通过重新发起任务，重新流转到下一个任务
        boolean res = bpmTaskService.approveTask(bpmRestartDTO.getLoginUserId(), bpmTaskApproveReqVO);
        return success(res);
    }

    @Override
    public String getNextTaskName(String taskId) {
        return bpmTaskService.getNextTaskName(taskId);
    }

    @Override
    public void saveCustomTaskExt(BpmTaskExtDTO bpmTaskExtDTO) {
        bpmTaskService.createCustomTaskExt(bpmTaskExtDTO);
    }

    @Override
    public void saveBatchCustomTaskExt(List<BpmTaskExtDTO> records) {
        records.forEach(record->{
            bpmTaskService.createCustomTaskExt(record);
        });
    }

    @Override
    public Map<String, String> getNeededTaskInfo(String category, Long loginUserId, String processInstanceId, Boolean isReceived, Boolean isDealt) {
        return bpmTaskService.getNeededTaskInfo(category,loginUserId, processInstanceId, isReceived, isDealt);
    }

    @Override
    public CommonResult<List<Long>> superviseTask(Long loginUserId, String processInstanceId) {
        return success(bpmTaskService.superviseTaskByProcInsId(loginUserId,processInstanceId));
    }
}
