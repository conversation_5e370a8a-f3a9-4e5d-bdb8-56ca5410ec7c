package com.unicom.swdx.module.bpm.controller.admin.external.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel("审批通过/驳回 Request VO")
public class BpmApproveReqVO {
    @ApiModelProperty(value = "审批人的用户id", required = true)
    private Long userId;

    @ApiModelProperty(value = "正在审批的任务Id", required = true)
    private String taskId;

    @ApiModelProperty(value = "正在审批的任务Id", required = true)
    private String processInstanceId;

    @ApiModelProperty("审批意见")
    private String comment;

    @ApiModelProperty(value = "审批过程中需要保存的日志信息-例如JsonStr形式保存")
    private String logParameters;

    /**
     * 审批过程中参数信息
     */
    @ApiModelProperty(value = "审批参数")
    private Map<String, Object> variables;

}
