package com.unicom.swdx.module.bpm.dal.mysql.definition;

import com.unicom.swdx.module.bpm.controller.admin.definition.vo.group.BpmUserGroupPageReqVO;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmUserGroupDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmUserGroupMapper extends BaseMapperX<BpmUserGroupDO> {

    default PageResult<BpmUserGroupDO> selectPage(BpmUserGroupPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpmUserGroupDO>()
                .likeIfPresent(BpmUserGroupDO::getName, reqVO.getName())
                .eqIfPresent(BpmUserGroupDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(BpmUserGroupDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BpmUserGroupDO::getId));
    }

    default List<BpmUserGroupDO> selectListByStatus(Integer status) {
        return selectList(BpmUserGroupDO::getStatus, status);
    }

    default List<BpmUserGroupDO> isExistedByName(String name, Long id) {
        return selectList(new LambdaQueryWrapperX<BpmUserGroupDO>().eqIfPresent(BpmUserGroupDO::getName,name).neIfPresent(BpmUserGroupDO::getId,id));
    }

}
