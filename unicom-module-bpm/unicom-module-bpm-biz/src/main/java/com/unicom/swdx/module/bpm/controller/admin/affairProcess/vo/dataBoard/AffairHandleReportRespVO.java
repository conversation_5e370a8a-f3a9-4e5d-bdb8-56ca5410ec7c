package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AffairHandleReportRespVO {
    @ApiModelProperty(value = "处室ID", required = true)
    private Long deptId;

    @ApiModelProperty(value = "处室名称")
    private String deptName;


    @ApiModelProperty(value = "办理总数")
    private Integer totalHandle;

    @ApiModelProperty(value = "好评数")
    private Integer favourableComment;

    @ApiModelProperty(value = "督办数")
    private Integer supervision;

    @ApiModelProperty(value = "逾期数")
    private Integer overdue;
}
