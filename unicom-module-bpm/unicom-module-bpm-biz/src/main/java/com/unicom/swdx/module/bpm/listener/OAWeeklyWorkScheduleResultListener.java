package com.unicom.swdx.module.bpm.listener;

import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEvent;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEventListener;
import com.unicom.swdx.module.oa.api.ScheduleApi;
import com.unicom.swdx.module.oa.api.dto.ScheduleDTO;
import com.unicom.swdx.module.oa.enums.ProcessDefinitionKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class OAWeeklyWorkScheduleResultListener extends BpmProcessInstanceResultEventListener {

    @Resource
    private ScheduleApi scheduleApi;


    @Override
    protected String getProcessDefinitionKey() {
        return ProcessDefinitionKeyConstants.SCHEDULE_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceResultEvent event) {
        if(Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())){
            String id = event.getId();
            ScheduleDTO scheduleDTO = scheduleApi.getItemId(id);
            // 改变安排上报表的结果为汇总中
            scheduleApi.updateStatusById(scheduleDTO.getId(), BpmProcessInstanceResultEnum.SUMMARIZE.getResult());
        }
    }
}
