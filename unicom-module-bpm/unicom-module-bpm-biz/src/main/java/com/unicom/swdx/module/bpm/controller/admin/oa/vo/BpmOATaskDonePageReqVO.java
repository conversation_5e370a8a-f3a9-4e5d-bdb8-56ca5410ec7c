package com.unicom.swdx.module.bpm.controller.admin.oa.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 流程任务的 Done 已办的分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmOATaskDonePageReqVO extends PageParam {

    @ApiModelProperty(value = "流程任务名", example = "sk")
    private String name;

    @ApiModelProperty(value = "开始的创建收间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginCreateTime;

    @ApiModelProperty(value = "结束的创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreateTime;

    @ApiModelProperty(value = "流程分类", notes = "参见 bpm_model_category 数据字典", example = "1")
    private String category;

    @ApiModelProperty(value = "是否时间升序")
    private boolean timeAsc;

    @ApiModelProperty(value = "事项发起人", required = true)
    private Long loginUserId;

    @ApiModelProperty(value = "事项名称", required = true)
    private String task;

    @ApiModelProperty(value = "发起人", required = true)
    private String Initiator;

    @ApiModelProperty(value = "事项来源", required = true)
    private String source;

    @ApiModelProperty(value = "是否为小程序", required = true)
    private Integer isApp=0;
}
