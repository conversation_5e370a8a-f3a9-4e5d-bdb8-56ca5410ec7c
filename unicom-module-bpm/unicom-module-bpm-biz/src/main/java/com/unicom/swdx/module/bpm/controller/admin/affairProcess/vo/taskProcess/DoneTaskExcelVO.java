package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.unicom.swdx.module.bpm.enums.DictTypeConstants.*;

@ApiModel("政务子系统 - 已办任务导出 Response VO")
@Data
public class DoneTaskExcelVO {

    @ExcelProperty(value = "服务事项")
    private String processInstanceName;

    @ExcelProperty(value = "事项类型",converter = DictConvert.class)
    @DictFormat(AFFAIR_BUSINESS_TYPE)
    private String processType;

    @ExcelProperty(value = "申请单位/人")
    private String applyEnterpriseName;

    //@ExcelProperty(value = "申请日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    //private LocalDateTime processInstanceStartTime;
    @ExcelProperty(value = "申请日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate submitDate;

    @ExcelProperty(value = "当前节点",converter = DictConvert.class)
    @DictFormat(AFFAIR_HANDLER_NODE)
    private String taskDefKey;

    @ExcelProperty(value = "办理人")
    private String assigneeUserNickname;

    @ExcelProperty(value = "所属组织")
    private String assigneeUserDeptName;

    @ExcelProperty(value = "状态",converter = DictConvert.class)
    @DictFormat(AFFAIR_BUSINESS_STATUS)
    private Integer processInstanceResult;



}
