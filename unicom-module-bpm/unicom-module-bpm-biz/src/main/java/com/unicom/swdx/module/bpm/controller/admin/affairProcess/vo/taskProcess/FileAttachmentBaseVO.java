package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FileAttachmentBaseVO {

    @ApiModelProperty(value = "对象ID")
    private Long typeId;

    @ApiModelProperty(value = "对象类型")
    private String type;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件业务类型")
    private String bussiType;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "标识")
    private String flag;
}
