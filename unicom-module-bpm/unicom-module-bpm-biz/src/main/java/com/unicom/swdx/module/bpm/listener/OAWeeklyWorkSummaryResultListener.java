package com.unicom.swdx.module.bpm.listener;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceRespDTO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEvent;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEventListener;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.ScheduleApi;
import com.unicom.swdx.module.oa.api.SummaryApi;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.api.dto.SummaryDTO;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.enums.ProcessDefinitionKeyConstants;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class OAWeeklyWorkSummaryResultListener extends BpmProcessInstanceResultEventListener {

    @Resource
    SummaryApi summaryApi;

    @Resource
    private ScheduleApi scheduleApi;

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private ReceiveApi receiveApi;

    @Resource
    private SmsSendApi smsSendApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Override
    protected String getProcessDefinitionKey() {
        return ProcessDefinitionKeyConstants.SUMMARY_KEY;
    }

    @Override
    @SneakyThrows
    protected void onEvent(BpmProcessInstanceResultEvent event) {
        Thread.sleep(2000);

        if(Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())){
            String id = event.getId();

            String scheduleIds = summaryApi.getWorkScheduleId(id);
            SummaryDTO summaryDTO = summaryApi.getItemId(id);
            List<Long> userIds = new ArrayList<>();
            // 改变安排上报表的结果为已完成
            if(scheduleIds != null) {
                List<Long> ids = new ArrayList<>(Arrays.stream(scheduleIds.split(",")).map(Long::parseLong).collect(Collectors.toList()));
                for (Long scheduleId : ids) {
                    scheduleApi.updateStatusById(scheduleId, BpmProcessInstanceResultEnum.APPROVE.getResult());
                }
                // 抄送给抄送人员
                log.info("开始抄送一周工作安排流程，id = {}", summaryDTO.getId());
                String copyTo= summaryApi.getCopyTo(summaryDTO.getId());
                if(copyTo != null) {
                    List<Long> userId = Arrays.stream(copyTo.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    userIds.addAll(userId);
                }
                userIds = userIds.stream().distinct().collect(Collectors.toList());
                BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(id);
                ReceiveDTO receiveDTO = new ReceiveDTO();
                receiveDTO.setProcessInstanceId(id);
                receiveDTO.setCategory(processInstanceVO.getCategory());
                receiveDTO.setApplyTime(processInstanceVO.getCreateTime());

                receiveDTO.setItemId(summaryDTO.getId());

                receiveDTO.setUserIds(userIds);
                receiveDTO.setPromoterUserId(processInstanceVO.getStartUser().getId());
                receiveApi.save(receiveDTO);
                //发送短信
                Integer year = summaryApi.getYear(id);
                Integer week = summaryApi.getWeek(id);
                String semester = summaryApi.getSemester(id);
                String message =  String.format("【湖南省委党校】%s年第%s周工作安排已汇总，请尽快登录湖南省党校系统一体化信息平台进行查阅。"
                        , year, week);
                List<AdminUserRespDTO> users  =  userApi.getUsers(userIds).getCheckedData();

                if(CollUtil.isNotEmpty(users)){

                    String mobile = users.stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                            .reduce( (k,v)-> k +"," + v  ).get();

                    Map<String,Object> map = new HashMap<>();
                    map.put("arg1", message);

                    smsSendApi.sendSingleSms(mobile, null,null ,"admin-sms-login-new",map);

                }
                //创建日程已经完成
                //id为pid
                summaryApi.getItemId(id);
                //userIds是抄送人列表
                List<Long> personIds =  summaryApi.getUsersById(id);
                // 合并两个列表并取并集（去重）
                List<Long> unionIds = Stream.of(userIds, personIds)
                        .flatMap(List::stream)
                        .distinct()
                        .collect(Collectors.toList());
                //添加一周报告汇总时间到日程
                Map<String, LocalDate> dateMap  = summaryApi.getDateById(id);
                ScheduleDto scheduleDto = new ScheduleDto();
                //操作所有参与人和抄送人
                scheduleDto.setUserIds(unionIds);
                //设置发起人id这边不需要这个字段
                scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
                //设置发起时间dateMap.get("startMap")
                LocalDate startDate =dateMap.get("startDate");
                LocalDate endDate =dateMap.get("endDate");
                scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                //传流程id赋值到
                scheduleDto.setProcessInstanceId(id);
                //传类型传14为一周安排汇总
                scheduleDto.setType(14);
                scheduleDto.setWeek(week);
                scheduleDto.setYear(year);
                scheduleServiceApi.createSchedulesOther(scheduleDto);

//                //删除姓名
//                message =  String.format("假期变更通知\n您申请的假期变更已完成，假期开始时间：【%s】，假期结束时间：【%s】"
//                        ,  leaveStart, leaveEnd);
//
                message =  String.format("待阅提醒\n %s年第%s周工作安排已汇总，请尽快进入OA系统进行查阅",
                        year,week);
                //待办事项办添加
                //设置内容
                receiveDTO.setCategory(message);

                //参与人和抄送人
                receiveDTO.setUserIds(personIds);
                //流程id
                receiveDTO.setProcessInstanceId(id);
                //
                //应该把发起人和时间加上,上面抄送已经加上

//        receiveApi.save(receiveDTO);
                receiveApi.saveWorkSummary(receiveDTO);

            }
        }
    }
}
