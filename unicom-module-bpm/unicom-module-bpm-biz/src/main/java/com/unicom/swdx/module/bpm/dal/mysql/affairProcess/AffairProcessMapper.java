package com.unicom.swdx.module.bpm.dal.mysql.affairProcess;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess.*;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmDoneListPageAllVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmTodoListPageVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceHandleLogRespVo;
import com.unicom.swdx.module.bpm.controller.app.dto.querycombined.AppQueryCombinedMap;
import com.unicom.swdx.module.bpm.controller.app.dto.querycombined.CombinedSqlInclusion;
import com.unicom.swdx.module.bpm.job.po.SuperviseTodoTaskPO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface AffairProcessMapper {

    List<BpmTaskPageItemRespVO> selectAllTaskForAppSearch(IPage<BpmTaskPageItemRespVO> page, @Param("userId") Long userId, @Param("pageReqVO") AppSearchPageReqVO pageReqVO);

    List<BpmTaskPageItemRespVO> selectTodoAffairTask(IPage<BpmTaskPageItemRespVO> page, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO);

//    List<BpmTaskPageItemRespVO> selectTodoTask(IPage<BpmTaskPageItemRespVO> page, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO);

    List<BpmTaskPageItemRespVO> selectDoneAffairTask(IPage<BpmTaskPageItemRespVO> donePage, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO);

    List<BpmTaskPageItemRespVO> selectStartAffairTask(IPage page, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO);

    //app查询待办mapper,union
    List<BpmTaskPageItemRespVO> selectCombinedForAppTodo(IPage<BpmTaskPageItemRespVO> page, @Param("userId") Long userId, @Param("conditionMap") AppQueryCombinedMap conditionMap, @Param("sqlInclusionMap") Map<String, CombinedSqlInclusion> sqlIncludedMap);

    //app查询已办mapper,union
    List<BpmTaskPageItemRespVO> selectCombinedForAppDone(IPage<BpmTaskPageItemRespVO> donePage, @Param("userId") Long userId, @Param("conditionMap") AppQueryCombinedMap conditionMap, @Param("sqlInclusionMap") Map<String, CombinedSqlInclusion> sqlIncludedMap);

    //app查询发起的mapper,union
    List<BpmTaskPageItemRespVO> selectCombinedForAppMyLaunch(IPage<BpmTaskPageItemRespVO> donePage, @Param("userId") Long userId, @Param("conditionMap") AppQueryCombinedMap conditionMap, @Param("sqlInclusionMap") Map<String, CombinedSqlInclusion> sqlIncludedMap);

    List<Long> selectAssigneeUserIds(@Param("processInstanceId") String processInstanceId, @Param("taskDefKey") String taskDefKey);

    List<Long> selectAllAssigneeUserIds(@Param("processInstanceId") String processInstanceId, @Param("taskDefKey") String taskDefKey);


    List<BpmProcessInstanceHandleLogRespVo> selectHandleLog(@Param("processInstanceId") String processInstanceId);

    List<BpmProcessInstanceHandleLogRespVo> selectHandleLogResultNoOne(@Param("processInstanceId") String processInstanceId);

    List<String> selectFormVariables(@Param("processKey") String processKey,@Param("formValue") String formValue);

    Integer selectTodoAffairTaskCount(@Param("userId") Long userId,@Param("keys") List<String> keys);
    
    @MapKey("supervise_status")
    List<Map<String, Object>> selectTodoTaskCountForApp(@Param("userId") Long userId, @Param("sqlInclusionMap") Map<String, CombinedSqlInclusion> sqlIncludedMap);

    List<SuperviseTodoTaskPO> selectSuperviseTodoTasks();

    List<BpmTaskPageItemRespVO> selectTodoAffairHandleTask(IPage<BpmTaskPageItemRespVO> page, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO,
                                                           @Param("handleSet") List<String> handleSet,@Param("checkSet") List<String> checkSet,
                                                           @Param("distributeSet") List<String> distributeSet,@Param("feedbackSet") List<String> feedbackSet);

    List<BpmTaskPageItemRespVO> selectTodoAffairHandleTask2(@Param("userId") Long userId, @Param("pageReqVO") TodoTaskExportReqVO pageReqVO,
                                                            @Param("handleSet") List<String> handleSet,@Param("checkSet") List<String> checkSet,
                                                            @Param("distributeSet") List<String> distributeSet,@Param("feedbackSet") List<String> feedbackSet);


    List<BpmTaskPageItemRespVO> selectDoneAffairHandleTask(IPage<BpmTaskPageItemRespVO> donePage, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO,
                                                           @Param("handleSet") List<String> handleSet,@Param("checkSet") List<String> checkSet,
                                                           @Param("distributeSet") List<String> distributeSet,@Param("feedbackSet") List<String> feedbackSet);

    List<BpmTaskPageItemRespVO> selectDoneAffairHandleTask2( @Param("userId") Long userId, @Param("pageReqVO") DoneTaskExportReqVO pageReqVO,
                                                             @Param("handleSet") List<String> handleSet,@Param("checkSet") List<String> checkSet,
                                                             @Param("distributeSet") List<String> distributeSet,@Param("feedbackSet") List<String> feedbackSet);

    List<BpmTaskPageItemRespVO> selectSuperviseAffairHandleTask(IPage<BpmTaskPageItemRespVO> supervisePage, @Param("userId") Long userId, @Param("pageReqVO") BpmTaskPageReqVO pageReqVO,
                                                                @Param("handleSet") List<String> handleSet,@Param("checkSet") List<String> checkSet,
                                                                @Param("distributeSet") List<String> distributeSet,@Param("feedbackSet") List<String> feedbackSet);


    //已办首页
    List<BpmTodoListPageVO> getDoneListPage(@Param("loginUserId") Long loginUserId,@Param("processInstanceIds") List<String> processInstanceIds,@Param("hhandle") Integer hhandle,@Param("tenantId") Long tenantId,@Param("deptId") Long deptId);

    List<BpmTodoListPageVO> getAppDoneListPage(@Param("loginUserId") Long loginUserId,@Param("processInstanceIds") List<String> processInstanceIds);

    //已办首页noids
    List<BpmTodoListPageVO> getDoneListPageNoIds(@Param("loginUserId") Long loginUserId,@Param("hhandle") Integer hhandle,@Param("tenantId") Long tenantId,@Param("deptId") Long deptId);

    List<BpmTodoListPageVO> getAppDoneListPageNoIds(@Param("loginUserId") Long loginUserId);

    //已办全部
    List<BpmDoneListPageAllVO> getDoneListPageAll(@Param("loginUserId") Long loginUserId, @Param("processInstanceIds") List<String> processInstanceIds, @Param("name1") String name1 , @Param("task1") String task1,@Param("hhandle") Integer hhandle,@Param("tenantId") Long tenantId,@Param("deptId") Long deptId,@Param("isApp") Integer isApp);

    //已办全部noids
    List<BpmDoneListPageAllVO> getDoneListPageAllNoIds(@Param("loginUserId") Long loginUserId, @Param("name1") String name1 , @Param("task1") String task1,@Param("hhandle") Integer hhandle,@Param("tenantId") Long tenantId,@Param("deptId") Long deptId);

    List<Long> selectByTenantIdBpm(@Param("tenantId") Long tenantId);

    List<Long> selectReportOfficerByTenantIdBpm(@Param("tenantId") Long tenantId);

    List<Long> selectPrincipalByTenantIdBpm(@Param("tenantId") Long tenantId);
}
