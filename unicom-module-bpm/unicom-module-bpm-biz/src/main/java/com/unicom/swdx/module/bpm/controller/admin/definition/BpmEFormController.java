package com.unicom.swdx.module.bpm.controller.admin.definition;

import com.unicom.swdx.module.bpm.controller.admin.definition.vo.eform.*;
import com.unicom.swdx.module.bpm.convert.definition.BpmEFormConvert;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmEFormDO;
import com.unicom.swdx.module.bpm.service.definition.BpmEFormService;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 动态电子表单")
@RestController
@RequestMapping("/bpm/e-form")
@Validated
public class BpmEFormController {

    @Resource
    private BpmEFormService formService;

    @PostMapping("/create")
    @ApiOperation("创建动态电子表单")
    @PreAuthorize("@ss.hasPermission('bpm:form:create')")
    public CommonResult<Long> createForm(@Valid @RequestBody BpmEFormCreateReqVO createReqVO) {
        return success(formService.createForm(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新动态电子表单")
    @PreAuthorize("@ss.hasPermission('bpm:form:update')")
    public CommonResult<Boolean> updateForm(@Valid @RequestBody BpmEFormUpdateReqVO updateReqVO) {
        formService.updateForm(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除动态电子表单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('bpm:form:delete')")
    public CommonResult<Boolean> deleteForm(@RequestParam("id") Long id) {
        formService.deleteForm(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得动态电子表单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
//    @PreAuthorize("@ss.hasPermission('bpm:form:query')")
    public CommonResult<BpmEFormRespVO> getForm(@RequestParam("id") Long id) {
        BpmEFormDO form = formService.getForm(id);
        return success(BpmEFormConvert.INSTANCE.convert(form));
    }

    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获得电子动态表单的精简列表", notes = "用于表单下拉框")
    public CommonResult<List<BpmEFormSimpleRespVO>> getSimpleForms() {
        List<BpmEFormDO> list = formService.getFormList();
        return success(BpmEFormConvert.INSTANCE.convertList2(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得动态电子表单分页")
    public CommonResult<PageResult<BpmEFormRespVO>> getFormPage(@Valid BpmEFormPageReqVO pageVO) {
        PageResult<BpmEFormDO> pageResult = formService.getFormPage(pageVO);
        return success(BpmEFormConvert.INSTANCE.convertPage(pageResult));
    }

}
