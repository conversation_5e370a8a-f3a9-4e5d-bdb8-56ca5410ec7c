package com.unicom.swdx.module.bpm.dal.mysql.task;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceMyPageReqVO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface BpmProcessInstanceExtMapper extends BaseMapperX<BpmProcessInstanceExtDO> {

    default PageResult<BpmProcessInstanceExtDO> selectPage(Long userId, BpmProcessInstanceMyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
                .eqIfPresent(BpmProcessInstanceExtDO::getStartUserId, userId)
                .eqIfPresent(BpmProcessInstanceExtDO::getSource, reqVO.getSource())
                //.likeIfPresent(BpmProcessInstanceExtDO::getName, reqVO.getName())
                //.eqIfPresent(BpmProcessInstanceExtDO::getProcessDefinitionId, reqVO.getProcessDefinitionId())
                .eqIfPresent(BpmProcessInstanceExtDO::getCategory, reqVO.getCategory())
                .eqIfPresent(BpmProcessInstanceExtDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BpmProcessInstanceExtDO::getResult, reqVO.getResult())
                .and(qw->{
                    qw.ne(BpmProcessInstanceExtDO::getFlowFlag, '1')
                            .or()
                            .isNull(BpmProcessInstanceExtDO::getFlowFlag);
                })
                .betweenIfPresent(BpmProcessInstanceExtDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getName()), qw -> {
                    qw.like(BpmProcessInstanceExtDO::getName,reqVO.getName())
                            .or()
                            .like(BpmProcessInstanceExtDO::getProcessDefinitionId,reqVO.getName());
                })
                .orderByDesc(BpmProcessInstanceExtDO::getId));
    }


    default PageResult<BpmProcessInstanceExtDO> selectXcxPageTimeAsc(Long userId, BpmProcessInstanceMyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
                .eqIfPresent(BpmProcessInstanceExtDO::getStartUserId, userId)
                //.likeIfPresent(BpmProcessInstanceExtDO::getName, reqVO.getName())
                //.eqIfPresent(BpmProcessInstanceExtDO::getProcessDefinitionId, reqVO.getProcessDefinitionId())
                .eqIfPresent(BpmProcessInstanceExtDO::getCategory, reqVO.getCategory())
                .eqIfPresent(BpmProcessInstanceExtDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BpmProcessInstanceExtDO::getResult, reqVO.getResult())
                .betweenIfPresent(BpmProcessInstanceExtDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getName()), qw -> {
                    qw.like(BpmProcessInstanceExtDO::getName,reqVO.getName())
                            .or()
                            .like(BpmProcessInstanceExtDO::getProcessDefinitionId,reqVO.getName());
                })
                .orderByAsc(BpmProcessInstanceExtDO::getCreateTime));
    }

    default PageResult<BpmProcessInstanceExtDO> selectXcxPageTimeDesc(Long userId, BpmProcessInstanceMyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
                .eqIfPresent(BpmProcessInstanceExtDO::getStartUserId, userId)
                //.likeIfPresent(BpmProcessInstanceExtDO::getName, reqVO.getName())
                //.eqIfPresent(BpmProcessInstanceExtDO::getProcessDefinitionId, reqVO.getProcessDefinitionId())
                .eqIfPresent(BpmProcessInstanceExtDO::getCategory, reqVO.getCategory())
                .eqIfPresent(BpmProcessInstanceExtDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BpmProcessInstanceExtDO::getResult, reqVO.getResult())
                .betweenIfPresent(BpmProcessInstanceExtDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getName()), qw -> {
                    qw.like(BpmProcessInstanceExtDO::getName,reqVO.getName())
                            .or()
                            .like(BpmProcessInstanceExtDO::getProcessDefinitionId,reqVO.getName());
                })
                .orderByDesc(BpmProcessInstanceExtDO::getCreateTime));
    }

    default BpmProcessInstanceExtDO selectByProcessInstanceId(String processInstanceId) {
        return selectOne(BpmProcessInstanceExtDO::getProcessInstanceId, processInstanceId);
    }

    default void updateByProcessInstanceId(BpmProcessInstanceExtDO updateObj) {
        update(updateObj, new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
                .eq(BpmProcessInstanceExtDO::getProcessInstanceId, updateObj.getProcessInstanceId()));
    }

    @Update(" update bpm_process_instance_ext SET flow_flag=#{flowFlag},update_time=NOW() where process_instance_id=#{processInstanceId} ")
    void updateProcessInstanceExtFlowFlag(@Param("processInstanceId") String processInstanceId, @Param("flowFlag") String flowFlag);

    @Update(" update bpm_process_instance_ext SET flow_flag=#{flowFlag},update_time=NOW(),sort_time_for_launch=NOW() where process_instance_id=#{processInstanceId} ")
    void updateProcessInstanceExtFlowFlagAndLaunchSortTime(@Param("processInstanceId") String processInstanceId, @Param("flowFlag") String flowFlag);

    @Update(" update bpm_process_instance_ext SET handle_instance_code=#{handleInstanceCode} where process_instance_id=#{processInstanceId} ")
    void updateProcessInstanceExtHandleInstanceCode(@Param("processInstanceId") String processInstanceId, @Param("handleInstanceCode") String handleInstanceCode);

    @Select("select count(1) from oa_central_task ct    left join oa_central_task_extend cte  on ct.id = cte.infor_id where ct.status != 50 and deleted != 1  and (ct.launch_user_id = #{userId} or cte.user_id = #{userId})")
    Long getTaskTotal(@Param("userId") Long userId);
}
