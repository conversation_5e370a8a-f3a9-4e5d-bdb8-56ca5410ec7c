package com.unicom.swdx.module.bpm.job.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("政务子系统 - 审批中心superviseTodoTask")
@Data
public class SuperviseTodoTaskPO {

    @ApiModelProperty(value = "任务编号", required = true, example = "1024")
    private String taskId;

    @ApiModelProperty(value = "任务名字", required = true, example = "sk")
    private String taskName;

    @ApiModelProperty(value = "流程实例编号", required = true, example = "1024")
    private String processInstanceId;

    @ApiModelProperty(value = "流程实例名称", required = true, example = "sk")
    private String processInstanceName;

    @ApiModelProperty(value = "流程实例标识", required = true, example = "sk")
    private String processInstanceKey;

    @ApiModelProperty(value = "任务节点")
    private String taskDefKey;

    @ApiModelProperty(value = "督办状态 1提醒 2督办 3逾期", required = true)
    private String superviseStatus;

    @ApiModelProperty(value = "办理人")
    private Long assigneeUserId;

    @ApiModelProperty(value = "任务结果", required = true)
    private Integer taskResult;

    @ApiModelProperty(value = "流程结果", required = true)
    private Integer processInstanceResult;

    @ApiModelProperty(value = "流程发起时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime processInstanceStartTime;

    @ApiModelProperty(value = "任务创建时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime taskStartTime;
}
