package com.unicom.swdx.module.bpm.controller.admin.task.vo.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("管理后台 - 不通过流程任务的 Request VO")
@Data
public class BpmTaskRejectReqVO {

    @ApiModelProperty(value = "任务编号", required = false, example = "1024")
//    @NotEmpty(message = "任务编号不能为空")
    private String id;

    @ApiModelProperty(value = "审批意见", required = false, example = "不错不错！")
    private String reason;

    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    @ApiModelProperty(value = "图片base64")
    private String imageUrl;

    /**
     * 审批过程中需要保存的日志信息
     */
    @ApiModelProperty(value = "审批过程中需要保存的日志信息-Json形式保存")
    private String logParameters;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID",required = true,example = "1024")
    private String processInstanceId ;

}
