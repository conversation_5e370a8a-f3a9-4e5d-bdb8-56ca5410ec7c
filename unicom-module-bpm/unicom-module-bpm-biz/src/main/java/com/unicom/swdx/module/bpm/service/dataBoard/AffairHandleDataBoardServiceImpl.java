package com.unicom.swdx.module.bpm.service.dataBoard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
//import com.unicom.tyj.module.affair.enums.Consts;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard.*;
import com.unicom.swdx.module.bpm.dal.dataobject.affairProcess.AffairReportHandleCountDO;
import com.unicom.swdx.module.bpm.dal.mysql.affairProcess.AffairHandleDataBoardMapper;
import com.unicom.swdx.module.bpm.dal.mysql.affairProcess.AffairReportHandleCountMapper;
import com.unicom.swdx.module.bpm.enums.definition.AffairHandleProcessNameTypeEnum;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.unicom.swdx.module.system.enums.oauth2.OAuth2ClientConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Service
@Validated
@Slf4j
public class AffairHandleDataBoardServiceImpl implements AffairHandleDataBoardService {

    @Resource
    AffairHandleDataBoardMapper affairHandleDataBoardMapper;

    @Resource
    AffairReportHandleCountMapper affairReportHandleCountMapper;

    @Resource
    private DeptApi deptApi;

    @Resource
    private PermissionApi permissionApi;

    /**
     * 获取政务办理计数
     *
     * @param reqVO
     * @return 统计信息
     */
    @Override
    public AffairHandleFirstLevelReportPageRespVO getHandleCount(AffairHandleAppCountReqVO reqVO) {
        //String [] types = {"permit","confirmation","award","other"};
        //获取数据权限,用于拼装展示的部门
        DeptDataPermissionRespDTO deptDataPermission = permissionApi.getDeptDataPermissionWithClientCode(getLoginUserId(), OAuth2ClientConstants.AFFAIR).getCheckedData();
        Set<Long> permissionDeptIds = getPermissionDeptIdsByList(deptDataPermission, reqVO.getDeptId());
        if (CollectionUtils.isAnyEmpty(permissionDeptIds)) {
            return null;
        }


        AffairHandleFirstLevelReportPageRespVO result = new AffairHandleFirstLevelReportPageRespVO();

        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        List<Long> deptIds = new ArrayList<>();//affairHandleDataBoardMapper.selectHandleDeptList(reqVO, affairHandleTypes);
        deptIds.addAll(permissionDeptIds);
        result.setIsBureauLeaderRole(deptIds.size() > 1);

        AffairHandleCountReqVO reqVO2 = convertAffairHandleCountReqVO(reqVO);


        result.getSummary().setPermit(affairHandleDataBoardMapper.selectHandleCountByType(reqVO2, AffairHandleProcessNameTypeEnum.getTypesBySubject("permit"), deptIds));
        result.getSummary().setConfirmation(affairHandleDataBoardMapper.selectHandleCountByType(reqVO2, AffairHandleProcessNameTypeEnum.getTypesBySubject("confirmation"), deptIds));
        result.getSummary().setAward(affairHandleDataBoardMapper.selectHandleCountByType(reqVO2, AffairHandleProcessNameTypeEnum.getTypesBySubject("award"), deptIds));
        result.getSummary().setOther(affairHandleDataBoardMapper.selectHandleCountByType(reqVO2, AffairHandleProcessNameTypeEnum.getTypesBySubject("other"), deptIds));
        result.getSummary().setTotalHandle(affairHandleDataBoardMapper.selectHandleCountByType(reqVO2, affairHandleTypes, deptIds));
        result.getSummary().setTotalProcessType(result.getSummary().getTotalHandle());


        result.getSummary().setVerySatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO2, 5, deptIds));
        result.getSummary().setSatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO2, 4, deptIds));
        result.getSummary().setBasicallySatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO2, 3, deptIds));
        result.getSummary().setDissatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO2, 2, deptIds));
        //result.getSummary().setVeryDissatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 1, deptIds));
        result.getSummary().setTotalSatisfaction(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO2, null, deptIds));

        result.getSummary().setFavourableComment(result.getSummary().getVerySatisfied() + result.getSummary().getSatisfied() + result.getSummary().getBasicallySatisfied());

        result.getSummary().setSupervision(affairHandleDataBoardMapper.selectHandleCountBySupervision(reqVO2, affairHandleTypes, 2, deptIds));
        result.getSummary().setOverdue(affairHandleDataBoardMapper.selectHandleCountBySupervision(reqVO2, affairHandleTypes, 3, deptIds));


        result.setStatisticalDetails(new ArrayList<>());
        List<AffairHandlePCReportRespVO> temp = affairHandleDataBoardMapper.analyseReport(reqVO2,deptIds);
        List<DeptRespDTO> deptRespDTOS = deptApi.getDepts(deptIds).getData();
        deptIds.forEach(id -> {

            AffairHandleReportRespVO subResult = new AffairHandleReportRespVO();
            List<AffairHandlePCReportRespVO> subTemp = temp.stream().filter(sub -> Objects.equals(sub.getDeptId(), id)).collect(Collectors.toList());
            if(Objects.nonNull(subTemp) && subTemp.size() > 0) {

                subResult.setTotalHandle(subTemp.get(0).getTotalHandle());
                subResult.setOverdue(subTemp.get(0).getOverdue());
                subResult.setSupervision(subTemp.get(0).getSupervision());
                subResult.setDeptName(subTemp.get(0).getDeptName());
                subResult.setDeptId(subTemp.get(0).getDeptId());
                subResult.setFavourableComment(subTemp.get(0).getVerySatisfied()+subTemp.get(0).getSatisfied()+subTemp.get(0).getBasicallySatisfied());
            }else {
                subResult.setTotalHandle(0);
                subResult.setOverdue(0);
                subResult.setSupervision(0);
                subResult.setDeptName(deptRespDTOS.stream().filter(deptRespDTO -> deptRespDTO.getId().equals(id)).collect(Collectors.toList()).get(0).getName());
                subResult.setDeptId(id);
                subResult.setFavourableComment(0);
            }

            result.getStatisticalDetails().add(subResult);


        });


        return result;
    }


    private AffairHandleCountReqVO convertAffairHandleCountReqVO(AffairHandleAppCountReqVO reqVO) {
        AffairHandleCountReqVO reqVO2 = new AffairHandleCountReqVO();
        reqVO2.setStartDate(reqVO.getStartDate());
        reqVO2.setEndDate(reqVO2.getEndDate());
        reqVO2.setOrderType(reqVO2.getOrderType());
        reqVO2.setOrderColumn(reqVO2.getOrderColumn());
        return reqVO2;
    }

    /**
     * 获取二级政务办理计数
     *
     * @param reqVO
     * @return 统计信息
     */
    @Override
    public PageResult<AffairHandleSecondLevelReportBaseVO> getSecondHandleCount(AffairHandleAppCountReqVO reqVO) {
        PageResult<AffairHandleSecondLevelReportBaseVO> result = new PageResult<>();

        DeptDataPermissionRespDTO deptDataPermission = permissionApi.getDeptDataPermissionWithClientCode(getLoginUserId(), OAuth2ClientConstants.AFFAIR).getCheckedData();
        Set<Long> permissionDeptIds = getPermissionDeptIdsByList(deptDataPermission, reqVO.getDeptId());
        if (CollectionUtils.isAnyEmpty(permissionDeptIds)) {
            return null;
        }

        List<Long> deptIds = new ArrayList<>();//affairHandleDataBoardMapper.selectHandleDeptList(reqVO, affairHandleTypes);
        deptIds.addAll(permissionDeptIds);



        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        IPage<AffairHandleSecondLevelReportBaseVO> page = MyBatisUtils.buildPage(reqVO);
        AffairHandleCountReqVO reqVO2 = convertAffairHandleCountReqVO(reqVO);
        result = (new PageResult<>(affairHandleDataBoardMapper.selectReportList(page,reqVO2, affairHandleTypes, deptIds), page.getTotal()));

        return result;
    }


    private Set<Long> getPermissionDeptIds(DeptDataPermissionRespDTO deptDataPermission, Long deptIdConstraint) {
        Set<Long> deptId = new HashSet<>();
        //情况一:查看全部
        if (deptDataPermission.getAll()) {
            List<DeptRespDTO> allDepts = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId()).getCheckedData();
            deptId.addAll(allDepts.stream().map(DeptRespDTO::getId).collect(Collectors.toSet()));
        }
        //情况二，即不能查看部门，又不能查看自己，则说明 100% 无权限
        else if (CollUtil.isEmpty(deptDataPermission.getDeptIds())
                && Boolean.FALSE.equals(deptDataPermission.getSelf())) {
            return Collections.emptySet();
        }
        //返回指定部门id
        else {
            deptId.addAll(deptDataPermission.getDeptIds());
        }
        if (deptIdConstraint != null) {
            if (deptId.contains(deptIdConstraint)) {
                deptId.clear();
                deptId.add(deptIdConstraint);
            } else return Collections.emptySet();
        }
        return deptId;
    }

    private Set<Long> getPermissionDeptIdsByList(DeptDataPermissionRespDTO deptDataPermission, List<Long> deptIdConstraint) {
        Set<Long> deptId = new HashSet<>();
        //情况一:查看全部
        if (deptDataPermission.getAll()) {
            List<DeptRespDTO> allDepts = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId()).getCheckedData();
            deptId.addAll(allDepts.stream().map(DeptRespDTO::getId).collect(Collectors.toSet()));
        }
        //情况二，即不能查看部门，又不能查看自己，则说明 100% 无权限
        else if (CollUtil.isEmpty(deptDataPermission.getDeptIds())
                && Boolean.FALSE.equals(deptDataPermission.getSelf())) {
            return Collections.emptySet();
        }
        //返回指定部门id
        else {
            deptId.addAll(deptDataPermission.getDeptIds());
        }
        if (deptIdConstraint != null) {
            if (deptId.containsAll(deptIdConstraint)) {
                deptId.clear();
                deptId.addAll(deptIdConstraint);
            } else return Collections.emptySet();
        }
        return deptId;
    }

    /**
     * 获取pc端办理计数
     *
     * @param reqVO
     * @return pc统计信息
     */
    @Override
    public AffairPCHandleReportPageRespVO getPCHandleCount(AffairHandleCountReqVO reqVO) {

        AffairPCHandleReportPageRespVO result = new AffairPCHandleReportPageRespVO();
        DeptDataPermissionRespDTO deptDataPermission = permissionApi.getDeptDataPermissionWithClientCode(getLoginUserId(), OAuth2ClientConstants.AFFAIR).getCheckedData();
        Set<Long> permissionDeptIds = getPermissionDeptIds(deptDataPermission, reqVO.getDeptId());
        if (CollectionUtils.isAnyEmpty(permissionDeptIds)) {
            return result;
        }



        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        List<Long> deptIds = new ArrayList<>();//affairHandleDataBoardMapper.selectHandleDeptList(reqVO, affairHandleTypes);
        deptIds.addAll(permissionDeptIds);
        result.setIsBureauLeaderRole(deptIds.size() > 1);

        result.getSummary().setPermit(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("permit"), deptIds));
        result.getSummary().setConfirmation(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("confirmation"), deptIds));
        result.getSummary().setAward(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("award"), deptIds));
        result.getSummary().setOther(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("other"), deptIds));
        result.getSummary().setTotalHandle(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, affairHandleTypes, deptIds));
        result.getSummary().setTotalProcessType(result.getSummary().getTotalHandle());


        result.getSummary().setVerySatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 5, deptIds));
        result.getSummary().setSatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 4, deptIds));
        result.getSummary().setBasicallySatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 3, deptIds));
        result.getSummary().setDissatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 2, deptIds));
        result.getSummary().setVeryDissatisfied(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 1, deptIds));
        result.getSummary().setTotalSatisfaction(affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, null, deptIds));

        result.getSummary().setFavourableComment(result.getSummary().getVerySatisfied() + result.getSummary().getSatisfied() + result.getSummary().getBasicallySatisfied());

        result.getSummary().setSupervision(affairHandleDataBoardMapper.selectHandleCountBySupervision(reqVO, affairHandleTypes, 2, deptIds));
        result.getSummary().setOverdue(affairHandleDataBoardMapper.selectHandleCountBySupervision(reqVO, affairHandleTypes, 3, deptIds));



        result.setStatisticalDetails(new ArrayList<>());

        List<DeptRespDTO> deptRespDTOS = deptApi.getDepts(deptIds).getData();


        result.getStatisticalDetails().addAll(affairHandleDataBoardMapper.analyseReport(reqVO,deptIds));


        deptIds.forEach(id -> {
            if(!result.getStatisticalDetails().stream().map(AffairHandlePCReportRespVO::getDeptId).collect(Collectors.toList()).contains(id))
            {
                AffairHandlePCReportRespVO subResult = new AffairHandlePCReportRespVO();
                subResult.setDeptId(id);
                subResult.setDeptName(deptRespDTOS.stream().filter(deptRespDTO -> deptRespDTO.getId().equals(id)).collect(Collectors.toList()).get(0).getName());
                result.getStatisticalDetails().add(subResult);
            }

        });

        if(Objects.isNull(reqVO.getOrderColumn()) && Objects.isNull(reqVO.getOrderType())){
            reqVO.setOrderColumn("totalHandle");
            reqVO.setOrderType("desc");
        }
        result.setStatisticalDetails(sort(result.getStatisticalDetails(), reqVO.getOrderColumn(), reqVO.getOrderType()));



        return result;
    }

    public List<AffairHandlePCReportRespVO> sort(List<AffairHandlePCReportRespVO> list, String key, String type) {
        List<Comparator<AffairHandlePCReportRespVO>> comparatorList = new ArrayList<>();
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getPermit));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getConfirmation));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getAward));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getOther));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getTotalHandle));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getVerySatisfied));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getSatisfied));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getBasicallySatisfied));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getDissatisfied));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getVeryDissatisfied));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getSupervision));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getOverdue));
        comparatorList.add(Comparator.comparing(AffairHandlePCReportRespVO::getGrade));

        String[] keys = {"permit", "confirmation", "award", "other",
                "totalHandle", "verySatisfied", "satisfied", "basicallySatisfied",
                "dissatisfied", "veryDissatisfied", "supervision", "overdue", "grade"};
        Integer compIndex = Arrays.stream(keys).collect(Collectors.toList()).indexOf(key);

        if (compIndex >= 0) {
            Collections.sort(list, comparatorList.get(compIndex));
            if (type.equals("desc")) {
                Collections.reverse(list);
            }
        }


        return list;
    }


    /**
     * 导出pc端办理计数
     *
     * @param reqVO
     * @return pc统计信息
     */
    @Override
    public List<AffairHandlePCReportExcelVO> exportPCHandleCount(AffairHandleCountReqVO reqVO) {
        DeptDataPermissionRespDTO deptDataPermission = permissionApi.getDeptDataPermissionWithClientCode(getLoginUserId(), OAuth2ClientConstants.AFFAIR).getCheckedData();
        Set<Long> permissionDeptIds = getPermissionDeptIds(deptDataPermission, reqVO.getDeptId());
        if (CollectionUtils.isAnyEmpty(permissionDeptIds)) {
            return null;
        }


        List<AffairHandlePCReportExcelVO> result = new ArrayList<>();

        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        List<Long> deptIds = new ArrayList<>();//affairHandleDataBoardMapper.selectHandleDeptList(reqVO, affairHandleTypes);
        deptIds.addAll(permissionDeptIds);


        List<DeptRespDTO> deptRespDTOS = deptApi.getDepts(deptIds).getData();


        result.addAll(affairHandleDataBoardMapper.analyseReportExport(reqVO,deptIds));


        deptIds.forEach(id -> {
            if(!result.stream().map(AffairHandlePCReportExcelVO::getDeptId).collect(Collectors.toList()).contains(id))
            {
                AffairHandlePCReportExcelVO subResult = new AffairHandlePCReportExcelVO();
                subResult.setDeptId(id);
                subResult.setDeptName(deptRespDTOS.stream().filter(deptRespDTO -> deptRespDTO.getId().equals(id)).collect(Collectors.toList()).get(0).getName());
                result.add(subResult);
            }

        });
        result.sort(Comparator.comparing(AffairHandlePCReportExcelVO::getTotalHandle));
        Collections.reverse(result);
        return result;



    }


    /**
     * 获取政务办理评价
     *
     * @param processInstanceId
     * @return 评价信息
     */
    @Override
    public AffairHandleEvaluationRespVO getEvaluation(String processInstanceId) {
        return affairHandleDataBoardMapper.getEvaluation(processInstanceId);
    }


    /**
     * 获取首页办理计数
     *
     * @param reqVO
     * @return 首页信息
     */
    @Override
    public AffairHandleHomePageRespVO getHomePageCount(AffairHandleCountReqVO reqVO) {
        DeptDataPermissionRespDTO deptDataPermission = permissionApi.getDeptDataPermissionWithClientCode(getLoginUserId(), OAuth2ClientConstants.AFFAIR).getCheckedData();
        Set<Long> permissionDeptIds = getPermissionDeptIds(deptDataPermission, reqVO.getDeptId());
        if (CollectionUtils.isAnyEmpty(permissionDeptIds)) {
            return null;
        }


        AffairHandleHomePageRespVO result = new AffairHandleHomePageRespVO();

        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        List<Long> deptIds = new ArrayList<>();//affairHandleDataBoardMapper.selectHandleDeptList(reqVO, affairHandleTypes);
        deptIds.addAll(permissionDeptIds);


        result.setPermit(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("permit"), deptIds));
        result.setConfirmation(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("confirmation"), deptIds));
        result.setAward(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("award"), deptIds));
        result.setOther(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("other"), deptIds));
        result.setTotalHandle(affairHandleDataBoardMapper.selectHandleCountByType(reqVO, affairHandleTypes, deptIds));


        Integer count5 = (affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 5, deptIds));
        Integer count4 = (affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 4, deptIds));
        Integer count3 = (affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 3, deptIds));
        Integer count2 = (affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 2, deptIds));
        Integer count1 = (affairHandleDataBoardMapper.selectHandleCountBySatisfactionDegree(reqVO, 1, deptIds));

        result.setFavourableComment(count5 + count4 + count3);
        result.setNegativeComment(count2 + count1);

        result.setSupervision(affairHandleDataBoardMapper.selectHandleCountBySupervision(reqVO, affairHandleTypes, 2, deptIds));
        result.setOverdue(affairHandleDataBoardMapper.selectHandleCountBySupervision(reqVO, affairHandleTypes, 3, deptIds));

        if (deptIds.size() == 1) {
            result.setDeptId(deptIds.get(0));
            result.setDeptName(deptApi.getDept(deptIds.get(0)).getData().getName());
        }

        return result;
    }

    @Override
    /**
     * 统计并生成某天PC报表数据
     * @param endDate 日期
     * */
    public void saveCountResultOnTheDay(LocalDate endDate) {


        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());
        List<Long> countDeptIds = affairHandleDataBoardMapper.selectTodayEndDeptIds(endDate,affairHandleTypes);

        AffairHandleCountReqVO reqVO = new AffairHandleCountReqVO();
        reqVO.setStartDate(Date.from(endDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        reqVO.setEndDate(Date.from(endDate.atTime(23,59,59).atZone(ZoneId.systemDefault()).toInstant()));

        List<Map> pList = affairHandleDataBoardMapper.getPCPageCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("permit"), countDeptIds);
        List<Map> cList = affairHandleDataBoardMapper.getPCPageCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("confirmation"), countDeptIds);
        List<Map> aList = affairHandleDataBoardMapper.getPCPageCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("award"), countDeptIds);
        List<Map> oList = affairHandleDataBoardMapper.getPCPageCountByType(reqVO, AffairHandleProcessNameTypeEnum.getTypesBySubject("other"), countDeptIds);
        List<Map> allList = affairHandleDataBoardMapper.getPCPageCountByType(reqVO, affairHandleTypes, countDeptIds);
        List<Map> suList = affairHandleDataBoardMapper.getPCPageCountBySupervision(reqVO, affairHandleTypes, 2, countDeptIds);
        List<Map> ovList = affairHandleDataBoardMapper.getPCPageCountBySupervision(reqVO, affairHandleTypes, 3, countDeptIds);


        List<AffairReportHandleCountDO> affairReportHandleCountDOS = new ArrayList<>();
        countDeptIds.forEach(id -> {
            if(id != null) {
                List<Long> subIds = new ArrayList<>();
                subIds.add(id);
                AffairReportHandleCountDO subResult = new AffairReportHandleCountDO();
                subResult.setDeptId(id);

                log.info("正在统计的部门id为：{}", id);
                if(deptApi.getDept(id).getCheckedData() != null && deptApi.getDept(id).getCheckedData().getName() != null)
                {
                    subResult.setDeptName(deptApi.getDept(id).getCheckedData().getName());
                }
                else
                {
                    log.info("id为：{}的部门，名称设置异常", id);
                }

                subResult.setEndDate(endDate);
                List<Map> subPlist = pList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subPlist.size() > 0) {
                    subResult.setPermitCount(((Long) subPlist.get(0).get("COUNT")));
                } else {
                    subResult.setPermitCount(0l);
                }
                List<Map> subClist = cList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subClist.size() > 0) {
                    subResult.setConfirmationCount(((Long) subClist.get(0).get("COUNT")));
                } else {
                    subResult.setConfirmationCount(0l);
                }
                List<Map> subAlist = aList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subAlist.size() > 0) {
                    subResult.setAwardCount(((Long) subAlist.get(0).get("COUNT")));
                } else {
                    subResult.setAwardCount(0l);
                }
                List<Map> subOlist = oList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subOlist.size() > 0) {
                    subResult.setOtherCount(((Long) subOlist.get(0).get("COUNT")));
                } else {
                    subResult.setOtherCount(0l);
                }
                List<Map> subAlllist = allList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subAlllist.size() > 0) {
                    subResult.setTotalHandleCount(((Long) subAlllist.get(0).get("COUNT")));
                } else {
                    subResult.setTotalHandleCount(0l);
                }
                List<Map> subSulist = suList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subSulist.size() > 0) {
                    subResult.setSuperviseCount(((Long) subSulist.get(0).get("COUNT")));
                } else {
                    subResult.setSuperviseCount(0l);
                }

                List<Map> subOvlist = ovList.stream().filter(map -> {
                    return map.get("deptId").equals(id);
                }).collect(Collectors.toList());
                if (subOvlist.size() > 0) {
                    subResult.setOverdueCount(((Long) subOvlist.get(0).get("COUNT")));
                } else {
                    subResult.setOverdueCount(0l);
                }

                affairReportHandleCountDOS.add(subResult);
            }

        });

        affairReportHandleCountMapper.insertBatch(affairReportHandleCountDOS);

        List<Map<String,Object>> evDatas = affairHandleDataBoardMapper.selectEvaluation(endDate);

        evDatas.forEach(e -> {
            List<Long> subIds = affairHandleDataBoardMapper.selectProcessDeptIds(e.get("processInstanceId").toString());

            subIds.forEach(s ->{
                QueryWrapper<AffairReportHandleCountDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("END_DATE",endDate);
                queryWrapper.eq("DEPT_ID",s);
               List<AffairReportHandleCountDO> target = affairReportHandleCountMapper.selectList(queryWrapper);
               if(target.size() > 0 )
               {
                   if((Integer)(e.get("satisfactionDegree")) == 5)
                   {
                       target.get(0).setVerySatisfiedCount(target.get(0).getVerySatisfiedCount()+1);
                       target.get(0).setTotalEvaluateCount(target.get(0).getTotalEvaluateCount()+1);
                   } else if ((Integer)(e.get("satisfactionDegree")) == 4) {
                       target.get(0).setSatisfiedCount(target.get(0).getSatisfiedCount()+1);
                       target.get(0).setTotalEvaluateCount(target.get(0).getTotalEvaluateCount()+1);
                   }else if ((Integer)(e.get("satisfactionDegree")) == 3) {
                       target.get(0).setBasicallySatisfiedCount(target.get(0).getBasicallySatisfiedCount()+1);
                       target.get(0).setTotalEvaluateCount(target.get(0).getTotalEvaluateCount()+1);
                   }else if ((Integer)(e.get("satisfactionDegree")) == 2) {
                       target.get(0).setDissatisfiedCount(target.get(0).getDissatisfiedCount()+1);
                       target.get(0).setTotalEvaluateCount(target.get(0).getTotalEvaluateCount()+1);
                   } else if ((Integer)(e.get("satisfactionDegree")) == 1) {
                       target.get(0).setVeryDissatisfiedCount(target.get(0).getVeryDissatisfiedCount()+1);
                       target.get(0).setTotalEvaluateCount(target.get(0).getTotalEvaluateCount()+1);
                   }

                   affairReportHandleCountMapper.updateById(target.get(0));
               }

            });

            
        });



        }


    @Override
    /**
     * 获取pc端二级报表分页
     * @param reqVO
     * @return pc端二级报表分页
     * */
    public PageResult<PCAffairHandleSecondLevelReportPageRespVO> getPCSecondHandleCount(PCAffairHandleSecondLevelReportReqVO reqVO) {
        IPage<PCAffairHandleSecondLevelReportPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        if(reqVO.getRouteColumn().equals("permit"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("permit");
        }
        if(reqVO.getRouteColumn().equals("confirmation"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("confirmation");
        }
        if(reqVO.getRouteColumn().equals("award"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("award");
        }
        if(reqVO.getRouteColumn().equals("other"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("other");
        }


        List<PCAffairHandleSecondLevelReportPageRespVO> data = affairHandleDataBoardMapper.selectPCSecondHandleCount(page, reqVO, affairHandleTypes);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        data.forEach(p -> {
            if (p.getFormVariables().get("applyName") != null) {
                p.setApplyEnterpriseName(p.getFormVariables().get("applyName").toString());
            }
            if (p.getFormVariables().get("submitTime") != null) {

                try {
                    p.setApplyTime(LocalDateTimeUtil.of(format.parse(p.getFormVariables().get("submitTime").toString())));
                } catch (ParseException e) {
                    p.setApplyTime(null);
                }

            }
            p.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(p.getProcessKey()).getSubject());
        });


        return new PageResult<>(data, page.getTotal());
    }


    /**
     * 导出pc端二级报表
     * @param reqVO
     * @return pc端二级报表
     * */
    public List<PCAffairHandleSecondLevelReportExcelVO> exportPCSecondHandleCount(PCAffairHandleSecondLevelReportListReqVO reqVO)
    {
        List<String> affairHandleTypes = Arrays.stream(AffairHandleProcessNameTypeEnum.values()).map(AffairHandleProcessNameTypeEnum::getType).collect(Collectors.toList());

        if(reqVO.getRouteColumn().equals("permit"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("permit");
        }
        if(reqVO.getRouteColumn().equals("confirmation"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("confirmation");
        }
        if(reqVO.getRouteColumn().equals("award"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("award");
        }
        if(reqVO.getRouteColumn().equals("other"))
        {
            affairHandleTypes = AffairHandleProcessNameTypeEnum.getTypesBySubject("other");
        }
        List<PCAffairHandleSecondLevelReportPageRespVO> data = affairHandleDataBoardMapper.selectPCSecondHandleCount2(reqVO, affairHandleTypes);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        data.forEach(p -> {
            if (p.getFormVariables().get("applyName") != null) {
                p.setApplyEnterpriseName(p.getFormVariables().get("applyName").toString());
            }
            if (p.getFormVariables().get("submitTime") != null) {

                try {
                    p.setApplyTime(LocalDateTimeUtil.of(format.parse(p.getFormVariables().get("submitTime").toString())));
                } catch (ParseException e) {
                    p.setApplyTime(null);
                }

            }
            p.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(p.getProcessKey()).getSubject());
        });

        List<PCAffairHandleSecondLevelReportExcelVO> result = new ArrayList<>();

        data.forEach(p -> {

            PCAffairHandleSecondLevelReportExcelVO sub = new PCAffairHandleSecondLevelReportExcelVO();
            sub.setHandleTime(LocalDateTimeUtil.format(p.getHandleTime(), FORMAT_YEAR_MONTH_DAY));
            sub.setName(p.getName());
            //sub.setGrade(p.getGrade());
            sub.setStatus(p.getStatus());
            sub.setApplyTime(LocalDateTimeUtil.format(p.getApplyTime(), FORMAT_YEAR_MONTH_DAY));
            sub.setApplyEnterpriseName(p.getApplyEnterpriseName());
            sub.setSatisfactionDegree(p.getSatisfactionDegree());
            sub.setSuperviseCount(p.getSuperviseCount());
            sub.setProcessType(p.getProcessType());

            result.add(sub);

        });
        return result;
    }


    /**
     * 测试用删除所有统计信息
     * */
    @Override
    public String testDeleteAll(){
        affairReportHandleCountMapper.delete(new QueryWrapper<>());
        return "成功";
    }
}
