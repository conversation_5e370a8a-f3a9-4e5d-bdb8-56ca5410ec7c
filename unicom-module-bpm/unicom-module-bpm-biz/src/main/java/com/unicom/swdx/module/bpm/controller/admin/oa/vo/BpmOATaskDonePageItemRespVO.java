package com.unicom.swdx.module.bpm.controller.admin.oa.vo;

import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskTodoPageItemRespVO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@ApiModel("管理后台 - 流程任务的 Done 已完成的分页项 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmOATaskDonePageItemRespVO extends BpmTaskTodoPageItemRespVO {

    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "持续时间", required = true, example = "1000")
    private Long durationInMillis;

    @ApiModelProperty(value = "任务结果", required = true, notes = "参见 bpm_process_instance_status", example = "2")
    private Integer status;

    @ApiModelProperty(value = "任务结果", required = true, notes = "参见 bpm_process_instance_result", example = "2")
    private Integer result;
    @ApiModelProperty(value = "审批建议", required = true, example = "不请假了！")
    private String reason;

    @ApiModelProperty(value = "驳回撤回状态", required = true, example = "1")
    private String revokeStatus;

    private List<Map<String,Object>> info;

}
