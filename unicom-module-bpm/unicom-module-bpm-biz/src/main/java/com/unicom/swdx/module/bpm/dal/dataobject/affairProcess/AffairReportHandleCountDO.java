package com.unicom.swdx.module.bpm.dal.dataobject.affairProcess;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 政务办理pc报表
 *
 * <AUTHOR>
 */


@TableName(value = "affair_report_handle_count", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AffairReportHandleCountDO  extends BaseDO {
    /**
     * 编号
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门id
     */
    private String deptName;

    /**
     * 督办计数
     */
    private Long superviseCount;

    /**
     * 逾期计数
     */
    private Long overdueCount;

    /**
     * 办结时间
     */
    private LocalDate endDate;

    /**
     * 行政确认计数
     */
    private Long confirmationCount;

    /**
     * 行政许可计数
     */
    private Long permitCount;

    /**
     * 行政奖励计数
     */
    private Long awardCount;

    /**
     * 其他行政权力计数
     */
    private Long otherCount;

    /**
     * 总办理件数
     */
    private Long totalHandleCount;

    /**
     * 非常满意计数
     */
    private Long verySatisfiedCount;

    /**
     * 满意计数
     */
    private Long satisfiedCount;

    /**
     * 基本满意计数
     */
    private Long basicallySatisfiedCount;

    /**
     * 不满意计数
     */
    private Long dissatisfiedCount;

    /**
     * 非常不满意计数
     */
    private Long veryDissatisfiedCount;

    /**
     * 分数
     */
    private Float grade;

    /**
     * 总评价计数
     */
    private Long totalEvaluateCount;
}
