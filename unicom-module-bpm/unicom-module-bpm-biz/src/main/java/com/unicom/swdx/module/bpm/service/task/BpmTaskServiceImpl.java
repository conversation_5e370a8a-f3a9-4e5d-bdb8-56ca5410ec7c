package com.unicom.swdx.module.bpm.service.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.common.util.number.NumberUtils;
import com.unicom.swdx.framework.common.util.object.PageUtils;
import com.unicom.swdx.framework.flowable.core.util.FlowableUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskExtDTO;
import com.unicom.swdx.module.bpm.api.task.dto.UserDTO;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess.*;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCreateReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceHandleLogRespVo;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.*;
import com.unicom.swdx.module.bpm.controller.app.dto.querycombined.AppQueryCombinedMap;
import com.unicom.swdx.module.bpm.controller.app.dto.querycombined.CombinedSqlInclusion;
import com.unicom.swdx.module.bpm.convert.task.BpmTaskConvert;
import com.unicom.swdx.module.bpm.dal.dataobject.definition.BpmTaskAssignRuleDO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.unicom.swdx.module.bpm.dal.mysql.affairProcess.AffairProcessMapper;
import com.unicom.swdx.module.bpm.dal.mysql.task.BpmProcessInstanceExtMapper;
import com.unicom.swdx.module.bpm.dal.mysql.task.BpmTaskExtMapper;
import com.unicom.swdx.module.bpm.enums.definition.*;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.ApprovalTypeTagEnum;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.PageNameEnum;
import com.unicom.swdx.module.bpm.enums.task.*;
import com.unicom.swdx.module.bpm.job.po.SuperviseTodoTaskPO;
import com.unicom.swdx.module.bpm.service.definition.BpmProcessDefinitionService;
import com.unicom.swdx.module.bpm.service.definition.BpmTaskAssignRuleService;
import com.unicom.swdx.module.bpm.service.message.BpmMessageService;
import com.unicom.swdx.module.bpm.service.message.dto.BpmMessageSendWhenTaskCreatedReqDTO;
import com.unicom.swdx.module.bpm.service.oa.BpmOALeaveService;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.oa.api.*;
import com.unicom.swdx.module.oa.api.dto.TaskLogDTO;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.enums.OperateEnum;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.todo.TodoItemServiceApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.history.NativeHistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertMap;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertSet;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.oa.enums.Consts.SELECT_APPROVALS;
import static com.unicom.swdx.module.oa.enums.Consts.START_PROCESS_BY_STARTER;
import static com.unicom.swdx.module.oa.enums.PostTypeEnum.SCHOOL_LEADER;
import static com.unicom.swdx.module.oa.enums.PostTypeEnum.VICE_CHANCELLOR;

/**
 * 流程任务实例 Service 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
@Service
public class BpmTaskServiceImpl implements BpmTaskService {

    @Resource
    private TaskService taskService;
    @Resource
    private HistoryService historyService;
    @Resource
    private BpmProcessInstanceExtMapper processInstanceExtMapper;
    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private PostApi postApi;
    @Resource
    private BpmTaskExtMapper taskExtMapper;
    @Resource
    private BpmMessageService messageService;

    @Resource
    protected RuntimeService runtimeService;

    @Resource
    protected RepositoryService repositoryService;
    @Resource
    protected ManagementService managementService;

    @Resource
    private AffairProcessMapper affairProcessMapper;

    @Resource
    private BpmTaskAssignRuleService bpmTaskAssignRuleService;

    @Resource
    private BpmProcessDefinitionService bpmProcessDefinitionService;

    @Resource
    private TodoItemServiceApi todoItemServiceApi;

    @Resource
    private BpmOALeaveService bpmOALeaveService;
//    @Resource
//    private MeetingReserveApi meetingReserveApi;
//
//    @Resource
//    private DocmanApi docmanApi;

    @Resource
    private FileApi fileApi;
//    @Resource
//    private MidOfficeTodoApi midOfficeTodoApi;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private RedisTemplate redisTemplate;


    @Override
    public PageResult<BpmTaskTodoPageItemRespVO> getTodoTaskPage(Long userId, BpmTaskTodoPageReqVO pageVO) {
        // 查询待办任务
        TaskQuery taskQuery = taskService.createTaskQuery().taskAssignee(String.valueOf(userId)) // 分配给自己
                .orderByTaskCreateTime().desc(); // 创建时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            taskQuery.taskCreatedAfter(DateUtils.of(pageVO.getBeginCreateTime()));
        }
        if (pageVO.getEndCreateTime() != null) {
            taskQuery.taskCreatedBefore(DateUtils.of(pageVO.getEndCreateTime()));
        }
        // 执行查询
        List<Task> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            return PageResult.empty(taskQuery.count());
        }

        // 获得 ProcessInstance Map
        Map<String, ProcessInstance> processInstanceMap =
                processInstanceService.getProcessInstanceMap(convertSet(tasks, Task::getProcessInstanceId));    //获得流程实例信息
        //获得 ProcessInstanceExtDO Map
        Set<String> processInstanceIdSet = tasks.stream().map(Task::getProcessInstanceId).collect(Collectors.toSet());
        List<BpmProcessInstanceExtDO> bpmProcessInstanceExtDOList = processInstanceExtMapper.selectList(BpmProcessInstanceExtDO::getProcessInstanceId,processInstanceIdSet);
        Map<String, BpmProcessInstanceExtDO> stringBpmProcessInstanceExtDOMap = convertMap(bpmProcessInstanceExtDOList, BpmProcessInstanceExtDO::getProcessInstanceId);
        // 获得 User Map
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(
                convertSet(processInstanceMap.values(), instance -> Long.valueOf(instance.getStartUserId())));  //获得流程实例发起人
        // 拼接结果
        PageResult<BpmTaskTodoPageItemRespVO> bpmTaskTodoPageItemRespVOPageResult = new PageResult<>(BpmTaskConvert.INSTANCE.convertList1(tasks, processInstanceMap, userMap,stringBpmProcessInstanceExtDOMap),
                taskQuery.count());

        return bpmTaskTodoPageItemRespVOPageResult;
    }

    @Override
    public PageResult<BpmTaskDonePageItemRespVO> getDoneTaskPage(Long userId, BpmTaskDonePageReqVO pageVO) {
        // 查询已办任务
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery().finished() // 已完成
                .taskAssignee(String.valueOf(userId)) // 分配给自己
                .orderByHistoricTaskInstanceEndTime().desc(); // 审批时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            taskQuery.taskCreatedAfter(DateUtils.of(pageVO.getBeginCreateTime()));
        }
        if (pageVO.getEndCreateTime() != null) {
            taskQuery.taskCreatedBefore(DateUtils.of(pageVO.getEndCreateTime()));
        }
        // 执行查询
        List<HistoricTaskInstance> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            return PageResult.empty(taskQuery.count());
        }

        // 获得 TaskExtDO Map
        List<BpmTaskExtDO> bpmTaskExtDOs =
                taskExtMapper.selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        Map<String, BpmTaskExtDO> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BpmTaskExtDO::getTaskId);
        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap =
                processInstanceService.getHistoricProcessInstanceMap(
                        convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));
        // 获得 User Map
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(
                convertSet(historicProcessInstanceMap.values(), instance -> Long.valueOf(instance.getStartUserId())));
        // 拼接结果
        return new PageResult<>(
                BpmTaskConvert.INSTANCE.convertList2(tasks, bpmTaskExtDOMap, historicProcessInstanceMap, userMap,null),
                taskQuery.count());
    }

    @Override
    public PageResult<BpmTaskDonePageItemRespVO> getDoneTaskPageWithFlowFlag(Long userId, BpmTaskDonePageReqVO pageVO) {
        // 查询已办任务:这里使用本地的sql查询HistoricTaskInstance，连接BPM_PROCESS_INSTANCE_EXT表，过滤处于撤回状态的流程
        NativeHistoricTaskInstanceQuery taskQuery = this.nativeHistoricTaskInstanceWithFlowFlag(userId,pageVO);
        // 执行查询
        List<HistoricTaskInstance> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            return PageResult.empty((long) taskQuery.list().size());
        }

        // 获得 TaskExtDO Map
        List<BpmTaskExtDO> bpmTaskExtDOs =
                taskExtMapper.selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        Map<String, BpmTaskExtDO> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BpmTaskExtDO::getTaskId);
        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap =
                processInstanceService.getHistoricProcessInstanceMap(
                        convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));
        //获得 ProcessInstanceExtDO Map
        Set<String> processInstanceIdSet = tasks.stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toSet());
        List<BpmProcessInstanceExtDO> bpmProcessInstanceExtDOList = processInstanceExtMapper.selectList(BpmProcessInstanceExtDO::getProcessInstanceId,processInstanceIdSet);
        Map<String, BpmProcessInstanceExtDO> stringBpmProcessInstanceExtDOMap = convertMap(bpmProcessInstanceExtDOList, BpmProcessInstanceExtDO::getProcessInstanceId);
        // 获得 User Map
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(
                convertSet(historicProcessInstanceMap.values(), instance -> Long.valueOf(instance.getStartUserId())));
        // 拼接结果
        return new PageResult<>(
                BpmTaskConvert.INSTANCE.convertList2(tasks, bpmTaskExtDOMap, historicProcessInstanceMap, userMap,stringBpmProcessInstanceExtDOMap),
                (long) taskQuery.list().size());
    }
    private NativeHistoricTaskInstanceQuery nativeHistoricTaskInstanceWithFlowFlag(Long userId, BpmTaskDonePageReqVO pageVO) {
        StringBuilder sqlBuilder = new StringBuilder( " SELECT RES.* FROM " + managementService.getTableName(HistoricTaskInstanceEntity.class) + " RES \n" +
                " LEFT JOIN BPM_PROCESS_INSTANCE_EXT BPIE ON BPIE.PROCESS_INSTANCE_ID = RES.PROC_INST_ID_ \n" +
                " WHERE RES.ASSIGNEE_ = #{ASSIGNEE_USER_ID} \n" +
                " AND RES.END_TIME_ IS NOT NULL \n" +
                " AND (BPIE.FLOW_FLAG != '1' OR BPIE.FLOW_FLAG IS NULL ) \n" );
        if (StrUtil.isNotBlank(pageVO.getName())) {
            sqlBuilder.append(" AND RES.NAME_ LIKE " + "'%").append(pageVO.getName()).append("%' \n");
        }
        if (pageVO.getBeginCreateTime() != null) {
            sqlBuilder.append(" AND RES.START_TIME_ > #{BeginCreateTime} \n");
        }
        if (pageVO.getEndCreateTime() != null) {
            sqlBuilder.append(" AND RES.START_TIME_ < #{EndCreateTime} \n");
        }
        sqlBuilder.append(" ORDER BY RES.END_TIME_ DESC ");

        NativeHistoricTaskInstanceQuery returnQuery =  historyService.createNativeHistoricTaskInstanceQuery()
                .sql( sqlBuilder.toString() )
                .parameter("ASSIGNEE_USER_ID",String.valueOf(userId));

        if (StrUtil.isNotBlank(pageVO.getName())) {
            returnQuery.parameter("reqName",pageVO.getName());
        }
        if (pageVO.getBeginCreateTime() != null) {
            returnQuery.parameter("BeginCreateTime",DateUtils.of(pageVO.getBeginCreateTime()));
        }
        if (pageVO.getEndCreateTime() != null) {
            returnQuery.parameter("EndCreateTime",DateUtils.of(pageVO.getEndCreateTime()));
        }
        return returnQuery;
    }

    @Override
    public List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds) {
        if (CollUtil.isEmpty(processInstanceIds)) {
            return Collections.emptyList();
        }
        return taskService.createTaskQuery().processInstanceIdIn(processInstanceIds).list();
    }

    @Override
    public List<AdminUserRespDTO> getTasksAssigneeByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        List<BpmTaskExtDO> bpmTaskExtDOS = taskExtMapper.selectList(lambdaQueryWrapperX);
        if (CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
            return Collections.emptyList();
        }
        List<Long> userIds = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toList());
        List<AdminUserRespDTO> adminUserApiUsers = adminUserApi.getUsers(userIds).getCheckedData();
        return adminUserApiUsers;
    }

    /**
     * 获取流程的当前任务
     * @param processInstanceId 流程实例的id
     * @return 流程任务
     */
    @Override
    public BpmTaskExtDO getProcessTasksByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        List<BpmTaskExtDO> bpmTaskExtDOS = taskExtMapper.selectList(lambdaQueryWrapperX);
        if (CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
            return null;
        }
        return bpmTaskExtDOS.get(0);
    }

    @Override
    public LocalDateTime getTime(String processInstanceId) {
        List<BpmTaskRespVO> list = getTaskListByProcessInstanceId(processInstanceId);
        BpmTaskRespVO bpmTaskRespVO = list.stream().sorted(Comparator.comparing(BpmTaskRespVO::getEndTime).reversed())
                .collect(Collectors.toList()).get(0);
        LocalDateTime endTime = bpmTaskRespVO.getEndTime();
        LocalDateTime now = LocalDateTime.now();
        return endTime.isAfter(now) ? endTime : now;
    }

    /**
     * 获得当前正在执行的任务列表
     * @param processInstanceId 流程实例的id
     * @return 流程任务列表
     */
    @Override
    public List<BpmTaskExtDO> getProcessTaskListByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        List<BpmTaskExtDO> bpmTaskExtDOS = taskExtMapper.selectList(lambdaQueryWrapperX);
        return bpmTaskExtDOS;
    }

    @Override
    public List<BpmTaskRespVO> getTaskListByProcessInstanceId(String processInstanceId) {
        // 获得任务列表
        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricTaskInstanceStartTime()
//                .orderByHistoricTaskInstanceEndTime()
                .asc() // 创建时间增序
                .list();
        if (CollUtil.isEmpty(tasks)) {
            return Collections.emptyList();
        }
        // 获得 TaskExtDO Map
        List<BpmTaskExtDO> bpmTaskExtDOs = taskExtMapper.selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        Map<String, BpmTaskExtDO> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BpmTaskExtDO::getTaskId);
        // 获得 ProcessInstance Map
        HistoricProcessInstance processInstance = processInstanceService.getHistoricProcessInstance(processInstanceId);
        // 获得 User Map
        Set<Long> userIds = convertSet(tasks, task -> NumberUtils.parseLong(task.getAssignee()));
        userIds.add(NumberUtils.parseLong(processInstance.getStartUserId()));
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
        // 获得 Dept Map
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(convertSet(userMap.values(), AdminUserRespDTO::getDeptId));

        // 拼接数据
        List<BpmTaskRespVO> bpmTaskRespVOS = BpmTaskConvert.INSTANCE.convertList3(tasks, bpmTaskExtDOMap, processInstance, userMap, deptMap);
        //多部门修改发起人的部门
        for (int i = 0; i < bpmTaskRespVOS.size() - 1; i++) {
            if (Objects.equals(bpmTaskRespVOS.get(i).getName(), "发起审批") &&
                    Objects.equals(bpmTaskRespVOS.get(i + 1).getName(), "部门负责人审批")) {
                Long deptId = bpmTaskRespVOS.get(i + 1).getAssigneeUser().getDeptId();
                String deptName = bpmTaskRespVOS.get(i + 1).getAssigneeUser().getDeptName();
                BpmTaskRespVO bpmTaskRespVO = bpmTaskRespVOS.get(i);
                UserDTO assigneeUser = bpmTaskRespVO.getAssigneeUser();
                assigneeUser.setDeptId(deptId);
                assigneeUser.setDeptName(deptName);
                bpmTaskRespVO.setAssigneeUser(assigneeUser);
                bpmTaskRespVOS.set(i, bpmTaskRespVO);
            }
            if(Objects.equals(bpmTaskRespVOS.get(i).getName(), "部门负责人审批") &&
                    Objects.equals(bpmTaskRespVOS.get(i + 1).getName(), "发起审批") &&
                    (i == bpmTaskRespVOS.size() - 2) ) {
                Long deptId = bpmTaskRespVOS.get(i).getAssigneeUser().getDeptId();
                String deptName = bpmTaskRespVOS.get(i).getAssigneeUser().getDeptName();
                BpmTaskRespVO bpmTaskRespVO = bpmTaskRespVOS.get(i + 1);
                UserDTO assigneeUser = bpmTaskRespVO.getAssigneeUser();
                assigneeUser.setDeptId(deptId);
                assigneeUser.setDeptName(deptName);
                bpmTaskRespVO.setAssigneeUser(assigneeUser);
                bpmTaskRespVOS.set(i + 1, bpmTaskRespVO);
            }
        }
        return bpmTaskRespVOS;
    }

    @Override
    public List<Map<String,Object>> getTaskLogByProcInsId(String processInstanceId) {
        List<Map<String,Object>> res = new ArrayList<>();
        List<TaskLogDTO> logs = new ArrayList<>();
        int index = 0;
        boolean isEnd = false;

        List<BpmTaskRespVO> unfinished = new ArrayList<>();
        List<BpmTaskRespVO> bpmTaskRespVOS = this.getTaskListByProcessInstanceId(processInstanceId);

        for (BpmTaskRespVO taskRespVO : bpmTaskRespVOS) {
//            if(StrUtil.startWith(taskRespVO.getReason(),"主动取消任务，原因：发起人")){
//                continue;
//            }
            if(Objects.equals(taskRespVO.getResult(),4) ){
                if(StrUtil.isNotBlank(taskRespVO.getReason())
                        && (StrUtil.startWith(taskRespVO.getReason(),"主动取消任务，原因：发起人")
                        || StrUtil.startWith(taskRespVO.getReason(),"Change parent activity to")
                        || StrUtil.equals(taskRespVO.getReason(),"系统自动取消，原因：多任务审批已经满足条件，无需审批该任务"))){
                    continue;
                }
            }
            if(Objects.equals(taskRespVO.getResult(),1)){
                //当前正在进行中的任务
                unfinished.add(taskRespVO);
                continue;
            }
            Map<String,Object> map = new HashMap<>();
            map.put("taskType",taskRespVO.getTaskType());
            map.put("taskName",taskRespVO.getName());
            map.put("endTime",taskRespVO.getEndTime());
            TaskLogDTO log = new TaskLogDTO();
            log.setIndex(index++);
            log.setCreateTime(taskRespVO.getCreateTime());
            log.setEndTime(taskRespVO.getEndTime());
            logs.add(log);
            try{
                map.put("nickname",taskRespVO.getAssigneeUser().getNickname());
                map.put("deptName",taskRespVO.getAssigneeUser().getDeptName());
            }catch (Exception e){
                map.put("nickname","");
                map.put("deptName","");
            }

            if( Objects.equals(taskRespVO.getTaskType(),1) ){
                map.putAll(taskRespVO.getParamsMap());
            }else {
                if( Objects.equals(taskRespVO.getTaskType(),2)) {
                    map.put("result", taskRespVO.getResult());
                    map.put("reason", taskRespVO.getReason());
                    map.put("handSignature",taskRespVO.getImageUrl());
                }
            }
            res.add(map);
        }
        List<Map<String,Object>> result = new ArrayList<>();
//        logs = logs.stream().sorted(Comparator.comparing(TaskLogDTO::getCreateTime)
//                        .thenComparing(TaskLogDTO::getEndTime))
//                .collect(Collectors.toList());

        logs = logs.stream().sorted(Comparator.comparing(TaskLogDTO::getEndTime))
                .collect(Collectors.toList());

        for (TaskLogDTO l : logs) {
            result.add(res.get(l.getIndex()));
        }

        BpmProcessInstanceRespVO instanceVO = processInstanceService.getProcessInstanceVO(processInstanceId);
        Integer status = instanceVO.getStatus();
        // 判断流程是否已结束
        if(!Objects.equals(status, BpmProcessInstanceStatusEnum.FINISH.getStatus())
        && !Objects.equals(status, BpmProcessInstanceStatusEnum.CANCEL.getStatus())){

            Map<String,Object> map = new HashMap<>();
            map.put("taskName",unfinished.get(0).getName());
            map.put("taskType",3);
            map.put("endTime",null);

            // 未结束，判断是否为多人会签，存在会签未审批完的
            boolean isMultiParallel = false;
            if(unfinished.size()>1) {
                BpmTaskRespVO task = unfinished.stream().filter(t -> Objects.isNull(t.getEndTime())).findFirst().get();
                Map<String, Object> variables = taskService.getVariables(task.getId());
                if (variables == null) {
                    variables = new HashMap<>();
                }
                if (null != variables.get("chargeLeaderSeq") && Objects.equals(variables.get("chargeLeaderSeq").toString(), "2")) {
                    isMultiParallel = true;
                }
            }
            if(isMultiParallel){
                Map<String, String> userDeptMap = new HashMap<>();
                for (BpmTaskRespVO unapproved : unfinished) {
                    userDeptMap.put(unapproved.getAssigneeUser().getNickname(),unapproved.getAssigneeUser().getDeptName());
                    map.put("unapproved",userDeptMap);
                }
            }else {
                try {
                    map.put("nickname",unfinished.get(0).getAssigneeUser().getNickname());
                    map.put("deptName",unfinished.get(0).getAssigneeUser().getDeptName());
                }catch (Exception e){
                    map.put("nickname","");
                    map.put("deptName","");
                }
            }
            result.add(map);

        }else {
            // 非撤回的流程
            if(!Objects.equals(status, BpmProcessInstanceStatusEnum.CANCEL.getStatus())){
                // 流程已经走完，查询流程以外的办理日志
                if(Objects.equals(instanceVO.getResult(), BpmProcessInstanceResultEnum.FINISH.getResult())){
                    List<BpmTaskExtDO> list = taskExtMapper.selectList(new LambdaQueryWrapperX<BpmTaskExtDO>()
                            .eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId)
                            .eq(BpmTaskExtDO::getTaskType, 6));  //手动结束
                    Map<String, Object> map = new HashMap<>();
                    if(!list.isEmpty()){
                        map.put("taskType", 6);
                        map.put("taskName", "结束");
                        map.put("endTime", list.get(0).getEndTime());
                        map.put("nickname",result.get(0).get("nickname"));
                        map.put("deptName",result.get(0).get("deptName"));
                    }
                    result.add(map);
                }else {
                    String category = processInstanceService.getProcessInstanceVO(processInstanceId).getCategory();
                    if (Objects.equals(category, OACategoryConstants.LEAVE)
                            || Objects.equals(category, OACategoryConstants.OUTREPORT)) {
                        // 流程已经结束，对于请假模块则查询是否有备案和销假的记录，其他模块另行处理
                        List<BpmTaskExtDO> list = taskExtMapper.selectList(new LambdaQueryWrapperX<BpmTaskExtDO>()
                                .eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId)
                                .and(i -> {
                                    i
                                            .eq(BpmTaskExtDO::getTaskType, 4)   //备案
                                            .or()
                                            .eq(BpmTaskExtDO::getTaskType, 5);  //销假
                                })
                                .orderByAsc(BpmTaskExtDO::getCreateTime)
                        );
                        for (BpmTaskExtDO bpmTaskExtDO : list) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("taskType", bpmTaskExtDO.getTaskType());
                            map.put("taskName", bpmTaskExtDO.getName());
                            map.put("endTime", bpmTaskExtDO.getEndTime());
                            if (bpmTaskExtDO.getTaskType() == 5) {
                                map.put("handSignature", bpmTaskExtDO.getImageUrl());
                            }
                            if (Objects.nonNull(bpmTaskExtDO.getParamsMap())) {
                                map.putAll(bpmTaskExtDO.getParamsMap());
                            }
                            result.add(map);
                        }
                        if (list.size() == 2) {
                            isEnd = true;
                        }
                    } else if (Objects.equals(category, OACategoryConstants.LECTURE)) {
                        // 流程已经结束，对于外出讲学模块则查询是否有办公室/教务部备案的记录
                        List<BpmTaskExtDO> list = taskExtMapper.selectList(new LambdaQueryWrapperX<BpmTaskExtDO>()
                                .eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId)
                                .eq(BpmTaskExtDO::getTaskType, 4));
                        if (!list.isEmpty()) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("taskType", 4);
                            map.put("taskName", list.get(0).getName());
                            map.put("endTime", list.get(0).getEndTime());
                            if (Objects.nonNull(list.get(0).getParamsMap())) {
                                map.putAll(list.get(0).getParamsMap());
                            }
                            result.add(map);
                            isEnd = true;
                        }
                    } else if (Objects.equals(category, OACategoryConstants.SCHEDULE)) {
                        // 流程已经结束
                        isEnd = true;
                    } else if (Objects.equals(category, OACategoryConstants.SUMMARY)) {
                        isEnd = true;
                    } else if (Objects.equals(category, OACategoryConstants.DUTY)) {
                        isEnd = true;
                    }
                }
            }else {
                // 撤回的流程
                Map<String, Object> map = new HashMap<>();
                map.put("taskName", "发起人撤回");
                map.put("endTime",instanceVO.getEndTime());
                try {
                    map.put("nickname",instanceVO.getStartUser().getNickname());
                    map.put("deptName",instanceVO.getStartUser().getDeptName());
                }catch (Exception e){
                    map.put("nickname","");
                    map.put("deptName","");
                }

                result.add(map);
                isEnd = true;
            }
        }
        result.get(result.size()-1).put("isEnd",isEnd);
        return result;
    }

    @Override
    public Map<String,Object> isSequenceLast(String processInstanceId) {
        Map<String, Object> map = new HashMap<>();

        List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime()
                .desc()
                .list();

        HistoricTaskInstance task = list.stream().filter(t -> Objects.isNull(t.getEndTime())).findFirst().get();


        Map<String, Object> processVariables = taskService.getVariables(task.getId());
        List<Integer> approvals = (List<Integer>) processVariables.get("approvals");

        if(processVariables.get("chargeLeaderSeq") == null) {
            map.put("isSequence", true);
            map.put("isLast", true);
            return map;
        }

        if(Objects.equals(processVariables.get("chargeLeaderSeq"), "1")) {
            map.put("isSequence", true);
            Integer nrOfInstances = (Integer) processVariables.get("nrOfInstances"); //总实例数量
            Integer nrOfCompletedInstances = (Integer) processVariables.get("nrOfCompletedInstances"); //已经完成的实例数
            if (nrOfInstances != null && nrOfCompletedInstances != null) {
                if(nrOfInstances - nrOfCompletedInstances <= 1) {
                    map.put("isLast", true);
                    return map;
                } else {
                    map.put("isLast", false);
                    map.put("nextApproval", approvals.get(nrOfCompletedInstances + 1));
                    map.put("nextApprovalName",adminUserApi.getUser(Long.valueOf(approvals.get(nrOfCompletedInstances + 1))).getCheckedData().getNickname());
                    return map;
                }
            } else {
                map.put("isLast", true);
                return map;
            }
        }

        if(Objects.equals(processVariables.get("chargeLeaderSeq"), "2") && processVariables.get("nrOfInstances") == null) {
            map.put("isSequence", true);
            map.put("isLast", true);
            return map;
        }
        map.put("isSequence", false);
        return map;
    }


    @Override
    public Map<String, String> getNeededTaskInfo(String category, Long loginUserId, String processInstanceId, Boolean isReceived, Boolean isDealt) {
        Map<String, String> map = new HashMap<>();

        BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(processInstanceId);
        map.put("status", processInstanceVO.getStatus().toString());

        map.put("result", processInstanceVO.getResult().toString());

        // 当前为我收到的页面的get请求
        if(null!=isReceived && isReceived){
            Long tenantId = adminUserApi.getUser(loginUserId).getCheckedData().getTenantId();
            // 判断是否为请假记录
            if(Objects.equals(OACategoryConstants.LEAVE,category)){
                if(postApi.getUserByPost("oa-hr-leave-deal",tenantId).contains(loginUserId)) {
                    if (null == isDealt || !isDealt) {
                        map.put("operateType", OperateEnum.UPDATE_LEAVE_END_TIME.getOperateType().toString());
                    } else {
                        map.put("operateType", OperateEnum.NO_OPERATE.getOperateType().toString());
                    }
                    return map;
                }
            }else if(Objects.equals(OACategoryConstants.OUTREPORT,category)){
                //只有人事部外出报告备案负责人或办公室外出报告备案负责人能销单
                if(postApi.getUserByPost("oa-hr-charge-out",tenantId).contains(loginUserId) ||
                        postApi.getUserByPost("oa-office-charge-out",tenantId).contains(loginUserId) ) {
                    if (null == isDealt || !isDealt) {
                        map.put("operateType", OperateEnum.UPDATE_LEAVE_END_TIME.getOperateType().toString());
                        return map;
                    }
                }
            }
        }

        // 当前为我发起，待审批，已审批等页面的get请求
        map.put("operateType", OperateEnum.NO_OPERATE.getOperateType().toString());
        //请假流程还在进行中，则需判断操作为审批还是选择审批人还是仅查看不能操作
        if(!Objects.equals(BpmProcessInstanceStatusEnum.FINISH.getStatus(), processInstanceVO.getStatus())
        && !Objects.equals(BpmProcessInstanceStatusEnum.CANCEL.getStatus(), processInstanceVO.getStatus())){

            List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .orderByTaskCreateTime()
                    .desc()
                    .list();

            HistoricTaskInstance task = list.stream().filter(t -> Objects.isNull(t.getEndTime())).findFirst().get();

            String taskName = task.getName();
            map.put("taskName", taskName);

            Map<String, Object> processVariables = taskService.getVariables(task.getId());
//        Map<String, Object> processVariables = processInstance.getProcessVariables();

            List<String> unApproved = new ArrayList<>();
            if (CollUtil.isNotEmpty(processVariables)) {
                if(null!=processVariables.get("taskType") && Objects.equals(processVariables.get("taskType").toString(),"多人会签")){
                    if(Objects.nonNull(processVariables.get("unapproved"))){
                        unApproved = (List<String>)processVariables.get("unapproved");
                    }
                }
            }
            if(CollUtil.isNotEmpty(unApproved)){
                if(!Objects.equals(BpmProcessInstanceStatusEnum.REJECT.getStatus(), processInstanceVO.getStatus())
                        && unApproved.contains(loginUserId.toString())){
                    map.put("operateType", OperateEnum.APPROVE.getOperateType().toString());
                }
            }else {
                if(Objects.equals(taskName, SELECT_APPROVALS)){
                    if(Objects.equals(loginUserId.toString(), processInstanceVO.getStartUser().getId().toString())){
                        map.put("operateType",OperateEnum.SELECT_APPROVALS.getOperateType().toString());
                    }
                }else {
                    if(Objects.equals(task.getAssignee(), loginUserId.toString())){
                        map.put("operateType", OperateEnum.APPROVE.getOperateType().toString());
                    }
                }
            }
        }
        return map;
    }

    @Override
    public List<BpmTaskRespVO> getTaskListByProcessInstanceIdWithFlowStatus(String processInstanceId) {
        List<BpmTaskRespVO> bpmTaskRespList = getTaskListByProcessInstanceId(processInstanceId);
        if (bpmTaskRespList.size() <= 1){
            return bpmTaskRespList;
        }
        String launchTaskKey = bpmTaskRespList.get(bpmTaskRespList.size() - 1).getDefinitionKey();
        int lastLaunchTaskIndex = CollUtil.indexOf(bpmTaskRespList,x -> launchTaskKey.equals(x.getDefinitionKey()));

        //只返回最近一次的发起节点及其后续节点
        return bpmTaskRespList.stream().limit(lastLaunchTaskIndex+1).collect(Collectors.toList());
    }

    @Override
    public String getTaskKeyByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        List<BpmTaskExtDO> bpmTaskExtDOS = taskExtMapper.selectList(lambdaQueryWrapperX);
        if (CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
            return "";
        }
        return bpmTaskExtDOS.get(0).getTaskDefKey();
    }

    @Override
    public List<Long> superviseTaskByProcInsId(Long loginUserId, String processInstanceId) {
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(processInstanceId);
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }
        // 只能催办自己的任务
        if (!Objects.equals(instance.getStartUserId(),loginUserId.toString())){
            throw exception(SUPERVISE_FAIL_NOT_SELF);
        }

        List<Long> assignee = new ArrayList<>();

        List<BpmTaskExtDO> taskList = taskExtMapper.selectList(new LambdaQueryWrapperX<BpmTaskExtDO>()
                .eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId)
                .eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult()));

        taskList.forEach(task->{
            updateTaskSuperviseStatus(task.getTaskId(),"1");
            assignee.add(task.getAssigneeUserId());
        });

        return assignee;
    }

    @Override
    public boolean isLast(String taskId, Map<String, Object> variables) {

        if (variables == null) {
            return false;
        }
        Integer nrOfInstances = (Integer) variables.get("nrOfInstances"); //总实例数量
        Integer nrOfCompletedInstances = (Integer) variables.get("nrOfCompletedInstances"); //已经完成的实例数
        if (nrOfInstances != null && nrOfCompletedInstances != null) {
            if(nrOfInstances - nrOfCompletedInstances <= 1) {
                return true;
            }else {
                return false;
            }
        }

        //还没开始多实例任务
        return false;

//        if(Objects.equals("1",variables.get("chargeLeaderSeq"))){
//            //顺序
//            ArrayList<Integer> approvals = (ArrayList<Integer>) variables.get("approvals");
//            if(CollUtil.isEmpty(approvals) || approvals.size()==1){
//                return false;
//            }
//            Integer completed = (Integer) variables.get("completed");
//            if(completed != null){
//                if(approvals.size() - completed <= 1) {
//                    return true;
//                }else {
//                    return false;
//                }
//            }else {
//                return true;
//            }
//        }else if(Objects.equals("2",variables.get("chargeLeaderSeq"))){
//            //会签
//            ArrayList<Integer> approvals = (ArrayList<Integer>) variables.get("approvals");
//            if(approvals.size()==1){
//                return false;
//            }else {
//                if(Objects.nonNull(variables.get("unapproved"))){
//                    ArrayList<String> unApproval = (ArrayList<String>) variables.get("unapproved");
//                    if(unApproval.size()==1){
//                        return true;
//                    }else {
//                        return false;
//                    }
//                }else {
//                    return true;
//                }
//            }
//        }else{
//            return true;
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approveTask(Long userId, @Valid BpmTaskApproveReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }
        Map<String, Object> variables = taskService.getVariables(task.getId());
        if (variables == null)  {
            variables = new HashMap<>();
        }
        if (reqVO.getVariables() != null) {
            variables.putAll(reqVO.getVariables());
        }
        if(isLast(task.getId(), variables)){
            variables.put("flag", null);
        }
        //处理多人会签，包括已存在有审批人拒绝的情况
        if(null!=variables.get("taskType") && Objects.equals(variables.get("taskType").toString(),"多人会签")){
            if(null!=variables.get("unapproved")) {
                List<String> unApproved = (List<String>) variables.get("unapproved");
                if (CollUtil.isNotEmpty(unApproved)) {
                    unApproved.remove(userId.toString());
                    if(unApproved.isEmpty()){
                        // 多人会签任务完成
                        variables.put("unapproved",null);
                        variables.put("taskType",null);
                        // 会签最后一个人
                        // 判断是否已经被前面的人驳回，是则驳回到发起人
                        if(Objects.nonNull(variables.get("waitToRejectToStarter"))
                                && Objects.equals(variables.get("waitToRejectToStarter").toString(),"1")){
                            // 驳回到发起人
                            returnToStarter(task.getProcessInstanceId(),task.getId());
                            // 并更新流程实例状态为驳回
                            BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO()
                                    .setProcessInstanceId(task.getProcessInstanceId())
                                    .setStatus(BpmProcessInstanceStatusEnum.REJECT.getStatus())
                                    .setResult(BpmProcessInstanceResultEnum.REJECT.getResult());
                            processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

                            // 更新任务拓展表不通过的理由
                            taskExtMapper.updateByTaskId(
                                    new BpmTaskExtDO().setTaskId(task.getId())
                                            .setEndTime(LocalDateTime.now())
                                            .setResult(BpmProcessInstanceResultEnum.APPROVE.getResult())
                                            .setReason(reqVO.getReason())
                                            .setImageUrl(reqVO.getImageUrl())
                                            .setTaskType(2)
                                            .setLogParameters(reqVO.getLogParameters())
                                            .setParamsMap(reqVO.getParamsMap()));
                            return true;
                        }
                    }else {
                        variables.put("unapproved",unApproved);
                    }
                }
            }
        }
        if(Objects.nonNull(variables.get("completed"))){
            Integer completed = (Integer)variables.get("completed");
            completed++;
            variables.put("completed",completed);
        }
        // 完成任务，审批通过
        taskService.complete(task.getId(),variables);

        // 重新发起时把流程状态修改为进行中
        if(reqVO.getTaskType()!=null){
            int taskType = reqVO.getTaskType();
            if(Objects.equals(taskType,0)){
                BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO()
                        .setProcessInstanceId(task.getProcessInstanceId())
                        .setStatus(BpmProcessInstanceStatusEnum.RUNNING.getStatus())
                        .setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
                processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);
            }
        }

        LocalDateTime time = Objects.nonNull(reqVO.getEndTime())? reqVO.getEndTime() : LocalDateTime.now();

        // 更新任务拓展表为通过
        taskExtMapper.updateByTaskId(
                new BpmTaskExtDO().setTaskId(task.getId())
                        .setEndTime(time)
                        .setResult(BpmProcessInstanceResultEnum.APPROVE.getResult())
                        .setReason(reqVO.getReason())
                        .setImageUrl(reqVO.getImageUrl())
                        .setTaskType(reqVO.getTaskType())
                        .setLogParameters(reqVO.getLogParameters())
                        .setParamsMap(reqVO.getParamsMap()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectTask(Long userId, @Valid BpmTaskRejectReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }

        // 判断是否为多人会签
        boolean isMultiParallel = false;
        boolean isLastParallel = false;
        Map<String, Object> variables = taskService.getVariables(task.getId());
        if (Objects.isNull(variables))  {
            variables = new HashMap<>();
        }
        if(CollUtil.isNotEmpty(variables)){
            if(null!=variables.get("taskType") && Objects.equals(variables.get("taskType").toString(),"多人会签")){
                isMultiParallel = true;
                if(null!=variables.get("unapproved")){
                    List<String> unApproved = (List<String>)variables.get("unapproved");
                    if(CollUtil.isNotEmpty(unApproved)){
                        unApproved.remove(userId.toString());
                        if(unApproved.isEmpty()){
                            // 多人会签任务完成
                            variables.put("unapproved",null);
                            variables.put("taskType",null);
                            variables.put("waitToRejectToStarter","0");
                            // 会签的最后一个人才更新流程实例状态为驳回并回到发起人
                            isLastParallel = true;

                        }else {
                            variables.put("unapproved",unApproved);
                            variables.put("waitToRejectToStarter","1");
                        }
                    }
                }
            }
        }
        if(!isMultiParallel || isLastParallel){
            // 不是多人会签或者已经是多人会签的最后一个人，直接驳回到发起人
            returnToStarter(task.getProcessInstanceId(), task.getId());
            // 更新流程实例状态为驳回
            BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO().setProcessInstanceId(task.getProcessInstanceId())
                    .setStatus(BpmProcessInstanceStatusEnum.REJECT.getStatus())
                    .setResult(BpmProcessInstanceResultEnum.REJECT.getResult());
            processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);
        }else {
            variables.put("flag", null);
            taskService.complete(task.getId(),variables);
        }
        taskExtMapper.updateByTaskId(
                new BpmTaskExtDO().setTaskId(task.getId())
                        .setResult(BpmProcessInstanceResultEnum.REJECT.getResult())
                        .setEndTime(LocalDateTime.now())
                        .setReason(reqVO.getReason())
                        .setImageUrl(reqVO.getImageUrl())
                        .setLogParameters(reqVO.getLogParameters())
                        .setTaskType(2));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnToStarter(String processInstanceId, String taskId) {

        List<HistoricTaskInstance> htiList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime()
                .asc()
                .list();

        String startTaskId = null;
        String startUserId = processInstanceService.getProcessInstanceVO(processInstanceId).getStartUser().getId().toString();

        HistoricTaskInstance startTask = null;
        for (HistoricTaskInstance hti : htiList) {
            if (startUserId.equals(hti.getAssignee()) && Objects.equals(hti.getName(),"发起审批")) {
                startTaskId = hti.getId();
                startTask = hti;
                break;
            }
        }
        if (null == startTaskId) {
            throw exception(TASK_ASSIGN_NO_START);
        }

        String processDefinitionId = startTask.getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

        //变量
//      Map<String, VariableInstance> variables = runtimeService.getVariableInstances(currentTask.getExecutionId());
        String startActivityId = null;
        List<HistoricActivityInstance> haiList = historyService.createHistoricActivityInstanceQuery()
                .executionId(startTask.getExecutionId()).finished().list();
        for (HistoricActivityInstance hai : haiList) {
            if (startTaskId.equals(hai.getTaskId())) {
                startActivityId = hai.getActivityId();
                break;
            }
        }
        if (Objects.isNull(startActivityId)) {
            throw exception(TASK_ASSIGN_NO_START);
        }
        FlowNode myFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(startActivityId);
        //删除运行和历史的节点信息
        this.deleteActivity(startActivityId, processInstanceId);
        //执行跳转
        List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
        List<String> executionIds = new ArrayList<>();
        executions.forEach(execution -> executionIds.add(execution.getId()));
        this.moveExecutionsToSingleActivityId(executionIds, startActivityId);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectTaskToEnd(Long userId, @Valid BpmTaskRejectReqVO reqVO) {
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }

        // 更新流程实例为不通过
        processInstanceService.updateProcessInstanceExtReject(instance.getProcessInstanceId(), reqVO.getReason());

        // 更新任务拓展表为不通过
        taskExtMapper.updateByTaskId(
                new BpmTaskExtDO().setTaskId(task.getId()).setResult(BpmProcessInstanceResultEnum.REJECT.getResult())
                        .setEndTime(LocalDateTime.now()).setReason(reqVO.getReason()).setLogParameters(reqVO.getLogParameters()));
    }

    @Override
    public void revokeProcess(BpmTaskVO bpmTaskVO) {
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(bpmTaskVO.getInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        // 只能取消自己的
        if (!Objects.equals(instance.getStartUserId(), String.valueOf(bpmTaskVO.getUserId()))) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
        }

        Task task = taskService.createTaskQuery().processInstanceId(bpmTaskVO.getInstanceId()).list().get(0);
        if (task == null) {
            throw exception(TASK_ASSIGN_NOT_RUN);
        }
        List<HistoricTaskInstance> htiList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .orderByTaskCreateTime()
                .asc()
                .list();
        String myTaskId = null;
        HistoricTaskInstance myTask = null;
        for (HistoricTaskInstance hti : htiList) {
            if (bpmTaskVO.getUserId().equals(hti.getAssignee())) {
                myTaskId = hti.getId();
                myTask = hti;
                break;
            }
        }
        if (null == myTaskId) {
            throw exception(TASK_ASSIGN_NOT_CURRENTUSER);
        }

        String processDefinitionId = myTask.getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

        //变量
//      Map<String, VariableInstance> variables = runtimeService.getVariableInstances(currentTask.getExecutionId());
        String myActivityId = null;
        List<HistoricActivityInstance> haiList = historyService.createHistoricActivityInstanceQuery()
                .executionId(myTask.getExecutionId()).finished().list();
        for (HistoricActivityInstance hai : haiList) {
            if (myTaskId.equals(hai.getTaskId())) {
                myActivityId = hai.getActivityId();
                break;
            }
        }
        if (Objects.isNull(myActivityId)) {
            throw exception(TASK_ASSIGN_INITIAL_REVOKE);
        }
        FlowNode myFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(myActivityId);
        //4.删除运行和历史的节点信息
        this.deleteActivity(myActivityId, bpmTaskVO.getInstanceId());
        //5.执行跳转
        List<Execution> executions = runtimeService.createExecutionQuery().parentId(bpmTaskVO.getInstanceId()).list();
        List<String> executionIds = new ArrayList<>();
        executions.forEach(execution -> executionIds.add(execution.getId()));
        this.moveExecutionsToSingleActivityId(executionIds, myActivityId);

        BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO().setProcessInstanceId(task.getProcessInstanceId())
                .setStatus(BpmProcessInstanceStatusEnum.CANCEL.getStatus())
                .setResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

//
//        Execution execution = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
//        String activityId = execution.getActivityId();
//        FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId);
//
//        //记录原活动方向
//        List<SequenceFlow> oriSequenceFlows = new ArrayList<>(flowNode.getOutgoingFlows());

    }

    @Override
    public void updateTaskAssignee(Long userId, BpmTaskUpdateAssigneeReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 更新负责人
        updateTaskAssignee(task.getId(), reqVO.getAssigneeUserId());
    }

    @Override
    public void changeTaskAssignee(BpmTaskUpdateAssigneeReqVO reqVO) {
        // 校验任务存在
        Task task = getTask(reqVO.getId());
        if (task == null) {
            throw exception(TASK_COMPLETE_FAIL_NOT_EXISTS);
        }
        // 更新负责人
        updateTaskAssignee(task.getId(), reqVO.getAssigneeUserId());
    }

    @Override
    public void updateTaskAssignee(String id, Long userId) {
        taskService.setAssignee(id, String.valueOf(userId));
    }

    /**
     * 校验任务是否存在， 并且是否是分配给自己的任务
     *
     * @param userId 用户 id
     * @param taskId task id
     */
    private Task checkTask(Long userId, String taskId) {
        Task task = getTask(taskId);
        if (task == null) {
             throw exception(TASK_COMPLETE_FAIL_NOT_EXISTS);
        }
        if (!Objects.equals(userId, NumberUtils.parseLong(task.getAssignee()))) {
            throw exception(TASK_COMPLETE_FAIL_ASSIGN_NOT_SELF);
        }
        return task;
    }

    @Override
    public void createTaskExt(Task task) {
        BpmTaskExtDO taskExtDO =
                BpmTaskConvert.INSTANCE.convert2TaskExt(task).setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
        taskExtMapper.insert(taskExtDO);
    }

    @Override
    public void createCustomTaskExt(BpmTaskExtDTO taskExtDTO) {
        BpmTaskExtDO taskExtDO = BpmTaskConvert.INSTANCE.convertToTaskExtDO(taskExtDTO);
        taskExtMapper.insert(taskExtDO);
    }

    @Override
    public void updateTaskExtComplete(Task task) {
        BpmTaskExtDO taskExtDO = BpmTaskConvert.INSTANCE.convert2TaskExt(task)
                .setResult(BpmProcessInstanceResultEnum.APPROVE.getResult()) // 不设置也问题不大，因为 Complete 一般是审核通过，已经设置
                .setEndTime(LocalDateTime.now());
        taskExtMapper.updateByTaskId(taskExtDO);
    }

    @Override
    public void updateTaskExtCancel(String taskId) {
        // 需要在事务提交后，才进行查询。不然查询不到历史的原因
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                // 可能只是活动，不是任务，所以查询不到
                HistoricTaskInstance task = getHistoricTask(taskId);
                if (task == null) {
                    return;
                }

                // 如果任务拓展表已经是完成的状态，则跳过
                BpmTaskExtDO taskExt = taskExtMapper.selectByTaskId(taskId);
                if (taskExt == null) {
                    log.error("[updateTaskExtCancel][taskId({}) 查找不到对应的记录，可能存在问题]", taskId);
                    return;
                }
                // 如果已经是最终的结果，则跳过
                if (BpmProcessInstanceResultEnum.isEndResult(taskExt.getResult())) {
                    log.error("[updateTaskExtCancel][taskId({}) 处于结果({})，无需进行更新]", taskId, taskExt.getResult());
                    return;
                }

                // 更新任务
                taskExtMapper.updateById(new BpmTaskExtDO().setId(taskExt.getId()).setResult(BpmProcessInstanceResultEnum.CANCEL.getResult())
                        .setEndTime(LocalDateTime.now()).setReason(BpmProcessInstanceDeleteReasonEnum.translateReason(task.getDeleteReason())));
            }

        });
    }

    @Override
    public void updateTaskExtAssign(Task task) {
        BpmTaskExtDO taskExtDO =
                new BpmTaskExtDO().setAssigneeUserId(NumberUtils.parseLong(task.getAssignee())).setTaskId(task.getId());
        taskExtMapper.updateByTaskId(taskExtDO);
        // 发送通知。在事务提交时，批量执行操作，所以直接查询会无法查询到 ProcessInstance，所以这里是通过监听事务的提交来实现。
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                ProcessInstance processInstance =
                        processInstanceService.getProcessInstance(task.getProcessInstanceId());
                AdminUserRespDTO startUser = adminUserApi.getUser(Long.valueOf(processInstance.getStartUserId())).getCheckedData();
                BpmMessageSendWhenTaskCreatedReqDTO reqDTO = BpmTaskConvert.INSTANCE.convert(processInstance, startUser, task);
                if (StringUtils.isBlank(reqDTO.getProcessInstanceName())) {
                    BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstance.getProcessInstanceId());
                    reqDTO.setProcessInstanceName(bpmProcessInstanceExtDO.getName());
                }
                messageService.sendMessageWhenTaskAssigned(reqDTO);
            }
        });
    }

    @Override
    public void taskReject(BpmTaskVO bpmTaskVO) {
        if (taskService.createTaskQuery().taskId(bpmTaskVO.getTaskId()).singleResult().isSuspended()) {
            throw exception(TASK_ASSIGN_HANNGUP);
        }
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(bpmTaskVO.getTaskId()).singleResult();
        // 获取流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        // 获取所有节点信息
        Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
        // 获取全部节点列表，包含子节点
        Collection<FlowElement> allElements = FlowableUtils.getAllElements(process.getFlowElements(), null);
        // 获取当前任务节点元素
        FlowElement source = null;
        if (allElements != null) {
            for (FlowElement flowElement : allElements) {
                // 类型为用户节点
                if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                    // 获取节点信息
                    source = flowElement;
                }
            }
        }

        // 目的获取所有跳转到的节点 targetIds
        // 获取当前节点的所有父级用户任务节点
        // 深度优先算法思想：延边迭代深入
        List<UserTask> parentUserTaskList = FlowableUtils.iteratorFindParentUserTasks(source, null, null);
        if (parentUserTaskList == null || parentUserTaskList.size() == 0) {
            throw exception(TASK_ASSIGN_INITIAL);
        }
        // 获取活动 ID 即节点 Key
        List<String> parentUserTaskKeyList = new ArrayList<>();
        parentUserTaskList.forEach(item -> parentUserTaskKeyList.add(item.getId()));
        // 获取全部历史节点活动实例，即已经走过的节点历史，数据采用开始时间升序
        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(task.getProcessInstanceId()).orderByHistoricTaskInstanceStartTime().asc().list();
        // 数据清洗，将回滚导致的脏数据清洗掉
        List<String> lastHistoricTaskInstanceList = FlowableUtils.historicTaskInstanceClean(allElements, historicTaskInstanceList);
        // 此时历史任务实例为倒序，获取最后走的节点
        List<String> targetIds = new ArrayList<>();
        // 循环结束标识，遇到当前目标节点的次数
        int number = 0;
        StringBuilder parentHistoricTaskKey = new StringBuilder();
        for (String historicTaskInstanceKey : lastHistoricTaskInstanceList) {
            // 当会签时候会出现特殊的，连续都是同一个节点历史数据的情况，这种时候跳过
            if (parentHistoricTaskKey.toString().equals(historicTaskInstanceKey)) {
                continue;
            }
            parentHistoricTaskKey = new StringBuilder(historicTaskInstanceKey);
            if (historicTaskInstanceKey.equals(task.getTaskDefinitionKey())) {
                number++;
            }
            // 在数据清洗后，历史节点就是唯一一条从起始到当前节点的历史记录，理论上每个点只会出现一次
            // 在流程中如果出现循环，那么每次循环中间的点也只会出现一次，再出现就是下次循环
            // number == 1，第一次遇到当前节点
            // number == 2，第二次遇到，代表最后一次的循环范围
            if (number == 2) {
                break;
            }
            // 如果当前历史节点，属于父级的节点，说明最后一次经过了这个点，需要退回这个点
            if (parentUserTaskKeyList.contains(historicTaskInstanceKey)) {
                targetIds.add(historicTaskInstanceKey);
            }
        }


        // 目的获取所有需要被跳转的节点 currentIds
        // 取其中一个父级任务，因为后续要么存在公共网关，要么就是串行公共线路
        UserTask oneUserTask = parentUserTaskList.get(0);
        // 获取所有正常进行的任务节点 Key，这些任务不能直接使用，需要找出其中需要撤回的任务
        List<Task> runTaskList = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
        List<String> runTaskKeyList = new ArrayList<>();
        runTaskList.forEach(item -> runTaskKeyList.add(item.getTaskDefinitionKey()));
        // 需驳回任务列表
        List<String> currentIds = new ArrayList<>();
        // 通过父级网关的出口连线，结合 runTaskList 比对，获取需要撤回的任务
        List<UserTask> currentUserTaskList = FlowableUtils.iteratorFindChildUserTasks(oneUserTask, runTaskKeyList, null, null);
        currentUserTaskList.forEach(item -> currentIds.add(item.getId()));


        // 规定：并行网关之前节点必须需存在唯一用户任务节点，如果出现多个任务节点，则并行网关节点默认为结束节点，原因为不考虑多对多情况
        if (targetIds.size() > 1 && currentIds.size() > 1) {
            throw exception(TASK_ASSIGN_MUTIPLY);
        }

        // 循环获取那些需要被撤回的节点的ID，用来设置驳回原因
        List<String> currentTaskIds = new ArrayList<>();
        currentIds.forEach(currentId -> runTaskList.forEach(runTask -> {
            if (currentId.equals(runTask.getTaskDefinitionKey())) {
                currentTaskIds.add(runTask.getId());
            }
        }));
        // 设置驳回意见
        currentTaskIds.forEach(item -> taskService.addComment(item, task.getProcessInstanceId(), BpmComment.REJECT.getType(), bpmTaskVO.getComment()));




        try {
            // 如果父级任务多于 1 个，说明当前节点不是并行节点，原因为不考虑多对多情况
            if (targetIds.size() > 1) {
                // 1 对 多任务跳转，currentIds 当前节点(1)，targetIds 跳转到的节点(多)
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId()).
                        moveActivityIdsToSingleActivityId(currentIds, targetIds.get(0)).changeState();
            }
            // 如果父级任务只有一个，因此当前任务可能为网关中的任务
            if (targetIds.size() == 1) {
                // 1 对 1 或 多 对 1 情况，currentIds 当前要跳转的节点列表(1或多)，targetIds.get(0) 跳转到的节点(1)
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId())
                        .moveActivityIdsToSingleActivityId(currentIds, targetIds.get(0)).changeState();
            }
        } catch (FlowableObjectNotFoundException e) {
            throw exception(TASK_ASSIGN_CHANGE);
        } catch (FlowableException e) {
            throw exception(TASK_ASSIGN_NO_START);
        }
    }

    @Override
    public BpmTaskExtDTO getDetailTask(Long assigneeUserId, String proinstantId) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getAssigneeUserId, assigneeUserId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, proinstantId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        BpmTaskExtDO bpmTaskExtDO = taskExtMapper.selectOne(lambdaQueryWrapperX);
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        if (bpmTaskExtDO != null) {
            BeanUtils.copyProperties(bpmTaskExtDO, bpmTaskExtDTO);
        }
        return bpmTaskExtDTO;
    }

    @Override
    public BpmTaskExtDTO getAllTask(Long assigneeUserId, String proinstantId,String result) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getAssigneeUserId, assigneeUserId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, proinstantId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult,result);
        BpmTaskExtDO bpmTaskExtDO = taskExtMapper.selectOne(lambdaQueryWrapperX);
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        BeanUtils.copyProperties(bpmTaskExtDO, bpmTaskExtDTO);
        return bpmTaskExtDTO;
    }

    @Override
    public List<BpmTaskExtDO> getDetailTasks(String proinstantId) {
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, proinstantId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        return  taskExtMapper.selectList(lambdaQueryWrapperX);
    }

    @Override
    public void batchUpdate(List<BpmTaskExtDO> bpmTaskExtDOS) {
        for (BpmTaskExtDO bpmTaskExtDO : bpmTaskExtDOS) {
            UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id",bpmTaskExtDO.getId());
            updateWrapper.set("revoke_status", bpmTaskExtDO.getRevokeStatus());
            taskExtMapper.update(null,updateWrapper);
        }
    }

    private Task getTask(String id) {
        return taskService.createTaskQuery().taskId(id).singleResult();
    }

    private HistoricTaskInstance getHistoricTask(String id) {
        return historyService.createHistoricTaskInstanceQuery().taskId(id).singleResult();
    }

    /**
     * 删除跳转的历史节点信息
     *
     * @param disActivityId     跳转的节点id
     * @param processInstanceId 流程实例id
     */
    protected void deleteActivity(String disActivityId, String processInstanceId) {
        String tableName = managementService.getTableName(ActivityInstanceEntity.class);
        String sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and t.ACT_ID_ = #{disActivityId} " +
                " order by t.END_TIME_ ASC";
        List<ActivityInstance> disActivities = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("disActivityId", disActivityId).list();
        //删除运行时和历史节点信息
        if (!CollectionUtils.isAnyEmpty(disActivities)) {
            ActivityInstance activityInstance = disActivities.get(0);
            sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)";
            List<ActivityInstance> datas = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId)
                    .parameter("endTime", activityInstance.getEndTime()).list();
            List<String> runActivityIds = new ArrayList<>();
            if (!CollectionUtils.isAnyEmpty(datas)) {
                datas.forEach(ai -> runActivityIds.add(ai.getId()));
            }
        }
    }

    /**
     * 执行跳转
     */
    protected void moveExecutionsToSingleActivityId(List<String> executionIds, String activityId) {
        runtimeService.createChangeActivityStateBuilder()
                .moveExecutionsToSingleActivityId(executionIds, activityId)
                .changeState();
    }
    @Override
    public PageResult<BpmTaskPageItemRespVO> getTodoAffairTaskPage(Long userId, BpmTaskPageReqVO pageReqVO) {
        IPage<BpmTaskPageItemRespVO> todoPage = MyBatisUtils.buildPage(pageReqVO);
        //  流程标识
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        if (pageReqVO.getClientTag() == null || pageReqVO.getClientTag().equals("web")){
            if (StringUtils.isNotEmpty(pageReqVO.getProcessType())) {
//                if (AffairProcessTypeEnum.CONTRACT_1.getProcessType().equals(pageReqVO.getProcessType())
//                    || AffairProcessTypeEnum.CONTRACT_2.getProcessType().equals(pageReqVO.getProcessType())){
//                    process.add(AffairProcessTypeEnum.CONTRACT_1.getProcessType());
//                    process.add(AffairProcessTypeEnum.CONTRACT_2.getProcessType());
//                    pageReqVO.setProcessType("");
//                }
//                else {
                    process.add(pageReqVO.getProcessType());
//                }
            } else {
                for (AffairProcessNameTypeEnum affairProcessNameTypeEnum : AffairProcessNameTypeEnum.values()) {
                    process.add(affairProcessNameTypeEnum.getType());
                }
            }
            pageReqVO.setKeys(process);
            datas = affairProcessMapper.selectTodoAffairTask(todoPage, userId, pageReqVO);
        } else if (pageReqVO.getClientTag().equals("app")){
            //准备APP查询的条件
            AppQueryCombinedMap conditionMap = AppQueryCombinedMap.initConditionMap(pageReqVO, PageNameEnum.TODO_PAGE);
            conditionMap.setUserId(userId);
            if ( AppQueryCombinedMap.nonNullSqlIncluded( conditionMap.getSqlInclusionMap() ) ){
                datas = affairProcessMapper.selectCombinedForAppTodo(todoPage, userId, conditionMap, conditionMap.getSqlInclusionMap());
            }
            else {
                datas = Collections.emptyList();
            }
            if (!CollectionUtils.isAnyEmpty(datas)){
                //处理摘要信息
//                this.dealApprovalVariables(datas,"todo"); // 待办
                dealApprovalVariables2(datas,PageNameEnum.TODO_PAGE.getPageName());
            }
        }

        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  UserMap
            Set<Long> involvedUserIds = new HashSet<>();
            involvedUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            involvedUserIds.addAll(datas.stream().map(x -> Long.parseLong(x.getAssigneeUserId())).collect(Collectors.toSet()));
            Map<Long,AdminUserRespDTO> tempUserMapPool = adminUserApi.getUserMap(involvedUserIds);
            Map<Long,DeptRespDTO> tempDeptMapPool = new HashMap<>();

            datas.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = lazyLoadUserMap(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId()),tempUserMapPool);
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = lazyLoadDeptMap(assigneeUser.getDeptId(),tempDeptMapPool);
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    List<AdminUserRespDTO> assigneeUsers = assigneeUserIds.stream().map( id -> lazyLoadUserMap(userId,tempUserMapPool)).collect(Collectors.toList());
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = lazyLoadUserMap(bpmTaskPageItemRespVO.getStartUserId(),tempUserMapPool);
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
            });
        }

        return new PageResult<>(datas, todoPage.getTotal());
    }

    @Override
    public PageResult<BpmTaskPageItemRespVO> getTodoAffairHandleTaskPage(Long userId, BpmTaskPageReqVO pageReqVO) {
        IPage<BpmTaskPageItemRespVO> todoPage = MyBatisUtils.buildPage(pageReqVO);
        //  流程标识
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 类型
        if (StringUtils.isEmpty(pageReqVO.getProcessType())) {
            for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
                process.add(affairHandleProcessNameTypeEnum.getType());
            }
            pageReqVO.setKeys(process);
        } else {
           pageReqVO.setKeys(AffairHandleProcessNameTypeEnum.getComprehensiveAffairs(pageReqVO.getProcessType()));
        }
        // 审批中
        //if ("1".equals(pageReqVO.getApprovalStatus())) {
        //    pageReqVO.setTaskDefKeysNo(AffairHandleTaskNameCodeEnum.getAllHandleTaskCode());
        //} else if("2".equals(pageReqVO.getApprovalStatus())) {
        //    pageReqVO.setTaskDefKeys(AffairHandleTaskNameCodeEnum.getAllHandleTaskCode());
        //}
        datas = affairProcessMapper.selectTodoAffairHandleTask(todoPage, userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  startUserMap
            HashSet<Long> startUserIds = new HashSet<>();
            startUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long, AdminUserRespDTO> startUserMap = adminUserApi.getUserMap(startUserIds);

            datas.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = adminUserApi.getUser(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId())).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = deptApi.getDept(assigneeUser.getDeptId()).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = adminUserApi.getUsers(assigneeUserIds).getCheckedData();
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(adminUserRespDTO -> adminUserRespDTO.getNickname()).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = startUserMap.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
                //  processType 和 状态
                convertTodoProcessForAffairHandle(bpmTaskPageItemRespVO);
                BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(bpmTaskPageItemRespVO.getProcessInstanceId());
                if(tempProcess.getFormVariables().get("applyName") != null) {
                    bpmTaskPageItemRespVO.setApplyEnterpriseName(tempProcess.getFormVariables().get("applyName").toString());
                }
                if(tempProcess.getFormVariables().get("submitTime") != null) {
                    try {
                        bpmTaskPageItemRespVO.setSubmitTime(LocalDateTimeUtil.parse(tempProcess.getFormVariables().get("submitTime").toString(),FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    }catch (DateTimeParseException e) {
                        bpmTaskPageItemRespVO.setSubmitTime(bpmTaskPageItemRespVO.getProcessInstanceStartTime());
                    }
                }
            });
        }
        return new PageResult<>(datas, todoPage.getTotal());
    }

    /**
     * bpmTaskPageItemRespVO 返回体的实例状态转换
     * @param bpmTaskPageItemRespVO
     */
    private static void convertTodoProcessForAffairHandle(BpmTaskPageItemRespVO bpmTaskPageItemRespVO){
        if ( StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getProcessInstanceKey()) && StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getTaskDefKey()) ){
            bpmTaskPageItemRespVO.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(bpmTaskPageItemRespVO.getProcessInstanceKey()).getSubject());
            bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.getTypeByRemark(AffairHandleTaskNameCodeEnum.getByCode(bpmTaskPageItemRespVO.getTaskDefKey()).getStatusName()));
        }
    }

    private static void convertDoneProcessForAffairHandle(BpmTaskPageItemRespVO bpmTaskPageItemRespVO){
        //if (StringUtils.isEmpty(bpmTaskPageItemRespVO.getFlowFlag())) {
        //    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.SUCCESS.getType());
        //} else

        if(bpmTaskPageItemRespVO.getProcessInstanceResult() == 1)
        {
            bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.getTypeByRemark(AffairHandleTaskNameCodeEnum.getByCode(bpmTaskPageItemRespVO.getTaskDefKey()).getStatusName()));
        }
        else
        {
            if (StringUtils.isEmpty(bpmTaskPageItemRespVO.getFlowFlag())  && bpmTaskPageItemRespVO.getProcessInstanceResult() == 2) {
                bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.SUCCESS.getType());
            }
        }

        if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getFlowFlag())
                && AffairHandleFlowFlagEnum.REJECT.getValue().equals(bpmTaskPageItemRespVO.getFlowFlag())){
            bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.REJECT.getType());
        } else if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getFlowFlag())
                && AffairHandleFlowFlagEnum.INCOMPLETE.getValue().equals(bpmTaskPageItemRespVO.getFlowFlag())) {
            bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.REJECT.getType());
        }
        //else if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getFlowFlag())
        //        && "2".equals(bpmTaskPageItemRespVO.getFlowFlag())) {
        //    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.CANCEL.getType());
        //}
    }

    /**
     * 获取待办任务导出列表
     * @param pageReqVO 查询条件
     * @return 结果
     * */
    public List<TodoTaskExcelVO> getTodoTaskList(Long userId,TodoTaskExportReqVO pageReqVO)
    {

        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 类型
        if (StringUtils.isEmpty(pageReqVO.getProcessType())) {
            for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
                process.add(affairHandleProcessNameTypeEnum.getType());
            }
            pageReqVO.setKeys(process);
        } else {
            pageReqVO.setKeys(AffairHandleProcessNameTypeEnum.getComprehensiveAffairs(pageReqVO.getProcessType()));
        }
        // 审批中
        //if ("1".equals(pageReqVO.getApprovalStatus())) {
        //    pageReqVO.setTaskDefKeysNo(AffairHandleTaskNameCodeEnum.getAllHandleTaskCode());
        //} else if("2".equals(pageReqVO.getApprovalStatus())) {
        //    pageReqVO.setTaskDefKeys(AffairHandleTaskNameCodeEnum.getAllHandleTaskCode());
        //}
        datas = affairProcessMapper.selectTodoAffairHandleTask2( userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  startUserMap
            HashSet<Long> startUserIds = new HashSet<>();
            startUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long, AdminUserRespDTO> startUserMap = adminUserApi.getUserMap(startUserIds);

            datas.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = adminUserApi.getUser(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId())).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = deptApi.getDept(assigneeUser.getDeptId()).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = adminUserApi.getUsers(assigneeUserIds).getCheckedData();
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(adminUserRespDTO -> adminUserRespDTO.getNickname()).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = startUserMap.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
                //  processType 和 状态
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getProcessInstanceKey()) && StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getTaskDefKey())) {
                    bpmTaskPageItemRespVO.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(bpmTaskPageItemRespVO.getProcessInstanceKey()).getSubject());
                    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.getTypeByRemark(AffairHandleTaskNameCodeEnum.getByCode(bpmTaskPageItemRespVO.getTaskDefKey()).getStatusName()));

                }


                BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(bpmTaskPageItemRespVO.getProcessInstanceId());
                if(tempProcess.getFormVariables().get("applyName") != null)
                {
                    bpmTaskPageItemRespVO.setApplyEnterpriseName(tempProcess.getFormVariables().get("applyName").toString());
                }
                if(tempProcess.getFormVariables().get("submitTime") != null) {
                    try {
                        bpmTaskPageItemRespVO.setSubmitTime(LocalDateTimeUtil.parse(tempProcess.getFormVariables().get("submitTime").toString(),FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    }catch (DateTimeParseException e) {
                        bpmTaskPageItemRespVO.setSubmitTime(bpmTaskPageItemRespVO.getProcessInstanceStartTime());
                    }
                }
            });
        }

        List<TodoTaskExcelVO> resultList = new ArrayList<>();
        datas.forEach(data -> {
            TodoTaskExcelVO todoTaskExcelVO = new TodoTaskExcelVO();
            todoTaskExcelVO.setTaskDefKey(data.getTaskDefKey());
            todoTaskExcelVO.setAssigneeUserDeptName(data.getAssigneeUserDeptName());
            todoTaskExcelVO.setAssigneeUserNickname(data.getAssigneeUserNickname());
            todoTaskExcelVO.setProcessType(data.getProcessType());
            todoTaskExcelVO.setApplyEnterpriseName(data.getApplyEnterpriseName());
            todoTaskExcelVO.setProcessInstanceName(data.getProcessInstanceName());
            if(Objects.nonNull(data.getSubmitTime())){
                todoTaskExcelVO.setSubmitDate(data.getSubmitTime().toLocalDate());
            }
            todoTaskExcelVO.setProcessInstanceResult(data.getProcessInstanceResult());

            resultList.add(todoTaskExcelVO);

        });
        return resultList;
    }

    /**
     * 获取已办任务导出列表
     * @param pageReqVO 查询条件
     * @return 结果
     * */
    public List<DoneTaskExcelVO> getDoneTaskList(Long userId, DoneTaskExportReqVO pageReqVO)
    {
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 政务办理 流程任务节点
        List<String> taskDefs = Arrays.stream(AffairHandleStartTaskCodeEnum.values()).map(AffairHandleStartTaskCodeEnum::getCode).collect(Collectors.toList());
        pageReqVO.setTaskDefKeys(taskDefs);
        // 政务办理 processType
        if (StringUtils.isEmpty(pageReqVO.getProcessType())) {
            for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
                process.add(affairHandleProcessNameTypeEnum.getType());
            }
            pageReqVO.setKeys(process);
        } else {
            pageReqVO.setKeys(AffairHandleProcessNameTypeEnum.getComprehensiveAffairs(pageReqVO.getProcessType()));
        }
        //根据查询审批类型设置需要查询的process
        //pageReqVO.setKeys(process);
        datas= affairProcessMapper.selectDoneAffairHandleTask2(userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  startUserMap
            HashSet<Long> startUserIds = new HashSet<>();
            startUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long, AdminUserRespDTO> startUserMap = adminUserApi.getUserMap(startUserIds);

            datas.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = adminUserApi.getUser(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId())).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = deptApi.getDept(assigneeUser.getDeptId()).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = adminUserApi.getUsers(assigneeUserIds).getCheckedData();
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(adminUserRespDTO -> adminUserRespDTO.getNickname()).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = startUserMap.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }

                if(bpmTaskPageItemRespVO.getProcessInstanceResult() == 1)
                {
                    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.getTypeByRemark(AffairHandleTaskNameCodeEnum.getByCode(bpmTaskPageItemRespVO.getTaskDefKey()).getStatusName()));
                }
                else
                {
                    if (StringUtils.isEmpty(bpmTaskPageItemRespVO.getFlowFlag()) && bpmTaskPageItemRespVO.getProcessInstanceResult() == 2) {
                        bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.SUCCESS.getType());
                    }
                }

                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getFlowFlag())
                        && AffairHandleFlowFlagEnum.REJECT.getValue().equals(bpmTaskPageItemRespVO.getFlowFlag())){
                    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.REJECT.getType());
                } else if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getFlowFlag())
                        && AffairHandleFlowFlagEnum.INCOMPLETE.getValue().equals(bpmTaskPageItemRespVO.getFlowFlag())) {
                    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.REJECT.getType());
                }
                //else if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getFlowFlag())
                //        && "2".equals(bpmTaskPageItemRespVO.getFlowFlag())) {
                //    bpmTaskPageItemRespVO.setProcessInstanceResult(AffairBusinessStatusEnum.CANCEL.getType());
                //}
                BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(bpmTaskPageItemRespVO.getProcessInstanceId());
                if(tempProcess.getFormVariables().get("applyName") != null) {
                    bpmTaskPageItemRespVO.setApplyEnterpriseName(tempProcess.getFormVariables().get("applyName").toString());
                }
                if(tempProcess.getFormVariables().get("submitTime") != null) {
                    try {
                        bpmTaskPageItemRespVO.setSubmitTime(LocalDateTimeUtil.parse(tempProcess.getFormVariables().get("submitTime").toString(),FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    }catch (DateTimeParseException e) {
                        bpmTaskPageItemRespVO.setSubmitTime(bpmTaskPageItemRespVO.getProcessInstanceStartTime());
                    }}
            });

        }
        datas.forEach(data -> data.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(data.getProcessInstanceKey()).getSubject()));

        List<DoneTaskExcelVO> resultList = new ArrayList<>();
        datas.forEach(data -> {
            DoneTaskExcelVO doneTaskExcelVO = new DoneTaskExcelVO();
            doneTaskExcelVO.setTaskDefKey(data.getTaskDefKey());
            doneTaskExcelVO.setAssigneeUserDeptName(data.getAssigneeUserDeptName());
            doneTaskExcelVO.setAssigneeUserNickname(data.getAssigneeUserNickname());
            doneTaskExcelVO.setProcessType(data.getProcessType());
            doneTaskExcelVO.setApplyEnterpriseName(data.getApplyEnterpriseName());
            doneTaskExcelVO.setProcessInstanceName(data.getProcessInstanceName());
            if(Objects.nonNull(data.getSubmitTime())){
                doneTaskExcelVO.setSubmitDate(data.getSubmitTime().toLocalDate());
            }
            doneTaskExcelVO.setProcessInstanceResult(data.getProcessInstanceResult());

            resultList.add(doneTaskExcelVO);

        });
        return resultList;


    }

    @Override
    public PageResult<BpmTaskPageItemRespVO> getDoneAffairHandleTaskPage(Long userId, BpmTaskPageReqVO pageReqVO) {
        IPage<BpmTaskPageItemRespVO>  donePage = MyBatisUtils.buildPage(pageReqVO);
        //  流程标识
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 政务办理 流程任务节点
        List<String> taskDefs = Arrays.stream(AffairHandleStartTaskCodeEnum.values()).map(AffairHandleStartTaskCodeEnum::getCode).collect(Collectors.toList());
        pageReqVO.setTaskDefKeys(taskDefs);
        // 政务办理 processType
        if (StringUtils.isEmpty(pageReqVO.getProcessType())) {
            for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
                process.add(affairHandleProcessNameTypeEnum.getType());
            }
            pageReqVO.setKeys(process);
        } else {
            pageReqVO.setKeys(AffairHandleProcessNameTypeEnum.getComprehensiveAffairs(pageReqVO.getProcessType()));
        }
        //根据查询审批类型设置需要查询的process
        //pageReqVO.setKeys(process);
        datas= affairProcessMapper.selectDoneAffairHandleTask(donePage,userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  startUserMap
            HashSet<Long> startUserIds = new HashSet<>();
            startUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long, AdminUserRespDTO> startUserMap = adminUserApi.getUserMap(startUserIds);
            datas.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = adminUserApi.getUser(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId())).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = deptApi.getDept(assigneeUser.getDeptId()).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = adminUserApi.getUsers(assigneeUserIds).getCheckedData();
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(adminUserRespDTO -> adminUserRespDTO.getNickname()).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = startUserMap.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
                convertDoneProcessForAffairHandle(bpmTaskPageItemRespVO);
                BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(bpmTaskPageItemRespVO.getProcessInstanceId());
                if(tempProcess.getFormVariables().get("applyName") != null)
                {
                    bpmTaskPageItemRespVO.setApplyEnterpriseName(tempProcess.getFormVariables().get("applyName").toString());
                }
                if(tempProcess.getFormVariables().get("submitTime") != null) {
                    try {
                        bpmTaskPageItemRespVO.setSubmitTime(LocalDateTimeUtil.parse(tempProcess.getFormVariables().get("submitTime").toString(),FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    }catch (DateTimeParseException e) {
                        bpmTaskPageItemRespVO.setSubmitTime(bpmTaskPageItemRespVO.getProcessInstanceStartTime());
                    }
                }
            });
        }
        datas.forEach(data -> data.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(data.getProcessInstanceKey()).getSubject()));
        return new PageResult<>(datas, donePage.getTotal());
    }

    /**
     * 获得政务办理的督办任务分页
     * @param userId
     * @param pageReqVO
     * @return
     */
    public PageResult<BpmTaskPageItemRespVO> getSuperviseAffairHandleTaskPage(Long userId, BpmTaskPageReqVO pageReqVO)
    {
        IPage<BpmTaskPageItemRespVO>  donePage = MyBatisUtils.buildPage(pageReqVO);
        //  流程标识
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 政务办理 流程任务节点
        List<String> taskDefs = Arrays.stream(AffairHandleStartTaskCodeEnum.values()).map(AffairHandleStartTaskCodeEnum::getCode).collect(Collectors.toList());
        pageReqVO.setTaskDefKeys(taskDefs);
        // 政务办理 processType
        if (StringUtils.isEmpty(pageReqVO.getProcessType())) {
            for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
                process.add(affairHandleProcessNameTypeEnum.getType());
            }
            pageReqVO.setKeys(process);
        } else {
            pageReqVO.setKeys(AffairHandleProcessNameTypeEnum.getComprehensiveAffairs(pageReqVO.getProcessType()));
        }
        //根据查询审批类型设置需要查询的process
        //pageReqVO.setKeys(process);
        datas= affairProcessMapper.selectSuperviseAffairHandleTask(donePage,userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  startUserMap
            HashSet<Long> startUserIds = new HashSet<>();
            startUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long, AdminUserRespDTO> startUserMap = adminUserApi.getUserMap(startUserIds);
            datas.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = adminUserApi.getUser(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId())).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = deptApi.getDept(assigneeUser.getDeptId()).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = adminUserApi.getUsers(assigneeUserIds).getCheckedData();
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(adminUserRespDTO -> adminUserRespDTO.getNickname()).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = startUserMap.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
                convertDoneProcessForAffairHandle(bpmTaskPageItemRespVO);
                BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(bpmTaskPageItemRespVO.getProcessInstanceId());
                if(tempProcess.getFormVariables().get("applyName") != null)
                {
                    bpmTaskPageItemRespVO.setApplyEnterpriseName(tempProcess.getFormVariables().get("applyName").toString());
                }
                if(tempProcess.getFormVariables().get("submitTime") != null) {
                    try {
                        bpmTaskPageItemRespVO.setSubmitTime(LocalDateTimeUtil.parse(tempProcess.getFormVariables().get("submitTime").toString(),FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    }catch (DateTimeParseException e) {
                        bpmTaskPageItemRespVO.setSubmitTime(bpmTaskPageItemRespVO.getProcessInstanceStartTime());
                    }
                }
            });
        }
        datas.forEach(data -> data.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(data.getProcessInstanceKey()).getSubject()));
        return new PageResult<>(datas, donePage.getTotal());
    }

    @Override
    public Integer getTodoAffairTaskCount(Long userId) {
        List<String> keys = new ArrayList<>();
        for (AffairProcessNameTypeEnum affairProcessNameTypeEnum : AffairProcessNameTypeEnum.values()) {
            keys.add(affairProcessNameTypeEnum.getType());
        }
        return affairProcessMapper.selectTodoAffairTaskCount(userId,keys);
    }

    @Override
    public Map<String, Long> getTodoAffairTaskCountForAPP(Long userId) {
        Map<String,CombinedSqlInclusion> sqlInclusionMap = CombinedSqlInclusion.createMapIncludeAll();
        List<Map<String, Object>> countMapList = affairProcessMapper.selectTodoTaskCountForApp(userId,sqlInclusionMap);
        Map<String,Long> resVO = new HashMap<>();
        //总数
        resVO.put("total",0L);
        //提醒
        resVO.put("remind_count",0L);
        //督办
        resVO.put("supervise_count",0L);
        //逾期
        resVO.put("overdue_count",0L);
        if (!CollectionUtils.isAnyEmpty(countMapList)){
            countMapList.forEach(x -> {
                Long xCount = Objects.nonNull(x.get("counts")) ? (Long) x.get("counts") : 0;
                Long total = resVO.get("total") + xCount;
                resVO.put("total",total);
                if (Objects.nonNull(x.get("superviseStatus")) && !"".equals(x.get("superviseStatus"))){
//                    if (DocRemindType.isRemind(x.get("superviseStatus").toString())){
//                        Long resCount = resVO.get("remind_count") + xCount;
//                        resVO.put("remind_count",resCount);
//                    }
//                    else if (DocRemindType.isSupervise(x.get("superviseStatus").toString())){
//                        Long resCount = resVO.get("supervise_count") + xCount;
//                        resVO.put("supervise_count",resCount);
//                    }
//                    if (DocRemindType.isExceed(x.get("superviseStatus").toString())){
//                        Long resCount = resVO.get("overdue_count") + xCount;
//                        resVO.put("overdue_count",resCount);
//                    }
                }
            });
        }
        return resVO;
    }

    @Override
    public Map<String,Object> getHandleCount(Long userId) {
        BpmTaskPageReqVO pageReqVO = new BpmTaskPageReqVO();
        IPage<BpmTaskPageItemRespVO>  donePage = MyBatisUtils.buildPage(pageReqVO);
        //  流程标识
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 政务办理 流程任务节点
        List<String> taskDefs = Arrays.stream(AffairHandleStartTaskCodeEnum.values()).map(AffairHandleStartTaskCodeEnum::getCode).collect(Collectors.toList());
        pageReqVO.setTaskDefKeys(taskDefs);
        // 政务办理 processType
        for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
            process.add(affairHandleProcessNameTypeEnum.getType());
        }
        //根据查询审批类型设置需要查询的process
        pageReqVO.setKeys(process);
        datas= affairProcessMapper.selectDoneAffairHandleTask(donePage,userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        Map<String, Object> map = new HashMap<>();
        map.put("doCount", donePage.getTotal());

        IPage<BpmTaskPageItemRespVO> todoPage = MyBatisUtils.buildPage(pageReqVO);
        //
        for (AffairHandleProcessNameTypeEnum affairHandleProcessNameTypeEnum : AffairHandleProcessNameTypeEnum.values()) {
            process.add(affairHandleProcessNameTypeEnum.getType());
        }
        pageReqVO.setTaskDefKeys(null);
        pageReqVO.setKeys(process);
        datas = affairProcessMapper.selectTodoAffairHandleTask(todoPage, userId, pageReqVO,
                AffairHandleTaskNameCodeEnum.getHandleSet(),AffairHandleTaskNameCodeEnum.getCheckSet(),
                AffairHandleTaskNameCodeEnum.getDistributeSet(),AffairHandleTaskNameCodeEnum.getFeedbackSet());
        map.put("toDoCount", todoPage.getTotal());

        return map;
    }

    @Override
    public PageResult<BpmTaskPageItemRespVO> getDoneAffairTaskPage(Long userId, BpmTaskPageReqVO pageReqVO) {
        IPage<BpmTaskPageItemRespVO>  donePage = MyBatisUtils.buildPage(pageReqVO);
        //  流程标识
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();
        // 流程任务节点
        List<String> taskDefs = Arrays.stream(AffairStartTaskCodeEnum.values()).map(AffairStartTaskCodeEnum::getCode).collect(Collectors.toList());
        pageReqVO.setTaskDefKeys(taskDefs);
        //判断是web查询还是app查询
        if (pageReqVO.getClientTag() == null || pageReqVO.getClientTag().equals("web")){
            if (StringUtils.isNotEmpty(pageReqVO.getProcessType())) {
//                if (AffairProcessTypeEnum.CONTRACT_1.getProcessType().equals(pageReqVO.getProcessType())
//                        || AffairProcessTypeEnum.CONTRACT_2.getProcessType().equals(pageReqVO.getProcessType())){
//                    process.add(AffairProcessTypeEnum.CONTRACT_1.getProcessType());
//                    process.add(AffairProcessTypeEnum.CONTRACT_2.getProcessType());
//                    pageReqVO.setProcessType("");
//                }
//                else {
                    process.add(pageReqVO.getProcessType());
//                }
            } else {
                for (AffairProcessNameTypeEnum affairProcessNameTypeEnum : AffairProcessNameTypeEnum.values()) {
                    process.add(affairProcessNameTypeEnum.getType());
                }
            }
            //根据查询审批类型设置需要查询的process
            pageReqVO.setKeys(process);
            datas= affairProcessMapper.selectDoneAffairTask(donePage,userId, pageReqVO);
        }
        else if (pageReqVO.getClientTag().equals("app")){
            //准备APP查询的条件
            AppQueryCombinedMap conditionMap = AppQueryCombinedMap.initConditionMap(pageReqVO, PageNameEnum.DONE_PAGE);
            if ( AppQueryCombinedMap.nonNullSqlIncluded( conditionMap.getSqlInclusionMap() ) ){
                datas = affairProcessMapper.selectCombinedForAppDone(donePage, userId, conditionMap, conditionMap.getSqlInclusionMap());
            }
            else {
                datas = Collections.emptyList();
            }
            if (!CollectionUtils.isAnyEmpty(datas)){
                //处理摘要信息
//                this.dealApprovalVariables(datas, "done"); // 已办
                dealApprovalVariables2(datas,PageNameEnum.DONE_PAGE.getPageName());
            }
        }

        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  UserMap
            Set<Long> involvedUserIds = new HashSet<>();
            involvedUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            involvedUserIds.addAll(datas.stream().map(x -> Long.parseLong(x.getAssigneeUserId())).collect(Collectors.toSet()));
            Map<Long,AdminUserRespDTO> tempUserMapPool = adminUserApi.getUserMap(involvedUserIds);
            Map<Long,DeptRespDTO> tempDeptMapPool = new HashMap<>();

            datas.forEach(bpmTaskPageItemRespVO -> {
//                if (BpmProcessInstanceResultEnum.APPROVE.getResult().equals(bpmTaskPageItemRespVO.getProcessInstanceResult())) {
//                    bpmTaskPageItemRespVO.setAssigneeUserId(null);
//                    bpmTaskPageItemRespVO.setTaskId(null).setTaskName(null).setTaskDefKey(null).setTaskResult(null).setTaskStartTime(null).setTaskEndTime(null);
//                }
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = lazyLoadUserMap(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId()),tempUserMapPool);
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = lazyLoadDeptMap(assigneeUser.getDeptId(),tempDeptMapPool);
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds  已办所有
                    List<Long> assigneeUserIds = affairProcessMapper.selectAllAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());

                    if(BpmSeqTaskEnum.contains(bpmTaskPageItemRespVO.getTaskDefKey())){
                        assigneeUserIds.clear();
                        assigneeUserIds.add(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId()));
                        bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    }else {
                        bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    }

                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = assigneeUserIds.stream().map( id -> lazyLoadUserMap(id,tempUserMapPool)).collect(Collectors.toList());
                    List<String> assigneeUserNickNames = new ArrayList<>();
                    List<String> assigneeUserDeptNames = new ArrayList<>();
                    for (AdminUserRespDTO adminUserRespDTO : assigneeUsers) {
                        assigneeUserNickNames.add(adminUserRespDTO.getNickname());
                        assigneeUserDeptNames.add(lazyLoadDeptMap(adminUserRespDTO.getDeptId(),tempDeptMapPool).getName());
                    }
                    bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    bpmTaskPageItemRespVO.setAssigneeUserDeptNames(assigneeUserDeptNames);

                }
                //  startUserNickName
                AdminUserRespDTO startUser = tempUserMapPool.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
            });
        }
        return new PageResult<>(datas, donePage.getTotal());
    }
    @Override
    public PageResult<BpmTaskPageItemRespVO> getStartAffairTaskPage(Long userId, BpmTaskPageReqVO pageReqVO) {
        // MyBatis Plus 查询
        IPage<BpmTaskPageItemRespVO> mpPage = MyBatisUtils.buildPage(pageReqVO);
        List<String> process = new ArrayList<>();
        List<BpmTaskPageItemRespVO> datas = new ArrayList<>();

        //判断是web查询还是app查询
        if (pageReqVO.getClientTag() == null || pageReqVO.getClientTag().equals("web")){
            if (StringUtils.isNotEmpty(pageReqVO.getProcessType())) {
//                if (AffairProcessTypeEnum.CONTRACT_1.getProcessType().equals(pageReqVO.getProcessType())
//                        || AffairProcessTypeEnum.CONTRACT_2.getProcessType().equals(pageReqVO.getProcessType())){
//                    process.add(AffairProcessTypeEnum.CONTRACT_1.getProcessType());
//                    process.add(AffairProcessTypeEnum.CONTRACT_2.getProcessType());
//                    pageReqVO.setProcessType("");
//                }
//                else {
                    process.add(pageReqVO.getProcessType());
//                }
            } else {
                for (AffairProcessNameTypeEnum affairProcessNameTypeEnum : AffairProcessNameTypeEnum.values()) {
                    process.add(affairProcessNameTypeEnum.getType());
                }
            }
            //根据查询审批类型设置需要查询的process
            pageReqVO.setKeys(process);
            datas= affairProcessMapper.selectStartAffairTask(mpPage,userId, pageReqVO);
            for (BpmTaskPageItemRespVO pageItemRespVO : datas) {
                if (StringUtils.isNotEmpty(pageItemRespVO.getFlowFlag())) {
                    if ("3".equals(pageItemRespVO.getFlowFlag())) {
                        pageItemRespVO.setFlowFlag("1");
                    } else if("4".equals(pageItemRespVO.getFlowFlag())) {
                        pageItemRespVO.setFlowFlag("2");
                    }
                }
            }
        }
        else if (pageReqVO.getClientTag().equals("app")){

            AppQueryCombinedMap conditionMap = AppQueryCombinedMap.initConditionMap(pageReqVO, PageNameEnum.MY_LAUNCH_PAGE);
            if ( AppQueryCombinedMap.nonNullSqlIncluded( conditionMap.getSqlInclusionMap() ) ){
                datas = affairProcessMapper.selectCombinedForAppMyLaunch(mpPage, userId, conditionMap, conditionMap.getSqlInclusionMap());
            }
            else {
                datas = Collections.emptyList();
            }
            if (!CollectionUtils.isAnyEmpty(datas)){
                //处理摘要信息
//                this.dealApprovalVariables(datas, "myLaunch");
                dealApprovalVariables2(datas,PageNameEnum.MY_LAUNCH_PAGE.getPageName());
            }

        }

        if (!CollectionUtils.isAnyEmpty(datas)) {
            //  UserMap
            Set<Long> involvedUserIds = new HashSet<>();
            involvedUserIds.addAll(datas.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long,AdminUserRespDTO> tempUserMapPool = adminUserApi.getUserMap(involvedUserIds);
            Map<Long,DeptRespDTO> tempDeptMapPool = new HashMap<>();

            for (BpmTaskPageItemRespVO bpmTaskPageItemRespVO : datas) {
                List<String> userIdStrs = new ArrayList<>();
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    userIdStrs.addAll(Arrays.asList(bpmTaskPageItemRespVO.getAssigneeUserId().split(",")));
                    List<Long> luserIds = userIdStrs.stream().map(Long::parseLong).collect(Collectors.toList());
                    List<AdminUserRespDTO> assigneeUsers = luserIds.stream().map( id -> lazyLoadUserMap(id,tempUserMapPool)).collect(Collectors.toList());
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> names = assigneeUsers.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
                        List<Long> deptIds = assigneeUsers.stream().map(AdminUserRespDTO::getDeptId).collect(Collectors.toList());
                        List<DeptRespDTO> deptRespDTOS =  deptIds.stream().map( id -> lazyLoadDeptMap(id,tempDeptMapPool)).collect(Collectors.toList());
                        List<String> deptNames = deptRespDTOS.stream().map(DeptRespDTO::getName).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickname(String.join(",",names));
                        bpmTaskPageItemRespVO.setAssigneeUserDeptName(String.join(",",deptNames));

                    }
                }
                AdminUserRespDTO startUser = lazyLoadUserMap(bpmTaskPageItemRespVO.getStartUserId(),tempUserMapPool);
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
            }
        }
        return new PageResult<>(datas, mpPage.getTotal());
    }

    @Override
    public String getTaskIdByInstanceId(Long userId, String processInstanceId) {
        BpmTaskExtDTO bpmTaskExtDTO = this.getDetailTask(userId,processInstanceId);
        if (bpmTaskExtDTO != null) {
            return bpmTaskExtDTO.getTaskId();
        }
        return "";
    }


    @Override
    public BpmTaskExtDTO getTaskByInstanceId(Long userId, String processInstanceId) {
        BpmTaskExtDTO bpmTaskExtDTO = this.getDetailTask(userId,processInstanceId);
        return bpmTaskExtDTO;
    }

    @Override
    public String getTaskDefByInstanceId(Long userId, String processInstanceId) {
        BpmTaskExtDTO bpmTaskExtDTO = this.getDetailTask(userId,processInstanceId);
        if (bpmTaskExtDTO != null) {
            return bpmTaskExtDTO.getTaskDefKey();
        }
        return "";
    }

    @Override
    public String getNextTaskName(String taskId){
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        // 获取流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        String nextTaskName = "";
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        ArrayList<UserTask> userTaskList = new ArrayList<>();
        getNextUserTaskList(userTaskList, bpmnModel, task.getTaskDefinitionKey());
        if (CollUtil.isNotEmpty(userTaskList)) {
            nextTaskName = userTaskList.get(0).getName();
        }
        return nextTaskName;
    }

    @Override
    public  Map<String,Object>  getNextTaskApprovals(Long loginUserId, String taskId, String nextTaskName, Long deptId){
        //当前节点为选择审批人时
        //先获取下一节点任务名nextTaskName
        if(Objects.isNull(nextTaskName) || nextTaskName.isEmpty()){
            nextTaskName = getNextTaskName(taskId);
        }

        Map<String, Object> map = new HashMap<>();
        List<String> postCodes = new ArrayList<>();
        List<AdminUserRespDTO> checkedData = new ArrayList<>();

        switch (nextTaskName){
            //若nextTaskName为分管校院领导审批，则返回对应审批人列表
            case "分管校（院）领导审批":
                map.put("post", "分管校（院）领导");
                //获得登录人所在机构的职位为分管校院领导的用户列表
                postCodes.add(SCHOOL_LEADER.getPostCode());
                checkedData = adminUserApi.getUsersByPostCode(loginUserId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            //若nextTaskName为常务副校（院）长审批
            case "常务副校（院）长审批":
                map.put("post", "常务副校（院）长");
                postCodes.add(VICE_CHANCELLOR.getPostCode());
                checkedData = adminUserApi.getUsersByPostCode(loginUserId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            case "分管日常工作的副校长（副院长）审批":
                map.put("post", "分管日常工作的副校长（副院长）");
                postCodes.add(VICE_CHANCELLOR.getPostCode());
                checkedData = adminUserApi.getUsersByPostCode(loginUserId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
//            case "校（院）委会议审批":
//                map.put("post", "校（院）委会议审批");
//                postCodes.add(SCHOOL_LEADER.getPostCode());
//                map.put("users", adminUserApi.getUsersByPostCode(loginUserId, postCodes).getCheckedData());
//                break;
            case "秘书科汇总":
                map.put("post", "秘书科");
                postCodes.add("oa-secretary");
                checkedData = adminUserApi.getUsersByPostCode(loginUserId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            case "分管副主任审批":
                map.put("post", "分管副主任审批");
                postCodes.add("swdx-vice-director");
                checkedData = adminUserApi.getDeptUsersByPostCode(loginUserId, deptId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            case "分管主任审批":
                map.put("post", "分管主任审批");
                postCodes.add("swdx-director");
                checkedData = adminUserApi.getDeptUsersByPostCode(loginUserId, deptId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            case "校领导审批":
                map.put("post", "校领导审批");
                postCodes.add("oa-vice-chancellor");
                postCodes.add("swdx-school-leader");
                checkedData = adminUserApi.getUsersByPostCode(loginUserId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            case "部门分管负责人审批":
                map.put("post", "副主任");
                postCodes.add("swdx-vice-director");
                checkedData = adminUserApi.getDeptUsersByPostCode(loginUserId, deptId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
            case "部门主要负责人审批":
                map.put("post", "部门主要负责人审批");
                postCodes.add("swdx-director");
                checkedData = adminUserApi.getDeptUsersByPostCode(loginUserId, deptId, postCodes).getCheckedData();
                if(checkedData.size() > 1) {
                    checkedData = checkedData.stream().filter(user -> !Objects.equals(user.getId(), loginUserId)).collect(Collectors.toList());
                }
                map.put("users", checkedData);
                break;
        }
        return map;
    }

    @Override
    public  Map<String,Object>  getNextTaskApproval(String taskId, String type,String proDefinitionKey, String condition) {
        String nextTaskDef = "";
        String nextTaskName = "";
        if ("1".equals(type) && StringUtils.isNotEmpty(taskId)) {
            // 正向流程
            if (taskService.createTaskQuery().taskId(taskId).singleResult().isSuspended()) {
                throw exception(TASK_ASSIGN_HANNGUP);
            }
            // 当前任务 task
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            // 获取流程定义信息
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
            if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(processDefinition.getKey()) &&
                    AffairTaskNameCodeEnum.WEB_CHECK.getCode().equals(task.getTaskDefinitionKey())) {
                if ("1".equals(condition)) {
                    nextTaskDef = AffairTaskNameCodeEnum.WEB_MAIN_F.getCode();
                    nextTaskName = AffairTaskNameCodeEnum.WEB_MAIN_F.getName();
                } else {
                    nextTaskDef = AffairTaskNameCodeEnum.WEB_APPROVE.getCode();
                    nextTaskName = AffairTaskNameCodeEnum.WEB_APPROVE.getName();
                }
            }else  if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(processDefinition.getKey()) &&
                    AffairTaskNameCodeEnum.WEB_APPROVE_MAIN.getCode().equals(task.getTaskDefinitionKey())
            ){
                BpmTaskExtDO taskExt = taskExtMapper.selectByTaskId(taskId);
                if (taskExt == null) {
                    throw exception(TASK_NO_EXIST);
                }
                BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(taskExt.getProcessInstanceId());
                Map<String, Object> data = bpmProcessInstanceExtDO.getFormVariables();
                List<Long> leaders =(List<Long>)data.get("chargeLeader");
                if (!Objects.isNull(data.get("chargeLeaderSeq")) && "1".equals(data.get("chargeLeaderSeq").toString())) {
                    List<Integer> leads  =(List<Integer>)data.get("chargeLeader");
                    List<Long> lds = leads.stream().map(a->Long.valueOf(a)).collect(Collectors.toList());
                    List<AdminUserRespDTO> adminUserRespDTO = adminUserApi.getUsers(lds).getCheckedData();
                    List<String> names = adminUserRespDTO.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
                    AdminUserRespDTO user = adminUserRespDTO.get(0);
                    user.setNickname(String.join(",", names));
                    Map<String, Object> map = new HashMap<>();
                    map.put("taskName", AffairTaskNameCodeEnum.WEB_LEADER_SIGN.getName());
                    map.put("taskDefKey", AffairTaskNameCodeEnum.WEB_LEADER_SIGN.getCode());
                    map.put("user", user);
                    return map;
                }
                Long value = Long.valueOf(String.valueOf(leaders.get(0)));
                AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(value).getCheckedData();
                Map<String, Object> map = new HashMap<>();
                map.put("taskName", AffairTaskNameCodeEnum.WEB_LEADER_SIGN_SEQ.getName());
                map.put("taskDefKey", AffairTaskNameCodeEnum.WEB_LEADER_SIGN_SEQ.getCode());
                map.put("user", adminUserRespDTO);
                return map;
            } else  if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(processDefinition.getKey()) &&
                    AffairTaskNameCodeEnum.WEB_LEADER_SIGN.getCode().equals(task.getTaskDefinitionKey())
            ) {
                if ("1".equals(condition)) {
                    BpmTaskExtDO taskExt = taskExtMapper.selectByTaskId(taskId);
                    if (taskExt == null) {
                        throw exception(TASK_NO_EXIST);
                    }
                    BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(taskExt.getProcessInstanceId());
                    Map<String, Object> data = bpmProcessInstanceExtDO.getFormVariables();
                    List<Long> leaders =(List<Long>)data.get("mainLeader");
                    if (!Objects.isNull(data.get("manSignSeq")) && "1".equals(data.get("manSignSeq").toString())) {
                        List<Integer> leads  =(List<Integer>)data.get("mainLeader");
                        List<Long> lds = leads.stream().map(a->Long.valueOf(a)).collect(Collectors.toList());
                        List<AdminUserRespDTO> adminUserRespDTO = adminUserApi.getUsers(lds).getCheckedData();
                        List<String> names = adminUserRespDTO.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
                        AdminUserRespDTO user = adminUserRespDTO.get(0);
                        user.setNickname(String.join(",", names));
                        Map<String, Object> map = new HashMap<>();
                        map.put("taskName", AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getName());
                        map.put("taskDefKey", AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode());
                        map.put("user", user);
                        return map;
                    }
                    Long value = Long.valueOf(String.valueOf(leaders.get(0)));
                    AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(value).getCheckedData();
                    Map<String, Object> map = new HashMap<>();
                    map.put("taskName", AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getName());
                    map.put("taskDefKey", AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode());
                    map.put("user", adminUserRespDTO);
                    return map;
                }
                return null;
            }else  if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(processDefinition.getKey()) &&
                    AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getCode().equals(task.getTaskDefinitionKey())
            ) {
                //这个1代表特殊驳回
                if ("1".equals(condition)) {
                    nextTaskDef = AffairTaskNameCodeEnum.WEB_APPROVE.getCode();
                    nextTaskName = AffairTaskNameCodeEnum.WEB_APPROVE.getName();
                }else {
                    return null;
                }

            }
            else if(AffairProcessNameTypeEnum.VEHICLE.getType().equals(processDefinition.getKey()) &&
                    AffairTaskNameCodeEnum.VEHICLE_LEADER_APPROVE.getCode().equals(task.getTaskDefinitionKey())){
                if ("1".equals(condition)) {
                    nextTaskDef = AffairTaskNameCodeEnum.VEHICLE_MAIN_APPROVE.getCode();
                    nextTaskName = AffairTaskNameCodeEnum.VEHICLE_MAIN_APPROVE.getName();
                }
            }  else if(AffairProcessNameTypeEnum.WEB_POST.getType().equals(processDefinition.getKey()) &&
                    AffairTaskNameCodeEnum.WEB_MAIN_F.getCode().equals(task.getTaskDefinitionKey())) {
                if ("1".equals(condition)) {
                    nextTaskDef = AffairTaskNameCodeEnum.WEB_HEAD_SIGN.getCode();
                    nextTaskName = AffairTaskNameCodeEnum.WEB_HEAD_SIGN.getName();
                }
            } else if ((AffairProcessNameTypeEnum.CONTRACT_1.getType().equals(processDefinition.getKey())
                        || AffairProcessNameTypeEnum.CONTRACT_2.getType().equals(processDefinition.getKey()))
                    && (AffairTaskNameCodeEnum.CONTRACT_1_BRANCH_LEADER.getCode().equals(task.getTaskDefinitionKey())
                        || AffairTaskNameCodeEnum.CONTRACT_2_BRANCH_LEADER.getCode().equals(task.getTaskDefinitionKey()))){

                BpmTaskExtDO taskExt = taskExtMapper.selectByTaskId(taskId);
                if (taskExt == null) {
                    throw exception(TASK_NO_EXIST);
                }
                BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(taskExt.getProcessInstanceId());
                condition = bpmProcessInstanceExtDO.getFormVariables().get("needMainLeader").toString();
                if ("1".equals(condition)){
                    if (AffairTaskNameCodeEnum.CONTRACT_1_BRANCH_LEADER.getCode().equals(task.getTaskDefinitionKey())){
                        nextTaskDef = AffairTaskNameCodeEnum.CONTRACT_1_MAIN_LEADER.getCode();
                        nextTaskName = AffairTaskNameCodeEnum.CONTRACT_1_MAIN_LEADER.getName();
                    }
                    else if (AffairTaskNameCodeEnum.CONTRACT_2_BRANCH_LEADER.getCode().equals(task.getTaskDefinitionKey())){
                        nextTaskDef = AffairTaskNameCodeEnum.CONTRACT_2_MAIN_LEADER.getCode();
                        nextTaskName = AffairTaskNameCodeEnum.CONTRACT_2_MAIN_LEADER.getName();
                    }
                }
            }
            else {
                BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
                ArrayList<UserTask> userTaskList = new ArrayList<>();
                getNextUserTaskList(userTaskList, bpmnModel, task.getTaskDefinitionKey());
                if (CollUtil.isNotEmpty(userTaskList)) {
                    nextTaskDef = userTaskList.get(0).getId();
                    nextTaskName = userTaskList.get(0).getName();

                }
            }
            // 获取所有节点信息

        } else  if ("1".equals(type) && StringUtils.isEmpty(taskId)) {
            // 获取流程定义信息
            ProcessDefinition processDefinition = bpmProcessDefinitionService.getActiveProcessDefinition(proDefinitionKey);
            // 获取所有节点信息
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            ArrayList<UserTask> userTaskList = new ArrayList<>();
            getAllUserTaskList(userTaskList, bpmnModel);
            if (CollUtil.isNotEmpty(userTaskList) && userTaskList.size() > 1) {
                nextTaskDef = userTaskList.get(1).getId();
                nextTaskName = userTaskList.get(1).getName();
            }
        } else if("3".equals(type) ) {
            // 顺序
            BpmTaskExtDO taskExt = taskExtMapper.selectByTaskId(taskId);
            if (taskExt == null) {
                throw exception(TASK_NO_EXIST);
            }
            BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(taskExt.getProcessInstanceId());
            Map<String, Object> data = bpmProcessInstanceExtDO.getFormVariables();
            List<Long> leaders =(List<Long>)data.get("approvals");
            //网站发文主要领导签发的顺序当condition为1时直接返回不进行下一步，因为1代表进行特殊驳回
            if ( !(condition.equals("1") && AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode().equals(taskExt.getTaskDefKey()))
                    && leaders.indexOf(getLoginUserId().intValue()) < leaders.size() -1) {
                int i = leaders.indexOf(getLoginUserId().intValue()) + 1;
                Long value = Long.valueOf(String.valueOf(leaders.get(i)));
                AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(value).getCheckedData();
                Map<String, Object> map = new HashMap<>();
                map.put("taskName", taskExt.getName());
                map.put("taskDefKey", taskExt.getTaskDefKey());
                map.put("user", adminUserRespDTO);
                return map;
            }  else {
                // 当前任务 task
                Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
                // 获取流程定义信息
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
                if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(processDefinition.getKey())) {
                    if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(processDefinition.getKey()) &&
                            AffairTaskNameCodeEnum.REQ_PROPOSE_1.getCode().equals(task.getTaskDefinitionKey())) {
                        nextTaskDef = AffairTaskNameCodeEnum.REQ_PROPOSE_SUM.getCode();
                        nextTaskName = AffairTaskNameCodeEnum.REQ_PROPOSE_SUM.getName();
                    }
                    if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(processDefinition.getKey()) &&
                            AffairTaskNameCodeEnum.REQ_INSTRUCT_1.getCode().equals(task.getTaskDefinitionKey())) {
                        nextTaskDef = AffairTaskNameCodeEnum.REQ_INSTRUCT_SUM.getCode();
                        nextTaskName = AffairTaskNameCodeEnum.REQ_INSTRUCT_SUM.getName();
                    }
                    if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(processDefinition.getKey()) &&
                            AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode().equals(task.getTaskDefinitionKey())) {
                        nextTaskDef = AffairTaskNameCodeEnum.REQ_INSTRUCT_SUM.getCode();
                        nextTaskName = AffairTaskNameCodeEnum.REQ_INSTRUCT_SUM.getName();
                    }
                } else if (AffairProcessNameTypeEnum.METTING_APPLY.getType().equals(processDefinition.getKey())) {
                    if (AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(task.getTaskDefinitionKey())
                            || AffairTaskNameCodeEnum.MEETING_APPLY_LEADER_APPROVE_1.getCode().equals(task.getTaskDefinitionKey())
                            || AffairTaskNameCodeEnum.MEETING_APPLY_LEADER_APPROVE_2.getCode().equals(task.getTaskDefinitionKey())) {
                        nextTaskDef = AffairTaskNameCodeEnum.MEETING_APPLY_APPLICANT_APPROVE.getCode();
                        nextTaskName = AffairTaskNameCodeEnum.MEETING_APPLY_APPLICANT_APPROVE.getName();
                    }
                } else if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(processDefinition.getKey())){
                    if (AffairTaskNameCodeEnum.WEB_LEADER_SIGN_SEQ.getCode().equals(task.getTaskDefinitionKey())) {
                        //需不需要主要领导签发
                        if(data.get("linkManSign").toString().equals("1"))
                        {
                            List<Long> mainLeader =(List<Long>)data.get("mainLeader");
                            if (!Objects.isNull(data.get("manSignSeq")) && "1".equals(data.get("manSignSeq").toString())) {
                                List<Integer> leads  =(List<Integer>)data.get("mainLeader");
                                List<Long> lds = leads.stream().map(a->Long.valueOf(a)).collect(Collectors.toList());
                                List<AdminUserRespDTO> adminUserRespDTO = adminUserApi.getUsers(lds).getCheckedData();
                                List<String> names = adminUserRespDTO.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
                                AdminUserRespDTO user = adminUserRespDTO.get(0);
                                user.setNickname(String.join(",", names));
                                Map<String, Object> map = new HashMap<>();
                                map.put("taskName", AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getName());
                                map.put("taskDefKey", AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getCode());
                                map.put("user", user);
                                return map;
                            }
                            Long value = Long.valueOf(String.valueOf(mainLeader.get(0)));
                            AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(value).getCheckedData();
                            Map<String, Object> map = new HashMap<>();
                            map.put("taskName", AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getName());
                            map.put("taskDefKey", AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode());
                            map.put("user", adminUserRespDTO);
                            return map;
                        } else {

                    }
                }else if (AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode().equals(task.getTaskDefinitionKey())) {
                        if("1".equals(condition))
                        {
                            nextTaskDef = AffairTaskNameCodeEnum.WEB_APPROVE.getCode();
                            nextTaskName = AffairTaskNameCodeEnum.WEB_APPROVE.getName();
                        } else {
                            return null;
                        }
                    }
                }else {
                    return null;
                }
            }
        } else {
            // 获取流程定义信息
            ProcessDefinition processDefinitionF = bpmProcessDefinitionService.getActiveProcessDefinition(proDefinitionKey);
            // 获取所有节点信息
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionF.getId());
            ArrayList<UserTask> userTaskList = new ArrayList<>();
            getAllUserTaskList(userTaskList, bpmnModel);
            // 驳回
            if (taskService.createTaskQuery().taskId(taskId).singleResult().isSuspended()) {
                throw exception(TASK_ASSIGN_HANNGUP);
            }
            // 当前任务 task
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            // 获取流程定义信息
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
            // 获取所有节点信息
            Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
            // 获取全部节点列表，包含子节点
            Collection<FlowElement> allElements = FlowableUtils.getAllElements(process.getFlowElements(), null);
            // 获取当前任务节点元素
            FlowElement source = null;
            if (allElements != null) {
                for (FlowElement flowElement : allElements) {
                    // 类型为用户节点
                    if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                        // 获取节点信息
                        source = flowElement;
                    }
                }
            }
            // 目的获取所有跳转到的节点 targetIds
            // 获取当前节点的所有父级用户任务节点
            // 深度优先算法思想：延边迭代深入
            List<UserTask> parentUserTaskList = FlowableUtils.iteratorFindParentUserTasks(source, null, null);
            if (parentUserTaskList == null || parentUserTaskList.size() == 0) {
                throw exception(TASK_ASSIGN_INITIAL);
            }
            // 获取活动 ID 即节点 Key
            List<String> parentUserTaskKeyList = new ArrayList<>();
            parentUserTaskList.forEach(item -> parentUserTaskKeyList.add(item.getId()));
            // 获取全部历史节点活动实例，即已经走过的节点历史，数据采用开始时间升序
            List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(task.getProcessInstanceId()).orderByHistoricTaskInstanceStartTime().asc().list();
            // 数据清洗，将回滚导致的脏数据清洗掉
            List<String> lastHistoricTaskInstanceList = FlowableUtils.historicTaskInstanceClean(allElements, historicTaskInstanceList);
            // 此时历史任务实例为倒序，获取最后走的节点
            List<String> targetIds = new ArrayList<>();
            // 循环结束标识，遇到当前目标节点的次数
            int number = 0;
            StringBuilder parentHistoricTaskKey = new StringBuilder();
            for (String historicTaskInstanceKey : lastHistoricTaskInstanceList) {
                // 当会签时候会出现特殊的，连续都是同一个节点历史数据的情况，这种时候跳过
                if (parentHistoricTaskKey.toString().equals(historicTaskInstanceKey)) {
                    continue;
                }
                parentHistoricTaskKey = new StringBuilder(historicTaskInstanceKey);
                if (historicTaskInstanceKey.equals(task.getTaskDefinitionKey())) {
                    number++;
                }
                // 在数据清洗后，历史节点就是唯一一条从起始到当前节点的历史记录，理论上每个点只会出现一次
                // 在流程中如果出现循环，那么每次循环中间的点也只会出现一次，再出现就是下次循环
                // number == 1，第一次遇到当前节点
                // number == 2，第二次遇到，代表最后一次的循环范围
                if (number == 2) {
                    break;
                }
                // 如果当前历史节点，属于父级的节点，说明最后一次经过了这个点，需要退回这个点
                if (parentUserTaskKeyList.contains(historicTaskInstanceKey)) {
                    targetIds.add(historicTaskInstanceKey);
                }
            }
            nextTaskDef = targetIds.get(0);
            if (StringUtils.isNotEmpty(nextTaskDef) && CollUtil.isNotEmpty(userTaskList)) {
                List<UserTask> userTasks = userTaskList.stream().filter(userTask -> userTask.getId().equals(targetIds.get(0))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(userTasks)) {
                    nextTaskName =  userTasks.get(0).getName();
                }
            }
            if (userTaskList.get(0).getId().equals(nextTaskDef)) {
                Map<String, Object> map = new HashMap<>();
                BpmTaskExtDO taskExtDO = taskExtMapper.selectByTaskId(taskId);
                if (taskExtDO != null) {
                    BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(taskExtDO.getProcessInstanceId());
                    AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(bpmProcessInstanceExtDO.getStartUserId()).getCheckedData();
                    map.put("taskName", nextTaskName);
                    map.put("taskDefKey", nextTaskDef);
                    map.put("user", adminUserRespDTO);
                }
                return map;
            }

        }
        ProcessDefinition processDefinition = bpmProcessDefinitionService.getActiveProcessDefinition(proDefinitionKey);
        List<BpmTaskAssignRuleDO>  doList = bpmTaskAssignRuleService.getTaskAssignRuleListByProcessDefinitionId(processDefinition.getId(), nextTaskDef);
        if (CollUtil.isEmpty(doList)) {
            return null;
        }
        Set<Long> userIds = null;
//        userIds = bpmTaskAssignRuleService.calculateTaskCandidateUsersByRule(doList.get(0));
        if (StringUtils.isBlank(taskId)) {
            userIds = bpmTaskAssignRuleService.calculateTaskCandidateUsersByRule1(getLoginUserId(), doList.get(0));
        } else {
            String processInstanceId = taskExtMapper.selectByTaskId(taskId).getProcessInstanceId();
            if (StringUtils.isNotBlank(processInstanceId)) {
                BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);
                if (bpmProcessInstanceExtDO != null) {
                    userIds = bpmTaskAssignRuleService.calculateTaskCandidateUsersByRule1(bpmProcessInstanceExtDO.getStartUserId(), doList.get(0));
                } else {
                    throw exception(PROCESS_INSTANCE_NOT_EXISTS);
                }
            }
        }
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userIds.iterator().next()).getCheckedData();
        Map<String, Object> map = new HashMap<>();
        map.put("taskName", nextTaskName);
        map.put("taskDefKey", nextTaskDef);
        map.put("user", adminUserRespDTO);
        return map;
    }

    @Override
    public void approveTaskAndComment(Long userId, BpmTaskApproveReqVO reqVO) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(reqVO.getProcessInstanceId());
        Map<String, Object> map = bpmProcessInstanceExtDO.getFormVariables();
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("approvaTime", new Date());
        jsonObject.put("approvalUser", adminUserRespDTO.getNickname());
        jsonObject.put("approvalId", String.valueOf(adminUserRespDTO.getId()));
        jsonObject.put("reason", reqVO.getReason());
        if (StringUtils.isNotEmpty(reqVO.getImageUrl()) && !reqVO.getImageUrl().startsWith("http")) {
            String base64 = reqVO.getImageUrl().substring(reqVO.getImageUrl().indexOf(",", 1) + 1);
            // 解密，解密的结果是一个byte数组
//            Base64.Decoder decoder = Base64.getMimeDecoder();
//            byte[] imgbytes = decoder.decode(base64);
            byte[] imgbytes = Base64Decoder.decode(base64);
            for (int i = 0; i < imgbytes.length; ++i) {
                if (imgbytes[i] < 0) {
                    imgbytes[i] += 256;
                }
            }
            String fileName = UUID.randomUUID().toString().replace("-","") +".png";
            String url =fileApi.createFile(fileName, imgbytes);
            jsonObject.put("imageUrl", url);
            reqVO.setImageUrl(url);
        } else {
            jsonObject.put("imageUrl", reqVO.getImageUrl());
        }
        Map<String, Object>  reqVarible = reqVO.getVariables();
        if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.WEB_CHECK.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("webCheck"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("webCheck"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("webCheck", jsonArray);
            } else if (AffairTaskNameCodeEnum.WEB_APPROVE.getCode().equals(reqVO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_APPROVE_MAIN.getCode().equals(reqVO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_HEAD_SIGN.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("webApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("webApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("webApprove", jsonArray);
            } else if(AffairTaskNameCodeEnum.WEB_LEADER_SIGN.getCode().equals(reqVO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_LEADER_SIGN_SEQ.getCode().equals(reqVO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getCode().equals(reqVO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode().equals(reqVO.getTaskDefKey()) ) {
                if (!Objects.isNull(map.get("webLeader"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("webLeader"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("webLeader", jsonArray);
            }
            if (!Objects.isNull(reqVarible.get("approvalNames"))) {
                map.put("approvalNames", reqVarible.get("approvalNames"));
            }
            if (!Objects.isNull(reqVarible.get("approvals"))) {
                map.put("approvals", reqVarible.get("approvals"));
            }
            if (!Objects.isNull(reqVarible.get("linkManSign"))) {
                if (StringUtils.isEmpty(reqVarible.get("linkManSign").toString())) {
                    map.put("linkManSign", 0L);
                } else {
                    map.put("linkManSign", reqVarible.get("linkManSign"));
                }
            }
            if (!Objects.isNull(reqVarible.get("linkMan"))) {
                map.put("linkMan", reqVarible.get("linkMan"));
            }
            if (!Objects.isNull(reqVarible.get("manSignSeq"))) {
                if (StringUtils.isEmpty(reqVarible.get("manSignSeq").toString())) {
                    map.put("manSignSeq", 0L);
                } else {
                    map.put("manSignSeq", reqVarible.get("manSignSeq"));
                }
            }

        }
        if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.REQ_PROPOSE_1.getCode().equals(reqVO.getTaskDefKey())
                    || AffairTaskNameCodeEnum.REQ_PROPOSE_2.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("reqPropose"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("reqPropose"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("reqPropose", jsonArray);
            } else if (AffairTaskNameCodeEnum.REQ_INSTRUCT_1.getCode().equals(reqVO.getTaskDefKey())
                    || AffairTaskNameCodeEnum.REQ_INSTRUCT_2.getCode().equals(reqVO.getTaskDefKey()) ) {
                if (!Objects.isNull(map.get("reqInstruct"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("reqInstruct"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("reqInstruct", jsonArray);
            }
            if (!Objects.isNull(reqVarible.get("conditionApproval"))) {
                map.put("conditionApproval", reqVarible.get("conditionApproval"));
            }
            if (!Objects.isNull(reqVarible.get("approvals"))) {
                map.put("approvals", reqVarible.get("approvals"));
            }
            if (!Objects.isNull(reqVarible.get("conditionComment"))) {
                map.put("conditionComment", reqVarible.get("conditionComment"));
            }
            if (!Objects.isNull(reqVarible.get("conditionApprovalseq"))) {
                map.put("conditionApprovalseq", reqVarible.get("conditionApprovalseq"));
            }
            if (!Objects.isNull(reqVarible.get("conditionTwoApproval"))) {
                map.put("conditionTwoApproval", reqVarible.get("conditionTwoApproval"));
            }
        }
        if (AffairProcessNameTypeEnum.MEETING_RESERVE.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.MEETING_RESERVE_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("meetingReserveOfficeApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("meetingReserveOfficeApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("meetingReserveOfficeApprove", jsonArray);
                /**
                 * [{"meetingRoomId":1,"startTime":"2023-03-30 12:12:12","endTime":"2023-03-30 12:12:12"}]
                 */
                if (!Objects.isNull(reqVarible.get("meetingRooms"))) {
//                    map.put("meetingRooms", reqVarible.get("meetingRooms"));

                    getMeetingRoomInfos(map, reqVarible);
                }

                map.put("meetingStatus", "2");
                map.put("applyUserId", bpmProcessInstanceExtDO.getStartUserId());
                meetingReserveUpdate(reqVO, map, AffairProcessNameTypeEnum.MEETING_RESERVE.getType());
            }
        }
        if (AffairProcessNameTypeEnum.METTING_APPLY.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!map.containsKey("classified")) {
                map.put("classified", "0");
            }
            if (AffairTaskNameCodeEnum.MEETING_APPLY_LEADER_APPROVE_1.getCode().equals(reqVO.getTaskDefKey()) || AffairTaskNameCodeEnum.MEETING_APPLY_LEADER_APPROVE_2.getCode().equals(reqVO.getTaskDefKey())) {
//            if (AffairTaskNameCodeEnum.MEETING_APPLY_CHARGE_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("meetingApplyChargeApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("meetingApplyChargeApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("meetingApplyChargeApprove", jsonArray);
            } else if (AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("meetingApplyOfficeApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("meetingApplyOfficeApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("meetingApplyOfficeApprove", jsonArray);
                /**
                 * [{"meetingRoomId":1,"startTime":"2023-03-30 12:12:12","endTime":"2023-03-30 12:12:12"}]
                 */
                if (!Objects.isNull(reqVarible.get("meetingRooms"))) {
//                    map.put("meetingRooms", reqVarible.get("meetingRooms"));

                    getMeetingRoomInfos(map, reqVarible);
                }

//                map.put("meetingStatus", "2");
//                map.put("applyUserId", bpmProcessInstanceExtDO.getStartUserId());
//                meetingReserveUpdate(reqVO, map, AffairProcessNameTypeEnum.METTING_APPLY.getType());

            } else if (AffairTaskNameCodeEnum.MEETING_APPLY_APPLICANT_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (reqVarible.containsKey("conditionOne") && !reqVarible.get("conditionOne").equals(2L)) {
                    Long conditionOne = Long.parseLong(reqVarible.get("conditionOne").toString());
                    map.put("conditionOne", conditionOne);

                    if (Objects.nonNull(reqVarible.get("approvals"))) {
                        List<Long> approvals = (List<Long>) reqVarible.get("approvals");
                        map.put("approvals", approvals);
                    }
                    if (conditionOne.equals(1L) && Objects.nonNull(reqVarible.get("conditionTwo"))) {
                        Long conditionTwo = Long.parseLong(reqVarible.get("conditionTwo").toString());
                        map.put("conditionTwo", conditionTwo);
                    }
                } else {
                    map.put("meetingStatus", "2");
                    map.put("applyUserId", bpmProcessInstanceExtDO.getStartUserId());
                    meetingReserveUpdate(reqVO, map, AffairProcessNameTypeEnum.METTING_APPLY.getType());
                }
            }
        }
        if (AffairProcessNameTypeEnum.VEHICLE.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.VEHICLE_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("vehicleOfficeApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("vehicleOfficeApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("vehicleOfficeApprove", jsonArray);

                if (!Objects.isNull(reqVarible.get("driver"))) {
                    map.put("driver", reqVarible.get("driver"));
                }
                if (!Objects.isNull(reqVarible.get("checkedTypes"))) {
                    map.put("checkedTypes", reqVarible.get("checkedTypes"));
                }
                if (!Objects.isNull(reqVarible.get("otherType"))) {
                    map.put("otherType", reqVarible.get("otherType"));
                }
                if (!Objects.isNull(reqVarible.get("carNumber"))) {
                    map.put("carNumber", reqVarible.get("carNumber"));
                }
                if (!Objects.isNull(reqVarible.get("checkedCharacters"))) {
                    map.put("checkedCharacters", reqVarible.get("checkedCharacters"));
                }
                if (!Objects.isNull(reqVarible.get("otherCharacter"))) {
                    map.put("otherCharacter", reqVarible.get("otherCharacter"));
                }
            } else if (AffairTaskNameCodeEnum.VEHICLE_LEADER_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("vehicleLeaderApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("vehicleLeaderApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("vehicleLeaderApprove", jsonArray);
            } else if (AffairTaskNameCodeEnum.VEHICLE_MAIN_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("vehicleMainApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("vehicleMainApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("vehicleMainApprove", jsonArray);
            }
        }
        if (AffairProcessNameTypeEnum.SEAL.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.SEAL_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("sealApprove"))) {
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("sealApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("sealApprove", jsonArray);
            }
        }
        if(AffairProcessNameTypeEnum.CONTRACT_1.getType().equals(bpmProcessInstanceExtDO.getProcessKey())){
            if(AffairTaskNameCodeEnum.CONTRACT_1_ECON_APPROVE.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("econApprove"))){
                    jsonArray = (JSONArray)JSONObject.toJSON(map.get("econApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("econApprove",jsonArray);
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_1_LAW_DEPT_APPROVE.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("lawDeptApprove"))){
                    jsonArray = (JSONArray)JSONObject.toJSON(map.get("lawDeptApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("lawDeptApprove",jsonArray);
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_1_BRANCH_LEADER.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("branchLeaderApprove"))){
                    jsonArray = (JSONArray)JSONObject.toJSON(map.get("branchLeaderApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("branchLeaderApprove",jsonArray);
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_1_MAIN_LEADER.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("mainLeaderApprove"))){
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("mainLeaderApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("mainLeaderApprove",jsonArray);
            }
        }
        if(AffairProcessNameTypeEnum.CONTRACT_2.getType().equals(bpmProcessInstanceExtDO.getProcessKey())){
            if(AffairTaskNameCodeEnum.CONTRACT_2_ECON_APPROVE.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("econApprove"))){
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("econApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("econApprove",jsonArray);
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_2_BRANCH_LEADER.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("branchLeaderApprove"))){
                    jsonArray =(JSONArray)JSONObject.toJSON(map.get("branchLeaderApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("branchLeaderApprove",jsonArray);
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_2_MAIN_LEADER.getCode().equals(reqVO.getTaskDefKey())){
                if (!Objects.isNull(map.get("mainLeaderApprove"))){
                    jsonArray = (JSONArray)JSONObject.toJSON(map.get("mainLeaderApprove"));
                    jsonArray = dealMutilp(jsonArray,adminUserRespDTO.getNickname());
                }
                jsonArray.add(jsonObject);
                map.put("mainLeaderApprove",jsonArray);
            }
        }

//        if (AffairProcessTypeEnum.LEAVE.getProcessType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
//            bpmOALeaveService.updateResult(bpmProcessInstanceExtDO.getProcessInstanceId(),bpmProcessInstanceExtDO.getResult());
//        }
        // TODO 其他流程节点审批意见
        bpmProcessInstanceExtDO.setFormVariables(map);
        processInstanceExtMapper.updateByProcessInstanceId(bpmProcessInstanceExtDO);

    }

    private void getMeetingRoomInfos(Map<String, Object> map, Map<String, Object> reqVarible) {
        List<Map<String, Object>> meetingRoomInfos =new ArrayList<Map<String, Object>>();

        String meetingRoomPrefix = "meetingRooms:";

        if (Objects.nonNull(map.get("meetingRooms"))) {

            String oldMeetingRoomsString = JSON.toJSONString(map.get("meetingRooms"));
            List<Map<String, Object>> oldMeetingRoomList = JSON.parseObject(oldMeetingRoomsString, new TypeReference<List<Map<String, Object>>>() {
            });

            for (Map<String, Object> oldMeetingRoom : oldMeetingRoomList) {
                String meetingRoomId = oldMeetingRoom.get("meetingRoomId").toString();
                String date = oldMeetingRoom.get("date").toString();
                String meetingRoomsKey = meetingRoomPrefix + date;
                List<String> toRemoveTimeRange = (List<String>) oldMeetingRoom.get("timeRange");

                HashOperations<String, String, List<String>> hashOperations = redisTemplate.opsForHash();
                List<String> timeRange = hashOperations.get(meetingRoomsKey, meetingRoomId);

                if (timeRange != null && !timeRange.isEmpty()) {
                    timeRange = timeRange.stream().filter(slot -> !toRemoveTimeRange.contains(slot)).collect(Collectors.toList());
                    hashOperations.put(meetingRoomsKey, meetingRoomId, timeRange);
                }
            }

        }
        //  上一步先更新完redis中的数据，再给map赋值meetingRooms
        map.put("meetingRooms", reqVarible.get("meetingRooms"));

        Object meetingRoomsObject = reqVarible.get("meetingRooms");
        String meetingRoomsString = JSON.toJSONString(meetingRoomsObject);
        List<Map<String, Object>> meetingRoomList = JSON.parseObject(meetingRoomsString, new TypeReference<List<Map<String, Object>>>() {});

        SimpleDateFormat sdf2mm = new SimpleDateFormat(DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        SimpleDateFormat sdf2ss = new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy年M月d日");
        SimpleDateFormat sdf2 = new SimpleDateFormat("HH:mm");

        for (Map<String, Object> meetingRoom : meetingRoomList) {

            String name = meetingRoom.get("name").toString();
            Long meetingRoomId = Long.parseLong(meetingRoom.get("meetingRoomId").toString());
            String date = meetingRoom.get("date").toString();
            /*String[] timeStrs = meetingRoom.get("timeStr").toString().split(",");
            for (String timeStr : timeStrs) {
                Map<String, Object> meetingRoomInfo = new HashMap<>();
                StringBuilder sb = new StringBuilder();

                String room_time = sb.append(name).append(" ").append(timeStr).toString();
                meetingRoomInfo.put("meetingRoomId", meetingRoomId);
                meetingRoomInfo.put("name", name);
                meetingRoomInfo.put("room-time", room_time);
                meetingRoomInfos.add(meetingRoomInfo);
            }*/

            List<String> timeRangeList = (List<String>) meetingRoom.get("timeRange");

            Collections.sort(timeRangeList);        //  选择的会议室的时间排序

            String meetingRoomsKey = meetingRoomPrefix + date;
            HashOperations<String, String, List<String>> hashOperations = redisTemplate.opsForHash();
            if (!hashOperations.hasKey(meetingRoomsKey, meetingRoomId.toString())) {
                // 在事务中创建新的Redis键值对
                redisTemplate.execute(new SessionCallback<Object>() {
                    @Override
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        operations.multi(); // 开启事务

                        // 添加新的键值对
                        hashOperations.put(meetingRoomsKey, meetingRoomId.toString(), new ArrayList<String>());

                        operations.exec(); // 提交事务
                        return null;
                    }
                });
                hashOperations.put(meetingRoomsKey, meetingRoomId.toString(), timeRangeList);

            } else {
                List<String> timeRange = hashOperations.get(meetingRoomsKey, meetingRoomId.toString());

                timeRange.addAll(timeRangeList);
                hashOperations.put(meetingRoomsKey, meetingRoomId.toString(), timeRange);
            }


            String startTimeString = timeRangeList.get(0).substring(0, 5);
            String endTimeString = timeRangeList.get(0).substring(6);

            int flag = 0;   //  最后一个时间段
            if (timeRangeList.size() == 1) {    //  选择的会议时间段只有1段
                flag = 1;
            }

            for (int i = 0; i < timeRangeList.size() - 1; i++) {
                if (i != timeRangeList.size() - 2) {
                    if (endTimeString.equals(timeRangeList.get(i+1).substring(0, 5))) {
                        endTimeString = timeRangeList.get(i+1).substring(6);
                        continue;
                    }
                } else {
                    if (endTimeString.equals(timeRangeList.get(i+1).substring(0, 5))) {
                        endTimeString = timeRangeList.get(i+1).substring(6);
                    } else {
                        flag = 1;
                    }
                }
                startTimeString = new StringBuilder().append(date).append(" ").append(startTimeString).toString();
                endTimeString = new StringBuilder().append(date).append(" ").append(endTimeString).toString();
                try {
                    Date startTime = sdf2ss.parse(sdf2ss.format(sdf2mm.parse(startTimeString)));
                    Date endTime = sdf2ss.parse(sdf2ss.format(sdf2mm.parse(endTimeString)));
                    String date1 = sdf1.format(startTime);
                    String time1 = sdf2.format(startTime);
                    String time2 = sdf2.format(endTime);
                    String time = new StringBuilder().append(time1).append("~").append(time2).toString();
                    Map<String, Object> meetingRoomInfo = new HashMap<>();
                    meetingRoomInfo.put("date", date1);
                    meetingRoomInfo.put("time", time);
                    meetingRoomInfo.put("meetingRoomId", meetingRoomId);
                    meetingRoomInfo.put("name", name);
                    //meetingRoomInfo.put("date", date);
                    meetingRoomInfo.put("startTime", startTime);
                    meetingRoomInfo.put("endTime", endTime);

                    meetingRoomInfos.add(meetingRoomInfo);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                startTimeString = timeRangeList.get(i+1).substring(0, 5);
                endTimeString = timeRangeList.get(i+1).substring(6);
            }

            if (flag == 1) {
                startTimeString = timeRangeList.get(timeRangeList.size() - 1).substring(0, 5);
                endTimeString = timeRangeList.get(timeRangeList.size() - 1).substring(6);
                startTimeString = new StringBuilder().append(date).append(" ").append(startTimeString).toString();
                endTimeString = new StringBuilder().append(date).append(" ").append(endTimeString).toString();
                try {
                    Date startTime = sdf2ss.parse(sdf2ss.format(sdf2mm.parse(startTimeString)));
                    Date endTime = sdf2ss.parse(sdf2ss.format(sdf2mm.parse(endTimeString)));
                    String date1 = sdf1.format(startTime);
                    String time1 = sdf2.format(startTime);
                    String time2 = sdf2.format(endTime);
                    String time = new StringBuilder().append(time1).append("~").append(time2).toString();
                    Map<String, Object> meetingRoomInfo = new HashMap<>();
                    meetingRoomInfo.put("date", date1);
                    meetingRoomInfo.put("time", time);
                    meetingRoomInfo.put("meetingRoomId", meetingRoomId);
                    meetingRoomInfo.put("name", name);
                    //meetingRoomInfo.put("date", date);
                    meetingRoomInfo.put("startTime", startTime);
                    meetingRoomInfo.put("endTime", endTime);

                    meetingRoomInfos.add(meetingRoomInfo);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        map.put("meetingRoomInfos", meetingRoomInfos);

        JSONArray jsonArray = JSONArray.parseArray(meetingRoomsString);
        List<LocalDateTime> startTimes = new ArrayList<>();
        List<LocalDateTime> endTimes = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject meetingObj = jsonArray.getJSONObject(i);
            String date = meetingObj.getString("date");
            JSONArray timeRangeArray = meetingObj.getJSONArray("timeRange");

            for (int j = 0; j < timeRangeArray.size(); j++) {
                String timeRange = timeRangeArray.getString(j);
                String[] times = timeRange.split("-");
                startTimes.add(LocalDateTime.parse(date + " " + times[0], dateTimeFormatter));
                endTimes.add(LocalDateTime.parse(date + " " + times[1], dateTimeFormatter));
            }
        }

        startTimes.sort(Comparator.naturalOrder());
        endTimes.sort(Comparator.naturalOrder());

        LocalDateTime startDate = startTimes.get(0);
        LocalDateTime endDate = endTimes.get(endTimes.size() - 1);
        JSONArray times = new JSONArray();
        times.add(startDate.format(dateTimeFormatter));
        times.add(endDate.format(dateTimeFormatter));

        map.put("time", times);
    }


    @Override
    public void rejectTaskAndComment(BpmTaskVO reqVO) {
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(reqVO.getProcessInstanceId());
        Map<String, Object> map = bpmProcessInstanceExtDO.getFormVariables();
        // 驳回找到当前节点
        List<BpmTaskExtDO> bpmTaskExtDOs = taskExtMapper.selectByProcessInstanceId(reqVO.getProcessInstanceId());
        if (CollectionUtils.isAnyEmpty(bpmTaskExtDOs)) {
            return;
        }
        List<String> approvalIds = bpmTaskExtDOs.stream().map(bpmTaskExtDO -> String.valueOf(bpmTaskExtDO.getAssigneeUserId())).collect(Collectors.toList());
        BpmTaskExtDO bpmTaskExtDO = bpmTaskExtDOs.get(0);
        if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.WEB_CHECK.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("webCheck"))) {
                    map.remove("webCheck");
                }
            } else if (AffairTaskNameCodeEnum.WEB_APPROVE.getCode().equals(bpmTaskExtDO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_APPROVE_MAIN.getCode().equals(bpmTaskExtDO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_HEAD_SIGN.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("webApprove"))) {
                    JSONArray dataArray =(JSONArray)JSONObject.toJSON(map.get("webApprove"));
                    JSONArray  jsonArray = new JSONArray();
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject jsonObject = dataArray.getJSONObject(i);
                        if (!Objects.isNull(jsonObject.get("approvalId"))
                                && approvalIds.contains(jsonObject.get("approvalId").toString())) {
                              continue;
                        }
                        jsonArray.add(jsonObject);
                    }
                    map.put("webApprove", jsonArray);
                }
            } else if(AffairTaskNameCodeEnum.WEB_LEADER_SIGN.getCode().equals(bpmTaskExtDO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getCode().equals(bpmTaskExtDO.getTaskDefKey()) ||
                    AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode().equals(bpmTaskExtDO.getTaskDefKey()) ) {
                if (!Objects.isNull(map.get("webLeader"))) {
                    JSONArray dataArray =(JSONArray)JSONObject.toJSON(map.get("webLeader"));
                    JSONArray  jsonArray = new JSONArray();
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject jsonObject = dataArray.getJSONObject(i);
                        if (!Objects.isNull(jsonObject.get("approvalId"))
                                && approvalIds.contains(jsonObject.get("approvalId").toString())) {
                            continue;
                        }
                        jsonArray.add(jsonObject);
                    }
                    map.put("webLeader", jsonArray);
                }
            }
            if (!Objects.isNull(map.get("approvalNames"))) {
                map.remove("approvalNames");
            }
            if (!Objects.isNull(map.get("approvals"))) {
                map.remove("approvals");
            }
            if (!Objects.isNull(map.get("linkManSign"))) {
                map.remove("linkManSign");
            }
            if (!Objects.isNull(map.get("linkMan"))) {
                map.remove("linkMan");
            }
            if (!Objects.isNull(map.get("manSignSeq"))) {
                map.remove("manSignSeq");
            }
        }
        /*if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.REQ_INSTRUCT.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("reqPropose"))) {
                    map.remove("reqPropose");
                }
            }
        }*/
        if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.REQ_INSTRUCT_SUM.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("reqPropose"))) {
                    map.remove("reqPropose");
                }
            }
        }
        if (AffairProcessNameTypeEnum.MEETING_RESERVE.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.MEETING_RESERVE_OFFICE_APPROVE.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("meetingReserveOfficeApprove"))) {
                    map.remove("meetingReserveOfficeApprove");
                }
            }
        }
        if (AffairProcessNameTypeEnum.METTING_APPLY.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("meetingApplyChargeApprove"))) {
                    map.remove("meetingApplyChargeApprove");
                }
            }
        }
        if (AffairProcessNameTypeEnum.VEHICLE.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.VEHICLE_LEADER_APPROVE.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("vehicleOfficeApprove"))) {
                    map.remove("vehicleOfficeApprove");
                }

                if (!Objects.isNull(map.get("driver"))) {
                    map.remove("driver");
                }
                if (!Objects.isNull(map.get("checkedTypes"))) {
                    map.remove("checkedTypes");
                }
                if (!Objects.isNull(map.get("otherType"))) {
                    map.remove("otherType");
                }
                if (!Objects.isNull(map.get("carNumber"))) {
                    map.remove("carNumber");
                }
                if (!Objects.isNull(map.get("checkedCharacters"))) {
                    map.remove("checkedCharacters");
                }
                if (!Objects.isNull(map.get("otherCharacter"))) {
                    map.remove("otherCharacter");
                }
            } else if (AffairTaskNameCodeEnum.VEHICLE_MAIN_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("vehicleLeaderApprove"))) {
                    map.remove("vehicleLeaderApprove");
                }
            }
        }
        if (AffairProcessNameTypeEnum.SEAL.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (AffairTaskNameCodeEnum.SEAL_APPROVE.getCode().equals(bpmTaskExtDO.getTaskDefKey())) {
                if (!Objects.isNull(map.get("sealApprove"))) {
                }
            }
        }
        if(AffairProcessNameTypeEnum.CONTRACT_1.getType().equals(bpmProcessInstanceExtDO.getProcessKey())){
            if(AffairTaskNameCodeEnum.CONTRACT_1_LAW_DEPT_APPROVE.getCode().equals(bpmTaskExtDO.getTaskDefKey())){
                if (!Objects.isNull(map.get("econApprove"))){
                    map.remove("econApprove");
                }
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_1_BRANCH_LEADER.getCode().equals(bpmTaskExtDO.getTaskDefKey())){
                if (!Objects.isNull(map.get("lawDeptApprove"))){
                    map.remove("lawDeptApprove");
                }
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_1_MAIN_LEADER.getCode().equals(bpmTaskExtDO.getTaskDefKey())){
                if (!Objects.isNull(map.get("branchLeaderApprove"))){
                    map.remove("branchLeaderApprove");
                }
            }
        }
        if(AffairProcessNameTypeEnum.CONTRACT_2.getType().equals(bpmProcessInstanceExtDO.getProcessKey())){
            if(AffairTaskNameCodeEnum.CONTRACT_2_BRANCH_LEADER.getCode().equals(bpmTaskExtDO.getTaskDefKey())){
                if (!Objects.isNull(map.get("econApprove"))){
                    map.remove("econApprove");
                }
            }
            else if(AffairTaskNameCodeEnum.CONTRACT_2_MAIN_LEADER.getCode().equals(bpmTaskExtDO.getTaskDefKey())){
                if (!Objects.isNull(map.get("branchLeaderApprove"))){
                    map.remove("branchLeaderApprove");
                }
            }
        }

//        if (AffairProcessTypeEnum.LEAVE.getProcessType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
//            bpmOALeaveService.updateResult(bpmProcessInstanceExtDO.getProcessInstanceId(),bpmProcessInstanceExtDO.getResult());
//        }
        // TODO 其他流程节点审批意见
        bpmProcessInstanceExtDO.setFormVariables(map);
        processInstanceExtMapper.updateByProcessInstanceId(bpmProcessInstanceExtDO);
    }

    @Override
    public void revokeTaskAndComment(BpmTaskVO reqVO) {
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(reqVO.getInstanceId());
        Map<String, Object> map = bpmProcessInstanceExtDO.getFormVariables();
        if (AffairProcessNameTypeEnum.WEB_POST.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!Objects.isNull(map.get("webCheck"))) {
                map.remove("webCheck");
            }
            if (!Objects.isNull(map.get("webApprove"))) {
                map.remove("webApprove");
            }
            if (!Objects.isNull(map.get("webLeader"))) {
                map.remove("webLeader");
            }

            if (!Objects.isNull(map.get("approvalNames"))) {
                map.remove("approvalNames");
            }
            if (!Objects.isNull(map.get("approvals"))) {
                map.remove("approvals");
            }
            if (!Objects.isNull(map.get("linkManSign"))) {
                map.remove("linkManSign");
            }
            if (!Objects.isNull(map.get("linkMan"))) {
                map.remove("linkMan");
            }
            if (!Objects.isNull(map.get("manSignSeq"))) {
                map.remove("manSignSeq");
            }

        }
        if (AffairProcessNameTypeEnum.REQ_INSTRUNCT.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!Objects.isNull(map.get("reqPropose"))) {
                map.remove("reqPropose");
            }
            if (!Objects.isNull(map.get("reqInstruct"))) {
                map.remove("reqInstruct");
            }
        }
        if (AffairProcessNameTypeEnum.MEETING_RESERVE.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!Objects.isNull(map.get("meetingReserveOfficeApprove"))) {
                map.remove("meetingReserveOfficeApprove");
            }
        }
        if (AffairProcessNameTypeEnum.METTING_APPLY.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!Objects.isNull(map.get("meetingApplyChargeApprove"))) {
                map.remove("meetingApplyChargeApprove");
            }
            if (!Objects.isNull(map.get("meetingApplyOfficeApprove"))) {
                map.remove("meetingApplyOfficeApprove");

                map.remove("selectedMeetingRoom");
            }
        }
        if (AffairProcessNameTypeEnum.VEHICLE.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!Objects.isNull(map.get("vehicleOfficeApprove"))) {
                map.remove("vehicleOfficeApprove");

                map.remove("driver");
                map.remove("checkedTypes");
                if (!Objects.isNull("otherType")) {
                    map.remove("otherType");
                }
                map.remove("carNumber");
                map.remove("checkedCharacters");
                if (!Objects.isNull("otherCharacter")) {
                    map.remove("otherCharacter");
                }
            }
            if (!Objects.isNull(map.get("vehicleLeaderApprove"))) {
                map.remove("vehicleLeaderApprove");
            }
            if (!Objects.isNull(map.get("vehicleMainApprove"))) {
                map.remove("vehicleMainApprove");
            }
        }
        if (AffairProcessNameTypeEnum.SEAL.getType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
            if (!Objects.isNull(map.get("sealApprove"))) {
                map.remove("sealApprove");
            }
        }
        if(AffairProcessNameTypeEnum.CONTRACT_1.getType().equals(bpmProcessInstanceExtDO.getProcessKey())){
            if (!Objects.isNull(map.get("econApprove"))){
                map.remove("econApprove");
            }
            if (!Objects.isNull(map.get("lawDeptApprove"))){
                map.remove("lawDeptApprove");
            }
            if (!Objects.isNull(map.get("branchLeaderApprove"))){
                map.remove("branchLeaderApprove");
            }
            if (!Objects.isNull(map.get("mainLeaderApprove"))){
                map.remove("mainLeaderApprove");
            }
        }
        if(AffairProcessNameTypeEnum.CONTRACT_2.getType().equals(bpmProcessInstanceExtDO.getProcessKey())){
            if (!Objects.isNull(map.get("econApprove"))){
                map.remove("econApprove");
            }
            if (!Objects.isNull(map.get("branchLeaderApprove"))){
                map.remove("branchLeaderApprove");
            }
            if (!Objects.isNull(map.get("mainLeaderApprove"))){
                map.remove("mainLeaderApprove");
            }
        }
        bpmProcessInstanceExtDO.setFormVariables(map);
        processInstanceExtMapper.updateByProcessInstanceId(bpmProcessInstanceExtDO);
    }

    @Override
    public void saveCreateHandleLog(Long userId, BpmProcessInstanceCreateReqVO reqVO,String instanceId) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
//        BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,instanceId);
        try {
            BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,instanceId);
            // 更新任务拓展表
            UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id",bpmTaskExtDTO.getTaskId());

            String logParam = this.saveGeneralLogInfo(adminUserRespDTO,null,BpmProcessInstanceResultEnum.APPROVE.getResult(),bpmTaskExtDTO.getName()).toJSONString();
            updateWrapper.set("log_parameters",logParam);
            AdminUserRespDTO userInfo = adminUserApi.getUser(userId).getCheckedData();
            if (userInfo != null
                    && !AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(bpmTaskExtDTO.getTaskDefKey())
                    && !AffairTaskNameCodeEnum.MEETING_RESERVE_OFFICE_APPROVE.getCode().equals(bpmTaskExtDTO.getTaskDefKey())) {
                updateWrapper.set("assignee_dept_id", userInfo.getDeptId());
            }
            if (AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(bpmTaskExtDTO.getTaskDefKey())
                    && AffairTaskNameCodeEnum.MEETING_RESERVE_OFFICE_APPROVE.getCode().equals(bpmTaskExtDTO.getTaskDefKey())
                    && reqVO.getVariables().containsKey("approvals")) {
                List<Integer> approvalsInt = (List<Integer>) reqVO.getVariables().get("approvals");
                List<Long> approvals = approvalsInt.stream().map(Integer::longValue).collect(Collectors.toList());
                updateWrapper.set("assignee_user_id", approvals.get(0));
                AdminUserRespDTO assigneeUserInfo = adminUserApi.getUser(approvals.get(0)).getCheckedData();
                if (assigneeUserInfo != null) {
                    updateWrapper.set("assignee_dept_id", assigneeUserInfo.getDeptId());
                }
                Task currentTask = taskService.createTaskQuery().processInstanceId(instanceId).singleResult();
                this.updateTaskAssignee(currentTask.getId(), approvals.get(0));
            }
            taskExtMapper.update(null,updateWrapper);
        }catch (Exception e){
            log.error("审批中心-发起流程-办理日志保存异常",e);
        }

        //正在进行的任务日志保存
        this.generateFollowLog(instanceId);
    }

    @Override
    public void saveCreateHandleLog1(Long userId, BpmProcessInstanceCreateReqVO reqVO, String instanceId) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
//        BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,instanceId);
        try {
            BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,instanceId);
            // 更新任务拓展表
            UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id",bpmTaskExtDTO.getTaskId());

            String logParam = this.saveGeneralLogInfo(adminUserRespDTO,null,BpmProcessInstanceResultEnum.APPROVE.getResult(),bpmTaskExtDTO.getName()).toJSONString();
            // 政务办理的数据同步时间
            if (!Objects.isNull(reqVO.getVariables().get("lastUpdatetime"))) {
                JSONObject jsonObject = JSONObject.parseObject(logParam);
                jsonObject.put("lastUpdateTime", reqVO.getVariables().get("lastUpdatetime"));
                logParam = JSONObject.toJSONString(jsonObject);
            }
            updateWrapper.set("log_parameters",logParam);
            AdminUserRespDTO userInfo = adminUserApi.getUser(userId).getCheckedData();
            if (userInfo != null) {
                updateWrapper.set("assignee_dept_id", userInfo.getDeptId());
            }
            taskExtMapper.update(null,updateWrapper);
        }catch (Exception e){
            log.error("审批中心-发起流程-办理日志保存异常",e);
        }
    }
    @Override
    public void saveApproveHandleLog(Long userId, BpmTaskApproveReqVO reqVO, BpmTaskExtDO taskExtDO) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(reqVO.getProcessInstanceId());
        //如果是发起节点，不需要更新发起节点的日志
        if (!AffairStartTaskCodeEnum.isStartTaskByCode(reqVO.getTaskDefKey())){
            // 更新任务拓展表
            UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id",reqVO.getId());
            //当前节点日志更新
            AffairTaskNameCodeEnum bpmTaskEnum = AffairTaskNameCodeEnum.getEnumByCode(reqVO.getTaskDefKey());
            String taskName = Objects.nonNull(bpmTaskEnum) ? bpmTaskEnum.getTaskShortName() : taskExtDO.getName();
            String logParam = this.saveApproveLogParam(adminUserRespDTO,reqVO,bpmProcessInstanceExtDO.getProcessKey(),BpmProcessInstanceResultEnum.APPROVE.getResult(),taskName);
            updateWrapper.set("log_parameters",logParam);
            taskExtMapper.update(null,updateWrapper);
        }
        //生产后续节点日志
        this.generateFollowLog(bpmProcessInstanceExtDO.getProcessInstanceId());
    }

    @Override
    public void saveReassignmentHandleLog(Long userId, BpmTaskUpdateAssigneeReqVO reqVO) {
        AdminUserRespDTO operatorInfo = adminUserApi.getUser(userId).getCheckedData();
        AdminUserRespDTO assignee = adminUserApi.getUser(reqVO.getAssigneeUserId()).getCheckedData();
        BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,reqVO.getProcessInstanceId());
        AffairTaskNameCodeEnum bpmTaskEnum = AffairTaskNameCodeEnum.getEnumByCode(bpmTaskExtDTO.getTaskDefKey());
        String taskName = Objects.nonNull(bpmTaskEnum) ? bpmTaskEnum.getTaskShortName() : bpmTaskExtDTO.getName();
        JSONObject jsonObject = this.saveGeneralLogInfo(operatorInfo,null,BpmProcessInstanceResultEnum.REASSIGNMENT.getResult(),taskName);

        //转派对象信息
        jsonObject.put("assignee",assignee);

        // 更新任务拓展表
        UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id",reqVO.getId());
        updateWrapper.set("log_parameters",jsonObject.toJSONString());
        taskExtMapper.update(null,updateWrapper);

        this.generateFollowLog(bpmTaskExtDTO.getProcessInstanceId());
    }

    @Override
    public void saveRejectHandleLog(Long userId, BpmTaskVO reqVO) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
        BpmTaskExtDO bpmTaskExtDO = taskExtMapper.selectByTaskId(reqVO.getTaskId());
        // 更新任务拓展表
        UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id",reqVO.getTaskId());
        AffairTaskNameCodeEnum bpmTaskEnum = AffairTaskNameCodeEnum.getEnumByCode(bpmTaskExtDO.getTaskDefKey());
        String taskName = Objects.nonNull(bpmTaskEnum) ? bpmTaskEnum.getTaskShortName() : bpmTaskExtDO.getName();
        String logParam = this.saveGeneralLogInfo(adminUserRespDTO,reqVO.getComment(),BpmProcessInstanceResultEnum.REJECT.getResult(),taskName).toJSONString();
        updateWrapper.set("log_parameters",logParam);
        updateWrapper.set("revoke_status","2");
        taskExtMapper.update(null,updateWrapper);

        this.generateFollowLog(bpmTaskExtDO.getProcessInstanceId());
    }

    @Override
    public void saveRevokeHandleLog(List<BpmTaskExtDO> bpmTaskExtDOS) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(getLoginUserId()).getCheckedData();
//        BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,reqVO.getInstanceId());
        // 更新任务拓展表
//        UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
//        updateWrapper.eq("task_id",reqVO.getTaskId());
//        String logParam = this.saveGeneralLogInfo(adminUserRespDTO,null,BpmProcessInstanceResultEnum.WITHDRAW.getResult(),bpmTaskExtDOS.getName()).toJSONString();
//        updateWrapper.set("log_parameters",logParam);
//        taskExtMapper.update(null,updateWrapper);

        bpmTaskExtDOS.forEach(bpmTaskExtDO -> {
            AdminUserRespDTO originOperator = adminUserApi.getUser(bpmTaskExtDO.getAssigneeUserId()).getCheckedData();
            AffairTaskNameCodeEnum bpmTaskEnum = AffairTaskNameCodeEnum.getEnumByCode(bpmTaskExtDO.getTaskDefKey());
            String taskName = Objects.nonNull(bpmTaskEnum) ? bpmTaskEnum.getTaskShortName() : bpmTaskExtDO.getName();
            JSONObject jsonObject = this.saveGeneralLogInfo(originOperator,null,BpmProcessInstanceResultEnum.WITHDRAW.getResult(),taskName);
            jsonObject.put("Canceller",adminUserRespDTO.getNickname());
            jsonObject.put("CancellerId",adminUserRespDTO.getId());
            DeptRespDTO cancellerDept = deptApi.getDept(adminUserRespDTO.getDeptId()).getCheckedData();
            if (cancellerDept != null){
                jsonObject.put("CancellerDept",cancellerDept.getName());
                jsonObject.put("CancellerDeptId",cancellerDept.getId());
            }
            bpmTaskExtDO.setLogParameters(jsonObject.toJSONString());
            UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id",bpmTaskExtDO.getId());
            updateWrapper.set("log_parameters", bpmTaskExtDO.getLogParameters());
            taskExtMapper.update(null,updateWrapper);
        });
        this.generateFollowLog(bpmTaskExtDOS.get(0).getProcessInstanceId());
    }

    public void generateFollowLog(String processInstanceId){
        //正在进行的任务的日志生成
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        List<BpmTaskExtDO> bpmTaskExtDOS = taskExtMapper.selectList(lambdaQueryWrapperX);
        if (!CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
            List<Long> userIds = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toList());
            Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
            bpmTaskExtDOS.forEach( bpmTaskExtDO -> {
                AdminUserRespDTO assigneeUser = userMap.get(bpmTaskExtDO.getAssigneeUserId());
                AffairTaskNameCodeEnum bpmTaskEnum = AffairTaskNameCodeEnum.getEnumByCode(bpmTaskExtDO.getTaskDefKey());
                String taskName = Objects.nonNull(bpmTaskEnum) ? bpmTaskEnum.getTaskShortName() : bpmTaskExtDO.getName();
                JSONObject jsonObject = this.saveGeneralLogInfo(assigneeUser,null,BpmProcessInstanceResultEnum.PROCESS.getResult(),taskName);
                bpmTaskExtDO.setLogParameters(jsonObject.toJSONString());
                UpdateWrapper<BpmTaskExtDO> updateWrapper2 = new UpdateWrapper<>();
                updateWrapper2.eq("task_id",bpmTaskExtDO.getTaskId());
                updateWrapper2.set("log_parameters",bpmTaskExtDO.getLogParameters());
                taskExtMapper.update(null,updateWrapper2);
            });
        }
    }

    public void affairHandleGenerateFollowLog(String processInstanceId){
        //正在进行的任务的日志生成
        LambdaQueryWrapperX<BpmTaskExtDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId);
        lambdaQueryWrapperX.eq(BpmTaskExtDO::getResult, BpmProcessInstanceResultEnum.PROCESS.getResult());
        List<BpmTaskExtDO> bpmTaskExtDOS = taskExtMapper.selectList(lambdaQueryWrapperX);
        if (!CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
            List<Long> userIds = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toList());
            Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
            bpmTaskExtDOS.forEach( bpmTaskExtDO -> {
                AdminUserRespDTO assigneeUser = userMap.get(bpmTaskExtDO.getAssigneeUserId());
                String shortName = dictDataApi.getDictData("affair_handler_node",bpmTaskExtDO.getTaskDefKey()).getData().getLabel();
                String taskName = Objects.nonNull(shortName) ? shortName : bpmTaskExtDO.getName();
                JSONObject jsonObject = this.saveGeneralLogInfo(assigneeUser,null,BpmProcessInstanceResultEnum.PROCESS.getResult(),taskName);
                bpmTaskExtDO.setLogParameters(jsonObject.toJSONString());
                UpdateWrapper<BpmTaskExtDO> updateWrapper2 = new UpdateWrapper<>();
                updateWrapper2.eq("task_id",bpmTaskExtDO.getTaskId());
                updateWrapper2.set("assignee_dept_id", assigneeUser.getDeptId());
                updateWrapper2.set("log_parameters",bpmTaskExtDO.getLogParameters());
                taskExtMapper.update(null,updateWrapper2);
            });
        }
    }


    @Override
    public List<BpmProcessInstanceHandleLogRespVo> getHandleLog(String processInstanceId) {
        List<BpmProcessInstanceHandleLogRespVo> handleLogResps = affairProcessMapper.selectHandleLog(processInstanceId);
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);
        try {
            Set<Long> userIds = new HashSet<>();
            //遍历获取办理日志中涉及到的用户的id
            handleLogResps.forEach( logResp -> {

                if(Objects.isNull(logResp.getLogParameters()))
                {
                    logResp.setTaskShortName(logResp.getName());
                    return;
                }

                JSONObject logJson = JSONObject.parseObject(logResp.getLogParameters());
                JSONObject userObject = logJson.getJSONObject("operator");
                //操作人信息
                if (userObject != null){
                    Number id = (Number) userObject.get("id");
                    userIds.add(id.longValue());
                }
                //转派人信息
                userObject = logJson.getJSONObject("assignee");
                if (userObject != null){
                    Number id = (Number) userObject.get("id");
                    userIds.add(id.longValue());
                }
                logResp.setTaskShortName(logResp.getName());
                String taskShortName = logJson.getString("taskName");
                if (StringUtils.isNotEmpty(taskShortName)){
                    logResp.setTaskShortName(taskShortName);
//                    if (AffairProcessTypeEnum.REQ_INSTRUNCT.getProcessType().equals(bpmProcessInstanceExtDO.getProcessKey())){
//                        logResp.setName(taskShortName);
//                    }
                }
            });
            //过滤掉并行任务中，驳回但办理人未操作的节点
            handleLogResps = handleLogResps.stream().filter( item -> {
                JSONObject logJson = JSONObject.parseObject(item.getLogParameters());
                Integer logResult = logJson.getInteger("result");
                if (item.getResult().equals(4) && logResult.equals(1)){
                    return false;
                }
                return true;
            }).collect(Collectors.toList());

            //用户map
            Map<Long,AdminUserRespDTO> adminUserRespDTOMap = adminUserApi.getUserMap(userIds);
            //部门map
            Map<Long,DeptRespDTO> deptMap = new HashMap<>();
            //遍历办理日志人员信息，并保持最新状态
            handleLogResps.forEach( logResp -> {
                JSONObject logJson = JSONObject.parseObject(logResp.getLogParameters());
                JSONObject userObject = logJson.getJSONObject("operator");
                if (userObject != null){
                    //获取操作人ID
                    Number id = (Number) userObject.get("id");
                    AdminUserRespDTO operator = adminUserRespDTOMap.get(id.longValue());
                    userObject.put("name",operator.getNickname());
                    userObject.put("id",operator.getId());
                    userObject.put("avatar",operator.getAvatar());
                    //获取部门信息
                    DeptRespDTO dept = deptMap.get(operator.getDeptId());
                    //如果没有则调用api查询部门信息
                    if (dept == null){
                        dept = deptApi.getDept(operator.getDeptId()).getCheckedData();
                        userObject.put("DeptId",dept.getId());
                        userObject.put("DeptName",dept.getName());
                        deptMap.put(operator.getDeptId(),dept);
                    }
                    else {
                        userObject.put("DeptId",dept.getId());
                        userObject.put("DeptName",dept.getName());
                    }
                    //更新操作人数据
                    logJson.put("operator",userObject);
                }
                userObject = logJson.getJSONObject("assignee");
                if (userObject != null){
                    //获取转派人ID
                    Number id = (Number) userObject.get("id");
                    AdminUserRespDTO assignee = adminUserRespDTOMap.get(id.longValue());
                    //更新转派人数据
                    logJson.put("assignee",assignee);
                }
                logResp.setLogParameters(logJson.toJSONString());
            });
            List<BpmProcessInstanceHandleLogRespVo> handleLogRespVos = new ArrayList<>();
            if (!CollectionUtils.isAnyEmpty(handleLogResps)) {
                //划分已办和待办,对并行待办进行聚合
                handleLogRespVos = handleLogResps.stream().filter(bpmProcessInstanceHandleLogRespVo -> !NumberUtil.equals(1,bpmProcessInstanceHandleLogRespVo.getResult())).collect(Collectors.toList());
                List<BpmProcessInstanceHandleLogRespVo>  handleLog = handleLogResps.stream().filter(bpmProcessInstanceHandleLogRespVo -> NumberUtil.equals(1,bpmProcessInstanceHandleLogRespVo.getResult())).collect(Collectors.toList());
                if (!CollectionUtils.isAnyEmpty(handleLog)) {
//                    if (AffairProcessTypeEnum.REQ_INSTRUNCT.getProcessType().equals(bpmProcessInstanceExtDO.getProcessKey())
//                        || AffairProcessTypeEnum.WEB_POST.getProcessType().equals(bpmProcessInstanceExtDO.getProcessKey())
//                        || AffairProcessTypeEnum.METTING_APPLY.getProcessType().equals(bpmProcessInstanceExtDO.getProcessKey())) {
//                        BpmProcessInstanceHandleLogRespVo vo = handleLog.get(0);
//                        JSONObject logJson = JSONObject.parseObject(vo.getLogParameters());
//                        JSONArray jsonArray = new JSONArray();
//                        for (BpmProcessInstanceHandleLogRespVo respVo : handleLog) {
//                            JSONObject log = JSONObject.parseObject(respVo.getLogParameters());
//                            JSONObject userObject = log.getJSONObject("operator");
//                            jsonArray.add(userObject);
//                        }
//                        logJson.put("operator", jsonArray);
//                        vo.setLogParameters(logJson.toJSONString());
//                        // 汇总节点
//                        handleLogRespVos.add(vo);
//                    } else {
                        handleLogRespVos.addAll(handleLog);
//                    }
                }
            }
            return handleLogRespVos;
        }
        catch (Exception e){
            return handleLogResps;
        }
    }

    @Override
    public List<BpmProcessInstanceHandleLogRespVo> getHandleLogResultNoOne(String processInstanceId) {
        return affairProcessMapper.selectHandleLogResultNoOne(processInstanceId);
    }

    @Override
    public boolean isFormFiledUnique(String processKey, String filed, String formValue, String dataType) {
        List<String> stringList = affairProcessMapper.selectFormVariables(processKey,formValue);
        //遍历查询结果，对比字段值
        for (String one:stringList){
            JSONObject jsonObject = JSONObject.parseObject(one);
            if (jsonObject.get(filed) == null){
                throw exception(new ErrorCode(400,"不存在该字段"));
            }
            //存在值相同的字段：不唯一
            switch (dataType){
                case "Long":{
                    if (NumberUtil.equals(Long.parseLong(jsonObject.get(filed).toString()),Long.parseLong(formValue))){
                        return false;
                    }
                }
                case "Integer":{
                    if (jsonObject.get(filed) == Integer.valueOf(formValue)){
                        return false;
                    }
                }
                //默认对比的字段是一个String对象
                default:{
                    if (jsonObject.get(filed).equals(formValue)){
                        return false;
                    }
                }
            }
        }
        //不存在相同的字段：唯一
        return true;
    }

    @Override
    public void pushTodoStatus(Integer todoType, String title, Integer subSystem, String processInstanceId, String processDefKey, Integer handleStatus) {

        //如果传入的流程定义key为空
        if (processDefKey == null){
            // 校验流程实例存在
            BpmProcessInstanceExtDO processInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);
            title = AffairProcessNameTypeEnum.getNameByType(processInstanceExtDO.getProcessKey()).getDesc();
            processDefKey = processInstanceExtDO.getProcessKey();
        }
        //当前操作用户ID
        Long submitter = getLoginUserId();
        Set<Long> currentUserId = new HashSet<>();
        currentUserId.add(submitter);
        String finalProcessType = processDefKey;
        String finalTitle = title;
        //todo 状态字段
//        BatchExecutor.get().execute(new Runnable() {
//            @Override
//            public void run() {
//                //更新本次待办事项
//                //封装更新体：用于查询
//                TodoItemUpdateReqDTO todoItemUpdateReqDTO = new TodoItemUpdateReqDTO();
//                todoItemUpdateReqDTO.setSubsystemId(subSystem);
//                todoItemUpdateReqDTO.setProcessType(finalProcessType);
//                todoItemUpdateReqDTO.setProcessId(processInstanceId);
//                //设定已办的状态
//                //todoItemUpdateReqDTO.setProcessStatus(handleStatus);
//                //todoItemUpdateReqDTO.setTaskCode();
//                todoItemUpdateReqDTO.setRemark(null);
//                todoItemUpdateReqDTO.setTodoUsers(currentUserId);
//                todoItemServiceApi.updateTodoItem(todoItemUpdateReqDTO);
//
//                //添加待办事项
//                //查询正在执行此流程任务的用户
//                List<BpmTaskExtDO> bpmTaskExtDOS = getDetailTasks(processInstanceId);
//                if (!CollectionUtils.isAnyEmpty(bpmTaskExtDOS)) {
//                    //待办用户ID
//                    Set<Long> userId = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toSet());
//
//                    TodoItemCreateReqDTO todoItemCreateReqDTO = new TodoItemCreateReqDTO();
//                    todoItemCreateReqDTO.setTodoUsers(userId);
//                    todoItemCreateReqDTO.setProcessId(processInstanceId);
//                    todoItemCreateReqDTO.setSubmitter(submitter);
//                    todoItemCreateReqDTO.setRemark(null);
//                    todoItemCreateReqDTO.setTitle(finalTitle);
//                    todoItemCreateReqDTO.setType(todoType);
//                    todoItemCreateReqDTO.setSubsystemId(subSystem);
//                    todoItemCreateReqDTO.setProcessType(finalProcessType);
//                    todoItemCreateReqDTO.setTaskCode("taskDef"); //todo 后续改为taskDefkey
//                    //新待办的流程状态
//                    //如果当前用户同意了，那么新待办的状态都为处理中
//                    if (handleStatus.equals(BpmProcessInstanceResultEnum.APPROVE.getResult())){
//                        todoItemCreateReqDTO.setProcessStatus(BpmProcessInstanceResultEnum.PROCESS.getResult());
//                    }
//                    else {
//                        todoItemCreateReqDTO.setProcessStatus(handleStatus);
//                    }
////                    todoItemCreateReqDTO.setSubmitTime(LocalDateTime.now());
//                    todoItemCreateReqDTO.setSubmitTime(bpmTaskExtDOS.get(0).getCreateTime());
//                    todoItemServiceApi.createTodoItem(todoItemCreateReqDTO);
//                }
//            }
//        });

    }

    @Override
    public void pushNewTodoToMidOffice(String processInstanceId, String processDefKey,Integer result) {
        //添加待办事项
        //查询正在执行此流程任务的用户
        List<BpmTaskExtDO> bpmTaskExtDOS = getDetailTasks(processInstanceId);
        if (!CollectionUtils.isAnyEmpty(bpmTaskExtDOS)){
            //如果正在执行的任务和taskDefKey相等，则说明是并行任务，且还未全部完成
            // 校验流程实例存在
            BpmProcessInstanceExtDO processInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);


            String title = null;
            if(Objects.nonNull(AffairProcessNameTypeEnum.getNameByType(processInstanceExtDO.getProcessKey()))) {
                title = AffairProcessNameTypeEnum.getNameByType(processInstanceExtDO.getProcessKey()).getDesc();
            }
            else if(Objects.nonNull(AffairHandleProcessNameTypeEnum.getNameByType(processInstanceExtDO.getProcessKey()))){
                title = AffairHandleProcessNameTypeEnum.getNameByType(processInstanceExtDO.getProcessKey()).getDesc();
            }


            String processType = processInstanceExtDO.getProcessKey();
            //待办用户ID
            Set<Long> userIdForNewTodo = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toSet());
//            PushMidOfficeTodoDTO createDTO = new PushMidOfficeTodoDTO();
//            //用户属性
//            createDTO.setUserIdForNewTodo(userIdForNewTodo);
//            createDTO.setUserIdForSubmit(processInstanceExtDO.getStartUserId());
//            //子系统属性
//            //政务子系统
//            createDTO.setSubsystemId(SubSystemEnum.GOVERNMENT.getId());
//            //政务
//            createDTO.setSubsystemTodoBizType(TodoType.AFFAIR.getId());
//            //流程属性
//            createDTO.setProcessType(processType);
//            createDTO.setProcessTaskTitle(title);
//            createDTO.setProcessId(processInstanceId);
//            createDTO.setProcessLaunchTime(LocalDateTime.now());
            //新待办的流程状态
            //如果当前用户同意了，那么新待办的状态都为处理中
//            if (BpmProcessInstanceResultEnum.APPROVE.getResult().equals(result)){
//                createDTO.setProcessTaskStatus(BpmProcessInstanceResultEnum.PROCESS.getResult());
//            }
//            else {
//                createDTO.setProcessTaskStatus(result);
//            }
//            createDTO.setProcessTaskCode(bpmTaskExtDOS.get(0).getTaskDefKey());
//            //DTO属性
//            createDTO.setDtoType("create");
//            createDTO.setDtoUpdateType(null);
//            midOfficeTodoApi.createMidOfficeTodo(createDTO);

        }
    }

    @Override
    public void pushDoneDTOtoMidOffice(String processInstanceId, String processDefKey, Integer result) {
//        PushMidOfficeTodoDTO updateDTO = new PushMidOfficeTodoDTO();
//        if (processDefKey == null){
//            BpmProcessInstanceExtDO processInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);
//            processDefKey = processInstanceExtDO.getProcessKey();
//        }
//        //dto属性
//        updateDTO.setDtoType("update");
//        //用户属性
//        updateDTO.setUserIdForFinish(getLoginUserId());
//        //撤回和驳回需要删除剩余待办用户的待办
//        if (BpmProcessInstanceResultEnum.BACK.getResult().equals(result)
//                || BpmProcessInstanceResultEnum.WITHDRAW.getResult().equals(result)
//                || BpmProcessInstanceResultEnum.CANCEL.getResult().equals(result)){
//            //查询正在执行此流程任务的用户
//            List<BpmTaskExtDO> bpmTaskExtDOS = getDetailTasks(processInstanceId);
//            //其他待办用户ID,将被删除
//            Set<Long> userIdForDel = bpmTaskExtDOS.stream().map(BpmTaskExtDO::getAssigneeUserId).collect(Collectors.toSet());
//            if (userIdForDel != null) {
//                userIdForDel.remove(getLoginUserId());
//                updateDTO.setUserIdForDel(userIdForDel);
//            }
//            if (BpmProcessInstanceResultEnum.BACK.getResult().equals(result)){
//                updateDTO.setDtoUpdateType("reject");
//            }
//            if (BpmProcessInstanceResultEnum.WITHDRAW.getResult().equals(result)){
//                updateDTO.setDtoUpdateType("revoke");
//            }
//            if (BpmProcessInstanceResultEnum.CANCEL.getResult().equals(result)) {
//                HashSet<Long> set = new HashSet<>();
//                set.add(getLoginUserId());
//                updateDTO.setUserIdForDel(set);
//                updateDTO.setDtoUpdateType("cancel");
//                updateDTO.setProcessTaskStatus(BpmProcessInstanceResultEnum.WITHDRAW.getResult());
//            }
//        }
//        else {
//            updateDTO.setDtoUpdateType("approve");
//            if (BpmProcessInstanceResultEnum.APPROVE.getResult().equals(result)){
//                List<BpmTaskExtDO> bpmTaskExtDOS = getDetailTasks(processInstanceId);
//                if (AffairStartTaskCodeEnum.isStartTaskByCode(bpmTaskExtDOS.get(0).getTaskDefKey())){
//                    HashSet<Long> set = new HashSet<>();
//                    set.add(getLoginUserId());
//                    updateDTO.setUserIdForDel(set);
//                    updateDTO.setDtoUpdateType("cancel");
//                    updateDTO.setProcessTaskCode(bpmTaskExtDOS.get(0).getTaskDefKey());
//                }
//            }
//        }
//        //子系统属性
//        updateDTO.setSubsystemId(SubSystemEnum.GOVERNMENT.getId());
//        updateDTO.setSubsystemTodoBizType(TodoType.AFFAIR.getId());
//        //待办流程属性
//        updateDTO.setProcessId(processInstanceId);
//        updateDTO.setProcessType(processDefKey);
//        midOfficeTodoApi.updateMidOfficeTodo(updateDTO);
    }

    @Override
    public List<SuperviseTodoTaskPO> getSuperviseTodoTasks() {
        List<SuperviseTodoTaskPO> superviseTodoTaskPOS = affairProcessMapper.selectSuperviseTodoTasks();
        return superviseTodoTaskPOS;
    }

    @Override
    public BpmTaskExtDO getDetailTaskByTaskId(String taskId) {
        BpmTaskExtDO bpmTaskExtDO = taskExtMapper.selectByTaskId(taskId);
        return bpmTaskExtDO;
    }

    @Override
    public void updateTaskExtDO(BpmTaskExtDO bpmTaskExtDO) {
        taskExtMapper.updateByTaskId(bpmTaskExtDO);
    }

    @Override
    public Boolean updateTaskSuperviseStatus(String taskId, String superviseStatus) {
        BpmTaskExtDO bpmTaskExtDO = taskExtMapper.selectByTaskId(taskId);
        bpmTaskExtDO.setSuperviseStatus(superviseStatus);
        taskExtMapper.updateByTaskId(bpmTaskExtDO);
        return true;
    }


    /**
     * APP搜索页面
     * @param userId 用户ID
     * @param pageReqVO 分页请求
     * @return PageResult
     */
    @Override
    public PageResult<BpmTaskPageItemRespVO> appSearchPage(Long userId, AppSearchPageReqVO pageReqVO) {

        IPage<BpmTaskPageItemRespVO> todoPage = MyBatisUtils.buildPage(pageReqVO);
        //搜索全部
        List<BpmTaskPageItemRespVO> data = affairProcessMapper.selectAllTaskForAppSearch(todoPage, userId, pageReqVO);

        if (!CollectionUtils.isAnyEmpty(data)) {
            this.dealApprovalVariables(data, "");
            //  startUserMap
            HashSet<Long> startUserIds = new HashSet<>();
            startUserIds.addAll(data.stream().map(BpmTaskPageItemRespVO::getStartUserId).collect(Collectors.toSet()));
            Map<Long, AdminUserRespDTO> startUserMap = adminUserApi.getUserMap(startUserIds);

            data.forEach(bpmTaskPageItemRespVO -> {
                if (StringUtils.isNotEmpty(bpmTaskPageItemRespVO.getAssigneeUserId())) {
                    //  assigneeUserNickName and assigneeUserDeptName
                    AdminUserRespDTO assigneeUser = adminUserApi.getUser(Long.parseLong(bpmTaskPageItemRespVO.getAssigneeUserId())).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserNickname(assigneeUser.getNickname());
                    DeptRespDTO assigneeUserDept = deptApi.getDept(assigneeUser.getDeptId()).getCheckedData();
                    bpmTaskPageItemRespVO.setAssigneeUserDeptName(assigneeUserDept.getName());
                    //  assigneeUserIds
                    List<Long> assigneeUserIds = affairProcessMapper.selectAssigneeUserIds(bpmTaskPageItemRespVO.getProcessInstanceId(), bpmTaskPageItemRespVO.getTaskDefKey());
                    bpmTaskPageItemRespVO.setAssigneeUserIds(assigneeUserIds);
                    //  assigneeUserNames
                    List<AdminUserRespDTO> assigneeUsers = adminUserApi.getUsers(assigneeUserIds).getCheckedData();
                    if (!CollectionUtils.isAnyEmpty(assigneeUsers)) {
                        List<String> assigneeUserNickNames = assigneeUsers.stream().map(adminUserRespDTO -> adminUserRespDTO.getNickname()).collect(Collectors.toList());
                        bpmTaskPageItemRespVO.setAssigneeUserNickNames(assigneeUserNickNames);
                    }
                }
                //  startUserNickName
                AdminUserRespDTO startUser = startUserMap.get(bpmTaskPageItemRespVO.getStartUserId());
                if (startUser != null) {
                    bpmTaskPageItemRespVO.setStartUserNickname(startUser.getNickname());
                }
            });
        }
        return new PageResult<>(data, todoPage.getTotal());
    }

    @Override
    public boolean meetingCancel(String processInstanceId) {
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);
        Map<String, Object> formVariables = bpmProcessInstanceExtDO.getFormVariables();

        this.deleteRedisMeetingRoomsTimeRange(formVariables);

        if (!Objects.isNull(formVariables.get("meetingStatus"))) {
            String meetingStatus = (String) formVariables.get("meetingStatus");
//            if (MeetingStatusEnum.TO_BE_STARTED.getCode().equals(meetingStatus)
//                    || MeetingStatusEnum.IN_PROGRESS.getCode().equals(meetingStatus)) {
//                formVariables.put("meetingStatus", MeetingStatusEnum.CANCELED.getCode());
//                bpmProcessInstanceExtDO.setFormVariables(formVariables);
//                bpmProcessInstanceExtDO.setMeetingCancelFlag("1");
//                processInstanceExtMapper.updateByProcessInstanceId(bpmProcessInstanceExtDO);
//            } else if (MeetingStatusEnum.CANCELED.getCode().equals(meetingStatus)) {
//                throw exception(MEETING_END);
//            }
        }
        return true;
    }

    @Override
    public void deleteRedisMeetingRoomsTimeRange(Map<String, Object> formVariables) {
        if (Objects.nonNull(formVariables.get("meetingRooms"))) {

            String oldMeetingRoomsString = JSON.toJSONString(formVariables.get("meetingRooms"));
            List<Map<String, Object>> toCancelMeetingRoomList = JSON.parseObject(oldMeetingRoomsString, new TypeReference<List<Map<String, Object>>>() {
            });

            String meetingRoomPrefix = "meetingRooms:";

            for (Map<String, Object> toCancelMeetingRoom : toCancelMeetingRoomList) {
                String meetingRoomId = toCancelMeetingRoom.get("meetingRoomId").toString();
                String date = toCancelMeetingRoom.get("date").toString();
                String meetingRoomsKey = meetingRoomPrefix + date;
                List<String> toRemoveTimeRange = (List<String>) toCancelMeetingRoom.get("timeRange");

                HashOperations<String, String, List<String>> hashOperations = redisTemplate.opsForHash();
                List<String> timeRange = hashOperations.get(meetingRoomsKey, meetingRoomId);

                if (timeRange != null && !timeRange.isEmpty()) {
                    timeRange = timeRange.stream().filter(slot -> !toRemoveTimeRange.contains(slot)).collect(Collectors.toList());
                    hashOperations.put(meetingRoomsKey, meetingRoomId, timeRange);
                    if (timeRange.isEmpty()) {
                        hashOperations.delete(meetingRoomsKey, meetingRoomId);
                    }
                }
            }
        }
    }


    @Override
    /**
     * 审批通过-政务办理日志保存
     * @param userId
     * @param reqVO
     */
    public void saveAffairApproveHandleLog(Long userId, BpmTaskApproveReqVO reqVO)
    {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(reqVO.getProcessInstanceId());
        // 更新任务拓展表
        UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id",reqVO.getId());
        JSONObject logParam = this.saveGeneralLogInfo(adminUserRespDTO,reqVO.getReason(),BpmProcessInstanceResultEnum.APPROVE.getResult(),bpmProcessInstanceExtDO.getName());
        Map<String, Object> map = reqVO.getVariables();
        if (Objects.isNull(map.get("fileAttachments")) || StringUtils.isEmpty(map.get("fileAttachments").toString())) {
            log.info("保存日志，无附加文件");
        }
        else
        {
            logParam.put("fileAttachments",map.get("fileAttachments"));
        }
        if (Objects.isNull(map.get("handleSituation")) || StringUtils.isEmpty(map.get("handleSituation").toString())) {
            log.info("保存日志，无办理情况");
        }
        else
        {
            logParam.put("handleSituation",map.get("handleSituation"));
        }
        updateWrapper.set("log_parameters",logParam.toJSONString());
        taskExtMapper.update(null,updateWrapper);

        this.generateFollowLog(bpmProcessInstanceExtDO.getProcessInstanceId());
    }


    /**
     * 审批通过-政务办理日志保存,flowFlag保存
     * @param userId
     * @param reqVO
     */
    public void saveAffairApproveHandleLog(Long userId, BpmTaskApproveReqVO reqVO,String flowFlag)
    {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
        BpmProcessInstanceExtDO bpmProcessInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(reqVO.getProcessInstanceId());
        // 更新任务拓展表
        UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id",reqVO.getId());
        BpmTaskExtDO nowTask = this.getDetailTaskByTaskId(reqVO.getId());

        String shortName = dictDataApi.getDictData("affair_handler_node",nowTask.getTaskDefKey()).getData().getLabel();


        JSONObject logParam = this.saveGeneralLogInfo(adminUserRespDTO,reqVO.getReason(),BpmProcessInstanceResultEnum.APPROVE.getResult(),shortName);
        Map<String, Object> map = reqVO.getVariables();
        if (Objects.isNull(reqVO.getFileAttachments()) || reqVO.getFileAttachments().size() == 0) {
            log.info("保存日志，无附加文件");
        }
        else
        {
            logParam.put("fileAttachments",JSON.toJSONString(reqVO.getFileAttachments()));
        }
        if (Objects.isNull(map.get("handleSituation")) || StringUtils.isEmpty(map.get("handleSituation").toString())) {
            log.info("保存日志，无办理情况");
        }
        else
        {
            logParam.put("handleSituation",map.get("handleSituation"));
        }
        logParam.put("flowFlag",flowFlag);
        updateWrapper.set("log_parameters",logParam.toJSONString());
        taskExtMapper.update(null,updateWrapper);

        this.affairHandleGenerateFollowLog(bpmProcessInstanceExtDO.getProcessInstanceId());
    }


    @Override
     /**
     * 审批驳回-政务办理日志保存
     * @param userId
     * @param reqVO
     */
    public void saveAffairRejectHandleLog(Long userId, BpmTaskVO reqVO)
    {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
        BpmTaskExtDO bpmTaskExtDO = taskExtMapper.selectByTaskId(reqVO.getTaskId());
        // 更新任务拓展表
        UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id",reqVO.getTaskId());
        JSONObject logParam = this.saveGeneralLogInfo(adminUserRespDTO,reqVO.getComment(),BpmProcessInstanceResultEnum.REJECT.getResult(),bpmTaskExtDO.getName());
        Map<String, Object> map = reqVO.getValues();
        if (Objects.isNull(map.get("fileAttachments")) || StringUtils.isEmpty(map.get("fileAttachments").toString())) {
            log.info("保存日志，无附加文件");
        }
        else
        {
            logParam.put("fileAttachments",map.get("fileAttachments"));
        }
        if (Objects.isNull(map.get("handleSituation")) || StringUtils.isEmpty(map.get("handleSituation").toString())) {
            log.info("保存日志，无办理情况");
        }
        else
        {
            logParam.put("handleSituation",map.get("handleSituation"));
        }
        updateWrapper.set("log_parameters",logParam.toJSONString());
        taskExtMapper.update(null,updateWrapper);

        this.generateFollowLog(bpmTaskExtDO.getProcessInstanceId());
    }

    @Override
    /**
     * 发起流程-办理日志保存
     * @param userId
     * @param reqVO
     */
    public void saveAffairCreateHandleLog(Long userId, BpmProcessInstanceCreateReqVO reqVO,String instanceId)
    {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getCheckedData();
//        BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,instanceId);
        try {
            BpmTaskExtDTO bpmTaskExtDTO = getDetailTask(userId,instanceId);
            // 更新任务拓展表
            UpdateWrapper<BpmTaskExtDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id",bpmTaskExtDTO.getTaskId());



            JSONObject logParam = this.saveGeneralLogInfo(adminUserRespDTO,null,bpmTaskExtDTO.getResult(),bpmTaskExtDTO.getName());
            Map<String, Object> map = reqVO.getVariables();
            if (Objects.isNull(map.get("fileAttachments")) || StringUtils.isEmpty(map.get("fileAttachments").toString())) {
                log.info("保存日志，无附加文件");
            }
            else
            {
                logParam.put("fileAttachments",map.get("fileAttachments"));
            }
            if (Objects.isNull(map.get("handleSituation")) || StringUtils.isEmpty(map.get("handleSituation").toString())) {
                log.info("保存日志，无办理情况");
            }
            else
            {
                logParam.put("handleSituation",map.get("handleSituation"));
            }
            updateWrapper.set("log_parameters",logParam.toJSONString());
            taskExtMapper.update(null,updateWrapper);
        }catch (Exception e){
            log.error("政务办理-发起流程-办理日志保存",e);
        }

        //正在进行的任务日志保存
        this.affairHandleGenerateFollowLog(instanceId);
    }




    /**
     * 下一节点审批人
     * @param userTaskList
     * @param bpmnModel
     * @param currentId
     */
    private void getNextUserTaskList(ArrayList<UserTask> userTaskList, BpmnModel bpmnModel, String currentId) {
        //通过节点定义id获取当前节点
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(currentId);
        //当前节点所有输出顺序流
        List<SequenceFlow> outgoingFlows = flowNode.getOutgoingFlows();

        FlowElement targetFlowElement = null;
        for (SequenceFlow outgoingFlow : outgoingFlows) {

            // 忽略非正常流转的节点
//            if (outgoingFlow.getConditionExpression() != null) {
//                continue;
//            }

            targetFlowElement = outgoingFlow.getTargetFlowElement();

            //用户节点
            if (targetFlowElement instanceof UserTask) {
                userTaskList.add((UserTask) targetFlowElement);
            } else if (targetFlowElement instanceof EndEvent) {
                // 结束节点
            }else {
                // 没有找到用户节点，且下一节点不是最后一个节点，继续查找
                getNextUserTaskList(userTaskList, bpmnModel, targetFlowElement.getId());
            }
        }
//        if (CollUtil.isEmpty(userTaskList)) {
//            // 为空，没有找到用户节点，继续查找下一个用户节点
//            getNextUserTaskList(userTaskList, bpmnModel, targetFlowElement.getId());
//        }
    }

    /**
     * 下一用户节点
     * @param userTaskList
     * @param bpmnModel
     */
    private void getAllUserTaskList(ArrayList<UserTask> userTaskList, BpmnModel bpmnModel) {
        List<FlowElement> flowElements = (List<FlowElement>) bpmnModel.getProcesses().get(0).getFlowElements();
        for (FlowElement flowElement : flowElements) {
            //用户节点
            if (flowElement instanceof UserTask) {
                userTaskList.add((UserTask) flowElement);
            }
        }
    }

    /**
     * 审批通过日志参数存储路线
     * @param adminUserRespDTO 审批人信息
     * @param reqVO 任务请求参数
     * @param processKey 流程信息
     * @param approvalResult 任务结果
     * @param taskName 任务节点名称
     */
    private String saveApproveLogParam(AdminUserRespDTO adminUserRespDTO, BpmTaskApproveReqVO reqVO, String processKey, Integer approvalResult,String taskName){
        JSONObject logRes = this.saveGeneralLogInfo(adminUserRespDTO,reqVO.getReason(),approvalResult,taskName);

        //其他需要特殊添加特殊参数的任务节点
        AffairProcessNameTypeEnum affairProcessNameTypeEnum = AffairProcessNameTypeEnum.getNameByType(processKey);
        switch (affairProcessNameTypeEnum){
            //用车申请
            case VEHICLE:{
                this.saveVehicleLog(reqVO,logRes);
                break;
            }
            //会议室预定
            case MEETING_RESERVE:{
                this.saveMeetingReserveLog(reqVO,logRes);
                break;
            }
            //会议申请
            case METTING_APPLY:{
                this.saveMeetingApplyLog(reqVO, logRes);
                break;
            }
            //网站发文
            case WEB_POST:{
                this.saveWeb_PostLog(reqVO,logRes);
                break;
            }
            //请示呈批
            case REQ_INSTRUNCT:{
                this.saveReqLog(reqVO,logRes);
                break;
            }
            //合同签审
            case CONTRACT_1:
            case CONTRACT_2:{
                this.saveContractLog(reqVO,logRes);
            }
        }

        return logRes.toJSONString();
    }


    /**
     * 通用的日志信息存储
     * @param adminUserRespDTO 审批人信息
     * @param reason 审批意见
     * @param approvalResult 任务结果
     */
    private JSONObject saveGeneralLogInfo(AdminUserRespDTO adminUserRespDTO, String reason, Integer approvalResult, String taskName){
        JSONObject userInfo = new JSONObject();
        userInfo.put("nickname",adminUserRespDTO.getNickname());
        userInfo.put("id",adminUserRespDTO.getId());
        //获取办理人所属部门信息
        DeptRespDTO deptRespDTO = deptApi.getDept(adminUserRespDTO.getDeptId()).getCheckedData();
        userInfo.put("DeptId",deptRespDTO.getId());
        userInfo.put("DeptName",deptRespDTO.getName());
        JSONObject res = new JSONObject();
        //审批人信息
        res.put("operator",userInfo);
        //审批时间
        res.put("operationTime", new Date());
        //审批结果
        res.put("result",approvalResult);
        //审批意见
        if (Objects.nonNull(reason)){
            res.put("reason",reason);
        }
        //节点名称
        if (Objects.nonNull(taskName)){
            res.put("taskName",taskName);
        }
        return res;
    }

    /**
     * 用车申请日志
     * @param reqVO 任务请求参数
     *
     */
    private void saveVehicleLog(BpmTaskApproveReqVO reqVO, JSONObject res){
        Map<String, Object> map = reqVO.getVariables();
        //局办公室审批
        if (AffairTaskNameCodeEnum.VEHICLE_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())){
            //用车类别
            res.put("checkedTypes",map.get("checkedTypes"));
            if (!Objects.isNull(map.get("otherType"))) {
                res.put("otherType", map.get("otherType"));
            }
            //用车性质
            res.put("checkedCharacters",map.get("checkedCharacters"));
            if (!Objects.isNull(map.get("otherCharacter"))) {
                res.put("otherCharacter", map.get("otherCharacter"));
            }
            //驾驶员
            res.put("driver",map.get("driver"));
            //车辆号码
            res.put("carNumber",map.get("carNumber"));
        }
    }

    private void saveMeetingReserveLog(BpmTaskApproveReqVO reqVO, JSONObject res){
        Map<String, Object> map = reqVO.getVariables();
        //局办公室审批
        if (AffairTaskNameCodeEnum.MEETING_RESERVE_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())){
            //保存选择的会议室信息
            /*if (!Objects.isNull(map.get("meetingRoomId"))) {
                res.put("meetingRoomId",map.get("meetingRoomId"));
            }*/
            if (!Objects.isNull(map.get("meetingRooms"))) {
                res.put("meetingRooms",map.get("meetingRooms"));
            }
            //todo
        }
    }

    private void saveMeetingApplyLog(BpmTaskApproveReqVO reqVO, JSONObject res) {
        Map<String, Object> map = reqVO.getVariables();
        /*if (AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
            res.put("selectedMeetingRoom", map.get("selectedMeetingRoom"));
        }*/
        if (AffairTaskNameCodeEnum.MEETING_APPLY_APPLICANT_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
            String approvalNames = null;
            if (Objects.nonNull(map.get("approvalNames"))) {
                approvalNames = map.get("approvalNames").toString();
            }
            if (map.get("conditionOne").equals(0L)) {               //会议室审批
                res.put("nextTaskDefKey", "会议室审批");
                res.put("nextApprovalNames", approvalNames);

            } else if (map.get("conditionOne").equals(1L)) {
                if (map.get("conditionTwo").equals(0L)) {           //领导审批（顺序）
                    res.put("nextTaskDefKey", "领导审批");
                    res.put("nextApprovalNames", approvalNames);
                    res.put("operatingMode", "顺序");

                } else if (map.get("conditionTwo").equals(1L)) {    //领导审批（会签）
                    res.put("nextTaskDefKey", "领导审批");
                    res.put("nextApprovalNames", approvalNames);
                    res.put("operatingMode", "会签");
                }
            } else {
                res.put("nextTaskDefKey", "结束");
            }
        }
        if (AffairTaskNameCodeEnum.MEETING_APPLY_OFFICE_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
            res.put("meetingRooms", map.get("meetingRooms"));
        }
    }
    private void saveWeb_PostLog(BpmTaskApproveReqVO reqVO, JSONObject res) {
        Map<String, Object> map = reqVO.getVariables();
        if (AffairTaskNameCodeEnum.WEB_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
            if (!Objects.isNull(map.get("linkManSign")) && "1".equals(map.get("linkManSign").toString())){
                res.put("linkManSign","是");
            } else {
                res.put("linkManSign","否");
            }
            if (!Objects.isNull(map.get("manSignSeq")) && "1".equals(map.get("manSignSeq").toString())){
                res.put("manSignSeq","会签");
            } else {
                res.put("manSignSeq","顺序");
            }
            if(Objects.nonNull(map.get("chargeLeaderNames"))){
               res.put("chargeLeaderNames", map.get("chargeLeaderNames"));
            }
            if (!Objects.isNull(map.get("chargeLeaderSeq")) && "1".equals(map.get("chargeLeaderSeq").toString())){
                res.put("chargeLeaderSeq","会签");
            } else {
                res.put("chargeLeaderSeq","顺序");
            }

//            if (Objects.isNull(map.get("approvalNames")) && !Objects.isNull("approvals")) {
//                List<Long> leaders =(List<Long>)map.get("approvals");
//                List<AdminUserRespDTO> adminUserRespDTOS = adminUserApi.getUsers(leaders).getCheckedData();
//                List<String> names = adminUserRespDTOS.stream().map(AdminUserRespDTO::getNickname).collect(Collectors.toList());
//                res.put("approvalNames", String.join(",", names));
//            } else {
//                res.put("approvalNames",map.get("approvalNames"));
//            }
            res.put("approvalNames",map.get("approvalNames"));
        }
        //在主要领导这里的特殊驳回时，在日志里存一个代表同意不同意的东西
        if (AffairTaskNameCodeEnum.WEB_MAIN_SIGN.getCode().equals(reqVO.getTaskDefKey()) ||
                AffairTaskNameCodeEnum.WEB_MAIN_SIGN_SEQ.getCode().equals(reqVO.getTaskDefKey())){
            if (!Objects.isNull(map.get("webPostBack")) && "1".equals(map.get("webPostBack").toString())){
                res.put("webPostBack","不同意");
            }else {
                res.put("webPostBack","同意");
            }
        }

    }

    private void saveReqLog(BpmTaskApproveReqVO reqVO, JSONObject res) {
        Map<String, Object> map = reqVO.getVariables();
        if (AffairTaskNameCodeEnum.REQ_PROPOSE.getCode().equals(reqVO.getTaskDefKey()) ||
                AffairTaskNameCodeEnum.REQ_PROPOSE_SUM.getCode().equals(reqVO.getTaskDefKey()) ||
                AffairTaskNameCodeEnum.REQ_INSTRUCT_SUM.getCode().equals(reqVO.getTaskDefKey())) {
            if (!Objects.isNull(map.get("conditionApproval")) && "1".equals(map.get("conditionApproval").toString())){
                res.put("conditionApproval","批示");
            } else if (!Objects.isNull(map.get("conditionApproval")) && "0".equals(map.get("conditionApproval").toString())) {
                res.put("conditionApproval","拟办");
            }
            if (!Objects.isNull(map.get("conditionComment")) && "1".equals(map.get("conditionComment").toString())){
                res.put("conditionComment","会签");
            } else if (!Objects.isNull(map.get("conditionComment")) && "0".equals(map.get("conditionComment").toString())){
                res.put("conditionComment","顺序");
            }
            if (!Objects.isNull(map.get("conditionApprovalseq")) && "1".equals(map.get("conditionApprovalseq").toString())){
                res.put("conditionComment","会签");
            } else if (!Objects.isNull(map.get("conditionApprovalseq")) && "0".equals(map.get("conditionApprovalseq").toString())){
                res.put("conditionComment","顺序");
            }
            if (!Objects.isNull(map.get("conditionTwoApproval")) && "1".equals(map.get("conditionTwoApproval").toString())){
                res.put("conditionApproval","批示");
            } else if (!Objects.isNull(map.get("conditionTwoApproval")) && "0".equals(map.get("conditionTwoApproval").toString())){
                res.put("conditionApproval","结束");
            }
            res.put("approvalNames",map.get("approvalNames"));

        }
    }

    private void saveContractLog(BpmTaskApproveReqVO reqVO, JSONObject res) {
        Map<String, Object> map = reqVO.getVariables();
        if (AffairTaskNameCodeEnum.CONTRACT_1_ECON_APPROVE.getCode().equals(reqVO.getTaskDefKey())
            || AffairTaskNameCodeEnum.CONTRACT_2_ECON_APPROVE.getCode().equals(reqVO.getTaskDefKey())) {
            if (Objects.nonNull(map.get("needMainLeader")) && "1".equals(map.get("needMainLeader").toString())){
                res.put("needMainLeader","是");
            }
            else {
                res.put("needMainLeader","否");
            }
        }
    }

    /**
     * 同步会议室预定
     * @param reqVO
     * @param map
     * @param processKey
     */
    private void meetingReserveUpdate(BpmTaskApproveReqVO reqVO, Map<String, Object> map, String processKey) {
//        MeetingReserveBaseDTO meetingReserveBaseDTO = new MeetingReserveBaseDTO();
//        meetingReserveBaseDTO.setProcessInstanceId(reqVO.getProcessInstanceId());
//        meetingReserveBaseDTO.setProcessDefinitionKey(processKey);
//        meetingReserveBaseDTO.setVariables(map);
//        meetingReserveApi.createMeetingReserve(meetingReserveBaseDTO);
    }

    /**
     * JSONArray 去重
     * @param jsonArray
     * @param approvalName
     * @return
     */
    private JSONArray dealMutilp(JSONArray jsonArray, String approvalName) {
        JSONArray data = new JSONArray();
        for (int i= 0; i<jsonArray.size();i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (!approvalName.equals(jsonObject.getString("approvalUser"))) {
                data.add(jsonObject);
            }
        }
        return data;
    }

    /**
     * 摘要信息和流程参数
     *
     */
    private void dealApprovalVariables(List<BpmTaskPageItemRespVO> itemRespVOS, String flag){
        Set<String> processInstanceIdSet = new HashSet<>();
        //过滤出发文流程的实例ID
        processInstanceIdSet = itemRespVOS
                .stream()
                .filter( (item) -> ApprovalTypeTagEnum.SENDING.getTagName().equals(item.getProcessInstanceKey()) )
                .map(BpmTaskPageItemRespVO::getProcessInstanceId)
                .collect(Collectors.toSet());

        //获取发文信息List
//        List<SendingDocDTO> sendingDocDTOList = docmanApi.getSendingDocListByProcessInstanceId(processInstanceIdSet).getCheckedData();
//        //List转发文Map
//        Map<String, SendingDocDTO> sendingDocDTOMap = convertMap(sendingDocDTOList,SendingDocDTO::getProcessInstanceId);
//
//        //过滤出收文流程的实例ID
//        processInstanceIdSet = itemRespVOS
//                .stream()
//                .filter( (item) -> ApprovalTypeTagEnum.RECEIVING.getTagName().equals(item.getProcessInstanceKey()) )
//                .map(BpmTaskPageItemRespVO::getProcessInstanceId)
//                .collect(Collectors.toSet());
//        //获取收文信息List
//        List<ReceivingDocDTO> ReceivingDocDTOList = docmanApi.getReceivingDocListByProcessInstanceId(processInstanceIdSet,"todo".equals(flag) ? "0" : "1").getCheckedData();
//        //List转收文Map
//        Map<String, ReceivingDocDTO> receivingDocDTOMap = convertMap(ReceivingDocDTOList,ReceivingDocDTO::getProcessInstanceId);
//
//        //审批中心和政务办理流程实例的ID
//        processInstanceIdSet = itemRespVOS
//                .stream()
//                .map(BpmTaskPageItemRespVO::getProcessInstanceId)
//                .collect(Collectors.toSet());
//        List<BpmProcessInstanceExtDO> bpmProcessInstanceExtDOList = processInstanceExtMapper.selectList(BpmProcessInstanceExtDO::getProcessInstanceId,processInstanceIdSet);
//        //List转审批中心流程Map
//        Map<String,BpmProcessInstanceExtDO> bpmProcessInstanceExtDOMap = convertMap(bpmProcessInstanceExtDOList,BpmProcessInstanceExtDO::getProcessInstanceId);
//
//        //处理重点工作督办摘要信息
//        Set<Long> keyWorkIds = itemRespVOS
//                .stream()
//                .filter( item -> ApprovalTypeTagEnum.KEY_WORK.getTagName().equals(item.getProcessInstanceKey()))
//                .map( item -> Long.valueOf(item.getProcessInstanceId()))
//                .collect(Collectors.toSet());
//        List<KeyWorkCardAbstractDTO> keyWorkAbsctractDTOList = docmanApi.getKeyWorkListByIds(keyWorkIds, getLoginUserId()).getCheckedData();
//
//        //处理政民互动摘要信息
//        Set<Long> peopleInteractionIds = itemRespVOS
//                .stream()
//                .filter( item -> ApprovalTypeTagEnum.PEOPLE_INTERACTION.getTagName().equals(item.getProcessInstanceKey()) )
//                .map( item -> Long.valueOf(item.getProcessInstanceId()))
//                .collect(Collectors.toSet());
//        List<PeopleInteractionAppCardDTO> peopleInteractionAppCardList = docmanApi.getPeopleInteraction(peopleInteractionIds).getCheckedData();
//
//
//        itemRespVOS.forEach(item ->{
//            if (item.getSuperviseStatus() == null){
//                item.setSuperviseStatus("");
//            }
//            //处理状态
//            switch (item.getProcessInstanceKey()){
//                //发文状态标记
//                case "affair-sending-flow":{
//                    SendingDocDTO dto = sendingDocDTOMap.get(item.getProcessInstanceId());
//                    if (dto != null){
//                        if (SendingTaskStatusEnum.REJECT.getStatus().equals(dto.getStatus())){
//                            item.setFlowFlag("2");
//                        }
//                        else if (SendingTaskStatusEnum.REVOKE.getStatus().equals(dto.getStatus())){
//                            item.setFlowFlag("1");
//                        }
//                        item.setProcessInstanceName("发文");
//                        //发文督办状态展示
//                        if (dto.getSuperviseStatus() == null){
//                            dto.setSuperviseStatus("");
//                        }
//                        //如果是请求待办，superviseStatus则为待办查询出来的状态
//                        if (item.getSuperviseStatus() != null && "todo".equals(flag) ){
//                            dto.setSuperviseStatus(item.getSuperviseStatus());
//                        }
//                        item.setSuperviseStatus(dto.getSuperviseStatus());
//                        //卡片标题和摘要信息
//                        dto.setCardTitle(dto.getName());
//                        dto.setCardAbstract(dto.getDocNo());
//                        item.setApprovalVariables(BeanUtil.beanToMap(dto));
//                    }
//                    break;
//                }
//                //收文
//                case "affair-receiving-flow":{
//                    ReceivingDocDTO dto = receivingDocDTOMap.get(item.getProcessInstanceId());
//                    if (dto != null){
//                        //收文状态标记
//                        if (RecevingTaskStatusEnum.REJECT.getStatus().equals(dto.getStatus())){
//                            item.setFlowFlag("2");
//                        }
//                        else if (RecevingTaskStatusEnum.REVOKE.getStatus().equals(dto.getStatus())){
//                            item.setFlowFlag("1");
//                        }
//                        item.setProcessInstanceName("收文");
//                        //收文督办状态展示
//                        if (dto.getSuperviseStatus() == null){
//                            dto.setSuperviseStatus("");
//                        }
//                        //如果是请求待办，superviseStatus则为待办查询出来的状态
//                        if (item.getSuperviseStatus() != null && "todo".equals(flag) ){
//                            dto.setSuperviseStatus(item.getSuperviseStatus());
//                        }
//                        item.setSuperviseStatus(dto.getSuperviseStatus());
//                        //卡片标题和摘要信息
//                        dto.setCardTitle(dto.getName());
//                        dto.setCardAbstract(dto.getDocNo()+dto.getDocNoYear()+dto.getDocCode());
//                        item.setApprovalVariables(BeanUtil.beanToMap(dto));
//                    }
//                    break;
//                }
//                //重点工作
//                case "keyWork":{
//                    KeyWorkCardAbstractDTO dto = keyWorkAbsctractDTOList
//                            .stream()
//                            .filter(keyWork ->keyWork.getId().equals(Long.valueOf(item.getProcessInstanceId())))
//                            .findFirst()
//                            .get();
//                    item.setApprovalVariables(BeanUtil.beanToMap(dto));
//                    try {
//                        String cardTitle = AppCardInfoUtil.getAppCardTitle(item.getApprovalVariables(),item.getProcessInstanceKey(),false);
//                        String cardAbstract = AppCardInfoUtil.getAppCardAbstract(item.getApprovalVariables(),item.getProcessInstanceKey(),false);
//                        item.getApprovalVariables().put("cardTitle",cardTitle);
//                        item.getApprovalVariables().put("cardAbstract",cardAbstract);
//                    }catch (Exception e){
//                        log.error("卡片信息转换异常",e);
//                    }
//                    item.setAssigneeUserId(String.valueOf(dto.getStartUserId()));
//                    item.setAssigneeUserNickname(dto.getStartUserName());
//                    break;
//                }
//                case "peopleInteraction":{
//                    PeopleInteractionAppCardDTO cardDTO = peopleInteractionAppCardList
//                            .stream()
//                            .filter( dto -> dto.getId().equals(Long.valueOf(item.getProcessInstanceId())) )
//                            .findFirst().get();
//                    if ( Objects.isNull(cardDTO.getSuperviseStatus()) ){
//                        cardDTO.setSuperviseStatus("");
//                    }
//                    item.setApprovalVariables(BeanUtil.beanToMap(cardDTO));
//                    break;
//                }
//                //审批中心
//                default:{
//                    BpmProcessInstanceExtDO processInstanceExt = bpmProcessInstanceExtDOMap.get(item.getProcessInstanceId());
//                    if (processInstanceExt != null){
//                        Map<String,Object> approvalVariables = BeanUtil.beanToMap(processInstanceExt);
//                        Map<String, Object>  formVariables = processInstanceExt.getFormVariables();
//                        //对审批中心的任务，需要根据formVariables的参数来设置卡片信息
//                        if (formVariables != null){
//                            try{
//                                String cardTitle = AppCardInfoUtil.getAppCardTitle(formVariables,item.getProcessInstanceKey(),true);
//                                approvalVariables.put("cardTitle",cardTitle);
//                                //如果查的是待办已办传approvalVariable
//                                if ("todo".equals(flag) || "done".equals(flag)){
//                                    String cardAbstract = AppCardInfoUtil.getAppCardAbstract(formVariables,item.getProcessInstanceKey(),true);
//                                    approvalVariables.put("cardAbstract",cardAbstract);
//                                }
//                                //如果是我发起，传approvalVariables下的formVariables
//                                else if ("myLaunch".equals(flag)){
//                                    String cardAbstract = AppCardInfoUtil.getAppCardAbstract(formVariables,item.getProcessInstanceKey(),true);
//                                    approvalVariables.put("cardAbstract",cardAbstract);
//                                }
//                            }catch (Exception e){
//                                log.info("卡片信息转换异常",e);
//                            }
//                        }
//                        approvalVariables.put("superviseStatus",item.getSuperviseStatus());
//                        item.setApprovalVariables(approvalVariables);
//                        if (StringUtils.isNotEmpty(item.getFlowFlag())) {
//                            if ("3".equals(item.getFlowFlag())) {
//                                item.setFlowFlag("1");
//                            } else if("4".equals(item.getFlowFlag())) {
//                                item.setFlowFlag("2");
//                            }
//                        }
//                        //政务办理状态转换
//                        if (StringUtils.isNotEmpty(item.getProcessInstanceKey()) && StringUtils.isNotEmpty(item.getTaskDefKey())) {
//                            try {
//                                convertTodoProcessForAffairHandle(item);
//                            }
//                            catch (Exception e){
//                                log.info("政务办理状态转换出错");
//                            }
//                            //todo
//                        }
//                    }
//                }
//            }
//        });

    }

    private void dealApprovalVariables2(List<BpmTaskPageItemRespVO> itemRespVOS, String flag){
        Map<String, Object> idMap = getProcessCardInfoMap(itemRespVOS,flag);
        determineRespVOS(itemRespVOS,idMap,flag);
    }
    private Map<String, Object> getProcessCardInfoMap(List<BpmTaskPageItemRespVO> itemRespVOS, String flag){
        //获取ID
        //审批中心和政务办理业务ID
        Set<String> bpmProcessInstanceIdSet = itemRespVOS.stream()
                .map(BpmTaskPageItemRespVO::getProcessInstanceId)
                .collect(Collectors.toSet());
        //发文流程ID
        Set<String> sendingProcessInstanceIdSet = itemRespVOS.stream()
                .filter( x -> ApprovalTypeTagEnum.SENDING.getTagName().equals( x.getProcessInstanceKey() ))
                .map(BpmTaskPageItemRespVO::getProcessInstanceId)
                .collect(Collectors.toSet());
        //收文流程ID
        Set<String> receivingProcessInstanceIdSet = itemRespVOS.stream()
                .filter( x -> ApprovalTypeTagEnum.RECEIVING.getTagName().equals( x.getProcessInstanceKey() ))
                .map(BpmTaskPageItemRespVO::getProcessInstanceId)
                .collect(Collectors.toSet());
        //重点工作ID
        Set<Long> keyWorkIds = itemRespVOS.stream()
                .filter( x -> ApprovalTypeTagEnum.KEY_WORK.getTagName().equals( x.getProcessInstanceKey() ))
                .map( item -> Long.valueOf(item.getProcessInstanceId()))
                .collect(Collectors.toSet());
        //处理政民互动ID
        Set<Long> peopleInteractionIds = itemRespVOS
                .stream()
                .filter( item -> ApprovalTypeTagEnum.PEOPLE_INTERACTION.getTagName().equals(item.getProcessInstanceKey()) )
                .map( item -> Long.valueOf(item.getProcessInstanceId()))
                .collect(Collectors.toSet());

        //获取list
        //获取bpm-server的流程实例
//        List<BpmProcessInstanceExtDO> bpmProcessInstanceExtDOList = getBpmInstanceExtListForAppQuery(bpmProcessInstanceIdSet);
//        //获取发文流程实例
//        List<SendingDocDTO> sendingDocDTOList = getSendingDetailForAppQuery(sendingProcessInstanceIdSet);
//        //获取收文流程实例
//        List<ReceivingDocDTO> ReceivingDocDTOList = getReceivingDetailForAppQuery(receivingProcessInstanceIdSet,flag);
//        //获取重点工作信息
//        List<KeyWorkCardAbstractDTO> keyWorkAbsctractDTOList = getKeyWorkDetailForAppQuery(keyWorkIds, getLoginUserId());
//        //获取政民互动信息
//        List<PeopleInteractionAppCardDTO> peopleInteractionAppCardList = getPeopleInteractionDetailForAppQuery(peopleInteractionIds);
//
//        //List转Map
//        //审批中心
//        Map<String,BpmProcessInstanceExtDO> bpmProcessInstanceExtDOMap = convertMap(bpmProcessInstanceExtDOList,BpmProcessInstanceExtDO::getProcessInstanceId);
//        //发文
//        Map<String, SendingDocDTO> sendingDocDTOMap = convertMap(sendingDocDTOList,SendingDocDTO::getProcessInstanceId);
//        //收文
//        Map<String, ReceivingDocDTO> receivingDocDTOMap = convertMap(ReceivingDocDTOList,ReceivingDocDTO::getProcessInstanceId);
//        //重点工作
//        Map<Long,KeyWorkCardAbstractDTO> keyWorkMap = convertMap(keyWorkAbsctractDTOList,KeyWorkCardAbstractDTO::getId);
//        //政民互动
//        Map<Long,PeopleInteractionAppCardDTO> peopleInteractionMap = convertMap(peopleInteractionAppCardList,PeopleInteractionAppCardDTO::getId);
        Map<String,Object> returnMap = new HashMap<>();
//        returnMap.put("bpmProcess",bpmProcessInstanceExtDOMap);
//        returnMap.put("sending",sendingDocDTOMap);
//        returnMap.put("receiving",receivingDocDTOMap);
//        returnMap.put("keyWork",keyWorkMap);
//        returnMap.put("peopleInteraction",peopleInteractionMap);
        return returnMap;
    }

    private List<BpmProcessInstanceExtDO> getBpmInstanceExtListForAppQuery(Set<String> bpmProcessInstanceIdSet){
        if ( CollectionUtils.isAnyEmpty(bpmProcessInstanceIdSet) ){
            return Collections.emptyList();
        }
        return processInstanceExtMapper.selectList(BpmProcessInstanceExtDO::getProcessInstanceId,bpmProcessInstanceIdSet);
    }

//    private List<SendingDocDTO> getSendingDetailForAppQuery(Set<String> sendingProcessInstanceIdSet){
//        if ( CollectionUtils.isAnyEmpty(sendingProcessInstanceIdSet) ){
//            return Collections.emptyList();
//        }
//        return docmanApi.getSendingDocListByProcessInstanceId(sendingProcessInstanceIdSet).getCheckedData();
//    }
//    private List<ReceivingDocDTO> getReceivingDetailForAppQuery(Set<String> receivingProcessInstanceIdSet,String flag){
//        if ( CollectionUtils.isAnyEmpty(receivingProcessInstanceIdSet) ){
//            return Collections.emptyList();
//        }
//        return docmanApi.getReceivingDocListByProcessInstanceId(receivingProcessInstanceIdSet,"todo".equals(flag) ? "0" : "1").getCheckedData();
//    }
//
//    private List<KeyWorkCardAbstractDTO> getKeyWorkDetailForAppQuery(Set<Long> keyWorkIds, Long userId){
//        if ( CollectionUtils.isAnyEmpty(keyWorkIds) ){
//            return Collections.emptyList();
//        }
//        return docmanApi.getKeyWorkListByIds(keyWorkIds, userId).getCheckedData();
//    }
//
//    private List<PeopleInteractionAppCardDTO> getPeopleInteractionDetailForAppQuery(Set<Long> peopleInteractionIds){
//        if ( CollectionUtils.isAnyEmpty(peopleInteractionIds) ){
//            return Collections.emptyList();
//        }
//        return docmanApi.getPeopleInteraction(peopleInteractionIds).getCheckedData();
//    }

    private static void determineRespVOS(List<BpmTaskPageItemRespVO> itemRespVOS,Map<String,Object> cardInfoMap, String flag){
//        Map<String,BpmProcessInstanceExtDO> bpmProcessInstanceExtDOMap = (Map<String, BpmProcessInstanceExtDO>) cardInfoMap.get("bpmProcess");
//        Map<String, SendingDocDTO> sendingDocDTOMap = (Map<String, SendingDocDTO>) cardInfoMap.get("sending");
//        Map<String, ReceivingDocDTO> receivingDocDTOMap = (Map<String, ReceivingDocDTO>) cardInfoMap.get("receiving");
//        Map<Long,KeyWorkCardAbstractDTO> keyWorkMap = (Map<Long, KeyWorkCardAbstractDTO>) cardInfoMap.get("keyWork");
//        Map<Long,PeopleInteractionAppCardDTO> peopleInteractionMap = (Map<Long, PeopleInteractionAppCardDTO>) cardInfoMap.get("peopleInteraction");
//        itemRespVOS.forEach( item -> {
//            if (item.getSuperviseStatus() == null){
//                item.setSuperviseStatus("");
//            }
//            SqlInclusionEnum queryCategory = SqlInclusionEnum.getByName( item.getQueryCategory() );
//            if ( queryCategory != null ){
//                //流程策略
//                switch ( queryCategory ){
//                    case SENDING_INCLUSION:{
//                        SendingDocDTO dto = sendingDocDTOMap.get( item.getProcessInstanceId() );
//                        if ( dto != null){
//                            dealSendingForAppQuery(dto,item,flag);
//                        }
//                        break;
//                    }
//                    case RECEIVING_INCLUSION:{
//                        ReceivingDocDTO dto = receivingDocDTOMap.get( item.getProcessInstanceId() );
//                        if (dto != null){
//                            dealReceivingForAppQuery(dto,item,flag);
//                        }
//                        break;
//                    }
//                    case KEY_WORK_INCLUSION:{
//                        KeyWorkCardAbstractDTO dto = keyWorkMap.get( Long.valueOf(item.getProcessInstanceId()) );
//                        if (dto != null){
//                            dealKeyWorkForAppQuery(dto,item);
//                        }
//                        break;
//                    }
//                    case PEOPLE_INTERACTION_INCLUSION:{
//                        PeopleInteractionAppCardDTO dto = peopleInteractionMap.get( Long.valueOf(item.getProcessInstanceId() ));
//                        if (dto != null){
//                            dealPeopleInteractionForAppQuery(dto,item);
//                        }
//                        break;
//                    }
//                    case APPROVAL_CENTER_INCLUSION:{
//                        BpmProcessInstanceExtDO processInstanceExt = bpmProcessInstanceExtDOMap.get( item.getProcessInstanceId() );
//                        if (processInstanceExt != null){
//                            dealApprovalCenterDetailForAppQuery(processInstanceExt,item,flag);
//                        }
//                        break;
//                    }
//                    case AFFAIR_HANDLE_INCLUSION:{
//                        BpmProcessInstanceExtDO processInstanceExt = bpmProcessInstanceExtDOMap.get( item.getProcessInstanceId() );
//                        if (processInstanceExt != null){
//                            dealAffairHandleDetailForAppQuery(processInstanceExt,item);
//                            if (PageNameEnum.TODO_PAGE.getPageName().equals(flag)){
//                                convertTodoProcessForAffairHandle(item);
//                            }
//                            else if (PageNameEnum.DONE_PAGE.getPageName().equals(flag)){
//                                convertDoneProcessForAffairHandle(item);
//                            }
//                        }
//                        break;
//                    }
//                }
//            }
//
//        });
    }

//    private static void dealSendingForAppQuery(SendingDocDTO dto, BpmTaskPageItemRespVO item, String flag){
//        if (SendingTaskStatusEnum.REJECT.getStatus().equals(dto.getStatus())){
//            item.setFlowFlag("2");
//        }
//        else if (SendingTaskStatusEnum.REVOKE.getStatus().equals(dto.getStatus())){
//            item.setFlowFlag("1");
//        }
//        item.setProcessInstanceName("发文");
//        //发文督办状态展示
//        if (dto.getSuperviseStatus() == null){
//            dto.setSuperviseStatus("");
//        }
//        //如果是请求待办，superviseStatus则为待办查询出来的状态
//        if (item.getSuperviseStatus() != null && PageNameEnum.TODO_PAGE.getPageName().equals(flag) ){
//            dto.setSuperviseStatus(item.getSuperviseStatus());
//        }
//        item.setSuperviseStatus(dto.getSuperviseStatus());
//        //卡片标题和摘要信息
//        dto.setCardTitle(dto.getName());
//        dto.setCardAbstract( dto.getIssuedNumber() );
//        item.setApprovalVariables(BeanUtil.beanToMap(dto));
//    }
//    private static void dealReceivingForAppQuery(ReceivingDocDTO dto, BpmTaskPageItemRespVO item, String flag){
//        //收文状态标记
//        if (RecevingTaskStatusEnum.REJECT.getStatus().equals(dto.getStatus())){
//            item.setFlowFlag("2");
//        }
//        else if (RecevingTaskStatusEnum.REVOKE.getStatus().equals(dto.getStatus())){
//            item.setFlowFlag("1");
//        }
//        item.setProcessInstanceName("收文");
//        //收文督办状态展示
//        if (dto.getSuperviseStatus() == null){
//            dto.setSuperviseStatus("");
//        }
//        //如果是请求待办，superviseStatus则为待办查询出来的状态
//        if (item.getSuperviseStatus() != null && PageNameEnum.TODO_PAGE.getPageName().equals(flag) ){
//            dto.setSuperviseStatus(item.getSuperviseStatus());
//        }
//        item.setSuperviseStatus(dto.getSuperviseStatus());
//        //卡片标题和摘要信息
//        dto.setCardTitle(dto.getName());
//        dto.setCardAbstract(dto.getDocNo()+dto.getDocNoYear()+dto.getDocCode());
//        item.setApprovalVariables(BeanUtil.beanToMap(dto));
//
//    }
//    private static void dealKeyWorkForAppQuery(KeyWorkCardAbstractDTO dto, BpmTaskPageItemRespVO item){
//        item.setSuperviseStatus(dto.getSuperviseStatus());
//        item.setApprovalVariables(BeanUtil.beanToMap(dto));
//        try {
//            String cardTitle = AppCardInfoUtil.getAppCardTitle(item.getApprovalVariables(),item.getProcessInstanceKey(),false);
//            String cardAbstract = AppCardInfoUtil.getAppCardAbstract(item.getApprovalVariables(),item.getProcessInstanceKey(),false);
//            item.getApprovalVariables().put("cardTitle",cardTitle);
//            item.getApprovalVariables().put("cardAbstract",cardAbstract);
//        }catch (Exception e){
//            log.error("卡片信息转换异常",e);
//        }
//        item.setAssigneeUserId(String.valueOf(dto.getStartUserId()));
//        item.setAssigneeUserNickname(dto.getStartUserName());
//    }
//
//    private static void dealPeopleInteractionForAppQuery(PeopleInteractionAppCardDTO dto, BpmTaskPageItemRespVO item) {
////        dto.setSuperviseStatus("");
////        item.setApprovalVariables(BeanUtil.beanToMap(dto));
//        Map<String, Object> approvalVariables = BeanUtil.beanToMap(dto);
//        if (getLoginUserId().equals(item.getStartUserId()) && "1".equals(dto.getStatus())) {
//            approvalVariables.put("canSupervise", true);
//        } else {
//            approvalVariables.put("canSupervise", false);
//        }
//        item.setApprovalVariables(approvalVariables);
//    }
//    private static void dealApprovalCenterDetailForAppQuery(BpmProcessInstanceExtDO processInstanceExt, BpmTaskPageItemRespVO item, String flag){
//        Map<String,Object> approvalVariables = BeanUtil.beanToMap(processInstanceExt);
//        Map<String, Object>  formVariables = processInstanceExt.getFormVariables();
//        //对审批中心的任务，需要根据formVariables的参数来设置卡片信息
//        try{
//            String cardTitle = AppCardInfoUtil.getAppCardTitle(formVariables,item.getProcessInstanceKey(),true);
//            approvalVariables.put("cardTitle",cardTitle);
//            //如果查的是待办已办传approvalVariable
//            if (PageNameEnum.TODO_PAGE.getPageName().equals(flag) || PageNameEnum.DONE_PAGE.getPageName().equals(flag)){
//                String cardAbstract = AppCardInfoUtil.getAppCardAbstract(formVariables,item.getProcessInstanceKey(),true);
//                approvalVariables.put("cardAbstract",cardAbstract);
//            }
//            //如果是我发起，传approvalVariables下的formVariables
//            else if (PageNameEnum.MY_LAUNCH_PAGE.getPageName().equals(flag)){
//                String cardAbstract = AppCardInfoUtil.getAppCardAbstract(formVariables,item.getProcessInstanceKey(),true);
//                approvalVariables.put("cardAbstract",cardAbstract);
//            }
//        }catch (Exception e){
//            log.info("卡片信息转换异常",e);
//        }
//        approvalVariables.put("superviseStatus",item.getSuperviseStatus());
//        item.setApprovalVariables(approvalVariables);
//        if (StringUtils.isNotEmpty(item.getFlowFlag())) {
//            if ("3".equals(item.getFlowFlag())) {
//                item.setFlowFlag("1");
//            } else if("4".equals(item.getFlowFlag())) {
//                item.setFlowFlag("2");
//            }
//        }
//    }

    private static void dealAffairHandleDetailForAppQuery(BpmProcessInstanceExtDO processInstanceExt, BpmTaskPageItemRespVO item){
        Map<String,Object> approvalVariables = BeanUtil.beanToMap(processInstanceExt);
        approvalVariables.put("superviseStatus",item.getSuperviseStatus());
        item.setApprovalVariables(approvalVariables);
    }

    private AdminUserRespDTO lazyLoadUserMap(Long userId,Map<Long,AdminUserRespDTO> userMap){
        if (Objects.isNull(userId) || Objects.isNull(userMap)){
            return new AdminUserRespDTO();
        }
        AdminUserRespDTO adminUserRespDTO = userMap.get(userId);
        if (Objects.nonNull(adminUserRespDTO)){
            return adminUserRespDTO;
        }
        else {
            adminUserRespDTO = adminUserApi.getUser(userId).getData();
            if (Objects.nonNull(adminUserRespDTO)){
             userMap.put(adminUserRespDTO.getId(),adminUserRespDTO);
             return adminUserRespDTO;
            }
            else {
                return new AdminUserRespDTO();
            }
        }
    }

    private DeptRespDTO lazyLoadDeptMap(Long deptId,Map<Long,DeptRespDTO> deptMap){
        if (Objects.isNull(deptId) || Objects.isNull(deptMap)){
            return new DeptRespDTO();
        }
        DeptRespDTO deptRespDTO = deptMap.get(deptId);
        if (Objects.nonNull(deptRespDTO)){
            return deptRespDTO;
        }
        else {
            deptRespDTO = deptApi.getDept(deptId).getData();
            if (Objects.nonNull(deptRespDTO)){
                deptMap.put(deptRespDTO.getId(),deptRespDTO);
                return deptRespDTO;
            }
            else {
                return new DeptRespDTO();
            }
        }
    }

    @Override
    public String getFlowFlagByConditions(Map<String,Object> variables) {
        String flowFlag = null;
        if(variables.get("firstCondition") != null) {
            if ((Integer)variables.get("firstCondition") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.REJECT.getValue();
            }
            if ((Integer)variables.get("firstCondition") == 2) {
                flowFlag = AffairHandleFlowFlagEnum.INCOMPLETE.getValue();
            }
        }
        if(variables.get("twoCondition")!= null) {
            if ((Integer)variables.get("twoCondition") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.REJECT.getValue();
            }
            if ((Integer)variables.get("twoCondition") == 2) {
                flowFlag = AffairHandleFlowFlagEnum.INCOMPLETE.getValue();
            }
        }
        if(variables.get("leaderCondition")!= null) {
            if ((Integer)variables.get("leaderCondition") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.REJECT.getValue();
            }
            if ((Integer)variables.get("leaderCondition") == 2) {
                flowFlag = AffairHandleFlowFlagEnum.INCOMPLETE.getValue();
            }
        }
        if(variables.get("mainFacility")!= null) {
            if ((Integer)variables.get("mainFacility") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.REJECT.getValue();
            }
            if ((Integer)variables.get("mainFacility") == 2) {
                flowFlag = AffairHandleFlowFlagEnum.INCOMPLETE.getValue();
            }
        }
        if(variables.get("approveCondition")!= null) {
            if ((Integer)variables.get("approveCondition") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.REJECT.getValue();
            }
            if ((Integer)variables.get("approveCondition") == 2) {
                flowFlag = AffairHandleFlowFlagEnum.INCOMPLETE.getValue();
            }
        }
        if(variables.get("thirdCondition")!= null) {
            if ((Integer)variables.get("thirdCondition") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.BACK.getValue();
            }
        }
        if(variables.get("distributeCondition") != null) {
            if ((Integer)variables.get("distributeCondition") == 0) {
                flowFlag = AffairHandleFlowFlagEnum.REJECT.getValue();
            }
            if ((Integer)variables.get("distributeCondition") == 2) {
                flowFlag = AffairHandleFlowFlagEnum.INCOMPLETE.getValue();
            }
        }
        return flowFlag;
    }

    /**
     * 把督办状态置空
     * */
    public void setSuperviseStatusToNull(Long id) {
        taskExtMapper.setSuperviseStatusToNull(id);
    }

}
