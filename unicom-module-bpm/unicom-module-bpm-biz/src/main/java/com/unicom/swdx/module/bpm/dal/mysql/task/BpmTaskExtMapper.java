package com.unicom.swdx.module.bpm.dal.mysql.task;

import cn.hutool.core.collection.CollectionUtil;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

@Mapper
public interface BpmTaskExtMapper extends BaseMapperX<BpmTaskExtDO> {

    default void updateByTaskId(BpmTaskExtDO entity) {
        update(entity, new LambdaQueryWrapper<BpmTaskExtDO>().eq(BpmTaskExtDO::getTaskId, entity.getTaskId()));
    }

    default List<BpmTaskExtDO> selectListByTaskIds(Collection<String> taskIds) {
        if(CollectionUtil.isEmpty(taskIds)){
            return null;
        }
        return selectList(BpmTaskExtDO::getTaskId, taskIds);
    }

    default BpmTaskExtDO selectByTaskId(String taskId) {
        return selectOne(BpmTaskExtDO::getTaskId, taskId);
    }

    default List<BpmTaskExtDO> selectByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper lambdaQueryWrapper =  new LambdaQueryWrapper<BpmTaskExtDO>()
                .eq(BpmTaskExtDO::getProcessInstanceId, processInstanceId)
                .eq(BpmTaskExtDO::getResult,"1");
        return selectList(lambdaQueryWrapper);
    }

    @Update("update BPM_TASK_EXT set SUPERVISE_STATUS = null where ID = #{id}")
    void setSuperviseStatusToNull(@Param("id") Long id);

}
