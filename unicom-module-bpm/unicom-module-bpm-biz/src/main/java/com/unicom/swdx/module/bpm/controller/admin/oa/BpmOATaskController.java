package com.unicom.swdx.module.bpm.controller.admin.oa;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.*;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceMyPageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstancePageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskDonePageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskTodoPageReqVO;
import com.unicom.swdx.module.bpm.enums.definition.SubSystemSecrectEnum;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.service.oa.BpmOATaskService;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.ArrayList;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Api(tags = "管理后台 - OA 审批中心")
@RestController
@RequestMapping("/bpm/oa/task")
@Validated
public class BpmOATaskController {

    @Resource
    private BpmOATaskService taskService;

    @Resource
    private BpmOATaskService bpmTaskService;

    @Resource
    private BpmProcessInstanceService bpmProcessInstanceService;

    @GetMapping("todo-page")
    @ApiOperation("获取Todo待审批任务分页")
    public CommonResult<PageResult<BpmOATaskTodoPageItemRespVO>> getTodoTaskPage(@Valid BpmOATaskTodoPageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        return success(taskService.getTodoTaskPage(getLoginUserId(), pageVO));
    }


    @GetMapping("/my-page")
    @ApiOperation(value = "获得我的实例分页列表", notes = "在【我的流程】菜单中，进行调用")
    public CommonResult<PageResult<BpmProcessInstancePageItemRespVO>> getMyProcessInstancePage(
            @Valid BpmProcessInstanceMyPageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        return success(taskService.getMyProcessInstancePage(SecurityFrameworkUtils.getLoginUserId(), pageVO));
    }


    @GetMapping("done-page")
    @ApiOperation("获取Done已审批任务分页")
    public CommonResult<PageResult<BpmOATaskDonePageItemRespVO>> getDoneTaskPage(@Valid BpmOATaskDonePageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        PageResult<BpmOATaskDonePageItemRespVO> res = taskService.getDoneTaskPageWithFlowFlag(getLoginUserId(), pageVO);


        res.getList().forEach( x -> {
            if ( StringUtils.isNotEmpty(x.getRevokeStatus()) ) {
                if ( "2".equals(x.getRevokeStatus()) ){
                    x.setResult(BpmProcessInstanceResultEnum.BACK.getResult());
                }
                else if ( "1".equals(x.getRevokeStatus()) ) {
                    x.setResult(BpmProcessInstanceResultEnum.WITHDRAW.getResult());
                }
            }

        });

        return success(res);
    }

    @GetMapping("/done-list-page")
    @ApiOperation("获取Done已审批任务分页")
    public CommonResult<PageResult<BpmTodoListPageVO>> getDoneListPage(@Valid BpmOATaskDonePageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        PageResult<BpmTodoListPageVO> donePage = taskService.getDonePage(getLoginUserId(), pageVO);

        return success(donePage);
    }

    @GetMapping("/list-pageDoneAll")
    @ApiOperation("获得已办事项分页")
    public CommonResult<PageResult<BpmDoneListPageAllVO>> getDoneListPageALL(@Valid BpmOATaskDonePageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        PageResult<BpmDoneListPageAllVO> donePageAll = taskService.getDonePageAll(getLoginUserId(), pageVO);
        return  success(donePageAll);
    }

    @GetMapping("/AppDone-list-page")
    @ApiOperation("获取APP已办任务分页")
    public CommonResult<PageResult<BpmTodoListPageVO>> getAppDoneListPage(@Valid BpmOATaskDonePageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        PageResult<BpmTodoListPageVO> donePage = taskService.getAppDonePage(getLoginUserId(), pageVO);

        return success(donePage);
    }

    @GetMapping("xcx-todo-page")
    @ApiOperation("小程序获取Todo待审批任务分页")
    public CommonResult<PageResult<BpmOATaskTodoPageItemRespVO>> getXcxTodoTaskPage(@Valid BpmOATaskTodoPageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        return success(taskService.getXcxTodoTaskPage(getLoginUserId(), pageVO));
    }


    @GetMapping("/xcx-my-page")
    @ApiOperation(value = "小程序获得我的实例分页列表", notes = "在【我的流程】菜单中，进行调用")
    public CommonResult<PageResult<BpmProcessInstancePageItemRespVO>> getXcxMyProcessInstancePage(
            @Valid BpmProcessInstanceMyPageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        return success(taskService.getXcxMyProcessInstancePage(SecurityFrameworkUtils.getLoginUserId(), pageVO));
    }


    @GetMapping("xcx-done-page")
    @ApiOperation("小程序获取Done已审批任务分页")
    public CommonResult<PageResult<BpmOATaskDonePageItemRespVO>> getXcxDoneTaskPage(@Valid BpmOATaskDonePageReqVO pageVO) {
        pageVO.setSource(SubSystemSecrectEnum.OA.getSource());
        PageResult<BpmOATaskDonePageItemRespVO> res = taskService.getXcxDoneTaskPageWithFlowFlag(getLoginUserId(), pageVO);


        res.getList().forEach( x -> {
            if ( StringUtils.isNotEmpty(x.getRevokeStatus()) ) {
                if ( "2".equals(x.getRevokeStatus()) ){
                    x.setResult(BpmProcessInstanceResultEnum.BACK.getResult());
                }
                else if ( "1".equals(x.getRevokeStatus()) ) {
                    x.setResult(BpmProcessInstanceResultEnum.WITHDRAW.getResult());
                }
            }

        });

        return success(res);
    }

    @GetMapping("/list-by-process-instance-id")
    @ApiOperation(value = "获得指定流程实例的任务列表", notes = "包括完成的、未完成的")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
    public CommonResult<List<BpmTaskRespVO>> getTaskListByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        return success(taskService.getTaskListByProcessInstanceId(processInstanceId));
    }


}
