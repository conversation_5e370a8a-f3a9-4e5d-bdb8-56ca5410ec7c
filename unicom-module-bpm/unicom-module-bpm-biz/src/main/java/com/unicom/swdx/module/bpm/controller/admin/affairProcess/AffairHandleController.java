package com.unicom.swdx.module.bpm.controller.admin.affairProcess;

//import com.unicom.tyj.module.affair.api.general.notification.AppNotificationApi;
//import com.unicom.tyj.module.affair.api.general.notification.dto.AppNotificationCreateDTO;
//import com.unicom.tyj.module.affair.api.handle.AffairHandleApi;
//import com.unicom.tyj.module.affair.api.handle.dto.AffairHandleDTO;
//import com.unicom.tyj.module.affair.api.handle.dto.FileAttachmentDTO;
//import com.unicom.tyj.module.affair.enums.AppNoticeTypeEnum;
//import com.unicom.tyj.module.affair.enums.DocRemindType;
import io.swagger.annotations.Api;
        import lombok.extern.slf4j.Slf4j;
        import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

        import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
        import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Api(tags = "政务子系统 - 政务办理")
@RestController
@RequestMapping("/bpm/affair-handle-process")
@Validated
@Slf4j
public class AffairHandleController {
//    @Resource
//    private BpmTaskService taskService;
//
//    @Resource
//    private BpmProcessInstanceService processInstanceService;
//
//    @Resource
//    private BpmTaskService bpmTaskService;
//
//    @Resource
//    private BpmProcessDefinitionService bpmProcessDefinitionService;
//
//
//    @Resource
//    private AdminUserApi adminUserApi;
//
//    @Autowired
//    private SmsSendApi smsSendApi;
//
////    @Resource
////    private AffairHandleApi affairHandleCallApi;
//
////    @Resource
////    private AppNotificationApi appNotificationApi;
//
//    @GetMapping("todo-page")
//    @ApiOperation("获取 Todo 待审批分页")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> getTodoTaskPage(@Valid BpmTaskPageReqVO pageReqVO) {
//        if (pageReqVO.getIsFindAll() != null && pageReqVO.getIsFindAll()) {//督办页面标记
//            PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getTodoAffairHandleTaskPage(null, pageReqVO);
//            affairTaskPage.getList().forEach(a -> a.setSuperviseStatus(null));//督办页面不展示督办标签
//            return success(affairTaskPage);
//        } else {
//            PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getTodoAffairHandleTaskPage(getLoginUserId(), pageReqVO);
//            return success(affairTaskPage);
//        }
//
//    }
//
//    @GetMapping("done-page")
//    @ApiOperation("获取 Done 已审批分页")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> getDoneTaskPage(@Valid BpmTaskPageReqVO pageReqVO) {
//        PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getDoneAffairHandleTaskPage(getLoginUserId(), pageReqVO);
//        return success(affairTaskPage);
//    }
//
//
//    @GetMapping("supervise-page")
//    @ApiOperation("获取督办分页")
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<PageResult<BpmTaskPageItemRespVO>> getSupervisePage(@Valid BpmTaskPageReqVO pageReqVO) {
//        PageResult<BpmTaskPageItemRespVO> affairTaskPage = taskService.getSuperviseAffairHandleTaskPage(null, pageReqVO);
//        return success(affairTaskPage);
//    }
//
//
//    @GetMapping("/export-done")
//    @ApiOperation("导出Done 已审批分页")
//    @PreAuthorize("@ss.hasPermission('affair:business-handle:export')")
//    @OperateLog(type = EXPORT)
//    public void exportDoneTaskPage(@Valid DoneTaskExportReqVO exportReqVO,
//                                   HttpServletResponse response) throws IOException {
//        List<DoneTaskExcelVO> list = taskService.getDoneTaskList(getLoginUserId(), exportReqVO);
//        String filename = "业务列表_已审批_" + DateUtils.datestamp() + ".xls";
//        ExcelUtils.write(response, filename, "数据", DoneTaskExcelVO.class, list);
//    }
//
//    @GetMapping("/export-todo")
//    @ApiOperation("导出todo 待审批分页")
//    @PreAuthorize("@ss.hasPermission('affair:business-handle:export')")
//    @OperateLog(type = EXPORT)
//    public void exportDoneTaskPage(@Valid TodoTaskExportReqVO exportReqVO,
//                                   HttpServletResponse response) throws IOException {
//        List<TodoTaskExcelVO> list = taskService.getTodoTaskList(getLoginUserId(), exportReqVO);
//        String filename = "业务列表_待审批_" + DateUtils.datestamp() + ".xls";
//        ExcelUtils.write(response, filename, "数据", TodoTaskExcelVO.class, list);
//    }
//
//    //通过
//    @PostMapping("/approve-task")
//    @ApiOperation("通过任务")
//    @PreAuthorize("@ss.hasPermission('bpm:task:create')")
//    public CommonResult<Boolean> approveTask(@Valid @RequestBody BpmTaskApproveReqVO bpmTaskApproveReqVO) {
//        BpmTaskExtDTO task = taskService.getTaskByInstanceId(getLoginUserId(), bpmTaskApproveReqVO.getProcessInstanceId());
//        if (Objects.isNull(task)) {
//            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
//        }
//
//        bpmTaskApproveReqVO.setId(task.getTaskId());
//        BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(bpmTaskApproveReqVO.getProcessInstanceId());
//
//        //设置flowFlag
//        String flowFlag = bpmTaskService.getFlowFlagByConditions(bpmTaskApproveReqVO.getVariables());
//        // 更新待办
//        //现根据已办流程的flowflag确定已办推送的状态
//        if (Objects.isNull(flowFlag)) {
//            taskService.pushDoneDTOtoMidOffice(tempProcess.getId(), tempProcess.getProcessKey(),
//                    BpmProcessInstanceResultEnum.APPROVE.getResult());
//        } else if (flowFlag.equals(AffairHandleFlowFlagEnum.REJECT.getValue()) || flowFlag.equals(AffairHandleFlowFlagEnum.INCOMPLETE.getValue())) {
//            taskService.pushDoneDTOtoMidOffice(tempProcess.getId(), tempProcess.getProcessKey(),
//                    BpmProcessInstanceResultEnum.REJECT.getResult());
//        } else if (flowFlag.equals(AffairHandleFlowFlagEnum.BACK.getValue())) {
//            taskService.pushDoneDTOtoMidOffice(tempProcess.getId(), tempProcess.getProcessKey(),
//                    BpmProcessInstanceResultEnum.REJECT.getResult());
//        }
//        // 审批通过
//        taskService.approveTask(getLoginUserId(), bpmTaskApproveReqVO);
//
//        //更新下个任务的部门
//        List<AdminUserRespDTO> userRespDTOS = taskService.getTasksAssigneeByProcessInstanceId(bpmTaskApproveReqVO.getProcessInstanceId());
//        if (!userRespDTOS.isEmpty()) {
//            BpmTaskExtDO nextTask = taskService.getProcessTasksByProcessInstanceId(bpmTaskApproveReqVO.getProcessInstanceId());
//            nextTask.setAssigneeDeptId(userRespDTOS.get(0).getDeptId());
//            taskService.updateTaskExtDO(nextTask);
//        }
//
//
//        //添加文件
//
//        if (bpmTaskApproveReqVO.getFileAttachments() != null) {
//
//
//            if (tempProcess.getFormVariables().get("fileAttachments") == null) {
//                tempProcess.getFormVariables().put("fileAttachments", new JSONArray());
//            }
//            JSONArray fileAttachments = JSONArray.parseArray(JSON.toJSONString(tempProcess.getFormVariables().get("fileAttachments")));
//
//            if (bpmTaskApproveReqVO.getVariables().get("type") != null && bpmTaskApproveReqVO.getVariables().get("type").equals("handle")) {
//
//                for (int i = fileAttachments.size() - 1; i >= 0; i--) {
//                    if (fileAttachments.getJSONObject(i).get("bussiType").toString().equals("handle")) {
//                        fileAttachments.remove(i);
//                    }
//                }
//
//            }
//
//
//            //JSONArray fileAttachments = (JSONArray) (tempProcess.getFormVariables().get("fileAttachments"));
//
//            fileAttachments.addAll(JSONArray.parseArray(JSON.toJSONString(bpmTaskApproveReqVO.getFileAttachments())));
//            tempProcess.getFormVariables().put("fileAttachments", fileAttachments);
//            BpmProcessInstanceUpdateReqDTO b = new BpmProcessInstanceUpdateReqDTO();
//            b.setProcessInstanceId(bpmTaskApproveReqVO.getProcessInstanceId());
//            b.setVariables(tempProcess.getFormVariables());
//            processInstanceService.updateProcessInstance(b);
//        }
//
//
//        processInstanceService.updateProcessInstanceExtFlowFlag(bpmTaskApproveReqVO.getProcessInstanceId(), flowFlag);
//
//
//        //如果还有下一个节点则推送下一节点的待办
//        if (!userRespDTOS.isEmpty()) {
//            taskService.pushNewTodoToMidOffice(tempProcess.getId(), tempProcess.getProcessKey(),
//                    BpmProcessInstanceResultEnum.PROCESS.getResult());
//        }
//
//
//        // 审批日志
//        taskService.saveAffairApproveHandleLog(getLoginUserId(), bpmTaskApproveReqVO, flowFlag);
//
//
//        // 图片签名
//        if (StringUtils.isNotEmpty(bpmTaskApproveReqVO.getImageUrl())) {
//            BpmTaskExtDO bpmTaskExtDO = new BpmTaskExtDO();
//            bpmTaskExtDO.setImageUrl(bpmTaskApproveReqVO.getImageUrl());
//            bpmTaskExtDO.setTaskId(task.getTaskId());
//            bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//        }
//
//        // 推送到政务办理平台
//        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(getLoginUserId()).getCheckedData();
////        AffairHandleDTO affairHandleDTO = new AffairHandleDTO();
////        affairHandleDTO.setUserName(adminUserRespDTO.getNickname());
////        affairHandleDTO.setBeginTime(task.getCreateTime());
////        affairHandleDTO.setFormVariables(tempProcess.getFormVariables());
////        affairHandleDTO.setCdOperation("I");
////        affairHandleDTO.setOpinion(bpmTaskApproveReqVO.getReason());
////        affairHandleDTO.setOpinionTime(LocalDateTime.now());
////        affairHandleDTO.setTacheName(task.getName());
////        affairHandleDTO.setFlag("0");
////        affairHandleDTO.setResult("1");
////        List<AdminUserRespDTO> respDTOS = bpmTaskService.getTasksAssigneeByProcessInstanceId(bpmTaskApproveReqVO.getProcessInstanceId());
////        if ("3".equals(flowFlag) || "4".equals(flowFlag)) {
////            affairHandleDTO.setResult("0");
////            affairHandleDTO.setFlag("1");
////        } else if (CollectionUtils.isAnyEmpty(respDTOS)) {
////            affairHandleDTO.setResult("1");
////            affairHandleDTO.setFlag("1");
////        }
////        // 文件
////        if (!CollectionUtils.isAnyEmpty(bpmTaskApproveReqVO.getFileAttachments())) {
////            List<FileAttachmentDTO> dtos = BeanUtil.copyToList(bpmTaskApproveReqVO.getFileAttachments(), FileAttachmentDTO.class);
////            affairHandleDTO.setFileAttachmentDTOS(dtos);
////        }
////        affairHandleCallApi.handleResultCallBack(affairHandleDTO);
//        return success(true);
//
//    }
//
//
//    //不予办理
//    @PostMapping("/reject-task")
//    @ApiOperation("驳回任务")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<Boolean> taskReject(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {
//        String taskId = taskService.getTaskIdByInstanceId(getLoginUserId(), reqVO.getProcessInstanceId());
//        if (StringUtils.isEmpty(taskId)) {
//            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
//        }
//        reqVO.setId(taskId);
//        bpmTaskService.rejectTask(getLoginUserId(), reqVO);
//        // 驳回审批意见
//        //taskService.rejectTaskAndComment(reqVO);
//        BpmTaskVO reqTask = new BpmTaskVO();
//        reqTask.setProcessInstanceId(reqVO.getProcessInstanceId());
//        reqTask.setTaskId(reqVO.getProcessInstanceId());
//        reqTask.setComment(reqVO.getReason());
//        taskService.saveAffairRejectHandleLog(getLoginUserId(), reqTask);
//        taskService.pushTodoStatus(TodoType.AFFAIR.getId(), null, SubSystemEnum.GOVERNMENT.getId(), reqVO.getProcessInstanceId(), null, BpmProcessInstanceResultEnum.BACK.getResult());
//        processInstanceService.updateProcessInstanceExtFlowFlag(reqVO.getProcessInstanceId(), "2");
//        return success(true);
//    }
//
//    //获取流程实例详情
//    @GetMapping("/getDetail")
//    @ApiOperation("获取流程实例详情")
//    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
//    public CommonResult<BpmProcessInstanceRespVO> getDetail(@RequestParam("processInstanceId") String processInstanceId) {
//        BpmProcessInstanceRespVO bpmProcessInstanceRespVO = processInstanceService.getProcessInstanceVO(processInstanceId);
//        List<BpmTaskExtDO> bpmTaskExtDOS = taskService.getDetailTasks(processInstanceId);
//        if (CollUtil.isNotEmpty(bpmTaskExtDOS)) {
//            List<BpmTaskExtDO> doList = bpmTaskExtDOS.stream().filter(bpmTaskExtDO -> getLoginUserId().equals(bpmTaskExtDO.getAssigneeUserId())).collect(Collectors.toList());
//            if (CollUtil.isNotEmpty(doList)) {
//                bpmProcessInstanceRespVO.setTaskDefKey(doList.get(0).getTaskDefKey());
//                bpmProcessInstanceRespVO.setTaskId(doList.get(0).getTaskId());
//            }
//        }
//        List<String> oneData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("1");
//        List<String> twoData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("2");
//        List<String> threeData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("3");
//        List<String> fourthData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("4");
//        List<String> fiveData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("5");
//        List<String> sixData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("6");
//        List<String> sevenData = AffairHandlerProcessTaskCodeEnum.getTypeHandleTaskCode("7");
//
//        if (oneData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("firstCondition");
//        } else if (twoData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("twoCondition");
//        } else if (threeData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("leaderCondition");
//        } else if (fourthData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("approveCondition");
//        } else if (fiveData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("mainFacility");
//        } else if (sixData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("distributeCondition");
//        } else if (sevenData.contains(bpmProcessInstanceRespVO.getTaskDefKey())) {
//            bpmProcessInstanceRespVO.setParamCondition("thirdCondition");
//        }
//        bpmProcessInstanceRespVO.setProcessType(AffairHandleProcessNameTypeEnum.getSubjectByType(bpmProcessInstanceRespVO.getProcessKey()).getSubject());
//
//        return success(bpmProcessInstanceRespVO);
//    }
//
//    @GetMapping("/get-handle-Count")
//    @ApiOperation("政务办理查询数量")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataTypeClass = Long.class),
//    })
//    @PreAuthorize("@ss.hasPermission('bpm:task:query')")
//    public CommonResult<Map<String, Object>> getHandleCount(@RequestParam("userId") Long userId) {
//        Map<String, Object> handleTaskCount = bpmTaskService.getHandleCount(userId);
//        return success(handleTaskCount);
//    }
//
//    //通过
//    @PostMapping("/create-process")
//    @ApiOperation("发起流程-测试")
//    @PreAuthorize("@ss.hasPermission('bpm:task:create')")
//    public CommonResult<Boolean> createProcess(@Valid @RequestBody BpmProcessInstanceCreateReqVO bpmProcessInstanceCreateReqVO) {
//        ProcessDefinition processDefinition = bpmProcessDefinitionService.getActiveProcessDefinition(bpmProcessInstanceCreateReqVO.getProcessDefinitionKey());
//        if (processDefinition == null) {
//            return error(400, "流程实例未找到");
//        }
//        bpmProcessInstanceCreateReqVO.setProcessDefinitionId(processDefinition.getId());
//        String instanceId = processInstanceService.createProcessInstance(getLoginUserId(), bpmProcessInstanceCreateReqVO);
//        //保存发起日志
//        taskService.saveCreateHandleLog1(getLoginUserId(), bpmProcessInstanceCreateReqVO, instanceId);
//        // 跳过第一个申请节点
//        processInstanceService.skipFirstTask(instanceId);
//        //保存发起日志
//        taskService.saveAffairCreateHandleLog(getLoginUserId(), bpmProcessInstanceCreateReqVO, instanceId);
//        //推送第一个节点的待办
//        taskService.pushNewTodoToMidOffice(instanceId, bpmProcessInstanceCreateReqVO.getProcessDefinitionKey(), BpmProcessInstanceResultEnum.PROCESS.getResult());
//        return success(true);
//    }
//
//
//    @GetMapping("/supervision-task")
//    @ApiOperation("督办任务")
//    @PreAuthorize("@ss.hasPermission('affair:business-handle:supervision')")
//    public CommonResult<Boolean> supervisionTask(@RequestParam("processInstanceId") String processInstanceId) {
//        List<AdminUserRespDTO> assignees = taskService.getTasksAssigneeByProcessInstanceId(processInstanceId);
//        ProcessInstance processInstance = processInstanceService.getProcessInstance(processInstanceId);
//
//
//        BpmTaskExtDO bpmTaskExtDO = bpmTaskService.getProcessTasksByProcessInstanceId(processInstanceId);
//
//        LocalDateTime lastSuperviseTime = bpmTaskExtDO.getSuperviseTime();
//        if (Objects.nonNull(lastSuperviseTime)) {
//            if (LocalDateTime.now().isBefore(lastSuperviseTime.plusHours(24))) {
//                throw exception(AFFAIR_HANDLE_LAST_SUPERVISE_IN_24H, AffairHandleTaskNameCodeEnum.getByCode(bpmTaskExtDO.getTaskDefKey()).getSuperviseType());
//            }
//        }
//
//        if (AffairHandleTaskNameCodeEnum.getByCode(bpmTaskExtDO.getTaskDefKey()).getSuperviseType().equals("督办")) {
//            if (Objects.isNull(bpmTaskExtDO.getSuperviseStatus()) || bpmTaskExtDO.getSuperviseStatus().equals(DocRemindType.SUPERVISE.getRemindType())) {
//                bpmTaskExtDO.setSuperviseStatus(DocRemindType.SUPERVISE.getRemindType());
//            } else if (bpmTaskExtDO.getSuperviseStatus().equals(DocRemindType.SUPERVISE_EXCEED.getRemindType()) || bpmTaskExtDO.getSuperviseStatus().equals(DocRemindType.EXCEED.getRemindType())) {
//                bpmTaskExtDO.setSuperviseStatus(DocRemindType.SUPERVISE_EXCEED.getRemindType());
//            }
//
//            bpmTaskExtDO.setSuperviseCount(bpmTaskExtDO.getSuperviseCount() + 1);
//        }
//        if (AffairHandleTaskNameCodeEnum.getByCode(bpmTaskExtDO.getTaskDefKey()).getSuperviseType().equals("提醒")) {
//            if (Objects.isNull(bpmTaskExtDO.getSuperviseStatus()) || bpmTaskExtDO.getSuperviseStatus().equals(DocRemindType.REMIND.getRemindType())) {
//                bpmTaskExtDO.setSuperviseStatus(DocRemindType.REMIND.getRemindType());
//            } else if (bpmTaskExtDO.getSuperviseStatus().equals(DocRemindType.REMIND_EXCEED.getRemindType()) || bpmTaskExtDO.getSuperviseStatus().equals(DocRemindType.EXCEED.getRemindType())) {
//                bpmTaskExtDO.setSuperviseStatus(DocRemindType.REMIND_EXCEED.getRemindType());
//            }
//            bpmTaskExtDO.setRemindCount(bpmTaskExtDO.getRemindCount() + 1);
//        }
//
//        bpmTaskExtDO.setSuperviseTime(LocalDateTime.now());
//        bpmTaskService.updateTaskExtDO(bpmTaskExtDO);
//
//        BpmProcessInstanceRespVO tempProcess = processInstanceService.getProcessInstanceVO(processInstanceId);
//        assignees.forEach(assignee -> {
//
//                    if (tempProcess != null) {
//
//                        AppNotificationCreateDTO appNotificationCreateDTO = new AppNotificationCreateDTO();
//                        appNotificationCreateDTO.setBizId(tempProcess.getId());
//                        appNotificationCreateDTO.setBizType(tempProcess.getProcessKey());
//                        appNotificationCreateDTO.setBizTaskDefKey(bpmTaskExtDO.getTaskDefKey());
//                        Set<Long> targetUserIds = Collections.singleton(bpmTaskExtDO.getAssigneeUserId());
//                        if (targetUserIds == null) {
//                            log.info("流程实例对应办理人为空：{}", bpmTaskExtDO.getProcessInstanceId());
//                            return;
//                        }
//                        appNotificationCreateDTO.setTargetUserIds(targetUserIds);
//                        //appNotificationCreateDTO.setTitle(superviseTodoTaskPO.getTaskName());
//                        appNotificationCreateDTO.setNoticeTypeEnum(AppNoticeTypeEnum.SUPERVISE_NOTICE);
//                        CommonResult<Boolean> createNoticeRes = appNotificationApi.createUnread(appNotificationCreateDTO);
//                        if (createNoticeRes.isError()) {
//                            log.info(createNoticeRes.getMsg());
//                        } else {
//                            log.info("app督办提醒通知发送成功, 流程实例ID：{}, 流程实例名称：{}, 发送人员：{}", tempProcess.getId(), tempProcess.getName(), targetUserIds);
//                        }
//                    }
//
//                    AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(assignee.getId()).getCheckedData();
//                    if (adminUserRespDTO != null) {
//                        SmsSendSingleToUserReqDTO smsSendSingleToUserReqDTO = new SmsSendSingleToUserReqDTO();
//                        smsSendSingleToUserReqDTO.setMobile(adminUserRespDTO.getMobile());
//                        smsSendSingleToUserReqDTO.setTemplateCode("dubantixing");
//                        smsSendSingleToUserReqDTO.setUserId(adminUserRespDTO.getId());
//                        Map<String, Object> content = new HashMap<>();
//                        //content.put("arg0","待审批");A
//                        content.put("arg1", processInstance.getName());
//                        smsSendSingleToUserReqDTO.setTemplateParams(content);
//                        try {
//                            smsSendApi.sendSingleSmsToAdmin(smsSendSingleToUserReqDTO).getCheckedData();
//                        } catch (Exception e) {
//                            throw e;
//                        }
//
//                        log.info("短信发送成功, 流程实例ID：{}, 发送人员：{}", processInstanceId, assignee.getId());
//                    } else {
//                        log.info("人员数据为空：流程实例ID：{}", processInstanceId);
//                    }
//                }
//
//        );
//        return success(true);
//    }


}
