package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;


import com.unicom.swdx.framework.common.pojo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("APP获得二级报表统计列表 Response VO")
@Data
@ToString(callSuper = true)
public class AppAffairHandleSecondLevelReportRespVO {



    @ApiModelProperty(value = "政务办理报表总结")
    PageResult<AffairHandleSecondLevelReportBaseVO> reportList;

    @ApiModelProperty(value = "是否局领导")
    Boolean isBureauLeaderRole;

    public AppAffairHandleSecondLevelReportRespVO()
    {
    }


}
