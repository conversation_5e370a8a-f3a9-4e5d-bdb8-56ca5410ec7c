package com.unicom.swdx.module.bpm.listener;

import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskExtDTO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEvent;
import com.unicom.swdx.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEventListener;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.oa.api.LeaveApi;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.enums.ProcessDefinitionKeyConstants;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.LEAVE_END_TIME_BEFORE_START_TIME;

@Component
@Slf4j
public class OALeaveResultListener extends BpmProcessInstanceResultEventListener {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private ReceiveApi receiveApi;

    @Resource
    private LeaveApi leaveApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private PostApi postApi;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Override
    protected String getProcessDefinitionKey() {
        return ProcessDefinitionKeyConstants.LEAVE_KEY;
    }

    @Override
    @SneakyThrows
    protected void onEvent(BpmProcessInstanceResultEvent event) {
        Thread.sleep(2000);

        if(Objects.equals(event.getResult(), BpmProcessInstanceResultEnum.APPROVE.getResult())) {
            String id = event.getId();
            log.info("开始抄送请假流程，id = {}", id);

            BpmProcessInstanceRespVO processInstanceVO = processInstanceService.getProcessInstanceVO(id);
            LeaveDTO leaveDTO = leaveApi.getItemId(id);
            // 改变请假表的结果为已完成
            leaveApi.updateResultById(leaveDTO.getId(), BpmProcessInstanceResultEnum.APPROVE.getResult());

            // 抄送给人事处
            ReceiveDTO receiveDTO = new ReceiveDTO();
            receiveDTO.setProcessInstanceId(id);
            receiveDTO.setCategory(processInstanceVO.getCategory());
            receiveDTO.setApplyTime(processInstanceVO.getCreateTime());

            receiveDTO.setItemId(leaveDTO.getId());
            List<Long> userIds = null;
            // 备案给人事处请假负责人（设置一个专门的postType
            userIds = postApi.getUserByPost("oa-hr-leave-deal", leaveDTO.getTenantId());
//        List<AdminUserRespDTO> users = userApi.getUsersByDeptName("组织人事部", leaveDTO.getTenantId()).getCheckedData();
//        userIds = users.stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
            receiveDTO.setUserIds(userIds);
            receiveDTO.setPromoterUserId(processInstanceVO.getStartUser().getId());
//        receiveApi.save(receiveDTO);
            receiveApi.save(receiveDTO);


            // 记录抄送日志到taskExt表
            BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
            bpmTaskExtDTO.setName("人事处备案");
            bpmTaskExtDTO.setTaskDefKey("copyLeaveInfoToHr");
            bpmTaskExtDTO.setTaskId("copyLeaveInfoToHr");
            bpmTaskExtDTO.setResult(2);
            bpmTaskExtDTO.setProcessInstanceId(id);
            bpmTaskExtDTO.setProcessDefinitionId(id);
            LocalDateTime now = LocalDateTime.now();
            bpmTaskExtDTO.setCreateTime(now);
            bpmTaskExtDTO.setEndTime(now);
            bpmTaskExtDTO.setTaskType(4);
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> userDeptMap = new HashMap<>();
            userIds.forEach(userId -> {
                AdminUserRespDTO user = userApi.getUser(userId).getCheckedData();
                String nickname = "";
                String deptName = "";
                if(Objects.nonNull(user)){
                    nickname = user.getNickname();
                    DeptRespDTO dept = deptApi.getDept(user.getDeptId()).getCheckedData();
                    if(Objects.nonNull(dept)){
                        deptName = dept.getName();
                    }
                    userDeptMap.put(nickname,deptName);
                }
            });
            map.put("copyTo", userDeptMap);
            bpmTaskExtDTO.setParamsMap(map);
            bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);

            //添加外出报告时间到日程
            Map<String, LocalDate> dateMap  = leaveApi.getDateById(leaveDTO.getId());
            ScheduleDto scheduleDto = new ScheduleDto();
            //设置发起人id
            scheduleDto.setUserId(processInstanceVO.getStartUser().getId());
            //设置发起时间dateMap.get("startMap")
            LocalDate startDate =dateMap.get("startDate");
            LocalDate endDate =dateMap.get("endDate");
            scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            //传流程id赋值到
            scheduleDto.setProcessInstanceId(id);
            //传类型传10为请假审批
            scheduleDto.setType(10);
            scheduleServiceApi.createScheduleOther(scheduleDto);

            // 如果请假结束时间在今天或今天之前，则直接系统销假
            if (!leaveDTO.getEndTime().toLocalDate().isAfter(LocalDate.now())) {
                LocalDateTime time = now.plusSeconds(1L);

                // 改变请假表为已销假
                LeaveDTO leaveInfo = new LeaveDTO();
                leaveInfo.setId(leaveDTO.getId());

                // 添加销假时间
                leaveInfo.setDealTime(time);

                // 设置为已销假
                leaveInfo.setIsDealt(true);

                leaveApi.updateById(leaveInfo);

                //添加系统销假日志记录
                BpmTaskExtDTO bpmTaskExtDTO1 = new BpmTaskExtDTO();
                bpmTaskExtDTO1.setName("系统销假");
                bpmTaskExtDTO1.setTaskDefKey("system-auto-deal-leave");
                bpmTaskExtDTO1.setTaskId("system-auto-deal-leave");
                bpmTaskExtDTO1.setResult(2);
                bpmTaskExtDTO1.setProcessInstanceId(id);
                bpmTaskExtDTO1.setProcessDefinitionId(id);
                bpmTaskExtDTO1.setCreateTime(time);
                bpmTaskExtDTO1.setEndTime(time);
                bpmTaskExtDTO1.setTaskType(5);
                Map<String, Object> map1 = new HashMap<>();
                map1.put("dealWay", "系统销假");
                map1.put("leaveStart", leaveDTO.getStartTime().toLocalDate().toString());
                map1.put("leaveEnd", leaveDTO.getEndTime().toLocalDate().toString());
                bpmTaskExtDTO1.setParamsMap(map1);
                bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO1);
            }
        }
    }
}
