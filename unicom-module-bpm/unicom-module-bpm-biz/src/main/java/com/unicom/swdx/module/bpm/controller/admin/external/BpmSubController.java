package com.unicom.swdx.module.bpm.controller.admin.external;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.bpm.controller.admin.external.vo.*;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskDonePageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskDonePageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskTodoPageItemRespVO;
import com.unicom.swdx.module.bpm.controller.admin.oa.vo.BpmOATaskTodoPageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.*;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskApproveReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRejectReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import com.unicom.swdx.module.bpm.enums.definition.SubSystemSecrectEnum;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.service.oa.BpmOATaskService;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
import com.unicom.swdx.module.oa.enums.Consts;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.SOURCE_OR_SECRET_ERROR;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.APPROVALS_IS_NULL;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.FLOW_CONF_ERROR;

@Api(tags = "管理后台 - 子系统调用流程实例") //提供给子系统
@RestController
@RequestMapping("/bpm/sub/process")
@Validated
public class BpmSubController {

    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private BpmTaskService bpmTaskService;
    @Resource
    private BpmOATaskService bpmOATaskService;

    @GetMapping("/my-page")
    @ApiOperation(value = "获得我的实例分页列表")
    public CommonResult<PageResult<BpmProcessInstancePageItemRespVO>> getMyProcessInstancePage(
            @Valid BpmProcessInstanceMyPage4SubReqVO pageReqVO) {
        if(!Objects.equals(SubSystemSecrectEnum.getSecretBySource(pageReqVO.getSource()),pageReqVO.getSecret())){
            throw exception(SOURCE_OR_SECRET_ERROR);
        }
        BpmProcessInstanceMyPageReqVO reqVO = new BpmProcessInstanceMyPageReqVO();
        BeanUtil.copyProperties(pageReqVO,reqVO);
        return success(bpmOATaskService.getMyProcessInstancePage(pageReqVO.getUserId(), reqVO));
    }

    @GetMapping("todo-page")
    @ApiOperation("获取Todo待审批任务分页")
    public CommonResult<PageResult<BpmOATaskTodoPageItemRespVO>> getTodoTaskPage(
            @Valid BpmTodoTaskPage4SubReqVO pageVO) {
        if(!Objects.equals(SubSystemSecrectEnum.getSecretBySource(pageVO.getSource()),pageVO.getSecret())){
            throw exception(SOURCE_OR_SECRET_ERROR);
        }
        BpmOATaskTodoPageReqVO reqVO = new BpmOATaskTodoPageReqVO();
        BeanUtil.copyProperties(pageVO, reqVO);
        return success(bpmOATaskService.getTodoTaskPage(pageVO.getUserId(), reqVO));
    }

    @GetMapping("done-page")
    @ApiOperation("获取Done已审批任务分页")
    public CommonResult<PageResult<BpmOATaskDonePageItemRespVO>> getDoneTaskPage(@Valid BpmDoneTaskPage4SubReqVO pageVO) {
        if(!Objects.equals(SubSystemSecrectEnum.getSecretBySource(pageVO.getSource()),pageVO.getSecret())){
            throw exception(SOURCE_OR_SECRET_ERROR);
        }
        BpmOATaskDonePageReqVO reqVO = new BpmOATaskDonePageReqVO();
        BeanUtil.copyProperties(pageVO, reqVO);

        PageResult<BpmOATaskDonePageItemRespVO> res = bpmOATaskService.getDoneTaskPageWithFlowFlag(pageVO.getUserId(), reqVO);

        res.getList().forEach( x -> {
            if ( StringUtils.isNotEmpty(x.getRevokeStatus()) ) {
                if ( "2".equals(x.getRevokeStatus()) ){
                    x.setResult(BpmProcessInstanceResultEnum.BACK.getResult());
                }
                else if ( "1".equals(x.getRevokeStatus()) ) {
                    x.setResult(BpmProcessInstanceResultEnum.WITHDRAW.getResult());
                }
            }
        });

        return success(res);
    }

    @PostMapping("/create")
    @ApiOperation("子系统发起流程")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:create')")
    public CommonResult<String> createProcessInstance4Ex(@Valid @RequestBody BpmProcessInstanceCreate4SubReqVO createReqVO) {
        if(!Objects.equals(SubSystemSecrectEnum.getSecretBySource(createReqVO.getSource()),createReqVO.getSecret())){
            throw exception(SOURCE_OR_SECRET_ERROR);
        }
        BpmProcessInstanceCreateReqVO reqVO = new  BpmProcessInstanceCreateReqVO();
        BeanUtils.copyProperties(createReqVO, reqVO);
        return success(processInstanceService.createProcessInstance(createReqVO.getUserId(), reqVO));
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "取消流程实例", notes = "撤回发起的流程")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:create')")
    public CommonResult<String> cancelProcessInstance(@Valid @RequestBody BpmProcessInstanceCancelReqVO cancelReqVO) {
        return success(processInstanceService.cancelProcessInstance(cancelReqVO.getUserId(), cancelReqVO));
    }

    @PostMapping("/submitApprovals")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:create')")
    @ApiOperation("提交选择的审批人")
    public CommonResult<Boolean> submitApprovals(@RequestBody BpmSubmitApprovalsReqVO submitApprovalsReqVO) {
        //设置下一任务节点的处理人
        BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
        bpmTaskApproveReqVO.setId(submitApprovalsReqVO.getTaskId());
        bpmTaskApproveReqVO.setTaskType(1);

        Map<String, Object> variables = new HashMap<>();
        variables.put("flag", Consts.ONE); // 多实例标识
        List<Long> approvals = submitApprovalsReqVO.getUserIds();
        if(CollUtil.isEmpty(approvals)){
            throw exception(APPROVALS_IS_NULL);
        }
        variables.put("approvals",approvals);
        variables.put("chargeLeaderSeq",submitApprovalsReqVO.getChargeLeaderSeq());
        if(approvals.size()>1 && Objects.equals(submitApprovalsReqVO.getChargeLeaderSeq(),"2")){
            List<String> unApproved = new ArrayList<>();
            approvals.forEach(u->{
                unApproved.add(u.toString());
            });
            variables.put("unapproved",unApproved);
            //多人会签任务
            variables.put("taskType","多人会签");
        }
        bpmTaskApproveReqVO.setVariables(variables);
        bpmTaskApproveReqVO.setLogParameters(submitApprovalsReqVO.getLogParameters());

        //设置选择审批人这一任务为通过
        try {
            bpmTaskService.approveTask(submitApprovalsReqVO.getUserId(),bpmTaskApproveReqVO);
        }catch (Exception e){
            throw exception(FLOW_CONF_ERROR);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得指定流程实例")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    public CommonResult<BpmProcessInstanceRespVO> getProcessInstance(@RequestParam("processInstanceId") String processInstanceId) {
        return success(processInstanceService.getProcessInstanceVO(processInstanceId));
    }

    @PostMapping("/approve")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    @ApiOperation("审批通过")
    public CommonResult<Boolean> approve(@RequestBody BpmApproveReqVO approveReqVO) {
        BpmTaskApproveReqVO bpmTaskApproveReqVO = new BpmTaskApproveReqVO();
        BeanUtil.copyProperties(approveReqVO,bpmTaskApproveReqVO);
        bpmTaskApproveReqVO.setTaskType(2);
        bpmTaskService.approveTask(approveReqVO.getUserId(), bpmTaskApproveReqVO);
        return success(true);
    }

    @PostMapping("/reject")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    @ApiOperation("审批不通过，直接结束")
    public CommonResult<Boolean> reject(@RequestBody BpmApproveReqVO approveReqVO) {
        BpmTaskRejectReqVO bpmTaskRejectReqVO = new BpmTaskRejectReqVO();
        BeanUtil.copyProperties(approveReqVO,bpmTaskRejectReqVO);
        bpmTaskRejectReqVO.setTaskType(2);
        bpmTaskService.rejectTaskToEnd(approveReqVO.getUserId(), bpmTaskRejectReqVO);
        return success(true);
    }

    @PostMapping("/rejectToStarter")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    @ApiOperation("审批不通过，流程重新回到发起人")
    public CommonResult<Boolean> rejectToStarter(@RequestBody BpmApproveReqVO approveReqVO) {
        BpmTaskRejectReqVO bpmTaskRejectReqVO = new BpmTaskRejectReqVO();
        BeanUtil.copyProperties(approveReqVO,bpmTaskRejectReqVO);
        bpmTaskService.rejectTask(approveReqVO.getUserId(), bpmTaskRejectReqVO);
        return success(true);
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:create')")
    @ApiOperation("驳回到发起人，再重新发起流程")
    public CommonResult<Boolean> restartProcess(@RequestBody BpmProcessInstanceCreate4SubReqVO reqVO) {
        processInstanceService.restartProcess(reqVO.getUserId(), reqVO.getProcessInstanceId(), reqVO.getVariables());
        return success(true);
    }

    @GetMapping("/getTaskListByProcessInstanceId")
    @ApiOperation(value = "获得指定流程实例的任务列表", notes = "包括完成的、未完成的")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    public CommonResult<List<BpmTaskRespVO>> getTaskListByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        List<BpmTaskRespVO> bpmTaskRespVOS = bpmTaskService.getTaskListByProcessInstanceId(processInstanceId);
        return success(bpmTaskRespVOS);
    }

    @GetMapping("/getTask")
    @ApiOperation(value = "获得指定流程的当前进行中的task")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    public CommonResult<BpmTaskRespVO> getTask(@RequestParam("userId") Long userId, @RequestParam("processInstanceId") String processInstanceId) {
        List<BpmTaskRespVO> bpmTasks = bpmTaskService.getTaskListByProcessInstanceId(processInstanceId);
        List<BpmTaskRespVO> list = bpmTasks.stream().filter(t -> t.getResult() == 1).collect(Collectors.toList());
        if(list.isEmpty()){
            return success(null);
        } else if (list.size()==1) {
            return success(list.get(0));
        } else {
            return success(list.stream().filter(t -> Objects.equals(t.getAssigneeUser().getId(),userId)).findFirst().orElse(new BpmTaskRespVO()));
        }
    }

    //    @GetMapping("/skipFirstTask")
//    @ApiOperation(value = "第一个用户任务为发起审批时，则跳过第一个用户任务")
//    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
//    @PreAuthorize("@ss.hasPermission('bpm:process-instance:create')")
//    public CommonResult<String> skipFirstTask(@RequestParam("processInstanceId") String processInstanceId) {
//        List<BpmTaskRespVO> bpmTasks = bpmTaskService.getTaskListByProcessInstanceId(processInstanceId);
//        List<BpmTaskRespVO> list = bpmTasks.stream().filter(t -> t.getResult() == 1).collect(Collectors.toList());
//        if(list.isEmpty()){
//            return success(null);
//        } else if (list.size()==1) {
//            return success(list.get(0).getId());
//        } else {
//            return success(list.stream().filter(t -> Objects.equals(t.getAssigneeUser().getId(),getLoginUserId())).findFirst().orElse(new BpmTaskRespDTO()).getId());
//        }
//    }
}
