package com.unicom.swdx.module.bpm.controller.admin.task.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@ApiModel("管理后台 - 流程实例的创建 Request VO")
@Data
public class BpmProcessInstanceCreateReqVO {

    @ApiModelProperty(value = "发起流程的子系统来源 默认业务中台", required = true, example = "1024")
    @NotBlank
    private String source = "OA";

    @ApiModelProperty(value = "流程定义的编号", example = "1024")
    private String processDefinitionId;

    @ApiModelProperty(value = "流程实例编号", example = "1024")
    private String processInstanceId;

    @ApiModelProperty(value = "流程定义的key，必传", example = "1024")
    //@NotBlank
    private String processDefinitionKey;

    @ApiModelProperty(value = "变量实例")
    private Map<String, Object> variables;

    @ApiModelProperty(value = "业务的唯一标识", notes = "例如请假申请表的编号。通过它，可以查询到对应的实例", example = "1024")
    private String businessKey;

}
