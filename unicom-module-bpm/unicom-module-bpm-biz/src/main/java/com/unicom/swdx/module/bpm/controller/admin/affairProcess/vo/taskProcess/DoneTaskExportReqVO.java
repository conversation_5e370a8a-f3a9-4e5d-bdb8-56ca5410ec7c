package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("政务子系统 - 已办任务导出 Request VO")
@Data
@ToString(callSuper = true)
public class DoneTaskExportReqVO {

    @ApiModelProperty(value = "流程实例名称")
    private String processInstanceName;

    @ApiModelProperty(value = "开始的发起时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime beginStartTime;

    @ApiModelProperty(value = "结束的发起时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endStartTime;

    /**
     * 流程实例名称
     */
    List<String> keys;

    /**
     * 任务节点名称
     */
    List<String> taskDefKeys;

    /**
     * APP查询还是网页查询？
     */
    @ApiModelProperty(value = "Web/APP查询标识 web:网页查询 app:app端查询")
    private String clientTag;

    /**
     * 审批类型
     */
    @ApiModelProperty(value = "审批类型 传processKey如:affair-sending-flow或affair-sending-flow,affair-receiving-flow")
    private List<String> approvalType;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态0:不予办理 1：审批中、2：办理中、3：已同意、4：已驳回、5：已撤销")
    private String approvalStatus;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "收发文已审批，待审批节点",hidden = true)
    private List<String> taskKeyFilterByApprovalStatus;

    /**
     * 督办状态
     */
    @ApiModelProperty(value = "督办状态 1、只看提醒 2、只看督办")
    private String superviseStatus;

    /**
     * 排序类型
     */
    @ApiModelProperty(value = "asc 时间升序/正序 desc 时间降序/倒序")
    private String sortingType;

    @ApiModelProperty(value = "搜索关键词")
    private String appSearchKeywords;

    @ApiModelProperty(value = "事项类型")
    private String processType;

    /**
     * 任务节点名称取非
     */
    List<String> taskDefKeysNo;
}
