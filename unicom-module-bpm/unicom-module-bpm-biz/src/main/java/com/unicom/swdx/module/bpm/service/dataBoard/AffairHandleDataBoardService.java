package com.unicom.swdx.module.bpm.service.dataBoard;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard.*;

import java.time.LocalDate;
import java.util.List;

public interface AffairHandleDataBoardService {
    /**
     * 获取政务办理计数
     *
     * @param reqVO
     * @return 统计信息
     */
    public AffairHandleFirstLevelReportPageRespVO getHandleCount(AffairHandleAppCountReqVO reqVO);


    /**
     * 获取二级政务办理计数
     *
     * @param reqVO
     * @return 统计信息
     */
    public PageResult<AffairHandleSecondLevelReportBaseVO> getSecondHandleCount(AffairHandleAppCountReqVO reqVO);


    /**
     * 获取pc端办理计数
     *
     * @param reqVO
     * @return pc统计信息
     */
    public AffairPCHandleReportPageRespVO getPCHandleCount(AffairHandleCountReqVO reqVO);

    /**
     * 导出pc端办理计数
     *
     * @param reqVO
     * @return pc统计信息
     */
    public List<AffairHandlePCReportExcelVO> exportPCHandleCount(AffairHandleCountReqVO reqVO);

    /**
     * 获取政务办理评价
     *
     * @param processInstanceId
     * @return 评价信息
     */
    public AffairHandleEvaluationRespVO getEvaluation(String processInstanceId);


    /**
     * 获取首页办理计数
     * @param reqVO
     * @return 首页信息
     */
    public AffairHandleHomePageRespVO getHomePageCount(AffairHandleCountReqVO reqVO);

    /**
    * 统计并生成某天PC报表数据
    * @param endDate 日期
    * */
    public void saveCountResultOnTheDay(LocalDate endDate);


    /**
     * 获取pc端二级报表分页
     * @param reqVO
     * @return pc端二级报表分页
     * */
    public PageResult<PCAffairHandleSecondLevelReportPageRespVO> getPCSecondHandleCount(PCAffairHandleSecondLevelReportReqVO reqVO);

    /**
     * 导出pc端二级报表
     * @param reqVO
     * @return pc端二级报表
     * */
    public List<PCAffairHandleSecondLevelReportExcelVO> exportPCSecondHandleCount(PCAffairHandleSecondLevelReportListReqVO reqVO);

    /**
     * 测试用删除所有统计信息
     * */
    public String testDeleteAll();
}



