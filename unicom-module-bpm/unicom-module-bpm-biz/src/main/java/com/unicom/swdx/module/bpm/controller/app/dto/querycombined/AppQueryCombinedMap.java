package com.unicom.swdx.module.bpm.controller.app.dto.querycombined;

import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
//import com.unicom.tyj.module.affair.enums.RecevingTaskCodeEnum;
//import com.unicom.tyj.module.affair.enums.SendingTaskCodeEnum;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.taskProcess.BpmTaskPageReqVO;
import com.unicom.swdx.module.bpm.enums.definition.*;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.ApprovalTypeTagEnum;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.ApproveStatusEnum;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.PageNameEnum;
import com.unicom.swdx.module.bpm.enums.definition.appQuery.SqlInclusionEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.ERROR_APPROVAL_STATUS;
import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.ERROR_APPROVAL_TYPE;

@ApiModel("APP查询 高级筛选条件封装map dto")
@Data
@Slf4j
public class AppQueryCombinedMap {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 高级筛选条件所属页面名称
     */
    private PageNameEnum whichPage;

    /**
     * 流程类型
     * 流程定义Key
     */
    private List<String> processKeyInCombined;

    /**
     * 搜索关键词
     */
    private String searchKeyWords;

    /**
     * 督办状态
     * 1：只看提醒 2：只看督办
     */
    private String superviseStatus;

    /**
     * flowStatus
     * 默认false
     * true:不查询驳回/撤回节点
     */
    private boolean notRejectedAndRevoked;

    /**
     * 审批中状态
     * 1 开启查询审批中的节点
     */
    private String approving;

    /**
     * 审批中状态
     * 2 查询办理中的节点
     */
    private Integer approved;

    /**
     * 驳回/撤回标记
     * 1 撤回
     * 2 驳回
     */
    private Integer flowFlag;

    /**
     * 查询已结束
     */
    private boolean inEnd;

    /**
     * 时间排序规则
     */
    private String sortingType;

    /**
     * 起始时间
     */
    private LocalDateTime beginStartTime;

    /**
     * 截止时间
     */
    private LocalDateTime endStartTime;
    /**
     * 模板SQL约束
     */
    private Map<String, CombinedSqlInclusion> sqlInclusionMap;

    public AppQueryCombinedMap(){
        this.sqlInclusionMap = CombinedSqlInclusion.createMapIncludeAll();
    }

    /**
     * 根据请求体初始化高级筛选
     * @param pageReqVO 页面请求
     * @param pageEnum 请求所属页面
     * @return 初始化后的conditionMap
     */
    public static AppQueryCombinedMap initConditionMap(BpmTaskPageReqVO pageReqVO, PageNameEnum pageEnum){
        AppQueryCombinedMap conditionMap = new AppQueryCombinedMap();
        conditionMap.setWhichPage( pageEnum );
        initCommonCondition( pageReqVO, conditionMap );
        initApproveTypeCondition( pageReqVO, conditionMap);
        initApprovalStatus( pageReqVO, conditionMap );
        initSuperviseStatus( pageReqVO, conditionMap );
        return conditionMap;
    }

    /**
     * 设置通用的条件
     * 搜索关键词条件、排序时间、排序规则
     * @param pageReqVO 页面请求
     */
    private static void initCommonCondition(BpmTaskPageReqVO pageReqVO, AppQueryCombinedMap conditionMap) {
        conditionMap.setSearchKeyWords( pageReqVO.getAppSearchKeywords() );
        if ( StringUtils.isNotEmpty(pageReqVO.getSortingType()) && "asc".equals(pageReqVO.getSortingType()) ){
            conditionMap.setSortingType("asc");
        }
        else {
            conditionMap.setSortingType("desc");
        }
        conditionMap.setBeginStartTime( pageReqVO.getBeginStartTime() );
        conditionMap.setEndStartTime( pageReqVO.getEndStartTime() );
        conditionMap.setNotRejectedAndRevoked(false);
    }

    /**
     * 高级筛选
     * 对审批类型条件进行封装
     */
    private static void initApproveTypeCondition(BpmTaskPageReqVO pageReqVO, AppQueryCombinedMap conditionMap){
        List<String> approvalTypeKeyList = new ArrayList<>(Collections.emptyList());
        //情况1 查询所有
        if ( CollectionUtils.isAnyEmpty(pageReqVO.getApprovalType()) ){
            Arrays.stream(SqlInclusionEnum.values()).forEach(  inclusion -> {
                approvalTypeKeyList.addAll( getInProcessListByEnum(inclusion, conditionMap.getSqlInclusionMap()) );
            });
        }
        //情况2 查询一个类型，且类型在请求类型枚举中
        else if ( 1 == pageReqVO.getApprovalType().size() ){

            ApprovalTypeTagEnum approvalTypeTagEnum = ApprovalTypeTagEnum.getByTagName( pageReqVO.getApprovalType().get(0) );

            if ( Objects.isNull(approvalTypeTagEnum)) {
                throw exception( ERROR_APPROVAL_TYPE );
            }

            SqlInclusionEnum sqlInclusionEnum = SqlInclusionEnum.getByApprovalTypeTag( approvalTypeTagEnum );

            approvalTypeKeyList.addAll( getInProcessListByEnum(sqlInclusionEnum, conditionMap.getSqlInclusionMap()) );
            if ( ApprovalTypeTagEnum.COMPREHENSIVE_AFFAIRS.equals(approvalTypeTagEnum) &&
                    !ApprovalTypeTagEnum.COMPREHENSIVE_AFFAIRS.getTagName().equals(pageReqVO.getApprovalType().get(0)) ) {
                approvalTypeKeyList.clear();
                approvalTypeKeyList.add(pageReqVO.getApprovalType().get(0));
            }
            //关闭不需要拼接的sql
            List<SqlInclusionEnum> closeList = Arrays.stream(SqlInclusionEnum.values())
                    .filter( x -> !x.equals(sqlInclusionEnum) )
                    .collect(Collectors.toList());
            conditionMap.closeSqlInclusionBatch(closeList);
        }
        //情况3 查询类型有多个，根据前端传来的的key查询对应的审批类型
        else {
            approvalTypeKeyList.addAll(pageReqVO.getApprovalType());
            if ( pageReqVO.getApprovalType().contains("affairHandling") ){
                approvalTypeKeyList.addAll( getInProcessListByEnum(SqlInclusionEnum.AFFAIR_HANDLE_INCLUSION,conditionMap.getSqlInclusionMap()) );
            }
        }
        conditionMap.setProcessKeyInCombined( approvalTypeKeyList );
    }

    /**
     * 高级筛选
     * 对审批状态条件进行封装
     * @param pageReqVO 页面请求
     * @param conditionMap 查询map
     */
    private static void initApprovalStatus(BpmTaskPageReqVO pageReqVO, AppQueryCombinedMap conditionMap) {
        ApproveStatusEnum approveStatusEnum = ApproveStatusEnum.getByReqStatus(pageReqVO.getApprovalStatus());
        if (Objects.isNull(approveStatusEnum)){
            throw exception(ERROR_APPROVAL_STATUS);
        }
        else {
            switch (approveStatusEnum){
                case APPROVING:{
                    //收发文节点约束
//                    setSqlIncludedMapInTaskDefKy( SendingTaskCodeEnum.getTaskCodeInApproval(), SqlInclusionEnum.SENDING_INCLUSION, conditionMap );
//                    setSqlIncludedMapInTaskDefKy( RecevingTaskCodeEnum.getTaskCodeInApproval(), SqlInclusionEnum.RECEIVING_INCLUSION, conditionMap );
                    //政务办理节点约束
                    setSqlIncludedMapInTaskDefKy( AffairHandleTaskNameCodeEnum.getCheckSet(), SqlInclusionEnum.AFFAIR_HANDLE_INCLUSION, conditionMap );
                    //审批中心节点约束：目前审批中心全是审批中

                    //驳回/撤回节点约束：flowStatus = 1 不查询驳回/撤回节点
                    conditionMap.setApproving( "1" );
                    conditionMap.setNotRejectedAndRevoked( true);

                    //重点工作没有审批中状态，不需要include
                    conditionMap.closeSqlInclusion(SqlInclusionEnum.KEY_WORK_INCLUSION);
                    break;
                }
                case HANDLING:{
                    //收发文节点约束
//                    setSqlIncludedMapInTaskDefKy( SendingTaskCodeEnum.getTaskCodeInHandling(), SqlInclusionEnum.SENDING_INCLUSION, conditionMap );
//                    setSqlIncludedMapInTaskDefKy( RecevingTaskCodeEnum.getTaskCodeInHandling(), SqlInclusionEnum.RECEIVING_INCLUSION, conditionMap );
                    //政务办理节点约束
                    setSqlIncludedMapInTaskDefKy( AffairHandleTaskNameCodeEnum.getHandleSet(), SqlInclusionEnum.AFFAIR_HANDLE_INCLUSION, conditionMap );
                    //审批中心节点约束:审批中心没有办理节点，不需要include
                    conditionMap.closeSqlInclusion(SqlInclusionEnum.APPROVAL_CENTER_INCLUSION);
                    conditionMap.setNotRejectedAndRevoked( true );
                    break;
                }
                //已同意
                case APPROVED:{
                    //已同意：针对已办页面：办理人经手时自己通过或者同意的任务，不包括被自己驳回过的
                    if ( !PageNameEnum.DONE_PAGE.equals(conditionMap.getWhichPage()) ){
                        throw exception(ERROR_APPROVAL_STATUS);
                    }
                    else {
                        conditionMap.setApproved( 2 );
                    }
                    break;
                }
                //flowFlag 1 撤回  2 是驳回
                case REJECTED:{
                    conditionMap.setFlowFlag( 2 );
                    //重点工作没有驳回，不需要include
                    conditionMap.closeSqlInclusion(SqlInclusionEnum.KEY_WORK_INCLUSION);
                    //政务办理没有驳回状态，不需要include
                    conditionMap.closeSqlInclusion(SqlInclusionEnum.AFFAIR_HANDLE_INCLUSION);
                    break;
                }
                case REVOKED:{
                    conditionMap.setFlowFlag( 1 );
                    //重点工作没有撤回，不需要include
                    conditionMap.closeSqlInclusion(SqlInclusionEnum.KEY_WORK_INCLUSION);
                    break;
                }
                case END:{
                    //只在我发起/已办里面有数据
                    if (PageNameEnum.TODO_PAGE.equals(conditionMap.getWhichPage())){
                        throw exception(ERROR_APPROVAL_STATUS);
                    }
                    conditionMap.setInEnd( true );
                    break;
                }
            }
        }
    }


    /**
     * 设置督办状态条件
     * @param pageReqVO 页面请求
     */
    public static void initSuperviseStatus(BpmTaskPageReqVO pageReqVO, AppQueryCombinedMap conditionMap) {
        conditionMap.setSuperviseStatus( pageReqVO.getSuperviseStatus() );
    }

    /**
     * 判断是否所有的sql include模板都被关闭了
     * @param sqlIncludedMap conditionMap的sql include模板map
     * @return 如果全部被关闭，则返回false：不需要建立数据库连接进行查询 true：需要进行一次数据查询
     */
    public static boolean nonNullSqlIncluded(Map<String, CombinedSqlInclusion> sqlIncludedMap){
        if (Objects.isNull(sqlIncludedMap)){
            return false;
        }
        else {
            //是否有已激活的sqlIncluded
            List<CombinedSqlInclusion> mapList = sqlIncludedMap.values()
                    .stream().filter(CombinedSqlInclusion::isActive)
                    .peek( x -> {
                        log.info("本次查询将组装的sql include模板有：{}，即 {}",x.getIncludedEnum().getInclusionName(), x.getIncludedEnum().getDescription());
                    })
                    .collect(Collectors.toList());
            return mapList.size() != 0;
        }
    }

    /**
     * 将对应流程的审批类型约束放入include中
     * @param includedNameEnum sql模板枚举
     * @param sqlIncludedMap conditionMap的sql include模板map
     * @return 审批类型数组
     */
    private static List<String> getInProcessListByEnum(SqlInclusionEnum includedNameEnum, Map<String, CombinedSqlInclusion> sqlIncludedMap) {
        if (Objects.isNull(includedNameEnum)){
            return Collections.emptyList();
        }
        else {
            return sqlIncludedMap.get(includedNameEnum.getInclusionName()).getInProcessKey();
        }
    }

    /**
     * 设置每个sql include 模板的节点约束
     * @param taskDefKyList 约束的节点
     * @param includedNameEnum 对应流程
     * @param conditionMap conditionMap
     */
    private static void setSqlIncludedMapInTaskDefKy(List<String> taskDefKyList, SqlInclusionEnum includedNameEnum , AppQueryCombinedMap conditionMap) {

        conditionMap.getSqlInclusionMap()
                .get( includedNameEnum.getInclusionName() )
                .setInTaskDefKey( taskDefKyList);

    }

    /**
     * 关闭某个模板，减少查询的sql
     * 某些流程不需要被查询
     * @param inclusionEnum 流程模板
     */
    public void closeSqlInclusion(SqlInclusionEnum inclusionEnum){
        if (Objects.isNull(inclusionEnum)){
            return;
        }
        CombinedSqlInclusion sqlIncluded = this.sqlInclusionMap.get(inclusionEnum.getInclusionName());
        if (Objects.nonNull(sqlIncluded)){
            sqlIncluded.setActive(false);
        }
    }

    /**
     * 批量关闭/删除某个模板，减少查询的sql
     * 某些流程不需要被查询
     * @param inclusionEnums 流程模板
     */
    public void closeSqlInclusionBatch(List<SqlInclusionEnum> inclusionEnums){
        inclusionEnums.forEach(this::closeSqlInclusion);
    }
}
