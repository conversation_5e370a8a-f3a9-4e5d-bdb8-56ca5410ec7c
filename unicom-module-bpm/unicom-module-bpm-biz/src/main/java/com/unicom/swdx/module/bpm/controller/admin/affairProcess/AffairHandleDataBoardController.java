package com.unicom.swdx.module.bpm.controller.admin.affairProcess;


import cn.hutool.core.date.LocalDateTimeUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard.*;
import com.unicom.swdx.module.bpm.service.dataBoard.AffairHandleDataBoardService;
import com.unicom.swdx.module.bpm.service.definition.BpmProcessDefinitionService;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
import com.unicom.swdx.module.bpm.util.excel.IgnoreFirstLongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "政务子系统 - 政务办理报表")
@RestController
@RequestMapping("/bpm/affair-handle-data-board")
@Validated
@Slf4j
public class AffairHandleDataBoardController {
    @Resource
    private BpmTaskService taskService;

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private BpmTaskService bpmTaskService;

    @Resource
    private BpmProcessDefinitionService bpmProcessDefinitionService;


    @Resource
    private AdminUserApi adminUserApi;

    @Autowired
    private SmsSendApi smsSendApi;

    @Autowired
    private DeptApi deptApi;

    @Resource
    private AffairHandleDataBoardService affairHandleDataBoardService;

    @GetMapping("getStatisticsList")
    @ApiOperation("app获得一级报表统计页面")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<AffairHandleFirstLevelReportPageRespVO> getHandleCount(@Valid AffairHandleAppCountReqVO reqVO) {
        setDefaultTime(reqVO);
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        return success(affairHandleDataBoardService.getHandleCount(reqVO));
    }

    @GetMapping("getAppSecondLevelReportList")
    @ApiOperation("APP获得二级报表统计列表")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<PageResult<AffairHandleSecondLevelReportBaseVO>> getAppHandleCount2(@Valid AffairHandleAppCountReqVO reqVO) {
        setDefaultTime(reqVO);
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        return success(affairHandleDataBoardService.getSecondHandleCount(reqVO));
    }


    @GetMapping("getPCStatisticsList")
    @ApiOperation("PC获得报表统计页面")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<AffairPCHandleReportPageRespVO> getPCHandleCount(@Valid AffairHandleCountReqVO reqVO) {
        setDefaultTime(reqVO);
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        return success(affairHandleDataBoardService.getPCHandleCount(reqVO));
    }


    @GetMapping("exportPCStatisticsList")
    @ApiOperation("PC导出报表统计页面")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    @OperateLog(type = EXPORT)
    public void exportPCHandleCount(@Valid AffairHandleCountReqVO reqVO,
                                    HttpServletResponse response) throws IOException {
        setDefaultTime(reqVO);
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        List<AffairHandlePCReportExcelVO> list = affairHandleDataBoardService.exportPCHandleCount(reqVO);
        String filename = "政务办理报表_" + DateUtils.datestamp() + ".xls";
        String[] column = {"处室", "办理总数", "督办件数", "逾期件数", "行政许可", "行政确认", "行政奖励", "其他行政权力","非常满意", "满意", "基本满意", "不满意", "非常不满意"};
        String title = "政务办理报表_" + DateUtils.datestamp();
        List<List<String>> heads = getHeads(column, title);
        ExcelUtils.write2(response, filename, heads, "数据", AffairHandlePCReportExcelVO.class, list,new IgnoreFirstLongestMatchColumnWidthStyleStrategy());
    }


    @GetMapping("getEvaluation")
    @ApiOperation("根据流程id获取评价信息")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<AffairHandleEvaluationRespVO> getEvaluation(@Valid String processInstanceId) {
        return success(affairHandleDataBoardService.getEvaluation(processInstanceId));
    }

    @GetMapping("getHomePageCount")
    @ApiOperation("获取首页计数")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<AffairHandleHomePageRespVO> getHomePageCountn() {


        AffairHandleCountReqVO reqVO = new AffairHandleCountReqVO();
        setDefaultTime(reqVO);
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        return success(affairHandleDataBoardService.getHomePageCount(reqVO));
    }

    @GetMapping("getPCSecondLevelReportList")
    @ApiOperation("PC获得二级报表统计列表")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<PageResult<PCAffairHandleSecondLevelReportPageRespVO>> getPCHandleCount2(@Valid PCAffairHandleSecondLevelReportReqVO reqVO) {
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        return success(affairHandleDataBoardService.getPCSecondHandleCount(reqVO));
    }

    @GetMapping("exportPCSecondLevelReportList")
    @ApiOperation("PC导出二级报表统计列表")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    @OperateLog(type = EXPORT)
    public void exportPCHandleCount2(@Valid PCAffairHandleSecondLevelReportListReqVO reqVO,
                                     HttpServletResponse response) throws IOException {
        if(Objects.nonNull(reqVO.getEndDate())) {
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
        List<PCAffairHandleSecondLevelReportExcelVO> list = affairHandleDataBoardService.exportPCSecondHandleCount(reqVO);
        String filename = "政务办理报表_" + PCAffairHandleSecondLevelReportListReqVO.getNameByRoute(reqVO.getRouteColumn()) + "_" + DateUtils.datestamp() + ".xls";
        String[] column = {"服务事项", "事项类型", "申请单位/人", "申请日期", "办结日期", "督办次数", "状态", "满意度"};
        String title = "政务办理报表_" + PCAffairHandleSecondLevelReportListReqVO.getNameByRoute(reqVO.getRouteColumn()) + "_" + DateUtils.datestamp();
        List<List<String>> heads = getHeads(column, title);
        ExcelUtils.write2(response, filename, heads, "数据", PCAffairHandleSecondLevelReportExcelVO.class, list,new IgnoreFirstLongestMatchColumnWidthStyleStrategy());
    }

    @GetMapping("testDeleteAll")
    @ApiOperation("测试用-删除所有报表数据")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<String> testDeleteAll() {

        return success(affairHandleDataBoardService.testDeleteAll());
    }

    @GetMapping("testCountFromAndTo")
    @ApiOperation("测试用-从一天到另一天执行统计")
    @PreAuthorize("@ss.hasPermission('bpm:affair-handle-data-board:query')")
    public CommonResult<String> testCountFromAndTo(@RequestParam("from") String from, @RequestParam("to") String to) {
        LocalDate f = LocalDateTimeUtil.parseDate(from, "yyyy-MM-dd");
        LocalDate t = LocalDateTimeUtil.parseDate(to, "yyyy-MM-dd");
        for (; f.isBefore(t); f = f.plusDays(1)) {
            affairHandleDataBoardService.saveCountResultOnTheDay(f);

            log.info("正在统计的时间为：{}", f);
        }
        return success("成功");
    }


    private void setDefaultTime(AffairHandleCountReqVO reqVO) {
        if (Objects.isNull(reqVO.getStartDate()) && Objects.isNull(reqVO.getEndDate())) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.DATE,-1);
            reqVO.setEndDate(cal.getTime());
            cal.add(Calendar.DATE, -29);
            reqVO.setStartDate(cal.getTime());
            reqVO.setStartDate(DateUtils.getDayStartTime(reqVO.getStartDate()));
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
    }

    private void setDefaultTime(AffairHandleAppCountReqVO reqVO) {
        if (Objects.isNull(reqVO.getStartDate()) && Objects.isNull(reqVO.getEndDate())) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.DATE,-1);
            reqVO.setEndDate(cal.getTime());
            cal.add(Calendar.DATE, -29);
            reqVO.setStartDate(cal.getTime());
            reqVO.setStartDate(DateUtils.getDayStartTime(reqVO.getStartDate()));
            reqVO.setEndDate(DateUtils.getDayEndTime(reqVO.getEndDate()));
        }
    }


    private static List<List<String>> getHeads(String[] column, String title) {
        List<List<String>> heads = new ArrayList<List<String>>();
        for (String s : column) {
            List<String> subHead = new ArrayList<String>();
            subHead.add(title);
            subHead.add(s);
            heads.add(subHead);
        }
        return heads;
    }

}
