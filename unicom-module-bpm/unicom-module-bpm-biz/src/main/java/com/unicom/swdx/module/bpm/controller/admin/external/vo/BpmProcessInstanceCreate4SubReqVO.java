package com.unicom.swdx.module.bpm.controller.admin.external.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@ApiModel("子系统调用 - 流程实例的创建 Request VO")
@Data
public class BpmProcessInstanceCreate4SubReqVO {

    @ApiModelProperty(value = "发起流程的子系统来源", required = true, example = "1024")
    private String source;

    @ApiModelProperty(value = "子系统的凭证", required = true, example = "1024")
    private String secret;

    @ApiModelProperty(value = "流程发起人的用户id", required = true, example = "1024")
    private Long userId;

    @ApiModelProperty(value = "流程定义", example = "1024")
    private String processDefinitionKey;

    @ApiModelProperty(value = "业务的唯一标识，例如请假申请的id", example = "1024")
    private String businessKey;

    @ApiModelProperty(value = "流程实例编号", notes = "创建流程不需要，重启流程需要", example = "1024")
    private String processInstanceId;

    @ApiModelProperty(value = "变量实例")
    private Map<String, Object> variables;

}
