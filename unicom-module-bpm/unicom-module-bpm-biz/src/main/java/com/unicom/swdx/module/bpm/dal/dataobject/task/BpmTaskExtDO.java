package com.unicom.swdx.module.bpm.dal.dataobject.task;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Bpm 流程任务的拓展表
 * 主要解决 Flowable Task 和 HistoricTaskInstance 不支持拓展字段，所以新建拓展表
 *
 * <AUTHOR>
 */
@TableName(value = "bpm_task_ext", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskExtDO extends BaseDO {

    /**
     * 编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务的审批人
     * <p>
     * 冗余 Task 的 assignee 属性
     */
    private Long assigneeUserId;
    /**
     * 任务的审批人的部门id
     */
    private Long assigneeDeptId;
    /**
     * 任务的名字
     * <p>
     * 冗余 Task 的 name 属性，为了筛选
     */
    private String name;
    /**
     * 任务的编号
     * <p>
     * 关联 Task 的 id 属性
     */
    private String taskId;
    /**
     * 任务的标识
     * <p>
     * 关联  Task#getTaskDefinitionKey()
     */
    private String taskDefKey;
    /**
     * 任务的结果
     * <p>
     * 枚举 {@link BpmProcessInstanceResultEnum}
     */
    private Integer result;
    /**
     * 审批建议
     */
    private String reason;
    /**
     * 任务的结束时间
     * <p>
     * 冗余 HistoricTaskInstance 的 endTime  属性
     */
    private LocalDateTime endTime;

    /**
     * 流程实例的编号
     * <p>
     * 关联 ProcessInstance 的 id 属性
     */
    private String processInstanceId;
    /**
     * 流程定义的编号
     * <p>
     * 关联 ProcessDefinition 的 id 属性
     */
    private String processDefinitionId;

    /**
     * 办理日志参数
     * <p>
     * 办理任务节点产生的日志信息
     */
    private String logParameters;

    /**
     * 办理日志参数Map
     * <p>
     * 办理任务节点产生的日志信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> paramsMap;

    /**
     * 任务类型
     * <p>
     * 0发起审批 1选择审批人 2单人审批和多人顺序 3多人会签 4人事处备案 5人事处销假 6手动结束
     */
    private Integer taskType;

    /**
     * 1 是撤回
     */
    private String revokeStatus;


    /**
     * 提醒状态 1 提醒 2 督办
     */
    private String superviseStatus;

    @ApiModelProperty(value = "用来存移动端手写签字等其他任务过程中产生的文件")
    private String imageUrl;

    /**
     * 提醒次数统计
     */
    private Integer remindCount;

    /**
     * 督办次数统计
     */
    private Integer superviseCount;

    /**
     * 逾期次数统计
     */
    private Integer overdueCount;

    /**
     * 督办时间
     */
    private LocalDateTime superviseTime;

}
