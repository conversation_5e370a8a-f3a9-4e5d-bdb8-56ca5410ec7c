package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Map;


import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("政务子系统 - 政务办理PC二级报表分页 Response VO")
@Data
public class PCAffairHandleSecondLevelReportPageRespVO {

    @ApiModelProperty(value = "处室ID", required = true)
    private Long deptId;


    @ApiModelProperty(value = "流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "流程实例标识")
    private String processKey;

    @ApiModelProperty(value = "服务事项名")
    private String name;

    @ApiModelProperty(value = "事项类型")
    private String processType;

    @ApiModelProperty(value = "申请单位/人")
    private String applyEnterpriseName;

    @ApiModelProperty(value = "申请日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "办理日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime handleTime;


    @ApiModelProperty(value = "督办次数")
    private Integer superviseCount;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "满意度")
    private Integer satisfactionDegree;

    @ApiModelProperty(value = "评分")
    private Float grade;

    @ApiModelProperty(value = "其他信息")
    private Map<String,Object> formVariables;

    @ApiModelProperty(value = "评价id")
    private Long evaluationId;

}
