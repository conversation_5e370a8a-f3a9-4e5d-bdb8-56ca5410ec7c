package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("获取政务办理评价 Response VO")
@Data
@ToString(callSuper = true)
public class AffairHandleEvaluationRespVO {
    @ApiModelProperty(value = "评价时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime evaluationTime;

    @ApiModelProperty(value = "评分")
    private Float grade;

    @ApiModelProperty(value = "满意度")
    private Integer satisfactionDegree;

    @ApiModelProperty(value = "评价内容")
    private String content;

}
