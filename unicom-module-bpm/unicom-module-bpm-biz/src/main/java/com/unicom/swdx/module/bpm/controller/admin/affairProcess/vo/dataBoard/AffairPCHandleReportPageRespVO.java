package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@ApiModel("pc获得报表统计页面 Response VO")
@Data
@ToString(callSuper = true)
public class AffairPCHandleReportPageRespVO {
    @ApiModelProperty(value = "各个处室统计明细")
    List<AffairHandlePCReportRespVO> statisticalDetails;

    @ApiModelProperty(value = "总计")
    AffairHandlePCReportSummaryVO summary;

    @ApiModelProperty(value = "是否局领导")
    Boolean isBureauLeaderRole;

    public AffairPCHandleReportPageRespVO() {
        this.statisticalDetails = new ArrayList<>(Collections.emptyList());
        this.summary = new AffairHandlePCReportSummaryVO();
    }
}
