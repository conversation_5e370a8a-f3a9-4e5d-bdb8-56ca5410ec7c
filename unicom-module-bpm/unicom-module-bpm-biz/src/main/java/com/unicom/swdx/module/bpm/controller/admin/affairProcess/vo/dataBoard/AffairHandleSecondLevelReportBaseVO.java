package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Data
public class AffairHandleSecondLevelReportBaseVO {

    @ApiModelProperty(value = "流程id")
    private String processInstanceId;


    @ApiModelProperty(value = "办理日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime handleTime;

    @ApiModelProperty(value = "评分")
    private Float grade;

    @ApiModelProperty(value = "满意度")
    private Integer satisfactionDegree;

    @ApiModelProperty(value = "服务事项名")
    private String name;

    //@ApiModelProperty(value = "申请单位/人")
    //private String applyEnterpriseName;

    @ApiModelProperty(value = "状态")
    private String status;

}
