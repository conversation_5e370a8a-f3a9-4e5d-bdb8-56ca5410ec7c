package com.unicom.swdx.module.bpm.controller.admin.task.vo.task;

import com.unicom.swdx.module.bpm.api.task.dto.ProcessInstanceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@ApiModel("管理后台 - 流程任务的 Running 进行中的分页项 Response VO")
@Data
public class BpmTaskTodoPageItemRespVO {

    @ApiModelProperty(value = "任务编号", required = true, example = "1024")
    private String id;

    @ApiModelProperty(value = "任务名字", required = true, example = "sk")
    private String name;

    @ApiModelProperty(value = "接收时间", required = true)
    private LocalDateTime claimTime;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "激活状态", required = true, example = "1", notes = "参见 SuspensionState 枚举")
    private Integer suspensionState;

    @ApiModelProperty(value = "流程日志信息")
    private Map<String, Object> paramsMap;

    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    /**
     * 所属流程实例
     */
    @ApiModelProperty(value = "流程实例简要信息")
    private ProcessInstanceDTO processInstance;

    @ApiModelProperty(value = "流程分类", notes = "参见 bpm_model_category 数据字典", example = "1")
    private String category;

}
