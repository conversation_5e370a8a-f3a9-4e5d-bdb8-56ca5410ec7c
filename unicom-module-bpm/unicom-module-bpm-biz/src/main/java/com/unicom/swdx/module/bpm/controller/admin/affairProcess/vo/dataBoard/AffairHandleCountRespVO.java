package com.unicom.swdx.module.bpm.controller.admin.affairProcess.vo.dataBoard;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("获取政务办理计数 Response VO")
@Data
@ToString(callSuper = true)
public class AffairHandleCountRespVO {

    @ApiModelProperty(value = "办理总数")
    private Integer totalHandle;

    @ApiModelProperty(value = "好评数")
    private Integer favourableComment;

    @ApiModelProperty(value = "督办数")
    private Integer supervision;

    @ApiModelProperty(value = "逾期数")
    private Integer overdue;

    @ApiModelProperty(value = "非常满意")
    private Integer verySatisfied;

    @ApiModelProperty(value = "非常满率")
    private Float verySatisfiedRate;

    @ApiModelProperty(value = "满意")
    private Integer satisfied;

    @ApiModelProperty(value = "满意率")
    private Float satisfiedRate;

    @ApiModelProperty(value = "基本满意")
    private Integer basicallySatisfied;

    @ApiModelProperty(value = "基本满意率")
    private Float basicallySatisfiedRate;

    @ApiModelProperty(value = "不满意")
    private Integer dissatisfied;

    @ApiModelProperty(value = "不满意率")
    private Float dissatisfiedRate;

    @ApiModelProperty(value = "非常不满意")
    private Integer veryDissatisfied;

    @ApiModelProperty(value = "非常不满意率")
    private Float veryDissatisfiedRate;

    @ApiModelProperty(value = "按满意度总数")
    private Integer totalSatisfaction;

    @ApiModelProperty(value = "行政许可")
    private Integer permit;

    @ApiModelProperty(value = "行政许可率")
    private Float permitRate;

    @ApiModelProperty(value = "行政确认")
    private Integer confirmation;

    @ApiModelProperty(value = "行政确认率")
    private Float confirmationRate;

    @ApiModelProperty(value = "行政奖励")
    private Integer award;

    @ApiModelProperty(value = "行政奖励率")
    private Float awardRate;

    @ApiModelProperty(value = "其他行政权力")
    private Integer other;

    @ApiModelProperty(value = "其他行政权力")
    private Float otherRate;

    @ApiModelProperty(value = "按业务类型总数")
    private Integer totalProcessType;
}
