package com.unicom.swdx.module.bpm.service.definition;


import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;
import com.unicom.swdx.module.bpm.BpmServerApplication;
import com.unicom.swdx.module.bpm.controller.admin.task.BpmTaskController;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceMyPageReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskApproveReqVO;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskTodoPageReqVO;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
import liquibase.pro.packaged.B;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.Map;

import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@SpringBootTest(classes = {BpmServerApplication.class} , webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class BpmProcessInstanceServiceTest extends BaseDbUnitTest {

    @Resource
    private TaskService taskService;

    @Resource
    BpmTaskService bpmTaskService;

    @Resource
    BpmProcessInstanceService bpmProcessInstanceService;

    @Resource
    BpmTaskController bpmTaskController;

    @Test
    public void testCreateInfor_success() {

        BpmTaskTodoPageReqVO  temp = new BpmTaskTodoPageReqVO();
        temp.setName("发起人发起审批");


        bpmTaskService.getTodoTaskPage(1l, temp);

        //4b8af600-df80-11ee-ad61-fefcfe926779
    }

    @Test
    public void testCreateInfor_success11() {


        Map<String,Object> variables = new HashMap<>();
        variables.put("postType" , 1);

        taskService.complete("4b8af600-df80-11ee-ad61-fefcfe926779",variables);

    }
}
