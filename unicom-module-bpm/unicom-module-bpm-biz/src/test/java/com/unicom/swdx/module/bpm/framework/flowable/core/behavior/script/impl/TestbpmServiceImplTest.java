package com.unicom.swdx.module.bpm.framework.flowable.core.behavior.script.impl;//package com.unicom.swdx.module.oa;


import cn.hutool.core.collection.ListUtil;
import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;
import com.unicom.swdx.module.bpm.BpmServerApplication;
import com.unicom.swdx.module.bpm.controller.admin.task.vo.task.BpmTaskApproveReqVO;
import com.unicom.swdx.module.bpm.service.task.BpmProcessInstanceService;
import com.unicom.swdx.module.bpm.service.task.BpmTaskService;
import com.unicom.swdx.module.oa.enums.Consts;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest(classes = {BpmServerApplication.class} , webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestbpmServiceImplTest  extends BaseDbUnitTest {



    @Resource
    BpmProcessInstanceService bpmProcessInstanceService;

    @Resource
    BpmTaskService bpmTaskService;



    @Test
    public void testCreateInfor_success() {
//        BpmTaskApproveReqVO reqVO = new BpmTaskApproveReqVO();
//        reqVO.setProcessInstanceId("065d5df0-1ce7-11ef-9293-5c60bab9cf0f");
//        reqVO.setId("1d2c72c6-1ce7-11ef-9293-5c60bab9cf0f");
//        bpmTaskService.approveTask(1l ,reqVO);  //完成分管校领导审批



        //id 查询 act_ru_task 表 proc_def_id  = vacations_duty:1:0676fe2b-1197-11ef-8f75-024257aaebdc 的数据的id  接口 @PostMapping("/update-assignee")
        bpmTaskService.updateTaskAssignee( "e093fe27-1d57-11ef-a18a-5c60bab9cf0f" , 9386l);//更新秘书科审批人为指定人选


    }

    @Test
    public void testCreateInfor_success11() {
        BpmTaskApproveReqVO reqVO = new BpmTaskApproveReqVO();
        reqVO.setProcessInstanceId("e0af710c-1da8-11ef-b9bf-5c60bab9cf0f");
        reqVO.setId("e8434aab-1da8-11ef-b9bf-5c60bab9cf0f");

        Map<String, Object> variables  = new HashMap<>();  //4  xyq01  9368 luoxiao

        variables.put("flag", Consts.ONE); // 多实例标识
        variables.put("chargeLeaderSeq", "2");
        variables.put("unapproved" , ListUtil.of(9386 , 4));
        variables.put("taskType" , "多人会签");

//        variables.put("approvals" , ListUtil.of(9386 , 4));

        bpmTaskService.approveTask(9342l ,reqVO);  //完成部门领导审批   到分管校领导审批



        //id 查询 act_ru_task 表 proc_def_id  = vacations_duty:1:0676fe2b-1197-11ef-8f75-024257aaebdc 的数据的id  接口 @PostMapping("/update-assignee")
//        bpmTaskService.updateTaskAssignee( "e093fe27-1d57-11ef-a18a-5c60bab9cf0f" , 9386l);//更新秘书科审批人为指定人选


    }

    @Resource
    private TaskService taskService;

    @Test
    public void testCreateInfor_success12() {

        BpmTaskApproveReqVO reqVO = new BpmTaskApproveReqVO();
        reqVO.setProcessInstanceId("04c6601a-20ba-11ef-9fb0-5c60bab9cf0f");
        reqVO.setId("0507aee8-20ba-11ef-9fb0-5c60bab9cf0f");
        Map<String, Object> variables  = new HashMap<>();  //4  xyq01  9368 luoxiao
        variables.put("approvalsnew" , ListUtil.of(9386 , 4));
        variables.put("flagnew" , "1");
        reqVO.setVariables(variables);
        bpmTaskService.approveTask(1L ,reqVO);  //完成部门领导审批   到分管校领导审批


//        taskService.complete(reqVO.getId(),variables);
        //id 查询 act_ru_task 表 proc_def_id  = vacations_duty:1:0676fe2b-1197-11ef-8f75-024257aaebdc 的数据的id  接口 @PostMapping("/update-assignee")
//        bpmTaskService.updateTaskAssignee( "e093fe27-1d57-11ef-a18a-5c60bab9cf0f" , 9386l);//更新秘书科审批人为指定人选


    }

}
