package com.unicom.swdx.module.bpm.service.oa;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class BpmOATaskServiceImplTest {

    @Test
    void getTodoTaskPage() {
    }

    @Test
    void getMyProcessInstancePage() {
    }

    @Test
    void getDoneTaskPageWithFlowFlag() {
    }

    @Test
    void getDonePage() {
    }

    @Test
    void getAppDonePage() {
    }

    @Test
    void getDonePageAll() {
    }

    @Test
    void getTasksByProcessInstanceIds() {
    }

    @Test
    void getXcxTodoTaskPage() {
    }

    @Test
    void getXcxMyProcessInstancePage() {
    }

    @Test
    void getXcxDoneTaskPageWithFlowFlag() {
    }

    @Test
    void getProcessTaskListByProcessInstanceId() {
    }

    @Test
    void getTaskListByProcessInstanceId() {
    }

    @Test
    void getTaskLogByProcInsId() {
    }
}