package com.unicom.swdx.gateway;

import com.unicom.swdx.module.infra.api.logger.ApiAccessLogApi;
import com.unicom.swdx.module.infra.api.logger.ApiErrorLogApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { ApiAccessLogApi.class, // 主要是引入相关的 API 服务
        ApiErrorLogApi.class})
public class RpcConfiguration {
}
