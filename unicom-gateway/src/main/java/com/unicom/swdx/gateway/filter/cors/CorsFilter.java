package com.unicom.swdx.gateway.filter.cors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Arrays;

/**
 * 跨域 Filter
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope
public class CorsFilter implements WebFilter {

    @Value("${unicom.cors-white-list}")
    private String whiteList;

    private static final String ALL = "*";
    private static final String MAX_AGE = "3600L";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        // 非跨域请求，直接放行
        ServerHttpRequest request = exchange.getRequest();
        if (!CorsUtils.isCorsRequest(request)) {
            return chain.filter(exchange);
        }
        String origin = request.getHeaders().getOrigin();
        // 设置跨域响应头
        ServerHttpResponse response = exchange.getResponse();
        if (!Arrays.asList(whiteList.split(",")).contains(origin)) {
            log.info("origin 跨域请求不通过:{}", origin);
            response.setStatusCode(HttpStatus.METHOD_NOT_ALLOWED);
            return Mono.empty();
        }else{
            log.info("origin 跨域请求通过:{}", origin);
        }
        HttpHeaders headers = response.getHeaders();
        headers.add("Access-Control-Allow-Origin", origin);
        headers.add("Access-Control-Allow-Methods",  "POST, GET, OPTIONS");
        headers.add("Access-Control-Allow-Headers", ALL);
        headers.add("Access-Control-Max-Age", MAX_AGE);
        if (request.getMethod() == HttpMethod.OPTIONS) {
            response.setStatusCode(HttpStatus.OK);
            return Mono.empty();
        }
        return chain.filter(exchange);
    }

}
