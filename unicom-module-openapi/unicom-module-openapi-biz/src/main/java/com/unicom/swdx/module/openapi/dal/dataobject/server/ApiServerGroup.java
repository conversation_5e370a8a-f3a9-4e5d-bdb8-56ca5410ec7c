package com.unicom.swdx.module.openapi.dal.dataobject.server;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

/**
 * api服务表 api_server
 *
 */
@Data
@TableName("api_server_group")
public class ApiServerGroup extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long serverGroupId;
    /**
     * 服务名称
     */
    private String serverGroupName;
    /**
     * 服务编号
     */
    private String serverGroupCode;
}
