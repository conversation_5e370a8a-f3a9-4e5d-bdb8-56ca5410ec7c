package com.unicom.swdx.module.openapi.controller.admin.apiapplication;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiapplication.ApiApplicationDO;
import com.unicom.swdx.module.openapi.service.apiapplication.IApiApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_APPLICATION_CODE_NOT_UNIQUE;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_APPLICATION_NAME_NOT_UNIQUE;

@RestController
@Api(tags = "共性应用-服务管理-应用管理")
@RequestMapping("/openapi/application")
public class ApiApplicationController {

    @Resource
    private IApiApplicationService apiApplicationService;

    /**
     * 分页查询应用管理
     */
    @GetMapping("/page")
    @ApiOperation("分页查询应用管理")
    @PreAuthorize("@ss.hasPermission('openapi:application:query')")
    public CommonResult<PageResult<ApiApplicationPageRespVO>> page(@Valid ApiApplicationPageReqVO apiAccountPageReqVO) {
        return CommonResult.success(apiApplicationService.getApiApplicationPage(apiAccountPageReqVO));
    }

    /**
     * 新增应用管理
     */
    @PostMapping("/add")
    @ApiOperation("新增应用管理")
    @PreAuthorize("@ss.hasPermission('openapi:application:create')")
    public CommonResult<Boolean> addSave(ApiApplicationDO apiApplicationDO) {
        if (!apiApplicationService.validNameUnique(apiApplicationDO.getName(), null)) {
            throw exception(API_APPLICATION_NAME_NOT_UNIQUE);
        }
        if (!apiApplicationService.validCodeUnique(apiApplicationDO.getCode(), null)) {
            throw exception(API_APPLICATION_CODE_NOT_UNIQUE);
        }
        apiApplicationService.save(apiApplicationDO);
        return CommonResult.success(true);
    }

    /**
     * 修改应用管理
     */
    @PostMapping("/edit")
    @ApiOperation("修改应用管理")
    @PreAuthorize("@ss.hasPermission('openapi:application:update')")
    public CommonResult<Boolean> editSave(ApiApplicationDO apiApplicationDO) {
        // todo 入参校验
        if (!apiApplicationService.validNameUnique(apiApplicationDO.getName(), apiApplicationDO.getId())) {
            throw exception(API_APPLICATION_NAME_NOT_UNIQUE);
        }
        if (!apiApplicationService.validCodeUnique(apiApplicationDO.getCode(), apiApplicationDO.getId())) {
            throw exception(API_APPLICATION_CODE_NOT_UNIQUE);
        }
        apiApplicationService.updateById(apiApplicationDO);
        return CommonResult.success(true);
    }

    /**
     * 删除应用管理
     */
    @PostMapping("/remove")
    @ApiOperation("删除应用管理")
    @PreAuthorize("@ss.hasPermission('openapi:application:delete')")
    public CommonResult<Boolean> remove(Long id) {
        apiApplicationService.delete(id);
        return CommonResult.success(true);
    }

    /**
     * 根据id获取应用管理
     */
    @GetMapping("/get")
    @ApiOperation("根据id获取应用管理")
    @PreAuthorize("@ss.hasPermission('openapi:application:get')")
    public CommonResult<ApiApplicationDO> get(Long id) {
        return CommonResult.success(apiApplicationService.getById(id));
    }
}
