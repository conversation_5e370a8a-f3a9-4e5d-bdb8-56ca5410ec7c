package com.unicom.swdx.module.openapi.service.apiurl.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlExportReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlExportRespVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrlParam;
import com.unicom.swdx.module.openapi.dal.mapper.account.ApiAccountMapper;
import com.unicom.swdx.module.openapi.dal.mapper.accountapilimit.AccountApiLimitMapper;
import com.unicom.swdx.module.openapi.dal.mapper.apiurl.ApiUrlMapper;
import com.unicom.swdx.module.openapi.dal.mapper.apiurl.ApiUrlParamMapper;
import com.unicom.swdx.module.openapi.mq.producer.account.ApiAccountProducer;
import com.unicom.swdx.module.openapi.mq.producer.apiurl.ApiUrlProducer;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ApiUrlServiceImpl extends ServiceImpl<ApiUrlMapper, ApiUrl> implements IApiUrlService {

    @Resource
    private ApiUrlProducer apiUrlProducer;

    /**
     * api缓存
     * key：api编号
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private volatile Map<Long, ApiUrl> apiUrlCache;

    /**
     * 初始化 {@link #apiUrlCache}
     */
    @Override
    @PostConstruct
    public synchronized void initLocalCache() {
        // 获取api账号列表，如果有更新
        List<ApiUrl> apiUrlList = baseMapper.selectCacheList();
        if (CollUtil.isEmpty(apiUrlList)) {
            return;
        }
        // 构建缓存
        ImmutableMap.Builder<Long, ApiUrl> apiUrlCacheBuilder = ImmutableMap.builder();
        apiUrlList.forEach(apiUrl -> apiUrlCacheBuilder.put(apiUrl.getApiId(), apiUrl));
        apiUrlCache = apiUrlCacheBuilder.build();
        log.info("[initLocalCache][缓存api，数量为:{}]", apiUrlList.size());
    }

    @Resource
    private ApiUrlParamMapper apiUrlParamMapper;

    @Resource
    private AccountApiLimitMapper accountApiLimitMapper;

    @Resource
    private ApiAccountMapper apiAccountMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        LambdaQueryWrapper<ApiUrlParam> wrapper = Wrappers.lambdaQuery();
        apiUrlParamMapper.delete(wrapper.eq(ApiUrlParam::getApiId,id));
        removeById(id);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiUrlProducer.sendRefreshMessage();
            }

        });
    }
    @Override
    /**
     * 分页查询
     *
     * */
    public PageResult<ApiUrlPageRespVO> getApiUrlPage(ApiUrlPageReqVO req){
        IPage<ApiUrlPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<ApiUrlPageRespVO> list = this.baseMapper.selectPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if (myPage.getTotal() % req.getPageSize() == 0) {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize()));
            } else {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
     /**
     * 导出
     *
     * */
    public List<ApiUrlExportRespVO> getApiUrlList(ApiUrlExportReqVO reqVO){
        return this.baseMapper.selectMyList(reqVO);
    }

    @Override
    /**
     * 检验名字独特
     * */
    public Boolean validNameOrUriUnique(Boolean isName, String nameOrUri, Long id){
        LambdaQueryWrapper<ApiUrl> wrapper = Wrappers.lambdaQuery();
        List<ApiUrl> tempList;
        if(isName){
            tempList = baseMapper.selectList(wrapper.eq(ApiUrl::getApiName,nameOrUri));
        }else {
            tempList = baseMapper.selectList(wrapper.eq(ApiUrl::getApiUrlSuffix,nameOrUri));
        }
        if(Objects.isNull(id)){
            return tempList.isEmpty();
        }else {
            if (tempList.isEmpty()) {
                return true;
            }else if (tempList.size() == 1){
                return tempList.get(0).getApiId().equals(id);
            }else {
                return false;
            }
        }
    }

    @Override
    /**
     * 保存响应示例
     *
     * */
    public Boolean saveResponseExample(String resp,Long id){
        ApiUrl target = new ApiUrl();
        target.setResponseExample(resp);
        target.setApiId(id);
        this.baseMapper.updateById(target);
        return true;
    }

    @Override
    /**
     * 检查删除可行性
     */
    public Boolean checkRemove(Long id){
        //1.筛选此api涉及的limit
        LambdaQueryWrapper<AccountApiLimit> qw1 = Wrappers.lambdaQuery();
        qw1.eq(AccountApiLimit::getApiId,id);
        List<AccountApiLimit> limitList = accountApiLimitMapper.selectList(qw1);
        //2.筛选这些limit涉及的account里没被删除的account(如果找不到limit直接返回ture)
        if(limitList.isEmpty()){
            return true;
        }
        LambdaQueryWrapper<ApiAccount> qw2 =  Wrappers.lambdaQuery();
        qw2.in(ApiAccount::getAccountId,limitList.stream().map(AccountApiLimit::getAccountId).collect(Collectors.toList()));
        List<ApiAccount> apiAccounts = apiAccountMapper.selectList(qw2);
        //3.若无则返回可删除，若有则返回不可删除
        return apiAccounts.size() == 0;
    }

    @Override
    public ApiUrl getByUri(String uri) {
        Optional<ApiUrl> any = apiUrlCache.values().stream().filter(apiUrl ->
                StrUtil.equals(uri, apiUrl.getApiUrlSuffix())
        ).findAny();
        return any.orElse(null);
    }

    @Override
    public List<String> getServerUrl() {
        return this.baseMapper.selectServerUrl();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ApiUrl apiUrl) {
        boolean save = super.save(apiUrl);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiUrlProducer.sendRefreshMessage();
            }

        });
        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(ApiUrl apiUrl) {
        boolean updateById = super.updateById(apiUrl);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiUrlProducer.sendRefreshMessage();
            }

        });
        return updateById;
    }

}
