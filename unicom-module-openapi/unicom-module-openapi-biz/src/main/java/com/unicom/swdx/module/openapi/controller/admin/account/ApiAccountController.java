package com.unicom.swdx.module.openapi.controller.admin.account;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import com.unicom.swdx.module.openapi.framework.openapi.config.ApiConfig;
import com.unicom.swdx.module.openapi.service.account.IApiAccountService;
import com.unicom.swdx.module.openapi.utils.MyMD5Util;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_ACCOUNT_NAME_NOT_UNIQUE;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_NAME_NOT_UNIQUE;

@Api(tags = "共性应用-API接入管理-API账号管理")
@RestController
@RequestMapping("/openapi/account")
public class ApiAccountController {
    @Resource
    private IApiAccountService apiAccountService;

    @Resource
    private ApiConfig apiConfig;

    /**
     * 分页查询api服务账号
     */
    @GetMapping("/page")
    @ApiOperation("分页查询api服务账号")
    @PreAuthorize("@ss.hasPermission('openapi:apiAccount:query')")
    public CommonResult<PageResult<ApiAccountPageRespVO>> page(@Valid ApiAccountPageReqVO apiAccountPageReqVO) {
        return CommonResult.success(apiAccountService.getApiAccountPage(apiAccountPageReqVO));
    }

    /**
     * 新增api服务账号
     */
    @PostMapping("/add")
    @ApiOperation("新增api服务账号")
    @PreAuthorize("@ss.hasPermission('openapi:apiAccount:create')")
    public CommonResult<Boolean> addSave(ApiAccount account) {
        // todo 入参校验
        if(!apiAccountService.validNameUnique(account.getAccountName(),null)){
            throw exception(API_ACCOUNT_NAME_NOT_UNIQUE);
        }

        String appKey = RandomStringUtils.random(10, true, true);
        String appSecret = "";
        try {
            appSecret = MyMD5Util.md5(appKey, apiConfig.getMd5key());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        account.setAppKey(appKey);
        account.setAppSecret(appSecret);

        apiAccountService.save(account);
        return CommonResult.success(true);
    }

    /**
     * 修改api服务账号
     */
    @PostMapping("/edit")
    @ApiOperation("修改api服务账号")
    @PreAuthorize("@ss.hasPermission('openapi:apiAccount:update')")
    public CommonResult<Boolean> editSave(ApiAccount account) {
        // todo 入参校验
        if(!apiAccountService.validNameUnique(account.getAccountName(),account.getAccountId())){
            throw exception(API_ACCOUNT_NAME_NOT_UNIQUE);
        }
        apiAccountService.updateById(account);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务账号
     */
    @PostMapping("/remove")
    @ApiOperation("删除api服务账号")
    @PreAuthorize("@ss.hasPermission('openapi:apiAccount:delete')")
    public CommonResult<Boolean> remove(Long id) {
        apiAccountService.delete(id);
        return CommonResult.success(true);
    }
}
