package com.unicom.swdx.module.openapi.dal.dataobject.apiapplication;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.module.system.enums.oauth2.OAuth2GrantTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@TableName(value = "api_application", autoResultMap = true)
@KeySequence("api_application_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiApplicationDO extends BaseDO {
    /**
     * 编号，数据库自增
     *
     * 由于 SQL Server 在存储 String 主键有点问题，所以暂时使用 Long 类型
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 应用标识
     */
    @ApiModelProperty(value = "应用标识")
    private String code;
    /**
     * 客户端编号
     */
    @ApiModelProperty(value = "客户端编号")
    private String clientId;
    /**
     * 客户端密钥
     */
    @ApiModelProperty(value = "客户端密钥")
    private String secret;
    /**
     * 应用名
     */
    @ApiModelProperty(value = "应用名")
    private String name;
    /**
     * 应用图标
     */
    @ApiModelProperty(value = "应用图标")
    private String logo;
    /**
     * 应用地址
     */
    @ApiModelProperty(value = "应用地址")
    private String path;
    /**
     * 应用描述
     */
    @ApiModelProperty(value = "应用描述")
    private String description;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 访问令牌的有效期
     */
    @ApiModelProperty(value = "访问令牌的有效期")
    private Integer accessTokenValiditySeconds;
    /**
     * 刷新令牌的有效期
     */
    @ApiModelProperty(value = "刷新令牌的有效期")
    private Integer refreshTokenValiditySeconds;
    /**
     * 可重定向的 URI 地址
     */
    @ApiModelProperty(value = "可重定向的 URI 地址")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> redirectUris;
    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer sort;
    /**
     * 授权类型（模式）
     *
     * 枚举 {@link OAuth2GrantTypeEnum}
     */
    @ApiModelProperty(value = "授权类型（模式）")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> authorizedGrantTypes;
    /**
     * 授权范围
     */
    @ApiModelProperty(value = "授权范围")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> scopes;
    /**
     * 自动授权的 Scope
     *
     * code 授权时，如果 scope 在这个范围内，则自动通过
     */
    @ApiModelProperty(value = "自动授权的 Scope")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> autoApproveScopes;
    /**
     * 权限
     */
    @ApiModelProperty(value = "权限")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> authorities;
    /**
     * 资源
     */
    @ApiModelProperty(value = "资源")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> resourceIds;
    /**
     * 附加信息，JSON 格式
     */
    @ApiModelProperty(value = "附加信息，JSON 格式")
    private String additionalInformation;

    /**
     * 系统提供商
     */
    @ApiModelProperty(value = "系统提供商")
    private String systemProvider;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    /**
     * 重定向授权地址
     */
    @ApiModelProperty(value = "重定向授权地址")
    private String redirectAuthAddr;
}
