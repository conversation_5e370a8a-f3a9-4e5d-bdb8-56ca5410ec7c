package com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("账号api访问限制分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class AccountApiLimitPageRespVO {

    @ApiModelProperty(value = "api地址")
    private String apiAddress;

    @ApiModelProperty(value = "是否有限制")
    private Boolean hasLimit;

    @ApiModelProperty(value = "限制类型")
    private String limitType;

    @ApiModelProperty(value = "限制数量")
    private Integer limitNumber;

    @ApiModelProperty(value = "时间范围大小")
    private Integer limitTimeNumber;

    @ApiModelProperty(value = "时间范围单位")
    private String limitTimeUnit;

    @ApiModelProperty(value = "剩余限制数量")
    private Integer limitRestNumber;

    @ApiModelProperty(value = "api账号id")
    private Long accountId;

    @ApiModelProperty(value = "api id")
    private Long apiId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "限制描述")
    private String des;
}
