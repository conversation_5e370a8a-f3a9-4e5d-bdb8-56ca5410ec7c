package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.ToString;

@ApiModel("Api Param分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiParamPageReqVO extends PageParam {

    @ApiModelProperty(value = "参数名称")
    private String paramName;

    @ApiModelProperty(value = "数据类型")
    private String paramType;

    @ApiModelProperty(value = "是否必填")
    private Boolean paramRequired;

    @ApiModelProperty(value = "所属apiId")
    @NonNull
    private Long apiId;

}
