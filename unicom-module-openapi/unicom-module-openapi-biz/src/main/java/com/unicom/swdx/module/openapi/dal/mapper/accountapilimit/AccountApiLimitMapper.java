package com.unicom.swdx.module.openapi.dal.mapper.accountapilimit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountApiLimitMapper extends BaseMapperX<AccountApiLimit> {
    /**
     * 分页查询api账号限制
     */
    List<AccountApiLimitPageRespVO> selectPage(IPage page, @Param("req") AccountApiLimitPageReqVO req);

    /**
     * 分页查询可授权的api
     */
    List<AvailableApiUrlPageRespVO> selectApiPage(IPage page, @Param("req") AvailableApiUrlPageReqVO req);
}
