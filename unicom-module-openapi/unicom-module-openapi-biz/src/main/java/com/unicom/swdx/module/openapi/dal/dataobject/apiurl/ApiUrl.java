package com.unicom.swdx.module.openapi.dal.dataobject.apiurl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("api_url")
public class ApiUrl extends BaseDO {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long apiId;

    @ApiModelProperty(value = "api名称")
    private String apiName;

    @ApiModelProperty(value = "api所属分组id")
    private Long apiGroupId;

    @ApiModelProperty(value = "api请求方式")
    private String apiRequestMethod;

    @ApiModelProperty(value = "api请求地址前缀")
    private String apiUrlPrefix;

    @ApiModelProperty(value = "api请求地址后缀")
    private String apiUrlSuffix;

    @ApiModelProperty(value = "转发地址-对应服务Id")
    private Long apiServerId;

    @ApiModelProperty(value = "api转发地址前缀")
    @TableField(exist = false)
    private String apiServerUrlPrefix;

    @ApiModelProperty(value = "api转发地址后缀")
    private String apiServerUrlSuffix;

    @ApiModelProperty(value = "完整的api转发地址，由前缀和后缀拼接")
    @TableField(exist = false)
    private String apiServerUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态：启用/禁用")
    private Boolean status;

    @ApiModelProperty(value = "api响应实例")
    private String responseExample;
}
