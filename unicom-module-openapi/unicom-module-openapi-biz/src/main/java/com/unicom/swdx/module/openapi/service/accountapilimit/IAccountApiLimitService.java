package com.unicom.swdx.module.openapi.service.accountapilimit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;

import java.util.Collection;
import java.util.List;

public interface IAccountApiLimitService extends IService<AccountApiLimit> {

    /**
     * 分页查询api服务账号限制
     * */
    PageResult<AccountApiLimitPageRespVO> getAccountApiLimitPage(AccountApiLimitPageReqVO accountApiLimitPageReqVO);


    /**
     * 更新api服务账号限制
     *
     * */
    Boolean updateAccountApiLimit(AccountApiLimit accountApiLimit);

    /**
     * 删除
     *
     * */
    void delete(Long accountId, Long apiId);

    /**
     * 账号限制判重
     * */
    Boolean validApiLimitUnique(Long accountId, Long apiId);

    AccountApiLimit checkApiLimit(Long accountId,Long apiId);

    /**
     * 分页查询可授权的api列表
     * */
    PageResult<AvailableApiUrlPageRespVO> getApiPage(AvailableApiUrlPageReqVO req);


    /**
     * 初始化api账号的本地缓存
     */
    void initLocalCache();


    /**
     * 获得指定账号的api限制，从缓存中
     *
     * 任一参数为空时，则返回为空
     *
     * @param apiAccountId api账号编号
     * @param apiId api编号
     * @return api账号限制
     */
    AccountApiLimit getAccountApiLimitListFromCache(Long apiAccountId, Long apiId);

    /**
     * 新增
     */
    boolean save(AccountApiLimit accountApiLimit);
}
