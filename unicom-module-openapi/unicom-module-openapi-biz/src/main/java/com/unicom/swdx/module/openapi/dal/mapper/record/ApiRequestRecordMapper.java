package com.unicom.swdx.module.openapi.dal.mapper.record;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.record.ApiRequestRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApiRequestRecordMapper extends BaseMapperX<ApiRequestRecord> {
    List<ApiRecordPageRespVO> selectPage(IPage page, @Param("req") ApiRecordPageReqVO req);
}
