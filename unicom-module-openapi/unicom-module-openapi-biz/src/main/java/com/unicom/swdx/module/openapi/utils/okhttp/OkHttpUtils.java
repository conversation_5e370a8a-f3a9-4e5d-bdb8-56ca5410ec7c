package com.unicom.swdx.module.openapi.utils.okhttp;

import cn.hutool.core.collection.CollUtil;
import lombok.SneakyThrows;
import okhttp3.*;

import java.util.Iterator;
import java.util.Map;

/**
 * 网络请求工厂类
 */
public class OkHttpUtils {

    @SneakyThrows
    public static Response doGet(OkHttpClient client, String url, Map<String, String> headers, Map<String, String> query) {
        StringBuffer urls = new StringBuffer(url);
        if(CollUtil.isNotEmpty(query)) {
            urls.append("?");
            query.forEach((key,value) -> {
                urls.append(key).append("=").append(value).append("&");
            });
            url = urls.substring(0,urls.length() - 1);
        }

        Request.Builder requestBuilder = new Request.Builder().get();
        if (CollUtil.isNotEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.url(url).build();
        return client.newCall(request).execute();
    }

    public static Response doPost(OkHttpClient client, String url, Map<String, String> headers, Map<String, String> query) throws Exception {
        FormBody.Builder formBody = new FormBody.Builder();
        if (CollUtil.isNotEmpty(query)) {
            query.forEach(formBody::add);
        }
        RequestBody body = formBody.build();
        Request.Builder requestBuilder = new Request.Builder();
        if (CollUtil.isNotEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.post(body).build();
        return client.newCall(request).execute();
    }

    public static Response doPost(OkHttpClient client, String url, Map<String, String> headers, String jsonBody) throws Exception {
        RequestBody body = FormBody.create(MediaType.parse("application/json"), jsonBody);
        Request.Builder requestBuilder = new Request.Builder().url(url);
        if (CollUtil.isNotEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.post(body).build();
        return client.newCall(request).execute();
    }

    public static Response doPut(OkHttpClient client, String url, Map<String, String> headers, String jsonBody) throws Exception {
        RequestBody body = FormBody.create(MediaType.parse("application/json"), jsonBody);;
        Request.Builder requestBuilder = new Request.Builder().url(url);
        if (CollUtil.isNotEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.put(body).build();
        return client.newCall(request).execute();

    }

    public static Response doDelete(OkHttpClient client, String url, Map<String, String> headers, String jsonBody) throws Exception {
        RequestBody body = FormBody.create(MediaType.parse("application/json"), jsonBody);;
        Request.Builder requestBuilder = new Request.Builder().url(url);
        if (CollUtil.isNotEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.delete(body).build();
        return client.newCall(request).execute();

    }

}
