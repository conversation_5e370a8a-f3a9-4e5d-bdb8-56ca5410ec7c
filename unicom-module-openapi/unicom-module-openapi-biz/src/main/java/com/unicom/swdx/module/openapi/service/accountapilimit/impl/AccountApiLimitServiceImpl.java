package com.unicom.swdx.module.openapi.service.accountapilimit.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;
import com.unicom.swdx.module.openapi.dal.mapper.accountapilimit.AccountApiLimitMapper;
import com.unicom.swdx.module.openapi.enums.ApiLimitTimeUnitEnums;
import com.unicom.swdx.module.openapi.enums.ApiLimitTypeEnums;
import com.unicom.swdx.module.openapi.mq.producer.account.AccountApiLimitProducer;
import com.unicom.swdx.module.openapi.service.accountapilimit.IAccountApiLimitService;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.*;

@Service
@Slf4j
public class AccountApiLimitServiceImpl extends ServiceImpl<AccountApiLimitMapper,AccountApiLimit> implements IAccountApiLimitService {

    @Resource
    private AccountApiLimitProducer accountApiLimitProducer;

    /**
     * api账号限制缓存
     * key：api账号限制编号
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private volatile Map<Long, AccountApiLimit> accountApiLimitCache;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 初始化 {@link #accountApiLimitCache}
     */
    @Override
    @PostConstruct
    public synchronized void initLocalCache() {
        // 获取api账号列表，如果有更新
        List<AccountApiLimit> accountApiLimits = baseMapper.selectList();
        if (CollUtil.isEmpty(accountApiLimits)) {
            return;
        }
        // 构建缓存
        ImmutableMap.Builder<Long, AccountApiLimit> accountApiLimitBuilder = ImmutableMap.builder();
        accountApiLimits.forEach(accountApiLimit -> {
            accountApiLimitBuilder.put(accountApiLimit.getId(),accountApiLimit);
        });
        accountApiLimitCache = accountApiLimitBuilder.build();
        log.info("[initLocalCache][缓存api账号限制，数量为:{}]", accountApiLimits.size());
    }

    /**
     * 获得指定账号的api限制，从缓存中
     *
     * 任一参数为空时，则返回为空
     *
     * @param apiAccountId api账号编号
     * @param apiId api编号
     * @return api账号限制
     */
    @Override
    public AccountApiLimit getAccountApiLimitListFromCache(Long apiAccountId, Long apiId){
        // 任一一个参数为空，则返回空
        if (Objects.isNull(apiAccountId) || Objects.isNull(apiId)) {
            return null;
        }
        Optional<AccountApiLimit> any = accountApiLimitCache.values().stream().filter(accountApiLimit -> Objects.equals(apiAccountId, accountApiLimit.getAccountId()) &&
                Objects.equals(apiId, accountApiLimit.getApiId())).findAny();
        return any.orElse(null);
    }




    @Override
    /**
     * 分页查询api服务账号限制
     * */
    public PageResult<AccountApiLimitPageRespVO> getAccountApiLimitPage(AccountApiLimitPageReqVO req){
        IPage<AccountApiLimitPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<AccountApiLimitPageRespVO> list = this.baseMapper.selectPage(myPage,req);

        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if (myPage.getTotal() % req.getPageSize() == 0) {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize()));
            } else {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }

        list.forEach(a -> {
                if(a.getHasLimit()) {
                    if(a.getLimitType().equals(ApiLimitTypeEnums.TIME_LIMIT.getCode())) {
                        //minute/hour/day
                        String unit = a.getLimitTimeUnit().equals(ApiLimitTimeUnitEnums.MINUTE.getCode())?"分钟":(a.getLimitTimeUnit().equals(ApiLimitTimeUnitEnums.HOUR.getCode())?"小时":"天");
                        a.setDes(a.getLimitNumber()+"次/" + a.getLimitTimeNumber() + unit);
                    }else {
                        a.setDes("总访问次数为"+a.getLimitNumber()+"次");
                    }
                    Integer useNum = (Integer)redisUtil.get("apiLimit:accountId:" + a.getAccountId() + ":apiId:" + a.getApiId());
                    Integer restNum = a.getLimitNumber();
                    if(Objects.nonNull(useNum)){
                        restNum -= useNum;
                    }
                    a.setLimitRestNumber(restNum);
                }else {
                    a.setDes("无限制");
                }
        });
        return new PageResult<>(list,myPage.getTotal());
    }


    /**
     * 更新api服务账号限制
     *
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAccountApiLimit(AccountApiLimit accountApiLimit){
        LambdaQueryWrapper<AccountApiLimit> wrapper = Wrappers.lambdaQuery();
        this.baseMapper.update(accountApiLimit,wrapper.eq(AccountApiLimit::getAccountId,accountApiLimit.getAccountId()).eq(AccountApiLimit::getApiId,accountApiLimit.getApiId()));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                accountApiLimitProducer.sendRefreshMessage();
            }

        });
        return true;
    }

    /**
     * 删除
     *
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long accountId, Long apiId){
        LambdaQueryWrapper<AccountApiLimit> wrapper = Wrappers.lambdaQuery();
        this.baseMapper.delete(wrapper.eq(AccountApiLimit::getApiId,apiId).eq(AccountApiLimit::getAccountId,accountId));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                accountApiLimitProducer.sendRefreshMessage();
            }

        });
    }

    /**
     * 账号限制判重
     * */
    @Override
    public Boolean validApiLimitUnique(Long accountId, Long apiId){
        LambdaQueryWrapper<AccountApiLimit> wrapper = Wrappers.lambdaQuery();
        List<AccountApiLimit> tempList = baseMapper.selectList(wrapper.eq(AccountApiLimit::getAccountId,accountId).eq(AccountApiLimit::getApiId,apiId));
        return tempList.size() == 0;
    }

    /**
     * 校验api调用的授权及次数限制
     * @param accountId
     * @param apiId
     */
    @Override
    public AccountApiLimit checkApiLimit(Long accountId,Long apiId){
        //1.拿到对应的api访问规则
        AccountApiLimit limit = this.getAccountApiLimitListFromCache(accountId,apiId);
        if (Objects.isNull(limit)) {
            throw exception(API_UNAUTH);
        }
        // 是否有限制,如果没有限制，则直接返回
        if (!limit.getHasLimit()) {
            return limit;
        }

        // 获取redis的账号api调用次数
        String redisKey = "apiLimit:accountId:" + accountId + ":apiId:" + apiId;
        Object value = redisUtil.get(redisKey);
        long apiCount = Objects.isNull(value) ? 0 : Long.parseLong(value.toString());
        if (apiCount < 1) {
            // 属于周期内第一次调用，初始化为0，待调用成功后，自增1
            redisUtil.incr(redisKey,0);
            // 时间范围内有次数限制，设置过期时间（总次数限制不设置过期时间）
            if (StrUtil.equals(limit.getLimitType(),ApiLimitTypeEnums.TIME_LIMIT.getCode())) {
                long expiredTime = 0;
                // 过期时间计算
                ApiLimitTimeUnitEnums unit = ApiLimitTimeUnitEnums.codeToType(limit.getLimitTimeUnit());
                if(Objects.nonNull(unit)){
                    expiredTime = (long) limit.getLimitTimeNumber() * unit.getSecNum();
                }
                // redis设置过期时间
                redisUtil.expire(redisKey,expiredTime);
            }
        } else if (apiCount >= limit.getLimitNumber()) {
            // 调用超过次数限制
            throw exception(API_REQUEST_LIMIT);
        }
        return limit;
    }

    /**
     * 分页查询可授权的api列表
     * */
    @Override
    public PageResult<AvailableApiUrlPageRespVO> getApiPage(AvailableApiUrlPageReqVO req){
        IPage<AvailableApiUrlPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<AvailableApiUrlPageRespVO> list = this.baseMapper.selectApiPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if(myPage.getTotal() % req.getPageSize() == 0){
                req.setPageNo((int) (myPage.getTotal()/req.getPageSize()));
            }else {
                req.setPageNo((int) (myPage.getTotal()/req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectApiPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    /**
     * 新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(AccountApiLimit accountApiLimit){
        boolean flag = super.save(accountApiLimit);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                accountApiLimitProducer.sendRefreshMessage();
            }

        });
        return flag;
    }

}
