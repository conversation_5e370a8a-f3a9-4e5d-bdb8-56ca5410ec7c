package com.unicom.swdx.module.openapi.service.apiapplication;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiapplication.ApiApplicationDO;

public interface IApiApplicationService extends IService<ApiApplicationDO> {

    /**
     * 分页查询应用管理
     *
     * */
    PageResult<ApiApplicationPageRespVO> getApiApplicationPage(ApiApplicationPageReqVO req);

    /**
     * 检验名字独特
     * */
    Boolean validNameUnique(String name,Long id);

    /**
     * 检验编号独特
     * */
    Boolean validCodeUnique(String code,Long id);
    /**
     * 删除
     * */
    void delete(Long id);
}
