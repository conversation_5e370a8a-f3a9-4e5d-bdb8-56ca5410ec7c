package com.unicom.swdx.module.openapi.dal.dataobject.apiurl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("api_url_param")
public class ApiUrlParam extends BaseDO {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long paramId;

    @ApiModelProperty(value = "参数所属apiId")
    private Long apiId;

    @ApiModelProperty(value = "参数名称")
    private String paramName;

    @ApiModelProperty(value = "必填与否")
    private Boolean paramRequired;

    /**
     * string,number,date,boolean,json,file,other
     */
    @ApiModelProperty(value = "数据类型")
    private String paramType;

    /**
     * Header,Query,Body
     */
    @ApiModelProperty(value = "参数类型")
    private String queryType;

    @ApiModelProperty(value = "备注")
    private String remark;
}
