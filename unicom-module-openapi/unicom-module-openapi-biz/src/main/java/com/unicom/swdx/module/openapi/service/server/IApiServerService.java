package com.unicom.swdx.module.openapi.service.server;


import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerSimpleVO;
import com.unicom.swdx.module.openapi.dal.dataobject.server.ApiServer;

import java.util.List;

/**
 * api服务 服务层
 *
 * <AUTHOR>
 */
public interface IApiServerService extends IService<ApiServer> {
    void initLocalCache();

    void delete(Long id);

    PageResult<ApiServerPageRespVO> getApiServerPage(ApiServerPageReqVO req);

    Boolean validCodeUnique(String code, Long id);

    List<ApiServerSimpleVO> getApiServerListFromCache();

    Boolean checkRemove(Long id);
}
