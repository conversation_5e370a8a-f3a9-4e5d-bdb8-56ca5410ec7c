package com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("账号api访问限制分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountApiLimitPageReqVO  extends PageParam {
    @ApiModelProperty(value = "api地址")
    private String apiUrlSuffix;

    @ApiModelProperty(value = "api账号id")
    private Long accountId;
}
