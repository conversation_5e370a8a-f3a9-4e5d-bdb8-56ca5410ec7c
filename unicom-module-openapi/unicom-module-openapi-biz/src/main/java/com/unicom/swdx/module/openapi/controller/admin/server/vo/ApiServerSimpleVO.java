package com.unicom.swdx.module.openapi.controller.admin.server.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("Api注册页面选择服务的返回对象ApiServerSimpleVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiServerSimpleVO {
    private Long serverId;

    @ApiModelProperty(value = "服务名称")
    private String serverName;

    @ApiModelProperty(value = "转发服务地址")
    private String serverUrl;
}
