package com.unicom.swdx.module.openapi.dal.mapper.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.server.ApiServer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * api服务 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiServerMapper extends BaseMapperX<ApiServer> {
    List<ApiServerPageRespVO> selectPage(IPage page, @Param("req") ApiServerPageReqVO req);
}
