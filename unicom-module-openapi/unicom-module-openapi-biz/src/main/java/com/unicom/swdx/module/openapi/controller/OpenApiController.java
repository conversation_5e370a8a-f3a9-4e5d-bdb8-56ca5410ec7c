package com.unicom.swdx.module.openapi.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrlParam;
import com.unicom.swdx.module.openapi.dal.dataobject.record.ApiRequestRecord;
import com.unicom.swdx.module.openapi.service.account.IApiAccountService;
import com.unicom.swdx.module.openapi.service.accountapilimit.IAccountApiLimitService;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlParamService;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlService;
import com.unicom.swdx.module.openapi.service.record.IApiRequestRecordService;
import com.unicom.swdx.module.openapi.utils.okhttp.OkHttpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2023/11/28 15:01
 **/
@Controller
@RequestMapping("/open-api")
@Api(tags = "开放API调用接口")
public class OpenApiController {

    @Resource
    private IApiAccountService accountService;

    @Resource
    private IApiUrlService apiUrlService;

    @Resource
    private IApiUrlParamService apiUrlParamService;

    @Resource
    private IAccountApiLimitService accountApiLimitService;

    @Resource
    private OkHttpClient okHttpClient;

    @Resource
    private IApiRequestRecordService recordService;

    @Resource
    private RedisUtil redisUtil;


    @RequestMapping("/**")
    @PermitAll
    @SneakyThrows
    @ApiOperation("通用接口")
    public void api(HttpServletRequest request, HttpServletResponse response) {
        // todo 该方法涉及的所有查询全部使用缓存，或异步，用以提高接口效率
        // 1、验证权限
        Map<String, String> params = buildParamMap(request.getParameterMap());
        Long accountId = checkKeyAndSecret(params);

        // 2、转发请求
        // 获取请求地址后缀
        String requestURI = request.getRequestURI();
        requestURI = requestURI.substring(9);

        // 查找该账号是否有该请求地址权限
        // 查询url对应的api接口
        ApiUrl apiUrl = apiUrlService.getByUri(requestURI);
        if (Objects.isNull(apiUrl)) {
            throw exception(API_URL_NOT_EXIST);
        }
        if (!apiUrl.getStatus()) {
            throw exception(API_DISABLE);
        }
        // 校验该账号是否授权该api接口
        AccountApiLimit limit = accountApiLimitService.checkApiLimit(accountId, apiUrl.getApiId());

        ApiRequestRecord record = new ApiRequestRecord();
        record.setAppKey(params.get("appKey"));
        //有body参数就保存body参数，没有body就保存query参数
        String requestBody = getRequestJson(request);
        if(!requestBody.isEmpty() && !"{}".equals(requestBody)){
            record.setRequestParams(requestBody);
        }else {
            record.setRequestParams(params.toString());
        }
        // 接口调用，并写回返回内容
        this.sendUrl(apiUrl, request, response, params, record);
        if (record.getForwardSuccess()) {
            // 有限制的，写入redis
            if (limit.getHasLimit()) {
                redisUtil.incr("apiLimit:accountId:" + accountId + ":apiId:" + apiUrl.getApiId(),1);
            }
        }
        record.setApiId(apiUrl.getApiId());
        record.setApiName(apiUrl.getApiName());
        record.setRequestIp(request.getRemoteHost());
        record.setRequestMethod(request.getMethod());
        record.setApiUrlSuffix(requestURI);
        record.setRequestUrl(request.getRequestURL().toString());
        record.setForwardUrl(apiUrl.getApiServerUrlPrefix() + apiUrl.getApiServerUrlSuffix());
        // 异步记录日志
        recordService.asyncSave(record);
    }

    /**
     * 参数转配普通map
     *
     * @param parameterMap 参数map
     */
    private Map<String, String> buildParamMap(Map<String, String[]> parameterMap) {
        Map<String, String> params = new HashMap<>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String key = entry.getKey();
            String[] value = entry.getValue();
            if (!ArrayUtils.isEmpty(value)) {
                if (StringUtils.isNotBlank(value[0])) {
                    params.put(key, value[0]);
                }
            }
        }
        return params;
    }

    /**
     * 校验appKey和appSecret
     *
     * @param params
     */
    private Long checkKeyAndSecret(Map<String, String> params) {
        // 校验appKey和appSecret是否存在
        ApiAccount apiAccount = accountService.getFromCacheByKeyAndSecret(params.get("appKey"), params.get("appSecret"));
        if (Objects.isNull(apiAccount)) {
            throw exception(API_ACCOUNT_NOT_EXIST);
        }
        return apiAccount.getAccountId();
    }

    @SneakyThrows
    private void sendUrl(ApiUrl apiUrl, HttpServletRequest oriRequest, HttpServletResponse oriResponse, Map<String, String> params,ApiRequestRecord record) {
        String url = apiUrl.getApiServerUrlPrefix() + apiUrl.getApiServerUrlSuffix();

        params.remove("appKey");
        params.remove("appSecret");

        // todo 待优化
        String method = oriRequest.getMethod();

        Map<String,String> headers = new HashMap<>();
        headers.put("Authorization",oriRequest.getHeader("Authorization"));

        List<ApiUrlParam> headNames = apiUrlParamService.getHeadNames(apiUrl.getApiId());
        if (CollUtil.isNotEmpty(headNames)) {
            headNames.forEach(param -> {
                String header = oriRequest.getHeader(param.getParamName());
                if (StrUtil.isNotBlank(header)) {
                    headers.put(param.getParamName(), header);
                }
            });
        }


        Response response = null;

        long start = System.currentTimeMillis();
        record.setRequestTime(LocalDateTime.now());
        try {
            if (StrUtil.equals(method,"GET")) {
                response = OkHttpUtils.doGet(okHttpClient, url, headers, params);
            } else if (StrUtil.equals(method,"POST")) {
                response = OkHttpUtils.doPost(okHttpClient, url, headers, getRequestJson(oriRequest));
            } else if (StrUtil.equals(method,"PUT")) {
                response = OkHttpUtils.doPut(okHttpClient, url, headers, getRequestJson(oriRequest));
            } else if (StrUtil.equals(method,"DELETE")) {
                response = OkHttpUtils.doDelete(okHttpClient, url, headers, getRequestJson(oriRequest));
            } else {
                throw exception(METHOD_NOT_SUPPORT);
            }
            record.setForwardSuccess(true);
            record.setResponseContentType(response.header("Content-Type"));
        } catch (Exception e) {
            record.setForwardSuccess(false);
        }
        long end = System.currentTimeMillis();
        record.setUseTime((int) (end - start));

        oriResponse.setCharacterEncoding("UTF-8");
        if (response != null) {
            oriResponse.setContentType(response.header("Content-Type"));
        }
        if (record.getForwardSuccess()) {
            if (response.body() != null) {
                oriResponse.getWriter().write(response.body().string());
            }
        } else {
            oriResponse.getWriter().write("{\"code\": 500,\"data\": null,\"msg\": \"接口调用失败，请检查api接口是否正常通讯!\"}");
        }




        // todo 换成异步
        //方法二,异步方法，放到队列中,处于子线程中，无法更新UI
//        call.enqueue(new Callback() {
//            //请求时失败时调用
//            @Override
//            public void onFailure(Call call, IOException e) {
//
//            }
//
//            //请求成功时调用
//            @Override
//            public void onResponse(Call call, Response response) throws IOException {
//                //处于子线程中，能够进行大文件下载，但是无法更新UI
//                final String res = response.body().string();//请求成功时返回的东西
//                //InputStream is=response.body().byteStream();
//                // 执行IO操作时，能够下载很大的文件，并且不会占用很大内存
//                /**
//                 * runOnUiThread方法切换到主线程中，或者用handler机制也可以
//                 */
////                runOnUiThread(new Runnable() {
////                    @Override
////                    public void run() {
////                        // 更新ui
////                    }
////                });
//
//            }
//        });
    }

    public String getRequestJson(HttpServletRequest request) throws IOException {
        StringBuilder buffer = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }
        return buffer.toString();
    }

}
