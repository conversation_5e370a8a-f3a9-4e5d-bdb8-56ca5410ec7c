package com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("应用管理分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiApplicationPageReqVO extends PageParam {

    /**
     * 应用标识
     */
    @ApiModelProperty(value = "应用标识")
    private String code;

    /**
     * 应用名
     */
    @ApiModelProperty(value = "应用名")
    private String name;

    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
}
