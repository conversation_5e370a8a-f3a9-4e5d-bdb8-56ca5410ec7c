package com.unicom.swdx.module.openapi.controller.admin.accountapilimit;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AccountApiLimitPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.accountapilimit.vo.AvailableApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiGroup;
import com.unicom.swdx.module.openapi.service.accountapilimit.IAccountApiLimitService;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.LIMIT_ID_NOT_UNIQUE;

@RestController
@Api(tags = "共性应用-API接入管理-API 授权")
@RequestMapping("/openapi/accountApiLimit")
public class AccountApiLimitController {
    @Resource
    private IAccountApiLimitService accountApiLimitService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 分页查询可被授权的api服务
     * */
    @GetMapping("/apiPage")
    @ApiOperation("分页查询可被授权的api服务")
    @PreAuthorize("@ss.hasPermission('openapi:accountApiLimit:query')")
    public CommonResult<PageResult<AvailableApiUrlPageRespVO>> apiPage(@Valid AvailableApiUrlPageReqVO availableApiUrlPageReqVO){
        return CommonResult.success(accountApiLimitService.getApiPage(availableApiUrlPageReqVO));
    }

    /**
     * 分页查询api服务账号限制
     */
    @GetMapping("/page")
    @ApiOperation("分页查询api服务账号限制")
    @PreAuthorize("@ss.hasPermission('openapi:accountApiLimit:query')")
    public CommonResult<PageResult<AccountApiLimitPageRespVO>> page(@Valid AccountApiLimitPageReqVO accountApiLimitPageReqVO) {
        return CommonResult.success(accountApiLimitService.getAccountApiLimitPage(accountApiLimitPageReqVO));
    }


    /**
     * 新增api服务账号限制
     */
    @PostMapping("/add")
    @ApiOperation("新增api服务账号限制")
    @PreAuthorize("@ss.hasPermission('openapi:accountApiLimit:create')")
    public CommonResult<Boolean> addSave(AccountApiLimit accountApiLimit) {
        // todo 入参校验
        if(!accountApiLimitService.validApiLimitUnique(accountApiLimit.getAccountId(), accountApiLimit.getApiId())){
            throw exception(LIMIT_ID_NOT_UNIQUE);
        }
        if(accountApiLimit.getHasLimit()){
            accountApiLimit.setLimitRestNumber(accountApiLimit.getLimitNumber());
        }
        accountApiLimitService.save(accountApiLimit);
        return CommonResult.success(true);
    }

    /**
     * 修改api服务账号限制
     */
    @PostMapping("/edit")
    @ResponseBody
    @ApiOperation("修改api服务账号限制")
    @PreAuthorize("@ss.hasPermission('openapi:accountApiLimit:update')")
    public CommonResult<Boolean> editSave(AccountApiLimit accountApiLimit) {
        // todo 入参校验
        if(accountApiLimit.getHasLimit()){
            accountApiLimit.setLimitRestNumber(accountApiLimit.getLimitNumber());
            if(Objects.nonNull(redisUtil.get("apiLimit:accountId:" + accountApiLimit.getAccountId() + ":apiId:" + accountApiLimit.getApiId()))) {
                redisUtil.del("apiLimit:accountId:" + accountApiLimit.getAccountId() + ":apiId:" + accountApiLimit.getApiId());
            }
        }
        accountApiLimitService.updateAccountApiLimit(accountApiLimit);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务账号限制
     */
    @PostMapping("/remove")
    @ResponseBody
    @ApiOperation("删除api服务账号限制")
    @PreAuthorize("@ss.hasPermission('openapi:accountApiLimit:delete')")
    public CommonResult<Boolean> remove(@RequestParam("accountId") Long accountId,@RequestParam("apiId") Long apiId) {
        accountApiLimitService.delete(accountId,apiId);
        return CommonResult.success(true);
    }

}
