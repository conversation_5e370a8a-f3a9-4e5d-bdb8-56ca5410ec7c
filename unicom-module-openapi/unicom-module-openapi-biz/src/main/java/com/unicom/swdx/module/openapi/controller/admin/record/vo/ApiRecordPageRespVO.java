package com.unicom.swdx.module.openapi.controller.admin.record.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

@ApiModel("Api Record分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class ApiRecordPageRespVO {
    private Long id;

    @ApiModelProperty(value = "帐号appKey")
    private String appKey;

    @ApiModelProperty(value = "API名称")
    private String apiName;

    @ApiModelProperty(value = "请求来源Ip")
    private String requestIp;

    @ApiModelProperty(value = "请求来源地理位置")
    private String requestLocation;

    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    @ApiModelProperty(value = "API地址后缀")
    private String apiUrlSuffix;

    @ApiModelProperty(value = "请求API")
    private String requestUrl;

    @ApiModelProperty(value = "转发API")
    private String forwardUrl;

    @ApiModelProperty(value = "转发用时ms")
    private Integer useTime;

    @ApiModelProperty(value = "请求时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requestTime;

    @ApiModelProperty(value = "请求参数")
    private String requestParams;

    @ApiModelProperty(value = "响应数据结构")
    private String responseContentType;

    @ApiModelProperty(value = "转发状态（与请求结果无关）")
    private Boolean forwardSuccess;
}
