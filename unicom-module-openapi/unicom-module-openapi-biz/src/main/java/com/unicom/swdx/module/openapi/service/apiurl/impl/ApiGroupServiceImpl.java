package com.unicom.swdx.module.openapi.service.apiurl.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiGroup;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.mapper.apiurl.ApiGroupMapper;
import com.unicom.swdx.module.openapi.dal.mapper.apiurl.ApiUrlMapper;
import com.unicom.swdx.module.openapi.service.apiurl.IApiGroupService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
public class ApiGroupServiceImpl extends ServiceImpl<ApiGroupMapper, ApiGroup> implements IApiGroupService {
    @Resource
    private ApiUrlMapper apiUrlMapper;

    @Override
    public void delete(Long id) {
        LambdaQueryWrapper<ApiUrl> wrapper = Wrappers.lambdaQuery();
        int count = apiUrlMapper.selectCount(wrapper.eq(ApiUrl::getApiGroupId, id)).intValue();
        if(count != 0){
            ErrorCode API_GROUP_DELETE_DENY = new ErrorCode(1004002003, "该分组下有"+count+"条服务，无法删除！");
            throw exception(API_GROUP_DELETE_DENY);
        }
        removeById(id);
    }

    @Override
    /**
     * 检验名字独特
     * */
    public Boolean validNameUnique(String name,Long id){
        LambdaQueryWrapper<ApiGroup> wrapper = Wrappers.lambdaQuery();
        List<ApiGroup> tempList = baseMapper.selectList(wrapper.eq(ApiGroup::getApiGroupName,name));
        if(Objects.isNull(id)){
            return tempList.isEmpty();
        }else {
            if (tempList.isEmpty()) {
                return true;
            }else if (tempList.size() == 1){
                return tempList.get(0).getApiGroupId().equals(id);
            }else {
                return false;
            }
        }
    }
}
