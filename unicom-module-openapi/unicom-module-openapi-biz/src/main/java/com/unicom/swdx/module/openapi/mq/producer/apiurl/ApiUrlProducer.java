package com.unicom.swdx.module.openapi.mq.producer.apiurl;

import com.unicom.swdx.module.openapi.mq.message.RefreshMessage;
import com.unicom.swdx.module.openapi.mq.producer.AbstractProducer;
import com.unicom.swdx.module.openapi.mq.producer.RefreshMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.unicom.swdx.module.openapi.mq.message.MessageConstant.REFRESH_MESSAGE_OUTPUT;


/**
 * apiAccount 相关消息的 Producer
 */
@Component
@Slf4j
public class ApiUrlProducer extends AbstractProducer implements RefreshMessageProducer {

    @Override
    public void sendRefreshMessage() {
        log.info("[send][ ApiUrlRefresh 发送刷新消息]");
        streamBridge.send(REFRESH_MESSAGE_OUTPUT, new RefreshMessage("ApiUrlRefresh"));
    }
}
