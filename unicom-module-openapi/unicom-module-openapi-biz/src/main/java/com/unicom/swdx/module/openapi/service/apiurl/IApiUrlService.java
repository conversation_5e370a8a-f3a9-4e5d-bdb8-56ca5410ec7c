package com.unicom.swdx.module.openapi.service.apiurl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlExportReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlExportRespVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;

import java.util.List;

public interface IApiUrlService extends IService<ApiUrl> {

    /**
     * 初始化api的本地缓存
     */
    void initLocalCache();

    void delete(Long id);

    /**
     * 分页查询
     *
     * */
    PageResult<ApiUrlPageRespVO> getApiUrlPage(ApiUrlPageReqVO req);

    /**
     * 导出
     *
     * */
    List<ApiUrlExportRespVO> getApiUrlList(ApiUrlExportReqVO reqVO);

    /**
     * 检验名字独特
     * */
    Boolean validNameOrUriUnique(Boolean isName,String nameOrUri,Long id);

    /**
     * 保存响应示例
     *
     * */
    Boolean saveResponseExample(String resp,Long id);

    /**
     * 检查删除可行性
     */
    Boolean checkRemove(Long id);

    /**
     * 根据uri获取api信息
     * @param uri 地址
     * @return api
     */
    ApiUrl getByUri(String uri);

    List<String> getServerUrl();

    /**
     * 新增
     */
    boolean save(ApiUrl apiUrl);

    /**
     * 更新
     * */
    boolean updateById(ApiUrl apiUrl);
}
