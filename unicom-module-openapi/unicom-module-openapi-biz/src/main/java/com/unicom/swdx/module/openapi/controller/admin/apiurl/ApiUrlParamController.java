package com.unicom.swdx.module.openapi.controller.admin.apiurl;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrlParam;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_GROUP_NAME_NOT_UNIQUE;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_PARAM_NAME_NOT_UNIQUE;

@Api(tags = "共性应用-API接入管理-API参数管理")
@RestController
@RequestMapping("/openapi/apiUrlParam")
public class ApiUrlParamController {
    @Resource
    private IApiUrlParamService apiUrlParamService;

    /**
     * 分页查询api参数
     * */
    @GetMapping("/page")
    @ApiOperation("分页查询apiParam")
    @PreAuthorize("@ss.hasPermission('openapi:apiParam:page')")
    public CommonResult<PageResult<ApiParamPageRespVO>> page(@Valid ApiParamPageReqVO apiParamPageReqVO){
        return CommonResult.success(apiUrlParamService.getApiParamPage(apiParamPageReqVO));
    }

    /**
     * 新增api服务参数
     */
    @PostMapping("/add")
    @ApiOperation("新增apiParam")
    @PreAuthorize("@ss.hasPermission('openapi:apiParam:add')")
    public CommonResult<Boolean> addSave(@RequestBody ApiUrlParam urlParam) {
        // todo 入参校验
        if(!apiUrlParamService.validNameUnique(urlParam.getApiId(), urlParam.getParamName(),null)){
            throw exception(API_PARAM_NAME_NOT_UNIQUE);
        }
        apiUrlParamService.save(urlParam);
        return CommonResult.success(true);
    }

    /**
     * 修改api服务参数
     */
    @PostMapping("/edit")
    @ApiOperation("编辑apiParam")
    @PreAuthorize("@ss.hasPermission('openapi:apiParam:edit')")
    public CommonResult<Boolean> editSave(@RequestBody ApiUrlParam urlParam) {
        // todo 入参校验
        if(!apiUrlParamService.validNameUnique(urlParam.getApiId(), urlParam.getParamName(), urlParam.getParamId())){
            throw exception(API_PARAM_NAME_NOT_UNIQUE);
        }
        apiUrlParamService.updateById(urlParam);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务参数
     */
    @PostMapping("/remove")
    @ApiOperation("删除apiParam")
    @PreAuthorize("@ss.hasPermission('openapi:apiParam:remove')")
    public CommonResult<Boolean> remove(Long id) {
        apiUrlParamService.delete(id);
        return CommonResult.success(true);
    }
}
