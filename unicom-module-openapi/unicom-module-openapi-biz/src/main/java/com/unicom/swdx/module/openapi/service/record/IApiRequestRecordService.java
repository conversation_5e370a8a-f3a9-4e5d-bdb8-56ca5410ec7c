package com.unicom.swdx.module.openapi.service.record;



import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.record.ApiRequestRecord;


public interface IApiRequestRecordService extends IService<ApiRequestRecord> {
    PageResult<ApiRecordPageRespVO> getApiRecordPage(ApiRecordPageReqVO req);

    void asyncSave(ApiRequestRecord record);


//    void delete(Long id);
}
