package com.unicom.swdx.module.openapi.controller.admin.apiurl;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.*;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.*;

@Api(tags = "共性应用-API注册管理-API URL管理")
@RestController
@RequestMapping("/openapi/apiUrl")
public class ApiUrlController {
    @Resource
    private IApiUrlService apiUrlService;

    /**
     * 分页查询api服务
     * */
    @GetMapping("/page")
    @ApiOperation("分页查询apiUrl")
    @PreAuthorize("@ss.hasAnyPermissions('openapi:apiUrl:page','openapi:apiDocument:page')")
    public CommonResult<PageResult<ApiUrlPageRespVO>> page(@Valid ApiUrlPageReqVO  apiUrlPageReqVO){
        return CommonResult.success(apiUrlService.getApiUrlPage(apiUrlPageReqVO));
    }

    /**
     * 导出apiUrl
     * */
    @GetMapping("/export")
    @ApiOperation("导出apiUrl")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:export')")
    public void export(@Valid ApiUrlExportReqVO exportReqVO,
                       HttpServletResponse response) throws IOException {
        List<ApiUrlExportRespVO> list = apiUrlService.getApiUrlList(exportReqVO);
        String filename = "API url列表_" + DateUtils.datestamp() + ".xlsx";
        ExcelUtils.write(response, filename, "已注册API数据", ApiUrlExportRespVO.class, list);
    }


    /**
     * 新增api服务
     */
    @PostMapping("/add")
    @ApiOperation("新增apiUrl")
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:add')")
    public CommonResult<Boolean> addSave(@RequestBody ApiUrl url) {
        if(!apiUrlService.validNameOrUriUnique(Boolean.TRUE, url.getApiName(), null)){
            throw exception(API_NAME_NOT_UNIQUE);
        }
        if(!apiUrlService.validNameOrUriUnique(Boolean.FALSE, url.getApiUrlSuffix(),null)){
            throw exception(API_URI_NOT_UNIQUE);
        }
        apiUrlService.save(url);
        return CommonResult.success(true);
    }

    /**
     * 修改api服务
     */
    @PostMapping("/edit")
    @ApiOperation("修改apiUrl")
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:edit')")
    public CommonResult<Boolean> editSave(@RequestBody ApiUrl url) {
        if(!apiUrlService.validNameOrUriUnique(Boolean.TRUE, url.getApiName(), url.getApiId())){
            throw exception(API_NAME_NOT_UNIQUE);
        }
        if(!apiUrlService.validNameOrUriUnique(Boolean.FALSE, url.getApiUrlSuffix(), url.getApiId())){
            throw exception(API_URI_NOT_UNIQUE);
        }
        apiUrlService.updateById(url);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务
     */
    @PostMapping("/remove")
    @ApiOperation("删除apiUrl")
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:remove')")
    public CommonResult<Boolean> remove(Long id) {
        if(apiUrlService.checkRemove(id)){
            apiUrlService.delete(id);
            return CommonResult.success(true);
        }else {
            throw exception(API_CAN_NOT_DELETE);
        }
    }

    /**
     * 下拉框选择后端服务地址
     */
    @GetMapping("/getServerUrl")
    @ApiOperation("后端服务地址列表")
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:add')")
    public CommonResult<List<String>> getServerUrl(){
        return CommonResult.success(apiUrlService.getServerUrl());
    }

    /**
     * 根据id获取api服务
     * */
    @GetMapping("/get")
    @ApiOperation("根据id获取api服务")
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:get')")
    public CommonResult<ApiUrl> get(Long id){
        return CommonResult.success(apiUrlService.getById(id));
    }

    /**
     * 手动保存响应体
     * */
    @PostMapping("/saveExample")
    @ApiOperation("手动保存响应体")
    @PreAuthorize("@ss.hasPermission('openapi:apiUrl:save')")
    public CommonResult<Boolean> saveExample(@RequestBody ApiUrlSaveExampleReqVO exampleReqVO){
        return CommonResult.success(apiUrlService.saveResponseExample(exampleReqVO.getExample(),exampleReqVO.getId()));
    }
}
