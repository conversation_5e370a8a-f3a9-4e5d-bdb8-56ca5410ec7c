package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("Api Url 保存响应体 Request VO")
@Data
@ToString(callSuper = true)
public class ApiUrlSaveExampleReqVO {
    @ApiModelProperty(value = "请求体")
    private String example;

    @ApiModelProperty(value = "api id")
    private Long id;
}
