package com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("应用管理分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class ApiApplicationPageRespVO {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 应用标识
     */
    @ApiModelProperty(value = "应用标识")
    private String code;

    /**
     * 应用名
     */
    @ApiModelProperty(value = "应用名")
    private String name;

    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 系统提供商
     */
    @ApiModelProperty(value = "系统提供商")
    private String systemProvider;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
