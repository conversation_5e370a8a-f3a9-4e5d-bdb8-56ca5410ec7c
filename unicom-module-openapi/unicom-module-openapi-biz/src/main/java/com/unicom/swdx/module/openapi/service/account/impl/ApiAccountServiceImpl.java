package com.unicom.swdx.module.openapi.service.account.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.mapper.account.ApiAccountMapper;
import com.unicom.swdx.module.openapi.mq.producer.account.ApiAccountProducer;
import com.unicom.swdx.module.openapi.service.account.IApiAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.*;

import java.util.*;

@Service
@Slf4j
public class ApiAccountServiceImpl extends ServiceImpl<ApiAccountMapper, ApiAccount> implements IApiAccountService {

    @Resource
    private ApiAccountProducer apiAccountProducer;

    /**
     * api账号缓存
     * key：api账号编号
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private volatile Map<Long, ApiAccount> apiAccountCache;

    /**
     * 初始化 {@link #apiAccountCache}
     */
    @Override
    @PostConstruct
    public synchronized void initLocalCache() {
        // 获取api账号列表，如果有更新
        List<ApiAccount> apiAccountList = baseMapper.selectList();
        if (CollUtil.isEmpty(apiAccountList)) {
            return;
        }
        // 构建缓存
        ImmutableMap.Builder<Long, ApiAccount> apiAccountCacheBuilder = ImmutableMap.builder();
        apiAccountList.forEach(apiAccount -> apiAccountCacheBuilder.put(apiAccount.getAccountId(), apiAccount));
        apiAccountCache = apiAccountCacheBuilder.build();
        log.info("[initLocalCache][缓存api账号，数量为:{}]", apiAccountList.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // todo 校验是否有服务器和api，如果有，则不能删除

        removeById(id);
        // 发送刷新消息. 注意，需要事务提交后，在进行发送刷新消息。不然 db 还未提交，结果缓存先刷新了
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiAccountProducer.sendRefreshMessage();
            }

        });
    }

    /**
     * 新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ApiAccount apiAccount){
         boolean flag = super.save(apiAccount);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiAccountProducer.sendRefreshMessage();
            }

        });
         return flag;
    }

    /**
     * 更新
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(ApiAccount apiAccount){
        boolean flag = super.updateById(apiAccount);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiAccountProducer.sendRefreshMessage();
            }

        });
        return flag;
    }

    @Override
    public ApiAccount getFromCacheByKeyAndSecret(String appKey, String appSecret) {
        if (StrUtil.isBlank(appKey)) {
            throw exception(API_APPKEY_NOTNULL);
        }
        if (StrUtil.isBlank(appSecret)) {
            throw exception(API_APPSECRET_NOTNULL);
        }
        Optional<ApiAccount> any = apiAccountCache.values().stream()
                .filter(apiAccount -> StrUtil.equals(appKey, apiAccount.getAppKey())
                        && StrUtil.equals(appSecret, apiAccount.getAppSecret())).findAny();
        return any.orElse(null);
    }


    /**
     * 分页查询
     *
     * */
    @Override
    public PageResult<ApiAccountPageRespVO> getApiAccountPage(ApiAccountPageReqVO req){
        IPage<ApiAccountPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<ApiAccountPageRespVO> list = this.baseMapper.selectPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if(myPage.getTotal() % req.getPageSize() == 0){
                req.setPageNo((int) (myPage.getTotal()/req.getPageSize()));
            }else {
                req.setPageNo((int) (myPage.getTotal()/req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    /**
     * 检验名字独特
     * */
    public Boolean validNameUnique(String name,Long id){
        LambdaQueryWrapper<ApiAccount> wrapper = Wrappers.lambdaQuery();
        List<ApiAccount> tempList = baseMapper.selectList(wrapper.eq(ApiAccount::getAccountName,name));
        if(Objects.isNull(id)){
            return tempList.size() == 0;
        }else {
            if (tempList.size() == 0) {
                return true;
            }else if (tempList.size() == 1){
                return tempList.get(0).getAccountId().equals(id);
            }else {
                return false;
            }
        }
    }
}
