package com.unicom.swdx.module.openapi.controller.admin.apiurl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiGroup;
import com.unicom.swdx.module.openapi.service.apiurl.IApiGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_GROUP_NAME_NOT_UNIQUE;

@Api(tags = "共性应用-API注册管理-API 分组管理")
@RestController
@RequestMapping("/openapi/apiGroup")
public class ApiGroupController {
    @Resource
    private IApiGroupService apiGroupService;


    /**
     * 查询api服务分组
     * */
    @GetMapping("/list")
    @ApiOperation("查询api服务分组")
    @PreAuthorize("@ss.hasPermission('openapi:apiGroup:list')")
    public CommonResult<List<ApiGroup>> page(@Valid ApiGroup apiGroup){
        LambdaQueryWrapper<ApiGroup> wrapper = Wrappers.lambdaQuery();
        if(Objects.nonNull(apiGroup.getApiGroupName())){
            wrapper.like(ApiGroup::getApiGroupName,apiGroup.getApiGroupName());
        }
        return CommonResult.success(apiGroupService.list(wrapper));
    }

    /**
     * 新增api服务分组
     */
    @PostMapping("/add")
    @ApiOperation("新增api服务分组")
    @PreAuthorize("@ss.hasPermission('openapi:apiGroup:add')")
    public CommonResult<Boolean> addSave(@RequestBody ApiGroup group) {
        // todo 入参校验
        if(!apiGroupService.validNameUnique(group.getApiGroupName(),null)){
            throw exception(API_GROUP_NAME_NOT_UNIQUE);
        }
        apiGroupService.save(group);
        return CommonResult.success(true);
    }

    /**
     * 修改api服务分组
     */
    @PostMapping("/edit")
    @ApiOperation("修改api服务分组")
    @PreAuthorize("@ss.hasPermission('openapi:apiGroup:edit')")
    public CommonResult<Boolean> editSave(@RequestBody ApiGroup group) {
        // todo 入参校验
        if(!apiGroupService.validNameUnique(group.getApiGroupName(),group.getApiGroupId())){
            throw exception(API_GROUP_NAME_NOT_UNIQUE);
        }
        apiGroupService.updateById(group);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务分组
     */
    @PostMapping("/remove")
    @ApiOperation("删除api服务分组")
    @PreAuthorize("@ss.hasPermission('openapi:apiGroup:remove')")
    public CommonResult<Boolean> remove(Long id) {
        apiGroupService.delete(id);
        return CommonResult.success(true);
    }
}
