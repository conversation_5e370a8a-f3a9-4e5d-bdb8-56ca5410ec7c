package com.unicom.swdx.module.openapi.dal.mapper.account;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApiAccountMapper extends BaseMapperX<ApiAccount> {

    /**
     * 分页查询api账号
     */
    List<ApiAccountPageRespVO> selectPage(IPage page, @Param("req") ApiAccountPageReqVO req);
}
