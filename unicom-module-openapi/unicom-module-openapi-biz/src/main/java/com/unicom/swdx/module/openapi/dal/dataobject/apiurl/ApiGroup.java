package com.unicom.swdx.module.openapi.dal.dataobject.apiurl;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("api_group")
public class ApiGroup extends BaseDO {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long apiGroupId;

    @ApiModelProperty(value = "分组名称")
    private String apiGroupName;

    @ApiModelProperty(value = "备注")
    private String remark;
}
