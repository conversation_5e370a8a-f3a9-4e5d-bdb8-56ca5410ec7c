package com.unicom.swdx.module.openapi.dal.dataobject.account;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("api_account")
public class ApiAccount extends BaseDO {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;


    /*
    * 账号id
    */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "账号id")
    private Long accountId;

    /*
     * 账号名称
     */
    @ApiModelProperty(value = "账号名称")
    private String accountName;

    /*
     * api访问秘钥
     */
    @ApiModelProperty(value = "api访问秘钥")
    private String appKey;

    /*
     * api访问秘钥（md5加密后）
     */
    @ApiModelProperty(value = "api访问秘钥（md5加密后）")
    private String appSecret;

    /*
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
