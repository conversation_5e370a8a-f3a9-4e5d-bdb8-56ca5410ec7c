package com.unicom.swdx.module.openapi.controller.admin.account.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("Api服务分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class ApiAccountPageRespVO {

    @ApiModelProperty(value = "api账号id")
    private Long accountId;

    @ApiModelProperty(value = "api账号名称")
    private String accountName;

    @ApiModelProperty(value = "apiKey")
    private String appKey;

    @ApiModelProperty(value = "appSecret")
    private String appSecret;

    @ApiModelProperty(value = "备注")
    private String remark;
}
