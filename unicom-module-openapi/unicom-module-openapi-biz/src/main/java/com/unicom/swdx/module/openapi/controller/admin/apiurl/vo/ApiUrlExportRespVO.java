package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.openapi.enums.DictTypeConstants;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

@ApiModel("Api Url导出 Response VO")
@Data
@ToString(callSuper = true)
public class ApiUrlExportRespVO {
    @ExcelIgnore
    private Long apiId;

    @ExcelProperty(value = "API名称")
    @ColumnWidth(20)
    private String apiName;

    @ExcelProperty(value = "API地址后缀")
    @ColumnWidth(20)
    private String apiUrlSuffix;

    @ExcelProperty(value = "转发地址")
    @ColumnWidth(20)
    private String apiServerUrl;

    @ExcelProperty(value = "请求方式")
    @ColumnWidth(20)
    private String apiRequestMethod;

    @ExcelProperty(value = "状态",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.API_STATUS)
    @ColumnWidth(20)
    private Boolean status;

    @ExcelProperty(value = "备注")
    @ColumnWidth(20)
    private String remark;
}
