package com.unicom.swdx.module.openapi.controller.admin.account.vo;


import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("Api账号分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiAccountPageReqVO  extends PageParam {

    @ApiModelProperty(value = "api账号名称")
    private String accountName;
}
