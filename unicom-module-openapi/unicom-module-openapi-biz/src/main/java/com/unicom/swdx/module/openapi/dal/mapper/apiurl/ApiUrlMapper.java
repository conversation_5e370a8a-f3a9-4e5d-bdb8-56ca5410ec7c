package com.unicom.swdx.module.openapi.dal.mapper.apiurl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlExportReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlExportRespVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiUrlPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ApiUrlMapper extends BaseMapperX<ApiUrl> {
    /**
     * 分页查询url
     * */
    List<ApiUrlPageRespVO> selectPage(IPage page, @Param("req")ApiUrlPageReqVO req);

    /**
     *
     * */
    List<ApiUrlExportRespVO> selectMyList(@Param("req") ApiUrlExportReqVO reqVO);

    List<String> selectServerUrl();

    List<ApiUrl> selectCacheList();
}
