package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("Api Url导出 Request VO")
@Data
@ToString(callSuper = true)
public class ApiUrlExportReqVO {
    @ApiModelProperty(value = "api名称")
    private String name;

    @ApiModelProperty(value = "请求方式")
    private String apiRequestMethod;

    @ApiModelProperty(value = "api分组id")
    private Long apiGroupId;
}
