package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("Api Url分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class ApiUrlPageRespVO {

    @ApiModelProperty(value = "api id")
    private Long apiId;

    @ApiModelProperty(value = "api 名称")
    private String apiName;

    @ApiModelProperty(value = "api 地址前缀")
    private String apiUrlPrefix;

    @ApiModelProperty(value = "api 地址后缀")
    private String apiUrlSuffix;

    @ApiModelProperty(value = "转发地址-对应服务Id")
    private Long apiServerId;

    @ApiModelProperty(value = "服务Id对应服务名称")
    private String apiServerName;

    @ApiModelProperty(value = "转发地址前缀")
    private String apiServerUrlPrefix;

    @ApiModelProperty(value = "转发地址后缀")
    private String apiServerUrlSuffix;

    @ApiModelProperty(value = "转发地址")
    private String apiServerUrl;

    @ApiModelProperty(value = "请求方式")
    private String apiRequestMethod;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "备注")
    private String remark;
}
