package com.unicom.swdx.module.openapi.service.record.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.record.ApiRequestRecord;
import com.unicom.swdx.module.openapi.dal.mapper.record.ApiRequestRecordMapper;
import com.unicom.swdx.module.openapi.service.record.IApiRequestRecordService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApiRequestRecordServiceImpl extends ServiceImpl<ApiRequestRecordMapper, ApiRequestRecord> implements IApiRequestRecordService {

    @Override
    public PageResult<ApiRecordPageRespVO> getApiRecordPage(ApiRecordPageReqVO req) {
        IPage<ApiRecordPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<ApiRecordPageRespVO> list = this.baseMapper.selectPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if (myPage.getTotal() % req.getPageSize() == 0) {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize()));
            } else {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    @Async
    @Override
    public void asyncSave(ApiRequestRecord record) {
        save(record);
    }

//    @Override
//    public void delete(Long id) {
//        // todo 校验是否有服务器和api，如果有，则不能删除
//        removeById(id);
//    }
}
