package com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("account_api_limit")
public class AccountApiLimit extends BaseDO {
    public final static String LIMIT_TYPE_TIME = "time_limit";
    public final static String LIMIT_TYPE_TOTAL = "total_limit";

    public final static String LIMIT_TYPE_TIME_UNIT_MINUTE = "minute";
    public final static String LIMIT_TYPE_TIME_UNIT_HOUR = "hour";
    public final static String LIMIT_TYPE_TIME_UNIT_DAY = "day";

    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否有限制
     */
    @ApiModelProperty(value = "是否有限制")
    private Boolean hasLimit;

    /**
     * 限制类型，time_limit是时间范围内次数限制，total_limit是总次数限制
     */
    @ApiModelProperty(value = "限制类型，time_limit是时间范围内次数限制，total_limit是总次数限制")
    private String limitType;

    /**
     * 限制调用次数
     */
    @ApiModelProperty(value = "限制调用次数")
    private Integer limitNumber;

    /**
     * 限制时间数量
     */
    @ApiModelProperty(value = "限制时间数量")
    private Integer limitTimeNumber;

    /**
     * 限制时间单位
     */
    @ApiModelProperty(value = "限制时间单位")
    private String limitTimeUnit;

    /**
     * 剩余限制次数
     */
    @ApiModelProperty(value = "剩余限制次数")
    private Integer limitRestNumber;

    /**
     * 账号id
     */
    @ApiModelProperty(value = "账号id")
    private Long accountId;

    /**
     * apiId
     */
    @ApiModelProperty(value = "apiId")
    private Long apiId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
