package com.unicom.swdx.module.openapi.service.apiapplication.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiapplication.ApiApplicationDO;
import com.unicom.swdx.module.openapi.dal.mapper.apiapplication.ApiApplicationMapper;
import com.unicom.swdx.module.openapi.service.apiapplication.IApiApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ApiApplicationServiceImpl extends ServiceImpl<ApiApplicationMapper, ApiApplicationDO> implements IApiApplicationService {

    @Override
    /**
     * 分页查询应用管理
     *
     * */
    public PageResult<ApiApplicationPageRespVO> getApiApplicationPage(ApiApplicationPageReqVO req) {
        IPage<ApiApplicationPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<ApiApplicationPageRespVO> list = this.baseMapper.selectPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if(myPage.getTotal() % req.getPageSize() == 0){
                req.setPageNo((int) (myPage.getTotal()/req.getPageSize()));
            }else {
                req.setPageNo((int) (myPage.getTotal()/req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    /**
     * 检验名字独特
     * */
    public Boolean validNameUnique(String name, Long id) {
        LambdaQueryWrapper<ApiApplicationDO> wrapper = Wrappers.lambdaQuery();
        List<ApiApplicationDO> tempList = baseMapper.selectList(wrapper.eq(ApiApplicationDO::getName, name));
        if (Objects.isNull(id)) {
            return tempList.size() == 0;
        } else {
            if (tempList.size() == 0) {
                return true;
            } else if (tempList.size() == 1) {
                return tempList.get(0).getId().equals(id);
            } else {
                return false;
            }
        }
    }

    @Override
    /**
     * 检验编号独特
     * */
    public Boolean validCodeUnique(String code, Long id) {
        LambdaQueryWrapper<ApiApplicationDO> wrapper = Wrappers.lambdaQuery();
        List<ApiApplicationDO> tempList = baseMapper.selectList(wrapper.eq(ApiApplicationDO::getCode, code));
        if (Objects.isNull(id)) {
            return tempList.size() == 0;
        } else {
            if (tempList.size() == 0) {
                return true;
            } else if (tempList.size() == 1) {
                return tempList.get(0).getId().equals(id);
            } else {
                return false;
            }
        }
    }

    /**
     * 删除
     * */
    @Override
    public void delete(Long id) {
        removeById(id);
    }
}
