package com.unicom.swdx.module.openapi.dal.dataobject.server;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * api服务表 api_server
 *
 * <AUTHOR>
 */
@TableName(value = "api_server", autoResultMap = true)
@KeySequence("api_server_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiServer extends BaseDO
{
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long serverId;

    @ApiModelProperty(value = "服务名称")
    private String serverName;

    @ApiModelProperty(value = "服务编号(唯一")
    private String serverCode;

    @ApiModelProperty(value = "服务访问地址")
    private String serverUrl;

    @ApiModelProperty(value = "备注")
    private String remark;
}

