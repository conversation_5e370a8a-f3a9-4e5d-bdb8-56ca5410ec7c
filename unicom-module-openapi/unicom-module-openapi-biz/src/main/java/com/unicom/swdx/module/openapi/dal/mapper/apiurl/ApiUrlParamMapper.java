package com.unicom.swdx.module.openapi.dal.mapper.apiurl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrlParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApiUrlParamMapper extends BaseMapperX<ApiUrlParam> {
    List<ApiParamPageRespVO> selectPage(IPage page, @Param("req") ApiParamPageReqVO req);
}
