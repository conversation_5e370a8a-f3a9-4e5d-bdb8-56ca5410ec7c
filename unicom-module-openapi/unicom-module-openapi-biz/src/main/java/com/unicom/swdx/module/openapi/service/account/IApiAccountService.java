package com.unicom.swdx.module.openapi.service.account;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.account.vo.ApiAccountPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.account.ApiAccount;

import java.util.Collection;
import java.util.List;

public interface IApiAccountService extends IService<ApiAccount> {


    /**
     * 初始化api账号的本地缓存
     */
    void initLocalCache();


    void delete(Long id);

    /**
     * 根据appKey和appSecret获取账号
     * @param appKey
     * @param appSecret
     * @return
     */
    ApiAccount getFromCacheByKeyAndSecret(String appKey, String appSecret);

    /**
     * 分页查询
     *
     * */
    PageResult<ApiAccountPageRespVO> getApiAccountPage(ApiAccountPageReqVO req);

    /**
     * 新增
     */
    boolean save(ApiAccount apiAccount);

    /**
     * 更新
     * */
    boolean updateById(ApiAccount apiAccount);

    /**
     * 检验名字独特
     * */
    Boolean validNameUnique(String name,Long id);
}
