package com.unicom.swdx.module.openapi.service.apiurl.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiGroup;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrlParam;
import com.unicom.swdx.module.openapi.dal.mapper.apiurl.ApiUrlParamMapper;
import com.unicom.swdx.module.openapi.mq.producer.apiurl.ApiUrlParamProducer;
import com.unicom.swdx.module.openapi.mq.producer.apiurl.ApiUrlProducer;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ApiUrlParamServiceImpl extends ServiceImpl<ApiUrlParamMapper, ApiUrlParam> implements IApiUrlParamService {


    @Resource
    private ApiUrlParamProducer apiUrlParamProducer;

    /**
     * api参数缓存
     * key：api参数编号
     * value: 参数集合
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private volatile Map<Long, List<ApiUrlParam>> apiUrlParamCache;

    /**
     * 初始化 {@link #apiUrlParamCache}
     */
    @Override
    @PostConstruct
    public synchronized void initLocalCache() {
        // 获取api参数列表，如果有更新
        List<ApiUrlParam> apiUrlParamList = baseMapper.selectList();
        if (CollUtil.isEmpty(apiUrlParamList)) {
            return;
        }
        // 构建缓存
        Set<Long> apiIdSet = apiUrlParamList.stream().map(ApiUrlParam::getApiId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(apiIdSet)) {
            return;
        }
        apiUrlParamCache = new HashMap<>();
        apiIdSet.forEach(apiId -> {
            apiUrlParamCache.put(apiId, apiUrlParamList.stream().filter(p -> Objects.equals(p.getApiId(),apiId)).collect(Collectors.toList()));
        });
        log.info("[initLocalCache][缓存api参数，数量为:{}]", apiUrlParamList.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        removeById(id);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiUrlParamProducer.sendRefreshMessage();
            }

        });
    }

    @Override
    public PageResult<ApiParamPageRespVO> getApiParamPage(ApiParamPageReqVO req) {
        IPage<ApiParamPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<ApiParamPageRespVO> list = this.baseMapper.selectPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if (myPage.getTotal() % req.getPageSize() == 0) {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize()));
            } else {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    public List<ApiUrlParam> getHeadNames(Long apiId) {
        List<ApiUrlParam> apiUrlParams = apiUrlParamCache.get(apiId);
        if (CollUtil.isEmpty(apiUrlParams)) {
            return apiUrlParams;
        }
        return apiUrlParams.stream().filter(p -> StrUtil.equals(p.getQueryType(),"Header")).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ApiUrlParam apiUrlParam) {
        boolean save = super.save(apiUrlParam);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiUrlParamProducer.sendRefreshMessage();
            }

        });
        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(ApiUrlParam apiUrlParam) {
        boolean updateById = super.updateById(apiUrlParam);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiUrlParamProducer.sendRefreshMessage();
            }

        });
        return updateById;
    }

    @Override
    /**
     * 检验名字独特
     * */
    public Boolean validNameUnique(Long apiId, String name,Long id){
        LambdaQueryWrapper<ApiUrlParam> wrapper = Wrappers.lambdaQuery();
        List<ApiUrlParam> tempList = baseMapper.selectList(wrapper.eq(ApiUrlParam::getApiId,apiId).eq(ApiUrlParam::getParamName,name));
        if(Objects.isNull(id)){
            return tempList.size() == 0;
        }else {
            if (tempList.size() == 0) {
                return true;
            }else if (tempList.size() == 1){
                return tempList.get(0).getParamId().equals(id);
            }else {
                return false;
            }
        }
    }
}
