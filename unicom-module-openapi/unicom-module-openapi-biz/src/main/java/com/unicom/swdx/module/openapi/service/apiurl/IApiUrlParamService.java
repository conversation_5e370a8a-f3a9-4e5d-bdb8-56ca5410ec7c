package com.unicom.swdx.module.openapi.service.apiurl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiurl.vo.ApiParamPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrlParam;

import java.util.List;

public interface IApiUrlParamService extends IService<ApiUrlParam> {

    /**
     * 初始化api的本地缓存
     */
    void initLocalCache();

    void delete(Long id);

    PageResult<ApiParamPageRespVO> getApiParamPage(ApiParamPageReqVO req);

    List<ApiUrlParam> getHeadNames(Long apiId);

    /**
     * 新增
     */
    boolean save(ApiUrlParam apiUrlParam);

    /**
     * 更新
     * */
    boolean updateById(ApiUrlParam apiUrlParam);

    Boolean validNameUnique(Long apiId, String name,Long id);
}
