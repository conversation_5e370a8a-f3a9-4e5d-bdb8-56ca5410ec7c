package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("Api Url分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiUrlPageReqVO  extends PageParam {

    @ApiModelProperty(value = "api名称")
    private String name;

    @ApiModelProperty(value = "请求方式")
    private String apiRequestMethod;

    @ApiModelProperty(value = "api分组id")
    private Long apiGroupId;
}
