package com.unicom.swdx.module.openapi.controller.admin.server;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.openapi.dal.dataobject.Ztree;
import com.unicom.swdx.module.openapi.dal.dataobject.server.ApiServerGroup;
import com.unicom.swdx.module.openapi.service.server.IApiServerGroupService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * api服务信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/openapi/serverGroup")
public class ApiServerGroupController {

    @Resource
    private IApiServerGroupService serverGroupService;

    @GetMapping("/treeData")
    public CommonResult<List<Ztree>> treeData() {
        List<ApiServerGroup> list = serverGroupService.list();

        List<Ztree> ztrees = new ArrayList<>();
//        Ztree t = new Ztree();
//        t.setId(0L);
//        t.setPId(-1L);
//        t.setName("默认分组");
//        t.setTitle("默认分组");
//        ztrees.add(t);

        for (ApiServerGroup group : list) {
            Ztree ztree = new Ztree();
            ztree.setId(group.getServerGroupId());
            ztree.setPId(-1L);
            ztree.setName(group.getServerGroupName());
            ztree.setTitle(group.getServerGroupName());
            ztrees.add(ztree);
        }
        return CommonResult.success(ztrees);
    }

    /**
     * 新增保存api服务
     */
    @PostMapping("/add")
    public CommonResult<Boolean> addSave(ApiServerGroup serverGroup) {
        // todo 入参校验
        serverGroupService.save(serverGroup);
        return CommonResult.success(true);
    }

    /**
     * 修改保存api服务
     */
    @PostMapping("/edit")
    @ResponseBody
    public CommonResult<Boolean> editSave(ApiServerGroup serverGroup) {
        // todo 入参校验
        serverGroupService.updateById(serverGroup);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务
     */
    @PostMapping("/remove")
    @ResponseBody
    public CommonResult<Boolean> remove(Long id) {
        serverGroupService.delete(id);
        return CommonResult.success(true);
    }

}
