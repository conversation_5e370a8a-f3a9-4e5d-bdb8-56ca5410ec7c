package com.unicom.swdx.module.openapi.controller.admin.record;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.record.vo.ApiRecordPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.record.ApiRequestRecord;
import com.unicom.swdx.module.openapi.service.record.impl.ApiRequestRecordServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "共性应用-API监控")
@RestController
@RequestMapping("/openapi/record")
public class ApiRequestRecordController {
    @Resource
    private ApiRequestRecordServiceImpl apiRequestRecordService;

    /**
     * 分页查询api请求记录
     * */
    @GetMapping("/page")
    @ApiOperation("分页查询apiRecord")
    @PreAuthorize("@ss.hasPermission('openapi:apiRecord:page')")
    public CommonResult<PageResult<ApiRecordPageRespVO>> page(@Valid ApiRecordPageReqVO apiRecordPageReqVO){
        return CommonResult.success(apiRequestRecordService.getApiRecordPage(apiRecordPageReqVO));
    }

    /**
     * 新增保存操作日志记录
     */
    @PostMapping("/add")
    public CommonResult<Boolean> addSave(@RequestBody ApiRequestRecord record) {
        // todo 入参校验
        apiRequestRecordService.save(record);
        return CommonResult.success(true);
    }

    /**
     * 修改保存操作日志记录
     */
    @PostMapping("/edit")
    public CommonResult<Boolean> editSave(@RequestBody ApiRequestRecord record) {
        // todo 入参校验
        apiRequestRecordService.updateById(record);
        return CommonResult.success(true);
    }

    /**
     * 删除操作日志记录
     */
//    @PostMapping("/remove")
//    @ResponseBody
//    public CommonResult<Boolean> remove(Long id) {
//        apiRequestRecordService.delete(id);
//        return CommonResult.success(true);
//    }
}
