package com.unicom.swdx.module.openapi.dal.dataobject.record;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("api_request_record")
public class ApiRequestRecord extends BaseDO {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "帐号appKey")
    private String appKey;

    @ApiModelProperty(value = "apiId")
    private Long apiId;

    @ApiModelProperty(value = "API名称")
    private String apiName;

    @ApiModelProperty(value = "请求来源Ip")
    private String requestIp;

    @ApiModelProperty(value = "请求来源地理位置")
    private String requestLocation;

    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    @ApiModelProperty(value = "API地址后缀")
    private String apiUrlSuffix;

    @ApiModelProperty(value = "请求API")
    private String requestUrl;

    @ApiModelProperty(value = "转发API")
    private String forwardUrl;

    @ApiModelProperty(value = "转发用时ms")
    private Integer useTime;

    @ApiModelProperty(value = "请求时间")
    private LocalDateTime requestTime;

    @ApiModelProperty(value = "请求参数")
    private String requestParams;

    @ApiModelProperty(value = "响应数据结构")
    private String responseContentType;

    @ApiModelProperty(value = "转发状态（与请求结果无关）")
    private Boolean forwardSuccess;

    //不展示，先保留
    private Long serverId;

    private String serverName;
}
