package com.unicom.swdx.module.openapi.service.server.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerSimpleVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import com.unicom.swdx.module.openapi.dal.dataobject.server.ApiServer;
import com.unicom.swdx.module.openapi.dal.mapper.apiurl.ApiUrlMapper;
import com.unicom.swdx.module.openapi.dal.mapper.server.ApiServerMapper;
import com.unicom.swdx.module.openapi.mq.producer.server.ApiServerProducer;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlService;
import com.unicom.swdx.module.openapi.service.server.IApiServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * api服务 服务层实现
 *
 * <AUTHOR>
 * @date 2019-08-05
 */
@Service
@Slf4j
public class ApiServerServiceImpl extends ServiceImpl<ApiServerMapper, ApiServer> implements IApiServerService {

    @Resource
    private ApiServerProducer apiServerProducer;

    private volatile List<ApiServerSimpleVO> apiServerCache;

    /**
     * 初始化 {@link #apiServerCache}
     */
    @Override
    @PostConstruct
    public synchronized void initLocalCache() {
        // 获取api服务列表，如果有更新
        List<ApiServer> apiServerList = baseMapper.selectList(new LambdaQueryWrapper<ApiServer>().orderByDesc(ApiServer::getCreateTime));
        if (CollUtil.isEmpty(apiServerList)) {
            return;
        }
        // 构建缓存
        ImmutableList.Builder<ApiServerSimpleVO> apiServerCacheBuilder = ImmutableList.builder();
        apiServerList.forEach(apiServer -> {
            apiServerCacheBuilder.add(new ApiServerSimpleVO(apiServer.getServerId(),apiServer.getServerName(),apiServer.getServerUrl()));
        });
        apiServerCache = apiServerCacheBuilder.build();
        log.info("[initLocalCache][缓存apiServer，数量为:{}]", apiServerList.size());
    }

    @Resource
    private ApiUrlMapper apiUrlMapper;

    @Override
    public List<ApiServerSimpleVO> getApiServerListFromCache() {
        return apiServerCache;
    }

    /**
     * 新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ApiServer apiServer){
        boolean flag = super.save(apiServer);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiServerProducer.sendRefreshMessage();
            }

        });
        return flag;
    }

    /**
     * 更新
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(ApiServer apiServer){
        boolean flag = super.updateById(apiServer);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiServerProducer.sendRefreshMessage();
            }

        });
        return flag;
    }

    @Override
    public Boolean checkRemove(Long id) {
        LambdaQueryWrapper<ApiUrl> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ApiUrl::getApiServerId,id);
        return apiUrlMapper.selectList(wrapper).isEmpty();
    }

    /**
     * 删除
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        removeById(id);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                apiServerProducer.sendRefreshMessage();
            }

        });
    }

    @Override
    public PageResult<ApiServerPageRespVO> getApiServerPage(ApiServerPageReqVO req) {
        IPage<ApiServerPageRespVO> myPage = MyBatisUtils.buildPage(req);
        List<ApiServerPageRespVO> list = this.baseMapper.selectPage(myPage,req);
        if(myPage.getTotal() < (long) req.getPageNo() * req.getPageSize()) {
            if (myPage.getTotal() % req.getPageSize() == 0) {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize()));
            } else {
                req.setPageNo((int) (myPage.getTotal() / req.getPageSize() + 1));
            }
            myPage = MyBatisUtils.buildPage(req);
            list = this.baseMapper.selectPage(myPage,req);
        }
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    public Boolean validCodeUnique(String code, Long id) {
        LambdaQueryWrapper<ApiServer> wrapper = Wrappers.lambdaQuery();
        List<ApiServer> tempList = baseMapper.selectList(wrapper.eq(ApiServer::getServerCode,code));
        if(Objects.isNull(id)){
            return tempList.isEmpty();
        }else {
            if (tempList.isEmpty()) {
                return true;
            }else if (tempList.size() == 1){
                return tempList.get(0).getServerId().equals(id);
            }else {
                return false;
            }
        }
    }

}
