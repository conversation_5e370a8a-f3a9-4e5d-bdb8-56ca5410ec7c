package com.unicom.swdx.module.openapi.mq.consumer;

import com.unicom.swdx.module.openapi.mq.message.RefreshMessage;
import com.unicom.swdx.module.openapi.service.account.IApiAccountService;
import com.unicom.swdx.module.openapi.service.accountapilimit.IAccountApiLimitService;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlParamService;
import com.unicom.swdx.module.openapi.service.apiurl.IApiUrlService;
import com.unicom.swdx.module.openapi.service.server.IApiServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * Refresh 的消费者
 *
 * <AUTHOR>
 * @date 2023/7/6 15:53
 **/
@Component
@Slf4j
public class RefreshConsumer {

    @Resource
    private IApiAccountService apiAccountService;

    @Resource
    private IAccountApiLimitService accountApiLimitService;

    @Resource
    private IApiUrlService apiUrlService;

    @Resource
    private IApiUrlParamService apiUrlParamService;

    @Resource
    private IApiServerService apiServerService;

    @Bean
    public Consumer<RefreshMessage> refresh() {
        return refreshMessage -> {
            String refreshTag = refreshMessage.getTag();
            log.info("[execute][收到 {} 刷新消息]",refreshTag);
            switch (refreshTag) {
                case "ApiAccountRefresh":
                    // apiAccount
                    apiAccountService.initLocalCache();
                    break;
                case "AccountApiLimitRefresh":
                    // AccountApiLimit
                    accountApiLimitService.initLocalCache();
                    break;
                case "ApiUrlRefresh":
                    apiUrlService.initLocalCache();
                    break;
                case "ApiUrlParamRefresh":
                    apiUrlParamService.initLocalCache();
                    break;
                case "ApiServerRefresh":
                    apiServerService.initLocalCache();
                    break;
                default:
                    log.error("没有配置Consumer");
            }
        };
    }
}
