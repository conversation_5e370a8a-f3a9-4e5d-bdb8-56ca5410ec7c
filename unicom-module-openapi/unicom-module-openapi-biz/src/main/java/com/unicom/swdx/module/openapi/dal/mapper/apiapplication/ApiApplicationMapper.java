package com.unicom.swdx.module.openapi.dal.mapper.apiapplication;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.apiapplication.vo.ApiApplicationPageRespVO;
import com.unicom.swdx.module.openapi.dal.dataobject.apiapplication.ApiApplicationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApiApplicationMapper extends BaseMapperX<ApiApplicationDO> {

    /**
     * 分页查询应用管理
     */
    List<ApiApplicationPageRespVO> selectPage(IPage page,@Param("req")  ApiApplicationPageReqVO reqVO);

}
