package com.unicom.swdx.module.openapi.controller.admin.apiurl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("Api Param分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class ApiParamPageRespVO {
    private Long paramId;

    @ApiModelProperty(value = "所属apiId")
    private Long apiId;

    @ApiModelProperty(value = "参数名称")
    private String paramName;

    @ApiModelProperty(value = "必填与否")
    private Boolean paramRequired;

    /**
     * string,number,date,boolean,json,file,other
     */
    @ApiModelProperty(value = "数据类型")
    private String paramType;

    /**
     * Header,Query,Body
     */
    @ApiModelProperty(value = "参数类型")
    private String queryType;

    @ApiModelProperty(value = "备注")
    private String remark;
}
