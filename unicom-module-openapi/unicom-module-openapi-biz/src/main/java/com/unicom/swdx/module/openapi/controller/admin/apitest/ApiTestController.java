package com.unicom.swdx.module.openapi.controller.admin.apitest;

import com.unicom.swdx.module.openapi.dal.dataobject.accountapilimit.AccountApiLimit;
import com.unicom.swdx.module.openapi.dal.dataobject.apiurl.ApiUrl;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/openapi/show")
public class ApiTestController {
}
class ApiUrlData extends ApiUrl {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private AccountApiLimit limit;

    public AccountApiLimit getLimit() {
        return limit;
    }

    public void setLimit(AccountApiLimit limit) {
        this.limit = limit;
    }
}