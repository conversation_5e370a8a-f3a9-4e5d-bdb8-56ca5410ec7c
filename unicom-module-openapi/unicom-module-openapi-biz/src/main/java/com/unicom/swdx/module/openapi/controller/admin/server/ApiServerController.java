package com.unicom.swdx.module.openapi.controller.admin.server;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageReqVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerPageRespVO;
import com.unicom.swdx.module.openapi.controller.admin.server.vo.ApiServerSimpleVO;
import com.unicom.swdx.module.openapi.dal.dataobject.server.ApiServer;
import com.unicom.swdx.module.openapi.service.server.IApiServerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_SERVER_CAN_NOT_DELETE;
import static com.unicom.swdx.module.openapi.enums.ErrorCodeConstants.API_SERVER_CODE_NOT_UNIQUE;

/**
 * api服务信息操作处理
 *
 * <AUTHOR>
 */
@Api(tags = "共性应用-服务注册管理")
@RestController
@RequestMapping("/openapi/apiServer")
public class ApiServerController {

    @Resource
    private IApiServerService serverService;

    /**
     * 分页查询api参数
     * */
    @GetMapping("/page")
    @ApiOperation("分页查询apiServer")
    @PreAuthorize("@ss.hasPermission('openapi:apiServer:page')")
    public CommonResult<PageResult<ApiServerPageRespVO>> page(@Valid ApiServerPageReqVO apiServerPageReqVO){
        return CommonResult.success(serverService.getApiServerPage(apiServerPageReqVO));
    }

    /**
     * 新增保存api服务
     */
    @PostMapping("/add")
    @ApiOperation("新增保存api服务")
    @PreAuthorize("@ss.hasPermission('openapi:apiServer:add')")
    public CommonResult<Boolean> addSave(@RequestBody ApiServer server) {
        // 服务编号唯一性校验
        if(!serverService.validCodeUnique(server.getServerCode(),null)){
            throw exception(API_SERVER_CODE_NOT_UNIQUE);
        }
        serverService.save(server);
        return CommonResult.success(true);
    }

    /**
     * 修改保存api服务
     */
    @PostMapping("/edit")
    @ApiOperation("修改保存api服务")
    @PreAuthorize("@ss.hasPermission('openapi:apiServer:edit')")
    public CommonResult<Boolean> editSave(@RequestBody ApiServer server) {
        // 服务编号唯一性校验
        if(!serverService.validCodeUnique(server.getServerCode(),server.getServerId())){
            throw exception(API_SERVER_CODE_NOT_UNIQUE);
        }
        serverService.updateById(server);
        return CommonResult.success(true);
    }

    /**
     * 删除api服务
     */
    @PostMapping("/remove")
    @ApiOperation("删除api服务")
    @PreAuthorize("@ss.hasPermission('openapi:apiServer:remove')")
    public CommonResult<Boolean> remove(Long id) {
        //校验服务在api注册页面是否已被选择
        if(!serverService.checkRemove(id)){
            throw exception(API_SERVER_CAN_NOT_DELETE);
        }
        serverService.delete(id);
        return CommonResult.success(true);
    }

    /**
     * API注册时获取服务列表
     */
    @GetMapping("/getServerList")
    @ApiOperation("API注册时获取服务列表")
    @PreAuthorize("@ss.hasPermission('openapi:apiServer:getServerList')")
    public CommonResult<List<ApiServerSimpleVO>> getServerList() {
        return CommonResult.success(serverService.getApiServerListFromCache());
    }
}
