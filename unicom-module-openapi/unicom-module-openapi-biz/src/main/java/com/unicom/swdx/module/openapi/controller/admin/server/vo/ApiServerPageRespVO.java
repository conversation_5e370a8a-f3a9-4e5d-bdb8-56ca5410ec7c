package com.unicom.swdx.module.openapi.controller.admin.server.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("Api Server分页查询 Response VO")
@Data
@ToString(callSuper = true)
public class ApiServerPageRespVO {
    private Long serverId;

    @ApiModelProperty(value = "服务名称")
    private String serverName;

    @ApiModelProperty(value = "服务编号(唯一")
    private String serverCode;

    @ApiModelProperty(value = "服务访问地址")
    private String serverUrl;

    @ApiModelProperty(value = "备注")
    private String remark;
}
