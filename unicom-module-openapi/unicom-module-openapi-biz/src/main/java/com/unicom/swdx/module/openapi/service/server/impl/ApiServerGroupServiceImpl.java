package com.unicom.swdx.module.openapi.service.server.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.openapi.dal.dataobject.server.ApiServerGroup;
import com.unicom.swdx.module.openapi.dal.mapper.server.ApiServerGroupMapper;
import com.unicom.swdx.module.openapi.service.server.IApiServerGroupService;
import org.springframework.stereotype.Service;


/**
 * api服务 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ApiServerGroupServiceImpl extends ServiceImpl<ApiServerGroupMapper, ApiServerGroup> implements IApiServerGroupService {

    @Override
    public void delete(Long id) {
        // todo 校验是否有服务器和api，如果有，则不能删除

        removeById(id);
    }

}
