package com.unicom.swdx.module.openapi.enums;

import com.unicom.swdx.framework.common.enums.RpcConstants;

/**
 * API 相关的枚举
 *
 * <AUTHOR>
 */
public class ApiConstants {

    /**
     * 服务名
     *
     * 注意，需要保证和 spring.application.name 保持一致
     */
    public static final String NAME = "openapi-server";

    public static final String PREFIX = RpcConstants.RPC_API_PREFIX +  "/openapi";

    public static final String VERSION = "${revision}";

    public static final String CLIENT_CODE = "openapi";

}
