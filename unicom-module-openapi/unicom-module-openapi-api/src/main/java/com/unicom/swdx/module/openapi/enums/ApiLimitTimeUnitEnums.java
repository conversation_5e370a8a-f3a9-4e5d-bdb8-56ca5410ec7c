package com.unicom.swdx.module.openapi.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApiLimitTimeUnitEnums {
    DAY("0","天",60*60*24),
    HOUR("1","小时",60*60),
    MINUTE("2","分钟",60);

    private final String code;
    private final String value;
    //秒数
    private final Integer secNum;

    public static ApiLimitTimeUnitEnums codeToType(String code){
        return ArrayUtil.firstMatch(a -> a.getCode().equals(code),values());
    }
}
