package com.unicom.swdx.module.openapi.enums;

import com.unicom.swdx.framework.common.exception.ErrorCode;

/**
 * OpenApi 错误码枚举类
 *
 * openapi 系统，使用 1-004-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== api 调用 ********** ==========
    ErrorCode API_APPKEY_NOTNULL = new ErrorCode(**********, "appKey不能为空!");
    ErrorCode API_APPSECRET_NOTNULL = new ErrorCode(**********, "appSecret不能为空!");
    ErrorCode API_ACCOUNT_NOT_EXIST = new ErrorCode(**********, "账号不存在，请检查appKey和appSecret!");
    ErrorCode API_URL_NOT_EXIST = new ErrorCode(**********, "API不存在，请检查请求地址!");

    ErrorCode METHOD_NOT_SUPPORT = new ErrorCode(**********, "请求类型暂不支持");
    ErrorCode API_FORWARD_FAILED = new ErrorCode(**********, "API转发失败，请检查服务端是否可用");
    ErrorCode API_UNAUTH = new ErrorCode(**********, "API未授权");
    ErrorCode API_REQUEST_LIMIT = new ErrorCode(**********, "API调用次数超过授权限制");
    ErrorCode API_DISABLE = new ErrorCode(**********, "API已禁用");



    ErrorCode API_NAME_NOT_UNIQUE = new ErrorCode(**********, "重复的API名称!");

    ErrorCode API_URI_NOT_UNIQUE = new ErrorCode(**********, "重复的API地址后缀!");

    ErrorCode API_GROUP_NAME_NOT_UNIQUE = new ErrorCode(**********, "重复的API分组名称!");

    ErrorCode API_CAN_NOT_DELETE = new ErrorCode(**********, "该API接口被引用，不可删除!");

    ErrorCode API_PARAM_NAME_NOT_UNIQUE = new ErrorCode(**********, "重复的API参数名称!");



    ErrorCode LIMIT_ID_NOT_UNIQUE = new ErrorCode(**********, "重复的账号API访问限制!");

    ErrorCode API_ACCOUNT_NAME_NOT_UNIQUE = new ErrorCode(**********, "重复的API账号名称!");

    ErrorCode API_APPLICATION_NAME_NOT_UNIQUE = new ErrorCode(**********, "重复的应用名称!");

    ErrorCode API_APPLICATION_CODE_NOT_UNIQUE = new ErrorCode(**********, "重复的应用编号!");

    ErrorCode API_SERVER_CODE_NOT_UNIQUE = new ErrorCode(**********, "重复的服务编号!");

    ErrorCode API_SERVER_CAN_NOT_DELETE = new ErrorCode(**********, "该条服务正在使用，不可删除!");
}
