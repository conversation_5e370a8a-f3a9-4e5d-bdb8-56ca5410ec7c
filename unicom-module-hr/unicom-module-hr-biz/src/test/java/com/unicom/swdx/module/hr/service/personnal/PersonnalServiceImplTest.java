//package com.unicom.swdx.module.hr.service.personnal;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.unicom.swdx.framework.common.exception.ServiceException;
//import com.unicom.swdx.framework.common.pojo.CommonResult;
//import com.unicom.swdx.framework.common.pojo.PageResult;
//import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
//import com.unicom.swdx.module.hr.api.dto.PersonDTO;
//import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
//import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
//import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
//import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
//import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
//import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
//import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
//import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
//import com.unicom.swdx.module.hr.dal.mysql.personnal.*;
//import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
//import com.unicom.swdx.module.system.api.dept.DeptApi;
//import com.unicom.swdx.module.system.api.dept.PostApi;
//import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
//import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
//import com.unicom.swdx.module.system.api.message.MessageApi;
//import com.unicom.swdx.module.system.api.message.dto.MessageAuthorityUpdateReqDTO;
//import com.unicom.swdx.module.system.api.permission.PermissionApi;
//import com.unicom.swdx.module.system.api.permission.RoleApi;
//import com.unicom.swdx.module.system.api.tenant.TenantApi;
//import com.unicom.swdx.module.system.api.user.AdminUserApi;
//import com.unicom.swdx.module.system.api.user.dto.AdminUserReqDTO;
//import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
//import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
//import com.unicom.swdx.module.system.api.user.dto.UserDeptDTO;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.time.LocalDateTime;
//import java.util.*;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.assertj.core.api.Assertions.assertThatThrownBy;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//class PersonnalServiceImplTest {
//
//    @Mock
//    private PersonnalMapper mockPersonnalMapper;
//    @Mock
//    private PersonnalStudyMapper mockPersonnalStudyMapper;
//    @Mock
//    private PersonnalPositionMapper mockPersonnalPositionMapper;
//    @Mock
//    private PersonnalWorkMapper mockPersonnalWorkMapper;
//    @Mock
//    private PersonnalPartyMapper mockPersonnalPartyMapper;
//    @Mock
//    private PersonnalRegistrationMapper mockPersonnalRegistrationMapper;
//    @Mock
//    private PermissionApi mockPermissionApi;
//    @Mock
//    private AdminUserApi mockAdminUserApi;
//    @Mock
//    private MessageApi mockMessageApi;
//    @Mock
//    private RoleApi mockRoleApi;
//    @Mock
//    private PostApi mockPostApi;
//    @Mock
//    private DeptApi mockDeptApi;
//    @Mock
//    private PersonProducer mockPersonProducer;
//    @Mock
//    private TenantApi mockTenantApi;
//
//    @InjectMocks
//    private PersonnalServiceImpl personnalServiceImplUnderTest;
//
//    @Test
//    void testCreatePersonnal() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword(SM2Utils.USER_PASSWORD);
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_PersonnalMapperSelectByMobileReturnsNull() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(null);
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword(SM2Utils.USER_PASSWORD);
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_PersonnalMapperSelectMaxWorkIdReturnsNull() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn(null);
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword(SM2Utils.USER_PASSWORD);
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_PermissionApiReturnsError() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_PostApiGetPostByCodeReturnsError() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.error(
//                new ServiceException(0, "message"));
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_AdminUserApiCreatePersonalReturnsError() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_RoleApiGetRoleIdByNameReturnsError() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_RoleApiGetInnerRoleIdByCodeReturnsError() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreatePersonnal_RoleApiGetCustomRoleIdByCodeReturnsError() {
//        // Setup
//        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setMobile("mobile");
//        createReqVO.setPersonnalStatus(0);
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.createPersonnal(createReqVO, 0, "positionName",
//                0);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreateEntryPersonnal() {
//        // Setup
//        final PersonnalBasicEntryVO createReqVO = new PersonnalBasicEntryVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setGender(0);
//        createReqVO.setMobile("mobile");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createEntryPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreateEntryPersonnal_PersonnalMapperSelectByMobileReturnsNull() {
//        // Setup
//        final PersonnalBasicEntryVO createReqVO = new PersonnalBasicEntryVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setGender(0);
//        createReqVO.setMobile("mobile");
//
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(null);
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createEntryPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreateEntryPersonnal_PersonnalMapperSelectMaxWorkIdReturnsNull() {
//        // Setup
//        final PersonnalBasicEntryVO createReqVO = new PersonnalBasicEntryVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setGender(0);
//        createReqVO.setMobile("mobile");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn(null);
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createEntryPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal_PersonnalMapperSelectByMobileReturnsNull() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(null);
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal_PersonnalMapperSelectMaxWorkIdReturnsNull() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn(null);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal_AdminUserApiCreatePersonalReturnsError() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal_RoleApiGetRoleIdByNameReturnsError() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal_RoleApiGetInnerRoleIdByCodeReturnsError() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher")).thenReturn(CommonResult.success(0L));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testCreateRegistrationPersonnal_RoleApiGetCustomRoleIdByCodeReturnsError() {
//        // Setup
//        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
//        createReqVO.setName("name");
//        createReqVO.setIdType(0);
//        createReqVO.setIdNumber("idNumber");
//        createReqVO.setMobile("mobile");
//        createReqVO.setDeptIds(Arrays.asList(0L));
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getCustomRoleIdByCode("teacher"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Run the test
//        final Long result = personnalServiceImplUnderTest.createRegistrationPersonnal(createReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//    }
//
//    @Test
//    void testGetPersonnalPage() {
//        // Setup
//        final PersonnalPageReqVO pageReqVO = new PersonnalPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setWorkId("workId");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final PageResult<PersonnalPageRespVO> expectedResult = new PageResult<>(Arrays.asList(personnalPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectPage(...).
//        final PersonnalPageRespVO personnalPageRespVO1 = new PersonnalPageRespVO();
//        personnalPageRespVO1.setId(0L);
//        personnalPageRespVO1.setName("name");
//        personnalPageRespVO1.setUserId("userId");
//        personnalPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO1.setDepartment(0L);
//        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO1);
//        final PersonnalPageReqVO reqVO = new PersonnalPageReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPage(any(IPage.class), eq(reqVO))).thenReturn(personnalPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPage(pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalPage_PersonnalMapperReturnsNoItems() {
//        // Setup
//        final PersonnalPageReqVO pageReqVO = new PersonnalPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setWorkId("workId");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final PageResult<PersonnalPageRespVO> expectedResult = new PageResult<>(Arrays.asList(personnalPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectPage(...).
//        final PersonnalPageReqVO reqVO = new PersonnalPageReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPage(any(IPage.class), eq(reqVO))).thenReturn(Collections.emptyList());
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPage(pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalPage_AdminUserApiReturnsNoItems() {
//        // Setup
//        final PersonnalPageReqVO pageReqVO = new PersonnalPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setWorkId("workId");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final PageResult<PersonnalPageRespVO> expectedResult = new PageResult<>(Arrays.asList(personnalPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectPage(...).
//        final PersonnalPageRespVO personnalPageRespVO1 = new PersonnalPageRespVO();
//        personnalPageRespVO1.setId(0L);
//        personnalPageRespVO1.setName("name");
//        personnalPageRespVO1.setUserId("userId");
//        personnalPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO1.setDepartment(0L);
//        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO1);
//        final PersonnalPageReqVO reqVO = new PersonnalPageReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPage(any(IPage.class), eq(reqVO))).thenReturn(personnalPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPage(pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalPage_AdminUserApiReturnsError() {
//        // Setup
//        final PersonnalPageReqVO pageReqVO = new PersonnalPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setWorkId("workId");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final PageResult<PersonnalPageRespVO> expectedResult = new PageResult<>(Arrays.asList(personnalPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectPage(...).
//        final PersonnalPageRespVO personnalPageRespVO1 = new PersonnalPageRespVO();
//        personnalPageRespVO1.setId(0L);
//        personnalPageRespVO1.setName("name");
//        personnalPageRespVO1.setUserId("userId");
//        personnalPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO1.setDepartment(0L);
//        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO1);
//        final PersonnalPageReqVO reqVO = new PersonnalPageReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPage(any(IPage.class), eq(reqVO))).thenReturn(personnalPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPage(pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalPageChildren() {
//        // Setup
//        final PersonnalChildrenReqVO reqVO = new PersonnalChildrenReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setUsername("username");
//
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final List<PersonnalPageRespVO> expectedResult = Arrays.asList(personnalPageRespVO);
//
//        // Configure PersonnalMapper.selectChildren(...).
//        final PersonnalPageRespVO personnalPageRespVO1 = new PersonnalPageRespVO();
//        personnalPageRespVO1.setId(0L);
//        personnalPageRespVO1.setName("name");
//        personnalPageRespVO1.setUserId("userId");
//        personnalPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO1.setDepartment(0L);
//        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO1);
//        final PersonnalChildrenReqVO reqVO1 = new PersonnalChildrenReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setUsername("username");
//        when(mockPersonnalMapper.selectChildren(reqVO1)).thenReturn(personnalPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPageChildren(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalPageChildren_PersonnalMapperReturnsNoItems() {
//        // Setup
//        final PersonnalChildrenReqVO reqVO = new PersonnalChildrenReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setUsername("username");
//
//        // Configure PersonnalMapper.selectChildren(...).
//        final PersonnalChildrenReqVO reqVO1 = new PersonnalChildrenReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setUsername("username");
//        when(mockPersonnalMapper.selectChildren(reqVO1)).thenReturn(Collections.emptyList());
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPageChildren(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(Collections.emptyList());
//    }
//
//    @Test
//    void testGetPersonnalPageChildren_AdminUserApiReturnsNoItems() {
//        // Setup
//        final PersonnalChildrenReqVO reqVO = new PersonnalChildrenReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setUsername("username");
//
//        // Configure PersonnalMapper.selectChildren(...).
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO);
//        final PersonnalChildrenReqVO reqVO1 = new PersonnalChildrenReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setUsername("username");
//        when(mockPersonnalMapper.selectChildren(reqVO1)).thenReturn(personnalPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPageChildren(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(Collections.emptyList());
//    }
//
//    @Test
//    void testGetPersonnalPageChildren_AdminUserApiReturnsError() {
//        // Setup
//        final PersonnalChildrenReqVO reqVO = new PersonnalChildrenReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setUsername("username");
//
//        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
//        personnalPageRespVO.setId(0L);
//        personnalPageRespVO.setName("name");
//        personnalPageRespVO.setUserId("userId");
//        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO.setDepartment(0L);
//        final List<PersonnalPageRespVO> expectedResult = Arrays.asList(personnalPageRespVO);
//
//        // Configure PersonnalMapper.selectChildren(...).
//        final PersonnalPageRespVO personnalPageRespVO1 = new PersonnalPageRespVO();
//        personnalPageRespVO1.setId(0L);
//        personnalPageRespVO1.setName("name");
//        personnalPageRespVO1.setUserId("userId");
//        personnalPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalPageRespVO1.setDepartment(0L);
//        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO1);
//        final PersonnalChildrenReqVO reqVO1 = new PersonnalChildrenReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setUsername("username");
//        when(mockPersonnalMapper.selectChildren(reqVO1)).thenReturn(personnalPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalPageRespVO> result = personnalServiceImplUnderTest.getPersonnalPageChildren(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalReviewPage() {
//        // Setup
//        final PersonnalReviewPageReqVO pageReqVO = new PersonnalReviewPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO.setId(0L);
//        personnalReviewPageRespVO.setName("name");
//        personnalReviewPageRespVO.setDepartment(0L);
//        personnalReviewPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO.setUserId(0L);
//        final PageResult<PersonnalReviewPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalReviewPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectReviewPage(...).
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO1 = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO1.setId(0L);
//        personnalReviewPageRespVO1.setName("name");
//        personnalReviewPageRespVO1.setDepartment(0L);
//        personnalReviewPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO1.setUserId(0L);
//        final List<PersonnalReviewPageRespVO> personnalReviewPageRespVOS = Arrays.asList(personnalReviewPageRespVO1);
//        final PersonnalReviewPageReqVO reqVO = new PersonnalReviewPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        when(mockPersonnalMapper.selectReviewPage(any(IPage.class), eq(reqVO))).thenReturn(personnalReviewPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalReviewPageRespVO> result = personnalServiceImplUnderTest.getPersonnalReviewPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalReviewPage_PersonnalMapperReturnsNoItems() {
//        // Setup
//        final PersonnalReviewPageReqVO pageReqVO = new PersonnalReviewPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO.setId(0L);
//        personnalReviewPageRespVO.setName("name");
//        personnalReviewPageRespVO.setDepartment(0L);
//        personnalReviewPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO.setUserId(0L);
//        final PageResult<PersonnalReviewPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalReviewPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectReviewPage(...).
//        final PersonnalReviewPageReqVO reqVO = new PersonnalReviewPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        when(mockPersonnalMapper.selectReviewPage(any(IPage.class), eq(reqVO))).thenReturn(Collections.emptyList());
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalReviewPageRespVO> result = personnalServiceImplUnderTest.getPersonnalReviewPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalReviewPage_AdminUserApiReturnsNoItems() {
//        // Setup
//        final PersonnalReviewPageReqVO pageReqVO = new PersonnalReviewPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO.setId(0L);
//        personnalReviewPageRespVO.setName("name");
//        personnalReviewPageRespVO.setDepartment(0L);
//        personnalReviewPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO.setUserId(0L);
//        final PageResult<PersonnalReviewPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalReviewPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectReviewPage(...).
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO1 = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO1.setId(0L);
//        personnalReviewPageRespVO1.setName("name");
//        personnalReviewPageRespVO1.setDepartment(0L);
//        personnalReviewPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO1.setUserId(0L);
//        final List<PersonnalReviewPageRespVO> personnalReviewPageRespVOS = Arrays.asList(personnalReviewPageRespVO1);
//        final PersonnalReviewPageReqVO reqVO = new PersonnalReviewPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        when(mockPersonnalMapper.selectReviewPage(any(IPage.class), eq(reqVO))).thenReturn(personnalReviewPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalReviewPageRespVO> result = personnalServiceImplUnderTest.getPersonnalReviewPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalReviewPage_AdminUserApiReturnsError() {
//        // Setup
//        final PersonnalReviewPageReqVO pageReqVO = new PersonnalReviewPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0L);
//
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO.setId(0L);
//        personnalReviewPageRespVO.setName("name");
//        personnalReviewPageRespVO.setDepartment(0L);
//        personnalReviewPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO.setUserId(0L);
//        final PageResult<PersonnalReviewPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalReviewPageRespVO), 0L);
//
//        // Configure PersonnalMapper.selectReviewPage(...).
//        final PersonnalReviewPageRespVO personnalReviewPageRespVO1 = new PersonnalReviewPageRespVO();
//        personnalReviewPageRespVO1.setId(0L);
//        personnalReviewPageRespVO1.setName("name");
//        personnalReviewPageRespVO1.setDepartment(0L);
//        personnalReviewPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalReviewPageRespVO1.setUserId(0L);
//        final List<PersonnalReviewPageRespVO> personnalReviewPageRespVOS = Arrays.asList(personnalReviewPageRespVO1);
//        final PersonnalReviewPageReqVO reqVO = new PersonnalReviewPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        when(mockPersonnalMapper.selectReviewPage(any(IPage.class), eq(reqVO))).thenReturn(personnalReviewPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalReviewPageRespVO> result = personnalServiceImplUnderTest.getPersonnalReviewPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalRegistrationPage() {
//        // Setup
//        final PersonnalRegistrationPageReqVO pageReqVO = new PersonnalRegistrationPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0);
//        pageReqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO.setId(0L);
//        personnalRegistrationPageRespVO.setName("name");
//        personnalRegistrationPageRespVO.setDepartment(0);
//        personnalRegistrationPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO.setUserId(0L);
//        final PageResult<PersonnalRegistrationPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalRegistrationPageRespVO), 0L);
//
//        // Configure PersonnalRegistrationMapper.selectRegistrationPage(...).
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO1 = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO1.setId(0L);
//        personnalRegistrationPageRespVO1.setName("name");
//        personnalRegistrationPageRespVO1.setDepartment(0);
//        personnalRegistrationPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO1.setUserId(0L);
//        final List<PersonnalRegistrationPageRespVO> personnalRegistrationPageRespVOS = Arrays.asList(
//                personnalRegistrationPageRespVO1);
//        final PersonnalRegistrationPageReqVO reqVO = new PersonnalRegistrationPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0);
//        reqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalRegistrationMapper.selectRegistrationPage(any(IPage.class), eq(reqVO)))
//                .thenReturn(personnalRegistrationPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalRegistrationPageRespVO> result = personnalServiceImplUnderTest.getPersonnalRegistrationPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalRegistrationPage_PersonnalRegistrationMapperReturnsNoItems() {
//        // Setup
//        final PersonnalRegistrationPageReqVO pageReqVO = new PersonnalRegistrationPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0);
//        pageReqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO.setId(0L);
//        personnalRegistrationPageRespVO.setName("name");
//        personnalRegistrationPageRespVO.setDepartment(0);
//        personnalRegistrationPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO.setUserId(0L);
//        final PageResult<PersonnalRegistrationPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalRegistrationPageRespVO), 0L);
//
//        // Configure PersonnalRegistrationMapper.selectRegistrationPage(...).
//        final PersonnalRegistrationPageReqVO reqVO = new PersonnalRegistrationPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0);
//        reqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalRegistrationMapper.selectRegistrationPage(any(IPage.class), eq(reqVO)))
//                .thenReturn(Collections.emptyList());
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final UserDeptDTO userDeptDTO = new UserDeptDTO();
//        userDeptDTO.setId(0L);
//        userDeptDTO.setUserId(0L);
//        userDeptDTO.setDeptId(0L);
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(userDeptDTO));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalRegistrationPageRespVO> result = personnalServiceImplUnderTest.getPersonnalRegistrationPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalRegistrationPage_AdminUserApiReturnsNoItems() {
//        // Setup
//        final PersonnalRegistrationPageReqVO pageReqVO = new PersonnalRegistrationPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0);
//        pageReqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO.setId(0L);
//        personnalRegistrationPageRespVO.setName("name");
//        personnalRegistrationPageRespVO.setDepartment(0);
//        personnalRegistrationPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO.setUserId(0L);
//        final PageResult<PersonnalRegistrationPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalRegistrationPageRespVO), 0L);
//
//        // Configure PersonnalRegistrationMapper.selectRegistrationPage(...).
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO1 = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO1.setId(0L);
//        personnalRegistrationPageRespVO1.setName("name");
//        personnalRegistrationPageRespVO1.setDepartment(0);
//        personnalRegistrationPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO1.setUserId(0L);
//        final List<PersonnalRegistrationPageRespVO> personnalRegistrationPageRespVOS = Arrays.asList(
//                personnalRegistrationPageRespVO1);
//        final PersonnalRegistrationPageReqVO reqVO = new PersonnalRegistrationPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0);
//        reqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalRegistrationMapper.selectRegistrationPage(any(IPage.class), eq(reqVO)))
//                .thenReturn(personnalRegistrationPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalRegistrationPageRespVO> result = personnalServiceImplUnderTest.getPersonnalRegistrationPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalRegistrationPage_AdminUserApiReturnsError() {
//        // Setup
//        final PersonnalRegistrationPageReqVO pageReqVO = new PersonnalRegistrationPageReqVO();
//        pageReqVO.setName("name");
//        pageReqVO.setGender(0);
//        pageReqVO.setDepartment(0);
//        pageReqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        pageReqVO.setPeronClassification(0);
//
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO.setId(0L);
//        personnalRegistrationPageRespVO.setName("name");
//        personnalRegistrationPageRespVO.setDepartment(0);
//        personnalRegistrationPageRespVO.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO.setUserId(0L);
//        final PageResult<PersonnalRegistrationPageRespVO> expectedResult = new PageResult<>(
//                Arrays.asList(personnalRegistrationPageRespVO), 0L);
//
//        // Configure PersonnalRegistrationMapper.selectRegistrationPage(...).
//        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO1 = new PersonnalRegistrationPageRespVO();
//        personnalRegistrationPageRespVO1.setId(0L);
//        personnalRegistrationPageRespVO1.setName("name");
//        personnalRegistrationPageRespVO1.setDepartment(0);
//        personnalRegistrationPageRespVO1.setDeptIds(Arrays.asList(0L));
//        personnalRegistrationPageRespVO1.setUserId(0L);
//        final List<PersonnalRegistrationPageRespVO> personnalRegistrationPageRespVOS = Arrays.asList(
//                personnalRegistrationPageRespVO1);
//        final PersonnalRegistrationPageReqVO reqVO = new PersonnalRegistrationPageReqVO();
//        reqVO.setName("name");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0);
//        reqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
//        reqVO.setPeronClassification(0);
//        when(mockPersonnalRegistrationMapper.selectRegistrationPage(any(IPage.class), eq(reqVO)))
//                .thenReturn(personnalRegistrationPageRespVOS);
//
//        // Configure AdminUserApi.selectUserDeptList(...).
//        final CommonResult<List<UserDeptDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
//        when(mockAdminUserApi.selectUserDeptList()).thenReturn(listCommonResult);
//
//        // Run the test
//        final PageResult<PersonnalRegistrationPageRespVO> result = personnalServiceImplUnderTest.getPersonnalRegistrationPage(
//                pageReqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnal() {
//        // Setup
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectById(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectById(0L)).thenReturn(personnalBasicDO);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.getPersonnal(0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnal_AdminUserApiReturnsNoItems() {
//        // Setup
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectById(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectById(0L)).thenReturn(personnalBasicDO);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Collections.emptyList()));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.getPersonnal(0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnal_AdminUserApiReturnsError() {
//        // Setup
//        final PersonnalBasicDO expectedResult = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//
//        // Configure PersonnalMapper.selectById(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectById(0L)).thenReturn(personnalBasicDO);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Run the test
//        final PersonnalBasicDO result = personnalServiceImplUnderTest.getPersonnal(0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalSimple() {
//        // Setup
//        final PersonnalSimpleRespVO expectedResult = new PersonnalSimpleRespVO();
//        expectedResult.setId(0L);
//        expectedResult.setName("name");
//        expectedResult.setReviewStatus("reviewStatus");
//        expectedResult.setPersonnalStatus(0);
//        expectedResult.setDepartment(0L);
//
//        // Configure PersonnalMapper.selectSimple(...).
//        final PersonnalSimpleRespVO personnalSimpleRespVO = new PersonnalSimpleRespVO();
//        personnalSimpleRespVO.setId(0L);
//        personnalSimpleRespVO.setName("name");
//        personnalSimpleRespVO.setReviewStatus("reviewStatus");
//        personnalSimpleRespVO.setPersonnalStatus(0);
//        personnalSimpleRespVO.setDepartment(0L);
//        when(mockPersonnalMapper.selectSimple(0L)).thenReturn(personnalSimpleRespVO);
//
//        // Run the test
//        final PersonnalSimpleRespVO result = personnalServiceImplUnderTest.getPersonnalSimple(0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testUpdatePersonnal() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_PersonnalMapperSelectByMobileReturnsNull() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(null);
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_PermissionApiReturnsError() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_AdminUserApiGetPersonalReturnsError() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
//                new ServiceException(0, "message"));
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_MessageApiGetMessageReturnsNull() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(null);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_AdminUserApiGetDeptListReturnsNoItems() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Collections.emptyList()));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_AdminUserApiGetDeptListReturnsError() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdatePersonnal_AdminUserApiUpdatePersonalReturnsError() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        // Configure PersonnalMapper.selectByMobile(...).
//        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build();
//        when(mockPersonnalMapper.selectByMobile("mobile")).thenReturn(personnalBasicDO);
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Run the test
//        personnalServiceImplUnderTest.updatePersonnal(reqVO);
//
//        // Verify the results
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateRetireTime(0L);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//    }
//
//    @Test
//    void testUpdateFromUser() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin")).thenReturn(CommonResult.success(false));
//
//        // Run the test
//        personnalServiceImplUnderTest.updateFromUser(reqVO);
//
//        // Verify the results
////        verify(mockPersonnalMapper).update(any(T.class), any(LambdaUpdateWrapper.class));
//    }
//
//    @Test
//    void testUpdateFromUser_PermissionApiReturnsError() {
//        // Setup
//        final PersonnalBasicGetVO reqVO = new PersonnalBasicGetVO();
//        reqVO.setMobile("mobile");
//        reqVO.setPersonnalStatus(0);
//        reqVO.setDeptIds(Arrays.asList(0L));
//        reqVO.setId(0L);
//        reqVO.setWorkId("workId");
//
//        when(mockPermissionApi.hasAnyRoles(0L, "hr-admin"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Run the test
//        personnalServiceImplUnderTest.updateFromUser(reqVO);
//
//        // Verify the results
////        verify(mockPersonnalMapper).update(any(T.class), any(LambdaUpdateWrapper.class));
//    }
//
//    @Test
//    void testGetPersonnalExcel() {
//        // Setup
//        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//
//        final PersonnalExcelVO personnalExcelVO = new PersonnalExcelVO();
//        personnalExcelVO.setUserId(0L);
//        personnalExcelVO.setName("name");
//        personnalExcelVO.setWorkId("workId");
//        personnalExcelVO.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> expectedResult = Arrays.asList(personnalExcelVO);
//
//        // Configure PersonnalMapper.selectPostList(...).
//        final PersonnalExcelVO personnalExcelVO1 = new PersonnalExcelVO();
//        personnalExcelVO1.setUserId(0L);
//        personnalExcelVO1.setName("name");
//        personnalExcelVO1.setWorkId("workId");
//        personnalExcelVO1.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO1.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> personnalExcelVOS = Arrays.asList(personnalExcelVO1);
//        final PersonnalExportReqVO reqVO1 = new PersonnalExportReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPostList(reqVO1)).thenReturn(personnalExcelVOS);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure DeptApi.getDepts(...).
//        final DeptRespDTO deptRespDTO = new DeptRespDTO();
//        deptRespDTO.setId(0L);
//        deptRespDTO.setName("name");
//        deptRespDTO.setParentId(0L);
//        deptRespDTO.setLeaderUserId(0L);
//        deptRespDTO.setStatus(0);
//        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
//        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalExcelVO> result = personnalServiceImplUnderTest.getPersonnalExcel(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalExcel_PersonnalMapperReturnsNoItems() {
//        // Setup
//        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//
//        // Configure PersonnalMapper.selectPostList(...).
//        final PersonnalExportReqVO reqVO1 = new PersonnalExportReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPostList(reqVO1)).thenReturn(Collections.emptyList());
//
//        // Run the test
//        final List<PersonnalExcelVO> result = personnalServiceImplUnderTest.getPersonnalExcel(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(Collections.emptyList());
//    }
//
//    @Test
//    void testGetPersonnalExcel_AdminUserApiReturnsNoItems() {
//        // Setup
//        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//
//        final PersonnalExcelVO personnalExcelVO = new PersonnalExcelVO();
//        personnalExcelVO.setUserId(0L);
//        personnalExcelVO.setName("name");
//        personnalExcelVO.setWorkId("workId");
//        personnalExcelVO.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> expectedResult = Arrays.asList(personnalExcelVO);
//
//        // Configure PersonnalMapper.selectPostList(...).
//        final PersonnalExcelVO personnalExcelVO1 = new PersonnalExcelVO();
//        personnalExcelVO1.setUserId(0L);
//        personnalExcelVO1.setName("name");
//        personnalExcelVO1.setWorkId("workId");
//        personnalExcelVO1.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO1.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> personnalExcelVOS = Arrays.asList(personnalExcelVO1);
//        final PersonnalExportReqVO reqVO1 = new PersonnalExportReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPostList(reqVO1)).thenReturn(personnalExcelVOS);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Collections.emptyList()));
//
//        // Configure DeptApi.getDepts(...).
//        final DeptRespDTO deptRespDTO = new DeptRespDTO();
//        deptRespDTO.setId(0L);
//        deptRespDTO.setName("name");
//        deptRespDTO.setParentId(0L);
//        deptRespDTO.setLeaderUserId(0L);
//        deptRespDTO.setStatus(0);
//        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
//        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalExcelVO> result = personnalServiceImplUnderTest.getPersonnalExcel(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalExcel_AdminUserApiReturnsError() {
//        // Setup
//        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//
//        final PersonnalExcelVO personnalExcelVO = new PersonnalExcelVO();
//        personnalExcelVO.setUserId(0L);
//        personnalExcelVO.setName("name");
//        personnalExcelVO.setWorkId("workId");
//        personnalExcelVO.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> expectedResult = Arrays.asList(personnalExcelVO);
//
//        // Configure PersonnalMapper.selectPostList(...).
//        final PersonnalExcelVO personnalExcelVO1 = new PersonnalExcelVO();
//        personnalExcelVO1.setUserId(0L);
//        personnalExcelVO1.setName("name");
//        personnalExcelVO1.setWorkId("workId");
//        personnalExcelVO1.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO1.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> personnalExcelVOS = Arrays.asList(personnalExcelVO1);
//        final PersonnalExportReqVO reqVO1 = new PersonnalExportReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPostList(reqVO1)).thenReturn(personnalExcelVOS);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        // Configure DeptApi.getDepts(...).
//        final DeptRespDTO deptRespDTO = new DeptRespDTO();
//        deptRespDTO.setId(0L);
//        deptRespDTO.setName("name");
//        deptRespDTO.setParentId(0L);
//        deptRespDTO.setLeaderUserId(0L);
//        deptRespDTO.setStatus(0);
//        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
//        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalExcelVO> result = personnalServiceImplUnderTest.getPersonnalExcel(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalExcel_DeptApiReturnsNoItems() {
//        // Setup
//        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//
//        final PersonnalExcelVO personnalExcelVO = new PersonnalExcelVO();
//        personnalExcelVO.setUserId(0L);
//        personnalExcelVO.setName("name");
//        personnalExcelVO.setWorkId("workId");
//        personnalExcelVO.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> expectedResult = Arrays.asList(personnalExcelVO);
//
//        // Configure PersonnalMapper.selectPostList(...).
//        final PersonnalExcelVO personnalExcelVO1 = new PersonnalExcelVO();
//        personnalExcelVO1.setUserId(0L);
//        personnalExcelVO1.setName("name");
//        personnalExcelVO1.setWorkId("workId");
//        personnalExcelVO1.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO1.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> personnalExcelVOS = Arrays.asList(personnalExcelVO1);
//        final PersonnalExportReqVO reqVO1 = new PersonnalExportReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPostList(reqVO1)).thenReturn(personnalExcelVOS);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure DeptApi.getDepts(...).
//        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
//        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalExcelVO> result = personnalServiceImplUnderTest.getPersonnalExcel(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testGetPersonnalExcel_DeptApiReturnsError() {
//        // Setup
//        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
//        reqVO.setName("name");
//        reqVO.setWorkId("workId");
//        reqVO.setGender(0);
//        reqVO.setDepartment(0L);
//        reqVO.setPeronClassification(0);
//
//        final PersonnalExcelVO personnalExcelVO = new PersonnalExcelVO();
//        personnalExcelVO.setUserId(0L);
//        personnalExcelVO.setName("name");
//        personnalExcelVO.setWorkId("workId");
//        personnalExcelVO.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> expectedResult = Arrays.asList(personnalExcelVO);
//
//        // Configure PersonnalMapper.selectPostList(...).
//        final PersonnalExcelVO personnalExcelVO1 = new PersonnalExcelVO();
//        personnalExcelVO1.setUserId(0L);
//        personnalExcelVO1.setName("name");
//        personnalExcelVO1.setWorkId("workId");
//        personnalExcelVO1.setAdministrativePositionName("administrativePositionName");
//        personnalExcelVO1.setDepartmentName("departmentName");
//        final List<PersonnalExcelVO> personnalExcelVOS = Arrays.asList(personnalExcelVO1);
//        final PersonnalExportReqVO reqVO1 = new PersonnalExportReqVO();
//        reqVO1.setName("name");
//        reqVO1.setWorkId("workId");
//        reqVO1.setGender(0);
//        reqVO1.setDepartment(0L);
//        reqVO1.setPeronClassification(0);
//        when(mockPersonnalMapper.selectPostList(reqVO1)).thenReturn(personnalExcelVOS);
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//
//        // Configure DeptApi.getDepts(...).
//        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
//        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);
//
//        // Run the test
//        final List<PersonnalExcelVO> result = personnalServiceImplUnderTest.getPersonnalExcel(reqVO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//    }
//
//    @Test
//    void testImportPersonnal() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//
//        // Configure PersonnalMapper.selectList(...).
//        final List<PersonnalBasicDO> personnalBasicDOS = Arrays.asList(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        when(mockPersonnalMapper.selectList()).thenReturn(personnalBasicDOS);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//
//        // Configure PersonnalWorkMapper.selectByPersonnalId(...).
//        final List<PersonnalWorkDO> personnalWorkDOS = Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build());
//        when(mockPersonnalWorkMapper.selectByPersonnalId(0L)).thenReturn(personnalWorkDOS);
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//        verify(mockAdminUserApi).updateUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).updateByPersonnalId(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).updateByPersonnalId(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).deleteById(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build());
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//
//    @Test
//    void testImportPersonnal_PersonnalMapperSelectListReturnsNoItems() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//        when(mockPersonnalMapper.selectList()).thenReturn(Collections.emptyList());
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Configure AdminUserApi.getPersonal(...).
//        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
//        adminUserRespDTO.setId(0L);
//        adminUserRespDTO.setNickname("name");
//        adminUserRespDTO.setStatus(0);
//        adminUserRespDTO.setDeptId(0L);
//        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
//        adminUserRespDTO.setMobile("mobile");
//        adminUserRespDTO.setSex(0);
//        adminUserRespDTO.setEmail("email");
//        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
//        when(mockAdminUserApi.getPersonal(0L)).thenReturn(adminUserRespDTOCommonResult);
//
//        // Configure AdminUserApi.updatePersonal(...).
//        final AdminUserRespDTO reqVO1 = new AdminUserRespDTO();
//        reqVO1.setId(0L);
//        reqVO1.setNickname("name");
//        reqVO1.setStatus(0);
//        reqVO1.setDeptId(0L);
//        reqVO1.setDeptIds(Arrays.asList(0L));
//        reqVO1.setMobile("mobile");
//        reqVO1.setSex(0);
//        reqVO1.setEmail("email");
//        when(mockAdminUserApi.updatePersonal(reqVO1)).thenReturn(CommonResult.success(false));
//
//        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));
//        when(mockMessageApi.getMessage(0L)).thenReturn(0L);
//
//        // Configure PersonnalWorkMapper.selectByPersonnalId(...).
//        final List<PersonnalWorkDO> personnalWorkDOS = Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build());
//        when(mockPersonnalWorkMapper.selectByPersonnalId(0L)).thenReturn(personnalWorkDOS);
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//        verify(mockAdminUserApi).updateUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm MessageApi.updateMessage(...).
//        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
//        updateReqDTO.setDataScopeIds("dataScopeIds");
//        updateReqDTO.setDataScopeNames("dataScopeNames");
//        updateReqDTO.setUserId(0L);
//        updateReqDTO.setId(0L);
//        updateReqDTO.setStatus(0);
//        verify(mockMessageApi).updateMessage(updateReqDTO);
//        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).updateByPersonnalId(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).updateByPersonnalId(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).deleteById(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build());
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//
//    @Test
//    void testImportPersonnal_PersonnalMapperSelectMaxWorkIdReturnsNull() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//
//        // Configure PersonnalMapper.selectList(...).
//        final List<PersonnalBasicDO> personnalBasicDOS = Arrays.asList(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        when(mockPersonnalMapper.selectList()).thenReturn(personnalBasicDOS);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn(null);
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//
//    @Test
//    void testImportPersonnal_PostApiGetPostByCodeReturnsError() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//
//        // Configure PersonnalMapper.selectList(...).
//        final List<PersonnalBasicDO> personnalBasicDOS = Arrays.asList(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        when(mockPersonnalMapper.selectList()).thenReturn(personnalBasicDOS);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.error(
//                new ServiceException(0, "message"));
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//
//    @Test
//    void testImportPersonnal_AdminUserApiCreatePersonalReturnsError() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//
//        // Configure PersonnalMapper.selectList(...).
//        final List<PersonnalBasicDO> personnalBasicDOS = Arrays.asList(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        when(mockPersonnalMapper.selectList()).thenReturn(personnalBasicDOS);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//
//        // Confirm AdminUserApi.createUserDept(...).
//        final UserDeptDTO userDept = new UserDeptDTO();
//        userDept.setId(0L);
//        userDept.setUserId(0L);
//        userDept.setDeptId(0L);
//        verify(mockAdminUserApi).createUserDept(userDept);
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//
//    @Test
//    void testImportPersonnal_RoleApiGetRoleIdByNameReturnsError() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//
//        // Configure PersonnalMapper.selectList(...).
//        final List<PersonnalBasicDO> personnalBasicDOS = Arrays.asList(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        when(mockPersonnalMapper.selectList()).thenReturn(personnalBasicDOS);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.error(new ServiceException(0, "message")));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage")).thenReturn(CommonResult.success(0L));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//
//    @Test
//    void testImportPersonnal_RoleApiGetInnerRoleIdByCodeReturnsError() {
//        // Setup
//        final List<PersonnalImportExcelVO> importPersonnals = Arrays.asList(PersonnalImportExcelVO.builder()
//                .name("name")
//                .birthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .mobile("mobile")
//                .departmentName("departmentName")
//                .workUnit1("workUnit1")
//                .startTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime1(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position1("position1")
//                .workContent1("workContent1")
//                .workUnit2("workUnit1")
//                .startTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime2(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position2("position1")
//                .workContent2("workContent1")
//                .workUnit3("workUnit1")
//                .startTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .endTime3(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .position3("position1")
//                .workContent3("workContent1")
//                .build());
//        final PersonnalImportRespVO expectedResult = PersonnalImportRespVO.builder()
//                .createPersonnalNames(Arrays.asList("value"))
//                .updatePersonnalNames(Arrays.asList("value"))
//                .failurePersonnalNames(new HashMap<>())
//                .build();
//
//        // Configure PersonnalMapper.selectList(...).
//        final List<PersonnalBasicDO> personnalBasicDOS = Arrays.asList(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        when(mockPersonnalMapper.selectList()).thenReturn(personnalBasicDOS);
//
//        when(mockPersonnalMapper.selectMaxWorkId()).thenReturn("result");
//        when(mockPersonnalMapper.selectDeptId("deptName", 0L)).thenReturn(0L);
//
//        // Configure PostApi.getPostByCode(...).
//        final PostRespDTO postRespDTO = new PostRespDTO();
//        postRespDTO.setId(0L);
//        postRespDTO.setTenant_id(0L);
//        postRespDTO.setName("name");
//        postRespDTO.setCode("code");
//        postRespDTO.setSort(0);
//        final CommonResult<PostRespDTO> postRespDTOCommonResult = CommonResult.success(postRespDTO);
//        when(mockPostApi.getPostByCode("postCode", 0L)).thenReturn(postRespDTOCommonResult);
//
//        // Configure AdminUserApi.createPersonal(...).
//        final AdminUserReqDTO reqVO = new AdminUserReqDTO();
//        reqVO.setUsername("mobile");
//        reqVO.setNickname("name");
//        reqVO.setDeptId(0L);
//        reqVO.setPostIds(new HashSet<>(Arrays.asList(0L)));
//        reqVO.setMobile("mobile");
//        reqVO.setStatus(0);
//        reqVO.setSort(0);
//        reqVO.setPassword("8$SxLPqTv^sR");
//        when(mockAdminUserApi.createPersonal(reqVO)).thenReturn(CommonResult.success(0L));
//
//        when(mockRoleApi.getRoleIdByName("应聘者")).thenReturn(CommonResult.success(0L));
//        when(mockRoleApi.getInnerRoleIdByCode("DX-Homepage"))
//                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
//        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));
//
//        // Run the test
//        final PersonnalImportRespVO result = personnalServiceImplUnderTest.importPersonnal(importPersonnals, false);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockAdminUserApi).createUserDeptBatch(0L, Arrays.asList(0L));
//        verify(mockPostApi).saveUserPost(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockRoleApi).createRole(0L, new HashSet<>(Arrays.asList(0L)));
//        verify(mockPersonnalMapper).insert(PersonnalBasicDO.builder()
//                .id(0L)
//                .workId("workId")
//                .name("name")
//                .gender(0)
//                .age("age")
//                .mobile("mobile")
//                .personnalStatus(0)
//                .department(0L)
//                .deptIds(Arrays.asList(0L))
//                .email("email")
//                .reviewStatus(0)
//                .retireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .retireRemarks("retireRemarks")
//                .leaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .leaveChannel("leaveChannel")
//                .leaveDestination("leaveDestination")
//                .leaveReason("leaveReason")
//                .leaveRemarks("leaveRemarks")
//                .deathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .deathStatus("deathStatus")
//                .deathRemarks("deathRemarks")
//                .userId(0L)
//                .build());
//        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
//                .personnalId(0L)
//                .build());
//        verify(mockPersonnalPositionMapper).insert(PersonnalPositionDO.builder()
//                .personnalId(0L)
//                .rank(0)
//                .administrativePositionName("administrativePositionName")
//                .administrativePositionRank(0)
//                .build());
//        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
//                .personnalId(0L)
//                .build()));
//
//        // Confirm PersonProducer.sendPersonListData(...).
//        final KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
//        kafkaPersonDTO.setPersonnalStatus(0);
//        kafkaPersonDTO.setDepartment(0L);
//        kafkaPersonDTO.setUserId(0L);
//        kafkaPersonDTO.setTenantCode("tenantCode");
//        kafkaPersonDTO.setTenantId(0L);
//        final List<KafkaPersonDTO> kafkaList = Arrays.asList(kafkaPersonDTO);
//        verify(mockPersonProducer).sendPersonListData(true, kafkaList);
//    }
//}
