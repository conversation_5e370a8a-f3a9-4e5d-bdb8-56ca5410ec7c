package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentPositionManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentPositionManagementVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitmentPositionManagementMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RecruitmentPositionManagementServiceImplTest {

    @Mock
    private RecruitmentPositionManagementMapper mockRecruitmentPositionManagementMapper;

    @InjectMocks
    private RecruitmentPositionManagementServiceImpl recruitmentPositionManagementServiceImplUnderTest;

    @Test
    void testQueryByList() {
        // Setup
        final RecruitmentPositionManagementVO recruitmentPositionManagementVO = new RecruitmentPositionManagementVO();
        recruitmentPositionManagementVO.setId(0);
        recruitmentPositionManagementVO.setRecruitmentUnit("recruitmentUnit");
        recruitmentPositionManagementVO.setRecruitmentDepartment("recruitmentDepartment");
        recruitmentPositionManagementVO.setPositionName("positionName");
        recruitmentPositionManagementVO.setPostCode("postCode");

        final RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
        recruitmentPositionManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setId(0);
        recruitmentPositionManagement.setPositionName("positionName");
        recruitmentPositionManagement.setPostCode("postCode");
        recruitmentPositionManagement.setRecruitmentPlan("recruitmentPlan");
        recruitmentPositionManagement.setAcademicDegree("academicDegree");
        recruitmentPositionManagement.setAge("age");
        recruitmentPositionManagement.setProfession("profession");
        recruitmentPositionManagement.setOther("other");
        recruitmentPositionManagement.setRemark("remark");
        recruitmentPositionManagement.setJobRequirements("jobRequirements");
        recruitmentPositionManagement.setJobResponsibility("jobResponsibility");
        recruitmentPositionManagement.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setReleaseStatus("releaseStatus");
        recruitmentPositionManagement.setOwningBatch(0);
        final PageResult<RecruitmentPositionManagement> expectedResult = new PageResult<>(
                Arrays.asList(recruitmentPositionManagement), 0L);

        // Configure RecruitmentPositionManagementMapper.queryByList(...).
        final RecruitmentPositionManagement recruitmentPositionManagement1 = new RecruitmentPositionManagement();
        recruitmentPositionManagement1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement1.setId(0);
        recruitmentPositionManagement1.setPositionName("positionName");
        recruitmentPositionManagement1.setPostCode("postCode");
        recruitmentPositionManagement1.setRecruitmentPlan("recruitmentPlan");
        recruitmentPositionManagement1.setAcademicDegree("academicDegree");
        recruitmentPositionManagement1.setAge("age");
        recruitmentPositionManagement1.setProfession("profession");
        recruitmentPositionManagement1.setOther("other");
        recruitmentPositionManagement1.setRemark("remark");
        recruitmentPositionManagement1.setJobRequirements("jobRequirements");
        recruitmentPositionManagement1.setJobResponsibility("jobResponsibility");
        recruitmentPositionManagement1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement1.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement1.setReleaseStatus("releaseStatus");
        recruitmentPositionManagement1.setOwningBatch(0);
        final List<RecruitmentPositionManagement> recruitmentPositionManagements = Arrays.asList(
                recruitmentPositionManagement1);
        final RecruitmentPositionManagementVO recruitmentPositionManagementVO1 = new RecruitmentPositionManagementVO();
        recruitmentPositionManagementVO1.setId(0);
        recruitmentPositionManagementVO1.setRecruitmentUnit("recruitmentUnit");
        recruitmentPositionManagementVO1.setRecruitmentDepartment("recruitmentDepartment");
        recruitmentPositionManagementVO1.setPositionName("positionName");
        recruitmentPositionManagementVO1.setPostCode("postCode");
        when(mockRecruitmentPositionManagementMapper.queryByList(any(IPage.class),
                eq(recruitmentPositionManagementVO1))).thenReturn(recruitmentPositionManagements);

        // Run the test
        final PageResult<RecruitmentPositionManagement> result = recruitmentPositionManagementServiceImplUnderTest.queryByList(
                recruitmentPositionManagementVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryByList_RecruitmentPositionManagementMapperReturnsNoItems() {
        // Setup
        final RecruitmentPositionManagementVO recruitmentPositionManagementVO = new RecruitmentPositionManagementVO();
        recruitmentPositionManagementVO.setId(0);
        recruitmentPositionManagementVO.setRecruitmentUnit("recruitmentUnit");
        recruitmentPositionManagementVO.setRecruitmentDepartment("recruitmentDepartment");
        recruitmentPositionManagementVO.setPositionName("positionName");
        recruitmentPositionManagementVO.setPostCode("postCode");

        final RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
        recruitmentPositionManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setId(0);
        recruitmentPositionManagement.setPositionName("positionName");
        recruitmentPositionManagement.setPostCode("postCode");
        recruitmentPositionManagement.setRecruitmentPlan("recruitmentPlan");
        recruitmentPositionManagement.setAcademicDegree("academicDegree");
        recruitmentPositionManagement.setAge("age");
        recruitmentPositionManagement.setProfession("profession");
        recruitmentPositionManagement.setOther("other");
        recruitmentPositionManagement.setRemark("remark");
        recruitmentPositionManagement.setJobRequirements("jobRequirements");
        recruitmentPositionManagement.setJobResponsibility("jobResponsibility");
        recruitmentPositionManagement.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setReleaseStatus("releaseStatus");
        recruitmentPositionManagement.setOwningBatch(0);
        final PageResult<RecruitmentPositionManagement> expectedResult = new PageResult<>(
                Arrays.asList(recruitmentPositionManagement), 0L);

        // Configure RecruitmentPositionManagementMapper.queryByList(...).
        final RecruitmentPositionManagementVO recruitmentPositionManagementVO1 = new RecruitmentPositionManagementVO();
        recruitmentPositionManagementVO1.setId(0);
        recruitmentPositionManagementVO1.setRecruitmentUnit("recruitmentUnit");
        recruitmentPositionManagementVO1.setRecruitmentDepartment("recruitmentDepartment");
        recruitmentPositionManagementVO1.setPositionName("positionName");
        recruitmentPositionManagementVO1.setPostCode("postCode");
        when(mockRecruitmentPositionManagementMapper.queryByList(any(IPage.class),
                eq(recruitmentPositionManagementVO1))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitmentPositionManagement> result = recruitmentPositionManagementServiceImplUnderTest.queryByList(
                recruitmentPositionManagementVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testAdd() {
        // Setup
        final RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
        recruitmentPositionManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setId(0);
        recruitmentPositionManagement.setPositionName("positionName");
        recruitmentPositionManagement.setPostCode("postCode");
        recruitmentPositionManagement.setRecruitmentPlan("recruitmentPlan");
        recruitmentPositionManagement.setAcademicDegree("academicDegree");
        recruitmentPositionManagement.setAge("age");
        recruitmentPositionManagement.setProfession("profession");
        recruitmentPositionManagement.setOther("other");
        recruitmentPositionManagement.setRemark("remark");
        recruitmentPositionManagement.setJobRequirements("jobRequirements");
        recruitmentPositionManagement.setJobResponsibility("jobResponsibility");
        recruitmentPositionManagement.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setReleaseStatus("releaseStatus");
        recruitmentPositionManagement.setOwningBatch(0);

        when(mockRecruitmentPositionManagementMapper.jobTitle("positionName", 0)).thenReturn(0);
        when(mockRecruitmentPositionManagementMapper.postCode("postCode")).thenReturn(0);

        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitmentPositionManagementServiceImplUnderTest.add(
                recruitmentPositionManagement);

        // Verify the results
        // Confirm RecruitmentPositionManagementMapper.insert(...).
        final RecruitmentPositionManagement entity = new RecruitmentPositionManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setPositionName("positionName");
        entity.setPostCode("postCode");
        entity.setRecruitmentPlan("recruitmentPlan");
        entity.setAcademicDegree("academicDegree");
        entity.setAge("age");
        entity.setProfession("profession");
        entity.setOther("other");
        entity.setRemark("remark");
        entity.setJobRequirements("jobRequirements");
        entity.setJobResponsibility("jobResponsibility");
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setReleaseStatus("releaseStatus");
        entity.setOwningBatch(0);
        verify(mockRecruitmentPositionManagementMapper).insert(entity);
    }

    @Test
    void testEdit() {
        // Setup
        final RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
        recruitmentPositionManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setId(0);
        recruitmentPositionManagement.setPositionName("positionName");
        recruitmentPositionManagement.setPostCode("postCode");
        recruitmentPositionManagement.setRecruitmentPlan("recruitmentPlan");
        recruitmentPositionManagement.setAcademicDegree("academicDegree");
        recruitmentPositionManagement.setAge("age");
        recruitmentPositionManagement.setProfession("profession");
        recruitmentPositionManagement.setOther("other");
        recruitmentPositionManagement.setRemark("remark");
        recruitmentPositionManagement.setJobRequirements("jobRequirements");
        recruitmentPositionManagement.setJobResponsibility("jobResponsibility");
        recruitmentPositionManagement.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setReleaseStatus("releaseStatus");
        recruitmentPositionManagement.setOwningBatch(0);

        when(mockRecruitmentPositionManagementMapper.jobTitleEdit("positionName", 0, 0)).thenReturn(0);
        when(mockRecruitmentPositionManagementMapper.postCodeEdit("postCode", 0)).thenReturn(0);

        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitmentPositionManagementServiceImplUnderTest.edit(
                recruitmentPositionManagement);

        // Verify the results
        // Confirm RecruitmentPositionManagementMapper.updateById(...).
        final RecruitmentPositionManagement entity = new RecruitmentPositionManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setPositionName("positionName");
        entity.setPostCode("postCode");
        entity.setRecruitmentPlan("recruitmentPlan");
        entity.setAcademicDegree("academicDegree");
        entity.setAge("age");
        entity.setProfession("profession");
        entity.setOther("other");
        entity.setRemark("remark");
        entity.setJobRequirements("jobRequirements");
        entity.setJobResponsibility("jobResponsibility");
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setReleaseStatus("releaseStatus");
        entity.setOwningBatch(0);
        verify(mockRecruitmentPositionManagementMapper).updateById(entity);
    }

    @Test
    void testDel() {
        // Setup
        final RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
        recruitmentPositionManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setId(0);
        recruitmentPositionManagement.setPositionName("positionName");
        recruitmentPositionManagement.setPostCode("postCode");
        recruitmentPositionManagement.setRecruitmentPlan("recruitmentPlan");
        recruitmentPositionManagement.setAcademicDegree("academicDegree");
        recruitmentPositionManagement.setAge("age");
        recruitmentPositionManagement.setProfession("profession");
        recruitmentPositionManagement.setOther("other");
        recruitmentPositionManagement.setRemark("remark");
        recruitmentPositionManagement.setJobRequirements("jobRequirements");
        recruitmentPositionManagement.setJobResponsibility("jobResponsibility");
        recruitmentPositionManagement.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentPositionManagement.setReleaseStatus("releaseStatus");
        recruitmentPositionManagement.setOwningBatch(0);

        final ResponseEntity<String> expectedResult = new ResponseEntity<>("body", HttpStatus.OK);

        // Run the test
        final ResponseEntity<String> result = recruitmentPositionManagementServiceImplUnderTest.del(
                recruitmentPositionManagement);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm RecruitmentPositionManagementMapper.deleteById(...).
        final RecruitmentPositionManagement entity = new RecruitmentPositionManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setPositionName("positionName");
        entity.setPostCode("postCode");
        entity.setRecruitmentPlan("recruitmentPlan");
        entity.setAcademicDegree("academicDegree");
        entity.setAge("age");
        entity.setProfession("profession");
        entity.setOther("other");
        entity.setRemark("remark");
        entity.setJobRequirements("jobRequirements");
        entity.setJobResponsibility("jobResponsibility");
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setReleaseStatus("releaseStatus");
        entity.setOwningBatch(0);
        verify(mockRecruitmentPositionManagementMapper).deleteById(entity);
    }

    @Test
    void testPublish() {
        // Setup
        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitmentPositionManagementServiceImplUnderTest.publish(0,
                "startTime", "cutTime");

        // Verify the results
        // Confirm RecruitmentPositionManagementMapper.updateById(...).
        final RecruitmentPositionManagement entity = new RecruitmentPositionManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setPositionName("positionName");
        entity.setPostCode("postCode");
        entity.setRecruitmentPlan("recruitmentPlan");
        entity.setAcademicDegree("academicDegree");
        entity.setAge("age");
        entity.setProfession("profession");
        entity.setOther("other");
        entity.setRemark("remark");
        entity.setJobRequirements("jobRequirements");
        entity.setJobResponsibility("jobResponsibility");
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setReleaseStatus("releaseStatus");
        entity.setOwningBatch(0);
        verify(mockRecruitmentPositionManagementMapper).updateById(entity);
    }

    @Test
    void testRecall() {
        // Setup
        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitmentPositionManagementServiceImplUnderTest.recall(0);

//        // Verify the results
//        verify(mockRecruitmentPositionManagementMapper).update(any(T.class), any(UpdateWrapper.class));

        // Confirm RecruitmentPositionManagementMapper.updateById(...).
        final RecruitmentPositionManagement entity = new RecruitmentPositionManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setPositionName("positionName");
        entity.setPostCode("postCode");
        entity.setRecruitmentPlan("recruitmentPlan");
        entity.setAcademicDegree("academicDegree");
        entity.setAge("age");
        entity.setProfession("profession");
        entity.setOther("other");
        entity.setRemark("remark");
        entity.setJobRequirements("jobRequirements");
        entity.setJobResponsibility("jobResponsibility");
        entity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setReleaseStatus("releaseStatus");
        entity.setOwningBatch(0);
        verify(mockRecruitmentPositionManagementMapper).updateById(entity);
    }

    @Test
    void testTimedTask() {
        // Setup
        final ResponseEntity<String> expectedResult = new ResponseEntity<>("body", HttpStatus.OK);

        // Run the test
        final ResponseEntity<String> result = recruitmentPositionManagementServiceImplUnderTest.timedTask();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
//        verify(mockRecruitmentPositionManagementMapper).update(any(T.class), any(UpdateWrapper.class));
    }
}
