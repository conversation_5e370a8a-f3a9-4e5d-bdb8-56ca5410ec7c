package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.*;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.*;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RecruitBasicInfoServiceImplTest {

    @Mock
    private RecruitBasicInfoMapper mockRecruitBasicInfoMapper;
    @Mock
    private RecruitRelationshipMapper mockRecruitRelationshipMapper;
    @Mock
    private RecruitLearningExpMapper mockRecruitLearningExpMapper;
    @Mock
    private RecruitWorkExpMapper mockRecruitWorkExpMapper;
    @Mock
    private RecruitAccessoryInfoMapper mockRecruitAccessoryInfoMapper;

    private RecruitBasicInfoServiceImpl recruitBasicInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        recruitBasicInfoServiceImplUnderTest = new RecruitBasicInfoServiceImpl();
        recruitBasicInfoServiceImplUnderTest.recruitBasicInfoMapper = mockRecruitBasicInfoMapper;
        recruitBasicInfoServiceImplUnderTest.recruitRelationshipMapper = mockRecruitRelationshipMapper;
        recruitBasicInfoServiceImplUnderTest.recruitLearningExpMapper = mockRecruitLearningExpMapper;
        recruitBasicInfoServiceImplUnderTest.recruitWorkExpMapper = mockRecruitWorkExpMapper;
        recruitBasicInfoServiceImplUnderTest.recruitAccessoryInfoMapper = mockRecruitAccessoryInfoMapper;
    }

    @Test
    void testQueryByList() {
        // Setup
        final RecruitBasicInfoVO recruitBasicInfoVO = new RecruitBasicInfoVO();
        recruitBasicInfoVO.setId(0);
        recruitBasicInfoVO.setSex(0);
        recruitBasicInfoVO.setStatus(0);
        recruitBasicInfoVO.setJobCategory(0);
        recruitBasicInfoVO.setFirstApprove("firstApprove");
        recruitBasicInfoVO.setFaceApprove("faceApprove");

        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        final PageResult<RecruitBasicInfo> expectedResult = new PageResult<>(Arrays.asList(recruitBasicInfo), 0L);

        // Configure RecruitBasicInfoMapper.queryByList(...).
        final RecruitBasicInfo recruitBasicInfo1 = new RecruitBasicInfo();
        recruitBasicInfo1.setId(0);
        recruitBasicInfo1.setSex(0);
        recruitBasicInfo1.setCardNo("cardNo");
        recruitBasicInfo1.setResumeInfo("resumeInfo");
        recruitBasicInfo1.setStatus(0);
        recruitBasicInfo1.setJobCategory(0);
        recruitBasicInfo1.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo1.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo1.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo1.setFirstApprove("firstApprove");
        recruitBasicInfo1.setFaceApprove("faceApprove");
        recruitBasicInfo1.setUserId(0L);
        final List<RecruitBasicInfo> recruitBasicInfos = Arrays.asList(recruitBasicInfo1);
        final RecruitBasicInfoVO recruitBasicInfoVO1 = new RecruitBasicInfoVO();
        recruitBasicInfoVO1.setId(0);
        recruitBasicInfoVO1.setSex(0);
        recruitBasicInfoVO1.setStatus(0);
        recruitBasicInfoVO1.setJobCategory(0);
        recruitBasicInfoVO1.setFirstApprove("firstApprove");
        recruitBasicInfoVO1.setFaceApprove("faceApprove");
        when(mockRecruitBasicInfoMapper.queryByList(any(IPage.class), eq(recruitBasicInfoVO1)))
                .thenReturn(recruitBasicInfos);

        // Run the test
        final PageResult<RecruitBasicInfo> result = recruitBasicInfoServiceImplUnderTest.queryByList(
                recruitBasicInfoVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryByList_RecruitBasicInfoMapperReturnsNoItems() {
        // Setup
        final RecruitBasicInfoVO recruitBasicInfoVO = new RecruitBasicInfoVO();
        recruitBasicInfoVO.setId(0);
        recruitBasicInfoVO.setSex(0);
        recruitBasicInfoVO.setStatus(0);
        recruitBasicInfoVO.setJobCategory(0);
        recruitBasicInfoVO.setFirstApprove("firstApprove");
        recruitBasicInfoVO.setFaceApprove("faceApprove");

        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        final PageResult<RecruitBasicInfo> expectedResult = new PageResult<>(Arrays.asList(recruitBasicInfo), 0L);

        // Configure RecruitBasicInfoMapper.queryByList(...).
        final RecruitBasicInfoVO recruitBasicInfoVO1 = new RecruitBasicInfoVO();
        recruitBasicInfoVO1.setId(0);
        recruitBasicInfoVO1.setSex(0);
        recruitBasicInfoVO1.setStatus(0);
        recruitBasicInfoVO1.setJobCategory(0);
        recruitBasicInfoVO1.setFirstApprove("firstApprove");
        recruitBasicInfoVO1.setFaceApprove("faceApprove");
        when(mockRecruitBasicInfoMapper.queryByList(any(IPage.class), eq(recruitBasicInfoVO1)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitBasicInfo> result = recruitBasicInfoServiceImplUnderTest.queryByList(
                recruitBasicInfoVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testFaceByList() {
        // Setup
        final RecruitBasicInfoVO recruitBasicInfoVO = new RecruitBasicInfoVO();
        recruitBasicInfoVO.setId(0);
        recruitBasicInfoVO.setSex(0);
        recruitBasicInfoVO.setStatus(0);
        recruitBasicInfoVO.setJobCategory(0);
        recruitBasicInfoVO.setFirstApprove("firstApprove");
        recruitBasicInfoVO.setFaceApprove("faceApprove");

        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        final PageResult<RecruitBasicInfo> expectedResult = new PageResult<>(Arrays.asList(recruitBasicInfo), 0L);

        // Configure RecruitBasicInfoMapper.faceByList(...).
        final RecruitBasicInfo recruitBasicInfo1 = new RecruitBasicInfo();
        recruitBasicInfo1.setId(0);
        recruitBasicInfo1.setSex(0);
        recruitBasicInfo1.setCardNo("cardNo");
        recruitBasicInfo1.setResumeInfo("resumeInfo");
        recruitBasicInfo1.setStatus(0);
        recruitBasicInfo1.setJobCategory(0);
        recruitBasicInfo1.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo1.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo1.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo1.setFirstApprove("firstApprove");
        recruitBasicInfo1.setFaceApprove("faceApprove");
        recruitBasicInfo1.setUserId(0L);
        final List<RecruitBasicInfo> recruitBasicInfos = Arrays.asList(recruitBasicInfo1);
        final RecruitBasicInfoVO recruitBasicInfoVO1 = new RecruitBasicInfoVO();
        recruitBasicInfoVO1.setId(0);
        recruitBasicInfoVO1.setSex(0);
        recruitBasicInfoVO1.setStatus(0);
        recruitBasicInfoVO1.setJobCategory(0);
        recruitBasicInfoVO1.setFirstApprove("firstApprove");
        recruitBasicInfoVO1.setFaceApprove("faceApprove");
        when(mockRecruitBasicInfoMapper.faceByList(any(IPage.class), eq(recruitBasicInfoVO1)))
                .thenReturn(recruitBasicInfos);

        // Run the test
        final PageResult<RecruitBasicInfo> result = recruitBasicInfoServiceImplUnderTest.faceByList(recruitBasicInfoVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testFaceByList_RecruitBasicInfoMapperReturnsNoItems() {
        // Setup
        final RecruitBasicInfoVO recruitBasicInfoVO = new RecruitBasicInfoVO();
        recruitBasicInfoVO.setId(0);
        recruitBasicInfoVO.setSex(0);
        recruitBasicInfoVO.setStatus(0);
        recruitBasicInfoVO.setJobCategory(0);
        recruitBasicInfoVO.setFirstApprove("firstApprove");
        recruitBasicInfoVO.setFaceApprove("faceApprove");

        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        final PageResult<RecruitBasicInfo> expectedResult = new PageResult<>(Arrays.asList(recruitBasicInfo), 0L);

        // Configure RecruitBasicInfoMapper.faceByList(...).
        final RecruitBasicInfoVO recruitBasicInfoVO1 = new RecruitBasicInfoVO();
        recruitBasicInfoVO1.setId(0);
        recruitBasicInfoVO1.setSex(0);
        recruitBasicInfoVO1.setStatus(0);
        recruitBasicInfoVO1.setJobCategory(0);
        recruitBasicInfoVO1.setFirstApprove("firstApprove");
        recruitBasicInfoVO1.setFaceApprove("faceApprove");
        when(mockRecruitBasicInfoMapper.faceByList(any(IPage.class), eq(recruitBasicInfoVO1)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitBasicInfo> result = recruitBasicInfoServiceImplUnderTest.faceByList(recruitBasicInfoVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testCheck() {
        // Setup
        final RecruitBasicInfoVO recruitBasicInfoVO = new RecruitBasicInfoVO();
        recruitBasicInfoVO.setId(0);
        recruitBasicInfoVO.setSex(0);
        recruitBasicInfoVO.setStatus(0);
        recruitBasicInfoVO.setJobCategory(0);
        recruitBasicInfoVO.setFirstApprove("firstApprove");
        recruitBasicInfoVO.setFaceApprove("faceApprove");

        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitBasicInfoServiceImplUnderTest.check(
                recruitBasicInfoVO);

        // Verify the results
        // Confirm RecruitBasicInfoMapper.updateById(...).
        final RecruitBasicInfo entity = new RecruitBasicInfo();
        entity.setId(0);
        entity.setSex(0);
        entity.setCardNo("cardNo");
        entity.setResumeInfo("resumeInfo");
        entity.setStatus(0);
        entity.setJobCategory(0);
        entity.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setFirstApprove("firstApprove");
        entity.setFaceApprove("faceApprove");
        entity.setUserId(0L);
        verify(mockRecruitBasicInfoMapper).updateById(entity);
    }

    @Test
    void testApply() {
        // Setup
        final RecruitApplyVO recruitApplyVO = new RecruitApplyVO();
        recruitApplyVO.setId(0);
        recruitApplyVO.setSex(0);
        recruitApplyVO.setStatus(0);
        recruitApplyVO.setJobCategory(0);
        recruitApplyVO.setFirstApprove("firstApprove");
        recruitApplyVO.setFaceApprove("faceApprove");
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        recruitApplyVO.setRecruitRelationships(Arrays.asList(recruitRelationship));
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        recruitApplyVO.setRecruitLearningExps(Arrays.asList(recruitLearningExp));
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        recruitApplyVO.setRecruitWorkExps(Arrays.asList(recruitWorkExp));
        final RecruitAccessoryInfo recruitAccessoryInfo = new RecruitAccessoryInfo();
        recruitAccessoryInfo.setBaseId(0);
        recruitApplyVO.setRecruitAccessoryInfo(Arrays.asList(recruitAccessoryInfo));

        when(mockRecruitBasicInfoMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        // Run the test
        final Integer result = recruitBasicInfoServiceImplUnderTest.apply(recruitApplyVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockRecruitLearningExpMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockRecruitWorkExpMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockRecruitRelationshipMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockRecruitAccessoryInfoMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm RecruitWorkExpMapper.insertBatch(...).
        final RecruitWorkExp recruitWorkExp1 = new RecruitWorkExp();
        recruitWorkExp1.setBaseId(0);
        recruitWorkExp1.setWorkUnit("workUnit");
        recruitWorkExp1.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp1.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp1.setJobTitle("jobTitle");
        final List<RecruitWorkExp> entities = Arrays.asList(recruitWorkExp1);
        verify(mockRecruitWorkExpMapper).insertBatch(entities);

        // Confirm RecruitLearningExpMapper.insertBatch(...).
        final RecruitLearningExp recruitLearningExp1 = new RecruitLearningExp();
        recruitLearningExp1.setBaseId(0);
        recruitLearningExp1.setEducationLevel("educationLevel");
        recruitLearningExp1.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp1.setSchoolName("schoolName");
        recruitLearningExp1.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> entities1 = Arrays.asList(recruitLearningExp1);
        verify(mockRecruitLearningExpMapper).insertBatch(entities1);

        // Confirm RecruitRelationshipMapper.insertBatch(...).
        final RecruitRelationship recruitRelationship1 = new RecruitRelationship();
        recruitRelationship1.setBaseId(0);
        recruitRelationship1.setRelation("relation");
        recruitRelationship1.setName("name");
        recruitRelationship1.setAge(0);
        recruitRelationship1.setPoliticalStatus("politicalStatus");
        recruitRelationship1.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> entities2 = Arrays.asList(recruitRelationship1);
        verify(mockRecruitRelationshipMapper).insertBatch(entities2);

        // Confirm RecruitAccessoryInfoMapper.insertBatch(...).
        final RecruitAccessoryInfo recruitAccessoryInfo1 = new RecruitAccessoryInfo();
        recruitAccessoryInfo1.setId(0);
        recruitAccessoryInfo1.setBaseId(0);
        recruitAccessoryInfo1.setAccessoryName("accessoryName");
        recruitAccessoryInfo1.setAccessoryType("accessoryType");
        recruitAccessoryInfo1.setAccessory("accessory");
        final List<RecruitAccessoryInfo> entities3 = Arrays.asList(recruitAccessoryInfo1);
        verify(mockRecruitAccessoryInfoMapper).insertBatch(entities3);
    }

    @Test
    void testSaveDraft() {
        // Setup
        final RecruitApplyVO recruitApplyVO = new RecruitApplyVO();
        recruitApplyVO.setId(0);
        recruitApplyVO.setSex(0);
        recruitApplyVO.setStatus(0);
        recruitApplyVO.setJobCategory(0);
        recruitApplyVO.setFirstApprove("firstApprove");
        recruitApplyVO.setFaceApprove("faceApprove");
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        recruitApplyVO.setRecruitRelationships(Arrays.asList(recruitRelationship));
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        recruitApplyVO.setRecruitLearningExps(Arrays.asList(recruitLearningExp));
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        recruitApplyVO.setRecruitWorkExps(Arrays.asList(recruitWorkExp));
        final RecruitAccessoryInfo recruitAccessoryInfo = new RecruitAccessoryInfo();
        recruitAccessoryInfo.setBaseId(0);
        recruitApplyVO.setRecruitAccessoryInfo(Arrays.asList(recruitAccessoryInfo));

        // Run the test
        final Integer result = recruitBasicInfoServiceImplUnderTest.saveDraft(recruitApplyVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockRecruitLearningExpMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockRecruitWorkExpMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockRecruitRelationshipMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockRecruitAccessoryInfoMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm RecruitLearningExpMapper.insertBatch(...).
        final RecruitLearningExp recruitLearningExp1 = new RecruitLearningExp();
        recruitLearningExp1.setBaseId(0);
        recruitLearningExp1.setEducationLevel("educationLevel");
        recruitLearningExp1.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp1.setSchoolName("schoolName");
        recruitLearningExp1.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> entities = Arrays.asList(recruitLearningExp1);
        verify(mockRecruitLearningExpMapper).insertBatch(entities);

        // Confirm RecruitWorkExpMapper.insertBatch(...).
        final RecruitWorkExp recruitWorkExp1 = new RecruitWorkExp();
        recruitWorkExp1.setBaseId(0);
        recruitWorkExp1.setWorkUnit("workUnit");
        recruitWorkExp1.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp1.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp1.setJobTitle("jobTitle");
        final List<RecruitWorkExp> entities1 = Arrays.asList(recruitWorkExp1);
        verify(mockRecruitWorkExpMapper).insertBatch(entities1);

        // Confirm RecruitRelationshipMapper.insertBatch(...).
        final RecruitRelationship recruitRelationship1 = new RecruitRelationship();
        recruitRelationship1.setBaseId(0);
        recruitRelationship1.setRelation("relation");
        recruitRelationship1.setName("name");
        recruitRelationship1.setAge(0);
        recruitRelationship1.setPoliticalStatus("politicalStatus");
        recruitRelationship1.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> entities2 = Arrays.asList(recruitRelationship1);
        verify(mockRecruitRelationshipMapper).insertBatch(entities2);

        // Confirm RecruitAccessoryInfoMapper.insertBatch(...).
        final RecruitAccessoryInfo recruitAccessoryInfo1 = new RecruitAccessoryInfo();
        recruitAccessoryInfo1.setId(0);
        recruitAccessoryInfo1.setBaseId(0);
        recruitAccessoryInfo1.setAccessoryName("accessoryName");
        recruitAccessoryInfo1.setAccessoryType("accessoryType");
        recruitAccessoryInfo1.setAccessory("accessory");
        final List<RecruitAccessoryInfo> entities3 = Arrays.asList(recruitAccessoryInfo1);
        verify(mockRecruitAccessoryInfoMapper).insertBatch(entities3);
    }

    @Test
    void testBackToModify() {
        // Setup
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);

        // Run the test
        recruitBasicInfoServiceImplUnderTest.backToModify(recruitBasicInfo);

        // Verify the results
    }

    @Test
    void testDetail() {
        // Setup
        final RecruitDetailRespVO expectedResult = new RecruitDetailRespVO();
        expectedResult.setId(0);
        expectedResult.setSex(0);
        expectedResult.setStatus(0);
        expectedResult.setJobCategory(0);
        expectedResult.setFirstApprove("firstApprove");
        expectedResult.setFaceApprove("faceApprove");
        expectedResult.setSexResp("其他");
        expectedResult.setJobCategoryResp("专技岗位");

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitAccessoryInfoMapper.selectList(...).
        final RecruitAccessoryInfo recruitAccessoryInfo = new RecruitAccessoryInfo();
        recruitAccessoryInfo.setId(0);
        recruitAccessoryInfo.setBaseId(0);
        recruitAccessoryInfo.setAccessoryName("accessoryName");
        recruitAccessoryInfo.setAccessoryType("accessoryType");
        recruitAccessoryInfo.setAccessory("accessory");
        final List<RecruitAccessoryInfo> recruitAccessoryInfos = Arrays.asList(recruitAccessoryInfo);
        when(mockRecruitAccessoryInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(recruitAccessoryInfos);

        // Run the test
        final RecruitDetailRespVO result = recruitBasicInfoServiceImplUnderTest.detail(0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDetail_RecruitLearningExpMapperReturnsNoItems() {
        // Setup
        final RecruitDetailRespVO expectedResult = new RecruitDetailRespVO();
        expectedResult.setId(0);
        expectedResult.setSex(0);
        expectedResult.setStatus(0);
        expectedResult.setJobCategory(0);
        expectedResult.setFirstApprove("firstApprove");
        expectedResult.setFaceApprove("faceApprove");
        expectedResult.setSexResp("其他");
        expectedResult.setJobCategoryResp("专技岗位");

        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitAccessoryInfoMapper.selectList(...).
        final RecruitAccessoryInfo recruitAccessoryInfo = new RecruitAccessoryInfo();
        recruitAccessoryInfo.setId(0);
        recruitAccessoryInfo.setBaseId(0);
        recruitAccessoryInfo.setAccessoryName("accessoryName");
        recruitAccessoryInfo.setAccessoryType("accessoryType");
        recruitAccessoryInfo.setAccessory("accessory");
        final List<RecruitAccessoryInfo> recruitAccessoryInfos = Arrays.asList(recruitAccessoryInfo);
        when(mockRecruitAccessoryInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(recruitAccessoryInfos);

        // Run the test
        final RecruitDetailRespVO result = recruitBasicInfoServiceImplUnderTest.detail(0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDetail_RecruitWorkExpMapperReturnsNoItems() {
        // Setup
        final RecruitDetailRespVO expectedResult = new RecruitDetailRespVO();
        expectedResult.setId(0);
        expectedResult.setSex(0);
        expectedResult.setStatus(0);
        expectedResult.setJobCategory(0);
        expectedResult.setFirstApprove("firstApprove");
        expectedResult.setFaceApprove("faceApprove");
        expectedResult.setSexResp("其他");
        expectedResult.setJobCategoryResp("专技岗位");

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitAccessoryInfoMapper.selectList(...).
        final RecruitAccessoryInfo recruitAccessoryInfo = new RecruitAccessoryInfo();
        recruitAccessoryInfo.setId(0);
        recruitAccessoryInfo.setBaseId(0);
        recruitAccessoryInfo.setAccessoryName("accessoryName");
        recruitAccessoryInfo.setAccessoryType("accessoryType");
        recruitAccessoryInfo.setAccessory("accessory");
        final List<RecruitAccessoryInfo> recruitAccessoryInfos = Arrays.asList(recruitAccessoryInfo);
        when(mockRecruitAccessoryInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(recruitAccessoryInfos);

        // Run the test
        final RecruitDetailRespVO result = recruitBasicInfoServiceImplUnderTest.detail(0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDetail_RecruitRelationshipMapperReturnsNoItems() {
        // Setup
        final RecruitDetailRespVO expectedResult = new RecruitDetailRespVO();
        expectedResult.setId(0);
        expectedResult.setSex(0);
        expectedResult.setStatus(0);
        expectedResult.setJobCategory(0);
        expectedResult.setFirstApprove("firstApprove");
        expectedResult.setFaceApprove("faceApprove");
        expectedResult.setSexResp("其他");
        expectedResult.setJobCategoryResp("专技岗位");

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RecruitAccessoryInfoMapper.selectList(...).
        final RecruitAccessoryInfo recruitAccessoryInfo = new RecruitAccessoryInfo();
        recruitAccessoryInfo.setId(0);
        recruitAccessoryInfo.setBaseId(0);
        recruitAccessoryInfo.setAccessoryName("accessoryName");
        recruitAccessoryInfo.setAccessoryType("accessoryType");
        recruitAccessoryInfo.setAccessory("accessory");
        final List<RecruitAccessoryInfo> recruitAccessoryInfos = Arrays.asList(recruitAccessoryInfo);
        when(mockRecruitAccessoryInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(recruitAccessoryInfos);

        // Run the test
        final RecruitDetailRespVO result = recruitBasicInfoServiceImplUnderTest.detail(0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDetail_RecruitAccessoryInfoMapperReturnsNoItems() {
        // Setup
        final RecruitDetailRespVO expectedResult = new RecruitDetailRespVO();
        expectedResult.setId(0);
        expectedResult.setSex(0);
        expectedResult.setStatus(0);
        expectedResult.setJobCategory(0);
        expectedResult.setFirstApprove("firstApprove");
        expectedResult.setFaceApprove("faceApprove");
        expectedResult.setSexResp("其他");
        expectedResult.setJobCategoryResp("专技岗位");

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        when(mockRecruitAccessoryInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final RecruitDetailRespVO result = recruitBasicInfoServiceImplUnderTest.detail(0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDeliveryStatus() {
        // Setup
        final RecruitBasicInfoRespVO recruitBasicInfoRespVO = new RecruitBasicInfoRespVO();
        recruitBasicInfoRespVO.setId(0);
        recruitBasicInfoRespVO.setStatus(0);
        recruitBasicInfoRespVO.setJobTitle("jobTitle");
        recruitBasicInfoRespVO.setRecruitmentUnit("recruitmentUnit");
        recruitBasicInfoRespVO.setRecruitmentDept("recruitmentDept");
        final List<RecruitBasicInfoRespVO> expectedResult = Arrays.asList(recruitBasicInfoRespVO);

        // Configure RecruitBasicInfoMapper.getByUserId(...).
        final RecruitBasicInfoRespVO recruitBasicInfoRespVO1 = new RecruitBasicInfoRespVO();
        recruitBasicInfoRespVO1.setId(0);
        recruitBasicInfoRespVO1.setStatus(0);
        recruitBasicInfoRespVO1.setJobTitle("jobTitle");
        recruitBasicInfoRespVO1.setRecruitmentUnit("recruitmentUnit");
        recruitBasicInfoRespVO1.setRecruitmentDept("recruitmentDept");
        final List<RecruitBasicInfoRespVO> recruitBasicInfoRespVOS = Arrays.asList(recruitBasicInfoRespVO1);
        when(mockRecruitBasicInfoMapper.getByUserId(0L)).thenReturn(recruitBasicInfoRespVOS);

        // Run the test
        final List<RecruitBasicInfoRespVO> result = recruitBasicInfoServiceImplUnderTest.deliveryStatus();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDeliveryStatus_RecruitBasicInfoMapperReturnsNoItems() {
        // Setup
        when(mockRecruitBasicInfoMapper.getByUserId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<RecruitBasicInfoRespVO> result = recruitBasicInfoServiceImplUnderTest.deliveryStatus();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetStationPage() {
        // Setup
        final RecruitStationRequestVO recruitStationRequestVO = new RecruitStationRequestVO();
        recruitStationRequestVO.setJobCategory(0);

        final RecruitStationVO recruitStationVO = new RecruitStationVO();
        recruitStationVO.setId(0);
        recruitStationVO.setRecruitmentUnit("recruitmentUnit");
        recruitStationVO.setRecruitmentDepartment("recruitmentDepartment");
        recruitStationVO.setPostCode("postCode");
        recruitStationVO.setPositionName("positionName");
        final PageResult<RecruitStationVO> expectedResult = new PageResult<>(Arrays.asList(recruitStationVO), 0L);

        // Configure RecruitBasicInfoMapper.getStationPage(...).
        final RecruitStationVO recruitStationVO1 = new RecruitStationVO();
        recruitStationVO1.setId(0);
        recruitStationVO1.setRecruitmentUnit("recruitmentUnit");
        recruitStationVO1.setRecruitmentDepartment("recruitmentDepartment");
        recruitStationVO1.setPostCode("postCode");
        recruitStationVO1.setPositionName("positionName");
        final List<RecruitStationVO> recruitStationVOS = Arrays.asList(recruitStationVO1);
        final RecruitStationRequestVO recruitStationRequestV = new RecruitStationRequestVO();
        recruitStationRequestV.setJobCategory(0);
        when(mockRecruitBasicInfoMapper.getStationPage(any(IPage.class), eq(recruitStationRequestV)))
                .thenReturn(recruitStationVOS);

        // Run the test
        final PageResult<RecruitStationVO> result = recruitBasicInfoServiceImplUnderTest.getStationPage(
                recruitStationRequestVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetStationPage_RecruitBasicInfoMapperReturnsNoItems() {
        // Setup
        final RecruitStationRequestVO recruitStationRequestVO = new RecruitStationRequestVO();
        recruitStationRequestVO.setJobCategory(0);

        final RecruitStationVO recruitStationVO = new RecruitStationVO();
        recruitStationVO.setId(0);
        recruitStationVO.setRecruitmentUnit("recruitmentUnit");
        recruitStationVO.setRecruitmentDepartment("recruitmentDepartment");
        recruitStationVO.setPostCode("postCode");
        recruitStationVO.setPositionName("positionName");
        final PageResult<RecruitStationVO> expectedResult = new PageResult<>(Arrays.asList(recruitStationVO), 0L);

        // Configure RecruitBasicInfoMapper.getStationPage(...).
        final RecruitStationRequestVO recruitStationRequestV = new RecruitStationRequestVO();
        recruitStationRequestV.setJobCategory(0);
        when(mockRecruitBasicInfoMapper.getStationPage(any(IPage.class), eq(recruitStationRequestV)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitStationVO> result = recruitBasicInfoServiceImplUnderTest.getStationPage(
                recruitStationRequestVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetInformationById() {
        // Setup
        final RecruitInformationVO expectedResult = new RecruitInformationVO();
        expectedResult.setPositionName("positionName");
        expectedResult.setRecruitmentUnit("recruitmentUnit");
        expectedResult.setRecruitmentDepartment("recruitmentDepartment");
        expectedResult.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setIsOverdue(false);

        // Configure RecruitBasicInfoMapper.getInformationById(...).
        final RecruitInformationVO recruitInformationVO = new RecruitInformationVO();
        recruitInformationVO.setPositionName("positionName");
        recruitInformationVO.setRecruitmentUnit("recruitmentUnit");
        recruitInformationVO.setRecruitmentDepartment("recruitmentDepartment");
        recruitInformationVO.setCutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitInformationVO.setIsOverdue(false);
        when(mockRecruitBasicInfoMapper.getInformationById(0L)).thenReturn(recruitInformationVO);

        // Run the test
        final RecruitInformationVO result = recruitBasicInfoServiceImplUnderTest.getInformationById(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetMobilesByIds() {
        // Setup
        when(mockRecruitBasicInfoMapper.getMobilesByIds(Arrays.asList(0L))).thenReturn(Arrays.asList(0L));

        // Run the test
        final List<Long> result = recruitBasicInfoServiceImplUnderTest.getMobilesByIds(Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList(0L));
    }

    @Test
    void testGetMobilesByIds_RecruitBasicInfoMapperReturnsNoItems() {
        // Setup
        when(mockRecruitBasicInfoMapper.getMobilesByIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = recruitBasicInfoServiceImplUnderTest.getMobilesByIds(Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetBasicInfoExcel() {
        // Setup
        final RecruitBasicInfoExportReqVO reqVO = new RecruitBasicInfoExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        final RecruitBasicInfoExcelVO recruitBasicInfoExcelVO = new RecruitBasicInfoExcelVO();
        recruitBasicInfoExcelVO.setGender("其他");
        recruitBasicInfoExcelVO.setWorkUnitType("公益一类");
        recruitBasicInfoExcelVO.setAge(0);
        recruitBasicInfoExcelVO.setPersonalClassification("专技岗位");
        recruitBasicInfoExcelVO.setRelation("relation");
        recruitBasicInfoExcelVO.setMemberName("name");
        recruitBasicInfoExcelVO.setMemberAge("memberAge");
        recruitBasicInfoExcelVO.setMemberPoliticalStatus("politicalStatus");
        recruitBasicInfoExcelVO.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitBasicInfoExcelVO> expectedResult = Arrays.asList(recruitBasicInfoExcelVO);

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Run the test
        final List<RecruitBasicInfoExcelVO> result = recruitBasicInfoServiceImplUnderTest.getBasicInfoExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetBasicInfoExcel_RecruitRelationshipMapperReturnsNoItems() {
        // Setup
        final RecruitBasicInfoExportReqVO reqVO = new RecruitBasicInfoExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Run the test
        final List<RecruitBasicInfoExcelVO> result = recruitBasicInfoServiceImplUnderTest.getBasicInfoExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetBasicInfoExcel_RecruitLearningExpMapperReturnsNoItems() {
        // Setup
        final RecruitBasicInfoExportReqVO reqVO = new RecruitBasicInfoExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Run the test
        final List<RecruitBasicInfoExcelVO> result = recruitBasicInfoServiceImplUnderTest.getBasicInfoExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetBasicInfoExcel_RecruitWorkExpMapperReturnsNoItems() {
        // Setup
        final RecruitBasicInfoExportReqVO reqVO = new RecruitBasicInfoExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<RecruitBasicInfoExcelVO> result = recruitBasicInfoServiceImplUnderTest.getBasicInfoExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetApplyExcel() {
        // Setup
        final RecruitApplyExportReqVO reqVO = new RecruitApplyExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        final RecruitApplyExcelVO recruitApplyExcelVO = new RecruitApplyExcelVO();
        recruitApplyExcelVO.setRelation("relation");
        recruitApplyExcelVO.setMemberName("name");
        recruitApplyExcelVO.setMemberAge("memberAge");
        recruitApplyExcelVO.setMemberPoliticalStatus("politicalStatus");
        recruitApplyExcelVO.setWorkUnitAndPosition("workUnitAndPosition");
        final List<List<RecruitApplyExcelVO>> expectedResult = Arrays.asList(Arrays.asList(recruitApplyExcelVO));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Run the test
        final List<List<RecruitApplyExcelVO>> result = recruitBasicInfoServiceImplUnderTest.getApplyExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetApplyExcel_RecruitRelationshipMapperReturnsNoItems() {
        // Setup
        final RecruitApplyExportReqVO reqVO = new RecruitApplyExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Run the test
        final List<List<RecruitApplyExcelVO>> result = recruitBasicInfoServiceImplUnderTest.getApplyExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetApplyExcel_RecruitLearningExpMapperReturnsNoItems() {
        // Setup
        final RecruitApplyExportReqVO reqVO = new RecruitApplyExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure RecruitWorkExpMapper.selectList(...).
        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setJobTitle("jobTitle");
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp);
        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitWorkExps);

        // Run the test
        final List<List<RecruitApplyExcelVO>> result = recruitBasicInfoServiceImplUnderTest.getApplyExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetApplyExcel_RecruitWorkExpMapperReturnsNoItems() {
        // Setup
        final RecruitApplyExportReqVO reqVO = new RecruitApplyExportReqVO();
        reqVO.setIds(Arrays.asList(0));

        // Configure RecruitBasicInfoMapper.selectById(...).
        final RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
        recruitBasicInfo.setId(0);
        recruitBasicInfo.setSex(0);
        recruitBasicInfo.setCardNo("cardNo");
        recruitBasicInfo.setResumeInfo("resumeInfo");
        recruitBasicInfo.setStatus(0);
        recruitBasicInfo.setJobCategory(0);
        recruitBasicInfo.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setTitleExaminationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setPassInterviewTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitBasicInfo.setFirstApprove("firstApprove");
        recruitBasicInfo.setFaceApprove("faceApprove");
        recruitBasicInfo.setUserId(0L);
        when(mockRecruitBasicInfoMapper.selectById(0)).thenReturn(recruitBasicInfo);

        // Configure RecruitRelationshipMapper.selectList(...).
        final RecruitRelationship recruitRelationship = new RecruitRelationship();
        recruitRelationship.setBaseId(0);
        recruitRelationship.setRelation("relation");
        recruitRelationship.setName("name");
        recruitRelationship.setAge(0);
        recruitRelationship.setPoliticalStatus("politicalStatus");
        recruitRelationship.setWorkUnitAndPosition("workUnitAndPosition");
        final List<RecruitRelationship> recruitRelationships = Arrays.asList(recruitRelationship);
        when(mockRecruitRelationshipMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitRelationships);

        // Configure RecruitLearningExpMapper.selectList(...).
        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        recruitLearningExp.setSchoolName("schoolName");
        recruitLearningExp.setEnrollmentDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp);
        when(mockRecruitLearningExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(recruitLearningExps);

        when(mockRecruitWorkExpMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<List<RecruitApplyExcelVO>> result = recruitBasicInfoServiceImplUnderTest.getApplyExcel(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
