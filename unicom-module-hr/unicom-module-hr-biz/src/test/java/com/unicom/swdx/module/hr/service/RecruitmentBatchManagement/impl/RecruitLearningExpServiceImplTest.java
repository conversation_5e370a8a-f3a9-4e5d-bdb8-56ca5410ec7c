package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitLearningExp;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitLearningExpVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitLearningExpMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RecruitLearningExpServiceImplTest {

    @Mock
    private RecruitLearningExpMapper mockRecruitLearningExpMapper;

    private RecruitLearningExpServiceImpl recruitLearningExpServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        recruitLearningExpServiceImplUnderTest = new RecruitLearningExpServiceImpl();
        recruitLearningExpServiceImplUnderTest.recruitLearningExpMapper = mockRecruitLearningExpMapper;
    }

    @Test
    void testQueryByList() {
        // Setup
        final RecruitLearningExpVO recruitLearningExpVO = new RecruitLearningExpVO();
        recruitLearningExpVO.setId(0);
        recruitLearningExpVO.setBaseId(0);
        recruitLearningExpVO.setEducationLevel("educationLevel");
        recruitLearningExpVO.setAcademicDegree("academicDegree");
        recruitLearningExpVO.setGraduationDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setId(0);
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setAcademicDegree("academicDegree");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        final PageResult<RecruitLearningExp> expectedResult = new PageResult<>(Arrays.asList(recruitLearningExp), 0L);

        // Configure RecruitLearningExpMapper.queryByList(...).
        final RecruitLearningExp recruitLearningExp1 = new RecruitLearningExp();
        recruitLearningExp1.setId(0);
        recruitLearningExp1.setBaseId(0);
        recruitLearningExp1.setEducationLevel("educationLevel");
        recruitLearningExp1.setAcademicDegree("academicDegree");
        recruitLearningExp1.setGraduationDate(LocalDate.of(2020, 1, 1));
        final List<RecruitLearningExp> recruitLearningExps = Arrays.asList(recruitLearningExp1);
        final RecruitLearningExpVO recruitLearningExpVO1 = new RecruitLearningExpVO();
        recruitLearningExpVO1.setId(0);
        recruitLearningExpVO1.setBaseId(0);
        recruitLearningExpVO1.setEducationLevel("educationLevel");
        recruitLearningExpVO1.setAcademicDegree("academicDegree");
        recruitLearningExpVO1.setGraduationDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRecruitLearningExpMapper.queryByList(any(IPage.class), eq(recruitLearningExpVO1)))
                .thenReturn(recruitLearningExps);

        // Run the test
        final PageResult<RecruitLearningExp> result = recruitLearningExpServiceImplUnderTest.queryByList(
                recruitLearningExpVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryByList_RecruitLearningExpMapperReturnsNoItems() {
        // Setup
        final RecruitLearningExpVO recruitLearningExpVO = new RecruitLearningExpVO();
        recruitLearningExpVO.setId(0);
        recruitLearningExpVO.setBaseId(0);
        recruitLearningExpVO.setEducationLevel("educationLevel");
        recruitLearningExpVO.setAcademicDegree("academicDegree");
        recruitLearningExpVO.setGraduationDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final RecruitLearningExp recruitLearningExp = new RecruitLearningExp();
        recruitLearningExp.setId(0);
        recruitLearningExp.setBaseId(0);
        recruitLearningExp.setEducationLevel("educationLevel");
        recruitLearningExp.setAcademicDegree("academicDegree");
        recruitLearningExp.setGraduationDate(LocalDate.of(2020, 1, 1));
        final PageResult<RecruitLearningExp> expectedResult = new PageResult<>(Arrays.asList(recruitLearningExp), 0L);

        // Configure RecruitLearningExpMapper.queryByList(...).
        final RecruitLearningExpVO recruitLearningExpVO1 = new RecruitLearningExpVO();
        recruitLearningExpVO1.setId(0);
        recruitLearningExpVO1.setBaseId(0);
        recruitLearningExpVO1.setEducationLevel("educationLevel");
        recruitLearningExpVO1.setAcademicDegree("academicDegree");
        recruitLearningExpVO1.setGraduationDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRecruitLearningExpMapper.queryByList(any(IPage.class), eq(recruitLearningExpVO1)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitLearningExp> result = recruitLearningExpServiceImplUnderTest.queryByList(
                recruitLearningExpVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
