package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.personnal.PersonnalInformationController;
import com.unicom.swdx.module.hr.controller.admin.personnal.PersonnalTransferPageRespVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalTransferDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalMapper;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalPositionMapper;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalTransferMapper;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.system.api.message.MessageApi;
import com.unicom.swdx.module.system.api.message.dto.MessageAuthorityUpdateReqDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.HrDeptDTO;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PersonnalTransferServiceImplTest {

    @Mock
    private PersonnalTransferMapper mockPersonnalTransferMapper;
    @Mock
    private PermissionApi mockPermissionApi;
    @Mock
    private AdminUserApi mockAdminUserApi;
    @Mock
    private MessageApi mockMessageApi;
    @Mock
    private PersonnalInformationController mockPersonnalInformationController;
    @Mock
    private PersonnalMapper mockPersonnalMapper;
    @Mock
    private PersonnalPositionMapper mockPersonnalPositionMapper;
    @Mock
    private PersonProducer mockPersonProducer;
    @Mock
    private TenantApi mockTenantApi;
    @Mock
    private PersonnalService mockPersonnalService;

    @InjectMocks
    private PersonnalTransferServiceImpl personnalTransferServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        personnalTransferServiceImplUnderTest.personnalTransferMapper = mockPersonnalTransferMapper;
    }

    @Test
    void testCreatePersonnalTransfer() {
        // Setup
        final PersonnalCreateTransferReqVO createReqVO = new PersonnalCreateTransferReqVO();
        createReqVO.setId(0L);
        createReqVO.setUserId(0L);
        createReqVO.setOutDeptIds("outDeptIds");
        createReqVO.setInDeptIds("inDeptIds");
        createReqVO.setInRank(0);
        createReqVO.setInAdministrativePositionName("administrativePositionName");
        createReqVO.setInAdministrativePositionRank(0);
        createReqVO.setInSubjectRoom("inSubjectRoom");
        createReqVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockMessageApi.getMessage(0L)).thenReturn(0L);

        // Configure PersonnalInformationController.getPersonnal(...).
        final PersonnalGetRespVO personnalGetRespVO = new PersonnalGetRespVO();
        final PersonnalBasicGetVO basicVO = new PersonnalBasicGetVO();
        basicVO.setDepartment(0L);
        basicVO.setId(0L);
        personnalGetRespVO.setBasicVO(basicVO);
        final PersonnalPositionGetVO positionVO = new PersonnalPositionGetVO();
        positionVO.setAdministrativePositionName("administrativePositionName");
        personnalGetRespVO.setPositionVO(positionVO);
        final CommonResult<PersonnalGetRespVO> result1 = CommonResult.success(personnalGetRespVO);
        when(mockPersonnalInformationController.getPersonnal(0L)).thenReturn(result1);

        when(mockTenantApi.getTenantCodeByUserId(0L)).thenReturn(CommonResult.success("value"));

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalTransferMapper.insert(PersonnalTransferDO.builder()
                .id(0L)
                .build())).thenReturn(0);

        // Run the test
        final Long result = personnalTransferServiceImplUnderTest.createPersonnalTransfer(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockAdminUserApi).updateDept(0L, 0L);

        // Confirm MessageApi.updateMessage(...).
        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
        updateReqDTO.setDataScopeIds("dataScopeIds");
        updateReqDTO.setDataScopeNames("dataScopeNames");
        updateReqDTO.setUserId(0L);
        updateReqDTO.setId(0L);
        updateReqDTO.setStatus(0);
        verify(mockMessageApi).updateMessage(updateReqDTO);
        verify(mockAdminUserApi).updateUserDeptBatch(0L, Arrays.asList(0L));
        verify(mockPersonnalMapper).update(eq(PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build()), any(LambdaUpdateWrapper.class));
        verify(mockPersonnalPositionMapper).update(eq(PersonnalPositionDO.builder()
                .personnalId(0L)
                .rank(0)
                .administrativePositionName("administrativePositionName")
                .administrativePositionRank(0)
                .build()), any(LambdaUpdateWrapper.class));

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setPeronClassification(0);
        personInfo.setName("name");
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        verify(mockPersonProducer).sendPersonData(false, personInfo, 0L);
    }

    @Test
    void testCreatePersonnalTransfer_MessageApiGetMessageReturnsNull() {
        // Setup
        final PersonnalCreateTransferReqVO createReqVO = new PersonnalCreateTransferReqVO();
        createReqVO.setId(0L);
        createReqVO.setUserId(0L);
        createReqVO.setOutDeptIds("outDeptIds");
        createReqVO.setInDeptIds("inDeptIds");
        createReqVO.setInRank(0);
        createReqVO.setInAdministrativePositionName("administrativePositionName");
        createReqVO.setInAdministrativePositionRank(0);
        createReqVO.setInSubjectRoom("inSubjectRoom");
        createReqVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockMessageApi.getMessage(0L)).thenReturn(null);

        // Configure PersonnalInformationController.getPersonnal(...).
        final PersonnalGetRespVO personnalGetRespVO = new PersonnalGetRespVO();
        final PersonnalBasicGetVO basicVO = new PersonnalBasicGetVO();
        basicVO.setDepartment(0L);
        basicVO.setId(0L);
        personnalGetRespVO.setBasicVO(basicVO);
        final PersonnalPositionGetVO positionVO = new PersonnalPositionGetVO();
        positionVO.setAdministrativePositionName("administrativePositionName");
        personnalGetRespVO.setPositionVO(positionVO);
        final CommonResult<PersonnalGetRespVO> result1 = CommonResult.success(personnalGetRespVO);
        when(mockPersonnalInformationController.getPersonnal(0L)).thenReturn(result1);

        when(mockTenantApi.getTenantCodeByUserId(0L)).thenReturn(CommonResult.success("value"));

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalTransferMapper.insert(PersonnalTransferDO.builder()
                .id(0L)
                .build())).thenReturn(0);

        // Run the test
        final Long result = personnalTransferServiceImplUnderTest.createPersonnalTransfer(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockAdminUserApi).updateDept(0L, 0L);
        verify(mockAdminUserApi).updateUserDeptBatch(0L, Arrays.asList(0L));
        verify(mockPersonnalMapper).update(eq(PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build()), any(LambdaUpdateWrapper.class));
        verify(mockPersonnalPositionMapper).update(eq(PersonnalPositionDO.builder()
                .personnalId(0L)
                .rank(0)
                .administrativePositionName("administrativePositionName")
                .administrativePositionRank(0)
                .build()), any(LambdaUpdateWrapper.class));

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setPeronClassification(0);
        personInfo.setName("name");
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        verify(mockPersonProducer).sendPersonData(false, personInfo, 0L);
    }

    @Test
    void testCreatePersonnalTransfer_PersonnalInformationControllerReturnsError() {
        // Setup
        final PersonnalCreateTransferReqVO createReqVO = new PersonnalCreateTransferReqVO();
        createReqVO.setId(0L);
        createReqVO.setUserId(0L);
        createReqVO.setOutDeptIds("outDeptIds");
        createReqVO.setInDeptIds("inDeptIds");
        createReqVO.setInRank(0);
        createReqVO.setInAdministrativePositionName("administrativePositionName");
        createReqVO.setInAdministrativePositionRank(0);
        createReqVO.setInSubjectRoom("inSubjectRoom");
        createReqVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockMessageApi.getMessage(0L)).thenReturn(0L);

        // Configure PersonnalInformationController.getPersonnal(...).
        final CommonResult<PersonnalGetRespVO> result1 = CommonResult.error(new ServiceException(0, "message"));
        when(mockPersonnalInformationController.getPersonnal(0L)).thenReturn(result1);

        when(mockTenantApi.getTenantCodeByUserId(0L)).thenReturn(CommonResult.success("value"));

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalTransferMapper.insert(PersonnalTransferDO.builder()
                .id(0L)
                .build())).thenReturn(0);

        // Run the test
        final Long result = personnalTransferServiceImplUnderTest.createPersonnalTransfer(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockAdminUserApi).updateDept(0L, 0L);

        // Confirm MessageApi.updateMessage(...).
        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
        updateReqDTO.setDataScopeIds("dataScopeIds");
        updateReqDTO.setDataScopeNames("dataScopeNames");
        updateReqDTO.setUserId(0L);
        updateReqDTO.setId(0L);
        updateReqDTO.setStatus(0);
        verify(mockMessageApi).updateMessage(updateReqDTO);
        verify(mockAdminUserApi).updateUserDeptBatch(0L, Arrays.asList(0L));
        verify(mockPersonnalMapper).update(eq(PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build()), any(LambdaUpdateWrapper.class));
        verify(mockPersonnalPositionMapper).update(eq(PersonnalPositionDO.builder()
                .personnalId(0L)
                .rank(0)
                .administrativePositionName("administrativePositionName")
                .administrativePositionRank(0)
                .build()), any(LambdaUpdateWrapper.class));

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setPeronClassification(0);
        personInfo.setName("name");
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        verify(mockPersonProducer).sendPersonData(false, personInfo, 0L);
    }

    @Test
    void testCreatePersonnalTransfer_TenantApiReturnsError() {
        // Setup
        final PersonnalCreateTransferReqVO createReqVO = new PersonnalCreateTransferReqVO();
        createReqVO.setId(0L);
        createReqVO.setUserId(0L);
        createReqVO.setOutDeptIds("outDeptIds");
        createReqVO.setInDeptIds("inDeptIds");
        createReqVO.setInRank(0);
        createReqVO.setInAdministrativePositionName("administrativePositionName");
        createReqVO.setInAdministrativePositionRank(0);
        createReqVO.setInSubjectRoom("inSubjectRoom");
        createReqVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockMessageApi.getMessage(0L)).thenReturn(0L);

        // Configure PersonnalInformationController.getPersonnal(...).
        final PersonnalGetRespVO personnalGetRespVO = new PersonnalGetRespVO();
        final PersonnalBasicGetVO basicVO = new PersonnalBasicGetVO();
        basicVO.setDepartment(0L);
        basicVO.setId(0L);
        personnalGetRespVO.setBasicVO(basicVO);
        final PersonnalPositionGetVO positionVO = new PersonnalPositionGetVO();
        positionVO.setAdministrativePositionName("administrativePositionName");
        personnalGetRespVO.setPositionVO(positionVO);
        final CommonResult<PersonnalGetRespVO> result1 = CommonResult.success(personnalGetRespVO);
        when(mockPersonnalInformationController.getPersonnal(0L)).thenReturn(result1);

        when(mockTenantApi.getTenantCodeByUserId(0L))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalTransferMapper.insert(PersonnalTransferDO.builder()
                .id(0L)
                .build())).thenReturn(0);

        // Run the test
        final Long result = personnalTransferServiceImplUnderTest.createPersonnalTransfer(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockAdminUserApi).updateDept(0L, 0L);

        // Confirm MessageApi.updateMessage(...).
        final MessageAuthorityUpdateReqDTO updateReqDTO = new MessageAuthorityUpdateReqDTO();
        updateReqDTO.setDataScopeIds("dataScopeIds");
        updateReqDTO.setDataScopeNames("dataScopeNames");
        updateReqDTO.setUserId(0L);
        updateReqDTO.setId(0L);
        updateReqDTO.setStatus(0);
        verify(mockMessageApi).updateMessage(updateReqDTO);
        verify(mockAdminUserApi).updateUserDeptBatch(0L, Arrays.asList(0L));
        verify(mockPersonnalMapper).update(eq(PersonnalBasicDO.builder()
                .department(0L)
                .subjectRoom("subjectRoom")
                .userId(0L)
                .build()), any(LambdaUpdateWrapper.class));
        verify(mockPersonnalPositionMapper).update(eq(PersonnalPositionDO.builder()
                .personnalId(0L)
                .rank(0)
                .administrativePositionName("administrativePositionName")
                .administrativePositionRank(0)
                .build()), any(LambdaUpdateWrapper.class));

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setPeronClassification(0);
        personInfo.setName("name");
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        verify(mockPersonProducer).sendPersonData(false, personInfo, 0L);
    }

    @Test
    void testGetPersonnalTransferPage() {
        // Setup
        final PersonnalTransferPageReqVO pageVO = new PersonnalTransferPageReqVO();
        pageVO.setId(0L);
        pageVO.setName("name");
        pageVO.setUserId("userId");
        pageVO.setOutDeptIds("outDeptIds");
        pageVO.setInDeptIds("inDeptIds");

        final PersonnalTransferPageRespVO personnalTransferPageRespVO = new PersonnalTransferPageRespVO();
        personnalTransferPageRespVO.setId(0L);
        personnalTransferPageRespVO.setUserId(0L);
        personnalTransferPageRespVO.setName("name");
        personnalTransferPageRespVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferPageRespVO.setOutDeptNames("outDeptNames");
        final PageResult<PersonnalTransferPageRespVO> expectedResult = new PageResult<>(
                Arrays.asList(personnalTransferPageRespVO), 0L);

        // Configure PersonnalTransferMapper.selectPage(...).
        final PersonnalTransferPageRespVO personnalTransferPageRespVO1 = new PersonnalTransferPageRespVO();
        personnalTransferPageRespVO1.setId(0L);
        personnalTransferPageRespVO1.setUserId(0L);
        personnalTransferPageRespVO1.setName("name");
        personnalTransferPageRespVO1.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferPageRespVO1.setOutDeptNames("outDeptNames");
        final List<PersonnalTransferPageRespVO> personnalTransferPageRespVOS = Arrays.asList(
                personnalTransferPageRespVO1);
        final PersonnalTransferPageReqVO param = new PersonnalTransferPageReqVO();
        param.setId(0L);
        param.setName("name");
        param.setUserId("userId");
        param.setOutDeptIds("outDeptIds");
        param.setInDeptIds("inDeptIds");
        when(mockPersonnalTransferMapper.selectPage(any(IPage.class), eq(param), eq(0L)))
                .thenReturn(personnalTransferPageRespVOS);

        // Run the test
        final PageResult<PersonnalTransferPageRespVO> result = personnalTransferServiceImplUnderTest.getPersonnalTransferPage(
                pageVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferPage_PersonnalTransferMapperReturnsNoItems() {
        // Setup
        final PersonnalTransferPageReqVO pageVO = new PersonnalTransferPageReqVO();
        pageVO.setId(0L);
        pageVO.setName("name");
        pageVO.setUserId("userId");
        pageVO.setOutDeptIds("outDeptIds");
        pageVO.setInDeptIds("inDeptIds");

        final PersonnalTransferPageRespVO personnalTransferPageRespVO = new PersonnalTransferPageRespVO();
        personnalTransferPageRespVO.setId(0L);
        personnalTransferPageRespVO.setUserId(0L);
        personnalTransferPageRespVO.setName("name");
        personnalTransferPageRespVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferPageRespVO.setOutDeptNames("outDeptNames");
        final PageResult<PersonnalTransferPageRespVO> expectedResult = new PageResult<>(
                Arrays.asList(personnalTransferPageRespVO), 0L);

        // Configure PersonnalTransferMapper.selectPage(...).
        final PersonnalTransferPageReqVO param = new PersonnalTransferPageReqVO();
        param.setId(0L);
        param.setName("name");
        param.setUserId("userId");
        param.setOutDeptIds("outDeptIds");
        param.setInDeptIds("inDeptIds");
        when(mockPersonnalTransferMapper.selectPage(any(IPage.class), eq(param), eq(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<PersonnalTransferPageRespVO> result = personnalTransferServiceImplUnderTest.getPersonnalTransferPage(
                pageVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferExcel() {
        // Setup
        final PersonnalTransferPageReqVO pageVO = new PersonnalTransferPageReqVO();
        pageVO.setId(0L);
        pageVO.setName("name");
        pageVO.setUserId("userId");
        pageVO.setOutDeptIds("outDeptIds");
        pageVO.setInDeptIds("inDeptIds");

        final PersonnalTransferExcelVO personnalTransferExcelVO = new PersonnalTransferExcelVO();
        personnalTransferExcelVO.setUserId(0L);
        personnalTransferExcelVO.setName("name");
        personnalTransferExcelVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferExcelVO.setOutDeptNames("outDeptNames");
        personnalTransferExcelVO.setOutSubjectRoom("outSubjectRoom");
        final List<PersonnalTransferExcelVO> expectedResult = Arrays.asList(personnalTransferExcelVO);

        // Configure PersonnalTransferMapper.selectList(...).
        final PersonnalTransferExcelVO personnalTransferExcelVO1 = new PersonnalTransferExcelVO();
        personnalTransferExcelVO1.setUserId(0L);
        personnalTransferExcelVO1.setName("name");
        personnalTransferExcelVO1.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferExcelVO1.setOutDeptNames("outDeptNames");
        personnalTransferExcelVO1.setOutSubjectRoom("outSubjectRoom");
        final List<PersonnalTransferExcelVO> personnalTransferExcelVOS = Arrays.asList(personnalTransferExcelVO1);
        final PersonnalTransferPageReqVO param = new PersonnalTransferPageReqVO();
        param.setId(0L);
        param.setName("name");
        param.setUserId("userId");
        param.setOutDeptIds("outDeptIds");
        param.setInDeptIds("inDeptIds");
        when(mockPersonnalTransferMapper.selectList(param, 0L)).thenReturn(personnalTransferExcelVOS);

        // Run the test
        final List<PersonnalTransferExcelVO> result = personnalTransferServiceImplUnderTest.getPersonnalTransferExcel(
                pageVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferExcel_PersonnalTransferMapperReturnsNoItems() {
        // Setup
        final PersonnalTransferPageReqVO pageVO = new PersonnalTransferPageReqVO();
        pageVO.setId(0L);
        pageVO.setName("name");
        pageVO.setUserId("userId");
        pageVO.setOutDeptIds("outDeptIds");
        pageVO.setInDeptIds("inDeptIds");

        // Configure PersonnalTransferMapper.selectList(...).
        final PersonnalTransferPageReqVO param = new PersonnalTransferPageReqVO();
        param.setId(0L);
        param.setName("name");
        param.setUserId("userId");
        param.setOutDeptIds("outDeptIds");
        param.setInDeptIds("inDeptIds");
        when(mockPersonnalTransferMapper.selectList(param, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PersonnalTransferExcelVO> result = personnalTransferServiceImplUnderTest.getPersonnalTransferExcel(
                pageVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetPersonnalTransferByUser() {
        // Setup
        final PersonnalTransferPageRespVO personnalTransferPageRespVO = new PersonnalTransferPageRespVO();
        personnalTransferPageRespVO.setId(0L);
        personnalTransferPageRespVO.setUserId(0L);
        personnalTransferPageRespVO.setName("name");
        personnalTransferPageRespVO.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferPageRespVO.setOutDeptNames("outDeptNames");
        final List<PersonnalTransferPageRespVO> expectedResult = Arrays.asList(personnalTransferPageRespVO);

        // Configure PersonnalTransferMapper.selectListByUser(...).
        final PersonnalTransferPageRespVO personnalTransferPageRespVO1 = new PersonnalTransferPageRespVO();
        personnalTransferPageRespVO1.setId(0L);
        personnalTransferPageRespVO1.setUserId(0L);
        personnalTransferPageRespVO1.setName("name");
        personnalTransferPageRespVO1.setTransferTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalTransferPageRespVO1.setOutDeptNames("outDeptNames");
        final List<PersonnalTransferPageRespVO> personnalTransferPageRespVOS = Arrays.asList(
                personnalTransferPageRespVO1);
        when(mockPersonnalTransferMapper.selectListByUser("userId", 0L)).thenReturn(personnalTransferPageRespVOS);

        // Run the test
        final List<PersonnalTransferPageRespVO> result = personnalTransferServiceImplUnderTest.getPersonnalTransferByUser(
                "userId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferByUser_PersonnalTransferMapperReturnsNoItems() {
        // Setup
        when(mockPersonnalTransferMapper.selectListByUser("userId", 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PersonnalTransferPageRespVO> result = personnalTransferServiceImplUnderTest.getPersonnalTransferByUser(
                "userId");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetPersonnalTransferId() {
        // Setup
        final PersonnalTransferDetailResqVO expectedResult = new PersonnalTransferDetailResqVO();
        expectedResult.setUserId(0L);
        expectedResult.setName("name");
        expectedResult.setOutDeptIds("outDeptIds");
        expectedResult.setOutDeptNames("outDeptNames");
        expectedResult.setOutRank(0);

        // Configure PersonnalTransferMapper.selectByPersonnalId(...).
        final PersonnalTransferDO personnalTransferDO = PersonnalTransferDO.builder()
                .id(0L)
                .build();
        when(mockPersonnalTransferMapper.selectByPersonnalId(0L)).thenReturn(personnalTransferDO);

        // Run the test
        final PersonnalTransferDetailResqVO result = personnalTransferServiceImplUnderTest.getPersonnalTransferId(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferOut() {
        // Setup
        final PersonnalTransferOutVO expectedResult = new PersonnalTransferOutVO();
        expectedResult.setId(0L);
        expectedResult.setName("name");
        expectedResult.setUserId("userId");
        expectedResult.setDeptId("deptId");
        expectedResult.setDeptName("deptName");

        // Configure PersonnalTransferMapper.selectPersonnalTransferOut(...).
        final PersonnalTransferOutVO personnalTransferOutVO = new PersonnalTransferOutVO();
        personnalTransferOutVO.setId(0L);
        personnalTransferOutVO.setName("name");
        personnalTransferOutVO.setUserId("userId");
        personnalTransferOutVO.setDeptId("deptId");
        personnalTransferOutVO.setDeptName("deptName");
        when(mockPersonnalTransferMapper.selectPersonnalTransferOut("userId")).thenReturn(personnalTransferOutVO);

        // Configure AdminUserApi.getDeptDetailList(...).
        final HrDeptDTO hrDeptDTO = new HrDeptDTO();
        hrDeptDTO.setDeptId(0L);
        hrDeptDTO.setName("name");
        final CommonResult<List<HrDeptDTO>> listCommonResult = CommonResult.success(Arrays.asList(hrDeptDTO));
        when(mockAdminUserApi.getDeptDetailList(0L)).thenReturn(listCommonResult);

        // Run the test
        final PersonnalTransferOutVO result = personnalTransferServiceImplUnderTest.getPersonnalTransferOut("userId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferOut_AdminUserApiReturnsNoItems() {
        // Setup
        final PersonnalTransferOutVO expectedResult = new PersonnalTransferOutVO();
        expectedResult.setId(0L);
        expectedResult.setName("name");
        expectedResult.setUserId("userId");
        expectedResult.setDeptId("deptId");
        expectedResult.setDeptName("deptName");

        // Configure PersonnalTransferMapper.selectPersonnalTransferOut(...).
        final PersonnalTransferOutVO personnalTransferOutVO = new PersonnalTransferOutVO();
        personnalTransferOutVO.setId(0L);
        personnalTransferOutVO.setName("name");
        personnalTransferOutVO.setUserId("userId");
        personnalTransferOutVO.setDeptId("deptId");
        personnalTransferOutVO.setDeptName("deptName");
        when(mockPersonnalTransferMapper.selectPersonnalTransferOut("userId")).thenReturn(personnalTransferOutVO);

        // Configure AdminUserApi.getDeptDetailList(...).
        final CommonResult<List<HrDeptDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockAdminUserApi.getDeptDetailList(0L)).thenReturn(listCommonResult);

        // Run the test
        final PersonnalTransferOutVO result = personnalTransferServiceImplUnderTest.getPersonnalTransferOut("userId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnalTransferOut_AdminUserApiReturnsError() {
        // Setup
        final PersonnalTransferOutVO expectedResult = new PersonnalTransferOutVO();
        expectedResult.setId(0L);
        expectedResult.setName("name");
        expectedResult.setUserId("userId");
        expectedResult.setDeptId("deptId");
        expectedResult.setDeptName("deptName");

        // Configure PersonnalTransferMapper.selectPersonnalTransferOut(...).
        final PersonnalTransferOutVO personnalTransferOutVO = new PersonnalTransferOutVO();
        personnalTransferOutVO.setId(0L);
        personnalTransferOutVO.setName("name");
        personnalTransferOutVO.setUserId("userId");
        personnalTransferOutVO.setDeptId("deptId");
        personnalTransferOutVO.setDeptName("deptName");
        when(mockPersonnalTransferMapper.selectPersonnalTransferOut("userId")).thenReturn(personnalTransferOutVO);

        // Configure AdminUserApi.getDeptDetailList(...).
        final CommonResult<List<HrDeptDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
        when(mockAdminUserApi.getDeptDetailList(0L)).thenReturn(listCommonResult);

        // Run the test
        final PersonnalTransferOutVO result = personnalTransferServiceImplUnderTest.getPersonnalTransferOut("userId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
