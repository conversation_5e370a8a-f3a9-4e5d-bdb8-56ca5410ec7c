package com.unicom.swdx.module.hr.service.personnal;

import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalStudyMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PersonnalStudyServiceImplTest {

    @Mock
    private PersonnalStudyMapper mockPersonnalStudyMapper;

    @InjectMocks
    private PersonnalStudyServiceImpl personnalStudyServiceImplUnderTest;

    @Test
    void testCreatePersonnal() {
        // Setup
        final PersonnalStudyVO createReqVO = new PersonnalStudyVO();
        createReqVO.setGraduationSchool("graduationSchool");
        createReqVO.setAdmissionDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO.setEndStudyDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO.setEducation(0);
        createReqVO.setAcademicDegree(0);

        // Run the test
        personnalStudyServiceImplUnderTest.createPersonnal(createReqVO, 0L);

        // Verify the results
        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
                .personnalId(0L)
                .build());
    }

    @Test
    void testCreatePersonnalRegis() {
        // Setup
        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
        createReqVO.setName("name");
        createReqVO.setIdType(0);
        createReqVO.setIdNumber("idNumber");
        createReqVO.setGender(0);
        createReqVO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        personnalStudyServiceImplUnderTest.createPersonnalRegis(createReqVO, 0L);

        // Verify the results
        verify(mockPersonnalStudyMapper).insert(PersonnalStudyDO.builder()
                .personnalId(0L)
                .build());
    }

    @Test
    void testGetPersonnal() {
        // Setup
        final PersonnalStudyDO expectedResult = PersonnalStudyDO.builder()
                .personnalId(0L)
                .build();

        // Configure PersonnalStudyMapper.selectByPersonnalId(...).
        final PersonnalStudyDO personnalStudyDO = PersonnalStudyDO.builder()
                .personnalId(0L)
                .build();
        when(mockPersonnalStudyMapper.selectByPersonnalId(0L)).thenReturn(personnalStudyDO);

        // Run the test
        final PersonnalStudyDO result = personnalStudyServiceImplUnderTest.getPersonnal(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testUpdatePersonnal() {
        // Setup
        final PersonnalStudyGetVO updateReqVO = new PersonnalStudyGetVO();
        updateReqVO.setId(0L);
        updateReqVO.setPersonnalId(0L);

        // Run the test
        personnalStudyServiceImplUnderTest.updatePersonnal(updateReqVO);

        // Verify the results
        verify(mockPersonnalStudyMapper).updateByPersonnalId(PersonnalStudyDO.builder()
                .personnalId(0L)
                .build());
    }
}
