package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentBatchManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentBatchManagementVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitmentBatchManagementMapper;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitmentPositionManagementMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RecruitmentBatchManagementServiceImplTest {

    @Mock
    private RecruitmentBatchManagementMapper mockRecruitmentBatchManagementMapper;
    @Mock
    private RecruitmentPositionManagementMapper mockRecruitmentPositionManagementMapper;

    @InjectMocks
    private RecruitmentBatchManagementServiceImpl recruitmentBatchManagementServiceImplUnderTest;

    @Test
    void testQueryByList() {
        // Setup
        final RecruitmentBatchManagementVO recruitmentBatchManagementVO = new RecruitmentBatchManagementVO();
        recruitmentBatchManagementVO.setId(0);
        recruitmentBatchManagementVO.setBatchName("batchName");
        recruitmentBatchManagementVO.setBatchClass("batchClass");
        recruitmentBatchManagementVO.setIsMobile(0);
        recruitmentBatchManagementVO.setStatus("status");

        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");
        final PageResult<RecruitmentBatchManagement> expectedResult = new PageResult<>(
                Arrays.asList(recruitmentBatchManagement), 0L);

        // Configure RecruitmentBatchManagementMapper.queryByList(...).
        final RecruitmentBatchManagement recruitmentBatchManagement1 = new RecruitmentBatchManagement();
        recruitmentBatchManagement1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement1.setId(0);
        recruitmentBatchManagement1.setBatchName("batchName");
        recruitmentBatchManagement1.setBatchClass("batchClass");
        recruitmentBatchManagement1.setStatus("status");
        final List<RecruitmentBatchManagement> recruitmentBatchManagements = Arrays.asList(recruitmentBatchManagement1);
        final RecruitmentBatchManagementVO recruitmentBatchManagementVO1 = new RecruitmentBatchManagementVO();
        recruitmentBatchManagementVO1.setId(0);
        recruitmentBatchManagementVO1.setBatchName("batchName");
        recruitmentBatchManagementVO1.setBatchClass("batchClass");
        recruitmentBatchManagementVO1.setIsMobile(0);
        recruitmentBatchManagementVO1.setStatus("status");
        when(mockRecruitmentBatchManagementMapper.queryByList(any(IPage.class),
                eq(recruitmentBatchManagementVO1))).thenReturn(recruitmentBatchManagements);

        // Run the test
        final PageResult<RecruitmentBatchManagement> result = recruitmentBatchManagementServiceImplUnderTest.queryByList(
                recruitmentBatchManagementVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryByList_RecruitmentBatchManagementMapperReturnsNoItems() {
        // Setup
        final RecruitmentBatchManagementVO recruitmentBatchManagementVO = new RecruitmentBatchManagementVO();
        recruitmentBatchManagementVO.setId(0);
        recruitmentBatchManagementVO.setBatchName("batchName");
        recruitmentBatchManagementVO.setBatchClass("batchClass");
        recruitmentBatchManagementVO.setIsMobile(0);
        recruitmentBatchManagementVO.setStatus("status");

        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");
        final PageResult<RecruitmentBatchManagement> expectedResult = new PageResult<>(
                Arrays.asList(recruitmentBatchManagement), 0L);

        // Configure RecruitmentBatchManagementMapper.queryByList(...).
        final RecruitmentBatchManagementVO recruitmentBatchManagementVO1 = new RecruitmentBatchManagementVO();
        recruitmentBatchManagementVO1.setId(0);
        recruitmentBatchManagementVO1.setBatchName("batchName");
        recruitmentBatchManagementVO1.setBatchClass("batchClass");
        recruitmentBatchManagementVO1.setIsMobile(0);
        recruitmentBatchManagementVO1.setStatus("status");
        when(mockRecruitmentBatchManagementMapper.queryByList(any(IPage.class),
                eq(recruitmentBatchManagementVO1))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitmentBatchManagement> result = recruitmentBatchManagementServiceImplUnderTest.queryByList(
                recruitmentBatchManagementVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryAll() {
        // Setup
        final RecruitmentBatchManagementVO recruitmentBatchManagementVO = new RecruitmentBatchManagementVO();
        recruitmentBatchManagementVO.setId(0);
        recruitmentBatchManagementVO.setBatchName("batchName");
        recruitmentBatchManagementVO.setBatchClass("batchClass");
        recruitmentBatchManagementVO.setIsMobile(0);
        recruitmentBatchManagementVO.setStatus("status");

        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");
        final List<RecruitmentBatchManagement> expectedResult = Arrays.asList(recruitmentBatchManagement);

        // Run the test
        final List<RecruitmentBatchManagement> result = recruitmentBatchManagementServiceImplUnderTest.queryAll(
                recruitmentBatchManagementVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testAdd() {
        // Setup
        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");

        when(mockRecruitmentBatchManagementMapper.deRepeat("batchName")).thenReturn(0);

        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitmentBatchManagementServiceImplUnderTest.add(
                recruitmentBatchManagement);

        // Verify the results
        // Confirm RecruitmentBatchManagementMapper.insert(...).
        final RecruitmentBatchManagement entity = new RecruitmentBatchManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setBatchName("batchName");
        entity.setBatchClass("batchClass");
        entity.setStatus("status");
        verify(mockRecruitmentBatchManagementMapper).insert(entity);
    }

    @Test
    void testEdit() {
        // Setup
        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");

        when(mockRecruitmentBatchManagementMapper.deRepeatEdit("batchName", 0)).thenReturn(0);

        // Run the test
        final ResponseEntity<Map<String, Object>> result = recruitmentBatchManagementServiceImplUnderTest.edit(
                recruitmentBatchManagement);

        // Verify the results
        // Confirm RecruitmentBatchManagementMapper.updateById(...).
        final RecruitmentBatchManagement entity = new RecruitmentBatchManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setBatchName("batchName");
        entity.setBatchClass("batchClass");
        entity.setStatus("status");
        verify(mockRecruitmentBatchManagementMapper).updateById(entity);
    }

    @Test
    void testDelete() {
        // Setup
        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");

        final ResponseEntity<String> expectedResult = new ResponseEntity<>("body", HttpStatus.OK);

        // Run the test
        final ResponseEntity<String> result = recruitmentBatchManagementServiceImplUnderTest.delete(
                recruitmentBatchManagement);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm RecruitmentBatchManagementMapper.deleteById(...).
        final RecruitmentBatchManagement entity = new RecruitmentBatchManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setBatchName("batchName");
        entity.setBatchClass("batchClass");
        entity.setStatus("status");
        verify(mockRecruitmentBatchManagementMapper).deleteById(entity);
        verify(mockRecruitmentPositionManagementMapper).delete(any(QueryWrapper.class));
    }

    @Test
    void testEnable() {
        // Setup
        final RecruitmentBatchManagement recruitmentBatchManagement = new RecruitmentBatchManagement();
        recruitmentBatchManagement.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitmentBatchManagement.setId(0);
        recruitmentBatchManagement.setBatchName("batchName");
        recruitmentBatchManagement.setBatchClass("batchClass");
        recruitmentBatchManagement.setStatus("status");

        final ResponseEntity<String> expectedResult = new ResponseEntity<>("body", HttpStatus.OK);

        // Run the test
        final ResponseEntity<String> result = recruitmentBatchManagementServiceImplUnderTest.enable(
                recruitmentBatchManagement);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm RecruitmentBatchManagementMapper.updateById(...).
        final RecruitmentBatchManagement entity = new RecruitmentBatchManagement();
        entity.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setId(0);
        entity.setBatchName("batchName");
        entity.setBatchClass("batchClass");
        entity.setStatus("status");
        verify(mockRecruitmentBatchManagementMapper).updateById(entity);
    }
}
