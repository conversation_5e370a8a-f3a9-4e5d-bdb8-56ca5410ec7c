package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitWorkExp;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitWorkExpVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitWorkExpMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RecruitWorkExpServiceImplTest {

    @Mock
    private RecruitWorkExpMapper mockRecruitWorkExpMapper;

    private RecruitWorkExpServiceImpl recruitWorkExpServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        recruitWorkExpServiceImplUnderTest = new RecruitWorkExpServiceImpl();
        recruitWorkExpServiceImplUnderTest.recruitWorkExpMapper = mockRecruitWorkExpMapper;
    }

    @Test
    void testQueryByList() {
        // Setup
        final RecruitWorkExpVO recruitWorkExpVO = new RecruitWorkExpVO();
        recruitWorkExpVO.setId(0);
        recruitWorkExpVO.setBaseId(0);
        recruitWorkExpVO.setWorkUnit("workUnit");
        recruitWorkExpVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitWorkExpVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setId(0);
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        final PageResult<RecruitWorkExp> expectedResult = new PageResult<>(Arrays.asList(recruitWorkExp), 0L);

        // Configure RecruitWorkExpMapper.queryByList(...).
        final RecruitWorkExp recruitWorkExp1 = new RecruitWorkExp();
        recruitWorkExp1.setId(0);
        recruitWorkExp1.setBaseId(0);
        recruitWorkExp1.setWorkUnit("workUnit");
        recruitWorkExp1.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp1.setEndTime(LocalDate.of(2020, 1, 1));
        final List<RecruitWorkExp> recruitWorkExps = Arrays.asList(recruitWorkExp1);
        final RecruitWorkExpVO recruitWorkExpVO1 = new RecruitWorkExpVO();
        recruitWorkExpVO1.setId(0);
        recruitWorkExpVO1.setBaseId(0);
        recruitWorkExpVO1.setWorkUnit("workUnit");
        recruitWorkExpVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitWorkExpVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRecruitWorkExpMapper.queryByList(any(IPage.class), eq(recruitWorkExpVO1))).thenReturn(recruitWorkExps);

        // Run the test
        final PageResult<RecruitWorkExp> result = recruitWorkExpServiceImplUnderTest.queryByList(recruitWorkExpVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryByList_RecruitWorkExpMapperReturnsNoItems() {
        // Setup
        final RecruitWorkExpVO recruitWorkExpVO = new RecruitWorkExpVO();
        recruitWorkExpVO.setId(0);
        recruitWorkExpVO.setBaseId(0);
        recruitWorkExpVO.setWorkUnit("workUnit");
        recruitWorkExpVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitWorkExpVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final RecruitWorkExp recruitWorkExp = new RecruitWorkExp();
        recruitWorkExp.setId(0);
        recruitWorkExp.setBaseId(0);
        recruitWorkExp.setWorkUnit("workUnit");
        recruitWorkExp.setStartTime(LocalDate.of(2020, 1, 1));
        recruitWorkExp.setEndTime(LocalDate.of(2020, 1, 1));
        final PageResult<RecruitWorkExp> expectedResult = new PageResult<>(Arrays.asList(recruitWorkExp), 0L);

        // Configure RecruitWorkExpMapper.queryByList(...).
        final RecruitWorkExpVO recruitWorkExpVO1 = new RecruitWorkExpVO();
        recruitWorkExpVO1.setId(0);
        recruitWorkExpVO1.setBaseId(0);
        recruitWorkExpVO1.setWorkUnit("workUnit");
        recruitWorkExpVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recruitWorkExpVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRecruitWorkExpMapper.queryByList(any(IPage.class), eq(recruitWorkExpVO1)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<RecruitWorkExp> result = recruitWorkExpServiceImplUnderTest.queryByList(recruitWorkExpVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
