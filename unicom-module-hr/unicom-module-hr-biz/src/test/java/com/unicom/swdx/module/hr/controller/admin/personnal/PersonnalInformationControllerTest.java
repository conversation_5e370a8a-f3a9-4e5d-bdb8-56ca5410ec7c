package com.unicom.swdx.module.hr.controller.admin.personnal;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.api.dto.PersonDTO;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalMapper;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.hr.service.personnal.*;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@ExtendWith(SpringExtension.class)
@WebMvcTest(PersonnalInformationController.class)
class PersonnalInformationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PersonnalService mockPersonnalService;
    @MockBean
    private PersonnalStudyService mockPersonnalStudyService;
    @MockBean
    private PersonnalPositionService mockPersonnalPositionService;
    @MockBean
    private PersonnalWorkService mockPersonnalWorkService;
    @MockBean
    private PersonnalPartyService mockPersonnalPartyService;
    @MockBean
    private PersonnalMapper mockPersonnalMapper;
    @MockBean
    private PersonProducer mockPersonProducer;
    @MockBean
    private TenantApi mockTenantApi;
    @MockBean
    private AdminUserApi mockAdminUserApi;
    @MockBean
    private DeptApi mockDeptApi;

    @Test
    void testCreatePersonnal() throws Exception {
        // Setup
        // Configure PersonnalService.createPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
        createReqVO.setName("name");
        createReqVO.setIdType(0);
        createReqVO.setIdNumber("idNumber");
        createReqVO.setGender(0);
        createReqVO.setPhoto("photo");
        when(mockPersonnalService.createPersonnal(createReqVO, 0, "administrativePositionName", 0))
                .thenReturn(personnalBasicDO);

        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.success("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalStudyService.createPersonnal(...).
        final PersonnalStudyVO createReqVO1 = new PersonnalStudyVO();
        createReqVO1.setGraduationSchool("graduationSchool");
        createReqVO1.setAdmissionDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO1.setEndStudyDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO1.setEducation(0);
        createReqVO1.setAcademicDegree(0);
        verify(mockPersonnalStudyService).createPersonnal(createReqVO1, 0L);

        // Confirm PersonnalWorkService.createPersonnal(...).
        final PersonnalWorkVO personnalWorkVO = new PersonnalWorkVO();
        personnalWorkVO.setWorkUnit("workUnit");
        personnalWorkVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalWorkVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalWorkVO.setPosition("position");
        personnalWorkVO.setWorkContent("workContent");
        final List<PersonnalWorkVO> createReqVO2 = Arrays.asList(personnalWorkVO);
        verify(mockPersonnalWorkService).createPersonnal(createReqVO2, 0L);

        // Confirm PersonnalPositionService.createPersonnal(...).
        final PersonnalPositionVO createReqVO3 = new PersonnalPositionVO();
        createReqVO3.setRank(0);
        createReqVO3.setAdministrativePositionName("administrativePositionName");
        createReqVO3.setAdministrativePositionRank(0);
        createReqVO3.setProfessionalTechnicalName(0);
        createReqVO3.setProfessionalTechnicalRank(0);
        verify(mockPersonnalPositionService).createPersonnal(createReqVO3, 0L);

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setDepartment(0L);
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        personInfo.setTenantId(0L);
        verify(mockPersonProducer).sendPersonData(true, personInfo, 0L);
    }

    @Test
    void testCreatePersonnal_TenantApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalService.createPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        final PersonnalBasicVO createReqVO = new PersonnalBasicVO();
        createReqVO.setName("name");
        createReqVO.setIdType(0);
        createReqVO.setIdNumber("idNumber");
        createReqVO.setGender(0);
        createReqVO.setPhoto("photo");
        when(mockPersonnalService.createPersonnal(createReqVO, 0, "administrativePositionName", 0))
                .thenReturn(personnalBasicDO);

        when(mockTenantApi.getTenantCodeById(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalStudyService.createPersonnal(...).
        final PersonnalStudyVO createReqVO1 = new PersonnalStudyVO();
        createReqVO1.setGraduationSchool("graduationSchool");
        createReqVO1.setAdmissionDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO1.setEndStudyDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO1.setEducation(0);
        createReqVO1.setAcademicDegree(0);
        verify(mockPersonnalStudyService).createPersonnal(createReqVO1, 0L);

        // Confirm PersonnalWorkService.createPersonnal(...).
        final PersonnalWorkVO personnalWorkVO = new PersonnalWorkVO();
        personnalWorkVO.setWorkUnit("workUnit");
        personnalWorkVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalWorkVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalWorkVO.setPosition("position");
        personnalWorkVO.setWorkContent("workContent");
        final List<PersonnalWorkVO> createReqVO2 = Arrays.asList(personnalWorkVO);
        verify(mockPersonnalWorkService).createPersonnal(createReqVO2, 0L);

        // Confirm PersonnalPositionService.createPersonnal(...).
        final PersonnalPositionVO createReqVO3 = new PersonnalPositionVO();
        createReqVO3.setRank(0);
        createReqVO3.setAdministrativePositionName("administrativePositionName");
        createReqVO3.setAdministrativePositionRank(0);
        createReqVO3.setProfessionalTechnicalName(0);
        createReqVO3.setProfessionalTechnicalRank(0);
        verify(mockPersonnalPositionService).createPersonnal(createReqVO3, 0L);

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setDepartment(0L);
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        personInfo.setTenantId(0L);
        verify(mockPersonProducer).sendPersonData(true, personInfo, 0L);
    }

    @Test
    void testGetPersonnalPage() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalPage(...).
        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
        personnalPageRespVO.setId(0L);
        personnalPageRespVO.setName("name");
        personnalPageRespVO.setUserId("userId");
        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
        personnalPageRespVO.setWorkId("workId");
        final PageResult<PersonnalPageRespVO> personnalPageRespVOPageResult = new PageResult<>(
                Arrays.asList(personnalPageRespVO), 0L);
        final PersonnalPageReqVO pageReqVO = new PersonnalPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setWorkId("workId");
        pageReqVO.setGender(0);
        pageReqVO.setDepartment(0L);
        pageReqVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalPage(pageReqVO)).thenReturn(personnalPageRespVOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalPage_PersonnalServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalPage(...).
        final PersonnalPageReqVO pageReqVO = new PersonnalPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setWorkId("workId");
        pageReqVO.setGender(0);
        pageReqVO.setDepartment(0L);
        pageReqVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalPage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    void testGetPersonnalPageChildren() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalPageChildren(...).
        final PersonnalPageRespVO personnalPageRespVO = new PersonnalPageRespVO();
        personnalPageRespVO.setId(0L);
        personnalPageRespVO.setName("name");
        personnalPageRespVO.setUserId("userId");
        personnalPageRespVO.setDeptIds(Arrays.asList(0L));
        personnalPageRespVO.setWorkId("workId");
        final List<PersonnalPageRespVO> personnalPageRespVOS = Arrays.asList(personnalPageRespVO);
        final PersonnalChildrenReqVO pageVO = new PersonnalChildrenReqVO();
        pageVO.setName("name");
        pageVO.setWorkId("workId");
        pageVO.setGender(0);
        pageVO.setDepartment(0L);
        pageVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalPageChildren(pageVO)).thenReturn(personnalPageRespVOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/pageChildren")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalPageChildren_PersonnalServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalPageChildren(...).
        final PersonnalChildrenReqVO pageVO = new PersonnalChildrenReqVO();
        pageVO.setName("name");
        pageVO.setWorkId("workId");
        pageVO.setGender(0);
        pageVO.setDepartment(0L);
        pageVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalPageChildren(pageVO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/pageChildren")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    void testGetPersonnal() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalStudyService.getPersonnal(0L)).thenReturn(PersonnalStudyDO.builder().build());
        when(mockPersonnalPositionService.getPersonnal(0L)).thenReturn(PersonnalPositionDO.builder().build());

        // Configure PersonnalWorkService.getPersonnal(...).
        final List<PersonnalWorkDO> personnalWorkDOS = Arrays.asList(PersonnalWorkDO.builder().build());
        when(mockPersonnalWorkService.getPersonnal(0L)).thenReturn(personnalWorkDOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/get")
                        .param("id", "0")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnal_PersonnalWorkServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalStudyService.getPersonnal(0L)).thenReturn(PersonnalStudyDO.builder().build());
        when(mockPersonnalPositionService.getPersonnal(0L)).thenReturn(PersonnalPositionDO.builder().build());
        when(mockPersonnalWorkService.getPersonnal(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/get")
                        .param("id", "0")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalById() throws Exception {
        // Setup
        when(mockPersonnalMapper.getPersonnalIdByUserId(0L)).thenReturn(0L);

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalStudyService.getPersonnal(0L)).thenReturn(PersonnalStudyDO.builder().build());
        when(mockPersonnalPositionService.getPersonnal(0L)).thenReturn(PersonnalPositionDO.builder().build());

        // Configure PersonnalWorkService.getPersonnal(...).
        final List<PersonnalWorkDO> personnalWorkDOS = Arrays.asList(PersonnalWorkDO.builder().build());
        when(mockPersonnalWorkService.getPersonnal(0L)).thenReturn(personnalWorkDOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/getInformation")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalById_PersonnalWorkServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockPersonnalMapper.getPersonnalIdByUserId(0L)).thenReturn(0L);

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        when(mockPersonnalStudyService.getPersonnal(0L)).thenReturn(PersonnalStudyDO.builder().build());
        when(mockPersonnalPositionService.getPersonnal(0L)).thenReturn(PersonnalPositionDO.builder().build());
        when(mockPersonnalWorkService.getPersonnal(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/getInformation")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testUpdatePersonnal() throws Exception {
        // Setup
        when(mockTenantApi.getTenantCodeByUserId(0L)).thenReturn(CommonResult.success("value"));

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalService.updatePersonnal(...).
        final PersonnalBasicGetVO personnalBasicVO = new PersonnalBasicGetVO();
        personnalBasicVO.setId(0L);
        personnalBasicVO.setWorkId("workId");
        personnalBasicVO.setUserId(0L);
        verify(mockPersonnalService).updatePersonnal(personnalBasicVO);

        // Confirm PersonnalStudyService.updatePersonnal(...).
        final PersonnalStudyGetVO updateReqVO = new PersonnalStudyGetVO();
        updateReqVO.setId(0L);
        updateReqVO.setPersonnalId(0L);
        verify(mockPersonnalStudyService).updatePersonnal(updateReqVO);

        // Confirm PersonnalWorkService.updatePersonnal(...).
        final PersonnalWorkGetVO personnalWorkGetVO = new PersonnalWorkGetVO();
        personnalWorkGetVO.setId(0L);
        personnalWorkGetVO.setPersonnalId(0L);
        final List<PersonnalWorkGetVO> updateReqVO1 = Arrays.asList(personnalWorkGetVO);
        verify(mockPersonnalWorkService).updatePersonnal(updateReqVO1);

        // Confirm PersonnalPositionService.updatePersonnal(...).
        final PersonnalPositionGetVO updateReqVO2 = new PersonnalPositionGetVO();
        updateReqVO2.setRank(0);
        updateReqVO2.setAdministrativePositionName("administrativePositionName");
        updateReqVO2.setAdministrativePositionRank(0);
        updateReqVO2.setId(0L);
        updateReqVO2.setPersonnalId(0L);
        verify(mockPersonnalPositionService).updatePersonnal(updateReqVO2);

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setDepartment(0L);
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        personInfo.setTenantId(0L);
        verify(mockPersonProducer).sendPersonData(false, personInfo, 0L);
    }

    @Test
    void testUpdatePersonnal_TenantApiReturnsError() throws Exception {
        // Setup
        when(mockTenantApi.getTenantCodeByUserId(0L))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure PersonnalService.getPersonnal(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalService.getPersonnal(0L)).thenReturn(personnalBasicDO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalService.updatePersonnal(...).
        final PersonnalBasicGetVO personnalBasicVO = new PersonnalBasicGetVO();
        personnalBasicVO.setId(0L);
        personnalBasicVO.setWorkId("workId");
        personnalBasicVO.setUserId(0L);
        verify(mockPersonnalService).updatePersonnal(personnalBasicVO);

        // Confirm PersonnalStudyService.updatePersonnal(...).
        final PersonnalStudyGetVO updateReqVO = new PersonnalStudyGetVO();
        updateReqVO.setId(0L);
        updateReqVO.setPersonnalId(0L);
        verify(mockPersonnalStudyService).updatePersonnal(updateReqVO);

        // Confirm PersonnalWorkService.updatePersonnal(...).
        final PersonnalWorkGetVO personnalWorkGetVO = new PersonnalWorkGetVO();
        personnalWorkGetVO.setId(0L);
        personnalWorkGetVO.setPersonnalId(0L);
        final List<PersonnalWorkGetVO> updateReqVO1 = Arrays.asList(personnalWorkGetVO);
        verify(mockPersonnalWorkService).updatePersonnal(updateReqVO1);

        // Confirm PersonnalPositionService.updatePersonnal(...).
        final PersonnalPositionGetVO updateReqVO2 = new PersonnalPositionGetVO();
        updateReqVO2.setRank(0);
        updateReqVO2.setAdministrativePositionName("administrativePositionName");
        updateReqVO2.setAdministrativePositionRank(0);
        updateReqVO2.setId(0L);
        updateReqVO2.setPersonnalId(0L);
        verify(mockPersonnalPositionService).updatePersonnal(updateReqVO2);

        // Confirm PersonProducer.sendPersonData(...).
        final KafkaPersonDTO personInfo = new KafkaPersonDTO();
        personInfo.setPersonnalStatus(0);
        personInfo.setDepartment(0L);
        personInfo.setUserId(0L);
        personInfo.setTenantCode("tenantCode");
        personInfo.setTenantId(0L);
        verify(mockPersonProducer).sendPersonData(false, personInfo, 0L);
    }

    @Test
    void testExport() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalExcel(...).
        final PersonnalExcelVO personnalExcelVO = new PersonnalExcelVO();
        personnalExcelVO.setUserId(0L);
        personnalExcelVO.setName("name");
        personnalExcelVO.setWorkId("workId");
        personnalExcelVO.setAdministrativePositionName("administrativePositionName");
        personnalExcelVO.setAdministrativePositionRank(0);
        final List<PersonnalExcelVO> personnalExcelVOS = Arrays.asList(personnalExcelVO);
        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
        reqVO.setName("name");
        reqVO.setWorkId("workId");
        reqVO.setGender(0);
        reqVO.setDepartment(0L);
        reqVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalExcel(reqVO)).thenReturn(personnalExcelVOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExport_PersonnalServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalExcel(...).
        final PersonnalExportReqVO reqVO = new PersonnalExportReqVO();
        reqVO.setName("name");
        reqVO.setWorkId("workId");
        reqVO.setGender(0);
        reqVO.setDepartment(0L);
        reqVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalExcel(reqVO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testImportTemplate() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/get-import-template")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testImportTemplate_ThrowsIOException() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/get-import-template")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testImportExcel() throws Exception {
        // Setup
        when(mockPersonnalService.importPersonnal(Arrays.asList(PersonnalImportExcelVO.builder().build()),
                false)).thenReturn(PersonnalImportRespVO.builder().build());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(multipart("/hr/personnal/import")
                        .file(new MockMultipartFile("file", "originalFilename", MediaType.APPLICATION_JSON_VALUE,
                                "content".getBytes()))
                        .param("updateSupport", "false")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalSimple() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalSimple(...).
        final PersonnalSimpleRespVO personnalSimpleRespVO = new PersonnalSimpleRespVO();
        personnalSimpleRespVO.setId(0L);
        personnalSimpleRespVO.setName("name");
        personnalSimpleRespVO.setReviewStatus("reviewStatus");
        personnalSimpleRespVO.setPersonnalStatus(0);
        personnalSimpleRespVO.setDepartment(0L);
        when(mockPersonnalService.getPersonnalSimple(0L)).thenReturn(personnalSimpleRespVO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/getSimple")
                        .param("id", "0")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalReviewPage() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalReviewPage(...).
        final PersonnalReviewPageRespVO personnalReviewPageRespVO = new PersonnalReviewPageRespVO();
        personnalReviewPageRespVO.setId(0L);
        personnalReviewPageRespVO.setName("name");
        personnalReviewPageRespVO.setWorkId("workId");
        personnalReviewPageRespVO.setDepartment(0L);
        personnalReviewPageRespVO.setDeptIds(Arrays.asList(0L));
        final PageResult<PersonnalReviewPageRespVO> personnalReviewPageRespVOPageResult = new PageResult<>(
                Arrays.asList(personnalReviewPageRespVO), 0L);
        final PersonnalReviewPageReqVO pageReqVO = new PersonnalReviewPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setGender(0);
        pageReqVO.setDepartment(0L);
        when(mockPersonnalService.getPersonnalReviewPage(pageReqVO)).thenReturn(personnalReviewPageRespVOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/pagePersonReview")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPersonnalReviewPage_PersonnalServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalReviewPage(...).
        final PersonnalReviewPageReqVO pageReqVO = new PersonnalReviewPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setGender(0);
        pageReqVO.setDepartment(0L);
        when(mockPersonnalService.getPersonnalReviewPage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/pagePersonReview")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    void testUpdateReviewStatus() throws Exception {
        // Setup
        // Configure PersonnalMapper.selectById(...).
        final PersonnalBasicDO personnalBasicDO = PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build();
        when(mockPersonnalMapper.selectById(0)).thenReturn(personnalBasicDO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/updateReviewStatus")
                        .param("requ", "0")
                        .param("id", "0")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockPersonnalMapper).updateById(PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build());
    }

    @Test
    void testCreateRegistrationPersonnal() throws Exception {
        // Setup
        // Configure PersonnalService.createRegistrationPersonnal(...).
        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
        createReqVO.setName("name");
        createReqVO.setIdType(0);
        createReqVO.setIdNumber("idNumber");
        createReqVO.setGender(0);
        createReqVO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.createRegistrationPersonnal(createReqVO)).thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/createRegistration")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalStudyService.createPersonnalRegis(...).
        final PersonnalCreateRegistrationVO createReqVO1 = new PersonnalCreateRegistrationVO();
        createReqVO1.setName("name");
        createReqVO1.setIdType(0);
        createReqVO1.setIdNumber("idNumber");
        createReqVO1.setGender(0);
        createReqVO1.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPersonnalStudyService).createPersonnalRegis(createReqVO1, 0L);

        // Confirm PersonnalWorkService.createPersonnalRegis(...).
        final PersonnalCreateRegistrationVO createReqVO2 = new PersonnalCreateRegistrationVO();
        createReqVO2.setName("name");
        createReqVO2.setIdType(0);
        createReqVO2.setIdNumber("idNumber");
        createReqVO2.setGender(0);
        createReqVO2.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPersonnalWorkService).createPersonnalRegis(createReqVO2, 0L);

        // Confirm PersonnalPositionService.createPersonnalRegis(...).
        final PersonnalCreateRegistrationVO createReqVO3 = new PersonnalCreateRegistrationVO();
        createReqVO3.setName("name");
        createReqVO3.setIdType(0);
        createReqVO3.setIdNumber("idNumber");
        createReqVO3.setGender(0);
        createReqVO3.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPersonnalPositionService).createPersonnalRegis(createReqVO3, 0L);
    }

    @Test
    void testGetPersonnalIdByUserId() throws Exception {
        // Setup
        when(mockPersonnalMapper.getPersonnalIdByUserId(0L)).thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/getPersonnalId")
                        .param("userId", "0")
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPageRegistration() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalRegistrationPage(...).
        final PersonnalRegistrationPageRespVO personnalRegistrationPageRespVO = new PersonnalRegistrationPageRespVO();
        personnalRegistrationPageRespVO.setId(0L);
        personnalRegistrationPageRespVO.setName("name");
        personnalRegistrationPageRespVO.setWorkId("workId");
        personnalRegistrationPageRespVO.setDepartment(0);
        personnalRegistrationPageRespVO.setDeptIds(Arrays.asList(0L));
        final PageResult<PersonnalRegistrationPageRespVO> personnalRegistrationPageRespVOPageResult = new PageResult<>(
                Arrays.asList(personnalRegistrationPageRespVO), 0L);
        final PersonnalRegistrationPageReqVO pageReqVO = new PersonnalRegistrationPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setGender(0);
        pageReqVO.setDepartment(0);
        pageReqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        pageReqVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalRegistrationPage(pageReqVO))
                .thenReturn(personnalRegistrationPageRespVOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/pageEntry")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetPageRegistration_PersonnalServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonnalRegistrationPage(...).
        final PersonnalRegistrationPageReqVO pageReqVO = new PersonnalRegistrationPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setGender(0);
        pageReqVO.setDepartment(0);
        pageReqVO.setRegistrationDate(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        pageReqVO.setPeronClassification(0);
        when(mockPersonnalService.getPersonnalRegistrationPage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/pageEntry")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    void testGetRetirePage() throws Exception {
        // Setup
        // Configure PersonnalService.getRetirePage(...).
        final PersonalRetirePageRespVO personalRetirePageRespVO = new PersonalRetirePageRespVO();
        personalRetirePageRespVO.setId(0L);
        personalRetirePageRespVO.setName("name");
        personalRetirePageRespVO.setDepartment("department");
        personalRetirePageRespVO.setUserId(0L);
        personalRetirePageRespVO.setDeptIds(Arrays.asList(0L));
        final PageResult<PersonalRetirePageRespVO> personalRetirePageRespVOPageResult = new PageResult<>(
                Arrays.asList(personalRetirePageRespVO), 0L);
        final PersonalRetirePageReqVO pageReqVO = new PersonalRetirePageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setDepartment(0L);
        pageReqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pageReqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getRetirePage(pageReqVO)).thenReturn(personalRetirePageRespVOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/retirePage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetRetirePage_PersonnalServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure PersonnalService.getRetirePage(...).
        final PersonalRetirePageReqVO pageReqVO = new PersonalRetirePageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setDepartment(0L);
        pageReqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pageReqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getRetirePage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/retirePage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    void testUpdatePersonalRetire() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/updateRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalService.updatePersonalRetire(...).
        final PersonalRetireUpdateVO reqVO = new PersonalRetireUpdateVO();
        reqVO.setId(0L);
        reqVO.setRetireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setRetireRemarks("retireRemarks");
        verify(mockPersonnalService).updatePersonalRetire(reqVO);
    }

    @Test
    void testExportRetire() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonalRetireExcel(...).
        final PersonalRetireExcelVO personalRetireExcelVO = new PersonalRetireExcelVO();
        personalRetireExcelVO.setUserId(0L);
        personalRetireExcelVO.setName("name");
        personalRetireExcelVO.setDepartment("department");
        personalRetireExcelVO.setGender(0);
        personalRetireExcelVO.setPersonnalStatus(0);
        final List<PersonalRetireExcelVO> personalRetireExcelVOS = Arrays.asList(personalRetireExcelVO);
        final PersonalRetireExcelReqVO reqVO = new PersonalRetireExcelReqVO();
        reqVO.setName("name");
        reqVO.setDepartment(0L);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getPersonalRetireExcel(reqVO)).thenReturn(personalRetireExcelVOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/exportRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportRetire_PersonnalServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonalRetireExcel(...).
        final PersonalRetireExcelReqVO reqVO = new PersonalRetireExcelReqVO();
        reqVO.setName("name");
        reqVO.setDepartment(0L);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getPersonalRetireExcel(reqVO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/exportRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportFewRetire() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalRetireDataById(...).
        final PersonalRetireExcelVO personalRetireExcelVO = new PersonalRetireExcelVO();
        personalRetireExcelVO.setUserId(0L);
        personalRetireExcelVO.setName("name");
        personalRetireExcelVO.setDepartment("department");
        personalRetireExcelVO.setGender(0);
        personalRetireExcelVO.setPersonnalStatus(0);
        when(mockPersonnalMapper.getPersonalRetireDataById("id")).thenReturn(personalRetireExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportFewRetire_PersonnalMapperReturnsNull() throws Exception {
        // Setup
        when(mockPersonnalMapper.getPersonalRetireDataById("id")).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportFewRetire_AdminUserApiReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalRetireDataById(...).
        final PersonalRetireExcelVO personalRetireExcelVO = new PersonalRetireExcelVO();
        personalRetireExcelVO.setUserId(0L);
        personalRetireExcelVO.setName("name");
        personalRetireExcelVO.setDepartment("department");
        personalRetireExcelVO.setGender(0);
        personalRetireExcelVO.setPersonnalStatus(0);
        when(mockPersonnalMapper.getPersonalRetireDataById("id")).thenReturn(personalRetireExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Collections.emptyList()));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportFewRetire_AdminUserApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalRetireDataById(...).
        final PersonalRetireExcelVO personalRetireExcelVO = new PersonalRetireExcelVO();
        personalRetireExcelVO.setUserId(0L);
        personalRetireExcelVO.setName("name");
        personalRetireExcelVO.setDepartment("department");
        personalRetireExcelVO.setGender(0);
        personalRetireExcelVO.setPersonnalStatus(0);
        when(mockPersonnalMapper.getPersonalRetireDataById("id")).thenReturn(personalRetireExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportFewRetire_DeptApiReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalRetireDataById(...).
        final PersonalRetireExcelVO personalRetireExcelVO = new PersonalRetireExcelVO();
        personalRetireExcelVO.setUserId(0L);
        personalRetireExcelVO.setName("name");
        personalRetireExcelVO.setDepartment("department");
        personalRetireExcelVO.setGender(0);
        personalRetireExcelVO.setPersonnalStatus(0);
        when(mockPersonnalMapper.getPersonalRetireDataById("id")).thenReturn(personalRetireExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportFewRetire_DeptApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalRetireDataById(...).
        final PersonalRetireExcelVO personalRetireExcelVO = new PersonalRetireExcelVO();
        personalRetireExcelVO.setUserId(0L);
        personalRetireExcelVO.setName("name");
        personalRetireExcelVO.setDepartment("department");
        personalRetireExcelVO.setGender(0);
        personalRetireExcelVO.setPersonnalStatus(0);
        when(mockPersonnalMapper.getPersonalRetireDataById("id")).thenReturn(personalRetireExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectRetire")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetLeavePage() throws Exception {
        // Setup
        // Configure PersonnalService.getLeavePage(...).
        final PersonalLeavePageRespVO personalLeavePageRespVO = new PersonalLeavePageRespVO();
        personalLeavePageRespVO.setId(0L);
        personalLeavePageRespVO.setName("name");
        personalLeavePageRespVO.setDepartment("department");
        personalLeavePageRespVO.setUserId(0L);
        personalLeavePageRespVO.setDeptIds(Arrays.asList(0L));
        final PageResult<PersonalLeavePageRespVO> personalLeavePageRespVOPageResult = new PageResult<>(
                Arrays.asList(personalLeavePageRespVO), 0L);
        final PersonalLeavePageReqVO pageReqVO = new PersonalLeavePageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setLeaveChannel("leaveChannel");
        pageReqVO.setDepartment(0L);
        pageReqVO.setLeaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getLeavePage(pageReqVO)).thenReturn(personalLeavePageRespVOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/leavePage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetLeavePage_PersonnalServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure PersonnalService.getLeavePage(...).
        final PersonalLeavePageReqVO pageReqVO = new PersonalLeavePageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setLeaveChannel("leaveChannel");
        pageReqVO.setDepartment(0L);
        pageReqVO.setLeaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getLeavePage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/leavePage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    void testUpdatePersonalLeave() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/updateLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalService.updatePersonalLeave(...).
        final PersonalLeaveUpdateVO reqVO = new PersonalLeaveUpdateVO();
        reqVO.setId(0L);
        reqVO.setLeaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setLeaveChannel("leaveChannel");
        reqVO.setLeaveDestination("leaveDestination");
        reqVO.setLeaveReason("leaveReason");
        verify(mockPersonnalService).updatePersonalLeave(reqVO);
    }

    @Test
    void testExportLeave() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonalLeaveExcel(...).
        final PersonalLeaveExcelVO personalLeaveExcelVO = new PersonalLeaveExcelVO();
        personalLeaveExcelVO.setUserId(0L);
        personalLeaveExcelVO.setName("name");
        personalLeaveExcelVO.setDepartment("department");
        personalLeaveExcelVO.setPersonnalStatus(0);
        personalLeaveExcelVO.setLeaveTime("leaveTime");
        final List<PersonalLeaveExcelVO> personalLeaveExcelVOS = Arrays.asList(personalLeaveExcelVO);
        final PersonalLeaveExcelReqVO reqVO = new PersonalLeaveExcelReqVO();
        reqVO.setName("name");
        reqVO.setLeaveChannel("leaveChannel");
        reqVO.setDepartment(0L);
        reqVO.setLeaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getPersonalLeaveExcel(reqVO)).thenReturn(personalLeaveExcelVOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/exportLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportLeave_PersonnalServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonalLeaveExcel(...).
        final PersonalLeaveExcelReqVO reqVO = new PersonalLeaveExcelReqVO();
        reqVO.setName("name");
        reqVO.setLeaveChannel("leaveChannel");
        reqVO.setDepartment(0L);
        reqVO.setLeaveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getPersonalLeaveExcel(reqVO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/exportLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectLeave() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalLeaveDataById(...).
        final PersonalLeaveExcelVO personalLeaveExcelVO = new PersonalLeaveExcelVO();
        personalLeaveExcelVO.setUserId(0L);
        personalLeaveExcelVO.setName("name");
        personalLeaveExcelVO.setDepartment("department");
        personalLeaveExcelVO.setPersonnalStatus(0);
        personalLeaveExcelVO.setLeaveTime("leaveTime");
        when(mockPersonnalMapper.getPersonalLeaveDataById("id")).thenReturn(personalLeaveExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectLeave_PersonnalMapperReturnsNull() throws Exception {
        // Setup
        when(mockPersonnalMapper.getPersonalLeaveDataById("id")).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectLeave_AdminUserApiReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalLeaveDataById(...).
        final PersonalLeaveExcelVO personalLeaveExcelVO = new PersonalLeaveExcelVO();
        personalLeaveExcelVO.setUserId(0L);
        personalLeaveExcelVO.setName("name");
        personalLeaveExcelVO.setDepartment("department");
        personalLeaveExcelVO.setPersonnalStatus(0);
        personalLeaveExcelVO.setLeaveTime("leaveTime");
        when(mockPersonnalMapper.getPersonalLeaveDataById("id")).thenReturn(personalLeaveExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Collections.emptyList()));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectLeave_AdminUserApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalLeaveDataById(...).
        final PersonalLeaveExcelVO personalLeaveExcelVO = new PersonalLeaveExcelVO();
        personalLeaveExcelVO.setUserId(0L);
        personalLeaveExcelVO.setName("name");
        personalLeaveExcelVO.setDepartment("department");
        personalLeaveExcelVO.setPersonnalStatus(0);
        personalLeaveExcelVO.setLeaveTime("leaveTime");
        when(mockPersonnalMapper.getPersonalLeaveDataById("id")).thenReturn(personalLeaveExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectLeave_DeptApiReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalLeaveDataById(...).
        final PersonalLeaveExcelVO personalLeaveExcelVO = new PersonalLeaveExcelVO();
        personalLeaveExcelVO.setUserId(0L);
        personalLeaveExcelVO.setName("name");
        personalLeaveExcelVO.setDepartment("department");
        personalLeaveExcelVO.setPersonnalStatus(0);
        personalLeaveExcelVO.setLeaveTime("leaveTime");
        when(mockPersonnalMapper.getPersonalLeaveDataById("id")).thenReturn(personalLeaveExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectLeave_DeptApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalLeaveDataById(...).
        final PersonalLeaveExcelVO personalLeaveExcelVO = new PersonalLeaveExcelVO();
        personalLeaveExcelVO.setUserId(0L);
        personalLeaveExcelVO.setName("name");
        personalLeaveExcelVO.setDepartment("department");
        personalLeaveExcelVO.setPersonnalStatus(0);
        personalLeaveExcelVO.setLeaveTime("leaveTime");
        when(mockPersonnalMapper.getPersonalLeaveDataById("id")).thenReturn(personalLeaveExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectLeave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetDeathPage() throws Exception {
        // Setup
        // Configure PersonnalService.getDeathPage(...).
        final PersonalDeathPageRespVO personalDeathPageRespVO = new PersonalDeathPageRespVO();
        personalDeathPageRespVO.setId(0L);
        personalDeathPageRespVO.setName("name");
        personalDeathPageRespVO.setWorkId("workId");
        personalDeathPageRespVO.setDepartment("department");
        personalDeathPageRespVO.setUserId(0L);
        final PageResult<PersonalDeathPageRespVO> personalDeathPageRespVOPageResult = new PageResult<>(
                Arrays.asList(personalDeathPageRespVO), 0L);
        final PersonalDeathPageReqVO pageReqVO = new PersonalDeathPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setDeathStatus("deathStatus");
        pageReqVO.setDepartment(0L);
        pageReqVO.setDeathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getDeathPage(pageReqVO)).thenReturn(personalDeathPageRespVOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/deathPage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetDeathPage_PersonnalServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure PersonnalService.getDeathPage(...).
        final PersonalDeathPageReqVO pageReqVO = new PersonalDeathPageReqVO();
        pageReqVO.setName("name");
        pageReqVO.setDeathStatus("deathStatus");
        pageReqVO.setDepartment(0L);
        pageReqVO.setDeathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getDeathPage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/deathPage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    void testUpdatePersonalDeath() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/updateDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PersonnalService.updatePersonalDeath(...).
        final PersonalDeathUpdateVO reqVO = new PersonalDeathUpdateVO();
        reqVO.setId(0L);
        reqVO.setDeathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setDeathStatus("deathStatus");
        reqVO.setDeathRemarks("deathRemarks");
        verify(mockPersonnalService).updatePersonalDeath(reqVO);
    }

    @Test
    void testExportDeath() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonalDeathExcel(...).
        final PersonalDeathExcelVO personalDeathExcelVO = new PersonalDeathExcelVO();
        personalDeathExcelVO.setUserId(0L);
        personalDeathExcelVO.setName("name");
        personalDeathExcelVO.setWorkId("workId");
        personalDeathExcelVO.setDepartment("department");
        personalDeathExcelVO.setGender(0);
        final List<PersonalDeathExcelVO> personalDeathExcelVOS = Arrays.asList(personalDeathExcelVO);
        final PersonalDeathExcelReqVO reqVO = new PersonalDeathExcelReqVO();
        reqVO.setName("name");
        reqVO.setDeathStatus("deathStatus");
        reqVO.setDepartment(0L);
        reqVO.setDeathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getPersonalDeathExcel(reqVO)).thenReturn(personalDeathExcelVOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/exportDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportDeath_PersonnalServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalService.getPersonalDeathExcel(...).
        final PersonalDeathExcelReqVO reqVO = new PersonalDeathExcelReqVO();
        reqVO.setName("name");
        reqVO.setDeathStatus("deathStatus");
        reqVO.setDepartment(0L);
        reqVO.setDeathTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPersonnalService.getPersonalDeathExcel(reqVO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/exportDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectDeath() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalDeathDataById(...).
        final PersonalDeathExcelVO personalDeathExcelVO = new PersonalDeathExcelVO();
        personalDeathExcelVO.setUserId(0L);
        personalDeathExcelVO.setName("name");
        personalDeathExcelVO.setWorkId("workId");
        personalDeathExcelVO.setDepartment("department");
        personalDeathExcelVO.setGender(0);
        when(mockPersonnalMapper.getPersonalDeathDataById("id")).thenReturn(personalDeathExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectDeath_PersonnalMapperReturnsNull() throws Exception {
        // Setup
        when(mockPersonnalMapper.getPersonalDeathDataById("id")).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectDeath_AdminUserApiReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalDeathDataById(...).
        final PersonalDeathExcelVO personalDeathExcelVO = new PersonalDeathExcelVO();
        personalDeathExcelVO.setUserId(0L);
        personalDeathExcelVO.setName("name");
        personalDeathExcelVO.setWorkId("workId");
        personalDeathExcelVO.setDepartment("department");
        personalDeathExcelVO.setGender(0);
        when(mockPersonnalMapper.getPersonalDeathDataById("id")).thenReturn(personalDeathExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Collections.emptyList()));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectDeath_AdminUserApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalDeathDataById(...).
        final PersonalDeathExcelVO personalDeathExcelVO = new PersonalDeathExcelVO();
        personalDeathExcelVO.setUserId(0L);
        personalDeathExcelVO.setName("name");
        personalDeathExcelVO.setWorkId("workId");
        personalDeathExcelVO.setDepartment("department");
        personalDeathExcelVO.setGender(0);
        when(mockPersonnalMapper.getPersonalDeathDataById("id")).thenReturn(personalDeathExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectDeath_DeptApiReturnsNoItems() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalDeathDataById(...).
        final PersonalDeathExcelVO personalDeathExcelVO = new PersonalDeathExcelVO();
        personalDeathExcelVO.setUserId(0L);
        personalDeathExcelVO.setName("name");
        personalDeathExcelVO.setWorkId("workId");
        personalDeathExcelVO.setDepartment("department");
        personalDeathExcelVO.setGender(0);
        when(mockPersonnalMapper.getPersonalDeathDataById("id")).thenReturn(personalDeathExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testExportSelectDeath_DeptApiReturnsError() throws Exception {
        // Setup
        // Configure PersonnalMapper.getPersonalDeathDataById(...).
        final PersonalDeathExcelVO personalDeathExcelVO = new PersonalDeathExcelVO();
        personalDeathExcelVO.setUserId(0L);
        personalDeathExcelVO.setName("name");
        personalDeathExcelVO.setWorkId("workId");
        personalDeathExcelVO.setDepartment("department");
        personalDeathExcelVO.setGender(0);
        when(mockPersonnalMapper.getPersonalDeathDataById("id")).thenReturn(personalDeathExcelVO);

        when(mockAdminUserApi.getDeptList(0L)).thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure DeptApi.getDepts(...).
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.error(new ServiceException(0, "message"));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/exportSelectDeath")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testSendAllPersonInfo() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/sendAllPersonInfo")
                        .param("url", "url")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockPersonnalService).sendAllPersonInfo("url");
    }

    @Test
    void testGetAllPersonInfo() throws Exception {
        // Setup
        // Configure PersonnalMapper.selectSimplePersonInfoList(...).
        final PersonDTO personDTO = new PersonDTO();
        personDTO.setEmployee_id("employee_id");
        personDTO.setName("name");
        personDTO.setSex("sex");
        personDTO.setEducation("education");
        personDTO.setMobile_phone("mobile_phone");
        final List<PersonDTO> personDTOS = Arrays.asList(personDTO);
        when(mockPersonnalMapper.selectSimplePersonInfoList("tenantCode")).thenReturn(personDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/getAllPersonInfo")
                        .param("data", "data")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    void testGetAllPersonInfo_PersonnalMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockPersonnalMapper.selectSimplePersonInfoList("tenantCode")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hr/personnal/getAllPersonInfo")
                        .param("data", "data")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    void testDes() throws Exception {
        // Setup
        // Configure PersonnalMapper.selectList(...).
        final List<PersonnalBasicDO> list = Arrays.asList(PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build());
        when(mockPersonnalMapper.selectList()).thenReturn(list);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/des")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockPersonnalMapper).update(eq(PersonnalBasicDO.builder()
                .id(0L)
                .idNumber("idNumber")
                .department(0L)
                .reviewStatus(0)
                .userId(0L)
                .idNumberDes("idNumberDes")
                .build()), any(LambdaUpdateWrapper.class));
    }

    @Test
    void testDes_PersonnalMapperSelectListReturnsNoItems() throws Exception {
        // Setup
        when(mockPersonnalMapper.selectList()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hr/personnal/des")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
