package com.unicom.swdx.module.hr.service.personnal;

import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalWorkGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalWorkVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalWorkMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PersonnalWorkServiceImplTest {

    @Mock
    private PersonnalWorkMapper mockPersonnalWorkMapper;

    @InjectMocks
    private PersonnalWorkServiceImpl personnalWorkServiceImplUnderTest;

    @Test
    void testCreatePersonnal() {
        // Setup
        final PersonnalWorkVO personnalWorkVO = new PersonnalWorkVO();
        personnalWorkVO.setWorkUnit("workUnit");
        personnalWorkVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalWorkVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        personnalWorkVO.setPosition("position");
        personnalWorkVO.setWorkContent("workContent");
        final List<PersonnalWorkVO> createReqVO = Arrays.asList(personnalWorkVO);

        // Run the test
        personnalWorkServiceImplUnderTest.createPersonnal(createReqVO, 0L);

        // Verify the results
        verify(mockPersonnalWorkMapper).insertBatch(Arrays.asList(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build()));
    }

    @Test
    void testCreatePersonnalRegis() {
        // Setup
        final PersonnalCreateRegistrationVO createReqVO = new PersonnalCreateRegistrationVO();
        createReqVO.setName("name");
        createReqVO.setIdType(0);
        createReqVO.setIdNumber("idNumber");
        createReqVO.setGender(0);
        createReqVO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        personnalWorkServiceImplUnderTest.createPersonnalRegis(createReqVO, 0L);

        // Verify the results
        verify(mockPersonnalWorkMapper).insert(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
    }

    @Test
    void testGetPersonnal() {
        // Setup
        final List<PersonnalWorkDO> expectedResult = Arrays.asList(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());

        // Configure PersonnalWorkMapper.selectByPersonnalId(...).
        final List<PersonnalWorkDO> personnalWorkDOS = Arrays.asList(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
        when(mockPersonnalWorkMapper.selectByPersonnalId(0L)).thenReturn(personnalWorkDOS);

        // Run the test
        final List<PersonnalWorkDO> result = personnalWorkServiceImplUnderTest.getPersonnal(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonnal_PersonnalWorkMapperReturnsNoItems() {
        // Setup
        when(mockPersonnalWorkMapper.selectByPersonnalId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PersonnalWorkDO> result = personnalWorkServiceImplUnderTest.getPersonnal(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdatePersonnal() {
        // Setup
        final PersonnalWorkGetVO personnalWorkGetVO = new PersonnalWorkGetVO();
        personnalWorkGetVO.setId(0L);
        personnalWorkGetVO.setPersonnalId(0L);
        final List<PersonnalWorkGetVO> updateReqVO = Arrays.asList(personnalWorkGetVO);

        // Configure PersonnalWorkMapper.selectByPersonnalId(...).
        final List<PersonnalWorkDO> personnalWorkDOS = Arrays.asList(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
        when(mockPersonnalWorkMapper.selectByPersonnalId(0L)).thenReturn(personnalWorkDOS);

        // Run the test
        personnalWorkServiceImplUnderTest.updatePersonnal(updateReqVO);

        // Verify the results
        verify(mockPersonnalWorkMapper).updateById(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
        verify(mockPersonnalWorkMapper).insert(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
        verify(mockPersonnalWorkMapper).deleteById(0L);
    }

    @Test
    void testUpdatePersonnal_PersonnalWorkMapperSelectByPersonnalIdReturnsNoItems() {
        // Setup
        final PersonnalWorkGetVO personnalWorkGetVO = new PersonnalWorkGetVO();
        personnalWorkGetVO.setId(0L);
        personnalWorkGetVO.setPersonnalId(0L);
        final List<PersonnalWorkGetVO> updateReqVO = Arrays.asList(personnalWorkGetVO);
        when(mockPersonnalWorkMapper.selectByPersonnalId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        personnalWorkServiceImplUnderTest.updatePersonnal(updateReqVO);

        // Verify the results
        verify(mockPersonnalWorkMapper).updateById(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
        verify(mockPersonnalWorkMapper).insert(PersonnalWorkDO.builder()
                .personnalId(0L)
                .id(0L)
                .build());
        verify(mockPersonnalWorkMapper).deleteById(0L);
    }
}
