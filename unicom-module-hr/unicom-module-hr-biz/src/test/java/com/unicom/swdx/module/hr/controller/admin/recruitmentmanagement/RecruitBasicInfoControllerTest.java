package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class RecruitBasicInfoControllerTest {

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void queryByList() {
    }

    @Test
    void faceByList() {
    }

    @Test
    void check() {
    }

    @Test
    void apply() {
    }

    @Test
    void saveDraft() {
    }

    @Test
    void backToModify() {
    }

    @Test
    void detail() {
    }

    @Test
    void deliveryStatus() {
    }

    @Test
    void getStationPage() {
    }

    @Test
    void getInformationById() {
    }

    @Test
    void export() {
    }

    @Test
    void exportPerson() {
    }
}