package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 传输类
 * <AUTHOR>
 * @data 2024/3/2 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RecruitmentPositionManagementVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 680686122302180868L;

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 招聘单位
     */
    @ApiModelProperty(value = "招聘单位")
    private String recruitmentUnit;
    /**
     * 招聘部门
     */
    @ApiModelProperty(value = "招聘部门")
    private String recruitmentDepartment;
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String positionName;
    /**
     * 岗位代码
     */
    @ApiModelProperty(value = "岗位代码")
    private String postCode;
    /**
     * 岗位类别，1-专业技术岗位,2-管理岗位,3-工勤技能岗位,4-其他岗位
     */
    @ApiModelProperty(value = "岗位类别")
    private Integer jobCategory;
    /**
     * 招聘计划
     */
    @ApiModelProperty(value = "招聘计划")
    private String recruitmentPlan;
    /**
     * 招聘类别:1为高层次，2为非高层次
     */
    @ApiModelProperty(value = "招聘类别")
    private Integer recruitmentCategory;
    /**
     * 学历学位
     */
    @ApiModelProperty(value = "学历学位")
    private String academicDegree;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private String age;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String profession;
    /**
     * 其它
     */
    @ApiModelProperty(value = "其它")
    private String other;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 任职要求
     */
    @ApiModelProperty(value = "任职要求")
    private String jobRequirements;
    /**
     * 岗位职责
     */
    @ApiModelProperty(value = "岗位职责")
    private String jobResponsibility;
    /**
     * 发布开始时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "发布开始时间")
    private LocalDateTime startTime;
    /**
     * 发布截止时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "发布截止时间")
    private LocalDateTime cutTime;
    /**
     * 发布状态,1-未发布,2-招聘中,3-完成
     */
    @ApiModelProperty(value = "发布状态")
    private String releaseStatus;
    /**
     * 所属批次,与批次管理表的id对应
     */
    @ApiModelProperty(value = "所属批次")
    private Integer owningBatch;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

}
