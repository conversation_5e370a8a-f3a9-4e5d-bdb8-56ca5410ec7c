package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
@ApiModel(value = "简历遴选个人报名信息导出 Request VO")
public class RecruitApplyExportReqVO {
    /**
     * 选中要导出数据的主键列表ids
     */
    @ApiModelProperty(value = "选中要导出数据的主键列表ids", required = true, example = "1")
    private List<Integer> ids;
}
