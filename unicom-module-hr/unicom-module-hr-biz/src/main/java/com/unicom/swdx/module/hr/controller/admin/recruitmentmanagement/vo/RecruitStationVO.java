package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@ToString(callSuper = true)
@ApiModel(value = "RecruitStation", description = "招聘岗位")
public class RecruitStationVO  implements Serializable {

//    private static final long serialVersionUID = 619951462166988548L;
    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 招聘单位
     */
    @ApiModelProperty(value = "招聘单位")
    private String recruitmentUnit;
    /**
     * 招聘部门
     */
    @ApiModelProperty(value = "招聘部门")
    private String recruitmentDepartment;
    /**
     * 岗位代码
     */
    @ApiModelProperty(value = "岗位代码")
    private String postCode;
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String positionName;
    /**
     * 招聘计划
     */
    @ApiModelProperty(value = "招聘计划")
    private String recruitmentPlan;
    /**
     * 发布开始时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "发布开始时间")
    private LocalDateTime startTime;
    /**
     * 发布截止时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "发布截止时间")
    private LocalDateTime cutTime;
}
