package com.unicom.swdx.module.hr.service.personnal;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.enums.ReviewStatusEnum;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.personnal.PersonnalInformationController;
import com.unicom.swdx.module.hr.controller.admin.personnal.PersonnalTransferPageRespVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.personnal.PersonnalTransferConvert;
import com.unicom.swdx.module.hr.dal.mysql.personnal.*;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.system.api.message.MessageApi;
import com.unicom.swdx.module.system.api.message.dto.MessageAuthorityUpdateReqDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import com.unicom.swdx.module.system.api.user.dto.HrDeptDTO;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.sql.Wrapper;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

/**
 * 人事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonnalTransferServiceImpl extends ServiceImpl<PersonnalTransferMapper, PersonnalTransferDO> implements PersonnalTransferService {

    @Resource
    PersonnalTransferMapper personnalTransferMapper;

    @Resource
    private PermissionApi permissionApi;
    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private MessageApi messageApi;

    @Resource
    private PersonnalInformationController personnalInformationController;

    @Resource
    private PersonnalMapper personnalMapper;

    @Resource
    private PersonnalPositionMapper personnalPositionMapper;

    @Resource
    private PersonProducer personProducer;
    @Resource
    private TenantApi tenantApi;

    @Resource
    private PersonnalService personnalService;


    @Override
    public Long createPersonnalTransfer(PersonnalCreateTransferReqVO createReqVO) {


//        // 原始用户信息
//        AdminUserRespDTO user = adminUserApi.getPersonal(createReqVO.getUserId()).getData();

        //调admin的api更改业务中台部门
        //同步禁用消息中心授权
        //判断调出部门是否为多部门
        List<Long> outDepts = Arrays.stream(createReqVO.getOutDeptIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        //判断调入部门是否为多部门
        List<Long> inDepts = Arrays.stream(createReqVO.getInDeptIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        //根据用户id获取对应的多部门Id
//        List<Long> deptList = adminUserApi.getDeptList(createReqVO.getUserId()).getCheckedData();
        //只有不相同如何都要更新users表的deptId和消息禁用
        if(!outDepts.equals(inDepts)){
            //更新单表
            adminUserApi.updateDept(createReqVO.getUserId(),inDepts.get(0));
            //修改消息状态
            transferMessage(createReqVO.getUserId());
        }
        //批量修改多部门(还有判断是否为多部门)
        adminUserApi.updateUserDeptBatch(createReqVO.getUserId(), inDepts);

        //更新basic表
        // 创建一个LambdaUpdateWrapper实例
        LambdaUpdateWrapper<PersonnalBasicDO> pbUpdateWrapper = new LambdaUpdateWrapper<>();
        // 设置更新条件：userId等于传入的userId
        pbUpdateWrapper.eq(PersonnalBasicDO::getUserId, createReqVO.getUserId());
        //
        pbUpdateWrapper.set(PersonnalBasicDO::getDepartment,inDepts.get(0));
        if(createReqVO.getInSubjectRoom()!=null){
            pbUpdateWrapper.set(PersonnalBasicDO::getSubjectRoom,createReqVO.getInSubjectRoom());
        }
        personnalMapper.update(new PersonnalBasicDO(),pbUpdateWrapper);
        //更改postion表
        LambdaUpdateWrapper<PersonnalPositionDO> ppUpdateWrapper = new LambdaUpdateWrapper<>();
        ppUpdateWrapper.eq(PersonnalPositionDO::getPersonnalId,createReqVO.getId());
        //职级
        if(createReqVO.getInRank()!=null){
            ppUpdateWrapper.set(PersonnalPositionDO::getRank,createReqVO.getInRank());
        }
        //级别
        if(createReqVO.getInAdministrativePositionRank()!=null){
            ppUpdateWrapper.set(PersonnalPositionDO::getAdministrativePositionRank,
                    createReqVO.getInAdministrativePositionRank());
        }
        //名称
        if(createReqVO.getInAdministrativePositionName()!=null){
            ppUpdateWrapper.set(PersonnalPositionDO::getAdministrativePositionName,
                    createReqVO.getInAdministrativePositionName());
        }
        personnalPositionMapper.update(new PersonnalPositionDO(),ppUpdateWrapper);
        if(createReqVO.getTransferTime()==null){
            createReqVO.setTransferTime(LocalDateTime.now());
        }

        //发kafka消息
        PersonnalGetRespVO result =  personnalInformationController.getPersonnal(createReqVO.getId()).getCheckedData();
        KafkaPersonDTO kafkaPersonDTO = PersonnalConvert.INSTANCE.convertToKafkaPerson6(result);
        //设置相关属性为更新后的
        result.getBasicVO().setDepartment(inDepts.get(0));
        if(createReqVO.getInAdministrativePositionName()!=null){
            result.getPositionVO().setAdministrativePositionName(createReqVO.getInAdministrativePositionName());
        }
        kafkaPersonDTO.setUserId(createReqVO.getUserId());
        kafkaPersonDTO.setTenantCode(tenantApi.getTenantCodeByUserId(createReqVO.getUserId()).getCheckedData());
        personProducer.sendPersonData(false,kafkaPersonDTO  , personnalService.getPersonnal(createReqVO.getId()).getDepartment() );
        PersonnalTransferDO personnalTransferDO = PersonnalTransferConvert.INSTANCE.convert(createReqVO);
        //id不是人事系统id
        personnalTransferDO.setId(null);
        return Long.valueOf(personnalTransferMapper.insert(personnalTransferDO));
    }

    @Override
    public PageResult<PersonnalTransferPageRespVO> getPersonnalTransferPage(PersonnalTransferPageReqVO pageVO) {
        Long tenantId = getTenantId();
        IPage pageResult = MyBatisUtils.buildPage(pageVO);
        List<PersonnalTransferPageRespVO> data = personnalTransferMapper.selectPage(pageResult,pageVO,tenantId);
        return new PageResult<>(data,pageResult.getTotal());
    }

    @Override
    public List<PersonnalTransferExcelVO> getPersonnalTransferExcel(PersonnalTransferPageReqVO pageVO) {
        Long tenantId = getTenantId();
        List<PersonnalTransferExcelVO> data = personnalTransferMapper.selectList(pageVO,tenantId);
        return data;
    }


    @Override
    public List<PersonnalTransferPageRespVO> getPersonnalTransferByUser(String userId) {
        Long tenantId = getTenantId();
        List<PersonnalTransferPageRespVO> data = personnalTransferMapper.selectListByUser(userId,tenantId);
        return data;
    }

    @Override
    public PersonnalTransferDetailResqVO getPersonnalTransferId(Long id) {
        PersonnalTransferDO transferDO = personnalTransferMapper.selectByPersonnalId(id);
        PersonnalTransferDetailResqVO resqVO = PersonnalTransferConvert.INSTANCE.convertDetail(transferDO);
        return resqVO;
    }

    @Override
    public PersonnalTransferOutVO getPersonnalTransferOut(String userId) {
        PersonnalTransferOutVO personnalTransferOutVO = personnalTransferMapper.selectPersonnalTransferOut(userId);

        //获取多部门信息
        List<HrDeptDTO> deptVOList= adminUserApi.getDeptDetailList(Long.valueOf(userId)).getCheckedData();
        if(!deptVOList.isEmpty()) {
// 拼接所有的id
            String ids = deptVOList.stream()
                    .map(HrDeptDTO::getDeptId)
                    .map(String::valueOf) // 将int类型的id转换为String
                    .collect(Collectors.joining(","));
            // 拼接所有的name
            String names = deptVOList.stream()
                    .map(HrDeptDTO::getName)
                    .collect(Collectors.joining(","));
            personnalTransferOutVO.setDeptId(ids);
            personnalTransferOutVO.setDeptName(names);
        }
        return personnalTransferOutVO;
    }

    //换部门同步消息中心权限禁用
    private void transferMessage(Long userId){
        Long mid = messageApi.getMessage(userId);
        //消息权限存在的话更改状态为禁用
        if (mid != null) {
            MessageAuthorityUpdateReqDTO messageAuthorityUpdateReqDTO = new MessageAuthorityUpdateReqDTO();
            messageAuthorityUpdateReqDTO.setId(mid);
            messageAuthorityUpdateReqDTO.setStatus(1);
            messageApi.updateMessage(messageAuthorityUpdateReqDTO);
        }
    }

}
