package com.unicom.swdx.module.hr.service.personnal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.hr.api.dto.PersonnalApiDO;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.hr.api.dto.PersonnalUpdateReqDTO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalPositionConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalStudyConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalWorkConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalMapper;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;


@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class PersonnalApiImpl implements PersonnalApi {


    @Resource
    PersonnalMapper personnalMapper;

    @Resource
    PersonnalServiceImpl personnalService;

    @Resource
    private PersonnalStudyService personnalStudyService;
    @Resource
    private PersonnalPositionService personnalPositionService;
    @Resource
    private PersonnalWorkService personnalWorkService;

    @Resource
    private PersonProducer personProducer;

    @Resource
    private TenantApi tenantApi;


    @Override
    public PersonnalApiDO createUser(PersonnalBasicVO createReqVO) {
        PersonnalBasicDO personnalBasicDO  = personnalService.createPersonnal(createReqVO ,null ,null,null);
        PersonnalApiDO apiDO =  PersonnalConvert.INSTANCE.convert(personnalBasicDO);
        return apiDO;
    }

    @Override
    public PersonnalApiDO getUser(Long id) {
        LambdaQueryWrapperX<PersonnalBasicDO> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(PersonnalBasicDO::getUserId , id);
        PersonnalBasicDO ba =   personnalMapper.selectOne(lambdaQueryWrapperX.last("limit 1"));
        PersonnalApiDO apiDO =  PersonnalConvert.INSTANCE.convert(ba);
        return apiDO;
    }

    @Override
    public CommonResult<Boolean> updateUser(PersonnalUpdateReqDTO reqDTO) {
        PersonnalBasicGetVO basicVO = PersonnalConvert.INSTANCE.convertDTO(reqDTO);

        personnalService.updateFromUser(basicVO);
        Long userId = reqDTO.getUserId();

        Long id = personnalMapper.getPersonnalIdByUserId(userId);

        basicVO.setId(id);

        basicVO.setDepartment(personnalService.getPersonnal(basicVO.getId()).getDepartment());

        PersonnalStudyDO personStudy = personnalStudyService.getPersonnal(id);

        PersonnalPositionDO personPosition = personnalPositionService.getPersonnal(id);

        List<PersonnalWorkDO> personWork = personnalWorkService.getPersonnal(id);

        PersonnalUpdateReqVO reqVO = new PersonnalUpdateReqVO();

        PersonnalStudyGetVO personnalStudy = PersonnalStudyConvert.INSTANCE.convert0(personStudy);

        PersonnalPositionGetVO personnalPosition = PersonnalPositionConvert.INSTANCE.convert0(personPosition);

        List<PersonnalWorkGetVO> personnalWork = PersonnalWorkConvert.INSTANCE.convert0(personWork);

        reqVO.setBasicVO(basicVO);
        reqVO.setStudyVO(personnalStudy);
        reqVO.setPositionVO(personnalPosition);
        reqVO.setWorkVOList(personnalWork);


        KafkaPersonDTO kafkaPersonDTO = PersonnalConvert.INSTANCE.convertToKafkaPerson2(reqVO);

        kafkaPersonDTO.setTenantCode(tenantApi.getTenantCodeByUserId(basicVO.getUserId()).getCheckedData());
        personProducer.sendPersonData(false,kafkaPersonDTO  , personnalService.getPersonnal(basicVO.getId()).getDepartment() );
        return success(true);
    }

    @Override
    public CommonResult<Boolean> deleteUser(Long userId) {
        personnalService.deletePersonnal(userId);

        KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateUserProfile(Long userId, Integer sex, String email) {
        LambdaUpdateWrapper<PersonnalBasicDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(PersonnalBasicDO::getEmail,email)
                .set(PersonnalBasicDO::getGender,sex)
                .eq(PersonnalBasicDO::getUserId,userId);
        personnalMapper.update(new PersonnalBasicDO(),lambdaUpdateWrapper);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateUserAvatar(Long userId, String avatar) {
        LambdaUpdateWrapper<PersonnalBasicDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(PersonnalBasicDO::getEmail,avatar)
                .eq(PersonnalBasicDO::getUserId,userId);
        personnalMapper.update(new PersonnalBasicDO(),lambdaUpdateWrapper);
        return success(true);
    }

    @Override
    public PersonnalApiDO getHrUserByMobileorName(String mobile, String name) {


        LambdaQueryWrapperX<PersonnalBasicDO> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(PersonnalBasicDO::getMobile , mobile);
        PersonnalBasicDO ba =   personnalMapper.selectOne(lambdaQueryWrapperX.last("limit 1"));


        if(ObjectUtil.isEmpty(ba)){

            LambdaQueryWrapperX<PersonnalBasicDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
            lambdaQueryWrapper.eq(PersonnalBasicDO::getName , name);
            List<PersonnalBasicDO> list =   personnalMapper.selectList(lambdaQueryWrapperX);


            if(CollectionUtil.isNotEmpty(list)&&list.size()==1){

                ba = list.get(0);

            }


        }



        PersonnalApiDO apiDO =  PersonnalConvert.INSTANCE.convert(ba);


        return apiDO;

    }

    @Override
    public void sendAllPersonInfo(String url) {
        personnalService.sendAllPersonInfo(url);
    }
}
