package com.unicom.swdx.module.hr.dal.dataobject.personnal.personnal;

import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateTransferReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalTransferDetailResqVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalTransferDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PersonnalTransferConvert {

    PersonnalTransferConvert INSTANCE = Mappers.getMapper(PersonnalTransferConvert.class);

    PersonnalTransferDO convert(PersonnalCreateTransferReqVO bean);

    PersonnalTransferDetailResqVO convertDetail(PersonnalTransferDO bean);

//    List<PersonnalWorkDO> convert(List<PersonnalWorkVO> bean);
//
//    List<PersonnalWorkGetVO> convert0(List<PersonnalWorkDO> bean);
//
//    List<PersonnalWorkDO> convert1(List<PersonnalWorkGetVO> bean);
//    List<PersonnalWorkDO> convert2(List<PersonnalWorkExcelVO> bean);
//    List<PersonnalWorkDO> convertList(List<PersonnalWorkExcelVO> bean);
}
