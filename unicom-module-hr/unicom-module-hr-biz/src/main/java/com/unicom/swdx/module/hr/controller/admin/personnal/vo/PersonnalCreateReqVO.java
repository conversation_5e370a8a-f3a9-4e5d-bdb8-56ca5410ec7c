package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@ApiModel("人事管理 - 新增人事信息 Request VO")
@Data
public class PersonnalCreateReqVO {
    private PersonnalBasicVO basicVO;
//    private PersonnalPartyVO partyVO;
    private PersonnalStudyVO studyVO;
    private PersonnalPositionVO positionVO;
    private List<PersonnalWorkVO> workVOList;
}
