package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicBook;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicBookVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitPublicBookMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitPublicBookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitPublicBookServiceImpl extends ServiceImpl<RecruitPublicBookMapper, RecruitPublicBook> implements RecruitPublicBookService {

    @Resource
    RecruitPublicBookMapper recruitPublicBookMapper ;


    @Override
    public PageResult<RecruitPublicBook> queryByList(RecruitPublicBookVO recruitPublicBookVO) {

        IPage<RecruitPublicBook> page = MyBatisUtils.buildPage(recruitPublicBookVO);
        List<RecruitPublicBook> data = recruitPublicBookMapper.queryByList(page, recruitPublicBookVO);

        return new PageResult<>(data, page.getTotal());
    }
}
