package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("人事信息分页 Response VO")
@Data
@ToString(callSuper = true)
public class PersonnalTransferExcelVO {


//    @ApiModelProperty(value = "人事编号", required = true, example = "1")
//    private Long id;
    @ExcelProperty(value = "用户id")
    private Long userId;
    @ExcelProperty(value = "姓名")
    private String name;
    /**
     * 调动时间
     */
    @ExcelProperty(value = "调动时间")
    private LocalDateTime transferTime;
    /**
     * 调出部门名称
     */
    @ExcelProperty(value = "调出部门")
    private String outDeptNames;

    /**
     * 调出科室
     */
    @ExcelProperty(value = "调出科室")
    private String outSubjectRoom;
    /**
     * 调入部门名称
     */
    @ExcelProperty(value = "调入部门")
    private String inDeptNames;


    /**
     * 调动原因
     */
    @ExcelProperty(value = "调动原因")
    private String transferReason;
//    /**
//     * 任务创建时间
//     */
//    @ExcelProperty(value = "任务创建时间")
//    private LocalDateTime createTime;

    /**
     * 职级
     */
    @ExcelProperty(value = "调出职级",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_PERSON_RANK)
    private Integer outRank;

    /**
     * 行政职务名称
     */
    @ExcelProperty(value = "行政职务名称")
    private String outAdministrativePositionName;

    /**
     * 行政职务级别
     */
    @ExcelProperty(value = "调入行政职务级别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_ADMINISTRATIVE_POSITION_RANK)
    private Integer outAdministrativePositionRank;


    /**
     * 调入职级
     */
    @ExcelProperty(value = "调入职级",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_PERSON_RANK)
    private Integer inRank;

    /**
     * 调入行政职务名称
     */
    @ExcelProperty(value = "调入行政职务名称")
    private String inAdministrativePositionName;

    /**
     * 调入行政职务级别
     */
    @ExcelProperty(value = "调入行政职务级别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_ADMINISTRATIVE_POSITION_RANK)
    private Integer inAdministrativePositionRank;

    /**
     * 调入科室
     */
    @ExcelProperty(value = "调入科室")
    private String inSubjectRoom;



    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String notes;



}
