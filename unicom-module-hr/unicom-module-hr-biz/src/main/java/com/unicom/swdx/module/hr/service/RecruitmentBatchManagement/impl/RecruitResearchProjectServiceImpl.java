package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitResearchProject;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitResearchProjectVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitResearchProjectMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitResearchProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitResearchProjectServiceImpl extends ServiceImpl<RecruitResearchProjectMapper, RecruitResearchProject> implements RecruitResearchProjectService {

    @Resource
    RecruitResearchProjectMapper recruitResearchProjectMapper ;


    @Override
    public PageResult<RecruitResearchProject> queryByList(RecruitResearchProjectVO recruitResearchProjectVO) {

        IPage<RecruitResearchProject> page = MyBatisUtils.buildPage(recruitResearchProjectVO);
        List<RecruitResearchProject> data = recruitResearchProjectMapper.queryByList(page, recruitResearchProjectVO);

        return new PageResult<>(data, page.getTotal());
    }
}
