package com.unicom.swdx.module.hr.convert.personnal;

import com.unicom.swdx.module.hr.api.dto.PersonnalApiDO;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.hr.api.dto.PersonnalUpdateReqDTO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PersonnalConvert {

    PersonnalConvert INSTANCE = Mappers.getMapper(PersonnalConvert.class);

    PersonnalBasicDO convert(PersonnalBasicVO bean);
    PersonnalBasicDO convert(PersonnalBasicEntryVO bean);

    PersonnalBasicDO convert(PersonnalCreateRegistrationVO bean);
    PersonnalBasicGetVO convert0(PersonnalBasicDO bean);
    PersonnalSimpleRespVO convert3(PersonnalBasicDO bean);
    PersonnalBasicDO convert1(PersonnalBasicGetVO bean);
    PersonnalBasicDO convert2(PersonnalImportExcelVO bean);
    List<PersonnalBasicDO> convertList(List<PersonnalImportExcelVO> bean);

    PersonnalApiDO convert(PersonnalBasicDO bean);

    PersonnalBasicGetVO convertDTO(PersonnalUpdateReqDTO bean);

    default KafkaPersonDTO convertToKafkaPerson(PersonnalCreateReqVO reqVO){
        KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
        kafkaPersonDTO.setPersonnalStatus(reqVO.getBasicVO().getPersonnalStatus());
        kafkaPersonDTO.setPeronClassification(reqVO.getBasicVO().getPeronClassification());
        kafkaPersonDTO.setName(reqVO.getBasicVO().getName());
        kafkaPersonDTO.setMobile(reqVO.getBasicVO().getMobile());
        kafkaPersonDTO.setGender(reqVO.getBasicVO().getGender());
        kafkaPersonDTO.setDepartment(reqVO.getBasicVO().getDepartment());
        kafkaPersonDTO.setEducation(reqVO.getStudyVO().getEducation());
        kafkaPersonDTO.setAcademicDegree(reqVO.getStudyVO().getAcademicDegree());
        kafkaPersonDTO.setAdministrativePositionName(reqVO.getPositionVO().getAdministrativePositionName());
        return kafkaPersonDTO;
    }

    default KafkaPersonDTO convertToKafkaPerson2(PersonnalUpdateReqVO reqVO){
        KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
        kafkaPersonDTO.setPersonnalStatus(reqVO.getBasicVO().getPersonnalStatus());
        kafkaPersonDTO.setPeronClassification(reqVO.getBasicVO().getPeronClassification());
        kafkaPersonDTO.setName(reqVO.getBasicVO().getName());
        kafkaPersonDTO.setMobile(reqVO.getBasicVO().getMobile());
        kafkaPersonDTO.setGender(reqVO.getBasicVO().getGender());
        kafkaPersonDTO.setDepartment(reqVO.getBasicVO().getDepartment());
        kafkaPersonDTO.setEducation(reqVO.getStudyVO().getEducation());
        kafkaPersonDTO.setAcademicDegree(reqVO.getStudyVO().getAcademicDegree());
        kafkaPersonDTO.setAdministrativePositionName(reqVO.getPositionVO().getAdministrativePositionName());
        kafkaPersonDTO.setUserId(reqVO.getBasicVO().getUserId());
        return kafkaPersonDTO;
    }


    default KafkaPersonDTO convertToKafkaPerson6(PersonnalGetRespVO reqVO){
        KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
        kafkaPersonDTO.setPersonnalStatus(reqVO.getBasicVO().getPersonnalStatus());
        kafkaPersonDTO.setPeronClassification(reqVO.getBasicVO().getPeronClassification());
        kafkaPersonDTO.setName(reqVO.getBasicVO().getName());
        kafkaPersonDTO.setMobile(reqVO.getBasicVO().getMobile());
        kafkaPersonDTO.setGender(reqVO.getBasicVO().getGender());
        kafkaPersonDTO.setDepartment(reqVO.getBasicVO().getDepartment());
        kafkaPersonDTO.setEducation(reqVO.getStudyVO().getEducation());
        kafkaPersonDTO.setAcademicDegree(reqVO.getStudyVO().getAcademicDegree());
        kafkaPersonDTO.setAdministrativePositionName(reqVO.getPositionVO().getAdministrativePositionName());
        kafkaPersonDTO.setUserId(reqVO.getBasicVO().getUserId());
        return kafkaPersonDTO;
    }

    default KafkaPersonDTO convertToKafkaPerson3(PersonnalUpdateReqVO reqVO){
        KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
        kafkaPersonDTO.setPersonnalStatus(reqVO.getBasicVO().getPersonnalStatus());
        kafkaPersonDTO.setPeronClassification(reqVO.getBasicVO().getPeronClassification());
        kafkaPersonDTO.setName(reqVO.getBasicVO().getName());
        kafkaPersonDTO.setMobile(reqVO.getBasicVO().getMobile());
        kafkaPersonDTO.setGender(reqVO.getBasicVO().getGender());
        kafkaPersonDTO.setDepartment(reqVO.getBasicVO().getDepartment());
        kafkaPersonDTO.setEducation(reqVO.getStudyVO().getEducation());
        kafkaPersonDTO.setAcademicDegree(reqVO.getStudyVO().getAcademicDegree());
        kafkaPersonDTO.setAdministrativePositionName(reqVO.getPositionVO().getAdministrativePositionName());
        kafkaPersonDTO.setUserId(reqVO.getBasicVO().getUserId());
        return kafkaPersonDTO;
    }

    KafkaPersonDTO convertToKafkaPerson3(PersonnalImportExcelVO importPersonnal);

    default KafkaPersonDTO convertToKafkaPerson4(PersonnalBasicGetVO reqVO){
        KafkaPersonDTO kafkaPersonDTO = new KafkaPersonDTO();
        kafkaPersonDTO.setPersonnalStatus(reqVO.getPersonnalStatus());
        kafkaPersonDTO.setPeronClassification(reqVO.getPeronClassification());
        kafkaPersonDTO.setName(reqVO.getName());
        kafkaPersonDTO.setMobile(reqVO.getMobile());
        kafkaPersonDTO.setGender(reqVO.getGender());
        kafkaPersonDTO.setDepartment(reqVO.getDepartment());
        kafkaPersonDTO.setUserId(reqVO.getUserId());
//        kafkaPersonDTO.setEducation(reqVO.getStudyVO().getEducation());
//        kafkaPersonDTO.setAcademicDegree(reqVO.getStudyVO().getAcademicDegree());
//        kafkaPersonDTO.setAdministrativePositionName(reqVO.getPositionVO().getAdministrativePositionName());
        return kafkaPersonDTO;
    }
}
