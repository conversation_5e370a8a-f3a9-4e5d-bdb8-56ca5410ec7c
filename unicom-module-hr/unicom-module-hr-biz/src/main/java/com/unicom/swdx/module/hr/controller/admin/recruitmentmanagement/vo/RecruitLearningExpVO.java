package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "RecruitLearningExp", description = "学习经历表")
public class RecruitLearningExpVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 759655775072490016L;

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 学历层级
     */
    @ApiModelProperty(value = "学历层级")
    private String educationLevel;
    /**
     * 学位层级
     */
    @ApiModelProperty(value = "学位层级")
    private String academicDegree;
    /**
     * 毕业日期
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "毕业日期")
    private LocalDateTime graduationDate;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String schoolName;
    /**
     * 研究方向
     */
    @ApiModelProperty(value = "研究方向")
    private String researchDirection;
    /**
     * 学习形式（全日制，非全日制）
     */
    @ApiModelProperty(value = "学习形式（全日制，非全日制）")
    private String learningForm;
}
