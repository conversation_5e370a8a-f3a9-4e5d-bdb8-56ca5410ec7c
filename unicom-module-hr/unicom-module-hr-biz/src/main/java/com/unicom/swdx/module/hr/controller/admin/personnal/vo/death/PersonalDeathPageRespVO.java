package com.unicom.swdx.module.hr.controller.admin.personnal.vo.death;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("人事信息分页-去世管理 Response VO")
@Data
@ToString(callSuper = true)
public class PersonalDeathPageRespVO {

    @ApiModelProperty(value = "人事编号", required = true, example = "1")
    @NotNull(message = "人事编号不能为空")
    private Long id;

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "工作证号", example = "202410001")
    private String workId;

    @ApiModelProperty(value = "部门全称",example = "1")
    private String department;

    private Long userId;

    private Long deptId;

    private List<Long> deptIds;

    @ApiModelProperty(value = "性别",example = "1")

    private Integer gender;

    @ApiModelProperty(value = "人员分类",example = "1")

    private Integer peronClassification;

    @ApiModelProperty(value = "人员状态",example = "1")

    private Integer personnalStatus;

    @ApiModelProperty(value = "去世时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime deathTime;

    @ApiModelProperty(value = "去世前状态")
    private String deathStatus;

}
