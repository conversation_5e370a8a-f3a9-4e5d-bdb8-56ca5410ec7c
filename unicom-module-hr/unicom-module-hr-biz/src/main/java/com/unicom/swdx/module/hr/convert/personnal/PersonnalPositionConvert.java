package com.unicom.swdx.module.hr.convert.personnal;



import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalImportExcelVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPositionGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPositionVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PersonnalPositionConvert {

    PersonnalPositionConvert INSTANCE = Mappers.getMapper(PersonnalPositionConvert.class);

    PersonnalPositionDO convert(PersonnalPositionVO bean);
    PersonnalPositionGetVO convert0(PersonnalPositionDO bean);
    PersonnalPositionDO convert1(PersonnalPositionGetVO bean);
    PersonnalPositionDO convert2(PersonnalImportExcelVO bean);
    List<PersonnalPositionDO> convertList(List<PersonnalImportExcelVO> bean);
}
