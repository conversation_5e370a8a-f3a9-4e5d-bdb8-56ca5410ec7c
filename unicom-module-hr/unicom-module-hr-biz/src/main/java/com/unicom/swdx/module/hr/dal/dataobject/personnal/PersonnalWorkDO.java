package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_work_experience", autoResultMap = true)
@KeySequence("hr_personnel_work_experience_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalWorkDO extends BaseDO {
    /**
     * 人事ID
     */
    @TableField(value = "personnel_id")
    private Long personnalId;
    /**
     * 机构ID
     */
    private Long tenantId;
    /**
     * 租户编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 担任职务
     */
    private String position;
    /**
     * 工作内容
     */
    private String workContent;

}
