package com.unicom.swdx.module.hr.controller.admin.personnal.vo.death;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class PersonalDeathUpdateVO {
    @ApiModelProperty(value = "编号", required = true, example = "1")
    @NotNull(message = "编号不能为空")
    private Long id;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "去世时间", example = "2024-01-01 00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime deathTime;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "去世前状态", example = "这是一条途径")
    private String deathStatus;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "去世备注", required = true, example = "这是一条备注")
    private String deathRemarks;
}

