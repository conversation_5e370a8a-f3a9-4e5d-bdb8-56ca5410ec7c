package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitLearningExp;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitLearningExpVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitLearningExpMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitLearningExpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitLearningExpServiceImpl extends ServiceImpl<RecruitLearningExpMapper, RecruitLearningExp> implements RecruitLearningExpService {

    @Resource
    RecruitLearningExpMapper recruitLearningExpMapper ;


    @Override
    public PageResult<RecruitLearningExp> queryByList(RecruitLearningExpVO recruitLearningExpVO) {

        IPage<RecruitLearningExp> page = MyBatisUtils.buildPage(recruitLearningExpVO);
        List<RecruitLearningExp> data = recruitLearningExpMapper.queryByList(page, recruitLearningExpVO);

        return new PageResult<>(data, page.getTotal());
    }
}
