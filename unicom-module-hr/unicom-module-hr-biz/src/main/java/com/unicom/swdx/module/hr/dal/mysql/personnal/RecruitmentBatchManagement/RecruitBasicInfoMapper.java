package com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitBasicInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:13
 */

@Mapper
public interface RecruitBasicInfoMapper extends BaseMapperX<RecruitBasicInfo> {

    List<RecruitBasicInfo> queryByList(IPage page, @Param("param") RecruitBasicInfoVO recruitBasicInfoVO);

    List<RecruitBasicInfo> faceByList(IPage page, @Param("param") RecruitBasicInfoVO recruitBasicInfoVO);

    List<RecruitBasicInfoRespVO> getByUserId(Long loginUserId);

    List<RecruitStationVO> getStationPage(IPage page, @Param("param") RecruitStationRequestVO recruitStationRequestV);

    RecruitInformationVO getInformationById(Long id);
    List<Long> getMobilesByIds(@Param("ids")List<Long> ids);
}
