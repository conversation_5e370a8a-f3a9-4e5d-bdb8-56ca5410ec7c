package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hr_recruit_research_achievement")
@ApiModel(value = "RecruitResearchAchievement", description = "科研成果，获奖表")
public class RecruitResearchAchievement implements Serializable {

    private static final long serialVersionUID = 728650852695539761L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 获奖时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "获奖时间")
    private LocalDateTime awardTime;
    /**
     * 获奖名称
     */
    @ApiModelProperty(value = "获奖名称")
    private String awardName;
    /**
     * 颁奖单位
     */
    @ApiModelProperty(value = "颁奖单位")
    private String awardUnit;
    /**
     * 获奖级别
     */
    @ApiModelProperty(value = "获奖级别")
    private String awardLever;
    /**
     * 角色排名（署名情况）
     */
    @ApiModelProperty(value = "角色排名（个人署名情况）")
    private String ranking;
}
