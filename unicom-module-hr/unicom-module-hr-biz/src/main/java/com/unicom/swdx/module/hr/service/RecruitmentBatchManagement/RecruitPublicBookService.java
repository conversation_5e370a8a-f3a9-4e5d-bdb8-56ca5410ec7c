package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitAccessoryInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicBook;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitAccessoryInfoVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicBookVO;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */
public interface RecruitPublicBookService extends IService<RecruitPublicBook> {

    PageResult<RecruitPublicBook> queryByList(RecruitPublicBookVO recruitPublicBookVO);


}
