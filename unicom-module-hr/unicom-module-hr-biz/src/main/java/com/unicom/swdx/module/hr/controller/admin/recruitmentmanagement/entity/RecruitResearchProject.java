package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hr_recruit_research_project")
@ApiModel(value = "RecruitResearchProject", description = "科研咨询课题表")
public class RecruitResearchProject implements Serializable {

    private static final long serialVersionUID = 619951462166988548L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 立项日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "立项日期")
    private LocalDateTime projectApprovalTime;
    /**
     * 结项日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结项日期")
    private LocalDateTime junctionTime;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别")
    private String projectLevel;
    /**
     * 本人角色（参与，负责人等）
     */
    @ApiModelProperty(value = "本人角色（参与，负责人等）")
    private String personalRole;
    /**
     * 本人排名（署名情况）
     */
    @ApiModelProperty(value = "本人排名（署名情况）")
    private String ranking;
    /**
     * 项目批准部门
     */
    @ApiModelProperty(value = "项目批准部门")
    private String approvingDepartment;
}
