package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitWorkExp;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitWorkExpVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitWorkExpMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitWorkExpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitWorkExpServiceImpl extends ServiceImpl<RecruitWorkExpMapper, RecruitWorkExp> implements RecruitWorkExpService {

    @Resource
    RecruitWorkExpMapper recruitWorkExpMapper ;


    @Override
    public PageResult<RecruitWorkExp> queryByList(RecruitWorkExpVO recruitWorkExpVO) {

        IPage<RecruitWorkExp> page = MyBatisUtils.buildPage(recruitWorkExpVO);
        List<RecruitWorkExp> data = recruitWorkExpMapper.queryByList(page, recruitWorkExpVO);

        return new PageResult<>(data, page.getTotal());
    }
}
