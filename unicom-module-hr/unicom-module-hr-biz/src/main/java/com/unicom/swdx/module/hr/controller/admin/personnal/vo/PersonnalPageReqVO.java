package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import cn.hutool.core.date.DatePattern;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@ApiModel("人事信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonnalPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "王二")

    private String name;

    @ApiModelProperty(value = "工作证号", example = "202410001")

    private String workId;

    @ApiModelProperty(value = "性别",example = "1")
    private Integer gender;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;

    @ApiModelProperty(value = "人员分类",example = "1")
    private Integer peronClassification;

    @ApiModelProperty(value = "人员状态",example = "1")
    private Integer personnalStatus;

    @ApiModelProperty(value = "获得学历",example = "大学本科毕业")
    private String education;

    @ApiModelProperty(value = "手机号码",example = "1")
    private String mobile;


    @ApiModelProperty(value = "行政职务级别",example = "正处")
    private String administrativePositionRank;

    @ApiModelProperty(value = "专业技术岗位级别",example = "1")
    private Integer professionalTechnicalRank;

    @ApiModelProperty(value = "专业技术职务",example = "1")
    private Integer professionalTechnicalName;


}
