package com.unicom.swdx.module.hr.service.kafka.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.hr.dal.dataobject.kafka.KafkaMessageDO;
import com.unicom.swdx.module.hr.dal.mysql.kafka.KafkaMessageMapper;
import com.unicom.swdx.module.hr.service.kafka.KafkaMessageService;
import org.springframework.stereotype.Service;

@Service
public class KafkaMessageServiceImpl extends ServiceImpl<KafkaMessageMapper, KafkaMessageDO> implements KafkaMessageService {

}
