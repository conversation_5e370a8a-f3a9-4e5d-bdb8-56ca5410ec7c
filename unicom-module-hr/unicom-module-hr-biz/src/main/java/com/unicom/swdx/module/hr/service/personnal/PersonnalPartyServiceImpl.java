package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalPartyConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPartyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalMapper;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalPartyMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

/**
 * 人事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonnalPartyServiceImpl extends ServiceImpl<PersonnalPartyMapper, PersonnalPartyDO> implements PersonnalPartyService {
    @Resource
    private PersonnalPartyMapper personnalPartyMapper;
    @Resource
    private PersonnalMapper personnalMapper;
    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnal(PersonnalPartyVO createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PersonnalPartyDO party = PersonnalPartyConvert.INSTANCE.convert(createReqVO);
        party.setPersonnalId(personnalId);
        personnalPartyMapper.insert(party);

    }
    @Override
    public PersonnalPartyDO getPersonnal(Long id) {
        return personnalPartyMapper.selectByPersonnalId(id);
    }
    @Override
    public void updatePersonnal(PersonnalPartyGetVO updateReqVO) {
        // 校验正确性
//        this.checkCreateOrUpdate(reqVO.getId(), reqVO.getName(), reqVO.getCode(), getTenantId());
        // 更新岗位
        PersonnalPartyDO updateObj = PersonnalPartyConvert.INSTANCE.convert1(updateReqVO);

        personnalPartyMapper.updateByPersonnalId(updateObj);
    }
}
