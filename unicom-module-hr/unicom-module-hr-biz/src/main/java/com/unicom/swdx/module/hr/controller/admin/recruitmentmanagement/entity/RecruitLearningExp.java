package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hr_recruit_learning_exp")
@KeySequence("hr_recruit_learning_exp_seq")
@ApiModel(value = "RecruitLearningExp", description = "学习经历表")
public class RecruitLearningExp implements Serializable {

    private static final long serialVersionUID = 759655775072490016L;

    /**
     * 时间格式
     */
    private static final String DATE = "yyyyMMdd";
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 学历层级
     */
    @ApiModelProperty(value = "学历层级")
    private String educationLevel;
    /**
     * 学位层级
     */
    @ApiModelProperty(value = "学位层级")
    private String academicDegree;
    /**
     * 毕业日期
     */
    @JsonFormat(pattern = DATE)
    @ApiModelProperty(value = "毕业日期")
    private LocalDate graduationDate;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @TableField(value = "school")
    private String schoolName;
    /**
     * 研究方向
     */
    @ApiModelProperty(value = "研究方向")
    private String researchDirection;
    /**
     * 学习形式（全日制，非全日制）
     */
    @ApiModelProperty(value = "学习形式（全日制，非全日制）")
    @TableField(value = "is_full_time")
    private String learningForm;
    /**
     * 入学日期
     */
    @JsonFormat(pattern = DATE)
    @ApiModelProperty(value = "入学日期")
    private LocalDate enrollmentDate;
}
