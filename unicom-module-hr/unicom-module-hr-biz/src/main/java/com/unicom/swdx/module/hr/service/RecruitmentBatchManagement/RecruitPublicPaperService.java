package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicBook;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicPaper;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicBookVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicPaperVO;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */
public interface RecruitPublicPaperService extends IService<RecruitPublicPaper> {

    PageResult<RecruitPublicPaper> queryByList(RecruitPublicPaperVO RecruitPublicPaperVO);


}
