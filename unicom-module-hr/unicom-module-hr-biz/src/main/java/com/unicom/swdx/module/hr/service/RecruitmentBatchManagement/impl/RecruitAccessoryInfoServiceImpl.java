package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitAccessoryInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitAccessoryInfoVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitAccessoryInfoMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitAccessoryInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitAccessoryInfoServiceImpl extends ServiceImpl<RecruitAccessoryInfoMapper, RecruitAccessoryInfo> implements RecruitAccessoryInfoService {

    @Resource
    RecruitAccessoryInfoMapper recruitAccessoryInfoMapper ;


    @Override
    public PageResult<RecruitAccessoryInfo> queryByList(RecruitAccessoryInfoVO recruitAccessoryInfoVO) {

        IPage<RecruitAccessoryInfo> page = MyBatisUtils.buildPage(recruitAccessoryInfoVO);
        List<RecruitAccessoryInfo> data = recruitAccessoryInfoMapper.queryByList(page, recruitAccessoryInfoVO);

        return new PageResult<>(data, page.getTotal());
    }
}
