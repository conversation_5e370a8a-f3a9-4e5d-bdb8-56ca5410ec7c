package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonnalCreateRegistrationVO {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true, example = "张三")
    @NotNull(message = "姓名不能为空")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String name;
    /**
     * 证件类型
     * 枚举
     */
    @ApiModelProperty(value = "证件类型")
    private Integer idType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码",example = "430521200001011234")

    private String idNumber;
    /**
     * 性别
     * 枚举
     */
    @ApiModelProperty(value = "性别", example = "1")

    private Integer gender;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", example = "2000-01-01T00:00:00")
    private LocalDateTime birthday;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13112345678")
    @NotNull(message = "手机号码不能为空")
    private String mobile;

    /**
     * 人员分类
     * 枚举
     */
    @ApiModelProperty(value = "人员分类", example = "1")
    @NotNull(message = "人员分类不能为空")
    private Integer peronClassification;

    /**
     * 部门
     * 枚举
     */
    @ApiModelProperty(value = "部门", example = "1")
    private Long department;

    @ApiModelProperty(value = "部门", example = "1")
    @NotNull(message = "部门不能为空")
    private List<Long> deptIds;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱", example = "<EMAIL>")
    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String email;

    @ApiModelProperty(value = "录用方式")
    private Integer recruitmentMethod;

    @ApiModelProperty(value = "报到日期", example = "2021-01-01T00:00:00")
    //@NotNull(message = "报到日期不能为空")
    private LocalDateTime registrationDate;

    @ApiModelProperty(value = "试用期截至日期", example = "2021-01-01T00:00:00")
    private LocalDateTime trialDeadline;


}
