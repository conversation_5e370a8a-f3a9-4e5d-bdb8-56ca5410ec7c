package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentBatchManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentBatchManagementVO;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitmentBatchManagementService;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

/**
 * 招聘批次管理(HrRecruitmentBatchManagement)表控制层
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:36
 */

@RestController
@RequestMapping("/hr/recruit")
public class RecruitmentBatchManagementController {

    /**
     * 服务对象
     */
    @Resource
    private RecruitmentBatchManagementService recruitmentBatchManagementService;

    /**
     * 分页查询
     *
     * @param recruitmentBatchManagementVO  传输实体类
     * @return 查询结果
     *
     */
    @GetMapping("/list")
    @ApiOperation("信息分页")
    @PreAuthorize("@ss.hasPermission('hr:recruit:list')")
    public CommonResult<PageResult<RecruitmentBatchManagement>> queryByList(@Valid RecruitmentBatchManagementVO recruitmentBatchManagementVO) {
        PageResult<RecruitmentBatchManagement> pageResult = recruitmentBatchManagementService.queryByList(recruitmentBatchManagementVO);
        return success(pageResult);
    }

    @GetMapping("/listAll")
    @ApiOperation("全部信息")
    @PreAuthorize("@ss.hasPermission('hr:recruit:list')")
    public CommonResult<List<RecruitmentBatchManagement>> queryAll(@Valid RecruitmentBatchManagementVO recruitmentBatchManagementVO) {
        List<RecruitmentBatchManagement> pageResult = recruitmentBatchManagementService.queryAll(recruitmentBatchManagementVO);
        return success(pageResult);
    }

    /**
     * 新增数据
     *
     * @param recruitmentBatchManagement  实体类
     * @return 查询结果
     *
     */
    @PostMapping("/add")
    @ApiOperation("新增数据")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:add')")
    public ResponseEntity<Map<String, Object>> add(@RequestBody RecruitmentBatchManagement recruitmentBatchManagement) {
        return recruitmentBatchManagementService.add(recruitmentBatchManagement);
    }

    /**
     * 编辑数据
     *
     * @param recruitmentBatchManagement  实体类
     * @return 查询结果
     *
     */
    @PostMapping("/edit")
    @ApiOperation("编辑数据")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:update')")
    public ResponseEntity<Map<String, Object>> edit(@RequestBody RecruitmentBatchManagement recruitmentBatchManagement) {
        return recruitmentBatchManagementService.edit(recruitmentBatchManagement);
    }

    /**
     * 删除数据
     *
     * @param recruitmentBatchManagement
     * @return 查询结果
     *
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    @OperateLog(type = DELETE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:del')")
    public ResponseEntity<String> delete(@RequestBody RecruitmentBatchManagement recruitmentBatchManagement) {
        return recruitmentBatchManagementService.delete(recruitmentBatchManagement);
    }

    /**
     * 启用
     *
     * @param recruitmentBatchManagement  实体类
     * @return 查询结果
     *
     */
    @PostMapping("/enable")
    @ApiOperation("是否启用")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:enable')")
    public ResponseEntity<String> enable(@RequestBody RecruitmentBatchManagement recruitmentBatchManagement) {
        return recruitmentBatchManagementService.enable(recruitmentBatchManagement);
    }
}
