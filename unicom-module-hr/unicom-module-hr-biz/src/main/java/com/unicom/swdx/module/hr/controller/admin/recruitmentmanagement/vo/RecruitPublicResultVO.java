package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "RecruitPublicResult", description = "决策咨询成果批示、发表情况")
public class RecruitPublicResultVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 291284685977724998L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 成果获取时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "成果获取时间")
    private LocalDateTime outcomeTime;
    /**
     * 成果名称
     */
    @ApiModelProperty(value = "成果名称")
    private String outcomeName;
    /**
     * 领导批示情况
     */
    @ApiModelProperty(value = "领导批示情况")
    private String instructionSituation;
    /**
     * 发表刊物级别
     */
    @ApiModelProperty(value = "发表刊物级别")
    private String journalLevel;
    /**
     * 角色排名（个人署名情况）
     */
    @ApiModelProperty(value = "角色排名（个人署名情况）")
    private String ranking;
}
