package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
public class RecruitBasicInfoRespVO implements Serializable {

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 审核状态
     *       1-待审核
     *       2-不通过
     *       3-待面试审核
     *       4-面试审核不通过
     *       5-拟录用
     *       6-草稿
     *       7-退回修改
     *       8-退回修改后重新申请
     */
    @ApiModelProperty(value = "审核状态")
    private Integer status;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String jobTitle;

    /**
     * 招聘单位
     */
    @ApiModelProperty(value = "招聘单位")
    private String recruitmentUnit;

    /**
     * 应聘部门
     */
    @ApiModelProperty(value = "应聘部门")
    private String recruitmentDept;

    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;
    /**
     * 修改意见
     */
    @ApiModelProperty(value = "修改意见")
    private String modifySuggestion;
}
