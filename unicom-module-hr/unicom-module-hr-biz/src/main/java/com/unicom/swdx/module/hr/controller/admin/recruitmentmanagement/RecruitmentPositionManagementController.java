package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentPositionManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentPositionManagementVO;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitmentPositionManagementService;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

/**
 * 招聘职位管理(RecruitmentPositionManagement)表控制层
 * <AUTHOR>
 * @data 2024/3/4 15:03
 */
@RestController
@RequestMapping("/hr/recruit/position")
public class RecruitmentPositionManagementController {

    /**
     * 服务对象
     */
    @Resource
    private RecruitmentPositionManagementService recruitmentPositionManagementService;

    /**
     * 分页查询
     *
     * @param recruitmentPositionManagementVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/list")
    @ApiOperation("信息分页")
    @PreAuthorize("@ss.hasPermission('hr:recruit:listp')")
    public CommonResult<PageResult<RecruitmentPositionManagement>> queryByList(@RequestBody RecruitmentPositionManagementVO recruitmentPositionManagementVO) {
        PageResult<RecruitmentPositionManagement> pageResult = recruitmentPositionManagementService.queryByList(recruitmentPositionManagementVO);
        return success(pageResult);
    }

    /**
     * 新增数据
     *
     * @param recruitmentPositionManagement  实体类
     * @return 新增结果
     *
     */
    @PostMapping("/add")
    @ApiOperation("新增数据")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:addp')")
    public ResponseEntity<Map<String, Object>> add(@RequestBody RecruitmentPositionManagement recruitmentPositionManagement) {
        return recruitmentPositionManagementService.add(recruitmentPositionManagement);
    }

    /**
     * 编辑数据
     *
     * @param recruitmentPositionManagement  实体类
     * @return 编辑结果
     *
     */
    @PostMapping("/edit")
    @ApiOperation("编辑数据")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:updatep')")
    public ResponseEntity<Map<String, Object>> edit(@RequestBody RecruitmentPositionManagement recruitmentPositionManagement) {
        return recruitmentPositionManagementService.edit(recruitmentPositionManagement);
    }

    /**
     * 删除数据
     *
     * @param recruitmentPositionManagement  实体类
     * @return 删除结果
     *
     */
    @PostMapping("/del")
    @ApiOperation("删除数据")
    @OperateLog(type = DELETE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:delp')")
    public ResponseEntity<String> del(@RequestBody RecruitmentPositionManagement recruitmentPositionManagement) {
        return recruitmentPositionManagementService.del(recruitmentPositionManagement);
    }

    /**
     * 发布
     *
     * @param id
     * @return 发布结果
     *
     */
    @GetMapping("/publish")
    @ApiOperation("发布")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:piblish')")
    public ResponseEntity<Map<String, Object>> publish(@RequestParam Integer id, String startTime, String cutTime) {
        return recruitmentPositionManagementService.publish(id, startTime, cutTime);
    }

    /**
     * 撤回
     *
     * @param id
     * @return 撤回结果
     *
     */
    @GetMapping("/recall")
    @ApiOperation("撤回")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:recall')")
    public ResponseEntity<Map<String, Object>> recall(@RequestParam Integer id) {
        return recruitmentPositionManagementService.recall(id);
    }

}
