package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;


/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hr_recruit_work_exp")
@KeySequence("hr_recruit_work_exp_seq")
@ApiModel(value = "RecruitWorkExp", description = "工作经历表")
public class RecruitWorkExp implements Serializable {

    private static final long serialVersionUID = 781982597708152275L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    private String workUnit;
    /**
     * 工作开始时间
     */
    @JsonFormat(pattern = "yyyyMMdd")
    @ApiModelProperty(value = "工作开始时间")
    private LocalDate startTime;
    /**
     * 工作结束时间
     */
    @JsonFormat(pattern = "yyyyMMdd")
    @ApiModelProperty(value = "工作结束时间")
    private LocalDate endTime;
    /**
     * 担任职务
     */
    @ApiModelProperty(value = "担任职务")
    @TableField(value = "position")
    private String jobTitle;

}
