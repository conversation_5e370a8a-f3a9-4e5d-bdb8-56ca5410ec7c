package com.unicom.swdx.module.hr.dal.dataobject.kafka;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import lombok.*;

import javax.servlet.annotation.HandlesTypes;
import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "kafka_message", autoResultMap = true)
@KeySequence("kafka_message_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KafkaMessageDO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 插入时间
     */
    private LocalDateTime createTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    private Long tenantId;

}
