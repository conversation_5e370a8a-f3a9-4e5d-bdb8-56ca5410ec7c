package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import lombok.Data;

/**
 * 人事信息Excel 导出响应 VO
 */
@Data
public class PersonnalExcelVO {

    @ExcelIgnore
    private Long userId;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "工作证号")
    private String workId;

    @ExcelProperty(value = "行政职务名称")
    private String administrativePositionName;

    @ExcelProperty(value = "行政职务级别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_ADMINISTRATIVE_POSITION_RANK)
    private Integer administrativePositionRank;

    @ExcelProperty(value = "部门全称")
//    @DictFormat(DictTypeConstants.PERSON_GENDER)
    private String departmentName;

    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSON_GENDER)
    private Integer gender;

    @ExcelProperty(value = "人员状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_STATUS)
    private Integer personnalStatus;

    @ExcelProperty(value = "手机号码")
    private String mobile;

    @ExcelProperty(value = "出生年月")
//    @DateTimeFormat(pattern = FORMAT_YEARMONTHDAY)
    private String birthday;

    @ExcelProperty(value = "籍贯")
    private String nativePlace;

    @ExcelProperty(value = "民族", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_NATION)
    private Integer  nation;

    @ExcelProperty(value = "人员分类", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_PERSON_CLASSIFICATION)
    private Integer peronClassification;

    @ExcelProperty(value = "获得学历", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_EDUCATION)
    private Integer education;

    @ExcelProperty(value = "获得学位", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_ACADEMIC_DEGREE)
    private Integer academicDegree;

    @ExcelProperty(value = "专业技术岗位级别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_TECHNICAL_RANK)
    private Integer professionalTechnicalRank;

    @ExcelProperty(value = "专业技术职称", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_TECHNICAL_NAME)
    private Integer professionalTechnicalName;
}
