package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonnalStudyVO  {

    /**
     * 毕业学校
     */
    @ApiModelProperty(value = "毕业学校")
    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    //@NotNull(message = "不能为空")
    private String graduationSchool;
    /**
     * 入学时间
     */
    @ApiModelProperty(value = "入学时间")
    //@NotNull(message = "不能为空")
    private LocalDateTime admissionDate;
    /**
     * 结束学业时间
     */
    @ApiModelProperty(value = "结束学业时间")
    //@NotNull(message = "不能为空")
    private LocalDateTime endStudyDate;
    /**
     * 获得学历（最高）
     */
    @ApiModelProperty(value = "获得学历（最高）")
    //@NotNull(message = "不能为空")
    private Integer education;
    /**
     * 获得学位（最高）
     */
    @ApiModelProperty(value = "获得学位（最高）")
    //@NotNull(message = "不能为空")
    private Integer academicDegree;

}
