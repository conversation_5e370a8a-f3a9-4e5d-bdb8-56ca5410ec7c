package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@ToString(callSuper = true)
@ApiModel(value = "RecruitStation", description = "招聘岗位")
public class RecruitInformationVO implements Serializable {

//    private static final long serialVersionUID = 619951462166988548L;
    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String positionName;
    /**
     * 招聘单位
     */
    @ApiModelProperty(value = "招聘单位")
    private String recruitmentUnit;
    /**
     * 招聘部门
     */
    @ApiModelProperty(value = "招聘部门")
    private String recruitmentDepartment;
    /**
     * 岗位类别，1-专业技术岗位,2-管理岗位,3-工勤技能岗位,4-其他岗位
     */
    @ApiModelProperty(value = "岗位类别")
    private Integer jobCategory;
    /**
     * 岗位代码
     */
    @ApiModelProperty(value = "岗位代码")
    private String postCode;
    /**
     * 学历学位
     */
    @ApiModelProperty(value = "学历学位")
    private String academicDegree;
    /**
     * 年龄要求
     */
    @ApiModelProperty(value = "年龄要求")
    private String age;
    /**
     * 招聘计划
     */
    @ApiModelProperty(value = "招聘计划")
    private String recruitmentPlan;
    /**
     * 发布开始时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "发布开始时间")
    private LocalDateTime startTime;
    /**
     * 发布截止时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "发布截止时间")
    private LocalDateTime cutTime;
    /**
     * 专业要求
     */
    @ApiModelProperty(value = "专业要求")
    private String profession;
    /**
     * 任职要求
     */
    @ApiModelProperty(value = "任职要求")
    private String jobRequirements;
    /**
     * 岗位职责
     */
    @ApiModelProperty(value = "岗位职责")
    private String jobResponsibility;
    /**
     * 其它要求
     */
    @ApiModelProperty(value = "其它要求")
    private String other;
    /**
     * 是否过期
     */
    @ApiModelProperty(value = "是否过期")
    private Boolean isOverdue;
    /**
     * 招聘类别
     */
    @ApiModelProperty(value = "招聘类别")
    private Integer recruitmentCategory;
}
