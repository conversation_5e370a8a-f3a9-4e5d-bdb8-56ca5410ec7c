package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonnalWorkVO {

    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String workUnit;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 担任职务
     */
    @ApiModelProperty(value = "担任职务")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String position;
    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @Length(min = 0, max = 500, message = "长度为 0-500 位")
    private String workContent;

}
