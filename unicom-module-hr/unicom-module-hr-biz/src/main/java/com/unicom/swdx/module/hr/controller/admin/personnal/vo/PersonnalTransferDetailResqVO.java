package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("人事调动 - 详情")
@Data
public class PersonnalTransferDetailResqVO {



    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    @ApiModelProperty(value = "用户id", example = "2")
    private Long userId;


    /**
     * 姓名
     */
    @NotNull(message = "用户姓名不能为空")
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 调出部门
     */
    @NotNull(message = "调出部门id不能为空")
    @ApiModelProperty(value = "调出部门id")
    private String outDeptIds;

    /**
     * 调出部门名称
     */
    @NotNull(message = "调出部门名称不能为空")
    @ApiModelProperty(value = "调出部门名称")
    private String outDeptNames;

    /**
     * 职级
     */
    @NotNull(message = "调出职级不能为空")
    @ApiModelProperty(value = "调出职级")
    private Integer outRank;

    /**
     * 行政职务名称
     */
    @NotNull(message = "调出行政职务名称不能为空")
    @ApiModelProperty(value = "行政职务名称")
    private String outAdministrativePositionName;

    /**
     * 行政职务级别
     */
    @NotNull(message = "调出行政职务级别不能为空")
    @ApiModelProperty(value = "调出行政职务级别")
    private Integer outAdministrativePositionRank;

    /**
     * 调出科室
     */
    @NotNull(message = "调出科室不能为空")
    @ApiModelProperty(value = "调出科室")
    private String outSubjectRoom;

    /**
     * 调入部门
     */
    @NotNull(message = "调入部门不能为空")
    @ApiModelProperty(value = "调入部门")
    private String inDeptIds;

    /**
     * 调入部门名称
     */
    @ApiModelProperty(value = "调入部门名称")
    private String inDeptNames;

    /**
     * 调入职级
     */
    @ApiModelProperty(value = "调入职级")
    private Integer inRank;

    /**
     * 调入行政职务名称
     */
    @ApiModelProperty(value = "调入行政职务名称")
    private String inAdministrativePositionName;

    /**
     * 调入行政职务级别
     */
    @ApiModelProperty(value = "调入行政职务级别")
    private Integer inAdministrativePositionRank;

    /**
     * 调入科室
     */
    @ApiModelProperty(value = "调入科室")
    private String inSubjectRoom;

    /**
     * 调动时间
     */
    @ApiModelProperty(value = "调动时间")
    private LocalDateTime transferTime;


    /**
     * 调动原因transfer_reason
     */
    @ApiModelProperty(value = "调动原因")
    private String transferReason;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String notes;

}
