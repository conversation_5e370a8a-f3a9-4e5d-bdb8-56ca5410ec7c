package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.module.system.api.user.dto.UserDeptDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("人事信息分页 Response VO")
@Data
@ToString(callSuper = true)
public class PersonnalReviewPageRespVO {


    @ApiModelProperty(value = "人事编号", example = "1")
    private Long id;

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "工作证号", example = "202410001")
    private String workId;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;

    @ApiModelProperty(value = "多部门")
    private List<Long> deptIds;

    private Long userId;

    @ApiModelProperty(value = "审核状态",example = "1")

    private Integer reviewStatus;

    @ApiModelProperty(value = "发起时间")
    private LocalDateTime updateTime;



}
