package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_registration", autoResultMap = true)
@KeySequence("hr_personnel_registration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalRegistrationDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 工作证号
     */
    private String workId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件类型
     * 枚举 {@link IdTypeEnum}
     */
    private Integer idType;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 性别
     * 枚举 {@link GenderEnum}
     */
    private Integer gender;


    /**
     * 出生日期
     */
    private LocalDateTime birthday;
    /**
     * 手机号码
     */
    private String mobile;


    /**
     * 人员分类
     * 枚举 {@link PersonClassificationEnum}
     */
    @TableField(value = "person_classification")
    private Integer peronClassification;

    /**
     * 人员状态
     * 枚举 {@link PersonnalStatusEnum}
     */
    @TableField(value = "person_status")
    private Integer personnalStatus;

    /**
     * 部门
     * 枚举 {@link DepartmentEnum}
     */
    @TableField(value = "dept_id")
    private Long department;


    private String email;


    /**
     * 录用方式
     * 枚举
     */
    private Integer recruitmentMethod;

    /**
     * 报到时间
     */
    private LocalDateTime registrationDate;
    /**
     * 是否有使用期
     */
    private Boolean ifTrial;
    /**
     * 试用期截至时间
     */
    private LocalDateTime trialDeadline;
}
