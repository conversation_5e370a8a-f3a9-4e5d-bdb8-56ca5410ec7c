package com.unicom.swdx.module.hr.dal.mysql.personnal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalRegistrationPageReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalRegistrationPageRespVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPartyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalRegistrationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PersonnalRegistrationMapper extends BaseMapperX<PersonnalRegistrationDO> {
//    List<PersonnalPageRespVO> selectPage(IPage page, @Param("param") PersonnalPageReqVO reqVO);
//    default PersonnalRegistrationDO selectByPersonnalId(Long personnalId) {
//        return selectOne(new LambdaQueryWrapper<PersonnalRegistrationDO>()
//                .eq(PersonnalRegistrationDO::getPersonnalId, personnalId));
//    }
//    default void updateByPersonnalId(PersonnalPartyDO entity) {
//        update(entity, new LambdaQueryWrapper<PersonnalPartyDO>().eq(PersonnalPartyDO::getPersonnalId, entity.getPersonnalId()));
//    }
    List<PersonnalRegistrationPageRespVO> selectRegistrationPage(IPage page, @Param("param") PersonnalRegistrationPageReqVO reqVO);
}
