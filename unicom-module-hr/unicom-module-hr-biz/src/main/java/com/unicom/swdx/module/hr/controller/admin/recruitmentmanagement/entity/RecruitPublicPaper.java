package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "recruit_public_paper")
@ApiModel(value = "RecruitPublicPaper", description = "公开发表论文表")
public class RecruitPublicPaper implements Serializable {

    private static final long serialVersionUID = -27002711565601547L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 论文发表时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "论文发表时间")
    private LocalDateTime publicationTime;
    /**
     * 论文题目
     */
    @ApiModelProperty(value = "论文题目")
    private String thesisTitle;
    /**
     * 刊物名称
     */
    @ApiModelProperty(value = "刊物名称")
    private String publicationTitle;
    /**
     * 论文等级
     */
    @ApiModelProperty(value = "论文等级")
    private String thesisLevel;
    /**
     * 本人排名（一作，共一等），本人署名情况
     */
    @ApiModelProperty(value = "本人排名（一作，共一等），本人署名情况")
    private String ranking;
}
