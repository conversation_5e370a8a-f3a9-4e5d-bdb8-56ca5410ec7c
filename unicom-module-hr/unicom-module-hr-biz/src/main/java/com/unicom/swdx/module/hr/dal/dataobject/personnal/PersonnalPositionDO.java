package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_position", autoResultMap = true)
@KeySequence("hr_personnel_position_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalPositionDO extends BaseDO {
    /**
     * 人事主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 人事ID
     */
    @TableField(value = "personnel_id")
    private Long personnalId;
    /**
     * 机构ID
     */
    private Long tenantId;
    /**
     * 职级
     */
    private Integer rank;
    /**
     * 行政职务名称
     */
    private String administrativePositionName;
    /**
     * 行政职务级别
     */
    private Integer administrativePositionRank;
    /**
     * 专业技术职称
     */

    private Integer professionalTechnicalName;
    /**
     * 专业技术岗位级别
     */
    private Integer professionalTechnicalRank;
    /**
     * 职务评选时间
     */
    private LocalDateTime jobSelectionTime;
    /**
     * 聘用现职时间
     */
    private LocalDateTime currentEmploymentTime;
    /**
     * 续评职务时间
     */
    private LocalDateTime jobContinuationTime;

}
