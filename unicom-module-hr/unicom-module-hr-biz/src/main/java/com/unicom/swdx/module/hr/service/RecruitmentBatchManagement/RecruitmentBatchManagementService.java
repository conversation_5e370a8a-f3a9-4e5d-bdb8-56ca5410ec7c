package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentBatchManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentBatchManagementVO;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * 招聘批次管理(HrRecruitmentBatchManagement)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:37
 */
public interface RecruitmentBatchManagementService extends IService<RecruitmentBatchManagement> {

    PageResult<RecruitmentBatchManagement> queryByList(RecruitmentBatchManagementVO recruitmentBatchManagementVO);

    List<RecruitmentBatchManagement> queryAll(RecruitmentBatchManagementVO recruitmentBatchManagementVO);

    ResponseEntity<Map<String, Object>> add(RecruitmentBatchManagement recruitmentBatchManagement);

    ResponseEntity<Map<String, Object>> edit(RecruitmentBatchManagement recruitmentBatchManagement);

    ResponseEntity<String> delete(RecruitmentBatchManagement recruitmentBatchManagement);

    ResponseEntity<String> enable(RecruitmentBatchManagement recruitmentBatchManagement);

}
