package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentPositionManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentPositionManagementVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitmentPositionManagementMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitmentPositionManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 招聘批次管理(RecruitmentPositionManagement)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:37
 */
@Service
@Validated
@Slf4j
public class RecruitmentPositionManagementServiceImpl extends ServiceImpl<RecruitmentPositionManagementMapper, RecruitmentPositionManagement> implements RecruitmentPositionManagementService {

    @Resource
    private RecruitmentPositionManagementMapper recruitmentPositionManagementMapper;

    /**
     * 发布状态
     *      招聘中
     */
    private static final String RELEASE_SUCCESS = "2";

    /**
     * 发布状态
     *      撤回
     */
    private static final String WITHDRAWAL_SUCCESS = "1";

    /**
     * 发布状态
     *      完成
     */
    private static final String COMPLETE_SUCCESS = "3";

    /**
     * 时间格式
     */
    private static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 输入校验
     *    30个字符
     */
    private static final Integer TEXT_LIMIT_THIRTY = 30;

    /**
     * 输入校验
     *    1000个字符
     */
    private static final Integer TEXT_LIMIT_ONE_THOUSAND = 1000;

    /**
     * 输入校验
     *    常量
     */
    private static final String INPUT_ONE_THOUSAND = "输入超过1000个字，请重新输入！";

    /**
     * 输入校验
     *    常量
     */
    private static final String INPUT_THIRTY = "输入超过30个字，请重新输入！";

    /**
     * 前端返回
     *    提示消息 message
     */
    private static final String MESSAGE_INFO = "message";

    /**
     * 前端返回
     *    提示消息 code
     */
    private static final String CODE_INFO = "code";

    @Override
    @Transactional(propagation= Propagation.REQUIRED ,rollbackFor = Exception.class)
    public PageResult<RecruitmentPositionManagement> queryByList(RecruitmentPositionManagementVO recruitmentPositionManagementVO) {

        IPage<RecruitmentPositionManagement> page = MyBatisUtils.buildPage(recruitmentPositionManagementVO);
        List<RecruitmentPositionManagement> data = recruitmentPositionManagementMapper.queryByList(page, recruitmentPositionManagementVO);
        return new PageResult<>(data, page.getTotal());
}


    @Override
    public ResponseEntity<Map<String, Object>> add(RecruitmentPositionManagement recruitmentPositionManagement) {

        HashMap<String,Object> result = new HashMap<>();

        //岗位名称  去重校验
        if(StringUtils.isNotBlank(recruitmentPositionManagement.getPositionName())){
            int jobNameCount = recruitmentPositionManagementMapper
                    .jobTitle(recruitmentPositionManagement.getPositionName(),recruitmentPositionManagement.getOwningBatch());
            if(jobNameCount > 0){
                result.put(CODE_INFO,300);
                result.put(MESSAGE_INFO,"岗位名称已存在，请重新输入！");
                return ResponseEntity.ok(result);
            }
        }else{
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,"岗位名称为空，请重新输入！");
            return ResponseEntity.ok(result);
        }

        //岗位代码  去重校验
        if(StringUtils.isNotBlank(recruitmentPositionManagement.getPostCode())){
            int jobCodeCount = recruitmentPositionManagementMapper.postCode(recruitmentPositionManagement.getPostCode());
            if(jobCodeCount > 0){
                result.put(CODE_INFO,300);
                result.put(MESSAGE_INFO,"岗位代码已存在，请重新输入！");
                return ResponseEntity.ok(result);
            }
        }

        //输入字数校验
        if(recruitmentPositionManagement.getPositionName().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getPostCode()) &&
                recruitmentPositionManagement.getPostCode().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(recruitmentPositionManagement.getRecruitmentPlan().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getAcademicDegree()) &&
                recruitmentPositionManagement.getAcademicDegree().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getAge()) &&
                recruitmentPositionManagement.getAge().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getProfession()) &&
                recruitmentPositionManagement.getProfession().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getOther()) &&
                recruitmentPositionManagement.getOther().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getRemark()) &&
                recruitmentPositionManagement.getRemark().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getJobRequirements()) &&
                recruitmentPositionManagement.getJobRequirements().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getJobResponsibility()) &&
                recruitmentPositionManagement.getJobResponsibility().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        //时间校验
        if(recruitmentPositionManagement.getStartTime() != null &&
                             recruitmentPositionManagement.getCutTime() != null){
            int comparisonResult = recruitmentPositionManagement.getStartTime()
                    .compareTo(recruitmentPositionManagement.getCutTime());
            if(comparisonResult > 0){
                result.put("error","开始时间不能大于结束时间！");
                return ResponseEntity.ok(result);
            }
        }


        try {
            recruitmentPositionManagement.setCreateTime(LocalDateTime.now());
            recruitmentPositionManagementMapper.insert(recruitmentPositionManagement);
            result.put(CODE_INFO,200);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put(CODE_INFO,300);
            return ResponseEntity.ok(result);
        }

    }

    @Override
    public ResponseEntity<Map<String, Object>> edit(RecruitmentPositionManagement recruitmentPositionManagement) {

        HashMap<String,Object> result = new HashMap<>();

        //岗位名称  去重校验
        if(StringUtils.isNotBlank(recruitmentPositionManagement.getPositionName())){
            int jobNameCount = recruitmentPositionManagementMapper
                    .jobTitleEdit(recruitmentPositionManagement.getPositionName(),
                            recruitmentPositionManagement.getOwningBatch(),
                            recruitmentPositionManagement.getId());
            if(jobNameCount > 0){
                result.put(CODE_INFO,300);
                result.put(MESSAGE_INFO,"岗位名称已存在，请重新输入！");
                return ResponseEntity.ok(result);
            }
        }else{
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,"岗位名称为空，请重新输入！");
            return ResponseEntity.ok(result);
        }

        //岗位代码  去重校验
        if(StringUtils.isNotBlank(recruitmentPositionManagement.getPostCode())){
            int jobCodeCount = recruitmentPositionManagementMapper
                    .postCodeEdit(recruitmentPositionManagement.getPostCode(),
                            recruitmentPositionManagement.getId());
            if(jobCodeCount > 0){
                result.put(CODE_INFO,300);
                result.put(MESSAGE_INFO,"岗位代码已存在，请重新输入！");
                return ResponseEntity.ok(result);
            }
        }

        if(recruitmentPositionManagement.getPositionName().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getPostCode()) &&
                recruitmentPositionManagement.getPostCode().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(recruitmentPositionManagement.getRecruitmentPlan().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getAcademicDegree()) &&
                recruitmentPositionManagement.getAcademicDegree().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getAge()) &&
                recruitmentPositionManagement.getAge().length() > TEXT_LIMIT_THIRTY){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_THIRTY);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getProfession()) &&
                recruitmentPositionManagement.getProfession().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getOther()) &&
                recruitmentPositionManagement.getOther().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getRemark()) &&
                recruitmentPositionManagement.getRemark().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getJobRequirements()) &&
                recruitmentPositionManagement.getJobRequirements().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        if(StringUtils.isNotBlank(recruitmentPositionManagement.getJobResponsibility()) &&
                recruitmentPositionManagement.getJobResponsibility().length() > TEXT_LIMIT_ONE_THOUSAND){
            result.put(CODE_INFO,300);
            result.put(MESSAGE_INFO,INPUT_ONE_THOUSAND);
            return ResponseEntity.ok(result);
        }

        try {
            recruitmentPositionManagementMapper.updateById(recruitmentPositionManagement);
            result.put(CODE_INFO,200);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put(CODE_INFO,300);
            return ResponseEntity.ok(result);
        }
    }

    @Override
    public ResponseEntity<String> del(RecruitmentPositionManagement recruitmentPositionManagement) {
        try {
            recruitmentPositionManagementMapper.deleteById(recruitmentPositionManagement);
            return ResponseEntity.ok("删除成功！");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("删除失败，请重试！");
        }
    }

    @Override
    public ResponseEntity<Map<String, Object>> publish(Integer id, String startTime, String cutTime) {

        HashMap<String,Object> result = new HashMap<>();

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_FORMAT);
        // 将字符串解析为 LocalDateTime 对象
        LocalDateTime start = LocalDateTime.parse(startTime, formatter);
        // 将字符串解析为 LocalDateTime 对象
        LocalDateTime end = LocalDateTime.parse(cutTime, formatter);

        //时间校验
        int comparisonResult = start.compareTo(end);
        if(comparisonResult > 0){
            result.put("error","开始时间不能大于结束时间！");
            return ResponseEntity.ok(result);
        }

        try {
            RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
            recruitmentPositionManagement.setId(id);
            recruitmentPositionManagement.setStartTime(start);
            recruitmentPositionManagement.setCutTime(end);
            recruitmentPositionManagement.setReleaseStatus(RELEASE_SUCCESS);
            recruitmentPositionManagementMapper.updateById(recruitmentPositionManagement);
            result.put(CODE_INFO,200);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put(CODE_INFO,300);
            return ResponseEntity.ok(result);
        }
    }

    @Override
    public ResponseEntity<Map<String, Object>> recall(Integer id) {

        HashMap<String,Object> result = new HashMap<>();

        try {
            //撤回 开始时间与结束时间清空
            UpdateWrapper<RecruitmentPositionManagement> wrapper = new UpdateWrapper<>();
            wrapper.set("start_time", null)
                    .set("end_time", null)
                    .eq("id", id);
            recruitmentPositionManagementMapper.update(null, wrapper);
            //根据 id 值 更新状态
            RecruitmentPositionManagement recruitmentPositionManagement = new RecruitmentPositionManagement();
            recruitmentPositionManagement.setId(id);
            recruitmentPositionManagement.setReleaseStatus(WITHDRAWAL_SUCCESS);
            recruitmentPositionManagementMapper.updateById(recruitmentPositionManagement);
            result.put(CODE_INFO,200);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put(CODE_INFO,300);
            return ResponseEntity.ok(result);
        }
    }

    @Override
    public ResponseEntity<String> timedTask(){
        try {
            LocalDateTime now = LocalDateTime.now();
            UpdateWrapper<RecruitmentPositionManagement> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lt("cut_time", now)
                    .set("release_status",COMPLETE_SUCCESS);
             // 调用更新方法
            recruitmentPositionManagementMapper.update(null, updateWrapper);
            return ResponseEntity.ok("数据库刷新任务完成！");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("数据库任务刷新失败，请重试！");
        }

    }

}
