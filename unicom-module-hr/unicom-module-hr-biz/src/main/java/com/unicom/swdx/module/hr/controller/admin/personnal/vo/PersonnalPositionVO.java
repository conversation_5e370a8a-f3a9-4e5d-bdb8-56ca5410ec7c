package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonnalPositionVO {

    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    private Integer rank;
    /**
     * 行政职务名称
     */
    @ApiModelProperty(value = "行政职务名称")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String administrativePositionName;
    /**
     * 行政职务级别
     */
    @ApiModelProperty(value = "行政职务级别")
    private Integer administrativePositionRank;
    /**
     * 专业技术职称
     */
    @ApiModelProperty(value = "专业技术职称")
    private Integer professionalTechnicalName;
    /**
     * 专业技术岗位级别
     */
    @ApiModelProperty(value = "专业技术岗位级别")
    private Integer professionalTechnicalRank;
    /**
     * 职务评选时间
     */
    @ApiModelProperty(value = "职务评选时间")
    private LocalDateTime jobSelectionTime;
    /**
     * 聘用现职时间
     */
    @ApiModelProperty(value = "聘用现职时间")
    private LocalDateTime currentEmploymentTime;
    /**
     * 续评职务时间
     */
    @ApiModelProperty(value = "续评职务时间")
    private LocalDateTime jobContinuationTime;
}
