package com.unicom.swdx.module.hr.dal.mysql.personnal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.hr.api.dto.PersonDTO;
import com.unicom.swdx.module.hr.controller.admin.personnal.PersonnalTransferPageRespVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.PersonalDeathExcelReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.PersonalDeathExcelVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.PersonalDeathPageReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.PersonalDeathPageRespVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.PersonalLeaveExcelReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.PersonalLeaveExcelVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.PersonalLeavePageReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.PersonalLeavePageRespVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.PersonalRetireExcelReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.PersonalRetireExcelVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.PersonalRetirePageReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.PersonalRetirePageRespVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalTransferDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PersonnalTransferMapper extends BaseMapperX<PersonnalTransferDO> {

    List<PersonnalTransferPageRespVO> selectPage(IPage page, @Param("param") PersonnalTransferPageReqVO param,
                                                 @Param("tenantId")Long tenantId);

    List<PersonnalTransferPageRespVO> selectListByUser(@Param("userId") String userId,
                                                 @Param("tenantId")Long tenantId);


    PersonnalTransferOutVO selectPersonnalTransferOut(@Param("userId") String userId);


    //导出
    List<PersonnalTransferExcelVO> selectList(@Param("param") PersonnalTransferPageReqVO param,
                                                 @Param("tenantId")Long tenantId);


    default PersonnalTransferDO selectByPersonnalId(@Param("id")Long id) {
        return selectById(id);
    }
}
