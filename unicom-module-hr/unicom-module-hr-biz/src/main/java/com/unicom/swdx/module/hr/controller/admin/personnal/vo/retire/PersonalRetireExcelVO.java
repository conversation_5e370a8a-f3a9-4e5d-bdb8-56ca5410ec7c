package com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("人事信息分页-退休管理 Response VO")
@Data
@ToString(callSuper = true)
public class PersonalRetireExcelVO {

    @ExcelIgnore
    private Long userId;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "原部门")
    private String department;

    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSON_GENDER)
    private Integer gender;

    @ExcelProperty(value = "人员状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_STATUS)
    private Integer personnalStatus;

    @ExcelProperty(value = "退休日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String retireTime;

    @ExcelProperty(value = "工作证号")
    private String workId;

}
