package com.unicom.swdx.module.hr.convert.personnal;


import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalImportExcelVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PersonnalStudyConvert {

    PersonnalStudyConvert INSTANCE = Mappers.getMapper(PersonnalStudyConvert.class);

    PersonnalStudyDO convert(PersonnalStudyVO bean);
    PersonnalStudyGetVO convert0(PersonnalStudyDO bean);
    PersonnalStudyDO convert1(PersonnalStudyGetVO bean);
    PersonnalStudyDO convert2(PersonnalImportExcelVO bean);
    List<PersonnalStudyDO> convertList(List<PersonnalImportExcelVO> bean);
}
