package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPositionGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPositionVO;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalPositionConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalPositionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 人事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonnalPositionServiceImpl extends ServiceImpl<PersonnalPositionMapper, PersonnalPositionDO> implements PersonnalPositionService {
    @Resource
    private PersonnalPositionMapper personnalPositionMapper;

    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnal(PersonnalPositionVO createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PersonnalPositionDO position = PersonnalPositionConvert.INSTANCE.convert(createReqVO);
        position.setPersonnalId(personnalId);
        personnalPositionMapper.insert(position);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnalRegis(PersonnalCreateRegistrationVO createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PersonnalPositionDO position = new PersonnalPositionDO();
        position.setPersonnalId(personnalId);
        personnalPositionMapper.insert(position);
    }
    @Override
    public PersonnalPositionDO getPersonnal(Long id) {
        return personnalPositionMapper.selectByPersonnalId(id);
    }
    @Override
    public void updatePersonnal(PersonnalPositionGetVO updateReqVO) {
        // 校验正确性
//        this.checkCreateOrUpdate(reqVO.getId(), reqVO.getName(), reqVO.getCode(), getTenantId());
        // 更新岗位
        PersonnalPositionDO updateObj = PersonnalPositionConvert.INSTANCE.convert1(updateReqVO);
        LambdaUpdateWrapper<PersonnalPositionDO> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.eq(PersonnalPositionDO::getPersonnalId,updateObj.getPersonnalId())
                .set(PersonnalPositionDO::getRank,updateObj.getRank())
                .set(PersonnalPositionDO::getAdministrativePositionRank,updateObj.getAdministrativePositionRank())
                .set(PersonnalPositionDO::getProfessionalTechnicalName,updateObj.getProfessionalTechnicalName())
                .set(PersonnalPositionDO::getProfessionalTechnicalRank,updateObj.getProfessionalTechnicalRank())
                .set(PersonnalPositionDO::getJobContinuationTime,updateObj.getJobContinuationTime())
                .set(PersonnalPositionDO::getJobSelectionTime,updateObj.getJobSelectionTime())
                .set(PersonnalPositionDO::getCurrentEmploymentTime,updateObj.getCurrentEmploymentTime());
        personnalPositionMapper.updateByPersonnalId(updateObj);
        personnalPositionMapper.update(new PersonnalPositionDO(),lambdaUpdateWrapper);
    }
}
