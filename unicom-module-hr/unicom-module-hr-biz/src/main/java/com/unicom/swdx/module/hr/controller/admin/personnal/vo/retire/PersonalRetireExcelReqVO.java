package com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("人事信息分页-退休管理 Request VO")
@Data
@ToString(callSuper = true)
public class PersonalRetireExcelReqVO {

    @ApiModelProperty(value = "姓名", example = "王二")

    private String name;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;

    @ApiModelProperty(value = "开始时间",example = "2020-01-01T00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间",example = "2020-01-01T00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

}
