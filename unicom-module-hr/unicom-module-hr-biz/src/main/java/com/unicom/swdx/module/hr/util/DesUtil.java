package com.unicom.swdx.module.hr.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.Key;
import java.security.spec.KeySpec;
import java.util.Base64;

public class DesUtil {
    //    private static  String KEY = key
    private static  String KEY_STR = "Hnswdx2020"; // 假设这是一个有效的 8 字节 DES 密钥
    private static  String CHARSETNAME = "UTF-8";
    private static  String ALGORITHM = "DES";


    public String jianmi(String id) throws Exception {
        Key key = getKey(KEY_STR);
        // 加密
        byte[] encryptedBytes = encrypt(id, key);
        System.out.println(encryptedBytes);
        String encryptedHex = Base64.getEncoder().encodeToString(encryptedBytes);
        return encryptedHex ;

    }


    private static Key getKey(String keyStr) throws Exception {
        // DESKeySpec 需要一个 8 字节的密钥
        KeySpec keySpec = new DESKeySpec(keyStr.getBytes(CHARSETNAME));
        // SecretKeyFactory 用于从给定的密钥规范生成 SecretKey 对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        return keyFactory.generateSecret(keySpec);
    }

    private static byte[] encrypt(String data, Key key) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        return cipher.doFinal(data.getBytes(CHARSETNAME));
    }
}
