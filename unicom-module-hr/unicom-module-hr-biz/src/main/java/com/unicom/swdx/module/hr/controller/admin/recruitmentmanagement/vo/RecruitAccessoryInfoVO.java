package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "RecruitAccessoryInfo", description = "附件信息")
public class RecruitAccessoryInfoVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 617352328164787392L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String accessoryName;
    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private String accessoryType;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String accessory;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private String accessorySort;
    /**
     * 附件url
     */
    @ApiModelProperty(value = "附件url")
    private String url;
}
