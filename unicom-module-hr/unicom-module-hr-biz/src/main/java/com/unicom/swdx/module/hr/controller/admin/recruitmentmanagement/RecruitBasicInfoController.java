package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitBasicInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.*;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitBasicInfoService;
import com.unicom.swdx.module.hr.task.ExcelMergeCellHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.CREATE;

/**
 * <AUTHOR>
 * @data 2024/3/5 19:47
 */

@RestController
@Api(tags = "招聘信息管理")
@RequestMapping("/hr/recruit/basic")
public class RecruitBasicInfoController {

    /**
     * 服务对象
     */
    @Resource
    RecruitBasicInfoService recruitBasicInfoService;

    /**
     * 分页查询
     *     初试
     * @param recruitBasicInfoVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/firstlist")
    @ApiOperation("信息分页")
    @PreAuthorize("@ss.hasPermission('hr:recruit:firstlist')")
    public CommonResult<PageResult<RecruitBasicInfo>> queryByList(@RequestBody RecruitBasicInfoVO recruitBasicInfoVO) {
        PageResult<RecruitBasicInfo> pageResult = recruitBasicInfoService.queryByList(recruitBasicInfoVO);
        return success(pageResult);
    }

    /**
     * 分页查询
     *    面试
     * @param recruitBasicInfoVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/facelist")
    @ApiOperation("信息分页")
    @PreAuthorize("@ss.hasPermission('hr:recruit:facelist')")
    public CommonResult<PageResult<RecruitBasicInfo>> faceByList(@RequestBody RecruitBasicInfoVO recruitBasicInfoVO) {
        PageResult<RecruitBasicInfo> pageResult = recruitBasicInfoService.faceByList(recruitBasicInfoVO);
        return success(pageResult);
    }

    /**
     *
     * 审核
     * @param recruitBasicInfoVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/check")
    @ApiOperation("审核")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('hr:recruit:check')")
    public ResponseEntity<Map<String,Object>> check(@RequestBody RecruitBasicInfoVO recruitBasicInfoVO) {

        return recruitBasicInfoService.check(recruitBasicInfoVO);
    }

    /**
     *
     * 应聘职位
     * @param recruitApplyVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/apply")
    @ApiOperation("应聘职位")
    public CommonResult<Integer> apply(@RequestBody RecruitApplyVO recruitApplyVO) {
        recruitApplyVO.setRecruitmentUnit("中共湖南省委党校（湖南行政学院）");
        return success(recruitBasicInfoService.apply(recruitApplyVO));
    }

    /**
     *
     * 保存草稿
     * @param recruitApplyVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/saveDraft")
    @ApiOperation("保存草稿")
    public CommonResult<Integer> saveDraft(@RequestBody RecruitApplyVO recruitApplyVO) {
        recruitApplyVO.setRecruitmentUnit("中共湖南省委党校（湖南行政学院）");
        return success(recruitBasicInfoService.saveDraft(recruitApplyVO));
    }

    /**
     *
     * 退回修改
     * @param recruitBasicInfo  实体类
     * @return 查询结果
     *
     */
    @PostMapping("/backToModify")
    @ApiOperation("退回修改")
    @PreAuthorize("@ss.hasPermission('hr:recruit:backToModify')")
    public CommonResult<Boolean> backToModify(@RequestBody RecruitBasicInfo recruitBasicInfo) {
        recruitBasicInfoService.backToModify(recruitBasicInfo);
        return success(true);
    }

    /**
     *
     * 详情
     * @param id 实体类
     * @return 查询结果
     *
     */
    @GetMapping("/detail")
    @ApiOperation("详情")
    @PreAuthorize("@ss.hasPermission('hr:recruit:detail')")
    public CommonResult<RecruitDetailRespVO> detail(@RequestParam Integer id) {

        return success(recruitBasicInfoService.detail(id));
    }

    /**
     *
     * 查看投递状态
     * @return 查询结果
     *
     */
    @PostMapping("/deliveryStatus")
    @ApiOperation("查看投递状态")
    public CommonResult<List<RecruitBasicInfoRespVO>> deliveryStatus() {

        return success(recruitBasicInfoService.deliveryStatus());
    }

    @PostMapping("/stationPage")
    @ApiOperation("查看招聘岗位分页")
    public CommonResult<PageResult<RecruitStationVO>> getStationPage(@RequestBody RecruitStationRequestVO recruitStationRequestVO) {

        return success(recruitBasicInfoService.getStationPage(recruitStationRequestVO));
    }

    @GetMapping("/getById")
    @ApiOperation("通过id查看招聘信息")
    public CommonResult<RecruitInformationVO> getInformationById(@RequestParam Long id) {

        return success(recruitBasicInfoService.getInformationById(id));
    }
    @GetMapping("/getMobilesByIds")
    @ApiOperation("通过id查看招聘信息")
    public CommonResult<List<Long>> getMobilesByIds(@RequestParam List<Long> ids) {
        return success(recruitBasicInfoService.getMobilesByIds(ids));
    }


    @GetMapping("/export")
    @ApiOperation("导出报名信息表")
//    @PreAuthorize("@ss.hasPermission('hr:recruit:export')")
//    @OperateLog(type = EXPORT)
    public void export(HttpServletResponse response, @Validated RecruitBasicInfoExportReqVO reqVO) throws IOException {
        List<RecruitBasicInfoExcelVO> basicInfoExcel = recruitBasicInfoService.getBasicInfoExcel(reqVO);
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteCellStyle bodyStyle = new WriteCellStyle();
        bodyStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        bodyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        WriteSheet sheet = EasyExcel.writerSheet("个人信息表").head(RecruitBasicInfoExcelVO.class).sheetNo(1).build();
        int[] mergeColIndex = IntStream.rangeClosed(0,38).toArray();

        ExcelWriter writer = EasyExcel.write(response.getOutputStream()).needHead(true)
                .excelType(ExcelTypeEnum.XLS)
                .registerWriteHandler(new ExcelMergeCellHandler(mergeColIndex,0))
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, bodyStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        writer.write(basicInfoExcel, sheet);
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("报名信息表.xls", "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setContentType("application/json;charset=UTF-8");
        writer.finish();

    }

    @GetMapping("/exportPerson")
    @ApiOperation("导出个人报名信息表")
//    @PreAuthorize("@ss.hasPermission('hr:recruit:export')")
//    @OperateLog(type = EXPORT)
    public void exportPerson(HttpServletResponse response, @Validated RecruitApplyExportReqVO reqVO) throws IOException {
        List<List<RecruitApplyExcelVO>> applyExcelVOS = recruitBasicInfoService.getApplyExcel(reqVO);
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteCellStyle bodyStyle = new WriteCellStyle();
        bodyStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        bodyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        WriteSheet sheet = EasyExcel.writerSheet("个人信息表").head(RecruitApplyExcelVO.class).sheetNo(1).build();
        int[] mergeColIndex = IntStream.rangeClosed(0,27).toArray();

        // 输出
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            for (List<RecruitApplyExcelVO> applyExcel : applyExcelVOS) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                RecruitApplyExcelVO recruitApplyExcelVO = applyExcel.get(0);
                ExcelWriter writer = null;
                try {
                    writer = EasyExcel.write(byteArrayOutputStream).needHead(true)
                            .excelType(ExcelTypeEnum.XLS)
                            .registerWriteHandler(new ExcelMergeCellHandler(mergeColIndex, 0))
                            .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, bodyStyle))
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();
                    zipOut.putNextEntry(new ZipEntry(recruitApplyExcelVO.getName() + "-个人报名信息表.xls"));
                    writer.write(applyExcel, sheet);
                    response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                    response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode( "个人报名信息表.zip", "UTF-8"));
                    response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                    response.setContentType("application/json;charset=UTF-8");
                } catch (Exception e) {
                    throw new RuntimeException("导出Excel异常", e);
                } finally {
                    if (writer != null) {
                        writer.finish();
                    }
                }
                byteArrayOutputStream.writeTo(zipOut);
                zipOut.closeEntry();
            }
        } catch (Exception e) {
            throw new RuntimeException("导出Excel异常", e);
        }
    }



}
