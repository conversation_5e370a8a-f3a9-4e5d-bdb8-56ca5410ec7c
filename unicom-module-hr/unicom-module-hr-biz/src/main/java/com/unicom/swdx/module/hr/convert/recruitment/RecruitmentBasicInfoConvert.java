package com.unicom.swdx.module.hr.convert.recruitment;

import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitBasicInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitApplyExcelVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitApplyVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitBasicInfoExcelVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitDetailRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RecruitmentBasicInfoConvert {
    RecruitmentBasicInfoConvert INSTANCE = Mappers.getMapper(RecruitmentBasicInfoConvert.class);

    RecruitBasicInfo convert(RecruitApplyVO bean);

    RecruitApplyVO convert(RecruitBasicInfo bean);

    RecruitDetailRespVO convertDetail(RecruitApplyVO bean);

    RecruitApplyExcelVO convertExcel(RecruitBasicInfo bean);

    RecruitBasicInfoExcelVO convertBasicExcel(RecruitBasicInfo bean);
}
