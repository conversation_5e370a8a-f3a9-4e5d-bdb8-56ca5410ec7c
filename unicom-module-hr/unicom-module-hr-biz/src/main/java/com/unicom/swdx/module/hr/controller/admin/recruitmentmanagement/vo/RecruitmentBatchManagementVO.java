package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 传输类
 * <AUTHOR>
 * @data 2024/3/2 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RecruitmentBatchManagementVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 952581252014550478L;

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 批次名称
     */
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    /**
     * 批次类别，1-专业技术人员,2-教辅人员,3-博士后，4-管理人员,5-人才引进
     */
    @ApiModelProperty(value = "批次类别")
    private String batchClass;
    /**
     * 是否移动端，默认否，0-否，1-是
     */
    @ApiModelProperty(value = "是否移动端")
    private Integer isMobile;
    /**
     * 限投次数
     */
    @ApiModelProperty(value = "限投次数")
    private Integer limitFrequency;
    /**
     * 默认0-未开始，1-进行中，2-完成
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

}
