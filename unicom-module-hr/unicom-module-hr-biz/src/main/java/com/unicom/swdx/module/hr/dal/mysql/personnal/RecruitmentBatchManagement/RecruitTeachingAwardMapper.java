package com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicPaper;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitTeachingAward;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicPaperVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitTeachingAwardVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (HrRecruitAccessoryInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:13
 */

@Mapper
public interface RecruitTeachingAwardMapper extends BaseMapperX<RecruitTeachingAward> {

    List<RecruitTeachingAward> queryByList(IPage page, @Param("param") RecruitTeachingAwardVO recruitTeachingAwardVO);
}
