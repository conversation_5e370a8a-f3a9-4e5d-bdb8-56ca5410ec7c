package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("人事信息分页 Response VO")
@Data
@ToString(callSuper = true)
public class PersonnalEntryPageRespVO {


    @ApiModelProperty(value = "人事编号", required = true, example = "1")
    @NotNull(message = "人事编号不能为空")
    private Long id;

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "工作证号", example = "202410001")
    private String workId;

    @ApiModelProperty(value = "行政职务名称",example = "主任")
    private String administrativePositionName;

    @ApiModelProperty(value = "行政职务级别",example = "正处")
    private String administrativePositionRank;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;

    @ApiModelProperty(value = "性别",example = "1")

    private Integer gender;

    @ApiModelProperty(value = "人员状态",example = "1")

    private Integer personnalStatus;

    @ApiModelProperty(value = "手机号码",example = "1")
    private String mobile;

    @ApiModelProperty(value = "出生年月")
    private LocalDateTime birthday;

    @ApiModelProperty(value = "籍贯",example = "长沙")
    private String nativePlace;

    @ApiModelProperty(value = "民族",example = "1")
    private Integer  nation;

    @ApiModelProperty(value = "人员分类",example = "1")
    private Integer peronClassification;

    @ApiModelProperty(value = "获得学历",example = "大学本科毕业")
    private Integer education;

    @ApiModelProperty(value = "获得学位",example = "1")
    private Integer academicDegree;

    @ApiModelProperty(value = "专业技术岗位级别",example = "1")
    private Integer professionalTechnicalRank;

    @ApiModelProperty(value = "专业技术职务",example = "1")
    private Integer professionalTechnicalName;

}
