package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalWorkGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalWorkVO;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalWorkConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalWorkMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 人事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonnalWorkServiceImpl extends ServiceImpl<PersonnalWorkMapper, PersonnalWorkDO> implements PersonnalWorkService {
    @Resource
    private PersonnalWorkMapper personnalWorkMapper;

    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnal(List<PersonnalWorkVO> createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        List<PersonnalWorkDO> works = PersonnalWorkConvert.INSTANCE.convert(createReqVO);
        works.forEach(work -> {
            work.setPersonnalId(personnalId);
        });
        personnalWorkMapper.insertBatch(works);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnalRegis(PersonnalCreateRegistrationVO createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PersonnalWorkDO work = new  PersonnalWorkDO();
        work.setPersonnalId(personnalId);
        personnalWorkMapper.insert(work);

    }
    @Override
    public List<PersonnalWorkDO> getPersonnal(Long id) {
        return personnalWorkMapper.selectByPersonnalId(id);
    }
    @Override
    public void updatePersonnal(List<PersonnalWorkGetVO>updateReqVO) {

        List<PersonnalWorkDO> updateObjs = PersonnalWorkConvert.INSTANCE.convert1(updateReqVO);
        if(!updateReqVO.isEmpty()) {
            Long personnalId = updateReqVO.get(0).getPersonnalId();
            if (updateReqVO.get(0).getPersonnalId() != null) {
                List<PersonnalWorkDO> exitWorks = personnalWorkMapper.selectByPersonnalId(personnalId);
                updateObjs.forEach(updateObj -> {
                    if (updateObj.getId() != null) {
                        LambdaUpdateWrapper<PersonnalWorkDO> lambdaUpdateWrapper = new LambdaUpdateWrapper();
                        lambdaUpdateWrapper.eq(PersonnalWorkDO::getPersonnalId,updateObj.getPersonnalId());
                        lambdaUpdateWrapper.set(PersonnalWorkDO::getStartTime,updateObj.getStartTime());
                        lambdaUpdateWrapper.set(PersonnalWorkDO::getEndTime,updateObj.getEndTime());
                        personnalWorkMapper.updateById(updateObj);
                        personnalWorkMapper.update(new PersonnalWorkDO(),lambdaUpdateWrapper);
                    } else {
                        updateObj.setPersonnalId(personnalId);
                        personnalWorkMapper.insert(updateObj);
                    }
                });

                for (int i = 0; i < exitWorks.size(); i++) {
                    int count = 0;
                    for (int j = 0; j < updateObjs.size(); j++) {
                        if (updateObjs.get(j).getId().equals(exitWorks.get(i).getId())) {
                            count++;
                        }
                    }
                    if (count == 0) {
                        personnalWorkMapper.deleteById(exitWorks.get(i).getId());
                    }
                }
            }
        }
    }
}
