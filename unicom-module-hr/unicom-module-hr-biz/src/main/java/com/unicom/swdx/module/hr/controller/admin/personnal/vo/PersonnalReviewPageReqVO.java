package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("人事信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonnalReviewPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "王二")

    private String name;

    @ApiModelProperty(value = "性别",example = "1")
    private Integer gender;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;


}
