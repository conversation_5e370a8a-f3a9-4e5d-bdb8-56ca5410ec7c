package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PersonnalBasicGetVO extends PersonnalBasicVO {
    @ApiModelProperty(value = "编号", required = true, example = "1")
    @NotNull(message = "编号不能为空")
    private Long id;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "工作证号", required = true, example = "202400001")
    @NotNull(message = "工作证号不能为空")
    private String workId;

    @ApiModelProperty(value = "用户id")
    private Long userId;
}

