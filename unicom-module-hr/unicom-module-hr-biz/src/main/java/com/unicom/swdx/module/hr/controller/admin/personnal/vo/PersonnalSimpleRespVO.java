package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("人事信息分页 Response VO")
@Data
@ToString(callSuper = true)
public class PersonnalSimpleRespVO {


    @ApiModelProperty(value = "人事编号", required = true, example = "1")
    @NotNull(message = "人事编号不能为空")
    private Long id;
    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "人员状态",example = "正处")
    private Integer personnalStatus;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;

    @ApiModelProperty(value = "性别",example = "1")

    private Integer gender;




}
