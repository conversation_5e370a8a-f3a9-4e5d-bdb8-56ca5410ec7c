package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitWorkExp;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitWorkExpVO;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitWorkExpService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @data 2024/3/5 19:47
 */

@RestController
@RequestMapping("/hr/recruit/workexp")
public class RecruitWorkExpController {

    /**
     * 服务对象
     */
    @Resource
    RecruitWorkExpService recruitWorkExpService;

    /**
     * 分页查询
     *
     * @param recruitWorkExpVO  传输实体类
     * @return 查询结果
     *
     */
    @PostMapping("/list")
    @ApiOperation("信息分页")
    public CommonResult<PageResult<RecruitWorkExp>> queryByList(@RequestBody RecruitWorkExpVO recruitWorkExpVO) {
        PageResult<RecruitWorkExp> pageResult = recruitWorkExpService.queryByList(recruitWorkExpVO);
        return success(pageResult);
    }
}
