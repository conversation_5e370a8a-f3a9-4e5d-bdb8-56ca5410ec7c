package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("人事信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonnalEntryPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "工作证号", example = "202410001")
    private String workId;

    @ApiModelProperty(value = "性别",example = "1")
    private Integer gender;

    @ApiModelProperty(value = "部门全称",example = "1")
    private Long department;

    @ApiModelProperty(value = "人员分类",example = "1")
    private Integer peronClassification;

    @ApiModelProperty(value = "人员状态",example = "1")
    private Integer personnalStatus;


    @ApiModelProperty(value = "报道日期")
    private LocalDateTime[] registrationDate;


}
