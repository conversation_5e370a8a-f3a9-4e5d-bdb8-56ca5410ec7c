package com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("人事信息分页-离校管理 Response VO")
@Data
@ToString(callSuper = true)
public class PersonalLeavePageRespVO {

    @ApiModelProperty(value = "人事编号", required = true, example = "1")
    @NotNull(message = "人事编号不能为空")
    private Long id;

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "部门全称",example = "1")
    private String department;

    private Long userId;

    private List<Long> deptIds;

    private Long deptId;

    @ApiModelProperty(value = "性别",example = "1")

    private Integer gender;

    @ApiModelProperty(value = "人员状态",example = "1")

    private Integer personnalStatus;

    @ApiModelProperty(value = "离校日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "离校途径")
    private String leaveChannel;

    @ApiModelProperty(value = "离校去向")
    private String leaveDestination;

    @ApiModelProperty(value = "离校原因")
    private String leaveReason;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校联系方式", required = true, example = "这是一条备注")
    private String leaveMobile;

    @ApiModelProperty(value = "工作证号", example = "202410001")
    private String workId;

}
