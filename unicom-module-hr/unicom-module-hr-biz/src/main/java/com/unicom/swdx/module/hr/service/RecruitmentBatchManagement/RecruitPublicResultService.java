package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicPaper;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicPaperVO;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicResultVO;

/**
 * (HrRecruitPublicResult)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */
public interface RecruitPublicResultService extends IService<RecruitPublicResult> {

    PageResult<RecruitPublicResult> queryByList(RecruitPublicResultVO recruitPublicResultVO);


}
