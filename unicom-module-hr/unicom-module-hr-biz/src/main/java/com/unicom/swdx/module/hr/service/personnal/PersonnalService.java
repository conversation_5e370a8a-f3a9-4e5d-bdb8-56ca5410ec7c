package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 人事Service 接口
 *
 */
public interface PersonnalService extends IService<PersonnalBasicDO> {


    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    PersonnalBasicDO createPersonnal(@Valid PersonnalBasicVO createReqVO, Integer rank, String positionName, Integer positionRank);
    /**
     * 新增人事入职信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEntryPersonnal(@Valid PersonnalBasicEntryVO createReqVO);
    /**
     * 新增报到登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRegistrationPersonnal(@Valid PersonnalCreateRegistrationVO createReqVO);
    /**
     * 获得人事信息分页
     *
     * @param pageReqVO 分页查询
     * @return 人事信息分页
     */
    PageResult<PersonnalPageRespVO> getPersonnalPage(PersonnalPageReqVO pageReqVO);

    /**
     * 获得人事信息
     *
     * @param id 岗位编号
     * @return 岗位信息
     */
    PersonnalBasicDO getPersonnal(Long id);
    PersonnalSimpleRespVO getPersonnalSimple(Long id);
    /**
     * 更新人事信息
     */
    void updatePersonnal(PersonnalBasicGetVO personnalBasicVO);
    /**
     * 导出人事信息
     */
    List<PersonnalExcelVO> getPersonnalExcel(PersonnalExportReqVO reqVO);
    /**
     * 导入人事信息
     */
    PersonnalImportRespVO importPersonnal(List<PersonnalImportExcelVO> importPosts, boolean isUpdateSupport);
    /**
     * 获得人事信息审核分页
     */
    PageResult<PersonnalReviewPageRespVO> getPersonnalReviewPage(PersonnalReviewPageReqVO pageReqVO);

//    PageResult<PersonnalEntryPageRespVO> getPersonnalEntryPage(PersonnalEntryPageReqVO pageReqVO);

    PageResult<PersonnalRegistrationPageRespVO> getPersonnalRegistrationPage(PersonnalRegistrationPageReqVO pageReqVO);

    /**
     * 获得退休人事信息分页
     * @param pageReqVO 分页查询
     * @return 退休人事信息分页
     */
    PageResult<PersonalRetirePageRespVO> getRetirePage(PersonalRetirePageReqVO pageReqVO);
    /**
     * 退休登记
     */
    void updatePersonalRetire(PersonalRetireUpdateVO reqVO);
    /**
     * 导出退休人事信息
     */
    List<PersonalRetireExcelVO> getPersonalRetireExcel(PersonalRetireExcelReqVO reqVO);

    /**
     * 获得离校人事信息分页
     * @param pageReqVO 分页查询
     * @return 退休人事信息分页
     */
    PageResult<PersonalLeavePageRespVO> getLeavePage(PersonalLeavePageReqVO pageReqVO);
    /**
     * 离校登记
     */
    void updatePersonalLeave(PersonalLeaveUpdateVO reqVO);
    /**
     * 导出退休人事信息
     */
    List<PersonalLeaveExcelVO> getPersonalLeaveExcel(PersonalLeaveExcelReqVO reqVO);

    /**
     * 获得去世人事信息分页
     * @param pageReqVO 分页查询
     * @return 退休人事信息分页
     */
    PageResult<PersonalDeathPageRespVO> getDeathPage(PersonalDeathPageReqVO pageReqVO);
    /**
     * 去世登记
     */
    void updatePersonalDeath(PersonalDeathUpdateVO reqVO);
    /**
     * 导出去世人事信息
     */
    List<PersonalDeathExcelVO> getPersonalDeathExcel(PersonalDeathExcelReqVO reqVO);

    void sendAllPersonInfo(String url);

    List<PersonnalPageRespVO> getPersonnalPageChildren(PersonnalChildrenReqVO pageVO);
}
