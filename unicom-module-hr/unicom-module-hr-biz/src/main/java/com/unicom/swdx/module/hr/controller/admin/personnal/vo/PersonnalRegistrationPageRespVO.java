package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("人事信息分页 Response VO")
@Data
@ToString(callSuper = true)
public class PersonnalRegistrationPageRespVO {


    @ApiModelProperty(value = "人事编号", example = "1")
    private Long id;

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "工作证号", example = "202410001")
    private String workId;
    @ApiModelProperty(value = "性别")
    private Integer department;

    private List<Long> deptIds;

    private Long userId;

    @ApiModelProperty(value = "部门")
    private Integer gender;
    @ApiModelProperty(value = "报到日期")
    private LocalDateTime registrationDate;
    @ApiModelProperty(value = "人员分类")
    private Integer peronClassification;
}
