package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("人事信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonnalRegistrationPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;


    @ApiModelProperty(value = "性别",example = "1")
    private Integer gender;

    @ApiModelProperty(value = "部门")
    private Integer department;

    @ApiModelProperty(value = "报到日期", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registrationDate;
    @ApiModelProperty(value = "人员分类")
    private Integer peronClassification;


}
