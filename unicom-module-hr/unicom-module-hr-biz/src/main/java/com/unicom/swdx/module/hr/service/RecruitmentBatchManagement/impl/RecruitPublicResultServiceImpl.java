package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicResultVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitPublicResultMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitPublicResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitPublicResultServiceImpl extends ServiceImpl<RecruitPublicResultMapper, RecruitPublicResult> implements RecruitPublicResultService {

    @Resource
    RecruitPublicResultMapper recruitPublicResultMapper;


    @Override
    public PageResult<RecruitPublicResult> queryByList(RecruitPublicResultVO recruitPublicResultVO) {

        IPage<RecruitPublicResult> page = MyBatisUtils.buildPage(recruitPublicResultVO);
        List<RecruitPublicResult> data = recruitPublicResultMapper.queryByList(page, recruitPublicResultVO);

        return new PageResult<>(data, page.getTotal());
    }
}
