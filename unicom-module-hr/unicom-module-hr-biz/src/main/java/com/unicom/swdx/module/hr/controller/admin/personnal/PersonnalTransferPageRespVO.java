package com.unicom.swdx.module.hr.controller.admin.personnal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveHrIDCardSerializer;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveMobileSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("人事信息分页 Response VO")
@Data
@ToString(callSuper = true)
public class PersonnalTransferPageRespVO {

    @ApiModelProperty(value = "人事编号", required = true, example = "1")
    private Long id;
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;
    /**
     * 调动时间
     */
    @ApiModelProperty(value = "调动时间")
    private LocalDateTime transferTime;
    /**
     * 调出部门名称
     */
    @ApiModelProperty(value = "调出部门名称")
    private String outDeptNames;
    /**
     * 调入部门名称
     */
    @ApiModelProperty(value = "调入部门名称")
    private String inDeptNames;
    /**
     * 调动原因
     */
    @ApiModelProperty(value = "调动原因")
    private String transferReason;
    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    private LocalDateTime createTime;


}
