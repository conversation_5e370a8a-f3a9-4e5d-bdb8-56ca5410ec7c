package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitAccessoryInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitLearningExp;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitRelationship;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitWorkExp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "RecruitApply", description = "应聘职位")
public class RecruitApplyVO extends RecruitBasicInfoVO {

    /**
     * 家庭成员及社会关系
     */
    @ApiModelProperty(value = "家庭成员及社会关系")
    private List<RecruitRelationship> recruitRelationships;

    /**
     * 教育经历
     */
    @ApiModelProperty(value = "教育经历")
    private List<RecruitLearningExp> recruitLearningExps;

    /**
     * 工作经历
     */
    @ApiModelProperty(value = "工作经历")
    private List<RecruitWorkExp> recruitWorkExps;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<RecruitAccessoryInfo> recruitAccessoryInfo;

}
