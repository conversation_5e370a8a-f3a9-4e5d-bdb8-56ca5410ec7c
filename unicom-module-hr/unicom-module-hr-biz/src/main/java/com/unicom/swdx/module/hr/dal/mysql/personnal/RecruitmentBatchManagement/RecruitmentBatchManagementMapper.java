package com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentBatchManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentBatchManagementVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招聘批次管理(HrRecruitmentBatchManagement)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:36
 */

@Mapper
public interface RecruitmentBatchManagementMapper extends BaseMapperX<RecruitmentBatchManagement> {

    List<RecruitmentBatchManagement> queryByList(IPage page, @Param("param") RecruitmentBatchManagementVO recruitmentBatchManagementVO);

    Integer deRepeat(@Param("params") String params);

    Integer deRepeatEdit(@Param("params") String params, @Param("id") Integer id);
}
