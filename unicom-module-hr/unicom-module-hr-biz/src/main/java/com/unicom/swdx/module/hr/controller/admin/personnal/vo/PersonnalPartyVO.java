package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonnalPartyVO {

    /**
     * 入党时间
     */
    @ApiModelProperty(value = "入党时间", required = true, example = "2022-01-01 00:00:00")
    private Date entryPartyTime;

    /**
     * 党员转正时间
     */
    @ApiModelProperty(value = "党员转正时间", required = true, example = "2023-01-01 00:00:00")
    private Date admissionPartyTime;

    /**
     * 所属党支部
     */
    @ApiModelProperty(value = "所属党支部", required = true, example = "1")
    private String affiliatedParty;

    /**
     * 上级党组织
     */
    @ApiModelProperty(value = "上级党组织", required = true, example = "中共XX市委")
    private String higherPartyOrganizations;

    /**
     * 入党介绍人
     */
    @ApiModelProperty(value = "入党介绍人", required = true, example = "李四")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String partyRecommendPerson;

    /**
     * 获得奖励情况
     */
    @ApiModelProperty(value = "获得奖励情况", required = true, example = "优秀党员")
    @Length(min = 0, max = 300, message = "长度为 0-300 位")
    private String awardSituation;

    /**
     * 获得处分情况
     */
    @ApiModelProperty(value = "获得处分情况", required = true, example = "无")
    @Length(min = 0, max = 300, message = "长度为 0-300 位")
    private String punishSituation;

}
