package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_transfer", autoResultMap = true)
@KeySequence("hr_personnel_transfer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalTransferDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;


    /**
     * 姓名
     */
    private String name;

    /**
     * 调出部门
     */
    private String outDeptIds;

    /**
     * 调出部门名称
     */
    private String outDeptNames;

    /**
     * 职级
     */
    private Integer outRank;

    /**
     * 行政职务名称
     */
    private String outAdministrativePositionName;

    /**
     * 行政职务级别
     */
    private Integer outAdministrativePositionRank;

    /**
     * 调出科室
     */
    private String outSubjectRoom;

    /**
     * 调入部门
     */
    private String inDeptIds;

    /**
     * 调入部门名称
     */
    private String inDeptNames;

    /**
     * 调入职级
     */
    private Integer inRank;

    /**
     * 调入行政职务名称
     */
    private String inAdministrativePositionName;

    /**
     * 调入行政职务级别
     */
    private Integer inAdministrativePositionRank;

    /**
     * 调入科室
     */
    private String inSubjectRoom;

    /**
     * 调动时间
     */
    private LocalDateTime transferTime;


    /**
     * 调动原因
     */
    private String transferReason;

    /**
     * 备注
     */
    private String notes;

    /**
     * 机构ID
     */
    private Long tenantId;
}
