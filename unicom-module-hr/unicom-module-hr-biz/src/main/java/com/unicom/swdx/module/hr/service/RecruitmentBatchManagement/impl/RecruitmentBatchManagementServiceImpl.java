package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentBatchManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentPositionManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentBatchManagementVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitmentBatchManagementMapper;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitmentPositionManagementMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitmentBatchManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 招聘批次管理(HrRecruitmentBatchManagement)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:37
 */
@Service
@Validated
@Slf4j
public class RecruitmentBatchManagementServiceImpl extends ServiceImpl<RecruitmentBatchManagementMapper, RecruitmentBatchManagement> implements RecruitmentBatchManagementService {

    @Resource
    private RecruitmentBatchManagementMapper recruitmentBatchManagementMapper;

    @Resource
    private RecruitmentPositionManagementMapper recruitmentPositionManagementMapper;

    /**
     * 批次启用状态
     * 进行中
     */
    private static final String ENABLE_YES = "1";

    /**
     * 批次启用状态
     * 完成
     */
    private static final String ENABLE_NO = "2";

    /**
     * 输入校验
     * 30个汉字
     */
    private static final Integer TEXT_LIMIT = 30;

    /**
     * 前端返回
     * 提示消息 code
     */
    private static final String CODE_INFO = "code";

    /**
     * 前端返回
     *    提示消息 message
     */
    private static final String MESSAGE_INFO = "message";


    @Override
    public PageResult<RecruitmentBatchManagement> queryByList(RecruitmentBatchManagementVO recruitmentBatchManagementVO) {

        IPage<RecruitmentBatchManagement> page = MyBatisUtils.buildPage(recruitmentBatchManagementVO);
        List<RecruitmentBatchManagement> data = recruitmentBatchManagementMapper.queryByList(page, recruitmentBatchManagementVO);
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public List<RecruitmentBatchManagement> queryAll(RecruitmentBatchManagementVO recruitmentBatchManagementVO) {
        LambdaQueryWrapperX<RecruitmentBatchManagement> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(RecruitmentBatchManagement::getStatus, recruitmentBatchManagementVO.getStatus())
                .orderByDesc(RecruitmentBatchManagement::getCreateTime);
        return this.list(wrapper);
    }


    @Override
    public ResponseEntity<Map<String, Object>> add(RecruitmentBatchManagement recruitmentBatchManagement) {

        HashMap<String, Object> result = new HashMap<>();

        if (recruitmentBatchManagement.getBatchName().length() > TEXT_LIMIT) {
            result.put(CODE_INFO, 300);
            result.put(MESSAGE_INFO, "输入超过30个字，请重新输入！");
            return ResponseEntity.ok(result);
        }


        //招聘批次名称 去重校验
        if(StringUtils.isNotBlank(recruitmentBatchManagement.getBatchName())){
            int count = recruitmentBatchManagementMapper.deRepeat(recruitmentBatchManagement.getBatchName());
            if (count > 0) {
                result.put(CODE_INFO, 300);
                result.put(MESSAGE_INFO, "批次名称已存在，请重新输入！");
                return ResponseEntity.ok(result);
            }
        }else{
            result.put(CODE_INFO, 300);
            result.put(MESSAGE_INFO, "批次名称为空，请重新输入！");
            return ResponseEntity.ok(result);
        }


        try {
            // 插入创建时间
 //           recruitmentBatchManagement.setCreateTime(LocalDateTime.now());
            recruitmentBatchManagementMapper.insert(recruitmentBatchManagement);
            result.put(CODE_INFO, 200);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put(CODE_INFO, 300);
            return ResponseEntity.ok(result);
        }

    }

    @Override
    public ResponseEntity<Map<String, Object>> edit(RecruitmentBatchManagement recruitmentBatchManagement) {

        HashMap<String, Object> result = new HashMap<>();

        //招聘批次名称 去重校验
        if(StringUtils.isNotBlank(recruitmentBatchManagement.getBatchName())){
            int count = recruitmentBatchManagementMapper.deRepeatEdit(recruitmentBatchManagement.getBatchName(),
                    recruitmentBatchManagement.getId());
            if (count > 0) {
                result.put(CODE_INFO, 300);
                result.put(MESSAGE_INFO, "批次名称已存在，请重新输入！");
                return ResponseEntity.ok(result);
            }
        }else{
            result.put(CODE_INFO, 300);
            result.put(MESSAGE_INFO, "批次名称为空，请重新输入！");
            return ResponseEntity.ok(result);
        }

        if (recruitmentBatchManagement.getBatchName().length() > TEXT_LIMIT) {
            result.put(CODE_INFO, 300);
            result.put(MESSAGE_INFO, "输入超过30个字，请重新输入！");
            return ResponseEntity.ok(result);
        }

        try {
            recruitmentBatchManagementMapper.updateById(recruitmentBatchManagement);
            result.put(CODE_INFO, 200);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put(CODE_INFO, 300);
            return ResponseEntity.ok(result);
        }
    }

    @Override
    public ResponseEntity<String> delete(RecruitmentBatchManagement recruitmentBatchManagement) {
        try {
            recruitmentBatchManagementMapper.deleteById(recruitmentBatchManagement);
            recruitmentPositionManagementMapper.delete(new QueryWrapper<RecruitmentPositionManagement>()
                    .eq("owning_batch", recruitmentBatchManagement.getId()));
            return ResponseEntity.ok("删除成功！");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("删除失败，请重试！");
        }
    }

    @Override
    public ResponseEntity<String> enable(RecruitmentBatchManagement recruitmentBatchManagement) {
        try {
            if (recruitmentBatchManagement.getStatus().equals(ENABLE_YES)) {
                recruitmentBatchManagement.setStatus(ENABLE_YES);
                recruitmentBatchManagementMapper.updateById(recruitmentBatchManagement);
                return ResponseEntity.ok("启用成功！");
            } else {
                recruitmentBatchManagement.setStatus(ENABLE_NO);
                recruitmentBatchManagementMapper.updateById(recruitmentBatchManagement);
                return ResponseEntity.ok("已完成！");
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("启用失败，请重试！");
        }
    }
}
