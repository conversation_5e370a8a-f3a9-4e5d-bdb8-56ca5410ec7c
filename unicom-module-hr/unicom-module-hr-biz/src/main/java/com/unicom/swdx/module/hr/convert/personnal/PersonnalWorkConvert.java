package com.unicom.swdx.module.hr.convert.personnal;

import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PersonnalWorkConvert {

    PersonnalWorkConvert INSTANCE = Mappers.getMapper(PersonnalWorkConvert.class);

    List<PersonnalWorkDO> convert(List<PersonnalWorkVO> bean);

    List<PersonnalWorkGetVO> convert0(List<PersonnalWorkDO> bean);

    List<PersonnalWorkDO> convert1(List<PersonnalWorkGetVO> bean);
    List<PersonnalWorkDO> convert2(List<PersonnalWorkExcelVO> bean);
    List<PersonnalWorkDO> convertList(List<PersonnalWorkExcelVO> bean);
}
