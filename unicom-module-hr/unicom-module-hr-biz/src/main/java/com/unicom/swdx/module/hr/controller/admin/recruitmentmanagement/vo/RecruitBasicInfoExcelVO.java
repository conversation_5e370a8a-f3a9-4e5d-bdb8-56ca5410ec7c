package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@ToString(callSuper = true)
@ApiModel(value = "HrRecruitBasicInfo", description = "简历基本信息")
public class RecruitBasicInfoExcelVO {
    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd";
    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    @ExcelProperty(value = "证件号")
    private String cardNo;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名")
    private String name;
    /**
     * 招聘单位
     */
    @ApiModelProperty(value = "招聘单位")
    @ExcelProperty(value = "招聘单位名称")
    private String recruitmentUnit;
    /**
     * 性别
     */
    @ExcelIgnore
    private Integer sex;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别")
    private String gender;
    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "出生日期")
    @ExcelProperty(value = "出生日期")
    private LocalDate birthday;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    @ExcelProperty(value = "政治面貌")
    private String politicsStatus;
    /**
     * 所属民族
     */
    @ApiModelProperty(value = "所属民族")
    @ExcelProperty(value = "民族")
    private String nation;
    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    @ExcelProperty(value = "籍贯")
    private String nativePlace;
    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @ExcelProperty(value = "婚姻状况")
    private String maritalStatus;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    @ExcelProperty(value = "联系电话")
    private String phone;
    /**
     * 户口性质
     */
    @ApiModelProperty(value = "户口性质")
    @ExcelProperty(value = "户口性质")
    private String householdType;
    /**
     * 单位性质
     */
    @ApiModelProperty(value = "单位性质")
    @ExcelProperty(value = "单位性质")
    private String workUnitType;
    /**
     * 户籍所在地
     */
    @ApiModelProperty(value = "户籍所在地")
    @ExcelProperty(value = "户籍所在地")
    private String domicile;
    /**
     * 居住所在地
     */
    @ApiModelProperty(value = "居住所在地")
    @ExcelProperty(value = "居住所在地")
    private String residence;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @ExcelProperty(value = "学历")
    private String highestEducationLevel;
    /**
     * 最高学位
     */
    @ApiModelProperty(value = "最高学位")
    @ExcelProperty(value = "学位")
    private String highestAcademicDegree;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    @ExcelProperty(value = "年龄")
    private Integer age;

    /**
     * 最高学历毕业院校
     */
    @ApiModelProperty(value = "最高学历毕业院校")
    @ExcelProperty(value = "毕业学校")
    private String highestGraduateSchool;
    /**
     * 最后毕业时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "最后毕业时间")
    @ExcelProperty(value = "毕业日期")
    private LocalDate lastGraduationTime;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    @ExcelProperty(value = "所学专业")
    private String major;
    /**
     * 人员类别
     */
    @ApiModelProperty(value = "人员类别")
    @ExcelProperty(value = "人员类别")
    private String personalClassification;
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称")
    private String recruitmentDept;
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @ExcelProperty(value = "应聘岗位")
    private String jobTitle;
    /**
     * 岗位代码
     */
    @ApiModelProperty(value = "岗位代码")
    @ExcelProperty(value = "岗位代码")
    private String postCode;
    /**
     * 参加工作时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "参加工作时间")
    @ExcelProperty(value = "参加工作日期")
    private LocalDate employmentTime;
    /**
     * 笔试成绩
     */
    @ApiModelProperty(value = "笔试成绩")
    @ExcelProperty(value = "笔试成绩")
    private String writtenTestScore;
    /**
     * 面试成绩
     */
    @ApiModelProperty(value = "面试成绩")
    @ExcelProperty(value = "面试成绩")
    private String interviewScore;
    /**
     * 实操成绩
     */
    @ApiModelProperty(value = "实操成绩")
    @ExcelProperty(value = "实操成绩")
    private String actualScore;
    /**
     * 综合成绩
     */
    @ApiModelProperty(value = "综合成绩")
    @ExcelProperty(value = "综合成绩")
    private String comprehensiveScore;
    /**
     * 专业技术资格
     */
    @ApiModelProperty(value = "专业技术资格")
    @ExcelProperty(value = "专业技术资格")
    private String professionalQualification;
    /**
     * 专业技术资格取得时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "专业技术资格取得时间")
    @ExcelProperty(value = "专业技术资格取得时间")
    private LocalDate professionalAcquireTime;
    /**
     * 职业资格
     */
    @ApiModelProperty(value = "职业资格")
    @ExcelProperty(value = "职业资格")
    private String qualificationCertificate;
    /**
     * 职称、执（职）业资格取得时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "执（职）业资格取得时间")
    @ExcelProperty(value = "职业资格取得时间")
    private LocalDate acquisitionTime;
    /**
     * 个人简历（从高中简历起）
     */
    @ApiModelProperty(value = "个人简历（从高中简历起）")
    @ExcelProperty(value = "个人简历（从高中简历起）")
    private String resumeInfo;
    /**
     * 曾受到何种奖励或处分
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty(value = "曾受到何种奖励或处分")
    private String rewardAndPunishment;
    /**
     * 体检结果
     */
    @ApiModelProperty(value = "体检结果")
    @ExcelProperty(value = "体检结果")
    private String physicalExamination;
    /**
     * 考察结果
     */
    @ApiModelProperty(value = "考察结果")
    @ExcelProperty(value = "考察结果")
    private String investigationResult;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;


    /**
     * 关系
     */
    @ApiModelProperty(value = "关系")
    @ExcelProperty({"家庭成员及主要社会关系","关系"})
    private String relation;
    /**
     * 成员姓名
     */
    @ApiModelProperty(value = "成员姓名")
    @ExcelProperty({"家庭成员及主要社会关系","姓名"})
    private String memberName;
    /**
     * 成员年龄
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty({"家庭成员及主要社会关系","年龄"})
    private String memberAge;
    /**
     * 成员政治面貌
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty({"家庭成员及主要社会关系","政治面貌"})
    private String memberPoliticalStatus;
    /**
     * 成员工作单位及职务
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty({"家庭成员及主要社会关系","工作单位及职务"})
    private String workUnitAndPosition;


}
