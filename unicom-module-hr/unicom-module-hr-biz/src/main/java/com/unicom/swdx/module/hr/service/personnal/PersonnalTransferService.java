package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.personnal.PersonnalTransferPageRespVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalTransferDO;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.List;

/**
 * 人事Service 接口
 *
 */
public interface PersonnalTransferService extends IService<PersonnalTransferDO> {

    //创建调动
    Long createPersonnalTransfer(PersonnalCreateTransferReqVO createReqVO);

    //分页
    PageResult<PersonnalTransferPageRespVO> getPersonnalTransferPage(PersonnalTransferPageReqVO pageVO);

    //获得某人的调动记录
    List<PersonnalTransferPageRespVO> getPersonnalTransferByUser(String userId);

    //获得详情调动记录
    PersonnalTransferDetailResqVO getPersonnalTransferId(Long id);

    //获得调出信息
    PersonnalTransferOutVO getPersonnalTransferOut(String userId);

    //分页
    List<PersonnalTransferExcelVO> getPersonnalTransferExcel(PersonnalTransferPageReqVO pageVO);

}
