package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitRelationship;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitRelationshipMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitRelationshipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Service
@Validated
@Slf4j
public class RecruitRelationshipServiceImpl extends ServiceImpl<RecruitRelationshipMapper, RecruitRelationship> implements RecruitRelationshipService {
}
