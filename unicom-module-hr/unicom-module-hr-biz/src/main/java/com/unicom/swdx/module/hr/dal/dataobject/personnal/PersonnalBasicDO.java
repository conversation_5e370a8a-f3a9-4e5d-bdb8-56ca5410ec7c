package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_basic_information", autoResultMap = true)
@KeySequence("hr_personnel_basic_information_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalBasicDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 机构ID
     */
    private Long tenantId;

    /**
     * 工作证号
     */
    private String workId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件类型
     * 枚举 {@link IdTypeEnum}
     */
    private Integer idType;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 性别
     * 枚举 {@link GenderEnum}
     */
    private Integer gender;
    /**
     * 照片地址
     */
    private String photo;
    /**
     * 年龄
     */
    private String age;
    /**
     * 出生日期
     */
    private LocalDateTime birthday;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 政治面貌
     * 枚举 {@link PoliticalOutlookEnum}
     */
    @TableField(value = "political_status")
    private Integer politicalOutlook;
    /**
     * 籍贯
     */
    private String nativePlace;
    /**
     * 民族
     * 枚举 {@link NationEnum}
     */
    private Integer nation;
    /**
     * 人员分类
     * 枚举 {@link PersonClassificationEnum}
     */
    @TableField(value = "person_classification")
    private Integer peronClassification;
    /**
     * 家庭住址
     */
    @TableField(value = "address")
    private String familyAddress;
    /**
     * 人员状态
     * 枚举 {@link PersonnalStatusEnum}
     */
    @TableField(value = "person_status")
    private Integer personnalStatus;
    /**
     * 入职时间
     */
    private LocalDateTime entryDate;
    /**
     * 转正时间
     */
    private LocalDateTime confirmationDate;
    /**
     * 部门
     * 枚举 {@link DepartmentEnum}
     */
    @TableField(value = "dept_id")
    private Long department;

    @TableField(exist = false)
    private List<Long> deptIds;
    /**
     * 科室
     */
    private String subjectRoom;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 婚姻状态
     * 枚举 {@link MarryStatusEnum}
     */
    private Integer marryStatus;
    /**
     * 审核状态
     * 枚举 {@link ReviewStatusEnum}
     */
    private Integer reviewStatus;
    /**
     * 录用方式
     * 枚举
     */
    private Integer recruitmentMethod;
    /**
     * 到校时间
     */
    private LocalDateTime arrivalDate;
    /**
     * 参加工作时间
     */
    private LocalDateTime joinWorkDate;
    /**
     * 报到时间
     */
    private LocalDateTime registrationDate;
    /**
     * 是否有使用期
     */
    private Boolean ifTrial;
    /**
     * 试用期截至时间
     */
    private LocalDateTime trialDeadline;

    /**
     * 退休时间
     */
    private LocalDateTime retireTime;
    /**
     * 退休备注
     */
    private String retireRemarks;
    /**
     * 离校时间
     */
    private LocalDateTime leaveTime;
    /**
     * 离校途径
     */
    private String leaveChannel;
    /**
     * 离校去向
     */
    private String leaveDestination;
    /**
     * 离校原因
     */
    private String leaveReason;
    /**
     * 退休备注
     */
    private String leaveRemarks;
    /**
     * 去世时间
     */
    private LocalDateTime deathTime;
    /**
     * 去世前状态
     */
    private String deathStatus;
    /**
     * 去世备注
     */
    private String deathRemarks;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 证件号码(des)加密
     */
    private String idNumberDes;
}
