package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitBasicInfo;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */
public interface RecruitBasicInfoService extends IService<RecruitBasicInfo> {

    PageResult<RecruitBasicInfo> queryByList(RecruitBasicInfoVO recruitBasicInfoVO);

    PageResult<RecruitBasicInfo> faceByList(RecruitBasicInfoVO recruitBasicInfoVO);

    ResponseEntity<Map<String,Object>> check(RecruitBasicInfoVO recruitBasicInfoVO);


    Integer apply(RecruitApplyVO recruitApplyVO);

    Integer saveDraft(RecruitApplyVO recruitApplyVO);

    void backToModify(RecruitBasicInfo recruitBasicInfo);
    RecruitDetailRespVO detail(Integer id);

    List<RecruitBasicInfoRespVO> deliveryStatus();

    PageResult<RecruitStationVO> getStationPage(RecruitStationRequestVO recruitStationRequestVO);

    RecruitInformationVO getInformationById(Long id);
    List<Long> getMobilesByIds(List<Long> ids);


    List<RecruitBasicInfoExcelVO> getBasicInfoExcel(RecruitBasicInfoExportReqVO reqVO);

    List<List<RecruitApplyExcelVO>> getApplyExcel(RecruitApplyExportReqVO reqVO);
}
