package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实体类
 *
 * <AUTHOR>
 * @data 2024/3/6 9:04
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "recruit_public_book")
@ApiModel(value = "RecruitPublicBook", description = "公开出版教材表")
public class RecruitPublicBook implements Serializable {

    private static final long serialVersionUID = -39063311227083900L;
    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 出版日期
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "出版日期")
    private LocalDateTime publicationTime;
    /**
     * 出版书籍题目
     */
    @ApiModelProperty(value = "出版书籍题目")
    private String title;
    /**
     * 出版单位
     */
    @ApiModelProperty(value = "出版单位")
    private String publicationUnit;
    /**
     * 出版刊物本人署名情况
     */
    @ApiModelProperty(value = "出版刊物本人署名情况")
    private String ranking;
}
