package com.unicom.swdx.module.hr.controller.admin.personnal;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalMapper;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.hr.service.personnal.*;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;
import static com.unicom.swdx.module.hr.enums.ErrorCodeConstants.PERSONNAL_IMPORT_OUT_MEMORY;

@Api(tags = "人事调动")
@RestController
@RequestMapping("/hr/personnal/transfer")
public class PersonnalTransferController {
    @Resource
    private PersonnalService personnalService;
    @Resource
    private PersonnalStudyService personnalStudyService;
    @Resource
    private PersonnalPositionService personnalPositionService;
    @Resource
    private PersonnalWorkService personnalWorkService;
    @Resource
    private PersonnalPartyService personnalPartyService;
    @Resource
    private PersonnalMapper personnalMapper;
    @Resource
    private PersonProducer personProducer;
    @Resource
    private TenantApi tenantApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    PersonnalTransferService personnalTransferService;



    @PostMapping("/create")
    @ApiOperation("新增人事调动信息")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:transfer')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Long> createPersonnalTransfer(@Valid @RequestBody PersonnalCreateTransferReqVO createReqVO) {
        Long id = personnalTransferService.createPersonnalTransfer(createReqVO);

        return success(id);
    }
    @GetMapping("/page")
    @ApiOperation("获得人事调动信息分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:transfer')")
    public CommonResult<PageResult<PersonnalTransferPageRespVO>> getPersonnalTransferPage(@Valid PersonnalTransferPageReqVO pageVO) {
        PageResult<PersonnalTransferPageRespVO> pageResult = personnalTransferService.getPersonnalTransferPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/get_user")
    @ApiOperation("获得个人人事调动详情")
    @PreAuthorize("@ss.hasPermission('hr:personnal:transfer')")
    public CommonResult<List<PersonnalTransferPageRespVO>> getPersonnalTransferUser(@RequestParam(required = true)String userId) {
        List<PersonnalTransferPageRespVO> pageResult = personnalTransferService.getPersonnalTransferByUser(userId);
        return success(pageResult);
    }

    @GetMapping("/get")
    @ApiOperation("获得人事调动详情")
    @PreAuthorize("@ss.hasPermission('hr:personnal:transfer')")
    public CommonResult<PersonnalTransferDetailResqVO> getPersonnalTransfer(@Valid Long id) {
        PersonnalTransferDetailResqVO pageResult = personnalTransferService.getPersonnalTransferId(id);
        return success(pageResult);
    }

    @GetMapping("/get_out")
    @ApiOperation("获得调出人事信息")
    @PreAuthorize("@ss.hasPermission('hr:personnal:transfer')")
    public CommonResult<PersonnalTransferOutVO> getPersonnalTransferOut(@RequestParam(required = true) String userId) {
        PersonnalTransferOutVO transferOut = personnalTransferService.getPersonnalTransferOut(userId);
        return success(transferOut);
    }

    @GetMapping("/export")
    @ApiOperation("导出人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:transfer')")
//    @OperateLog(type = EXPORT)
    public void export(HttpServletResponse response, @Validated PersonnalTransferPageReqVO reqVO) throws IOException {
        List<PersonnalTransferExcelVO> personnals = personnalTransferService.getPersonnalTransferExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "人事调动信息.xls", "人事信息调动列表", PersonnalTransferExcelVO.class, personnals);
    }



}
