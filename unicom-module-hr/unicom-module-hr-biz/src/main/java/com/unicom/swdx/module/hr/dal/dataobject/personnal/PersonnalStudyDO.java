package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_study_experience", autoResultMap = true)
@KeySequence("hr_personnel_study_experience_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalStudyDO extends BaseDO {
    /**
     * 人事ID
     */
    @TableField(value = "personnel_id")
    private Long personnalId;
    /**
     * 机构ID
     */
    private Long tenantId;
    /**
     * 租户编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 毕业学校
     */
    @TableField(value = "school")
    private String graduationSchool;
    /**
     * 入学时间
     */
    private LocalDateTime admissionDate;
    /**
     * 结束学业时间
     */
    private LocalDateTime endStudyDate;
    /**
     * 获得学历（最高）
     */
    private Integer education;
    /**
     * 获得学位（最高）
     */
    @TableField(value = "degree")
    private Integer academicDegree;

}
