package com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class PersonalLeaveUpdateVO {
    @ApiModelProperty(value = "编号", required = true, example = "1")
    @NotNull(message = "编号不能为空")
    private Long id;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校时间", required = true, example = "2024-01-01 00:00:00")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime leaveTime;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校途径", required = true, example = "这是一条途径")
    private String leaveChannel;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校去向", required = true, example = "这是一条去向")
    private String leaveDestination;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校原因", required = true, example = "这是一条原因")
    private String leaveReason;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校备注", required = true, example = "这是一条备注")
    private String leaveRemarks;
    /**
     * 工作证号
     * 枚举
     */
    @ApiModelProperty(value = "离校联系方式", required = true, example = "这是一条备注")
    private String leaveMobile;
}

