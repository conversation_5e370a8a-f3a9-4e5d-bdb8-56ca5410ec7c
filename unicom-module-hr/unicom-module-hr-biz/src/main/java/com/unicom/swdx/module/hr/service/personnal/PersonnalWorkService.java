package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalWorkGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalWorkVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 人事工作经历Service 接口
 *
 */
public interface PersonnalWorkService extends IService<PersonnalWorkDO> {


    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createPersonnal(@Valid List<PersonnalWorkVO> createReqVO, Long personnalId);
    void createPersonnalRegis(@Valid PersonnalCreateRegistrationVO createReqVO, Long personnalId);
    List<PersonnalWorkDO> getPersonnal(Long id);
    void  updatePersonnal(List<PersonnalWorkGetVO> updateReqVO);
}
