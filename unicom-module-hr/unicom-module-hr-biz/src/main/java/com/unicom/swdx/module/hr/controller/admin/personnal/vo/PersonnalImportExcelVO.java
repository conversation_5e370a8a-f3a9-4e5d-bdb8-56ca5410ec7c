package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.hr.convert.utils.LocalDateTimeConverter;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免人事信息导入有问题
public class PersonnalImportExcelVO {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名\n（必填）")
//    @NotNull(message = "姓名不能为空")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String name;

    /**
     * 证件类型
     * 枚举
     */
    @ExcelProperty(value = "证件类型\n（必填）",converter = DictConvert.class)
//    @NotNull(message = "证件类型不能为空")
    @DictFormat(DictTypeConstants.PERSONNEL_ID_TYPE)
    private Integer idType;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码\n（必填）")
//    @NotNull(message = "证件号码不能为空")
    private String idNumber;
    /**
     * 性别
     * 枚举
     */
    @ExcelProperty(value = "性别\n（必填）",converter = DictConvert.class)
//    @NotNull(message = "性别不能为空")
    @DictFormat(DictTypeConstants.PERSON_GENDER)
    private Integer gender;
    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期",converter = LocalDateTimeConverter.class)
//    @NotNull(message = "出生日期不能为空")
    private LocalDateTime birthday;
    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码/座机号\n（必填）")
//    @NotNull(message = "手机号码/座机号不能为空")
    private String mobile;
    /**
     * 政治面貌
     * 枚举
     */
    @ExcelProperty(value = "政治面貌",converter = DictConvert.class)
//    @NotNull(message = "政治面貌不能为空")
    @DictFormat(DictTypeConstants.PERSONNEL_POLITICAL_OUTLOOK)
    private Integer politicalOutlook;
    /**
     * 婚姻状态
     * 枚举
     */
    @ExcelProperty(value = "婚姻状态",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_MARRYL_STATUS)
    private Integer marryStatus;
//    /**
//     * 照片地址
//     */
//    @ExcelProperty(value = "照片地址\n必填")
//    @NotNull(message = "不能为空")
//    private String photo;


//    /**
//     * 年龄
//     */
//    @ExcelProperty(value = "年龄")
//    private String age;
    /**
     * 籍贯
     */
    @ExcelProperty(value = "籍贯")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String nativePlace;
    /**
     * 民族
     * 枚举
     */
    @ExcelProperty(value = "民族",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_NATION)
    private Integer nation;

    /**
     * 部门
     * 枚举
     */
    @ExcelProperty(value = "部门\n（必填）")
//    @NotNull(message = "部门\n不能为空")
    private String departmentName;
    /**
     * 人员分类
     * 枚举
     */
    @ExcelProperty(value = "人员分类\n（必填）",converter = DictConvert.class)
//    @NotNull(message = "人员分类不能为空")
    @DictFormat(DictTypeConstants.PERSONNEL_PERSON_CLASSIFICATION)
    private Integer peronClassification;
    /**
     * 人员状态
     * 枚举
     */
    @ExcelProperty(value = "人员状态\n（必填）",converter = DictConvert.class)
//    @NotNull(message = "人员状态不能为空")
    @DictFormat(DictTypeConstants.PERSONNEL_STATUS)
    private Integer personnalStatus;
    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "电子邮箱")
//    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String email;
    /**
     * 家庭住址
     */
    @ExcelProperty(value = "家庭住址")
//    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String familyAddress;
    /**
     * 报到时间
     */
    @ExcelProperty(value = "报到时间")
//    @NotNull(message = "报到时间不能为空")
    private LocalDateTime registrationDate;
    /**
     * 转正时间
     */
    @ExcelProperty(value = "转正时间")
    private LocalDateTime confirmationDate;
    /**
     * 试用期截止时间
     */
    @ExcelProperty(value = "试用期截止时间")
    private LocalDateTime trialDeadline;
    /**
     * 科室
     */
    @ExcelProperty(value = "科室")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String subjectRoom;

    @ExcelProperty(value = "录用方式",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.RECRUITMENT_METHOD)
    private Integer recruitmentStatus;
//    /**
//     * 入党时间
//     */
//    @ExcelProperty(value = "入党时间")
//    private LocalDateTime entryPartyTime;
//
//    /**
//     * 党员转正时间
//     */
//    @ExcelProperty(value = "党员转正时间")
//    private LocalDateTime admissionPartyTime;
//    /**
//     * 入党介绍人
//     */
//    @ExcelProperty(value = "入党介绍人")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
//    private String partyRecommendPerson;
//
//    /**
//     * 获得奖励情况
//     */
//    @ExcelProperty(value = "获得奖励情况")
//    @Length(min = 0, max = 300, message = "长度为 0-300 位")
//    private String awardSituation;
//
//    /**
//     * 获得处分情况
//     */
//    @ExcelProperty(value = "受到处分情况")
//    @Length(min = 0, max = 300, message = "长度为 0-300 位")
//    private String punishSituation;
    /**
     * 入学时间
     */
    @ExcelProperty(value = "入学日期")
//    @NotNull(message = "入学日期不能为空")
    private LocalDateTime admissionDate;
    /**
     * 结束学业时间
     */
    @ExcelProperty(value = "结束学业日期")
//    @NotNull(message = "结束学业日期不能为空")
    private LocalDateTime endStudyDate;
    /**
     * 毕业学校
     */
    @ExcelProperty(value = "毕业学校")
//    @NotNull(message = "毕业学校不能为空")
//    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String graduationSchool;

    /**
     * 获得学历（最高）
     */
    @ExcelProperty(value = "获得学历（最高）",converter = DictConvert.class)
//    @NotNull(message = "获得学历（最高）不能为空")
    @DictFormat(DictTypeConstants.PERSONNEL_EDUCATION)
    private Integer education;
    /**
     * 获得学位（最高）
     */

    @ExcelProperty(value = "获得学位（最高）",converter = DictConvert.class)
//    @NotNull(message = "获得学位（最高）不能为空")
    @DictFormat(DictTypeConstants.PERSONNEL_ACADEMIC_DEGREE)
    private Integer academicDegree;

    /**
     * 工作经历list
     */
    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位（一）")
//    @Length(min = 0, max = 50, message = "长度为 0-50 位")
//    @NotNull(message = "工作单位（一）不能为空")
    private String workUnit1;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间（一）")
//    @NotNull(message = "开始时间（一）不能为空")
    private LocalDateTime startTime1;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间（一）")
//    @NotNull(message = "结束时间（一）不能为空")
    private LocalDateTime endTime1;
    /**
     * 担任职务
     */
    @ExcelProperty(value = "担任职务（一）")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
//    @NotNull(message = "担任职务（一）不能为空")
    private String position1;
    /**
     * 工作内容
     */
    @ExcelProperty(value = "工作内容（一）")
//    @Length(min = 0, max = 500, message = "长度为 0-500 位")
    private String workContent1;
    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位（二）")
//    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String workUnit2;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间（二）")
    private LocalDateTime startTime2;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间（二）")
    private LocalDateTime endTime2;
    /**
     * 担任职务
     */
    @ExcelProperty(value = "担任职务（二）")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String position2;
    /**
     * 工作内容
     */
    @ExcelProperty(value = "工作内容（二）")
//    @Length(min = 0, max = 500, message = "长度为 0-500 位")
    private String workContent2;
    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位（三）")
//    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String workUnit3;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间（三）")
    private LocalDateTime startTime3;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间（三）")
    private LocalDateTime endTime3;
    /**
     * 担任职务
     */
    @ExcelProperty(value = "担任职务（三）")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String position3;
    /**
     * 工作内容
     */
    @ExcelProperty(value = "工作内容（三）")
//    @Length(min = 0, max = 500, message = "长度为 0-500 位")
    private String workContent3;




//    /**
//     * 所属党支部
//     */
//    @ExcelProperty(value = "所属党支部",converter = DictConvert.class)
//    @DictFormat(DictTypeConstants.PERSONNEL_PARTY_DEPT)
//    private Integer affiliatedParty;
//
//    /**
//     * 上级党组织
//     */
//    @ExcelProperty(value = "上级党组织",converter = DictConvert.class)
//    @DictFormat(DictTypeConstants.PERSONNEL_PARTY_HIGH_ORGANIZATION)
//    private Integer higherPartyOrganizations;

    /**
     * 职级
     */
    @ExcelProperty(value = "职级",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_PERSON_RANK)
    private Integer rank;
    /**
     * 行政职务名称
     */
    @ExcelProperty(value = "行政职务名称")
//    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String administrativePositionName;
    /**
     * 行政职务级别
     */
    @ExcelProperty(value = "行政职务级别",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_ADMINISTRATIVE_POSITION_RANK)
    private Integer administrativePositionRank;
    /**
     * 专业技术职称
     */
    @ExcelProperty(value = "专业技术职称",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_TECHNICAL_NAME)
    private Integer professionalTechnicalName;
    /**
     * 专业技术岗位级别
     */
    @ExcelProperty(value = "专业技术岗位级别",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_TECHNICAL_RANK)
    private Integer professionalTechnicalRank;
    /**
     * 职务评选时间
     */
    @ExcelProperty(value = "职务评选时间")
    private LocalDateTime jobSelectionTime;
    /**
     * 聘用现职时间
     */
    @ExcelProperty(value = "聘用现职时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime currentEmploymentTime;
    /**
     * 续评职务时间
     */
    @ExcelProperty(value = "续评职务时间")
    private LocalDateTime jobContinuationTime;


    /**
     * workid
     */
    private String workId;



    /**
     * workid
     */
    private String sendkafka;

}
