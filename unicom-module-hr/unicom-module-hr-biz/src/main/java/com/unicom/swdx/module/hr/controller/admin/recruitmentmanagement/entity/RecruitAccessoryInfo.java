package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * 实体类
 * <AUTHOR>
 * @data 2024/3/6 9:04
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hr_recruit_accessory_info")
@KeySequence("hr_recruit_accessory_info_seq")
@ApiModel(value = "RecruitAccessoryInfo", description = "附件信息")
public class RecruitAccessoryInfo implements Serializable {

    private static final long serialVersionUID = 617352328164787392L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 对应简历基本信息表的主键id
     */
    @ApiModelProperty(value = "对应简历表id", required = true, example = "1")
    private Integer baseId;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String accessoryName;
    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private String accessoryType;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String accessory;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private String accessorySort;
    /**
     * 附件url
     */
    @ApiModelProperty(value = "附件url")
    @TableField(value = "file")
    private String url;
}
