package com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentPositionManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentPositionManagementVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招聘批次管理(HrRecruitmentPositionManagement)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:36
 */

@Mapper
public interface RecruitmentPositionManagementMapper extends BaseMapperX<RecruitmentPositionManagement> {

    List<RecruitmentPositionManagement> queryByList(IPage page,@Param("param") RecruitmentPositionManagementVO recruitmentPositionManagementVO);

    Integer jobTitle(@Param("positionName") String positionName, @Param("owningBatch") Integer owningBatch);

    Integer jobTitleEdit(@Param("positionName") String positionName, @Param("owningBatch") Integer owningBatch, @Param("id") Integer id);

    Integer postCode(@Param("param") String params);

    Integer postCodeEdit(@Param("param") String params, @Param("id") Integer id);

}
