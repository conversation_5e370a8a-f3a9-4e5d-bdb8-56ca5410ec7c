package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@ToString(callSuper = true)
@ApiModel(value = "HrRecruitBasicInfo", description = "简历基本信息")
public class RecruitApplyExcelVO {
    /**
     * 时间格式
     */
    private static final String EXPORTDATE = "yyyy.MM.dd";
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @ExcelProperty(value = "应聘职位")
    private String jobTitle;
    /**
     * 岗位代码
     */
    @ApiModelProperty(value = "岗位代码")
    @ExcelProperty(value = "岗位代码")
    private String postCode;
    /**
     * 招聘单位
     */
    @ApiModelProperty(value = "应聘部门")
    @ExcelProperty(value = "应聘部门")
    private String recruitmentDept;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名")
    private String name;
    /**
     * 所属民族
     */
    @ApiModelProperty(value = "所属民族")
    @ExcelProperty(value = "民族")
    private String nation;
    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = EXPORTDATE)
    @ApiModelProperty(value = "出生日期")
    @ExcelProperty(value = "出生日期")
    private LocalDate birthday;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    @ExcelProperty(value = "联系电话")
    private String phone;
    /**
     * 最高学历毕业院校
     */
    @ApiModelProperty(value = "最高学历毕业院校")
    @ExcelProperty(value = "毕业学校")
    private String highestGraduateSchool;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    @ExcelProperty(value = "所学专业")
    private String major;
    /**
     * 最后毕业时间
     */
    @DateTimeFormat(pattern = EXPORTDATE)
    @ApiModelProperty(value = "最后毕业时间")
    @ExcelProperty(value = "毕业时间")
    private LocalDate lastGraduationTime;
    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    @ExcelProperty(value = "最高学历")
    private String highestEducationLevel;
    /**
     * 最高学位
     */
    @ApiModelProperty(value = "最高学位")
    @ExcelProperty(value = "最高学位")
    private String highestAcademicDegree;
    /**
     * 参加工作时间
     */
    @DateTimeFormat(pattern = EXPORTDATE)
    @ApiModelProperty(value = "参加工作时间")
    @ExcelProperty(value = "参加工作时间")
    private LocalDate employmentTime;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    @ExcelProperty(value = "政治面貌")
    private String politicsStatus;
    /**
     * 入党时间
     */
    @DateTimeFormat(pattern = EXPORTDATE)
    @ApiModelProperty(value = "入党时间")
    @ExcelProperty(value = "入党时间")
    private LocalDate partyJoinTime;
    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    @ExcelProperty(value = "籍贯")
    private String nativePlace;
    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @ExcelProperty(value = "婚姻状况")
    private String maritalStatus;
    /**
     * 户口性质
     */
    @ApiModelProperty(value = "户口性质")
    @ExcelProperty(value = "户口性质")
    private String householdType;

    /**
     * 户籍所在地
     */
    @ApiModelProperty(value = "户籍所在地")
    @ExcelProperty(value = "户口所在地")
    private String domicile;
    /**
     * 居住所在地
     */
    @ApiModelProperty(value = "居住所在地")
    @ExcelProperty(value = "居住所在地")
    private String residence;
    /**
     * 是否应届生
     */
    @ApiModelProperty(value = "是否应届生")
    @ExcelProperty(value = "是否应届生")
    private String freshGraduate;
    /**
     * 专业技术资格
     */
    @ApiModelProperty(value = "专业技术资格")
    @ExcelProperty(value = "专业技术资格")
    private String professionalQualification;
    /**
     * 专业技术资格取得时间
     */
    @DateTimeFormat(pattern = EXPORTDATE)
    @ApiModelProperty(value = "专业技术资格取得时间")
    @ExcelProperty(value = "专业技术资格取得时间")
    private LocalDate professionalAcquireTime;
    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @ExcelProperty(value = "职称")
    private String professionalTitle;
    /**
     * 职业资格
     */
    @ApiModelProperty(value = "职业资格")
    @ExcelProperty(value = "职业资格")
    private String qualificationCertificate;
    /**
     * 职称、执（职）业资格取得时间
     */
    @DateTimeFormat(pattern = EXPORTDATE)
    @ApiModelProperty(value = "执（职）业资格取得时间")
    @ExcelProperty(value = "职业资格取得时间")
    private LocalDate acquisitionTime;
    /**
     * 个人简历（从高中简历起）
     */
    @ApiModelProperty(value = "个人简历（从高中简历起）")
    @ExcelProperty(value = "个人简历（从高中简历起）")
    private String resumeInfo;
    /**
     * 曾受到何种奖励或处分
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty(value = "曾受到何种奖励或处分")
    private String rewardAndPunishment;
    /**
     * 关系
     */
    @ApiModelProperty(value = "关系")
    @ExcelProperty({"家庭成员及主要社会关系","关系"})
    private String relation;
    /**
     * 成员姓名
     */
    @ApiModelProperty(value = "成员姓名")
    @ExcelProperty({"家庭成员及主要社会关系","姓名"})
    private String memberName;
    /**
     * 成员年龄
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty({"家庭成员及主要社会关系","年龄"})
    private String memberAge;
    /**
     * 成员政治面貌
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty({"家庭成员及主要社会关系","政治面貌"})
    private String memberPoliticalStatus;
    /**
     * 成员工作单位及职务
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    @ExcelProperty({"家庭成员及主要社会关系","工作单位及职务"})
    private String workUnitAndPosition;

}
