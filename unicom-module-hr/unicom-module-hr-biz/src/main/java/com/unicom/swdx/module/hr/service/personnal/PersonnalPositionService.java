package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPositionGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPositionVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;

import javax.validation.Valid;

/**
 * 人事学习经历Service 接口
 *
 */
public interface PersonnalPositionService extends IService<PersonnalPositionDO> {


    /**
     * 新增人事职务信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createPersonnal(@Valid PersonnalPositionVO createReqVO, Long personnalId);
    void createPersonnalRegis(@Valid PersonnalCreateRegistrationVO createReqVO, Long personnalId);
    PersonnalPositionDO getPersonnal(Long id);
    void  updatePersonnal(PersonnalPositionGetVO updateReqVO);
}
