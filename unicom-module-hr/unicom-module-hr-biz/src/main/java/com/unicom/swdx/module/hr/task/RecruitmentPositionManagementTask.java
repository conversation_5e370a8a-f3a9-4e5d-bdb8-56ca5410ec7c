package com.unicom.swdx.module.hr.task;

import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitmentPositionManagementService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务
 * <AUTHOR>
 * @data 2024/3/5 11:17
 */

@Component
@Log4j
public class RecruitmentPositionManagementTask {

    /**
     * 服务对象
     */
    @Resource
    private RecruitmentPositionManagementService recruitmentPositionManagementService;

    // 每天凌晨3点执行任务
   // @Scheduled(cron = "0 0 3 * * ?")
    @XxlJob("syncCalendarHrPositionStatus")
    public ResponseEntity<String>refreshDatabase() {
        // 定时任务开始
        log.info("数据库刷新任务开始......");
        return recruitmentPositionManagementService.timedTask();

    }
}
