package com.unicom.swdx.module.hr.util;

import javax.crypto.*;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

public class PasswordProvider {
    private static Key key;
    private static String KEY_STR = "Hnswdx2020";
    private static String CHARSETNAME = "UTF-8";
    private static String ALGORITHM = "DES";

    public static void main(String[] args) throws NoSuchPaddingException, IllegalBlockSizeException, UnsupportedEncodingException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {



        String[] dataArray = {
                "cmponFQtnGUTLMQjeWAMIFS1k34CJeXH"
        };




        for (String s : dataArray) {

            System.out.println(decode(s));
        }


//        System.out.println("加密：" + encrypt("430922198508157230"));

    }

    static {
        try {
            // 生成DES算法对象
            KeyGenerator generator = KeyGenerator.getInstance(ALGORITHM);
            // 运用SHA1安全策略
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            // 设置上密钥种子
            secureRandom.setSeed(KEY_STR.getBytes());
            // 初始化基于SHA1的算法对象
            generator.init(secureRandom);
            // 生成密钥对象
            key = generator.generateKey();
            generator = null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /***
     * 获取加密的信息
     *
     * @param str
     * @return
     */
    public static String encrypt(String str) {
        // 基于BASE64编码，接收byte[]并转换成String
        try {
            // 按utf8编码
            byte[] bytes = str.getBytes(CHARSETNAME);
            // 获取加密对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            // 初始化密码信息
            cipher.init(Cipher.ENCRYPT_MODE, key);
            // 加密
            byte[] doFinal = cipher.doFinal(bytes);
            // byte[]to encode好的String 并返回
            return Base64.getEncoder().encodeToString(doFinal);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /***
     * 获取解密之后的信息
     *
     * @param str
     * @return
     */
    public static String decode(String str) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
            // 将字符串decode成byte[]
            byte[] bytes = Base64.getDecoder().decode((str));
            // 获取解密对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            // 初始化解密信息
            cipher.init(Cipher.DECRYPT_MODE, key);
            // 解密
            byte[] doFial = cipher.doFinal(bytes);
            return new String(doFial, CHARSETNAME);
    }
}
