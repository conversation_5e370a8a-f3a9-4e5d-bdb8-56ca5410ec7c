package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ApiModel("管理后台 - 用户导入 Response VO")
@Data
@Builder
public class PersonnalImportRespVO {
//
    @ApiModelProperty(value = "创建成功的岗位数组", required = true)
    private List<String> createPersonnalNames;

    @ApiModelProperty(value = "更新成功的岗位数组", required = true)
    private List<String> updatePersonnalNames;

    @ApiModelProperty(value = "导入失败的岗位集合", required = true, notes = "key 为用户名，value 为失败原因")
    private Map<String, String> failurePersonnalNames;


}
