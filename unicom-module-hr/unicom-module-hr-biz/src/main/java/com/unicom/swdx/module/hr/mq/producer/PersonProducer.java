package com.unicom.swdx.module.hr.mq.producer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.unicom.swdx.framework.dict.core.util.DictFrameworkUtils;
import com.unicom.swdx.module.hr.dal.dataobject.kafka.KafkaMessageDO;
import com.unicom.swdx.module.hr.service.kafka.KafkaMessageService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.OldDeptDTO;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import com.unicom.swdx.module.system.api.user.dto.OldPersonDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaHeaderDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaPersonDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.unicom.swdx.module.system.enums.kafka.person.PersonEventType.*;

@Slf4j
@Component
public class PersonProducer {

    @Value("${sendTopic.oldKafka}")
    private String oldKafkaTopic;

    @Value("${sendTopic.newKafka}")
    private String newKafkaTopic;

    @Resource
    private KafkaTemplate<Object, Object> kafkaTemplate;

    @Resource
    private KafkaMessageService kafkaMessageService;

    @Resource
    private DeptApi deptApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private SmsSendApi smsSendApi;

    /**
     * {"header":{
     * "eventId":"10864e52-79f9-4c14-8532-6a81d88417ca",
     * "sender":"003",
     * "eventType":"00300005",
     * "timestamp":"1711012122483"
     * },
     * "body":{
     * "education":"大学本科毕业",
     * "mobile_phone":"15243670643",
     * "employee_id":"003c73fd7b1f26511eab285d39f7c63a093",
     * "sex":"男",
     * "edu_degree":"教育学学士学位",
     * "name":"罗松",
     * "dept_name":"离退休人员工作办公室",
     * "duty":"",
     * "staff_type_":"工勤技能岗位人员",
     * "dept_id":"001001017000",
     * "status":"在职"}
     * }
     */

    @Async
    public void sendPersonData(boolean isAdd, KafkaPersonDTO personInfo , Long deptid) {
        log.info("[send][ 新增或修改人事数据成功，向kafka发送老系统教职工更新数据 ]  == {}" ,deptid   );
        log.info("[send][ 新增或修改人事数据成功，向kafka发送老系统教职工更新数据 personInfo  == {}" ,personInfo.toString()   );

        OldKafkaMessageDTO message = getPerson(isAdd, personInfo );
        String data = JSON.toJSONString(message);
            log.info("打印发送人事数据body内容-----------"+data);

        KafkaMessageDO kafkaMessageDO = new KafkaMessageDO()
                .setMessage(data)
                .setCreateTime(LocalDateTime.now())
                .setDeleted(false);
        kafkaMessageService.save(kafkaMessageDO);

        if(Objects.equals(userApi.getUser(personInfo.getUserId()).getCheckedData().getTenantId(),25L)){
            send(oldKafkaTopic, data, kafkaMessageDO.getId());
        }
        send(newKafkaTopic, data, kafkaMessageDO.getId());
    }

    private void send(String topic, String data, Long id){
        kafkaTemplate.send(topic, data).addCallback(
                new ListenableFutureCallback<SendResult<Object, Object>>() {
                    @Override
                    public void onSuccess(SendResult<Object, Object> result) {
                        // 处理成功发送的逻辑
//                        result.getProducerRecord();
                        //发送成功则删除
                        kafkaMessageService.removeById(id);
                    }
                    @Override
                    public void onFailure(Throwable ex) {
                        // 处理发送失败的逻辑
                        String message = "【湖南省委党校】kafka发送消息失败，消息内容为:"+data+"，请尽快进行处理";

                        Map<String,Object> map = new HashMap<>();
                        map.put("arg1", message);

                        smsSendApi.sendSingleSms("18608404235", null,null ,"admin-sms-login-new",map);
                    }
                }
        );
    }

    @Async
    public void sendPersonListData(boolean isAdd, List<KafkaPersonDTO> kafkaList) {
        Long tenantId = kafkaList.get(0).getTenantId();
        log.info("[send][ 导入人事数据成功，开始向kafka发送老系统教职工更新数据 ]");
        kafkaList.forEach(a->{
            OldKafkaMessageDTO message = getPerson(isAdd, a);
            if(Objects.equals(tenantId,25L)){
                kafkaTemplate.send(oldKafkaTopic, JSON.toJSONString(message));
            }
            kafkaTemplate.send(newKafkaTopic, JSON.toJSONString(message));
        });
    }

    private OldKafkaMessageDTO getPerson(boolean isAdd, KafkaPersonDTO personInfo){
        OldKafkaHeaderDTO header = new OldKafkaHeaderDTO();

        OldKafkaPersonDTO body = new OldKafkaPersonDTO();

        header.setTenant_code(personInfo.getTenantCode());

        header.setSender(personSender);

        header.setEventId(UUID.randomUUID().toString());

        if(isAdd){


            OldPersonDTO oldPerson = userApi.getOldPersonByMobile(personInfo.getMobile()).getCheckedData();

            if(oldPerson!=null){

                header.setEventType(editPersonEventType);

                body.setUserId(personInfo.getUserId());

                if(Objects.nonNull(oldPerson) ){

                    if(oldPerson.getUniconId()!=null){
                        body.setEmployee_id(oldPerson.getUniconId());
                    }else{
                        body.setEmployee_id(personSender+oldPerson.getEmployeeId());
                    }


                    AdminUserRespDTO user = userApi.getUser(personInfo.getUserId()).getCheckedData();

                    oldPerson.setMobilePhone(personInfo.getMobile());
                    oldPerson.setName(personInfo.getName());
                    oldPerson.setUserId(user.getId());
                    //修改到OldPerson里
                    userApi.updateOldPerson(oldPerson);


                }else {
                    body.setEmployee_id(personSender+personInfo.getUserId().toString());
                }

            }else{
                header.setEventType(addPersonEventType);

                body.setEmployee_id(personSender+personInfo.getUserId().toString());

                body.setUserId(personInfo.getUserId());
            }

        }else {
            header.setEventType(editPersonEventType);

            body.setUserId(personInfo.getUserId());

            OldPersonDTO oldPerson = userApi.getOldPersonByUserId(personInfo.getUserId()).getCheckedData();
            if(Objects.nonNull(oldPerson) && Objects.nonNull(oldPerson.getUserId())){


                if(oldPerson.getUniconId()!=null){
                    body.setEmployee_id(oldPerson.getUniconId());
                }else{
                    body.setEmployee_id(personSender+oldPerson.getEmployeeId());
                }


                AdminUserRespDTO user = userApi.getUser(personInfo.getUserId()).getCheckedData();
                if(!Objects.equals(user.getNickname(),personInfo.getName()) || !Objects.equals(user.getMobile(),personInfo.getMobile())){
                    oldPerson.setMobilePhone(personInfo.getMobile());
                    oldPerson.setName(personInfo.getName());
                    //修改到OldPerson里
                    userApi.updateOldPerson(oldPerson);
                }

            }else {
                body.setEmployee_id(personSender+personInfo.getUserId().toString());
            }
        }

        header.setTimestamp(System.currentTimeMillis());

        //人员状态
        if(Objects.nonNull(personInfo.getPersonnalStatus())) {
            body.setStatus(DictFrameworkUtils.parseDictDataValue("peron_status",
                    personInfo.getPersonnalStatus().toString()));
        }else{
            body.setStatus("在职");
        }
        //学历
        if(Objects.nonNull(personInfo.getEducation())) {
            body.setEducation(DictFrameworkUtils.parseDictDataValue("person_education",
                    personInfo.getEducation().toString()));
        }else{
            body.setEducation("本科毕业");
        }
        //学位
        if(Objects.nonNull(personInfo.getAcademicDegree())) {
            body.setEdu_degree(DictFrameworkUtils.parseDictDataValue("person_academic_degree",
                    personInfo.getAcademicDegree().toString()));
        }else{
            body.setEdu_degree("其他或无");
        }
        //人员分类
        if(Objects.nonNull(personInfo.getPeronClassification())) {
            body.setStaff_type_(DictFrameworkUtils.parseDictDataValue("peron_classification",
                    personInfo.getPeronClassification().toString()));
        }else{
            body.setStaff_type_("其他");
        }

        //行政职务名称
        body.setDuty(personInfo.getAdministrativePositionName());

        body.setName(personInfo.getName());

        body.setMobile_phone(personInfo.getMobile());

        if(Objects.equals(personInfo.getGender(),1)) {
            body.setSex("男");
        } else if (Objects.equals(personInfo.getGender(), 2)) {
            body.setSex("女");
        }

        OldDeptDTO oldDept = deptApi.getOldDeptByDeptId(personInfo.getDepartment()).getCheckedData();

        log.info("[send][ 新增或修改人事数据成功，向kafka发送老系统教职工更新数据 部门 == {}" ,oldDept   );


        if(Objects.nonNull(oldDept)&& StrUtil.isNotEmpty(oldDept.getOldDeptId())){
            body.setDept_id(oldDept.getOldDeptId());
            body.setDept_name(oldDept.getOldDeptName());
        }else {
            body.setDept_id(personInfo.getDepartment().toString());
            body.setDept_name(deptApi.getDept(personInfo.getDepartment()).getCheckedData().getName());
        }
        body.setNew_dept_id(personInfo.getDepartment());

        OldKafkaMessageDTO message = new OldKafkaMessageDTO();
        message.setHeader(header);
        message.setBody(body);

        return message;
    }
}
