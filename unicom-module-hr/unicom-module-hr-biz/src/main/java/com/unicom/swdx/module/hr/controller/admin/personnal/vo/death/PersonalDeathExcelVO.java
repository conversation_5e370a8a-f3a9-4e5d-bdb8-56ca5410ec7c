package com.unicom.swdx.module.hr.controller.admin.personnal.vo.death;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@ApiModel("人事信息分页-去世管理 Response VO")
@Data
@ToString(callSuper = true)
public class PersonalDeathExcelVO {

    @ExcelIgnore
    private Long userId;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "工作证号")
    private String workId;

    @ExcelProperty(value = "原部门")
    private String department;

    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSON_GENDER)
    private Integer gender;


    @ExcelProperty(value = "人员分类", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_PERSON_CLASSIFICATION)
    private Integer peronClassification;

    @ExcelProperty(value = "人员状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_STATUS)
    private Integer personnalStatus;

    @ExcelProperty(value = "去世时间")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String deathTime;

    @ExcelProperty(value = "去世前状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PERSONNEL_STATUS)
    private String deathStatus;

}
