package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitResearchAchievement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitResearchAchievementVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitResearchAchievementMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitResearchAchievementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitResearchAchievementServiceImpl extends ServiceImpl<RecruitResearchAchievementMapper, RecruitResearchAchievement> implements RecruitResearchAchievementService {

    @Resource
    RecruitResearchAchievementMapper recruitResearchAchievementMapper ;


    @Override
    public PageResult<RecruitResearchAchievement> queryByList(RecruitResearchAchievementVO recruitResearchAchievementVO) {

        IPage<RecruitResearchAchievement> page = MyBatisUtils.buildPage(recruitResearchAchievementVO);
        List<RecruitResearchAchievement> data = recruitResearchAchievementMapper.queryByList(page, recruitResearchAchievementVO);

        return new PageResult<>(data, page.getTotal());
    }
}
