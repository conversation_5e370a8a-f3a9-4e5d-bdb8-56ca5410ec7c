package com.unicom.swdx.module.hr.dal.mysql.personnal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPartyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PersonnalPositionMapper extends BaseMapperX<PersonnalPositionDO> {
//    List<PersonnalPageRespVO> selectPage(IPage page, @Param("param") PersonnalPageReqVO reqVO);
    default PersonnalPositionDO selectByPersonnalId(Long personnalId) {
        return selectOne(new LambdaQueryWrapper<PersonnalPositionDO>()
                .eq(PersonnalPositionDO::getPersonnalId, personnalId));
    }
    default void updateByPersonnalId(PersonnalPositionDO entity) {
        update(entity, new LambdaQueryWrapper<PersonnalPositionDO>().eq(PersonnalPositionDO::getPersonnalId, entity.getPersonnalId()));
    }
}
