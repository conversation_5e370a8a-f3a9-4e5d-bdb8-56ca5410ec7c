package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitmentPositionManagement;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitmentPositionManagementVO;
import org.springframework.http.ResponseEntity;

import java.util.Map;

/**
 * 招聘批次管理(HrRecruitmentPositionManagement)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-02 15:07:37
 */
public interface RecruitmentPositionManagementService extends IService<RecruitmentPositionManagement> {

    PageResult<RecruitmentPositionManagement> queryByList(RecruitmentPositionManagementVO recruitmentPositionManagementVO);

    ResponseEntity<Map<String, Object>> add(RecruitmentPositionManagement recruitmentPositionManagement);

    ResponseEntity<Map<String, Object>> edit(RecruitmentPositionManagement recruitmentPositionManagement);

    ResponseEntity<String> del(RecruitmentPositionManagement recruitmentPositionManagement);

    ResponseEntity<Map<String, Object>> publish(Integer id, String startTime, String cutTime);

    ResponseEntity<Map<String, Object>> recall(Integer id);

    ResponseEntity<String> timedTask();

}
