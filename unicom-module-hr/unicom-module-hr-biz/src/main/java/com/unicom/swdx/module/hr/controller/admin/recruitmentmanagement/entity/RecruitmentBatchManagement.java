package com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 实体类
 * <AUTHOR>
 * @data 2024/3/2 15:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hr_recruitment_batch_management")
@KeySequence("hr_recruitment_batch_management_seq")
@ApiModel(value = "HrRecruitmentBatchManagement", description = "招聘批次管理")
public class RecruitmentBatchManagement extends BaseDO implements Serializable {

    private static final long serialVersionUID = 952581252014550478L;
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 批次名称
     */
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    /**
     * 批次类别，1-专业技术人员,2-教辅人员,3-博士后，4-管理人员,5-人才引进
     */
    @ApiModelProperty(value = "批次类别")
    private String batchClass;
    /**
     * 是否移动端，默认否，0-否，1-是
     */
    @ApiModelProperty(value = "是否移动端")
    private Integer isMobile;
    /**
     * 限投次数
     */
    @ApiModelProperty(value = "限投次数")
    private Integer limitFrequency;
    /**
     * 默认0-未开始，1-进行中，2-完成
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "batch_status")
    private String status;
//    /**
//     * 创建时间
//     */
//    @ApiModelProperty(value = "创建时间")
//    private LocalDateTime createTime;
}
