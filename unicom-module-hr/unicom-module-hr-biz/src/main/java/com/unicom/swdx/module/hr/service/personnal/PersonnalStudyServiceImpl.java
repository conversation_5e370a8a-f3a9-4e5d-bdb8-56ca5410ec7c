package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyVO;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalStudyConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalStudyMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 人事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonnalStudyServiceImpl extends ServiceImpl<PersonnalStudyMapper, PersonnalStudyDO> implements PersonnalStudyService {
    @Resource
    private PersonnalStudyMapper personnalStudyMapper;

    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnal(PersonnalStudyVO createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PersonnalStudyDO study = PersonnalStudyConvert.INSTANCE.convert(createReqVO);
        study.setPersonnalId(personnalId);
        personnalStudyMapper.insert(study);

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPersonnalRegis(PersonnalCreateRegistrationVO createReqVO, Long personnalId) {
        // 校验正确性
//        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PersonnalStudyDO study = new PersonnalStudyDO();
        study.setPersonnalId(personnalId);
        personnalStudyMapper.insert(study);

    }
    @Override
    public PersonnalStudyDO getPersonnal(Long id) {
        return personnalStudyMapper.selectByPersonnalId(id);
    }
    @Override
    public void updatePersonnal(PersonnalStudyGetVO updateReqVO) {
        // 校验正确性
//        this.checkCreateOrUpdate(reqVO.getId(), reqVO.getName(), reqVO.getCode(), getTenantId());
        // 更新岗位
        PersonnalStudyDO updateObj = PersonnalStudyConvert.INSTANCE.convert1(updateReqVO);
        LambdaUpdateWrapper<PersonnalStudyDO> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.eq(PersonnalStudyDO::getPersonnalId,updateObj.getPersonnalId());
        lambdaUpdateWrapper.set(PersonnalStudyDO::getEducation,updateObj.getEducation())
                .set(PersonnalStudyDO::getAdmissionDate,updateObj.getAdmissionDate())
                .set(PersonnalStudyDO::getEndStudyDate,updateObj.getEndStudyDate());
        lambdaUpdateWrapper.set(PersonnalStudyDO::getGraduationSchool,updateObj.getGraduationSchool());
        lambdaUpdateWrapper.set(PersonnalStudyDO::getAcademicDegree,updateObj.getAcademicDegree());
        personnalStudyMapper.updateByPersonnalId(updateObj);
        personnalStudyMapper.update(new PersonnalStudyDO(),lambdaUpdateWrapper);

    }
}
