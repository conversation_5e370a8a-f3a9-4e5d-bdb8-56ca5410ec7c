package com.unicom.swdx.module.hr.framework.rpc.config;

import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.message.MessageApi;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.permission.RoleApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {PermissionApi.class, AdminUserApi.class, RoleApi.class, PostApi.class, DeptApi.class, SmsSendApi.class, TenantApi.class,  MessageApi.class,BpmProcessInstanceApi.class})
public class RpcConfiguration {
}
