package com.unicom.swdx.module.hr.convert.personnal;

import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPartyDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PersonnalPartyConvert {

    PersonnalPartyConvert INSTANCE = Mappers.getMapper(PersonnalPartyConvert.class);

    PersonnalPartyDO convert(PersonnalPartyVO bean);
    PersonnalPartyGetVO convert0(PersonnalPartyDO bean);
    PersonnalPartyDO convert1(PersonnalPartyGetVO bean);
    PersonnalPartyDO convert2(PersonnalImportExcelVO bean);
    List<PersonnalPartyDO> convertList(List<PersonnalImportExcelVO> bean);
}
