package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("人事调动调出信息 Request VO")
@Data
@ToString(callSuper = true)
public class PersonnalTransferOutVO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "姓名", example = "王二")
    private String name;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "调出部门",example = "1")
    private String deptId;


    /**
     * 调出部门名称
     */
    @ApiModelProperty(value = "调出部门名称")
    private String deptName;

    /**
     * 职级
     */
    @ApiModelProperty(value = "调出职级")
    private Integer rank;

    /**
     * 行政职务名称
     */
    @ApiModelProperty(value = "行政职务名称")
    private String administrativePositionName;

    /**
     * 行政职务级别
     */
    @ApiModelProperty(value = "调出行政职务级别")
    private Integer administrativePositionRank;

    /**
     * 调出科室
     */
    @ApiModelProperty(value = "调出科室")
    private String subjectRoom;

}
