package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitTeachingAward;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitTeachingAwardVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitTeachingAwardMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitTeachingAwardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitTeachingAwardServiceImpl extends ServiceImpl<RecruitTeachingAwardMapper, RecruitTeachingAward> implements RecruitTeachingAwardService {

    @Resource
    RecruitTeachingAwardMapper recruitTeachingAwardMapper ;


    @Override
    public PageResult<RecruitTeachingAward> queryByList(RecruitTeachingAwardVO recruitTeachingAwardVO) {

        IPage<RecruitTeachingAward> page = MyBatisUtils.buildPage(recruitTeachingAwardVO);
        List<RecruitTeachingAward> data = recruitTeachingAwardMapper.queryByList(page, recruitTeachingAwardVO);

        return new PageResult<>(data, page.getTotal());
    }
}
