package com.unicom.swdx.module.hr.controller.admin.personnal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.Aes.AesUtils;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.hr.api.dto.PersonDTO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
import com.unicom.swdx.module.hr.convert.personnal.*;
import com.unicom.swdx.module.hr.dal.dataobject.kafka.KafkaMessageDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.PersonnalMapper;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.hr.service.kafka.KafkaMessageService;
import com.unicom.swdx.module.hr.service.personnal.*;
import com.unicom.swdx.module.hr.task.GetIdTypeSheetWriteHandler;
import com.unicom.swdx.module.hr.task.GetPeronClassificationSheetWriteHandler;
import com.unicom.swdx.module.hr.task.GetPersonnalStatusSheetWriteHandler;
import com.unicom.swdx.module.hr.util.DesUtil;
import com.unicom.swdx.module.hr.util.PasswordProvider;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.DangJianDTO;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.Key;
import java.security.spec.KeySpec;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.hr.enums.ErrorCodeConstants.PERSONNAL_IMPORT_OUT_MEMORY;

@Api(tags = "人事信息管理")
@RestController
@RequestMapping("/hr/personnal")
public class PersonnalInformationController {
    @Resource
    private PersonnalService personnalService;
    @Resource
    private PersonnalStudyService personnalStudyService;
    @Resource
    private PersonnalPositionService personnalPositionService;
    @Resource
    private PersonnalWorkService personnalWorkService;
    @Resource
    private PersonnalPartyService personnalPartyService;
    @Resource
    private PersonnalMapper personnalMapper;
    @Resource
    private PersonProducer personProducer;
    @Resource
    private TenantApi tenantApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeptApi deptApi;



    @PostMapping("/create")
    @ApiOperation("新增人事信息")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:create')")
    @Transactional(rollbackFor = Exception.class)

    public CommonResult<Long> createPersonnal(@Valid @RequestBody PersonnalCreateReqVO createReqVO) {
        PersonnalBasicDO basic = personnalService.createPersonnal(createReqVO.getBasicVO(),
                createReqVO.getPositionVO().getRank(),
                createReqVO.getPositionVO().getAdministrativePositionName(),
                createReqVO.getPositionVO().getAdministrativePositionRank());
        personnalStudyService.createPersonnal(createReqVO.getStudyVO(), basic.getId());
//        personnalPartyService.createPersonnal(createReqVO.getPartyVO(), personnalId);
        personnalWorkService.createPersonnal(createReqVO.getWorkVOList(), basic.getId());
        personnalPositionService.createPersonnal(createReqVO.getPositionVO(), basic.getId());


        KafkaPersonDTO kafkaPersonDTO = PersonnalConvert.INSTANCE.convertToKafkaPerson(createReqVO);
        kafkaPersonDTO.setDepartment(basic.getDepartment());
        kafkaPersonDTO.setUserId(basic.getUserId());
        Long tenantId = SecurityFrameworkUtils.getTenantId();
        kafkaPersonDTO.setTenantId(tenantId);
        kafkaPersonDTO.setTenantCode(tenantApi.getTenantCodeById(tenantId).getCheckedData());
        personProducer.sendPersonData(true,kafkaPersonDTO  ,basic.getId());
        return success(basic.getId());
    }
    @GetMapping("/page")
    @ApiOperation("获得人事信息分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:page')")
    public CommonResult<PageResult<PersonnalPageRespVO>> getPersonnalPage(@Valid PersonnalPageReqVO pageVO) {
        PageResult<PersonnalPageRespVO> pageResult = personnalService.getPersonnalPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/pageChildren")
    @ApiOperation("获得人事信息(包含子部门)")
    @PreAuthorize("@ss.hasPermission('hr:personnal:page')")
    public CommonResult<List<PersonnalPageRespVO>> getPersonnalPageChildren(@Valid PersonnalChildrenReqVO pageVO) {
        List<PersonnalPageRespVO> pageResult = personnalService.getPersonnalPageChildren(pageVO);
        return success(pageResult);
    }
    @GetMapping(value = "/get")
    @ApiOperation("获得人事信息")
    @ApiImplicitParam(name = "id", value = "人事编号", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('hr:personnal:get')")
    public CommonResult<PersonnalGetRespVO> getPersonnal(@RequestParam("id") Long id) {

//        人事基本信息
        PersonnalBasicDO personnalBasic = personnalService.getPersonnal(id);
        PersonnalGetRespVO personnalGetRespVO = new PersonnalGetRespVO();
        personnalGetRespVO.setBasicVO(PersonnalConvert.INSTANCE.convert0(personnalBasic));
//        人事学习经历
        PersonnalStudyDO personnalStudy = personnalStudyService.getPersonnal(id);
        personnalGetRespVO.setStudyVO(PersonnalStudyConvert.INSTANCE.convert0(personnalStudy));
//        人事党员信息
//        PersonnalPartyDO personnalParty = personnalPartyService.getPersonnal(id);
        PersonnalPartyGetVO personnalParty = new PersonnalPartyGetVO();
        DangJianDTO d = adminUserApi.getDangJian(personnalBasic.getMobile()).getData();
        if(d!=null){
            //党支部
            personnalParty.setAffiliatedParty(d.getPartyOrgName());
            //党员转正时间
            personnalParty.setAdmissionPartyTime(d.getOfficialDate());
            //入党时间
            personnalParty.setEntryPartyTime(d.getJoinPartyDate());
            //入党介绍人
            personnalParty.setPartyRecommendPerson(d.getCombinedIntroducers());
            //上级党组织
            personnalParty.setHigherPartyOrganizations(d.getParentPartyOrgName());
        }
        personnalGetRespVO.setPartyVO(personnalParty);
//        人事职位信息
        PersonnalPositionDO personnalPosition = personnalPositionService.getPersonnal(id);
        personnalGetRespVO.setPositionVO(PersonnalPositionConvert.INSTANCE.convert0(personnalPosition));
//        人事工作经历
        List<PersonnalWorkDO> personnalWork = personnalWorkService.getPersonnal(id);
        personnalGetRespVO.setWorkVOList(PersonnalWorkConvert.INSTANCE.convert0(personnalWork));

    return success(personnalGetRespVO);

    }

    @GetMapping(value = "/getInformation")
    @ApiOperation("获得人事信息")
    @PreAuthorize("@ss.hasPermission('hr:personnal:get')")
    public CommonResult<PersonnalGetRespVO> getPersonnalById() {

        CommonResult<Long> personnalIdByUserId = getPersonnalIdByUserId(getLoginUserId());
        Long id =personnalIdByUserId.getData();
        if(id == null){
            throw exception(new ErrorCode(500, "人员信息和系统用户未关联！"));
        }
//        人事基本信息
        PersonnalBasicDO personnalBasic = personnalService.getPersonnal(id);
        PersonnalGetRespVO personnalGetRespVO = new PersonnalGetRespVO();
        personnalGetRespVO.setBasicVO(PersonnalConvert.INSTANCE.convert0(personnalBasic));
//        人事学习经历
        PersonnalStudyDO personnalStudy = personnalStudyService.getPersonnal(id);
        personnalGetRespVO.setStudyVO(PersonnalStudyConvert.INSTANCE.convert0(personnalStudy));
//        人事党员信息
        PersonnalPartyGetVO personnalParty = new PersonnalPartyGetVO();
        DangJianDTO d = adminUserApi.getDangJian(personnalBasic.getMobile()).getData();
        if(d!=null){
            //党支部
            personnalParty.setAffiliatedParty(d.getPartyOrgName());
            //党员转正时间
            personnalParty.setAdmissionPartyTime(d.getOfficialDate());
            //入党时间
            personnalParty.setEntryPartyTime(d.getJoinPartyDate());
            //入党介绍人
            personnalParty.setPartyRecommendPerson(d.getCombinedIntroducers());
            //上级党组织
            personnalParty.setHigherPartyOrganizations(d.getParentPartyOrgName());
        }
        personnalGetRespVO.setPartyVO(personnalParty);
//        personnalGetRespVO.setPartyVO(PersonnalPartyConvert.INSTANCE.convert0(personnalParty));

//        人事职位信息
        PersonnalPositionDO personnalPosition = personnalPositionService.getPersonnal(id);
        personnalGetRespVO.setPositionVO(PersonnalPositionConvert.INSTANCE.convert0(personnalPosition));
//        人事工作经历
        List<PersonnalWorkDO> personnalWork = personnalWorkService.getPersonnal(id);
        personnalGetRespVO.setWorkVOList(PersonnalWorkConvert.INSTANCE.convert0(personnalWork));

        return success(personnalGetRespVO);

    }


    @PostMapping("/update")
    @ApiOperation("更新人事信息")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:update')")
    public CommonResult<Boolean> updatePersonnal(@Valid @RequestBody PersonnalUpdateReqVO reqVO) {
        personnalService.updatePersonnal(reqVO.getBasicVO());
//        personnalPartyService.updatePersonnal(reqVO.getPartyVO());

        personnalStudyService.updatePersonnal(reqVO.getStudyVO());
        personnalWorkService.updatePersonnal(reqVO.getWorkVOList());
        personnalPositionService.updatePersonnal(reqVO.getPositionVO());

        KafkaPersonDTO kafkaPersonDTO = PersonnalConvert.INSTANCE.convertToKafkaPerson2(reqVO);

        kafkaPersonDTO.setTenantCode(tenantApi.getTenantCodeByUserId(reqVO.getBasicVO().getUserId()).getCheckedData());
        personProducer.sendPersonData(false,kafkaPersonDTO  , personnalService.getPersonnal(reqVO.getBasicVO().getId()).getDepartment() );
        return success(true);
    }
    @GetMapping("/export")
    @ApiOperation("导出人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
//    @OperateLog(type = EXPORT)
    public void export(HttpServletResponse response, @Validated PersonnalExportReqVO reqVO) throws IOException {
        List<PersonnalExcelVO> personnals = personnalService.getPersonnalExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "人事信息.xls", "人事信息列表", PersonnalExcelVO.class, personnals);
    }

    @GetMapping("/get-import-template")
    @ApiOperation("获得导入人事信息模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        String filename = "人事信息导入模板.xls";
        String sheetName = "人事信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), PersonnalImportExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new GetIdTypeSheetWriteHandler())
                    .registerWriteHandler(new GetPersonnalStatusSheetWriteHandler())
                    .registerWriteHandler(new GetPeronClassificationSheetWriteHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(Collections.emptyList());
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入人事信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('hr:personnal:import')")
    public CommonResult<PersonnalImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                           @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        long fileSizeInBytes = file.getSize();
        long maxSizeInBytes = 16 * 1024 * 1024; // 16MB
        if (fileSizeInBytes > maxSizeInBytes) {
            throw exception(PERSONNAL_IMPORT_OUT_MEMORY);
        }
        List<PersonnalImportExcelVO> list = ExcelUtils.read(file, PersonnalImportExcelVO.class);
        ExcelValidator.valid(list,1);
        return success(personnalService.importPersonnal(list, updateSupport));
    }
    @GetMapping("/getSimple")
    @ApiOperation("(职工)提交审核详情")
    @ApiImplicitParam(name = "id", value = "人事编号", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('hr:personnal:getSimple')")
    public CommonResult<PersonnalSimpleRespVO> getPersonnalSimple(@RequestParam("id") Long id) {

        PersonnalSimpleRespVO personnalSimpleRespVO = personnalService.getPersonnalSimple(id);
        return success(personnalSimpleRespVO);
    }
    @GetMapping("/pagePersonReview")
    @ApiOperation("获得人事信息审核分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:pagePersonReview')")
    public CommonResult<PageResult<PersonnalReviewPageRespVO>> getPersonnalReviewPage(@Valid PersonnalReviewPageReqVO pageVO) {
        PageResult<PersonnalReviewPageRespVO> pageResult = personnalService.getPersonnalReviewPage(pageVO);
        return success(pageResult);
    }
    @GetMapping("/updateReviewStatus")
    @ApiOperation("审核状态更新")
    @PreAuthorize("@ss.hasPermission('hr:personnal:updateReviewStatus')")
    public CommonResult<Boolean> updateReviewStatus(@RequestParam Integer requ, @RequestParam Integer id) {
        PersonnalBasicDO basic = personnalMapper.selectById(id);
        basic.setReviewStatus(requ);
        personnalMapper.updateById(basic);
        if(requ==2){
            permissionApi.assignUserRoleGroup(basic.getUserId(),"党校教职工");
        }
        return success(true);
    }

    @PostMapping("/createRegistration")
    @ApiOperation("办理入职报到登记新增")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:createRegistration')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Long> createRegistrationPersonnal(@Valid @RequestBody PersonnalCreateRegistrationVO createReqVO) {
        Long personnalId = personnalService.createRegistrationPersonnal(createReqVO);
        personnalStudyService.createPersonnalRegis(createReqVO, personnalId);
//        personnalPartyService.createPersonnal(createReqVO.getPartyVO(), personnalId);
        personnalWorkService.createPersonnalRegis(createReqVO, personnalId);
        personnalPositionService.createPersonnalRegis(createReqVO, personnalId);
        return success(personnalId);
    }
    @GetMapping("/getPersonnalId")
    @ApiOperation("获得人事编号根据用户id")
    @ApiImplicitParam(name = "userId", value = "用户编号", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('hr:personnal:getPersonnalId')")
    public CommonResult<Long> getPersonnalIdByUserId(@RequestParam("userId") Long userId) {
        Long personnalId = personnalMapper.getPersonnalIdByUserId(userId);
        return success(personnalId);
    }
    @GetMapping("/pageEntry")
    @ApiOperation("办理报到登记分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:pageEntry')")
    public CommonResult<PageResult<PersonnalRegistrationPageRespVO>> getPageRegistration(@Valid PersonnalRegistrationPageReqVO pageVO) {
        PageResult<PersonnalRegistrationPageRespVO> pageResult = personnalService.getPersonnalRegistrationPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/retirePage")
    @ApiOperation("获得退休人事信息分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:retirePage')")
    public CommonResult<PageResult<PersonalRetirePageRespVO>> getRetirePage(@Valid PersonalRetirePageReqVO pageVO) {
        PageResult<PersonalRetirePageRespVO> pageResult = personnalService.getRetirePage(pageVO);
        return success(pageResult);
    }

    @PostMapping("/updateRetire")
    @ApiOperation("更新退休人事信息【退休登记】")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:update')")
    public CommonResult<Boolean> updatePersonalRetire(@Valid @RequestBody PersonalRetireUpdateVO reqVO) {
        personnalService.updatePersonalRetire(reqVO);
        return success(true);
    }

    @GetMapping("/exportRetire")
    @ApiOperation("导出退休人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
    public void exportRetire(HttpServletResponse response, @Validated PersonalRetireExcelReqVO reqVO) throws IOException {
        List<PersonalRetireExcelVO> personnals = personnalService.getPersonalRetireExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "退休人事信息.xls", "退休人事信息列表", PersonalRetireExcelVO.class, personnals);
    }

//    @PostMapping("/exportSelectRetire")
//    @ApiOperation("导出选中退休人事列表")
//    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
//    public void exportFewRetire(HttpServletResponse response,
//                                @Validated List<PersonalRetireExcelVO> personnals) throws IOException {
//        // 输出
//        ExcelUtils.write(response, "退休人事信息.xls", "退休人事信息列表", PersonalRetireExcelVO.class, personnals);
//    }

    @PostMapping("/exportSelectRetire")
    @ApiOperation("导出选中退休人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
    public void exportFewRetire(HttpServletResponse response,
                                 @RequestBody String json) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(json);
        String ids = rootNode.get("ids").asText();

        List<PersonalRetireExcelVO> personnals = new ArrayList<>();
        //测试
      //  System.out.println("IDs: " + ids);
        // 遍历数组
        for (String id : ids.split(",")) {
            // 测试
          //  System.out.println("Processing ID: " + id);
            PersonalRetireExcelVO personal = personnalMapper.getPersonalRetireDataById(id);
            if (personal != null) {
                Long userId = personal.getUserId();
                List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
                if(!CollUtil.isEmpty(deptIds)) {
                    List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                    String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                    personal.setDepartment(departments);
                }
                personnals.add(personal);
           }
        }
        ExcelUtils.write(response, "退休人事信息.xls", "退休人事信息列表", PersonalRetireExcelVO.class, personnals);
    }


    @GetMapping("/leavePage")
    @ApiOperation("获得离校人事信息分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:leavePage')")
    public CommonResult<PageResult<PersonalLeavePageRespVO>> getLeavePage(@Valid PersonalLeavePageReqVO pageVO) {
        PageResult<PersonalLeavePageRespVO> pageResult = personnalService.getLeavePage(pageVO);
        return success(pageResult);
    }

    @PostMapping("/updateLeave")
    @ApiOperation("更新离校人事信息【离校登记】")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:update')")
    public CommonResult<Boolean> updatePersonalLeave(@Valid @RequestBody PersonalLeaveUpdateVO reqVO) {
        personnalService.updatePersonalLeave(reqVO);
        return success(true);
    }

    @GetMapping("/exportLeave")
    @ApiOperation("导出离校人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
    public void exportLeave(HttpServletResponse response, @Validated PersonalLeaveExcelReqVO reqVO) throws IOException {
        List<PersonalLeaveExcelVO> personnals = personnalService.getPersonalLeaveExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "离校人事信息.xls", "离校人事信息列表", PersonalLeaveExcelVO.class, personnals);
    }

//    @PostMapping("/exportSelectLeave")
//    @ApiOperation("导出选中离校人事列表")
//    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
//    public void exportSelectLeave(HttpServletResponse response,
//                                  @Validated List<PersonalLeaveExcelVO> personnals) throws IOException {
//        // 输出
//        ExcelUtils.write(response, "离校人事信息.xls", "离校人事信息列表", PersonalLeaveExcelVO.class, personnals);
//    }

    @PostMapping("/exportSelectLeave")
    @ApiOperation("导出选中离校人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
    public void exportSelectLeave(HttpServletResponse response,
                                @RequestBody String json) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(json);
        String ids = rootNode.get("ids").asText();

        List<PersonalLeaveExcelVO> personnals = new ArrayList<>();
        //测试
        //  System.out.println("IDs: " + ids);
        // 遍历数组
        for (String id : ids.split(",")) {
            // 测试
            //  System.out.println("Processing ID: " + id);
            PersonalLeaveExcelVO personal = personnalMapper.getPersonalLeaveDataById(id);
            if (personal != null) {
                Long userId = personal.getUserId();
                List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
                if(!CollUtil.isEmpty(deptIds)) {
                    List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                    String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                    personal.setDepartment(departments);
                }
                personnals.add(personal);
            }
        }
        ExcelUtils.write(response, "离校人事信息.xls", "离校人事信息列表", PersonalLeaveExcelVO.class, personnals);
    }

    @GetMapping("/deathPage")
    @ApiOperation("获得去世人事信息分页")
    @PreAuthorize("@ss.hasPermission('hr:personnal:deathPage')")
    public CommonResult<PageResult<PersonalDeathPageRespVO>> getDeathPage(@Valid PersonalDeathPageReqVO pageVO) {
        PageResult<PersonalDeathPageRespVO> pageResult = personnalService.getDeathPage(pageVO);
        return success(pageResult);
    }

    @PostMapping("/updateDeath")
    @ApiOperation("更新去世人事信息【去世登记】")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('hr:personnal:update')")
    public CommonResult<Boolean> updatePersonalDeath(@Valid @RequestBody PersonalDeathUpdateVO reqVO) {
        personnalService.updatePersonalDeath(reqVO);
        return success(true);
    }

    @GetMapping("/exportDeath")
    @ApiOperation("导出去世人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
    public void exportDeath(HttpServletResponse response, @Validated PersonalDeathExcelReqVO reqVO) throws IOException {
        List<PersonalDeathExcelVO> personnals = personnalService.getPersonalDeathExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "去世人事信息.xls", "去世人事信息列表", PersonalDeathExcelVO.class, personnals);
    }

//    @PostMapping("/exportSelectDeath")
//    @ApiOperation("导出去世人事列表")
//    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
//    public void exportSelectDeath(HttpServletResponse response,
//                                  @Validated List<PersonalDeathExcelVO> personnals) throws IOException {
//        // 输出
//        ExcelUtils.write(response, "去世人事信息.xls", "去世人事信息列表", PersonalDeathExcelVO.class, personnals);
//    }

    @PostMapping("/exportSelectDeath")
    @ApiOperation("导出选中去世人事列表")
    @PreAuthorize("@ss.hasPermission('hr:personnal:export')")
    public void exportSelectDeath(HttpServletResponse response,
                                  @RequestBody String json) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(json);
        String ids = rootNode.get("ids").asText();

        List<PersonalDeathExcelVO> personnals = new ArrayList<>();
        //测试
        //  System.out.println("IDs: " + ids);
        // 遍历数组
        for (String id : ids.split(",")) {
            // 测试
            //  System.out.println("Processing ID: " + id);
            PersonalDeathExcelVO personal = personnalMapper.getPersonalDeathDataById(id);
            if (personal != null) {
                Long userId = personal.getUserId();
                List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
                if(!CollUtil.isEmpty(deptIds)) {
                    List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                    String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                    personal.setDepartment(departments);
                }
                personnals.add(personal);
            }
        }
        ExcelUtils.write(response, "去世人事信息.xls", "去世人事信息列表", PersonalDeathExcelVO.class, personnals);
    }

    @GetMapping("/sendAllPersonInfo")
    @ApiOperation("发送全量的教职工数据到教务系统")
    public CommonResult<Boolean> sendAllPersonInfo(@RequestParam("url") String url) {
        personnalService.sendAllPersonInfo(url);
        return success(true);
    }



    @PostMapping("/getAllPersonInfo")
    @ApiOperation("得到全量的教职工数据")
    @PermitAll
    public CommonResult<List<PersonDTO>> getAllPersonInfo(@RequestParam(value = "data" ) String data) {


        String parameters =  AesUtils.decryptFromString(data , Mode.CBC, Padding.ZeroPadding);

        if(StrUtil.isEmpty(parameters)){
            throw exception( 500 , "参数解码错误");
        }

        AllPersonInfoReq reqVO  = JSONUtil.toBean(parameters , AllPersonInfoReq.class);

        if(ObjectUtil.isEmpty(reqVO)){
            throw exception(500 ,  "参数解析错误");
        }


//        if( System.currentTimeMillis() - reqVO.getTimestamp().longValue() >10000){
//            //差10s
//            throw exception(500 ,  "请求超时");
//        }

        List<PersonDTO> list = personnalMapper.selectSimplePersonInfoList(reqVO.getTenantCode());
        return success(list);
    }
    @GetMapping("/des")
    @ApiOperation("人事身份证加密")
    public CommonResult<Boolean> des() throws Exception {
        // 待加密数据
        String encryptedHex = "1";
        List<PersonnalBasicDO> list = personnalMapper.selectList();
        list = list.stream()
                .filter(record -> record.getIdNumber() != null)
                .collect(Collectors.toList());
        for (PersonnalBasicDO personnalBasicDO : list) {
            encryptedHex = PasswordProvider.encrypt(personnalBasicDO.getIdNumber()); // 加密后通常使用 Base64 编码
            LambdaUpdateWrapper<PersonnalBasicDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.set(PersonnalBasicDO::getIdNumberDes, encryptedHex);
            lambdaUpdateWrapper.eq(PersonnalBasicDO::getIdNumber, personnalBasicDO.getIdNumber());
            personnalMapper.update(new PersonnalBasicDO(), lambdaUpdateWrapper);
            System.out.println("加密后（Base64 编码）: " + encryptedHex);
        }
        return success(true);
    }

    @GetMapping("/getDangJian")
    @ApiOperation("发送全量的教职工数据到教务系统")
    public CommonResult<DangJianDTO> getDangJian(@RequestParam("mobile") String mobile){
        CommonResult<DangJianDTO> commonResult= adminUserApi.getDangJian(mobile);
        return success(commonResult.getData());
    }

    @Resource
    private KafkaMessageService kafkaMessageService;


    @GetMapping("/test")
    @ApiOperation("test")
    @PermitAll
    public CommonResult<Float> test() {

        LambdaUpdateWrapper<KafkaMessageDO> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.apply("ANY(string_to_array(tenant_id, ','));", "25");
        //SELECT id, message, create_time, deleted, tenant_id FROM kafka_message WHERE deleted = 0 AND '1' = ANY(string_to_array(tenant_id, ','));
        List<KafkaMessageDO> kafkaMessageDOS = kafkaMessageService.getBaseMapper().selectList(lambdaUpdateWrapper);

        System.out.println("kafkaMessageDOS12345:" + kafkaMessageDOS);
        return success(1f);
    }

}
