package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
@Data
public class PersonnalWorkExcelVO {
    /**
     * 工作单位
     */
    @ApiModelProperty (value = "工作单位")
    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String workUnit;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 担任职务
     */
    @ApiModelProperty(value = "担任职务")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String position;
    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @Length(min = 0, max = 500, message = "长度为 0-500 位")
    private String workContent;
}
