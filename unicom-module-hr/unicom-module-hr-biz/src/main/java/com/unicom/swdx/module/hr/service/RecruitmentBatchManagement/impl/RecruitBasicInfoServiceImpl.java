package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.*;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.*;
import com.unicom.swdx.module.hr.convert.recruitment.RecruitmentBasicInfoConvert;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.*;
import com.unicom.swdx.module.hr.enums.RecruitStatusEnum;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitBasicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.CARD_NO_DUPLICATE;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitBasicInfoServiceImpl extends ServiceImpl<RecruitBasicInfoMapper, RecruitBasicInfo> implements RecruitBasicInfoService {

    @Resource
    RecruitBasicInfoMapper recruitBasicInfoMapper;

    @Resource
    RecruitRelationshipMapper recruitRelationshipMapper;

    @Resource
    RecruitLearningExpMapper recruitLearningExpMapper;

    @Resource
    RecruitWorkExpMapper recruitWorkExpMapper;

    @Resource
    RecruitAccessoryInfoMapper recruitAccessoryInfoMapper;

    @Override
    public PageResult<RecruitBasicInfo> queryByList(RecruitBasicInfoVO recruitBasicInfoVO) {

        IPage<RecruitBasicInfo> page = MyBatisUtils.buildPage(recruitBasicInfoVO);
        List<RecruitBasicInfo> data = recruitBasicInfoMapper.queryByList(page, recruitBasicInfoVO);

        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public PageResult<RecruitBasicInfo> faceByList(RecruitBasicInfoVO recruitBasicInfoVO) {

        IPage<RecruitBasicInfo> page = MyBatisUtils.buildPage(recruitBasicInfoVO);
        List<RecruitBasicInfo> data = recruitBasicInfoMapper.faceByList(page, recruitBasicInfoVO);

        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public ResponseEntity<Map<String,Object>> check(RecruitBasicInfoVO recruitBasicInfoVO) {

        HashMap<String,Object> data = new HashMap<>();

        if(recruitBasicInfoVO.getStatus() != null){
            //根据 id 值 更新状态
            RecruitBasicInfo recruitBasicInfo = new RecruitBasicInfo();
            recruitBasicInfo.setId(recruitBasicInfoVO.getId());
            recruitBasicInfo.setStatus(recruitBasicInfoVO.getStatus());


            //插入资格审查的时间 审核通过与不通过操作时间一致
            if(recruitBasicInfoVO.getStatus() == 2 || recruitBasicInfoVO.getStatus() == 3){
                recruitBasicInfo.setTitleExaminationTime(LocalDateTime.now());
            }
            //插入面试通过时间 与 拟录用时间一致
            if(recruitBasicInfoVO.getStatus() == 4 || recruitBasicInfoVO.getStatus() == 5){
                recruitBasicInfo.setPassInterviewTime(LocalDateTime.now());
            }
            //插入资格审查-审批人
            if(StringUtils.isNotBlank(recruitBasicInfoVO.getFirstApprove())){
                recruitBasicInfo.setFirstApprove(recruitBasicInfoVO.getFirstApprove());
            }
            //插入面试审查-审批人
            if(StringUtils.isNotBlank(recruitBasicInfoVO.getFaceApprove())){
                recruitBasicInfo.setFaceApprove(recruitBasicInfoVO.getFaceApprove());
            }

            recruitBasicInfoMapper.updateById(recruitBasicInfo);
            data.put("code",200);
            data.put("message","操作成功！");
        }else{
            data.put("code",300);
            data.put("message","操作失败！");
        }
        return ResponseEntity.ok(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer apply(RecruitApplyVO recruitApplyVO) {
        RecruitBasicInfo recruitBasicInfo = RecruitmentBasicInfoConvert.INSTANCE.convert(recruitApplyVO);
        recruitBasicInfo.setUserId(getLoginUserId());
        recruitBasicInfo.setApplyTime(LocalDateTime.now());
        recruitBasicInfo.setStatus(RecruitStatusEnum.RUNNING.getStatus());
        String cardNo = recruitBasicInfo.getCardNo();
        LambdaQueryWrapper<RecruitBasicInfo> cardNoCountWrapper = new LambdaQueryWrapper<>();
        cardNoCountWrapper.eq(RecruitBasicInfo::getCardNo, cardNo)
                .in(RecruitBasicInfo::getStatus, RecruitStatusEnum.RUNNING.getStatus(), RecruitStatusEnum.INTERVIEW.getStatus(),
                        RecruitStatusEnum.PROPOSED_RECRUITMENT.getStatus());
        Long count = recruitBasicInfoMapper.selectCount(cardNoCountWrapper);
        if (count != 0L) {
            throw exception(CARD_NO_DUPLICATE);
        }
        //正常申请
        if (Objects.isNull(recruitApplyVO.getId())) {
            this.save(recruitBasicInfo);
        } else { //从草稿发起申请或者退回修改后重新申请
            //删去旧有的学习经历、工作经历、家庭成员及主要社会关系、附件
            LambdaQueryWrapper<RecruitLearningExp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecruitLearningExp::getBaseId, recruitApplyVO.getId());
            recruitLearningExpMapper.delete(queryWrapper);

            LambdaQueryWrapper<RecruitWorkExp> workQueryWrapper = new LambdaQueryWrapper<>();
            workQueryWrapper.eq(RecruitWorkExp::getBaseId, recruitApplyVO.getId());
            recruitWorkExpMapper.delete(workQueryWrapper);

            LambdaQueryWrapper<RecruitRelationship> relationQueryWrapper = new LambdaQueryWrapper<>();
            relationQueryWrapper.eq(RecruitRelationship::getBaseId, recruitApplyVO.getId());
            recruitRelationshipMapper.delete(relationQueryWrapper);

            LambdaQueryWrapper<RecruitAccessoryInfo> accessoryQueryWrapper = new LambdaQueryWrapper<>();
            accessoryQueryWrapper.eq(RecruitAccessoryInfo::getBaseId, recruitApplyVO.getId());
            recruitAccessoryInfoMapper.delete(accessoryQueryWrapper);
            // 从草稿发起申请
            if (Objects.equals(recruitApplyVO.getStatus(), RecruitStatusEnum.DRAFT.getStatus())) {
                this.updateById(recruitBasicInfo);
            } else { //退回修改后重新申请
                //修改退回的状态
                recruitBasicInfo.setStatus(RecruitStatusEnum.REAPPLY.getStatus());
                this.updateById(recruitBasicInfo);
                //发起新的申请
                recruitBasicInfo.setId(null);
                recruitBasicInfo.setStatus(RecruitStatusEnum.RUNNING.getStatus());
                this.save(recruitBasicInfo);
            }
        }
        //学习经历
        List<RecruitLearningExp> learningExps = recruitApplyVO.getRecruitLearningExps();
        learningExps.forEach(learningExp -> learningExp.setBaseId(recruitBasicInfo.getId()));
        //工作经历
        if(recruitApplyVO.getRecruitWorkExps() != null) {
            List<RecruitWorkExp> workExps = recruitApplyVO.getRecruitWorkExps();
            workExps.forEach(workExp -> workExp.setBaseId(recruitBasicInfo.getId()));
            recruitWorkExpMapper.insertBatch(workExps);
        }

        //家庭成员及主要社会关系
        List<RecruitRelationship> relationships = recruitApplyVO.getRecruitRelationships();
        relationships.forEach(relationship -> relationship.setBaseId(recruitBasicInfo.getId()));
        //附件
        List<RecruitAccessoryInfo> accessoryInfos = recruitApplyVO.getRecruitAccessoryInfo();
        accessoryInfos.forEach(accessoryInfo -> accessoryInfo.setBaseId(recruitBasicInfo.getId()));

        recruitLearningExpMapper.insertBatch(learningExps);
        recruitRelationshipMapper.insertBatch(relationships);
        recruitAccessoryInfoMapper.insertBatch(accessoryInfos);

        return recruitBasicInfo.getId();
    }

    @Override
    public Integer saveDraft(RecruitApplyVO recruitApplyVO) {
        RecruitBasicInfo recruitBasicInfo = RecruitmentBasicInfoConvert.INSTANCE.convert(recruitApplyVO);
        recruitBasicInfo.setUserId(getLoginUserId());
        recruitBasicInfo.setApplyTime(LocalDateTime.now());
        recruitBasicInfo.setStatus(RecruitStatusEnum.DRAFT.getStatus());

        //存草稿
        if (Objects.isNull(recruitApplyVO.getId())) {
            this.save(recruitBasicInfo);
        } else { //修改草稿
            //删去旧有的学习经历、工作经历、家庭成员及主要社会关系、附件
            if(recruitApplyVO.getStatus() != null && recruitApplyVO.getStatus().equals(RecruitStatusEnum.BACK.getStatus())){
                recruitBasicInfo.setStatus(RecruitStatusEnum.REAPPLYDRAFT.getStatus());
            }
            LambdaQueryWrapper<RecruitLearningExp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecruitLearningExp::getBaseId, recruitApplyVO.getId());
            recruitLearningExpMapper.delete(queryWrapper);

            LambdaQueryWrapper<RecruitWorkExp> workQueryWrapper = new LambdaQueryWrapper<>();
            workQueryWrapper.eq(RecruitWorkExp::getBaseId, recruitApplyVO.getId());
            recruitWorkExpMapper.delete(workQueryWrapper);

            LambdaQueryWrapper<RecruitRelationship> relationQueryWrapper = new LambdaQueryWrapper<>();
            relationQueryWrapper.eq(RecruitRelationship::getBaseId, recruitApplyVO.getId());
            recruitRelationshipMapper.delete(relationQueryWrapper);

            LambdaQueryWrapper<RecruitAccessoryInfo> accessoryQueryWrapper = new LambdaQueryWrapper<>();
            accessoryQueryWrapper.eq(RecruitAccessoryInfo::getBaseId, recruitApplyVO.getId());
            recruitAccessoryInfoMapper.delete(accessoryQueryWrapper);
            this.updateById(recruitBasicInfo);
        }
        //学习经历
        if (recruitApplyVO.getRecruitLearningExps() != null) {
            List<RecruitLearningExp> learningExps = recruitApplyVO.getRecruitLearningExps();
            learningExps.forEach(learningExp -> learningExp.setBaseId(recruitBasicInfo.getId()));
            recruitLearningExpMapper.insertBatch(learningExps);
        }
        //工作经历
        if (recruitApplyVO.getRecruitWorkExps() != null) {
            List<RecruitWorkExp> workExps = recruitApplyVO.getRecruitWorkExps();
            workExps.forEach(workExp -> workExp.setBaseId(recruitBasicInfo.getId()));
            recruitWorkExpMapper.insertBatch(workExps);
        }
        //家庭成员及主要社会关系
        if (recruitApplyVO.getRecruitRelationships() != null) {
            List<RecruitRelationship> relationships = recruitApplyVO.getRecruitRelationships();
            relationships.forEach(relationship -> relationship.setBaseId(recruitBasicInfo.getId()));
            recruitRelationshipMapper.insertBatch(relationships);
        }
        //附件
        if (recruitApplyVO.getRecruitAccessoryInfo() != null) {
            List<RecruitAccessoryInfo> accessoryInfos = recruitApplyVO.getRecruitAccessoryInfo();
            accessoryInfos.forEach(accessoryInfo -> accessoryInfo.setBaseId(recruitBasicInfo.getId()));
            recruitAccessoryInfoMapper.insertBatch(accessoryInfos);
        }
        return recruitBasicInfo.getId();
    }

    @Override
    public void backToModify(RecruitBasicInfo recruitBasicInfo) {
        recruitBasicInfo.setStatus(RecruitStatusEnum.BACK.getStatus());
        this.updateById(recruitBasicInfo);
    }

    @Override
    public RecruitDetailRespVO detail(Integer id) {
        RecruitBasicInfo recruit = this.getById(id);
        RecruitApplyVO recruitApply = RecruitmentBasicInfoConvert.INSTANCE.convert(recruit);
        //根据id查询对应的工作经历、学习经历、家庭关系、附件
        LambdaQueryWrapper<RecruitLearningExp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecruitLearningExp::getBaseId, recruitApply.getId());
        List<RecruitLearningExp> learningExps = recruitLearningExpMapper.selectList(queryWrapper);

        LambdaQueryWrapper<RecruitWorkExp> workQueryWrapper = new LambdaQueryWrapper<>();
        workQueryWrapper.eq(RecruitWorkExp::getBaseId, recruitApply.getId());
        List<RecruitWorkExp> workExps = recruitWorkExpMapper.selectList(workQueryWrapper);

        LambdaQueryWrapper<RecruitRelationship> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.eq(RecruitRelationship::getBaseId, recruitApply.getId());
        List<RecruitRelationship> relationships = recruitRelationshipMapper.selectList(relationQueryWrapper);

        LambdaQueryWrapper<RecruitAccessoryInfo> accessoryQueryWrapper = new LambdaQueryWrapper<>();
        accessoryQueryWrapper.eq(RecruitAccessoryInfo::getBaseId, recruitApply.getId());
        List<RecruitAccessoryInfo> accessoryInfos = recruitAccessoryInfoMapper.selectList(accessoryQueryWrapper);

        recruitApply.setRecruitRelationships(relationships);
        recruitApply.setRecruitWorkExps(workExps);
        recruitApply.setRecruitLearningExps(learningExps);
        recruitApply.setRecruitAccessoryInfo(accessoryInfos);
        RecruitDetailRespVO recruitDetailRespVO = RecruitmentBasicInfoConvert.INSTANCE.convertDetail(recruitApply);
        String jobCategory;
        String gender;
        if (recruitApply.getJobCategory() != null) {
            switch (recruitApply.getJobCategory()) {
                case 1:
                    jobCategory = "专技岗位";
                    break;
                case 2:
                    jobCategory = "管理岗位";
                    break;
                case 3:
                    jobCategory = "工勤岗位";
                    break;
                default:
                    jobCategory = "其他岗位";
            }
            recruitDetailRespVO.setJobCategoryResp(jobCategory);
        }
        if(recruitApply.getSex() != null) {
            switch (recruitApply.getSex()) {
                case 3:
                    gender = "男";
                    break;
                case 2:
                    gender = "女";
                    break;
                default:
                    gender = "其他";
            }
            recruitDetailRespVO.setSexResp(gender);
        }

        return recruitDetailRespVO;
    }

    @Override
    public List<RecruitBasicInfoRespVO> deliveryStatus() {
        return recruitBasicInfoMapper.getByUserId(getLoginUserId());
    }

    @Override
    public PageResult<RecruitStationVO> getStationPage(RecruitStationRequestVO recruitStationRequestVO) {
        IPage<RecruitStationVO> page = MyBatisUtils.buildPage(recruitStationRequestVO);
        List<RecruitStationVO> data = recruitBasicInfoMapper.getStationPage(page, recruitStationRequestVO);
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public RecruitInformationVO getInformationById(Long id) {
        RecruitInformationVO recruitInformationVO = recruitBasicInfoMapper.getInformationById(id);
        LocalDateTime cutTime = recruitInformationVO.getCutTime();
        LocalDateTime now=LocalDateTime.now();
        if(now.isAfter(cutTime)){
            recruitInformationVO.setIsOverdue(true);
        }else {
            recruitInformationVO.setIsOverdue(false);
        }
        return recruitInformationVO;
    }

    @Override
    public List<Long> getMobilesByIds(List<Long> ids) {
        return recruitBasicInfoMapper.getMobilesByIds(ids);
    }

    @Override
    public List<RecruitBasicInfoExcelVO> getBasicInfoExcel(RecruitBasicInfoExportReqVO reqVO) {
        List<Integer> ids = reqVO.getIds();
        List<RecruitBasicInfoExcelVO> result = new ArrayList<>();
        for (Integer id : ids) {
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy.MM.dd");
            //查询对应的简历信息
            RecruitBasicInfo recruitBasicInfo = recruitBasicInfoMapper.selectById(id);
            //截取出生年月日
            String birthday = recruitBasicInfo.getCardNo().substring(6, 14);
            //当前日期
            String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
            //计算年龄差
            int ageBit = Integer.parseInt(date) - Integer.parseInt(birthday);
            int personAge;
            //当年龄差的长度大于4位时，即出生年份小于当前年份
            if (Integer.toString(ageBit).length() > 4) {
            //截取掉后四位即为年龄
                personAge = Integer.parseInt(Integer.toString(ageBit).substring(0, Integer.toString(ageBit).length() - 4));
            }else {//当前年份出生，直接赋值为0岁
                personAge = 0;
            }
            //查询家庭成员及社会关系
            LambdaQueryWrapper<RecruitRelationship> relations = new LambdaQueryWrapper<>();
            relations.eq(RecruitRelationship::getBaseId, recruitBasicInfo.getId());
            List<RecruitRelationship> relationships = recruitRelationshipMapper.selectList(relations);
            //查询学习经历
            LambdaQueryWrapper<RecruitLearningExp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecruitLearningExp::getBaseId, recruitBasicInfo.getId()).orderByAsc(RecruitLearningExp::getEnrollmentDate);
            List<RecruitLearningExp> learningExps = recruitLearningExpMapper.selectList(queryWrapper);
            //查询工作经历
            LambdaQueryWrapper<RecruitWorkExp> workQueryWrapper = new LambdaQueryWrapper<>();
            workQueryWrapper.eq(RecruitWorkExp::getBaseId, recruitBasicInfo.getId()).orderByAsc(RecruitWorkExp::getStartTime);
            List<RecruitWorkExp> workExps = recruitWorkExpMapper.selectList(workQueryWrapper);
            //将学习经历和工作经历合并成个人简历
            StringBuilder resume = new StringBuilder();
            if (!learningExps.isEmpty()) {
                for (RecruitLearningExp learningExp : learningExps) {
                    resume.append(learningExp.getEnrollmentDate().format(fmt)).append("-").append(learningExp.getGraduationDate().format(fmt))
                            .append(" ").append(learningExp.getEducationLevel()).append("就读于").append(learningExp.getSchoolName()).append("\n");
                }
            }
            if (!workExps.isEmpty()) {
                for (RecruitWorkExp workExp : workExps) {
                    resume.append(workExp.getStartTime().format(fmt)).append("-").append(workExp.getEndTime().format(fmt)).append(" ")
                            .append("在").append(workExp.getWorkUnit()).append("担任").append(workExp.getJobTitle()).append("\n");
                }
            }
            recruitBasicInfo.setResumeInfo(resume.toString());
            Integer category = recruitBasicInfo.getJobCategory();
            String jobCategory;
            String gender;
            switch (category) {
                case 1 : jobCategory = "专技岗位";  break;
                case 2 : jobCategory = "管理岗位";  break;
                case 3 : jobCategory = "工勤岗位";  break;
                default : jobCategory = "其他岗位";
            }
            switch (recruitBasicInfo.getSex()) {
                case 3 : gender = "男"; break;
                case 2 : gender = "女"; break;
                default : gender = "其他";
            }
            //为每个家庭成员生成一条数据，方便后续导出
            if (!relationships.isEmpty()) {
                for (RecruitRelationship relationship : relationships) {
                    RecruitBasicInfoExcelVO excelVO = RecruitmentBasicInfoConvert.INSTANCE.convertBasicExcel(recruitBasicInfo);

                    excelVO.setMemberAge(String.valueOf(relationship.getAge()));
                    excelVO.setMemberName(relationship.getName());
                    excelVO.setRelation(relationship.getRelation());
                    excelVO.setMemberPoliticalStatus(relationship.getPoliticalStatus());
                    excelVO.setWorkUnitAndPosition(relationship.getWorkUnitAndPosition());
                    excelVO.setWorkUnitType("公益一类");
                    excelVO.setGender(gender);
                    excelVO.setAge(personAge);
                    excelVO.setPersonalClassification(jobCategory);
                    result.add(excelVO);
                }
            } else {
                RecruitBasicInfoExcelVO excelVO = RecruitmentBasicInfoConvert.INSTANCE.convertBasicExcel(recruitBasicInfo);
                excelVO.setWorkUnitType("公益一类");
                excelVO.setPersonalClassification(jobCategory);
                excelVO.setGender(gender);
                result.add(excelVO);
            }
        }
        return result;
    }

    @Override
    public List<List<RecruitApplyExcelVO>> getApplyExcel(RecruitApplyExportReqVO reqVO) {
        List<Integer> ids = reqVO.getIds();
        List<List<RecruitApplyExcelVO>> result = new ArrayList<>();
        for (Integer id : ids) {
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy.MM.dd");
            //查询对应的简历信息
            RecruitBasicInfo recruitBasicInfo = recruitBasicInfoMapper.selectById(id);
            List<RecruitApplyExcelVO> excelVOS = new ArrayList<>();
            //查询家庭成员及社会关系
            LambdaQueryWrapper<RecruitRelationship> relations = new LambdaQueryWrapper<>();
            relations.eq(RecruitRelationship::getBaseId, recruitBasicInfo.getId());
            List<RecruitRelationship> relationships = recruitRelationshipMapper.selectList(relations);
            //查询学习经历
            LambdaQueryWrapper<RecruitLearningExp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecruitLearningExp::getBaseId, recruitBasicInfo.getId()).orderByAsc(RecruitLearningExp::getEnrollmentDate);
            List<RecruitLearningExp> learningExps = recruitLearningExpMapper.selectList(queryWrapper);
            //查询工作经历
            LambdaQueryWrapper<RecruitWorkExp> workQueryWrapper = new LambdaQueryWrapper<>();
            workQueryWrapper.eq(RecruitWorkExp::getBaseId, recruitBasicInfo.getId()).orderByAsc(RecruitWorkExp::getStartTime);
            List<RecruitWorkExp> workExps = recruitWorkExpMapper.selectList(workQueryWrapper);
            //将学习经历和工作经历合并成个人简历
            StringBuilder resume = new StringBuilder();
            if (!learningExps.isEmpty()) {
                for (RecruitLearningExp learningExp : learningExps) {
                    resume.append(learningExp.getEnrollmentDate().format(fmt)).append("-").append(learningExp.getGraduationDate().format(fmt))
                            .append(" ").append(learningExp.getEducationLevel()).append("就读于").append(learningExp.getSchoolName()).append("\n");
                }
            }
            if (!workExps.isEmpty()) {
                for (RecruitWorkExp workExp : workExps) {
                    resume.append(workExp.getStartTime().format(fmt)).append("-").append(workExp.getEndTime().format(fmt)).append(" ")
                            .append("在").append(workExp.getWorkUnit()).append("担任").append(workExp.getJobTitle()).append("\n");
                }
            }
            recruitBasicInfo.setResumeInfo(resume.toString());
            //为每个家庭成员生成一条数据，方便后续导出
            if (!relationships.isEmpty()) {
                for (RecruitRelationship relationship : relationships) {
                    RecruitApplyExcelVO excelVO = RecruitmentBasicInfoConvert.INSTANCE.convertExcel(recruitBasicInfo);

                    excelVO.setMemberAge(String.valueOf(relationship.getAge()));
                    excelVO.setMemberName(relationship.getName());
                    excelVO.setRelation(relationship.getRelation());
                    excelVO.setMemberPoliticalStatus(relationship.getPoliticalStatus());
                    excelVO.setWorkUnitAndPosition(relationship.getWorkUnitAndPosition());
                    excelVOS.add(excelVO);
                }
                result.add(excelVOS);
            } else {
                RecruitApplyExcelVO excelVO = RecruitmentBasicInfoConvert.INSTANCE.convertExcel(recruitBasicInfo);
                excelVOS.add(excelVO);
                result.add(excelVOS);
            }
        }
        return result;
    }
}
