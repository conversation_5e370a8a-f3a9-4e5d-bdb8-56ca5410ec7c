package com.unicom.swdx.module.hr.controller.admin.personnal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonnalBasicEntryVO {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true, example = "张三")
    @NotNull(message = "姓名不能为空")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String name;
    /**
     * 证件类型
     * 枚举
     */
    @ApiModelProperty(value = "证件类型", required = true, example = "1")
    @NotNull(message = "证件类型不能为空")
    private Integer idType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码", required = true, example = "430521200001011234")
    @NotNull(message = "证件号码不能为空")
    private String idNumber;
    /**
     * 性别
     * 枚举
     */
    @ApiModelProperty(value = "性别", required = true, example = "1")
    @NotNull(message = "不能为空")

    private Integer gender;
    /**
     * 照片地址
     */
    @ApiModelProperty(value = "照片地址", example = "https://example.com/photo.png")
//    @NotNull(message = "不能为空")
    private String photo;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", example = "21")

    private String age;
    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", example = "2000-01-01T00:00:00")
    @NotNull(message = "不能为空")
    private LocalDateTime birthday;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13112345678")
    @NotNull(message = "不能为空")
    private String mobile;
    /**
     * 政治面貌
     * 枚举
     */
    @ApiModelProperty(value = "政治面貌", example = "1")
    @NotNull(message = "不能为空")

    private Integer politicalOutlook;
    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯", example = "湖南省长沙市")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String nativePlace;
    /**
     * 民族
     * 枚举
     */
    @ApiModelProperty(value = "民族", example = "1")

    private Integer nation;
    /**
     * 部门
     * 枚举
     */
    @ApiModelProperty(value = "部门", example = "1")
    @NotNull(message = "不能为空")
    private Long department;
    /**
     * 人员分类
     * 枚举
     */
    @ApiModelProperty(value = "人员分类", example = "1")
    @NotNull(message = "不能为空")

    private Integer peronClassification;
    /**
     * 家庭住址
     */
    @ApiModelProperty(value = "家庭住址", example = "湖南省长沙市xxx街道")
    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    private String familyAddress;
    /**
     * 人员状态
     * 枚举
     */
    @ApiModelProperty(value = "人员状态", example = "1")
    @NotNull(message = "不能为空")
    private Integer personnalStatus;


    /**
     * 科室
     */
    @ApiModelProperty(value = "科室", example = "xxx科室")
    @Length(min = 0, max = 20, message = "长度为 0-20 位")
    private String subjectRoom;
    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱", example = "<EMAIL>")
    @Length(min = 0, max = 50, message = "长度为 0-50 位")
    @NotNull(message = "不能为空")
    private String email;




    @ApiModelProperty(value = "报道日期", example = "2021-01-01T00:00:00")
    @NotNull(message = "不能为空")
    private LocalDateTime registrationDate;


    @ApiModelProperty(value = "试用期截至日期", example = "2021-01-01T00:00:00")
    private LocalDateTime trialDeadline;
    /**
     * 转正时间
     */
    @ApiModelProperty(value = "转正时间", example = "2021-01-01T00:00:00")
    private LocalDateTime confirmationDate;
    /**
     * 婚姻状态
     * 枚举
     */
    @ApiModelProperty(value = "婚姻状态", example = "1")
    private Integer marryStatus;

    @ApiModelProperty(value = "录用方式", example = "<EMAIL>")
    private Integer recruitmentMethod;

}
