package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateReqVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPartyGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalPartyVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPartyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;

import javax.validation.Valid;

/**
 * 人事党员Service 接口
 *
 */
public interface PersonnalPartyService extends IService<PersonnalPartyDO> {


    /**
     * 新增人事党员信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createPersonnal(@Valid PersonnalPartyVO createReqVO, Long personnalId);

    PersonnalPartyDO getPersonnal(Long id);
    void  updatePersonnal(PersonnalPartyGetVO updateReqVO);
}
