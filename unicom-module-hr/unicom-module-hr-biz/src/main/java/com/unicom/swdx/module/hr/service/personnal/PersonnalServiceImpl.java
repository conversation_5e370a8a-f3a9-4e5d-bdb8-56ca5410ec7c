package com.unicom.swdx.module.hr.service.personnal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.enums.ReviewStatusEnum;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.crypt.sm2.SM2Utils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.hr.api.dto.PersonDTO;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.death.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.leave.*;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.retire.*;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalPositionConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalStudyConvert;
import com.unicom.swdx.module.hr.convert.personnal.PersonnalWorkConvert;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalBasicDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalPositionDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalWorkDO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.*;
import com.unicom.swdx.module.hr.mq.producer.PersonProducer;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.message.MessageApi;
import com.unicom.swdx.module.system.api.message.dto.MessageAuthorityUpdateReqDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.permission.RoleApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserReqDTO;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import com.unicom.swdx.module.system.api.user.dto.KafkaPersonDTO;
import com.unicom.swdx.module.system.api.user.dto.UserDeptDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertList;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertMap;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.hr.enums.ErrorCodeConstants.PERSONNAL_IMPORT_LIST_IS_EMPTY;
import static com.unicom.swdx.module.hr.enums.ErrorCodeConstants.PERSONNAL_NAME_EXISTS;
import static com.unicom.swdx.module.hr.enums.PostEnum.*;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * 人事信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonnalServiceImpl extends ServiceImpl<PersonnalMapper, PersonnalBasicDO> implements PersonnalService {
    @Resource
    private PersonnalMapper personnalMapper;
    @Resource
    private PersonnalStudyMapper personnalStudyMapper;
    @Resource
    private PersonnalPositionMapper personnalPositionMapper;
    @Resource
    private PersonnalWorkMapper personnalWorkMapper;
    @Resource
    private PersonnalPartyMapper personnalPartyMapper;
    @Resource
    private PersonnalRegistrationMapper personnalRegistrationMapper;
    @Resource
    private PermissionApi permissionApi;

    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;
    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private MessageApi messageApi;

    @Resource
    private RoleApi roleApi;
    @Resource
    private PostApi postApi;

    @Resource
    private DeptApi deptApi;
    @Resource
    private PersonProducer personProducer;

    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonnalBasicDO createPersonnal(PersonnalBasicVO createReqVO, Integer rank, String positionName, Integer positionRank) {
        // 校验正确性
        validCreateOrUpdate(null,createReqVO);

        String workId = generateWorkId();
        PersonnalBasicDO basic = PersonnalConvert.INSTANCE.convert(createReqVO);
        if(basic.getDepartment() == null) {
            basic.setDepartment(createReqVO.getDeptIds().get(0));
        }

        basic.setWorkId(workId);
        if (!(permissionApi.hasAnyRoles(getLoginUserId(),"hr-admin").getData())) {
            basic.setReviewStatus(ReviewStatusEnum.DEP_REVIRW.getCode());
        }else {
            AdminUserReqDTO user = new AdminUserReqDTO();
            user.setMobile(basic.getMobile());
            user.setPassword(SM2Utils.USER_PASSWORD);
            user.setNickname(basic.getName());
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            user.setSort(0);
            user.setDeptId(basic.getDepartment());
            user.setUsername(basic.getMobile());
            Set<Long> postIds = new LinkedHashSet<>();
            //职级
            Long tenantId = SecurityFrameworkUtils.getTenantId();
            if(Objects.equals(rank,1)){
                Long postId = postApi.getPostByCode(INSPECTOR_1.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }else if(Objects.equals(rank,2)){
                Long postId = postApi.getPostByCode(INSPECTOR_2.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }else if(Objects.equals(rank,3) || Objects.equals(rank,4)
                    || Objects.equals(rank,5) || Objects.equals(rank,6)){
                Long postId = postApi.getPostByCode(INVESTIGATOR.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }
            //职务名称
            if(Objects.equals(positionName,"副校（院）长") || Objects.equals(positionName,"教育长")
                    || Objects.equals(positionName,"韶山干部学院院长")){
                Long postId = postApi.getPostByCode(SCHOOL_LEADER.getPostCode(), tenantId).getCheckedData().getId();
                Long postId1 = postApi.getPostByCode(PROVINCE_LEADER.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
                postIds.add(postId1);
            }else if(Objects.equals(positionName,"分管日常工作的副校长（副院长）")){
                Long postId = postApi.getPostByCode(VICE_CHANCELLOR.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }else if(Objects.equals(positionName,"主任") || Objects.equals(positionName,"院长")
                    || Objects.equals(positionName,"主任（校长）")){
                Long postId = postApi.getPostByCode(DIRECTOR.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }else if(Objects.equals(positionName,"副主任") || Objects.equals(positionName,"副院长")
                    || Objects.equals(positionName,"副主任（副校长）") || Objects.equals(positionName,"副馆长")){
                Long postId = postApi.getPostByCode(VICE_DIRECTOR.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }
            //职务级别
            if(Objects.equals(positionRank,1) || Objects.equals(positionRank,2)
                    ||Objects.equals(positionRank,5) || Objects.equals(positionRank,6)) {
                Long postId = postApi.getPostByCode(PROVINCE_LEADER.getPostCode(), tenantId).getCheckedData().getId();
                postIds.add(postId);
            }
            if(CollUtil.isEmpty(postIds)){
                if(Objects.equals(tenantId,25L)) {
                    Long postId = postApi.getPostByCode(CLERK.getPostCode(), tenantId).getCheckedData().getId();
                    postIds.add(postId);
                }
            }
            user.setPostIds(postIds);

            //如果之前用户表有这个人 直接返回该用户id 并加入教职工权限
            CommonResult<Long> result = adminUserApi.createPersonal(user);
            Long userId= result.getData();
            //增加多部门信息
            List<Long> deptIds = createReqVO.getDeptIds();
            if(deptIds != null) {
                adminUserApi.createUserDeptBatch(userId, deptIds);
            } else {
                UserDeptDTO userDept = new UserDeptDTO();
                userDept.setUserId(userId);
                userDept.setDeptId(user.getDeptId());
                adminUserApi.createUserDept(userDept);
            }
            Integer code = result.getCode();
            String msg = result.getMsg();
            if(0!=code){
                throw exception(new ErrorCode(code, msg));
            }
            basic.setUserId(userId);
            // 插入关联岗位
            if (CollectionUtil.isNotEmpty(user.getPostIds())) {
                postApi.saveUserPost(userId, user.getPostIds());
            }
            Long applicant = roleApi.getRoleIdByName("应聘者").getCheckedData();
//            分配角色权限
            Set<Long> roleIds = new HashSet<>();
            roleIds.add(roleApi.getInnerRoleIdByCode("DX-Homepage").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("DX-Teacher").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("jiaowu-role").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("teacher-role").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("yikatongzhuye-role").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("zhaopiancaiji-role").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("yikatong-role").getCheckedData());
            roleIds.add(roleApi.getInnerRoleIdByCode("staff-role").getCheckedData());
            roleIds.add(roleApi.getCustomRoleIdByCode("teacher").getCheckedData());
            //staff-role人事教职工角色因为不是内置角色所有直接写id
            roleIds.add(64L);
            //week_report周工作安排上报
//            roleIds.add(88L);
            roleIds.add(applicant);
            roleApi.createRole(userId, roleIds);
            //创建同步角色组
            if(createReqVO.getPersonnalStatus()!=null){
                //在职
                if(createReqVO.getPersonnalStatus() == 1){
                    permissionApi.assignUserRoleGroup(userId,"党校教职工");
                }else if(createReqVO.getPersonnalStatus() == 3){
                    permissionApi.assignUserRoleGroup(userId,"党校退休人员");
                }
                List<String> mobile = adminUserApi.getDangJianMobile().getCheckedData();
                if(mobile.contains(user.getMobile())){
                    //党员角色
                    Set<Long> dangIds = new HashSet<>();
                    dangIds.add(roleApi.getCustomRoleIdByCode("dangwu-role").getCheckedData());
                    dangIds.add(roleApi.getCustomRoleIdByCode("dangyuan-role").getCheckedData());
                    dangIds.add(roleApi.getCustomRoleIdByCode("dangjian").getCheckedData());
                    roleApi.createRole(userId, dangIds);
                }
            }
        }

        //去除默认值
        if("0".equals(basic.getMobile())){
            basic.setMobile(null);
        }
        personnalMapper.insert(basic);
        return basic;
    }
    /**
     * 新增人事入职信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEntryPersonnal(PersonnalBasicEntryVO createReqVO) {
        // 校验正确性
        validCreateOrUpdateEntry(null, createReqVO);
        String workId = generateWorkId();
        PersonnalBasicDO basic = PersonnalConvert.INSTANCE.convert(createReqVO);
        basic.setWorkId(workId);
        personnalMapper.insert(basic);
        return basic.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRegistrationPersonnal(PersonnalCreateRegistrationVO createReqVO) {
        // 校验手机号
        checkMobileUnique(null, createReqVO.getMobile());
        String workId = generateWorkId();
        PersonnalBasicDO basic = PersonnalConvert.INSTANCE.convert(createReqVO);
        basic.setReviewStatus(ReviewStatusEnum.REGISTRATION_REVIRW.getCode());
        basic.setPersonnalStatus(1);
        basic.setWorkId(workId);
        if(basic.getDepartment() == null) {
            basic.setDepartment(createReqVO.getDeptIds().get(0));
        }
        //     创建职工用户账号
        AdminUserReqDTO user = new AdminUserReqDTO();
        user.setMobile(basic.getMobile());
        user.setPassword(SM2Utils.USER_PASSWORD);
        user.setNickname(basic.getName());
        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
        user.setSort(0);
        user.setDeptId(basic.getDepartment());
        user.setUsername(basic.getMobile());
        CommonResult<Long> result = adminUserApi.createPersonal(user);
        Long userId= result.getData();
        Integer code = result.getCode();
        String msg = result.getMsg();
        if(0!=code){
            throw exception(new ErrorCode(code, msg));
        }
        Long applicant = roleApi.getRoleIdByName("应聘者").getCheckedData();
        Set<Long> roleIds = new HashSet<>();
        roleIds.add(roleApi.getInnerRoleIdByCode("DX-Homepage").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("DX-Teacher").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("jiaowu-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("teacher-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("yikatongzhuye-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("zhaopiancaiji-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("yikatong-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("staff-role").getCheckedData());
        roleIds.add(roleApi.getCustomRoleIdByCode("teacher").getCheckedData());
        //staff-role人事教职工角色因为不是内置角色所有直接写id
        roleIds.add(64L);
        //week_report周工作安排上报
//            roleIds.add(88L);
        roleIds.add(applicant);
        roleApi.createRole(userId, roleIds);

        //分配离校角色组
        permissionApi.assignUserRoleGroup(userId,"人事教职工");
        basic.setUserId(userId);
        personnalMapper.insert(basic);
        //增加多部门信息
        List<Long> deptIds = createReqVO.getDeptIds();
        if(deptIds != null) {
            adminUserApi.createUserDeptBatch(userId, deptIds);
        } else {
            UserDeptDTO userDept = new UserDeptDTO();
            userDept.setUserId(userId);
            userDept.setDeptId(user.getDeptId());
            adminUserApi.createUserDept(userDept);
        }
        return basic.getId();
    }

    /**
     * 获得人事信息分页
     *
     * @param pageReqVO 分页查询
     * @return 人事信息分页
     */
    @Override
    public PageResult<PersonnalPageRespVO> getPersonnalPage(PersonnalPageReqVO pageReqVO) {
//        if (permissionService.isSuperAdmin(getLoginUserId())) {
//            pageReqVO.setTenantId(null);
//        } else {
//            pageReqVO.setTenantId(getTenantId());
//        }
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        DeptRespDTO dept = deptApi.getDeptByTenantAndName(getTenantId(), "服务企业").getCheckedData();
        List<PersonnalPageRespVO> data = personnalMapper.selectPage(page, pageReqVO);
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        for (PersonnalPageRespVO person : data) {
            List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(dptIds)) {
                if (person.getDepartment() != null) {
                    List<Long> depts = new ArrayList<>();
                    depts.add(person.getDepartment());
                    person.setDeptIds(depts);
                }
            } else {
                person.setDeptIds(dptIds);
            }
        }
        if (dept != null) {
            data = data.stream().filter(i -> !i.getDeptIds().contains(dept.getId())).collect(Collectors.toList());
        }
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public List<PersonnalPageRespVO> getPersonnalPageChildren(PersonnalChildrenReqVO reqVO) {
        List<PersonnalPageRespVO> data = personnalMapper.selectChildren(reqVO);
        DeptRespDTO dept = deptApi.getDeptByTenantAndName(getTenantId(), "服务企业").getCheckedData();
        if (org.springframework.util.StringUtils.hasText(reqVO.getUsername())) {
            //修复模糊查询逃逸
            reqVO.setUsername(reqVO.getUsername().replace("%","\\%").replace("_","\\_"));
        }
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        for (PersonnalPageRespVO person : data) {
            List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(dptIds)) {
                if (person.getDepartment() != null) {
                    List<Long> depts = new ArrayList<>();
                    depts.add(person.getDepartment());
                    person.setDeptIds(depts);
                }
            } else {
                person.setDeptIds(dptIds);
            }
        }
        if (dept != null) {
            data = data.stream().filter(i -> !i.getDeptIds().contains(dept.getId())).collect(Collectors.toList());
        }
        return data;
    }

    @Override
    public PageResult<PersonnalReviewPageRespVO> getPersonnalReviewPage(PersonnalReviewPageReqVO pageReqVO) {
//        if (permissionService.isSuperAdmin(getLoginUserId())) {
//            pageReqVO.setTenantId(null);
//        } else {
//            pageReqVO.setTenantId(getTenantId());
//        }
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        List<PersonnalReviewPageRespVO> data = personnalMapper.selectReviewPage(page, pageReqVO);
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        DeptRespDTO dept = deptApi.getDeptByTenantAndName(getTenantId(), "服务企业").getCheckedData();
        for (PersonnalReviewPageRespVO person : data) {
            if(person.getUserId() != null) {
                List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(dptIds)) {
                    if (person.getDepartment() != null) {
                        List<Long> depts = new ArrayList<>();
                        depts.add(person.getDepartment());
                        person.setDeptIds(depts);
                    }
                } else {
                    person.setDeptIds(dptIds);
                }
            }
        }
        if (dept != null) {
            data = data.stream().filter(i -> !i.getDeptIds().contains(dept.getId())).collect(Collectors.toList());
        }
        return new PageResult<>(data, page.getTotal());
    }
//    @Override
//    public PageResult<PersonnalEntryPageRespVO> getPersonnalEntryPage(PersonnalEntryPageReqVO pageReqVO) {
////        if (permissionService.isSuperAdmin(getLoginUserId())) {
////            pageReqVO.setTenantId(null);
////        } else {
////            pageReqVO.setTenantId(getTenantId());
////        }
//        IPage page = MyBatisUtils.buildPage(pageReqVO);
////        List<PersonnalEntryPageRespVO> data = personnalMapper.selectEntryPage(page, pageReqVO);
//        return new PageResult<>(data, page.getTotal());
//    }

    @Override
    public PageResult<PersonnalRegistrationPageRespVO> getPersonnalRegistrationPage(PersonnalRegistrationPageReqVO pageReqVO) {
//        if (permissionService.isSuperAdmin(getLoginUserId())) {
//            pageReqVO.setTenantId(null);
//        } else {
//            pageReqVO.setTenantId(getTenantId());
//        }
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        List<PersonnalRegistrationPageRespVO> data = personnalRegistrationMapper.selectRegistrationPage(page, pageReqVO);
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        DeptRespDTO dept = deptApi.getDeptByTenantAndName(getTenantId(), "服务企业").getCheckedData();
        for (PersonnalRegistrationPageRespVO person : data) {
            if(person.getUserId() != null) {
                List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(dptIds)) {
                    if (person.getDepartment() != null) {
                        List<Long> depts = new ArrayList<>();
                        depts.add(person.getDepartment().longValue());
                        person.setDeptIds(depts);
                    }
                } else {
                    person.setDeptIds(dptIds);
                }
            }
        }
        if (dept != null) {
            data = data.stream().filter(i -> !i.getDeptIds().contains(dept.getId())).collect(Collectors.toList());
        }
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public PersonnalBasicDO getPersonnal(Long id) {
        PersonnalBasicDO personnalBasicDO = personnalMapper.selectById(id);
        Long userId = personnalBasicDO.getUserId();
        List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
        if(CollUtil.isEmpty(deptIds)) {
            deptIds.add(personnalBasicDO.getDepartment());
        }
        personnalBasicDO.setDeptIds(deptIds);
        return personnalBasicDO;
    }

    @Override
    public PersonnalSimpleRespVO getPersonnalSimple(Long id) {
        return personnalMapper.selectSimple(id);
    }
    @Override
    public void updatePersonnal(PersonnalBasicGetVO reqVO) {
        // 校验正确性
        validCreateOrUpdate(reqVO.getId(), reqVO);

        PersonnalBasicDO updateObj = PersonnalConvert.INSTANCE.convert1(reqVO);
        if(updateObj.getDeptIds() != null) {
            updateObj.setDepartment(updateObj.getDeptIds().get(0));
        }
        if (!(permissionApi.hasAnyRoles(getLoginUserId(),"hr-admin").getData())) {
            updateObj.setReviewStatus(ReviewStatusEnum.DEP_REVIRW.getCode());
        }else{
//            更新用户信息
            AdminUserRespDTO  user = adminUserApi.getPersonal(updateObj.getUserId()).getData();
            //判断是否部门是否更改reqVO.getDepartment()
            if(!user.getDeptId().equals(updateObj.getDepartment())){
                Long mid = messageApi.getMessage(user.getId());
                //消息权限存在的话更改状态为禁用
                if(mid!=null){
                    MessageAuthorityUpdateReqDTO messageAuthorityUpdateReqDTO = new MessageAuthorityUpdateReqDTO();
                    messageAuthorityUpdateReqDTO.setId(mid);
                    messageAuthorityUpdateReqDTO.setStatus(1);
                    messageApi.updateMessage(messageAuthorityUpdateReqDTO);
                }
            }
            //判断是否部门是否更改reqVO.getDepartment()
            if (!CollUtil.isEmpty(reqVO.getDeptIds())) {
                List<Long> deptIds = adminUserApi.getDeptList(user.getId()).getCheckedData();
                //判断是否部门是否更改reqVO.getDepartment()
                if(!deptIds.equals(reqVO.getDeptIds())){
                    Long mid = messageApi.getMessage(user.getId());
                    //消息权限存在的话更改状态为禁用
                    if(mid!=null){
                        MessageAuthorityUpdateReqDTO messageAuthorityUpdateReqDTO = new MessageAuthorityUpdateReqDTO();
                        messageAuthorityUpdateReqDTO.setId(mid);
                        messageAuthorityUpdateReqDTO.setStatus(1);
                        messageApi.updateMessage(messageAuthorityUpdateReqDTO);
                    }
                }
            }
            user.setMobile(updateObj.getMobile());
            user.setDeptId(updateObj.getDepartment());
            user.setNickname(updateObj.getName());
            user.setDeptIds(reqVO.getDeptIds());
            user.setAvatar(updateObj.getPhoto());
            user.setSex(updateObj.getGender());
            user.setEmail(updateObj.getEmail());

            //改为在职需要把用户状态设置为0
            if(reqVO.getPersonnalStatus()==1){
                user.setStatus(0);
                //好像这样更新时间不太行
                updateObj.setDeathTime(null);
                updateObj.setRetireTime(null);
                updateObj.setLeaveTime(null);
                //这样才有效
                personnalMapper.updateRetireTime(updateObj.getId());
            }
            if(reqVO.getId()!=null) {
                PersonnalBasicDO personnalBasic = getPersonnal(reqVO.getId());
                //判断是否在职状态有变化
                if (reqVO.getPersonnalStatus() != null
                        && !personnalBasic.getPersonnalStatus().equals(reqVO.getPersonnalStatus())) {
                    //创建同步角色组
                    //在职
                    if (reqVO.getPersonnalStatus() == 1) {
                        permissionApi.assignUserRoleGroup(reqVO.getUserId(), "党校教职工");
                    } else if (reqVO.getPersonnalStatus() == 3) {
                        Long total = bpmProcessInstanceApi.getTodoTaskTotal(reqVO.getUserId());
                        //中心工作未完成
                        Long zxTotal = bpmProcessInstanceApi.getTaskTotal(reqVO.getUserId());
                        //判断是否有待处理事项
                        if (total > 0 || zxTotal > 0) {
                            throw exception(USER_EXIST_UNFINISHED_TASK);
                        }
                        permissionApi.assignUserRoleGroup(reqVO.getUserId(), "党校退休人员");
                    } else if (reqVO.getPersonnalStatus() == 2) {
                        Long total = bpmProcessInstanceApi.getTodoTaskTotal(reqVO.getUserId());
                        //中心工作未完成
                        Long zxTotal = bpmProcessInstanceApi.getTaskTotal(reqVO.getUserId());
                        //判断是否有待处理事项
                        if (total > 0 || zxTotal > 0) {
                            throw exception(USER_EXIST_UNFINISHED_TASK);
                        }
                    }
                }
                List<String> mobile = adminUserApi.getDangJianMobile().getCheckedData();
                if(mobile.contains(user.getMobile())){
                    //党员角色
                    Set<Long> dangIds = new HashSet<>();
                    dangIds.add(roleApi.getCustomRoleIdByCode("dangwu-role").getCheckedData());
                    dangIds.add(roleApi.getCustomRoleIdByCode("dangyuan-role").getCheckedData());
                    dangIds.add(roleApi.getCustomRoleIdByCode("dangjian").getCheckedData());
                    roleApi.createRole(personnalBasic.getUserId(), dangIds);
                }
            }
            CommonResult<Boolean> result = adminUserApi.updatePersonal(user);
            Integer code = result.getCode();
            String msg = result.getMsg();
            if(0!=code){
                throw exception(new ErrorCode(code, msg));
            }
        }
        //传空字段变更为空
        LambdaUpdateWrapper<PersonnalBasicDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(PersonnalBasicDO::getId,updateObj.getId());
        if(updateObj.getPoliticalOutlook() == null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getPoliticalOutlook,updateObj.getPoliticalOutlook());
        }
        if(updateObj.getMarryStatus() == null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getMarryStatus,updateObj.getMarryStatus());
        }
        if(updateObj.getNation()==null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getNation,updateObj.getNation());
        }
        if(updateObj.getRegistrationDate() == null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getRegistrationDate,updateObj.getRegistrationDate());
        }
        if(updateObj.getConfirmationDate() == null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getConfirmationDate,updateObj.getConfirmationDate());
        }
        if(updateObj.getTrialDeadline() == null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getTrialDeadline,updateObj.getTrialDeadline());
        }
        if(updateObj.getRecruitmentMethod() == null){
            lambdaUpdateWrapper.set(PersonnalBasicDO::getRecruitmentMethod,updateObj.getRecruitmentMethod());
        }
        personnalMapper.updateById(updateObj);
        personnalMapper.update(new PersonnalBasicDO(),lambdaUpdateWrapper);

    }

    public void updateFromUser(PersonnalBasicGetVO reqVO) {
        // 校验正确性
        //validCreateOrUpdate(reqVO.getId(), reqVO);

        PersonnalBasicDO updateObj = PersonnalConvert.INSTANCE.convert1(reqVO);
        if(updateObj.getDeptIds() != null) {
            updateObj.setDepartment(updateObj.getDeptIds().get(0));
        }
        if (!(permissionApi.hasAnyRoles(getLoginUserId(),"hr-admin").getData())) {
            updateObj.setReviewStatus(ReviewStatusEnum.DEP_REVIRW.getCode());
        }
        LambdaUpdateWrapper<PersonnalBasicDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PersonnalBasicDO::getUserId, updateObj.getUserId());
        wrapper.set(updateObj.getGender() != null, PersonnalBasicDO::getGender, updateObj.getGender());
        wrapper.set(updateObj.getName() != null, PersonnalBasicDO::getName, updateObj.getName());
        wrapper.set(updateObj.getMobile() != null, PersonnalBasicDO::getMobile, updateObj.getMobile());
        wrapper.set(updateObj.getEmail() != null, PersonnalBasicDO::getEmail, updateObj.getEmail());
        personnalMapper.update(null ,wrapper);

    }

    @Override
    public List<PersonnalExcelVO> getPersonnalExcel(PersonnalExportReqVO reqVO){
//        Long tenantId = null;
//        if (!permissionService.isSuperAdmin(getLoginUserId())) {
//            tenantId = getTenantId();
//        }
        List<PersonnalExcelVO> personnalExcelVOS = personnalMapper.selectPostList(reqVO);
        for (PersonnalExcelVO personnalExcelVO : personnalExcelVOS) {
            Long userId = personnalExcelVO.getUserId();
            List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
            if(!CollUtil.isEmpty(deptIds)) {
                List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                personnalExcelVO.setDepartmentName(departments);
            }
        }
        return personnalExcelVOS;
    }
    @Override
//    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public PersonnalImportRespVO importPersonnal(List<PersonnalImportExcelVO> importPersonnals, boolean isUpdateSupport) {

        if (CollUtil.isEmpty(importPersonnals)) {
            throw exception(PERSONNAL_IMPORT_LIST_IS_EMPTY);
        }

        Long tenantId = SecurityFrameworkUtils.getTenantId();

        PersonnalImportRespVO respVO = PersonnalImportRespVO.builder().createPersonnalNames(new ArrayList<>())
                .updatePersonnalNames(new ArrayList<>()).failurePersonnalNames(new LinkedHashMap<>()).build();
        List<PersonnalBasicDO> existUsers = personnalMapper.selectList();

        List<PersonnalBasicDO> personnalBasicS = PersonnalConvert.INSTANCE.convertList(importPersonnals);
        List<PersonnalStudyDO> personnalStudyS = PersonnalStudyConvert.INSTANCE.convertList(importPersonnals);
//        List<PersonnalPartyDO> personnalPartyS = PersonnalPartyConvert.INSTANCE.convertList(importPersonnals);
        List<PersonnalPositionDO> personnalPositionS = PersonnalPositionConvert.INSTANCE.convertList(importPersonnals);

        List<KafkaPersonDTO> kafkaAddList = new ArrayList<>();
        List<KafkaPersonDTO> kafkaUpdateList = new ArrayList<>();

        for(int i = 0; i<importPersonnals.size(); i++){

            try {

                //为0的号码 设置成null
                if("0".equals(personnalBasicS.get(i).getMobile())){
                    personnalBasicS.get(i).setMobile(null);
                }

                //如果年龄和出生日期为空则根据身份证自动获取
                String idCard = personnalBasicS.get(i).getIdNumber();
                if(idCard.length() != 18 && idCard.length() != 15) {
                    throw exception(new ErrorCode(501, "第"+ (i + 1) +"行导入数据的身份证号位数不对！"));
                }

                if(StrUtil.isEmpty(personnalBasicS.get(i).getAge())
                        && StrUtil.isNotEmpty(idCard) && idCard.length()>=15) {
                    int age = 0;

                    String birth = "";
                    if (idCard.length() == 18) {
                        birth = idCard.substring(6, 14);
                    } else if (idCard.length() == 15) {
                        birth = "19" + idCard.substring(6, 12);
                    }

                    int year = Integer.valueOf(birth.substring(0, 4));
                    int month = Integer.valueOf(birth.substring(4, 6));
                    int day = Integer.valueOf(birth.substring(6));
                    Calendar cal = Calendar.getInstance();
                    age = cal.get(Calendar.YEAR) - year;
                    //周岁计算
                    if (cal.get(Calendar.MONTH) < (month - 1) || (cal.get(Calendar.MONTH) == (month - 1) && cal.get(Calendar.DATE) < day)) {
                        age--;
                    }
                    personnalBasicS.get(i).setAge(String.valueOf(age));
                }
                if(personnalBasicS.get(i).getBirthday() == null && StrUtil.isNotEmpty(idCard) && idCard.length()>=15) {
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    if (idCard.length() == 18) {
                        String year = idCard.substring(6).substring(0, 4);// 得到年份
                        String month = idCard.substring(10).substring(0, 2);// 得到月份
                        String day = idCard.substring(12).substring(0, 2);// 得到日
                        LocalDate birth = LocalDate.parse(year + "-" + month + "-" + day, fmt);
                        personnalBasicS.get(i).setBirthday(birth.atStartOfDay());
                    } else if (idCard.length() == 15) {
                        String year = "19" + idCard.substring(6, 8);// 年份
                        String month = idCard.substring(8, 10);// 月份
                        String day = idCard.substring(10, 12);// 得到日
                        LocalDate birth = LocalDate.parse(year + "-" + month + "-" + day, fmt);
                        personnalBasicS.get(i).setBirthday(birth.atStartOfDay());
                    }

                }


            }catch (Exception e){
                log.error("用户导入身份证错误信息 {}" ,   e.getMessage());
            }

            PersonnalImportExcelVO importPersonnal = importPersonnals.get(i);
            Optional<PersonnalBasicDO> personnalDO = existUsers.stream().filter(u -> {
                        if(StrUtil.isEmpty( importPersonnal.getWorkId() )){
                            return Objects.equals(u.getMobile(), importPersonnal.getMobile());
                        }else{
                            return Objects.equals(u.getWorkId(), importPersonnal.getWorkId());  //上传了workid的话以这个为准
                        }
            }).findFirst();
            PersonnalBasicDO existMobile = personnalDO==null? null:personnalDO.orElse(null);

            PersonnalBasicDO basic = personnalBasicS.get(i);
            PersonnalStudyDO studyDO = personnalStudyS.get(i);
            PersonnalPositionDO positionDO = personnalPositionS.get(i);


            if(existMobile == null){
                PersonnalBasicDO person = personnalBasicS.get(i);


                //校验手机号是否重复
//                if(existUsers.stream().anyMatch(u->Objects.equals(u.getMobile(),person.getMobile()))){
//                    throw exception(USER_MOBILE_EXISTS);
//                }
                String workId = generateWorkId();
                personnalBasicS.get(i).setWorkId(workId);
                String[] departmentName = importPersonnal.getDepartmentName().split(",");
                List<Long> deptIds = new ArrayList<>();
                for (String deptName : departmentName) {
                    Long depId = personnalMapper.selectDeptId(deptName, tenantId);
                    if(depId == null) {
                        throw exception(new ErrorCode(500, "第"+ (i + 1) +"行导入数据的当前部门不存在"));
                    }
                    deptIds.add(depId);
                }
                personnalBasicS.get(i).setDepartment(deptIds.get(0));
                personnalBasicS.get(i).setDeptIds(deptIds);
                if(Objects.nonNull(importPersonnal.getBirthday()) && StrUtil.isNotEmpty(personnalBasicS.get(i).getAge())){
                    String age = getAge(importPersonnal.getBirthday());
                    personnalBasicS.get(i).setAge(age);
                }


                Long userId = createPersonnalUser(basic , studyDO  , positionDO ,tenantId  , deptIds);

                personnalMapper.insert(basic);

                Long personnalId = personnalBasicS.get(i).getId();

                personnalStudyS.get(i).setPersonnalId(personnalId);
                personnalStudyMapper.insert(personnalStudyS.get(i));

//                personnalPartyS.get(i).setPersonnalId(personnalId);
//                personnalPartyMapper.insert(personnalPartyS.get(i));

                personnalPositionS.get(i).setPersonnalId(personnalId);
                personnalPositionMapper.insert(personnalPositionS.get(i));

                List<PersonnalWorkExcelVO> workList = getMyWorkList(importPersonnal);
                List<PersonnalWorkDO> personnalWorks = PersonnalWorkConvert.INSTANCE.convertList(workList);
                personnalWorks.forEach(work -> {
                    work.setPersonnalId(personnalId);
                });
                personnalWorkMapper.insertBatch(personnalWorks);
                existUsers.add(personnalBasicS.get(i));
                respVO.getCreatePersonnalNames().add(importPersonnal.getName());

                KafkaPersonDTO kafkaPersonDTO = PersonnalConvert.INSTANCE.convertToKafkaPerson3(importPersonnal);
                kafkaPersonDTO.setDepartment(deptIds.get(0));
                kafkaPersonDTO.setUserId(userId);

                kafkaPersonDTO.setTenantId(tenantId);
                kafkaPersonDTO.setTenantCode(tenantApi.getTenantCodeById(tenantId).getCheckedData());


                if("0".equals(importPersonnal.getSendkafka())){
                    //0的时候不发kafka
                }else{
                    kafkaAddList.add(kafkaPersonDTO);
                }


                continue;
            }
            if (!isUpdateSupport) {
                respVO.getFailurePersonnalNames().put(importPersonnal.getName(), USER_MOBILE_EXISTS.getMsg());
            }else {
//                校验手机号是否重复
//                if(existUsers.stream().anyMatch(u->Objects.equals(u.getMobile(),importPersonnal.getMobile())
//                        && !Objects.equals(u.getName(),importPersonnal.getName()))){
//                    throw exception(USER_MOBILE_EXISTS);
//                }
                PersonnalBasicDO updateObj = PersonnalConvert.INSTANCE.convert2(importPersonnal);

                String[] departmentName = null;
                try {
                    departmentName = importPersonnal.getDepartmentName().split(",");
                }catch (Exception e){
                    throw exception(new ErrorCode(500, "部门信息不存在"));
                }

                List<Long> deptIds = new ArrayList<>();
                for (String deptName : departmentName) {
                    Long depId = personnalMapper.selectDeptId(deptName, tenantId);
                    if(depId == null) {
                        throw exception(new ErrorCode(500, "第"+ (i + 1) +"行导入数据的当前部门不存在"));
                    }
                    deptIds.add(depId);
                }
                personnalBasicS.get(i).setDepartment(deptIds.get(0));
                personnalBasicS.get(i).setDeptIds(deptIds);
                updateObj.setDepartment(deptIds.get(0));
                updateObj.setDeptIds(deptIds);
                updateObj.setId(existMobile.getId());
                updateObj.setUserId(existMobile.getUserId());

                if(Objects.nonNull(importPersonnal.getBirthday())){
                    String age = getAge(importPersonnal.getBirthday());
                    updateObj.setAge(age);
                }


                AdminUserRespDTO  user = adminUserApi.getPersonal(updateObj.getUserId()).getData();

                if(user ==null && basic.getMobile()!=null){

                    Long userId  = createPersonnalUser(basic , studyDO, positionDO , tenantId,  deptIds);
                    user = adminUserApi.getPersonal(userId).getData();

                }


                if(user==null){
                    continue;
                }


                updateObj.setUserId(user.getId());

                if(updateObj.getPersonnalStatus()==null){
                    updateObj.setPersonnalStatus(1);
                }

                //设置账号状态
                if(updateObj.getPersonnalStatus() == 1 || updateObj.getPersonnalStatus() == 3){
                    user.setStatus(0);
                }else {
                    user.setStatus(1);
                }
                if(updateObj.getPersonnalStatus() == 2 || updateObj.getPersonnalStatus() == 3){
                    //判断是否有待处理事项
                    Long total = bpmProcessInstanceApi.getTodoTaskTotal(existMobile.getUserId());
                    //中心工作未完成
                    Long zxTotal = bpmProcessInstanceApi.getTaskTotal(existMobile.getUserId());
                    //判断是否有待处理事项
                    if (total > 0 || zxTotal >0 ) {
                        respVO.getFailurePersonnalNames().put(importPersonnal.getName(), USER_EXIST_UNFINISHED_TASK.getMsg());
                        continue;
                    }
                }
                //分配角色组
                createAssignUserRoleGroup(existMobile.getUserId(), updateObj.getPersonnalStatus(),user.getMobile());

                user.setMobile(updateObj.getMobile());
                user.setDeptId(updateObj.getDepartment());
                user.setNickname(updateObj.getName());
                CommonResult<Boolean> result = adminUserApi.updatePersonal(user);
                if (!CollUtil.isEmpty(deptIds)) {
                    List<Long> dptIds = adminUserApi.getDeptList(user.getId()).getCheckedData();
                    adminUserApi.updateUserDeptBatch(user.getId(), deptIds);
                    //判断是否部门是否更改reqVO.getDepartment()
                    if(!dptIds.equals(deptIds)){
                        Long mid = messageApi.getMessage(user.getId());
                        //消息权限存在的话更改状态为禁用
                        if(mid!=null){
                            MessageAuthorityUpdateReqDTO messageAuthorityUpdateReqDTO = new MessageAuthorityUpdateReqDTO();
                            messageAuthorityUpdateReqDTO.setId(mid);
                            messageAuthorityUpdateReqDTO.setStatus(1);
                            messageApi.updateMessage(messageAuthorityUpdateReqDTO);
                        }
                    }
                }
                Integer code = result.getCode();
                String msg = result.getMsg();
                if(0!=code){
                    throw exception(new ErrorCode(code, "第"+ (i + 1) +"行导入数据的"+msg));
                }

                personnalMapper.updateById(updateObj);

                Long personnalId = existMobile.getId();

//                PersonnalPartyDO updatePartyObj = PersonnalPartyConvert.INSTANCE.convert2(importPersonnal);
//                updatePartyObj.setPersonnalId(personnalId);
//                personnalPartyMapper.updateByPersonnalId(updatePartyObj);

                PersonnalStudyDO updateStudyObj = PersonnalStudyConvert.INSTANCE.convert2(importPersonnal);
                updateStudyObj.setPersonnalId(personnalId);
                LambdaUpdateWrapper<PersonnalStudyDO> lambdaUpdateWrapper = new LambdaUpdateWrapper();
                lambdaUpdateWrapper.eq(PersonnalStudyDO::getPersonnalId,updateStudyObj.getPersonnalId());
                lambdaUpdateWrapper.set(PersonnalStudyDO::getEducation,updateStudyObj.getEducation())
                        .set(PersonnalStudyDO::getAdmissionDate,updateStudyObj.getAdmissionDate())
                        .set(PersonnalStudyDO::getEndStudyDate,updateStudyObj.getEndStudyDate());
                lambdaUpdateWrapper.set(PersonnalStudyDO::getGraduationSchool,updateStudyObj.getGraduationSchool());
                lambdaUpdateWrapper.set(PersonnalStudyDO::getAcademicDegree,updateStudyObj.getAcademicDegree());
                personnalStudyMapper.updateByPersonnalId(updateStudyObj);
                personnalStudyMapper.update(new PersonnalStudyDO(),lambdaUpdateWrapper);

                PersonnalPositionDO updatePositionObj = PersonnalPositionConvert.INSTANCE.convert2(importPersonnal);
                updatePositionObj.setPersonnalId(personnalId);

                personnalPositionMapper.updateByPersonnalId(updatePositionObj);
                LambdaUpdateWrapper<PersonnalPositionDO> lambdaUpdateWrapper1 = new LambdaUpdateWrapper();
                lambdaUpdateWrapper1.eq(PersonnalPositionDO::getPersonnalId,updatePositionObj.getPersonnalId())
                        .set(PersonnalPositionDO::getRank,updatePositionObj.getRank())
                        .set(PersonnalPositionDO::getAdministrativePositionRank,updatePositionObj.getAdministrativePositionRank())
                        .set(PersonnalPositionDO::getProfessionalTechnicalName,updatePositionObj.getProfessionalTechnicalName())
                        .set(PersonnalPositionDO::getProfessionalTechnicalRank,updatePositionObj.getProfessionalTechnicalRank())
                        .set(PersonnalPositionDO::getJobContinuationTime,updatePositionObj.getJobContinuationTime())
                        .set(PersonnalPositionDO::getJobSelectionTime,updatePositionObj.getJobSelectionTime())
                        .set(PersonnalPositionDO::getCurrentEmploymentTime,updatePositionObj.getCurrentEmploymentTime());
                personnalPositionMapper.update(new PersonnalPositionDO(),lambdaUpdateWrapper1);

                List<PersonnalWorkExcelVO> workList = getMyWorkList(importPersonnal);
                List<PersonnalWorkDO> updateWorkObjs = PersonnalWorkConvert.INSTANCE.convertList(workList);
                List<PersonnalWorkDO> exitWorks = personnalWorkMapper.selectByPersonnalId(personnalId);
                if(exitWorks.size()>0){
                    exitWorks.forEach(exitWork->{
                        personnalWorkMapper.deleteById(exitWork);
                    });
                }
                updateWorkObjs.forEach(updateWorkObj -> {
                    updateWorkObj.setPersonnalId(personnalId);
                });
                personnalWorkMapper.insertBatch(updateWorkObjs);
                respVO.getUpdatePersonnalNames().add(importPersonnal.getName());

                KafkaPersonDTO kafkaPersonDTO = PersonnalConvert.INSTANCE.convertToKafkaPerson3(importPersonnal);
                kafkaPersonDTO.setDepartment(deptIds.get(0));
                kafkaPersonDTO.setUserId(updateObj.getUserId());

                kafkaPersonDTO.setTenantId(tenantId);
                kafkaPersonDTO.setTenantCode(tenantApi.getTenantCodeById(tenantId).getCheckedData());

                if("0".equals(importPersonnal.getSendkafka())){
                    //0的时候不发kafka
                }else{
                    kafkaUpdateList.add(kafkaPersonDTO);
                }


            }
        }
        personProducer.sendPersonListData(true,kafkaAddList);
        if(isUpdateSupport){
            personProducer.sendPersonListData(false,kafkaUpdateList);
        }

        return respVO;
    }

    private Long createPersonnalUser(PersonnalBasicDO basic, PersonnalStudyDO studyDO, PersonnalPositionDO positionDO, Long tenantId, List<Long> deptIds) {

        AdminUserReqDTO user = new AdminUserReqDTO();
        user.setMobile(basic.getMobile());
        user.setPassword(SM2Utils.USER_PASSWORD);
        user.setNickname(basic.getName());
        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
        user.setSort(0);
        user.setDeptId(basic.getDepartment());
        user.setUsername(basic.getMobile());
        Set<Long> postIds = new LinkedHashSet<>();
        //职级
        Integer rank = positionDO.getRank();
        if(Objects.equals(rank,1)){
            Long postId = postApi.getPostByCode(INSPECTOR_1.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }else if(Objects.equals(rank,2)){
            Long postId = postApi.getPostByCode(INSPECTOR_2.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }else if(Objects.equals(rank,3) || Objects.equals(rank,4)
                || Objects.equals(rank,5) || Objects.equals(rank,6)){
            Long postId = postApi.getPostByCode(INVESTIGATOR.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }
        //职务名称
        String positionName = positionDO.getAdministrativePositionName();
        if(Objects.equals(positionName,"副校（院）长") || Objects.equals(positionName,"教育长")
                || Objects.equals(positionName,"韶山干部学院院长")){
            Long postId = postApi.getPostByCode(SCHOOL_LEADER.getPostCode(), tenantId).getCheckedData().getId();
            Long postId1 = postApi.getPostByCode(PROVINCE_LEADER.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
            postIds.add(postId1);
        }else if(Objects.equals(positionName,"分管日常工作的副校长（副院长）")){
            Long postId = postApi.getPostByCode(VICE_CHANCELLOR.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }else if(Objects.equals(positionName,"主任") || Objects.equals(positionName,"院长")
                || Objects.equals(positionName,"主任（校长）")){
            Long postId = postApi.getPostByCode(DIRECTOR.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }else if(Objects.equals(positionName,"副主任") || Objects.equals(positionName,"副院长")
                || Objects.equals(positionName,"副主任（副校长）") || Objects.equals(positionName,"副馆长")){
            Long postId = postApi.getPostByCode(VICE_DIRECTOR.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }
        //职务级别
        Integer positionRank = positionDO.getAdministrativePositionRank();
        if(Objects.equals(positionRank,1) || Objects.equals(positionRank,2)
                ||Objects.equals(positionRank,5) || Objects.equals(positionRank,6)) {
            Long postId = postApi.getPostByCode(PROVINCE_LEADER.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(postId);
        }
        if(CollUtil.isEmpty(postIds)){
            Long clerkPostId = postApi.getPostByCode(CLERK.getPostCode(), tenantId).getCheckedData().getId();
            postIds.add(clerkPostId);
        }
        user.setPostIds(postIds);



        CommonResult<Long> result = adminUserApi.createPersonal(user);
        //增加多部门信息
        if(deptIds != null) {
            adminUserApi.createUserDeptBatch(result.getCheckedData(), deptIds);
        } else {
            UserDeptDTO userDept = new UserDeptDTO();
            userDept.setDeptId(result.getCheckedData());
            userDept.setUserId(user.getDeptId());
            adminUserApi.createUserDept(userDept);
        }
        Long userId= result.getData();
        Integer code = result.getCode();
        String msg = result.getMsg();
        if(0!=code){
            throw exception(new ErrorCode(code, msg));
        }
        basic.setUserId(userId);
        // 插入关联岗位
        if (CollectionUtil.isNotEmpty(user.getPostIds())) {
            postApi.saveUserPost(userId, user.getPostIds());
        }
        Long applicant = roleApi.getRoleIdByName("应聘者").getCheckedData();
//            分配角色权限
        Set<Long> roleIds = new HashSet<>();
        roleIds.add(roleApi.getInnerRoleIdByCode("DX-Homepage").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("DX-Teacher").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("jiaowu-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("teacher-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("yikatongzhuye-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("zhaopiancaiji-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("yikatong-role").getCheckedData());
        roleIds.add(roleApi.getInnerRoleIdByCode("staff-role").getCheckedData());
        roleIds.add(88L);
        roleIds.add(applicant);
        roleApi.createRole(userId, roleIds);

        //分配角色组
        if(basic.getPersonnalStatus()!=null){
            createAssignUserRoleGroup(userId,basic.getPersonnalStatus(),basic.getMobile());
        }

        return userId;
    }


    @Resource
    private TenantApi tenantApi;

    private List<PersonnalWorkExcelVO> getMyWorkList(PersonnalImportExcelVO importPersonnal){

        List<PersonnalWorkExcelVO> personnalWorkExcelList = new ArrayList<>();
        PersonnalWorkExcelVO personnalWorkExcelVO = new PersonnalWorkExcelVO();
        personnalWorkExcelVO.setWorkUnit(importPersonnal.getWorkUnit1());
        personnalWorkExcelVO.setStartTime(importPersonnal.getStartTime1());
        personnalWorkExcelVO.setEndTime(importPersonnal.getEndTime1());
        personnalWorkExcelVO.setPosition(importPersonnal.getPosition1());
        personnalWorkExcelVO.setWorkContent(importPersonnal.getWorkContent1());
        if(personnalWorkExcelVO.getWorkUnit() != null && !"".equals(personnalWorkExcelVO.getWorkUnit())){
            personnalWorkExcelList.add(personnalWorkExcelVO);
        }
        PersonnalWorkExcelVO personnalWorkExcelVO1 = new PersonnalWorkExcelVO();
        personnalWorkExcelVO1.setWorkUnit(importPersonnal.getWorkUnit2());
        personnalWorkExcelVO1.setStartTime(importPersonnal.getStartTime2());
        personnalWorkExcelVO1.setEndTime(importPersonnal.getEndTime2());
        personnalWorkExcelVO1.setPosition(importPersonnal.getPosition2());
        personnalWorkExcelVO1.setWorkContent(importPersonnal.getWorkContent2());
        if(personnalWorkExcelVO1.getWorkUnit() != null && !"".equals(personnalWorkExcelVO1.getWorkUnit())){
            personnalWorkExcelList.add(personnalWorkExcelVO1);
        }
        PersonnalWorkExcelVO personnalWorkExcelVO2 = new PersonnalWorkExcelVO();
        personnalWorkExcelVO2.setWorkUnit(importPersonnal.getWorkUnit3());
        personnalWorkExcelVO2.setStartTime(importPersonnal.getStartTime3());
        personnalWorkExcelVO2.setEndTime(importPersonnal.getEndTime3());
        personnalWorkExcelVO2.setPosition(importPersonnal.getPosition3());
        personnalWorkExcelVO2.setWorkContent(importPersonnal.getWorkContent3());
        if(personnalWorkExcelVO2.getWorkUnit() != null && !"".equals(personnalWorkExcelVO2.getWorkUnit())){
            personnalWorkExcelList.add(personnalWorkExcelVO2);
        }
        return personnalWorkExcelList;

    }
    private void validCreateOrUpdate(Long id, PersonnalBasicVO createReqVO) {
//        checkName(id, createReqVO.getName());
        checkMobileUnique(id, createReqVO.getMobile());
    }
    private void validCreateOrUpdateEntry(Long id, PersonnalBasicEntryVO createReqVO) {
//        checkName(id, createReqVO.getName());
        checkMobileUnique(id, createReqVO.getMobile());
    }
    private void checkName(Long id, String Name) {
        if (StrUtil.isBlank(Name)) {
            return;
        }
        PersonnalBasicDO basic = personnalMapper.selectByname(Name);
        if (Objects.isNull(basic)) {
            return;
        }
        if (Objects.isNull(id) || !Objects.equals(basic.getId(), id)) {
            throw exception(PERSONNAL_NAME_EXISTS);
        }
    }
    private void checkMobileUnique(Long id, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        PersonnalBasicDO basic = personnalMapper.selectByMobile(mobile);
        if (basic == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_MOBILE_EXISTS);
        }
        if (!basic.getId().equals(id)) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }
    private String generateWorkId() {
        DateFormat df = new SimpleDateFormat("yyyy");
        String year = df.format(new Date());
        String maxNumber = personnalMapper.selectMaxWorkId();
        if(maxNumber==null){
            maxNumber = "0000";
        }
        int num = Integer.parseInt(maxNumber) + 1;
        String number = String.format("%05d", num); // 生成编号部分
        return year + number;
    }
    private String getAge(LocalDateTime date) {
        LocalDate birthDate = date.toLocalDate();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算年龄
        Period period = Period.between(birthDate, currentDate);
        return Integer.toString(period.getYears());
    }

    /**
     * 获得退休人事信息分页
     * @param pageReqVO 分页查询
     * @return 退休人事信息分页
     */
    @Override
    public PageResult<PersonalRetirePageRespVO> getRetirePage(PersonalRetirePageReqVO pageReqVO) {
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        List<PersonalRetirePageRespVO> data = personnalMapper.selectRetirePage(page, pageReqVO);
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        for (PersonalRetirePageRespVO person : data) {
            if(person.getUserId() != null) {
                List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(dptIds)) {
                    if (person.getDepartment() != null) {
                        List<Long> depts = new ArrayList<>();
                        depts.add(person.getDeptId());
                        person.setDeptIds(depts);
                    }
                } else {
                    person.setDeptIds(dptIds);
                }
            }
        }
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public void updatePersonalRetire(PersonalRetireUpdateVO reqVO) {
        // 校验id的正确性
        PersonnalBasicDO personnalBasicDO =
                personnalMapper.selectOne(new LambdaQueryWrapper<PersonnalBasicDO>()
                        .eq(PersonnalBasicDO::getId, reqVO.getId()));
        if (personnalBasicDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        AdminUserRespDTO  user = adminUserApi.getPersonal(personnalMapper.getUserIdById(personnalBasicDO.getId())).getData();
        Long total = bpmProcessInstanceApi.getTodoTaskTotal(personnalBasicDO.getUserId());
        //中心工作未完成
        Long zxTotal = bpmProcessInstanceApi.getTaskTotal(personnalBasicDO.getUserId());
        //判断是否有待处理事项
        if (total > 0 || zxTotal >0 ) {
            throw exception(USER_EXIST_UNFINISHED_TASK);
        }
        //分配离校角色组
        permissionApi.assignUserRoleGroup(personnalBasicDO.getUserId(),"党校退休人员");
        //退休还要用系统所以注释
        //user.setStatus(1);
        adminUserApi.updatePersonal(user);
        //执行更新操作
        LambdaUpdateWrapper<PersonnalBasicDO> wrapper=new LambdaUpdateWrapper<>();
        wrapper.set(reqVO.getRetireTime()!=null,PersonnalBasicDO::getRetireTime, reqVO.getRetireTime())
                .set(reqVO.getRetireRemarks()!=null,PersonnalBasicDO::getRetireRemarks, reqVO.getRetireRemarks())
                .set(PersonnalBasicDO::getPersonnalStatus,3)
                .eq(PersonnalBasicDO::getId, reqVO.getId());
        update(new PersonnalBasicDO(), wrapper);

    }

    @Override
    public List<PersonalRetireExcelVO> getPersonalRetireExcel(PersonalRetireExcelReqVO reqVO){
        List<PersonalRetireExcelVO> personalRetireExcelVOS = personnalMapper.selectRetireList(reqVO);
        for (PersonalRetireExcelVO personnalRetireExcelVO : personalRetireExcelVOS) {
            Long userId = personnalRetireExcelVO.getUserId();
            List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
            if(!CollUtil.isEmpty(deptIds)) {
                List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                personnalRetireExcelVO.setDepartment(departments);
            }
        }
        return personalRetireExcelVOS;
    }

    /**
     * 获得离校人事信息分页
     * @param pageReqVO 分页查询
     * @return 退休人事信息分页
     */
    @Override
    public PageResult<PersonalLeavePageRespVO> getLeavePage(PersonalLeavePageReqVO pageReqVO) {
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        List<PersonalLeavePageRespVO> data = personnalMapper.selectLeavePage(page, pageReqVO);
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        for (PersonalLeavePageRespVO person : data) {
            if(person.getUserId() != null) {
                List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(dptIds)) {
                    if (person.getDepartment() != null) {
                        List<Long> depts = new ArrayList<>();
                        depts.add(person.getDeptId());
                        person.setDeptIds(depts);
                    }
                } else {
                    person.setDeptIds(dptIds);
                }
            }
        }
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public void updatePersonalLeave(PersonalLeaveUpdateVO reqVO) {
        // 校验id的正确性
        PersonnalBasicDO personnalBasicDO =
                personnalMapper.selectOne(new LambdaQueryWrapper<PersonnalBasicDO>()
                        .eq(PersonnalBasicDO::getId, reqVO.getId()));
        if (personnalBasicDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        //oa未完成
        Long total = bpmProcessInstanceApi.getTodoTaskTotal(personnalBasicDO.getUserId());
        //中心工作未完成
        Long zxTotal = bpmProcessInstanceApi.getTaskTotal(personnalBasicDO.getUserId());
        //判断是否有待处理事项
        if (total > 0 || zxTotal > 0) {
            throw exception(USER_EXIST_UNFINISHED_TASK);
        }
        //分配离校角色组(禁用账号不用分配离校人员)
//        permissionApi.assignUserRoleGroup(personnalBasicDO.getUserId(),"离校人员");

        AdminUserRespDTO  user = adminUserApi.getPersonal(personnalMapper.getUserIdById(personnalBasicDO.getId())).getData();
        user.setStatus(1);
        adminUserApi.updatePersonal(user);
        //执行更新操作
        LambdaUpdateWrapper<PersonnalBasicDO> wrapper=new LambdaUpdateWrapper<>();
        wrapper.set(reqVO.getLeaveTime()!=null,PersonnalBasicDO::getLeaveTime, reqVO.getLeaveTime())
                .set(reqVO.getLeaveChannel()!=null,PersonnalBasicDO::getLeaveChannel, reqVO.getLeaveChannel())
                .set(reqVO.getLeaveDestination()!=null,PersonnalBasicDO::getLeaveDestination, reqVO.getLeaveDestination())
                .set(reqVO.getLeaveReason()!=null,PersonnalBasicDO::getLeaveReason, reqVO.getLeaveReason())
                .set(reqVO.getLeaveRemarks()!=null,PersonnalBasicDO::getLeaveRemarks, reqVO.getLeaveRemarks())
                .set(PersonnalBasicDO::getPersonnalStatus,2)
                .eq(PersonnalBasicDO::getId, reqVO.getId());
        update(new PersonnalBasicDO(), wrapper);

    }

    @Override
    public List<PersonalLeaveExcelVO> getPersonalLeaveExcel(PersonalLeaveExcelReqVO reqVO){
        List<PersonalLeaveExcelVO> personalLeaveExcelVOS = personnalMapper.selectLeaveList(reqVO);
        for (PersonalLeaveExcelVO personnalLeaveExcelVO : personalLeaveExcelVOS) {
            Long userId = personnalLeaveExcelVO.getUserId();
            List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
            if(!CollUtil.isEmpty(deptIds)) {
                List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                personnalLeaveExcelVO.setDepartment(departments);
            }
        }
        return personalLeaveExcelVOS;
    }

    /**
     * 获得去世人事信息分页
     * @param pageReqVO 分页查询
     * @return 退休人事信息分页
     */
    @Override
    public PageResult<PersonalDeathPageRespVO> getDeathPage(PersonalDeathPageReqVO pageReqVO) {
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        List<PersonalDeathPageRespVO> data = personnalMapper.selectDeathPage(page, pageReqVO);
        List<UserDeptDTO> userDept = adminUserApi.selectUserDeptList().getCheckedData();
        for (PersonalDeathPageRespVO person : data) {
            if(person.getUserId() != null) {
                List<Long> dptIds = userDept.stream().filter(i -> Objects.equals(i.getUserId(), Long.valueOf(person.getUserId()))).map(UserDeptDTO::getDeptId).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(dptIds)) {
                    if (person.getDepartment() != null) {
                        List<Long> depts = new ArrayList<>();
                        depts.add(person.getDeptId());
                        person.setDeptIds(depts);
                    }
                } else {
                    person.setDeptIds(dptIds);
                }
            }
        }
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public void updatePersonalDeath(PersonalDeathUpdateVO reqVO) {
        // 校验id的正确性
        PersonnalBasicDO personnalBasicDO =
                personnalMapper.selectOne(new LambdaQueryWrapper<PersonnalBasicDO>()
                        .eq(PersonnalBasicDO::getId, reqVO.getId()));
        if (personnalBasicDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        //去世人员不需要判断
//        Long total = bpmProcessInstanceApi.getTodoTaskTotal(personnalBasicDO.getUserId());
//        //中心工作未完成
//        Long zxTotal = bpmProcessInstanceApi.getTaskTotal(personnalBasicDO.getUserId());
//        //判断是否有待处理事项
//        if (total > 0 || zxTotal >0 ) {
//            throw exception(USER_EXIST_UNFINISHED_TASK);
//        }
//        //分配离校角色组
//        permissionApi.assignUserRoleGroup(personnalBasicDO.getUserId(),"离校人员");
        AdminUserRespDTO  user = adminUserApi.getPersonal(personnalMapper.getUserIdById(personnalBasicDO.getId())).getData();
        user.setStatus(1);
        adminUserApi.updatePersonal(user);
        //执行更新操作
        LambdaUpdateWrapper<PersonnalBasicDO> wrapper=new LambdaUpdateWrapper<>();
        wrapper.set(reqVO.getDeathTime()!=null,PersonnalBasicDO::getDeathTime, reqVO.getDeathTime())
                .set(reqVO.getDeathStatus()!=null,PersonnalBasicDO::getDeathStatus, reqVO.getDeathStatus())
                .set(reqVO.getDeathRemarks()!=null,PersonnalBasicDO::getDeathRemarks, reqVO.getDeathRemarks())
                .set(PersonnalBasicDO::getPersonnalStatus,4)
                .eq(PersonnalBasicDO::getId, reqVO.getId());
        update(new PersonnalBasicDO(), wrapper);

    }

    @Override
    public List<PersonalDeathExcelVO> getPersonalDeathExcel(PersonalDeathExcelReqVO reqVO){
        List<PersonalDeathExcelVO> personalDeathExcelVOS = personnalMapper.selectDeathList(reqVO);
        for (PersonalDeathExcelVO personnalDeathExcelVO : personalDeathExcelVOS) {
            Long userId = personnalDeathExcelVO.getUserId();
            List<Long> deptIds = adminUserApi.getDeptList(userId).getCheckedData();
            if(!CollUtil.isEmpty(deptIds)) {
                List<DeptRespDTO> depts = deptApi.getDepts(deptIds).getCheckedData();
                String departments = depts.stream().map(DeptRespDTO::getName).collect(Collectors.joining(","));
                personnalDeathExcelVO.setDepartment(departments);
            }
        }
        return personalDeathExcelVOS;
    }

    @Override
    public void sendAllPersonInfo(String url) {
        List<PersonDTO> list = personnalMapper.selectSimplePersonInfoList(null);
        log.info("打印全量发送的教职工数据-------"+JSONUtil.toJsonStr(list));
        String resp = HttpUtil.post(url+"/system/yzAddition/userAdmin", JSONUtil.toJsonStr(list));
        log.info("全量发送教职工数据完毕-------"+resp);
    }

    public void deletePersonnal(Long userId) {
        LambdaQueryWrapperX<PersonnalBasicDO> wrapperX = new LambdaQueryWrapperX<>();

        wrapperX.eq(PersonnalBasicDO::getUserId, userId);

        personnalMapper.delete(wrapperX);

    }
    //变更分配角色组
    private void updateAssignUserRoleGroup(Long userId,Integer status){
        //在职
        if(status == 1){
            permissionApi.assignUserRoleGroup(userId,"党校教职工");
        }else if(status == 3){
            Long total = bpmProcessInstanceApi.getTodoTaskTotal(userId);
            //中心工作未完成
            Long zxTotal = bpmProcessInstanceApi.getTaskTotal(userId);
            //判断是否有待处理事项
            if (total > 0 || zxTotal >0 ) {
                throw exception(USER_EXIST_UNFINISHED_TASK);
            }
            permissionApi.assignUserRoleGroup(userId,"党校退休人员");
        }else if(status == 2){
            Long total = bpmProcessInstanceApi.getTodoTaskTotal(userId);
            //中心工作未完成
            Long zxTotal = bpmProcessInstanceApi.getTaskTotal(userId);
            //判断是否有待处理事项
            if (total > 0 || zxTotal >0 ) {
                throw exception(USER_EXIST_UNFINISHED_TASK);
            }
        }
    }
    //创建分配角色组
    private void createAssignUserRoleGroup(Long userId,Integer status,String mobile){
        //在职
        if(status == 1){
            permissionApi.assignUserRoleGroup(userId,"党校教职工");
        }else if(status == 3){

            permissionApi.assignUserRoleGroup(userId,"党校退休人员");
        }
        List<String> mobiles = adminUserApi.getDangJianMobile().getCheckedData();
        if(mobiles.contains(mobile)){
            //党员角色
            Set<Long> dangIds = new HashSet<>();
            dangIds.add(roleApi.getCustomRoleIdByCode("dangwu-role").getCheckedData());
            dangIds.add(roleApi.getCustomRoleIdByCode("dangyuan-role").getCheckedData());
            dangIds.add(roleApi.getCustomRoleIdByCode("dangjian").getCheckedData());
            roleApi.createRole(userId, dangIds);
        }
    }
}
