package com.unicom.swdx.module.hr.dal.dataobject.personnal;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *  DO
 *
 * <AUTHOR>
 */
@TableName(value = "hr_personnel_party_member_file", autoResultMap = true)
@KeySequence("hr_personnel_party_member_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnalPartyDO extends BaseDO {

    /**
     * 人事ID
     */
    @TableField(value = "personnel_id")
    private Long personnalId;
    /**
     * 机构ID
     */
    private Long tenantId;
    /**
     * 主键自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 入党时间
     */
    private LocalDateTime entryPartyTime;
    /**
     * 党员转正时间
     */

    private LocalDateTime admissionPartyTime;
    /**
     * 所属党支部
     */
    private Integer affiliatedParty;
    /**
     * 上级党组织
     */

    private Integer higherPartyOrganizations;
    /**
     * 入党介绍人
     *
     */
    private String partyRecommendPerson;
    /**
     * 获得奖励情况
     */
    private String awardSituation;
    /**
     * 获得处分情况
     */
    private String punishSituation;


}
