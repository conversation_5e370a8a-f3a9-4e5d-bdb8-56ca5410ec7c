package com.unicom.swdx.module.hr.service.personnal;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalCreateRegistrationVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyGetVO;
import com.unicom.swdx.module.hr.controller.admin.personnal.vo.PersonnalStudyVO;
import com.unicom.swdx.module.hr.dal.dataobject.personnal.PersonnalStudyDO;

import javax.validation.Valid;

/**
 * 人事学习经历Service 接口
 *
 */
public interface PersonnalStudyService extends IService<PersonnalStudyDO> {


    /**
     * 新增人事信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createPersonnal(@Valid PersonnalStudyVO createReqVO, Long personnalId);
    void createPersonnalRegis(@Valid PersonnalCreateRegistrationVO createReqVO, Long personnalId);
    PersonnalStudyDO getPersonnal(Long id);
    void  updatePersonnal(PersonnalStudyGetVO updateReqVO);
}
