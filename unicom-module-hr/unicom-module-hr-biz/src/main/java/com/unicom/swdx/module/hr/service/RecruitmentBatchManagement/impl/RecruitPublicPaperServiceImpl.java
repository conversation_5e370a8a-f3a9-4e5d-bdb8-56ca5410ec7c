package com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.entity.RecruitPublicPaper;
import com.unicom.swdx.module.hr.controller.admin.recruitmentmanagement.vo.RecruitPublicPaperVO;
import com.unicom.swdx.module.hr.dal.mysql.personnal.RecruitmentBatchManagement.RecruitPublicPaperMapper;
import com.unicom.swdx.module.hr.service.RecruitmentBatchManagement.RecruitPublicPaperService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * (HrRecruitmentResumeSelection)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 19:36:14
 */

@Service
@Validated
@Slf4j
public class RecruitPublicPaperServiceImpl extends ServiceImpl<RecruitPublicPaperMapper, RecruitPublicPaper> implements RecruitPublicPaperService {

    @Resource
    RecruitPublicPaperMapper recruitPublicPaperMapper ;


    @Override
    public PageResult<RecruitPublicPaper> queryByList(RecruitPublicPaperVO RecruitPublicPaperVO) {

        IPage<RecruitPublicPaper> page = MyBatisUtils.buildPage(RecruitPublicPaperVO);
        List<RecruitPublicPaper> data = recruitPublicPaperMapper.queryByList(page, RecruitPublicPaperVO);

        return new PageResult<>(data, page.getTotal());
    }
}
