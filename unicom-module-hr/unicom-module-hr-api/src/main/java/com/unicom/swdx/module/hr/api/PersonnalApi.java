package com.unicom.swdx.module.hr.api;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.hr.api.dto.PersonnalApiDO;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.hr.api.dto.PersonnalUpdateReqDTO;
import com.unicom.swdx.module.hr.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 人事")
public interface PersonnalApi {


    String PREFIX = ApiConstants.PREFIX + "/person";

    @PostMapping(PREFIX + "/createUser")
    PersonnalApiDO createUser(@RequestBody PersonnalBasicVO createReqVO );

    @GetMapping(PREFIX + "/getUser")
    PersonnalApiDO getUser(@RequestParam("processInstanceId") Long id);

    @PostMapping(PREFIX + "/updateUser")
    CommonResult<Boolean> updateUser(@RequestBody PersonnalUpdateReqDTO reqDTO);

    @PostMapping(PREFIX + "/deleteUser")
    CommonResult<Boolean> deleteUser(@RequestParam("userId") Long userId);


    @PostMapping(PREFIX + "/updateUserProfile")
    CommonResult<Boolean> updateUserProfile(@RequestParam("userId") Long userId,@RequestParam("sex") Integer sex,
                                            @RequestParam("email") String email);

    @PostMapping(PREFIX + "/updateUserAvatar")
    CommonResult<Boolean> updateUserAvatar(@RequestParam("userId") Long userId,@RequestParam("avatar") String avatar);

    @GetMapping(PREFIX + "/getHrUserByMobileorName")
    PersonnalApiDO getHrUserByMobileorName(@RequestParam("mobile") String mobile  ,@RequestParam("name") String name );

    @GetMapping(PREFIX + "/sendAllPersonInfo")
    void sendAllPersonInfo(@RequestParam("url")  String url);

}
