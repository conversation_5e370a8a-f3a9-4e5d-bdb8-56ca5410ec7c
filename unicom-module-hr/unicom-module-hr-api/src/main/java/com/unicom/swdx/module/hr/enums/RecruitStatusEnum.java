package com.unicom.swdx.module.hr.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应聘职位的状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RecruitStatusEnum {
    RUNNING(1, "待审核"),
    UNAPPROVED(2, "不通过"),
    INTERVIEW(3,"待面试审核"),
    INTERVIEW_REJECT(4,"面试审核不通过"),
    PROPOSED_RECRUITMENT(5, "拟录用"),
    DRAFT(6,"草稿"),
    BACK(7,"退回修改"),
    REAPPLY(8,"退回修改重新申请"),
    REAPPLYDRAFT(9,"退回修改存草稿");



    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;
}
