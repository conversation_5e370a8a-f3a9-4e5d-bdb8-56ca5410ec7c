package com.unicom.swdx.module.hr.enums;

import com.unicom.swdx.framework.common.exception.ErrorCode;

/**
 * hr 错误码枚举类
 *
 * hr 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {
    ErrorCode PERSONNAL_NAME_EXISTS = new ErrorCode(1003000000, "人事信息已经存在");
    ErrorCode PERSONNAL_IMPORT_OUT_MEMORY = new ErrorCode(1003000001, "导入文件不能超过16M");

    ErrorCode PERSONNAL_IMPORT_LIST_IS_EMPTY = new ErrorCode(1003000002, "导入用户数据不能为空");
    ErrorCode PERSONNAL_EXISTS = new ErrorCode(1002003000, "人事信息已经存在");

    ErrorCode PERSONNAL_IMPORTING = new ErrorCode(1003000003, "人事信息正在导入，请稍后再试");
}
