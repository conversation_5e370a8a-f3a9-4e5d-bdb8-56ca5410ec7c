package com.unicom.swdx.framework.banner.config;

import com.unicom.swdx.framework.banner.core.BannerApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Banner 的自动配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class UnicomBannerAutoConfiguration {

    @Bean
    public BannerApplicationRunner bannerApplicationRunner() {
        return new BannerApplicationRunner();
    }

}
