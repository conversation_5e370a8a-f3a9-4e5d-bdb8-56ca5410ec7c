package com.unicom.swdx.framework.social.config;

import com.unicom.swdx.framework.social.core.UnicomAuthRequestFactory;
import com.xkcoding.http.HttpUtil;
import com.xkcoding.http.support.hutool.HutoolImpl;
import com.xkcoding.justauth.autoconfigure.JustAuthProperties;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 社交自动装配类
 *
 * <AUTHOR>
 * @date 2021-10-30
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(JustAuthProperties.class)
@Slf4j
public class UnicomSocialAutoConfiguration {

    @Bean
    @ConditionalOnProperty(prefix = "justauth", value = "enabled", havingValue = "true", matchIfMissing = true)
    public UnicomAuthRequestFactory unicomAuthRequestFactory(JustAuthProperties properties, AuthStateCache authStateCache) {
        // 需要修改 HttpUtil 使用的实现，避免类报错
        HttpUtil.setHttp(new HutoolImpl());
        // 创建 UnicomAuthRequestFactory
        return new UnicomAuthRequestFactory(properties, authStateCache);
    }

}
