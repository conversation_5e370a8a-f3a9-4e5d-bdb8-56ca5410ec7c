package com.unicom.swdx.framework.jackson.core.databind;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/9/20 09:23
 **/
public class SensitiveMobileSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(StrUtil.isBlank(value) ? "" : DesensitizedUtil.mobilePhone(value));
    }

}
