package com.unicom.swdx.framework.jackson.core.databind;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.unicom.swdx.framework.common.util.crypt.sm2.SM2Utils;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/7/17 17:01
 **/
public class SMEncryptSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(SM2Utils.encrypt(value));
    }
}
