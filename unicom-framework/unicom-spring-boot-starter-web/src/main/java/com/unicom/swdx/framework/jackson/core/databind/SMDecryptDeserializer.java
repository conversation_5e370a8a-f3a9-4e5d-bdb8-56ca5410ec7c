package com.unicom.swdx.framework.jackson.core.databind;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.unicom.swdx.framework.common.util.crypt.sm2.SM2Utils;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:10
 **/
public class SMDecryptDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        return SM2Utils.decrypt(p.getValueAsString());
    }
}
