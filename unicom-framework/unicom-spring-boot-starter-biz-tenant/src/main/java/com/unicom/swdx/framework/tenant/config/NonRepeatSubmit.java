package com.unicom.swdx.framework.tenant.config;

import java.lang.annotation.*;
/**
 * <AUTHOR> Kang
 * @ClassName NonRepeatSubmit
 * @Description 防止重复提交注解
 * @Date 2022/8/24 15:03
 * @Version 1.0
 * @Motto 让营地比你来时更干净
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface NonRepeatSubmit {
    /**
     * 支持两种防重复提交方式:
     *  1.方法参数
     *  2.令牌
     */
    enum Type {PARAM,TOKEN}

    /**
     * 默认防重复提交,是方法参数
     * @return
     */
    Type limitType() default Type.PARAM;

    /**
     * 加锁过期时间,默认是5s
     * @return
     */
    long lockTime() default 5;
}
