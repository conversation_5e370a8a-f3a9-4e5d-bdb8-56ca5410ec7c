package com.unicom.swdx.framework.tenant.config;

import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmitAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration(proxyBeanMethods = false)
public class UnicomNonRepeatConfiguration {

    @Bean
    public com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmitAspect nonRepeatSubmitAspect(StringRedisTemplate stringRedisTemplate) {
        return new NonRepeatSubmitAspect(stringRedisTemplate);
    }

}
