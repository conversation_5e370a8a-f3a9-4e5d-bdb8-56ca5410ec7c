package com.unicom.swdx.framework.tenant.core.db;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SelectItem;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class TenantDptLineInnerInterceptor extends TenantLineInnerInterceptor {


    private TenantLineHandler tenantLineHandler;

    public TenantDptLineInnerInterceptor(TenantLineHandler handler) {
        super(handler);
        tenantLineHandler = handler;
    }


    @Override
    protected void appendSelectItem(List<SelectItem> selectItems) {
        super.appendSelectItem(selectItems);
        log.info("租户管理 ：  查询前处理");

    }



    @Override
    protected Expression builderExpression(Expression currentExpression, List<Table> tables, String whereSegment) {
        // 没有表需要处理直接返回
        if (CollectionUtils.isEmpty(tables)) {
            return currentExpression;
        }
        // 构造每张表的条件
        List<Table> tempTables = tables.stream()
                .filter(x -> !tenantLineHandler.ignoreTable(x.getName()))
                .collect(Collectors.toList());

        // 没有表需要处理直接返回
        if (CollectionUtils.isEmpty(tempTables)) {
            return currentExpression;
        }

        Expression tenantId = tenantLineHandler.getTenantId();
        List<EqualsTo> equalsTos = tempTables.stream()
                .map(item -> new EqualsTo(getAliasColumn(item), tenantId))
                .collect(Collectors.toList());

        //写一个in的demo
        // 构造值列表
//        MultiExpressionList itemsList = new MultiExpressionList();
//        List<Expression> lists = new ArrayList<>();
//        lists.add(new LongValue("1"));
//        lists.add(new LongValue("2"));
//        lists.add(new LongValue("3"));
//        lists.add(new LongValue("4"));
//        itemsList.addExpressionList(lists);
//
//        List<InExpression> inTos = tempTables.stream()
//                .map(item -> new InExpression(getAliasColumn(item), itemsList))
//                .collect(Collectors.toList());

        // 注入的表达式
        Expression injectExpression = equalsTos.get(0);
        // 如果有多表，则用 and 连接
        if (equalsTos.size() > 1) {
            for (int i = 1; i < equalsTos.size(); i++) {
                injectExpression = new AndExpression(injectExpression, equalsTos.get(i));
            }
        }

        if (currentExpression == null) {
            return injectExpression;
        }
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), injectExpression);
        } else {
            return new AndExpression(currentExpression, injectExpression);
        }
    }
}
