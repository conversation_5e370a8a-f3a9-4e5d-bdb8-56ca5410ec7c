package com.unicom.swdx.framework.tenant.config;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR> Kang
 * @ClassName NonRepeatSubmitAspect
 * @Description 利用切面对使用自定义注解的地方防止重复提交
 * @Date 2022/8/24 15:44
 * @Version 1.0
 * @Motto 让营地比你来时更干净
 */
@Aspect
@Slf4j
@RequiredArgsConstructor
public class NonRepeatSubmitAspect {


    private final  StringRedisTemplate redisTemplate;

    private final String REDISKEY_REPEAT = "REPEAT:";

    /**
     * 环绕通知,围绕方法执行
     * 两种环绕方式:
     * 方式一：单用 @Around("execution(* com.kang.redis.controller.*.*(..))")可以
     * 方式二：用@Pointcut和@Around联合注解也可以(本地采用这个)
     * 防重复提交的两种方式
     * 方式一：加锁 固定时间内不能重复提交
     * 方式二：先请求获取token，这边再删除token,删除成功则是第一次提交
     * @param joinPoint
     * @param nonRepeatSubmit
     * @return
     */
    @Around("@annotation(nonRepeatSubmit)")
    public Object around(ProceedingJoinPoint joinPoint,NonRepeatSubmit nonRepeatSubmit){
        try {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if(servletRequestAttributes == null){
                throw new ConfirmTokenException("AOP拦截:无法获取当前请求request");
            }
            HttpServletRequest request =  servletRequestAttributes.getRequest();

            //一般都是从request中获取当前用户ID
            String userId = "0001";
            //用于记录成功还是失败
            boolean result = false;


            //防重复提交的类型
            String type = nonRepeatSubmit.limitType().name();
            //注解中有默认配置所以不用考虑isBlank的情况
            if(type.equals(NonRepeatSubmit.Type.PARAM.name())){
                //方式一，参数形式防重提交
                log.info("AOP拦截:采用参数形式防重复提交");
                String parameter =  getParam(joinPoint);

                log.info("AOP拦截:采用参数形式防重复提交 parameter = {}" ,parameter );
                boolean haskey = redisTemplate.hasKey(REDISKEY_REPEAT+ parameter);

                if(!haskey){
                    redisTemplate.opsForValue().set    (REDISKEY_REPEAT+ parameter , "" ,  nonRepeatSubmit.lockTime(), TimeUnit.MILLISECONDS);;
                    result =true;
                }


            } else {
                //方式二，令牌形式防重提交
                String token = request.getHeader("token");
                if(StrUtil.isBlank(token)){
                    throw new ConfirmTokenException("AOP拦截:token为空,非法请求");
                }
                String key = String.format("REPEAT_KEY",userId,token);
                /**
                 * 只有第一次提交时才会删除成功
                 * 方式一：不用lua脚本获取再判断，之前是因为 key组成是 order:submit:accountNo, value是对应的token，所以需要先获取值，再判断
                 * 方式二：可以直接key是 order:submit:accountNo:token,然后直接删除成功则完成
                 */
                result = redisTemplate.delete(key);
            }

            if(!result){
                log.error("AOP拦截:请求重复提交");
                log.info("AOP拦截:环绕该方法进行通知");
                throw new ConfirmTokenException("AOP拦截:请求重复提交");
            }
            log.info("AOP拦截:方法执行前");
            Object object = joinPoint.proceed();
            log.info("AOP拦截:方法执行后获得结果为:{}",object);
            return object;
        } catch (Throwable e) {
            log.error("AOP拦截:执行出错",e);
            return e.getMessage();
        }
    }


    //获取参数名和参数值
    public String getParam(ProceedingJoinPoint proceedingJoinPoint) {
        Map<String, Object> map = new HashMap<String, Object>();
        Object[] values = proceedingJoinPoint.getArgs();
        String[] names = ((CodeSignature) proceedingJoinPoint.getSignature()).getParameterNames();
        for (int i = 0; i < names.length; i++) {
            map.put(names[i], values[i]);
        }
        return JSONObject.toJSONString(map);
    }

}
