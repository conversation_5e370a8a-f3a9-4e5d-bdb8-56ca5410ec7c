package com.unicom.swdx.framework.tenant.config;

/**
 * <AUTHOR>
 * @ClassName ConfirmToeknException
 * @Description 自定义异常
 * @Date 2022/8/24 16:22
 * @Version 1.0
 * @Motto 让营地比你来时更干净
 */
public class ConfirmTokenException extends Exception{
    public ConfirmTokenException() {
        super();
    }

    public ConfirmTokenException(String message) {
        super(message);
    }

    public ConfirmTokenException(String message, Throwable cause) {
        super(message, cause);
    }

    public ConfirmTokenException(Throwable cause) {
        super(cause);
    }

    protected ConfirmTokenException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
