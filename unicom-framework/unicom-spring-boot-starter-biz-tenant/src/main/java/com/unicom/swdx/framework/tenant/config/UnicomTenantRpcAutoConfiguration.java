package com.unicom.swdx.framework.tenant.config;

import com.unicom.swdx.framework.tenant.core.rpc.TenantRequestInterceptor;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = "unicom.tenant", value = "enable", matchIfMissing = true) // 允许使用 unicom.tenant.enable=false 禁用多租户
@EnableFeignClients(clients = TenantApi.class) // 主要是引入相关的 API 服务
public class UnicomTenantRpcAutoConfiguration {

    @Bean
    public TenantRequestInterceptor tenantRequestInterceptor() {
        return new TenantRequestInterceptor();
    }

}
