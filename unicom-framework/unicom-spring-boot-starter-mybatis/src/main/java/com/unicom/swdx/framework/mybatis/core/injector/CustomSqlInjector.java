package com.unicom.swdx.framework.mybatis.core.injector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.unicom.swdx.framework.mybatis.core.injector.abstractmethod.DeleteAbsoluteBatchIds;
import com.unicom.swdx.framework.mybatis.core.injector.abstractmethod.DeleteAbsoluteById;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 自定义sql注入器
 * @date 2024-10-24
 */
public class CustomSqlInjector extends DefaultSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        methodList.add(new DeleteAbsoluteById());
        methodList.add(new DeleteAbsoluteBatchIds());
        return methodList;
    }

}
