package com.unicom.swdx.framework.mybatis.core.query;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.unicom.swdx.framework.common.util.collection.ArrayUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 拓展 MyBatis Plus QueryWrapper 类，主要增加如下功能：
 *
 * 1. 拼接条件的方法，增加 xxxIfPresent 方法，用于判断值不存在的时候，不要拼接到条件中。
 *
 * @param <T> 数据类型
 */
public class LambdaQueryWrapperX<T> extends LambdaQueryWrapper<T> {

    public LambdaQueryWrapperX<T> likeIfPresent(SFunction<T, ?> column, String val) {
        if (StringUtils.hasText(val)) {
            //修复模糊查询逃逸
            val = val.replace("%","\\%").replace("_","\\_");
            return (LambdaQueryWrapperX<T>) super.like(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> inIfPresent(SFunction<T, ?> column, Collection<?> values) {
        if (!CollectionUtils.isEmpty(values)) {
            return (LambdaQueryWrapperX<T>) super.in(column, values);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> inIfPresent(SFunction<T, ?> column, Object... values) {
        if (!ArrayUtil.isEmpty(values)) {
            return (LambdaQueryWrapperX<T>) super.in(column, values);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> eqIfPresent(SFunction<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val) ) {
            return (LambdaQueryWrapperX<T>) super.eq(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> eqStrIfPresent(SFunction<T, ?> column, String val) {
        if (StringUtils.hasText(val)) {
            return (LambdaQueryWrapperX<T>) super.eq(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> neIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            return (LambdaQueryWrapperX<T>) super.ne(column, val);
        }
        return this;
    }


    public LambdaQueryWrapperX<T> ne(boolean condition , SFunction<T, ?> column, Object val) {
        if (condition && val != null) {
            return (LambdaQueryWrapperX<T>) super.ne(condition ,column, val);
        }
        return this;
    }



    public LambdaQueryWrapperX<T> gtIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            return (LambdaQueryWrapperX<T>) super.gt(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> gtCondition( Boolean condition      ,SFunction<T, ?> column, Object val) {
        if (condition && val != null) {
            return (LambdaQueryWrapperX<T>) super.gt(condition ,column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> geCondition( Boolean condition      ,SFunction<T, ?> column, Object val) {
        if (condition && val != null) {
            return (LambdaQueryWrapperX<T>) super.ge(condition ,column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> geIfPresent(SFunction<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val)) {
            return (LambdaQueryWrapperX<T>) super.ge(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> ltIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            return (LambdaQueryWrapperX<T>) super.lt(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> leIfPresent(SFunction<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val) ) {
            return (LambdaQueryWrapperX<T>) super.le(column, val);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> betweenIfPresent(SFunction<T, ?> column, Object val1, Object val2) {
        if (val1 != null && val2 != null) {
            return (LambdaQueryWrapperX<T>) super.between(column, val1, val2);
        }
        if (val1 != null) {
            return (LambdaQueryWrapperX<T>) ge(column, val1);
        }
        if (val2 != null) {
            return (LambdaQueryWrapperX<T>) le(column, val2);
        }
        return this;
    }

    public LambdaQueryWrapperX<T> betweenIfPresent(SFunction<T, ?> column, Object[] values) {
        if (Objects.nonNull(values)) {
            Object val1 = ArrayUtils.get(values, 0);
            Object val2 = ArrayUtils.get(values, 1);
            return betweenIfPresent(column, val1, val2);
        }
        return this;
    }

    // ========== 重写父类方法，方便链式调用 ==========

    @Override
    public LambdaQueryWrapperX<T> eq(boolean condition, SFunction<T, ?> column, Object val) {
        super.eq(condition, column, val);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> eq(SFunction<T, ?> column, Object val) {
        super.eq(column, val);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> orderByDesc(SFunction<T, ?> column) {
        super.orderByDesc(true, column);
        return this;
    }




    @Override
    public LambdaQueryWrapperX<T> orderByAsc(SFunction<T, ?> column) {
        super.orderByAsc(true, column);
        return this;
    }



    // @Override
    // public LambdaQueryWrapperX<T> orderByAsc(SFunction<T, ?> column, SFunction<T, ?>... columns) {
    //     super.orderByAsc(true,column,columns);
    //     return this;
    // }

    @Override
    public LambdaQueryWrapperX<T> last(String lastSql) {
        super.last(lastSql);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> in(SFunction<T, ?> column, Collection<?> coll) {
        super.in(column, coll);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> and(boolean condition, Consumer<LambdaQueryWrapper<T>> consumer) {
        super.and(condition, consumer);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> and(Consumer<LambdaQueryWrapper<T>> consumer) {
        super.and(consumer);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> or(Consumer<LambdaQueryWrapper<T>> consumer) {
        super.or(consumer);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> or(boolean condition, Consumer<LambdaQueryWrapper<T>> consumer) {
        super.or(condition, consumer);
        return this;
    }

    @Override
    public LambdaQueryWrapperX<T> or() {
        super.or();
        return this;
    }

    public LambdaQueryWrapperX<T> selectX(SFunction<T, ?>... columns) {
        // 调用父类的select方法设置查询字段
        select(columns);
        return this;
    }

//    public LambdaQueryWrapperX<T> caseWhen(SFunction<T, ?> column, String alias, String... conditions) {
//        if (alias.isEmpty() || conditions == null || conditions.length == 0) {
//            return this;
//        }
//
//        StringBuilder caseWhenBuilder = new StringBuilder("CASE ");
//        for (int i = 0; i < conditions.length; i++) {
//            if (i % 2 == 0) {
//                caseWhenBuilder.append("WHEN ");
//                caseWhenBuilder.append(conditions[i]);
//                caseWhenBuilder.append(" THEN ");
//            } else {
//                caseWhenBuilder.append(conditions[i]);
//                caseWhenBuilder.append(" ");
//            }
//        }
//        caseWhenBuilder.append("ELSE ");
//        caseWhenBuilder.append(alias);
//        caseWhenBuilder.append(" END");
//
//        select(column, SqlKeyword.IGNORE, caseWhenBuilder.toString());
//
//        return this;
//    }
}
