package com.unicom.swdx.framework.mybatis.core.type;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.List;
import java.util.stream.Collectors;

/**
 * List<Long> 的类型转换器实现类，对应数据库的 varchar 类型
 *
 * <AUTHOR>
 * @since 2023 2/13 12:50:15
 */
public class ListLongTypeHandler extends AbstractJsonTypeHandler<List<Long>> {

    private static final String COMMA = ",";

    private static final TypeReference<List<Long>> TYPE_REFERENCE = new TypeReference<List<Long>>(){};

    @Override
    protected List<Long> parse(String json) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        List<String> list = StrUtil.splitTrim(json,COMMA);
        return list.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    @Override
    protected String toJson(List<Long> list) {
        return CollUtil.join(list,COMMA);
    }
}
