package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DegreeEnum {

    // 博士学位
    DOCTOR_OF_PHILOSOPHY(0, "哲学博士学位"),
    DOCTOR_OF_ECONOMICS(1, "经济学博士学位"),
    DOCTOR_OF_LAW(2, "法学博士学位"),
    DOCTOR_OF_EDUCATION(3, "教育学博士学位"),
    DOCTOR_OF_LITERATURE(4, "文学博士学位"),
    DOCTOR_OF_HISTORY(5, "历史学博士学位"),
    DOCTOR_OF_SCIENCE(6, "理学博士学位"),
    DOCTOR_OF_ENGINEERING(7, "工学博士学位"),
    DOCTOR_OF_AGRICULTURE(8, "农学博士学位"),
    DOCTOR_OF_MEDICINE(9, "医学博士学位"),
    DOCTOR_OF_MILITARY_SCIENCE(10, "军事学博士学位"),
    DOCTOR_OF_MANAGEMENT(11, "管理学博士学位"),
    DOCTOR_OF_ARTS(12, "艺术学博士学位"),
    DOCTOR_OF_EDUCATION_PROFESSIONAL_DEGREE(13, "教育博士专业学位"),
    DOCTOR_OF_ENGINEERING_PROFESSIONAL_DEGREE(14, "工程博士专业学位"),
    DOCTOR_OF_CLINICAL_MEDICINE_PROFESSIONAL_DEGREE(15, "临床医学博士专业学位"),
    DOCTOR_OF_VETERINARY_MEDICINE_PROFESSIONAL_DEGREE(16, "兽医博士专业学位"),
    DOCTOR_OF_STOMATOLOGY_PROFESSIONAL_DEGREE(17, "口腔医学博士专业学位"),

    // 硕士学位
    MASTER_OF_PHILOSOPHY(100, "哲学硕士学位"),
    MASTER_OF_ECONOMICS(101, "经济学硕士学位"),
    MASTER_OF_LAW(102, "法学硕士学位"),
    MASTER_OF_EDUCATION(103, "教育学硕士学位"),
    MASTER_OF_LITERATURE(104, "文学硕士学位"),
    MASTER_OF_HISTORY(105, "历史学硕士学位"),
    MASTER_OF_SCIENCE(106, "理学硕士学位"),
    MASTER_OF_ENGINEERING(107, "工学硕士学位"),
    MASTER_OF_AGRICULTURE(108, "农学硕士学位"),
    MASTER_OF_MEDICINE(109, "医学硕士学位"),
    MASTER_OF_MILITARY_SCIENCE(110, "军事学硕士学位"),
    MASTER_OF_MANAGEMENT(111, "管理学硕士学位"),
    MASTER_OF_ARTS(112, "艺术学硕士学位"),
    MASTER_OF_LAW_PROFESSIONAL_DEGREE(113, "法律硕士专业学位"),
    MASTER_OF_EDUCATION_PROFESSIONAL_DEGREE(114, "教育硕士专业学位"),
    MASTER_OF_ENGINEERING_PROFESSIONAL_DEGREE(115, "工程硕士专业学位"),
    MASTER_OF_ARCHITECTURE_PROFESSIONAL_DEGREE(116, "建筑学硕士专业学位"),
    MASTER_OF_CLINICAL_MEDICINE_PROFESSIONAL_DEGREE(117, "临床学硕士专业学位"),
    MASTER_OF_BUSINESS_ADMINISTRATION_PROFESSIONAL_DEGREE(118, "工商管理硕士专业学位"),
    MASTER_OF_AGRICULTURAL_EXTENSION_PROFESSIONAL_DEGREE(119, "农业推广硕士专业学位"),
    MASTER_OF_VETERINARY_PROFESSIONAL_DEGREE(120, "兽医硕士专业学位"),
    MASTER_OF_PUBLIC_MANAGEMENT_PROFESSIONAL_DEGREE(121, "公共管理硕士专业学位"),
    MASTER_OF_STOMATOLOGY_PROFESSIONAL_DEGREE(122, "口腔医学硕士专业学位"),
    MASTER_OF_PUBLIC_HEALTH_PROFESSIONAL_DEGREE(123, "公共卫生硕士专业学位"),
    MASTER_OF_MILITARY_PROFESSIONAL_DEGREE(124, "军事硕士专业学位"),
    MASTER_OF_FINANCE_PROFESSIONAL_DEGREE(125, "金融硕士专业学位"),
    MASTER_OF_APPLIED_STATISTICS_PROFESSIONAL_DEGREE(126, "应用统计硕士专业学位"),
    MASTER_OF_TAXATION_PROFESSIONAL_DEGREE(127, "税务硕士专业学位"),
    MASTER_OF_INTERNATIONAL_BUSINESS_PROFESSIONAL_DEGREE(128, "国际商务硕士专业学位"),
    MASTER_OF_INSURANCE_PROFESSIONAL_DEGREE(129, "保险硕士专业学位"),
    MASTER_OF_ASSET_VALUATION_PROFESSIONAL_DEGREE(130, "资产评估硕士专业学位"),
    MASTER_OF_AUDITING_PROFESSIONAL_DEGREE(131, "审计硕士专业学位"),
    MASTER_OF_SOCIAL_WORK_PROFESSIONAL_DEGREE(132, "社会工作硕士专业学位"),
    MASTER_OF_POLICE_PROFESSIONAL_DEGREE(133, "警务硕士专业学位"),
    MASTER_OF_PHYSICAL_EDUCATION_PROFESSIONAL_DEGREE(134, "体育硕士专业学位"),
    MASTER_OF_TEACHING_CHINESE_TO_SPEAKERS_OF_OTHER_LANGUAGES_PROFESSIONAL_DEGREE(135, "汉语国际教育硕士专业学位"),
    MASTER_OF_APPLIED_PSYCHOLOGY_PROFESSIONAL_DEGREE(136, "应用心理硕士专业学位"),
    MASTER_OF_TRANSLATION_PROFESSIONAL_DEGREE(137, "翻译硕士专业学位"),
    MASTER_OF_JOURNALISM_AND_COMMUNICATION_PROFESSIONAL_DEGREE(138, "新闻与传播硕士专业学位"),
    MASTER_OF_PUBLISHING_PROFESSIONAL_DEGREE(139, "出版硕士专业学位"),
    MASTER_OF_CULTURAL_RELICS_AND_MUSEUMS_PROFESSIONAL_DEGREE(140, "文物与博物馆硕士专业学位"),
    MASTER_OF_URBAN_PLANNING_PROFESSIONAL_DEGREE(141, "城市规划硕士专业学位"),
    MASTER_OF_LANDSCAPE_ARCHITECTURE_PROFESSIONAL_DEGREE(142, "风景园林硕士专业学位"),
    MASTER_OF_FORESTRY_PROFESSIONAL_DEGREE(143, "林业硕士专业学位"),
    MASTER_OF_NURSING_PROFESSIONAL_DEGREE(144, "护理硕士专业学位"),
    MASTER_OF_PHARMACY_PROFESSIONAL_DEGREE(145, "药学硕士专业学位"),
    MASTER_OF_CHINESE_MEDICINE_PROFESSIONAL_DEGREE(146, "中药学硕士专业学位"),
    MASTER_OF_ACCOUNTING_PROFESSIONAL_DEGREE(147, "会计硕士专业学位"),
    MASTER_OF_TOURISM_MANAGEMENT_PROFESSIONAL_DEGREE(148, "旅游管理硕士专业学位"),
    MASTER_OF_LIBRARY_AND_INFORMATION_SCIENCE_PROFESSIONAL_DEGREE(149, "图书情报硕士专业学位"),
    MASTER_OF_ENGINEERING_MANAGEMENT_PROFESSIONAL_DEGREE(150, "工程管理硕士专业学位"),
    MASTER_OF_ART_PROFESSIONAL_DEGREE(151, "艺术硕士专业学位"),

    // 学士学位
    BACHELOR_OF_PHILOSOPHY(200, "哲学学士学位"),
    BACHELOR_OF_ECONOMICS(201, "经济学学士学位"),
    BACHELOR_OF_LAW(202, "法学学士学位"),
    BACHELOR_OF_EDUCATION(203, "教育学学士学位"),
    BACHELOR_OF_LITERATURE(204, "文学学士学位"),
    BACHELOR_OF_HISTORY(205, "历史学学士学位"),
    BACHELOR_OF_SCIENCE(206, "理学学士学位"),
    BACHELOR_OF_ENGINEERING(207, "工学学士学位"),
    BACHELOR_OF_AGRICULTURE(208, "农学学士学位"),
    BACHELOR_OF_MEDICINE(209, "医学学士学位"),
    BACHELOR_OF_MILITARY_SCIENCE(210, "军事学学士学位"),
    BACHELOR_OF_MANAGEMENT(211, "管理学学士学位"),
    BACHELOR_OF_ARTS(212, "艺术学学士学位"),
    BACHELOR_OF_ARCHITECTURE_PROFESSIONAL_DEGREE(213, "建筑学学士专业学位");

    /**
     * 标识
     */
    private final Integer code;
    /**
     * 性别
     */
    private final String name;

}
