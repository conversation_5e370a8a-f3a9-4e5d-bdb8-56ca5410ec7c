package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PersonnalStatusEnum {

    ON_JOB(1, "在职"),

    OLDON_JOB(1, "报到中"),

    OFF_JOB(2, "离职"),
    RETIREMENT(3, "退休"),
    OLDRETIREMENT(3, "离退休"),
    DEATH(4, "去世");



    /**
     * 标识
     */
    private final Integer code;
    /**
     * 性别
     */
    private final String name;


    // 静态方法：通过民族名称查找对应的 code
    public static Integer getCodeByName(String name) {
        for (PersonnalStatusEnum nation : PersonnalStatusEnum.values()) {
            if (nation.getName().equals(name)) {
                return nation.getCode();
            }
        }
        return 1; // 如果找不到对应的名称，返回null
    }

}
