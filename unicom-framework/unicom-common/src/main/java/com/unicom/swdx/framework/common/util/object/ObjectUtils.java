package com.unicom.swdx.framework.common.util.object;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.function.Consumer;

/**
 * Object 工具类
 *
 * <AUTHOR>
 */
public class ObjectUtils {

    /**
     * 复制对象，并忽略 Id 编号
     *
     * @param object 被复制对象
     * @param consumer 消费者，可以二次编辑被复制对象
     * @return 复制后的对象
     */
    public static <T> T cloneIgnoreId(T object, Consumer<T> consumer) {
        T result = ObjectUtil.clone(object);
        // 忽略 id 编号
        Field field = ReflectUtil.getField(object.getClass(), "id");
        if (field != null) {
            ReflectUtil.setFieldValue(result, field, null);
        }
        // 二次编辑
        if (result != null) {
            consumer.accept(result);
        }
        return result;
    }

    public static <T extends Comparable<T>> T max(T obj1, T obj2) {
        if (obj1 == null) {
            return obj2;
        }
        if (obj2 == null) {
            return obj1;
        }
        return obj1.compareTo(obj2) > 0 ? obj1 : obj2;
    }

    public static <T> T defaultIfNull(T... array) {
        for (T item : array) {
            if (item != null) {
                return item;
            }
        }
        return null;
    }

    public static <T> boolean equalsAny(T obj, T... array) {
        return Arrays.asList(array).contains(obj);
    }


    public static boolean checkFields(Object obj) throws IllegalAccessException {
        if (obj == null) {
            return false;
        }

        // 通过反射获取该对象的所有字段
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);  // 允许访问 private 字段

            Object value = field.get(obj);

            if (value == null) {
                System.out.println(field.getName() + " is null.");
                return false;
            }

            // 如果字段是 String 类型，还需要检查是否为空字符串
            if (value instanceof String && ((String) value).trim().isEmpty()) {
                System.out.println(field.getName() + " is empty.");
                return false;
            }

            // 如果字段是一个嵌套对象，递归检查
            if (!field.getType().isPrimitive() && !(value instanceof String)) {
                if (!checkFields(value)) {
                    return false;
                }
            }
        }

        return true;
    }

    public static <T> T mergeNonNullFields(T target, T source) {
        if (target == null || source == null) return target;
        Field[] fields = source.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(source);
                if (value != null) {
                    field.set(target, value);
                }
            } catch (IllegalAccessException e) {
                // ignore or log
            }
        }
        return target;
    }



}
