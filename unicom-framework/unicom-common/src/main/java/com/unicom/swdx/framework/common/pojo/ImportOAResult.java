package com.unicom.swdx.framework.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.exception.enums.GlobalErrorCodeConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Objects;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportOAResult<T> implements Serializable {

    /**
     * 错误码
     *
     * @see ErrorCode#getCode()
     */
    private Integer code;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 错误提示，用户可阅读
     *
     * @see ErrorCode#getMsg() ()
     */
    private String msg1;

    private String msg2;

    public static <T> ImportOAResult<T> error(Integer code, String message) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code), "code 必须是错误的！");
        ImportOAResult<T> result = new ImportOAResult<>();
        result.code = code;
        result.msg1 = message;
        return result;
    }

}
