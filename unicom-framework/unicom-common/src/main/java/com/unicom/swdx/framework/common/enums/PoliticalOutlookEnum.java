package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PoliticalOutlookEnum {

    CHINA_PARTY(1, "中共党员"),
    PRO_CHINA_PARTY(2, "中共预备党员"),
    COMMUNIST_YOUTH(3, "共青团员"),
    DEMOCRATIC_PARTY(4, "民革会员（民革党员）"),
    DEMOCRATIC_LEAGUE_PARTY(5, "民盟盟员"),
    Civil_PARTY(6, "民建会员"),
    DEMOCRATIC_PROGRESSIVE_PARTY(7, "民进会员"),
    AGRICULTURAL_PARTY(8, "农工党党员"),
    ZhIGONE_PARTY(9, "致公党党员"),
    SOCIETY_93_PARTY(10, "九三学社社员"),
    TAIWAN_PARTY(11, "台盟盟员"),
    INDEPENDENT_PARTY(12, "无党派民主人士"),
    MASSES(13, "群众");

    /**
     * 标识
     */
    private final Integer code;
    /**
     * 政治面貌
     */
    private final String name;

}
