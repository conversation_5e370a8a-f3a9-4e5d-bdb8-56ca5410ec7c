package com.unicom.swdx.framework.common.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class OneCardResult implements Serializable {

    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("code")
    private String code;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("message")
    private Object message;
    @JsonProperty("currentTime")
    private Long currentTime;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("message")
        private String message;
    }

}
