package com.unicom.swdx.framework.common.util.Aes;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;


import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


public class AesUtils {


    private static  String ENCODE_KEY ="dGKKabJtYX4c0cx3";

    private static String IV_KEY ="SQ5n9MnlQ5g2mm83";


    public  static String decryptFromString(String data, Mode mode, Padding padding) {
        AES aes;
        if (Mode.CBC == mode) {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"),
                    new IvParameterSpec(IV_KEY.getBytes()));
        } else {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"));
        }
        byte[] decryptDataBase64 = aes.decrypt(data);
        return new String(decryptDataBase64, StandardCharsets.UTF_8);
    }



    public static String encryptFromString(String data, Mode mode, Padding padding) {
        AES aes;
        if (Mode.CBC == mode) {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"),
                    new IvParameterSpec(IV_KEY.getBytes()));
        } else {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"));
        }
        return aes.encryptBase64(data, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {

        try {
            System.out.println(URLEncoder.encode(AesUtils.encryptFromString("{\"tenantCode\":\"410000\",\"timestamp\":\"1721013008000\"}" ,Mode.CBC, Padding.ZeroPadding) , "utf-8"));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        try {
            System.out.println(AesUtils.decryptFromString(URLDecoder.decode("wKFOrp6CPJIR4CeIHHem9CIv9Fyo6ZKH9qXwPl/gH8BiI0fuu3sWUd2naSQrO+pJR+qK78xe41/Y8DC4oC32509Fhm3jbmsuSRpQOgLiKHo=", "UTF-8"),Mode.CBC, Padding.ZeroPadding));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }


    }



}
