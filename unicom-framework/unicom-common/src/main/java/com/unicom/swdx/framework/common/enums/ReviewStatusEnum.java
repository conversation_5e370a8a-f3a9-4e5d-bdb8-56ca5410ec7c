package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReviewStatusEnum {

    REVIRW_PASS(2, "审核通过"),
    RETURN_MODIFY(3, "退回修改"),
    DEP_REVIRW(4, "人事部门审核"),
    REGISTRATION_REVIRW(5, "报到登记状态");
    /**
     * 标识
     */
    private final Integer code;
    /**
     * 审核状态
     */
    private final String name;

}
