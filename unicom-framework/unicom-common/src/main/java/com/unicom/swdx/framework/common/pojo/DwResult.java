package com.unicom.swdx.framework.common.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class DwResult {


    @JsonProperty("responseEntity")
    private ResponseEntityDTO responseEntity;
    @JsonProperty("serverResult")
    private ServerResultDTO serverResult;

    @NoArgsConstructor
    @Data
    public static class ResponseEntityDTO {
        @JsonProperty("resultCode")
        private String resultCode;
        @JsonProperty("resultMsg")
        private String resultMsg;
        @JsonProperty("internalMsg")
        private Object internalMsg;
        @JsonProperty("errorParam")
        private Object errorParam;
    }

    @NoArgsConstructor
    @Data
    public static class ServerResultDTO {
        @JsonProperty("resultCode")
        private String resultCode;
        @JsonProperty("resultMsg")
        private String resultMsg;
        @JsonProperty("internalMsg")
        private Object internalMsg;
        @JsonProperty("errorParam")
        private Object errorParam;
    }
}
