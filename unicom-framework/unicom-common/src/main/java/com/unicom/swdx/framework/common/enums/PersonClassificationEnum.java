package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PersonClassificationEnum {

    ON_JOB(1, "在职"),
    OFF_JOB(2, "离职"),
    RETIREMENT(3, "退休"),
    DEATH(4, "去世");

    /**
     * 标识
     */
    private final Integer code;
    /**
     * 人员分类
     */
    private final String name;

}
