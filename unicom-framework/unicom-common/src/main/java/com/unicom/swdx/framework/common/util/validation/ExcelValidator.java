package com.unicom.swdx.framework.common.util.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.IMPORT_VALID_FAILED;

@Slf4j
public class ExcelValidator {

    private static final Validator validator;

    static {
        HibernateValidatorConfiguration configure = Validation.byProvider(HibernateValidator.class).configure();
        ValidatorFactory validatorFactory = configure.failFast(false).buildValidatorFactory();
        // 根据validatorFactory拿到一个Validator
        validator = validatorFactory.getValidator();
    }

    public static <T> void valid(List<T> records,int headNum) {
        if(CollUtil.isEmpty(records)){
            return;
        }
        // 使用validator对结果进行校验
        StringBuilder msg = new StringBuilder();
        for (int i = 0; i < records.size(); i++) {
            Set<ConstraintViolation<T>> result = validator.validate(records.get(i));
            String validMsg = result.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(validMsg)) {
                msg.append("第").append(i + 1 + headNum).append("行：")
                        .append(validMsg)
                        .append("\n");
            }
        }
        if (StrUtil.isNotBlank(msg.toString())) {
            throw ServiceExceptionUtil.exception(IMPORT_VALID_FAILED,msg);
        }
    }
}