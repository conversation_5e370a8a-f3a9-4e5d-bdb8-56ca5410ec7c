package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NationEnum {

    HAN(1, "汉族"),
    MONGOLIAN(2, "蒙古族"),
    HUI(3, "回族"),
    TIBETAN(4, "藏族"),
    UYGHUR(5, "维吾尔族"),
    MIAO(6, "苗族"),
    YI(7, "彝族"),
    ZHUANG(8, "壮族"),
    BUYI(9, "布依族"),
    KOREAN(10, "朝鲜族"),
    MANCHU(11, "满族"),
    DONG(12, "侗族"),
    YAO(13, "瑶族"),
    BAI(14, "白族"),
    TUJIA(15, "土家族"),
    HANI(16, "哈尼族"),
    KAZAKH(17, "哈萨克族"),
    DAI(18, "傣族"),
    LI(19, "黎族"),
    LISU(20, "傈僳族"),
    VA(21, "佤族"),
    SHE(22, "畲族"),
    GAOSHAN(23, "高山族"),
    LAHU(24, "拉祜族"),
    SHUI(25, "水族"),
    DONGXIANG(26, "东乡族"),
    NAXI(27, "纳西族"),
    JINGPO(28, "景颇族"),
    KYRGYZ(29, "柯尔克孜族"),
    TU(30, "土族"),
    DAUR(31, "达斡尔族"),
    MULAO(32, "仫佬族"),
    QIANG(33, "羌族"),
    BLANG(34, "布朗族"),
    SALAR(35, "撒拉族"),
    MAONAN(36, "毛南族"),
    GELAO(37, "仡佬族"),
    XIBE(38, "锡伯族"),
    ACHANG(39, "阿昌族"),
    PUMI(40, "普米族"),
    TAJIK(41, "塔吉克族"),
    NU(42, "怒族"),
    UZBEK(43, "乌孜别克族"),
    RUSSIAN(44, "俄罗斯族"),
    EWENKI(45, "鄂温克族"),
    BONGGEUN(46, "崩龙族"),
    BONAN(47, "保安族"),
    YUGUR(48, "裕固族"),
    JING(49, "京族"),
    TATAR(50, "塔塔尔族"),
    DRUNG(51, "独龙族"),
    OROQEN(52, "鄂伦春族"),
    HEZHEN(53, "赫哲族"),
    MENBA(54, "门巴族"),
    LUOBA(55, "珞巴族"),
    JINO(56, "基诺族");

    /**
     * 标识
     */
    private final Integer code;
    /**
     * 民族
     */
    private final String name;


    // 静态方法：通过民族名称查找对应的 code
    public static Integer getCodeByName(String name) {
        for (NationEnum nation : NationEnum.values()) {
            if (nation.getName().equals(name)) {
                return nation.getCode();
            }
        }
        return null; // 如果找不到对应的名称，返回null
    }

}
