package com.unicom.swdx.framework.common.util.crypt.sm4;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import icu.xuyijie.sm4utils.util.SM4Utils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/29 21:13
 **/
public class SM4Util {

    private final static String secretKey = "xWCw8MwMKJjX!Q52";

    private final static String iv = "tzGjhXstHa5BT7V!";

    public static String decrypt(String encryptText) {
        return StrUtil.isBlank(encryptText)?encryptText:SM4Utils.decryptData_CBC(encryptText,secretKey,iv);
    }

    public static String encrypt(String text) {
        return StrUtil.isBlank(text)?text:SM4Utils.encryptData_CBC(text,secretKey,iv);
    }

    public static List<String> encryptList(List<String> list) {
        List<String> newList = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return newList;
        }
        for (String s : list) {
            newList.add(encrypt(s));
        }
        return newList;
    }
}
