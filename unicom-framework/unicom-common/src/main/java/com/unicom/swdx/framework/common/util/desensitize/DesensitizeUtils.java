package com.unicom.swdx.framework.common.util.desensitize;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 数据脱敏工具类
 * @date: 2024-11-22
 */
public class DesensitizeUtils {

    /**
     * 替换字符
     */
    private static final String REPLACER = "*";

    private static final int ID_CARD_CUT_POINT = 9;

    /**
     * 保留前缀长度
     */
    public final static Integer PHONE_PREFIX_KEEP = 3;

    /**
     * 保留后缀长度
     */
    public final static Integer PHONE_SUFFIX_KEEP = 4;

    /**
     * 证件号脱敏
     *
     * @param origin     原始字符串
     * @param prefixKeep 保留前缀长度
     * @param suffixKeep 保留后缀长度
     * @return 脱敏后数据
     */
    public static String idCardDesensitize(String origin, int prefixKeep, int suffixKeep) {
        if (origin == null) {
            return null;
        }

        if (origin.length() > ID_CARD_CUT_POINT){
            prefixKeep = Integer.MAX_VALUE;
            suffixKeep = Integer.MAX_VALUE;
        }

        return sliderDesensitize(origin, prefixKeep, suffixKeep, REPLACER);

    }

    /**
     * 功能描述：滑动脱敏处理器
     *
     * @param origin     原始字符串
     * @param prefixKeep 保留前缀长度
     * @param suffixKeep 保留后缀长度
     * @return
     */
    public static String sliderDesensitize(String origin, int prefixKeep, int suffixKeep) {
        return sliderDesensitize(origin, prefixKeep, suffixKeep, REPLACER);
    }

    /**
     * 功能描述：滑动脱敏处理器
     *
     * @param origin     原始字符串
     * @param prefixKeep 保留前缀长度
     * @param suffixKeep 保留后缀长度
     * @param replacer   替换字符
     * @return 结果
     */
    public static String sliderDesensitize(String origin, int prefixKeep, int suffixKeep, String replacer) {
        if (origin == null) {
            return null;
        }
        int length = origin.length();

        // 情况一：原始字符串长度小于等于保留长度，则原始字符串全部替换
        if (prefixKeep >= length || suffixKeep >= length) {
            return buildReplacerByLength(replacer, length);
        }

        // 情况二：原始字符串长度小于等于前后缀保留字符串长度，则原始字符串全部替换
        if ((prefixKeep + suffixKeep) >= length) {
            return buildReplacerByLength(replacer, length);
        }

        // 情况三：原始字符串长度大于前后缀保留字符串长度，则替换中间字符串
        int interval = length - prefixKeep - suffixKeep;
        return origin.substring(0, prefixKeep) +
                buildReplacerByLength(replacer, interval) +
                origin.substring(prefixKeep + interval);
    }

    private static String buildReplacerByLength(String replacer, int length) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            builder.append(replacer);
        }
        return builder.toString();
    }

    /**
     * 手机号脱敏
     * 136****3647
     *
     * @param mobile 原手机号
     * @return 脱敏后手机号
     */
    public static String mobileDesensitize(String mobile) {
        if (Objects.isNull(mobile)) {
            return null;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }


}
