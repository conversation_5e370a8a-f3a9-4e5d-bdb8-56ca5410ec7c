package com.unicom.swdx.framework.common.util.crypt.sm2;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.crypto.engines.SM2Engine;

/**
 * <AUTHOR>
 * @date 2023/9/18 17:47
 **/
public class SM2Utils {

    private static final String privateKeyStr = "566dfab900d6b1f7e1bcef59e0d35f45b7930b3fbc4d9fd3d4e289df00929381";

    private static final  String publicKeyStr = "04247aacf337ec1c18b7a6575e807edced8832460905a780e7ccd0382e1a9c43e9fb5af1cfd232ec7eedc1b6a4a611dfd22e43ec152dfb9f5e197a54906641dfc2";

    public static final  String USER_PASSWORD = "8$SxLPqTv^sR";

    private static final SM2 sm2 = SmUtil.sm2(privateKeyStr,publicKeyStr);

    {
        sm2.setMode(SM2Engine.Mode.C1C3C2);
    }

    public static String encrypt(String content) {
        return sm2.encryptHex(content, KeyType.PublicKey);
    }

    public static String decrypt(String content) {
        //return HexUtil.encodeHexStr(sm2.decrypt(content, KeyType.PrivateKey));
        return sm2.decryptStr(content, KeyType.PrivateKey);
    }

    public static void main(String[] args) {

//        // 私钥：这个保存好，切记不要泄漏，真的泄露了就重新生成一下
//        byte[] privateKey = BCUtil.encodeECPrivateKey(sm2.getPrivateKey());
//        // 公钥：这个是前后端加密用的，不压缩选择带04的，不带04到时候前端会报错
//        byte[] publicKey = ((BCECPublicKey) sm2.getPublicKey()).getQ().getEncoded(false);
//        System.out.println("公钥：" + HexUtil.encodeHexStr(publicKey));
//        System.out.println("私钥：" + HexUtil.encodeHexStr(privateKey));


    }

}
