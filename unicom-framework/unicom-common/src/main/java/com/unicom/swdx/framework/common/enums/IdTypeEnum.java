package com.unicom.swdx.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IdTypeEnum {

    RESIDENT_ID_CARD(1, "居民身份证"),
    CERTIFICATRE_OF_OFFICE(2, "军官证"),
    SOLDIER_CERTIFICATE(3, "士兵证"),
    CIVILIAN_CERTIFICATE(4, "文职干部证"),
    MILITARY_RETIREMENT_CERTIFICATE(5, "部队离退休证"),
    HONG_KONG_PASSRORT(6, "香港特区护照/身份证明"),
    AOMEN_PASSRORT(7, "澳门特区护照/身份证明"),
    TAIWAN_PASSRORT(8, "台湾居民来往大陆通行证"),
    OVERSEAS_RESIDENCE_PERMIT(9, "境外永久居住证");

    /**
     * 标识
     */
    private final Integer code;
    /**
     * id类型
     */
    private final String name;

}
