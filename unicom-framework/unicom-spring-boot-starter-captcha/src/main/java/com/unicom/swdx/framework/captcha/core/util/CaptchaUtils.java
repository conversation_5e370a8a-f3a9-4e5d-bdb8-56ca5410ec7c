package com.unicom.swdx.framework.captcha.core.util;

import cn.hutool.core.util.RandomUtil;

import java.security.SecureRandom;

/**
 * <AUTHOR>
 * @date 2023/2/27 11:30
 **/
public class CaptchaUtils {

    public static String getNumCaptcha() {
        return getNumCaptcha(6);
    }

    public static String getNumCaptcha(int length) {
        SecureRandom sr = RandomUtil.getSecureRandom();
        StringBuilder captcha = new StringBuilder();
        for (int i = 0; i < length; i++) {
            captcha.append(sr.nextInt(10));
        }
        return captcha.toString();
    }

}
