package com.unicom.swdx.framework.excel.core.handler;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 在需要的列上设置下拉框、提示信息
 */
public class SelectedColumnWriteHandler implements SheetWriteHandler {
    /**
     * 需要设置的下拉框列和对应可选值 {列索引值：下拉框列表值}
     */
    private final Map<Integer, List<String>> selectedCellColumnMap;

    /**
     * 需要设置的提示信息列和对应提示信息 {列索引值：提示信息}
     */
    private final Map<Integer, String> columnPromptsMap;
    private Integer rowSize = 5000;
    private Integer column = 20;

    /**
     * @param selectedCellColumnMap 需要设置的下拉框列和对应可选值 {列索引值：下拉框列表值}
     */
    public SelectedColumnWriteHandler(Map<Integer, List<String>> selectedCellColumnMap) {
        this.selectedCellColumnMap = selectedCellColumnMap;
        this.columnPromptsMap = new HashMap<>();
    }

    public SelectedColumnWriteHandler(Map<Integer, List<String>> selectedCellColumnMap, Map<Integer, String> columnPromptsMap) {
        this.selectedCellColumnMap = selectedCellColumnMap;
        this.columnPromptsMap = columnPromptsMap;
    }

    public SelectedColumnWriteHandler(Map<Integer, List<String>> selectedCellColumnMap, Integer rowSize) {
        this.selectedCellColumnMap = selectedCellColumnMap;
        this.rowSize = rowSize;
        this.columnPromptsMap = new HashMap<>();
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        final Sheet sheet = writeSheetHolder.getSheet();
        final DataValidationHelper helper = sheet.getDataValidationHelper();
        selectedCellColumnMap.forEach((columnIndex, data) -> {
            if (Objects.isNull(data) || data.isEmpty()) {
                return;
            }
            // 定义sheet的名称
            // 1.创建一个隐藏的sheet 名称为 hidden + k
            final String sheetName = "hidden" + columnIndex;
            final Workbook workbook = writeWorkbookHolder.getWorkbook();
            final Sheet hiddenSheet = workbook.createSheet(sheetName);
            for (int i = 0, length = data.size(); i < length; i++) {
                // 开始的行数i，列数k
                hiddenSheet.createRow(i).createCell(columnIndex).setCellValue(data.get(i));
            }
            final Name category1Name = workbook.createName();
            category1Name.setNameName(sheetName);
            String columnName = excelColIndexToStr(columnIndex + 1);
            // $columnName$1:$columnName$N代表 以A列1行开始获取N行下拉数据 将刚才设置的sheet引用到你的下拉列表中
            category1Name.setRefersToFormula(sheetName + "!$" + columnName + "$1:$" + columnName + "$" + (data.size()));
            // 设置下拉列表的行： 首行，末行，首列，末列
            final CellRangeAddressList rangeList = new CellRangeAddressList(1, rowSize, columnIndex, columnIndex);
            final DataValidationConstraint constraint = helper.createFormulaListConstraint(sheetName);
            final DataValidation dataValidation = helper.createValidation(constraint, rangeList);
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请输入下拉选项中的内容");
            sheet.addValidationData(dataValidation);
            // 设置存储下拉列值得sheet为隐藏
            final int hiddenIndex = workbook.getSheetIndex(sheetName);
            if (!workbook.isSheetHidden(hiddenIndex)) {
                workbook.setSheetHidden(hiddenIndex, true);
            }
        });

        columnPromptsMap.forEach((columnIndex, promptContent) -> {
            if (StringUtils.isEmpty(promptContent)) {
                return;
            }
            setColumnPrompt(writeSheetHolder, columnIndex, promptContent);
        });

        for (int i = 0; i < column; i++) {
            // 设置为文本格式
            final SXSSFSheet sxssfSheet = (SXSSFSheet) writeSheetHolder.getSheet();
            final CellStyle cellStyle = writeWorkbookHolder.getCachedWorkbook().createCellStyle();
            // 49为文本格式
            cellStyle.setDataFormat((short) 49);
            // i为列，一整列设置为文本格式
            sxssfSheet.setDefaultColumnStyle(i, cellStyle);
        }



    }

    /**
     * 设置某一列的提示信息
     *
     * @param writeSheetHolder   工作表持有者
     * @param columnIndex        列索引
     * @param promptContent      提示内容
     */
    private void setColumnPrompt(WriteSheetHolder writeSheetHolder, int columnIndex, String promptContent) {
        final Sheet sheet = writeSheetHolder.getSheet();
        final DataValidationHelper helper = sheet.getDataValidationHelper();

        // 定义提示信息的范围（整列）
        final CellRangeAddressList rangeList = new CellRangeAddressList(1, rowSize, columnIndex, columnIndex);

        // 创建提示信息的约束条件（这里使用空约束，因为提示信息不需要限制输入内容）
        final DataValidationConstraint constraint = helper.createCustomConstraint("BB1");

        // 创建数据验证对象
        final DataValidation dataValidation = helper.createValidation(constraint, rangeList);

        // 设置提示信息的标题和内容
        dataValidation.setShowPromptBox(true);
        dataValidation.createPromptBox("", promptContent);

        // 将数据验证添加到工作表
        sheet.addValidationData(dataValidation);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    /**
     * excel表列索引转字母
     *
     * @param columnIndex 列索引 >=1
     * @return 索引对应字母
     */
    public static String excelColIndexToStr(int columnIndex) {
        if (columnIndex <= 0) {
            return null;
        }
        StringBuilder columnStr = new StringBuilder();
        columnIndex--;
        do {
            if (StringUtils.isNotBlank(columnStr)) {
                columnIndex--;
            }
            columnStr.insert(0, (char) (columnIndex % 26 + (int) 'A'));
            columnIndex = (columnIndex - columnIndex % 26) / 26;
        } while (columnIndex > 0);
        return columnStr.toString();
    }

}

