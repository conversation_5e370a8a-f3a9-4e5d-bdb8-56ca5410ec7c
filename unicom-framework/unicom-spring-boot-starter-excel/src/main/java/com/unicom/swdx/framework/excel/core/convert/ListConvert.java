package com.unicom.swdx.framework.excel.core.convert;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.util.List;
import java.util.stream.Collectors;

public class ListConvert implements Converter<List> {

    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<List> context) throws Exception {
        String value = "";
        List list = context.getValue();
        if (CollUtil.isNotEmpty(list)) {
            value = list.stream().map(String::valueOf).collect(Collectors.joining(",")).toString();
        }
        return new WriteCellData<>(value);
    }


}
