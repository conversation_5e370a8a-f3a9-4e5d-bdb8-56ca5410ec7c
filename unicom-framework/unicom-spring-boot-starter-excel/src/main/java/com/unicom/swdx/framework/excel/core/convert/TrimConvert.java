package com.unicom.swdx.framework.excel.core.convert;

import cn.hutool.core.convert.Convert;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * Excel 去除空格和回车 转换器
 *
 */
public class TrimConvert implements Converter<Object> {

    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public Object convertToJavaData(ReadCellData readCellData, ExcelContentProperty contentProperty,
                                                   GlobalConfiguration globalConfiguration) {
        // 将 String 的 value 转换成对应的属性
        Class<?> fieldClazz = contentProperty.getField().getType();
        if (readCellData.getType().equals(CellDataTypeEnum.NUMBER)) {
            return Convert.convert(fieldClazz, String.valueOf(readCellData.getNumberValue()).replaceAll("[\\s\u3000]+", ""));
        } else if (readCellData.getType().equals(CellDataTypeEnum.STRING)) {
            return Convert.convert(fieldClazz, String.valueOf(readCellData.getStringValue()).replaceAll("[\\s\u3000]+", ""));
        }
        return null;
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object value, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(String.valueOf(value));
    }

}
