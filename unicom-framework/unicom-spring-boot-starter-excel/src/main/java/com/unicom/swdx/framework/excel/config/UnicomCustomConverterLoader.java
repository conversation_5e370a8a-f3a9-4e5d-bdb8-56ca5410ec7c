package com.unicom.swdx.framework.excel.config;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ConverterKeyBuild;
import com.alibaba.excel.converters.DefaultConverterLoader;
import com.unicom.swdx.framework.excel.core.convert.LocalDateConvert;
import com.unicom.swdx.framework.excel.core.convert.LocalDateTimeConvert;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Configuration
public class UnicomCustomConverterLoader {

    @Bean
    public DefaultConverterLoader defaultConverterLoader() {
        DefaultConverterLoader converters = new DefaultConverterLoader();
        Map<ConverterKeyBuild.ConverterKey, Converter<?>> defaultWriteConverterrMap = converters.loadDefaultWriteConverter();
        Map<ConverterKeyBuild.ConverterKey, Converter<?>> allConverter = converters.loadAllConverter();

        defaultWriteConverterrMap.put(ConverterKeyBuild.buildKey(LocalDate.class),LocalDateConvert.INSTANCE);
        defaultWriteConverterrMap.put(ConverterKeyBuild.buildKey(LocalDateTime.class), LocalDateTimeConvert.INSTANCE);

        allConverter.put(ConverterKeyBuild.buildKey(LocalDate.class), LocalDateConvert.INSTANCE);
        allConverter.put(ConverterKeyBuild.buildKey(LocalDateTime.class), LocalDateTimeConvert.INSTANCE);
        return converters;
    }
}