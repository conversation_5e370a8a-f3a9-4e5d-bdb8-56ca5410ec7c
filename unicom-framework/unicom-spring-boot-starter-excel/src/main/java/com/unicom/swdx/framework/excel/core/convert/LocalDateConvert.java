package com.unicom.swdx.framework.excel.core.convert;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/13 14:22
 **/
@Slf4j
public class LocalDateConvert implements Converter<LocalDate> {

    public static final LocalDateConvert INSTANCE = new LocalDateConvert();

    @Override
    public Class<LocalDate> supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String date = "";
        String pattern = getPattern(contentProperty);
        if (Objects.nonNull(value)) {
            if (StrUtil.isBlank(pattern)) {
                date = DateUtils.format(value);
            } else {
                date = DateUtils.format(value,pattern);
            }
        }
        return new WriteCellData<>(date);
    }

    @Override
    public LocalDate convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        BigDecimal dateNum = cellData.getNumberValue();
        String dateStr = cellData.getStringValue();
        String pattern = getPattern(contentProperty);
        if (StrUtil.isNotBlank(dateStr)) {
            if (StrUtil.isBlank(pattern)) {
                return DateUtils.parseLocalDate(dateStr);
            } else {
                return DateUtils.parseLocalDate(dateStr,pattern);
            }
        } else if (Objects.nonNull(dateNum)) {
            return DateUtils.ofExcelDay(dateNum.longValue());
        }
        log.error("{} 转换 LocalDate 失败",dateStr);
        return null;
    }

    private static String getPattern(ExcelContentProperty contentProperty) {
        DateTimeFormat annotation = contentProperty.getField().getAnnotation(DateTimeFormat.class);
        if (Objects.nonNull(annotation)) {
            return annotation.value();
        }
        return null;
    }

}
