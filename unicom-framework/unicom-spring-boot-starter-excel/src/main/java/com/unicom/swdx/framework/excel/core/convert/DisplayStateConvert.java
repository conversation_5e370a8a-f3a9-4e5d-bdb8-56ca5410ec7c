package com.unicom.swdx.framework.excel.core.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.unicom.swdx.framework.dict.core.util.DictFrameworkUtils;

public class DisplayStateConvert implements Converter<Boolean> {
    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Boolean convertToJavaData(ReadCellData readCellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public WriteCellData convertToExcelData(Boolean displayState, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        // 空时，返回空
        if (displayState == null) {
            return new WriteCellData<>("");
        }
        String label = "";
        if (displayState.booleanValue()) {
            label = "展示";
        } else  {
            label = "不展示";
        }
        // 生成 Excel 小表格
        return new WriteCellData<>(label);
    }
}
