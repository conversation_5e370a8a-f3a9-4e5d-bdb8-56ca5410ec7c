package com.unicom.swdx.framework.excel.core.util;

import com.alibaba.excel.write.handler.WriteHandler;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 一个excel 中多个sheet数据封装
 * @date 2025-03-17
 */
@Data
public class ExcelDataWriteDTO<T> {

    /**
     * sheet页名称
     */
    private String sheetName;

    /**
     * Class head 列名信息
     */
    private Class<T> head;

    /**
     * 数据
     */
    private List<T> data;

    /**
     * 排除一些列导出
     */
    private Collection<Integer> excludeColumnIndexes;

    /**
     * 包含指定列导出
     */
    private Collection<Integer> includeColumnIndexes;

    /**
     * WriteHandler 列表  比如<SelectedColumnWriteHandler>
     */
    private List<WriteHandler> writeHandlerList;

}
