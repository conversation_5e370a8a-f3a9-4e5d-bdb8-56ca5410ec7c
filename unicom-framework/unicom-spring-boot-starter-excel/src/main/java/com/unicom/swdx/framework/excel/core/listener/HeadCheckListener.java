package com.unicom.swdx.framework.excel.core.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: excel表头校验
 * @date 2024-10-17
 */
@Slf4j
public class HeadCheckListener<T> extends AnalysisEventListener<T> {

    private List<String> excelHeadList;

    @Override
    public void invoke(T data, AnalysisContext context) {
        // 处理数据逻辑
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 所有数据处理完成后的逻辑
    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头信息
     * @param context 上下文
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        this.excelHeadList = new ArrayList<>(headMap.values());
    }

    public List<String> getExcelHeadList() {
        return excelHeadList;
    }
}