package com.unicom.swdx.framework.security.config;

import com.unicom.swdx.framework.security.core.rpc.LoginUserRequestInterceptor;
import com.unicom.swdx.module.system.api.oauth2.OAuth2TokenApi;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Security 使用到 Feign 的配置项
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {OAuth2TokenApi.class, // 主要是引入相关的 API 服务
        PermissionApi.class})
public class UnicomSecurityRpcAutoConfiguration {

    @Bean
    public LoginUserRequestInterceptor loginUserRequestInterceptor() {
        return new LoginUserRequestInterceptor();
    }

}
