package com.unicom.swdx.framework.security.core.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.unicom.swdx.framework.security.core.LoginUser;

/**
 * <AUTHOR>
 * @date 2023/11/9 09:54
 **/
public class LoginUserContextHolder {

    private static final ThreadLocal<LoginUser> LOGIN_USER = new TransmittableThreadLocal<>();

    public static LoginUser get() {
        return LOGIN_USER.get();
    }

    public static Long getLoginUserId() {
        return get().getId();
    }

    public static void set(LoginUser loginUser) {
        LOGIN_USER.set(loginUser);
    }

    public static void clear() {
        LOGIN_USER.remove();
    }

}
