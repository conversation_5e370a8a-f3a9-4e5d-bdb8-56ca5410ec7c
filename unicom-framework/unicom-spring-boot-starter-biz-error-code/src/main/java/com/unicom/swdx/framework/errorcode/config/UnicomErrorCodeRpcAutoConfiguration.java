package com.unicom.swdx.framework.errorcode.config;

import com.unicom.swdx.module.system.api.errorcode.ErrorCodeApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * 错误码用到 Feign 的配置项
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = ErrorCodeApi.class) // 主要是引入相关的 API 服务
public class UnicomErrorCodeRpcAutoConfiguration {
}
