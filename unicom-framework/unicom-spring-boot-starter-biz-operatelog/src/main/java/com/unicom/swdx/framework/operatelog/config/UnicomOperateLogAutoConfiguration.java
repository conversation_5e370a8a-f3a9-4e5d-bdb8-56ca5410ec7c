package com.unicom.swdx.framework.operatelog.config;

import com.unicom.swdx.framework.operatelog.core.aop.OperateLogAspect;
import com.unicom.swdx.framework.operatelog.core.service.OperateLogFrameworkService;
import com.unicom.swdx.framework.operatelog.core.service.OperateLogFrameworkServiceImpl;
import com.unicom.swdx.module.system.api.logger.OperateLogApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class UnicomOperateLogAutoConfiguration {

    @Bean
    public OperateLogAspect operateLogAspect() {
        return new OperateLogAspect();
    }

    @Bean
    public OperateLogFrameworkService operateLogFrameworkService(OperateLogApi operateLogApi) {
        return new OperateLogFrameworkServiceImpl(operateLogApi);
    }

}
