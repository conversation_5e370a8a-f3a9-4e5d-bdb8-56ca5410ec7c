package com.unicom.swdx.framework.file.config;

import com.unicom.swdx.framework.file.core.client.FileClientFactory;
import com.unicom.swdx.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class UnicomFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
