package com.unicom.swdx.framework.sms.core.client.impl.debug;

import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.unicom.swdx.framework.sms.core.client.SmsCodeMapping;
import com.unicom.swdx.framework.sms.core.enums.SmsFrameworkErrorCodeConstants;

import java.util.Objects;

/**
 * 钉钉的 SmsCodeMapping 实现类
 *
 * <AUTHOR>
 */
public class DebugDingTalkCodeMapping implements SmsCodeMapping {

    @Override
    public ErrorCode apply(String apiCode) {
        return Objects.equals(apiCode, "0") ? GlobalErrorCodeConstants.SUCCESS : SmsFrameworkErrorCodeConstants.SMS_UNKNOWN;
    }

}
