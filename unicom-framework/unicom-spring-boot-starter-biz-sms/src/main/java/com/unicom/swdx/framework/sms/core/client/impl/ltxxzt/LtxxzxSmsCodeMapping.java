package com.unicom.swdx.framework.sms.core.client.impl.ltxxzt;

import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.unicom.swdx.framework.sms.core.client.SmsCodeMapping;
import com.unicom.swdx.framework.sms.core.enums.SmsFrameworkErrorCodeConstants;

/**
 * 阿里云的 SmsCodeMapping 实现类
 *
 * 参见 https://help.aliyun.com/document_detail/101346.htm 文档
 *
 * <AUTHOR>
public class LtxxzxSmsCodeMapping implements SmsCodeMapping {

    @Override
    public ErrorCode apply(String apiCode) {
        switch (apiCode) {
            case "0": return GlobalErrorCodeConstants.SUCCESS;

            case "99": return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_PARAM_ERROR;

        }
        return SmsFrameworkErrorCodeConstants.SMS_SEND_ERROR;
    }

}
