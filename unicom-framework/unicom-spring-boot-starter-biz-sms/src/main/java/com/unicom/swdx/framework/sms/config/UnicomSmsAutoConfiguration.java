package com.unicom.swdx.framework.sms.config;


import com.unicom.swdx.framework.sms.core.client.SmsClientFactory;
import com.unicom.swdx.framework.sms.core.client.impl.SmsClientFactoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 *
 * <AUTHOR>
 */
@Configuration
public class UnicomSmsAutoConfiguration {

    @Bean
    public SmsClientFactory smsClientFactory() {
        return new SmsClientFactoryImpl();
    }

}
