package com.unicom.swdx.framework.sms.core.client.impl.ltxxzt;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.common.core.KeyValue;
import com.unicom.swdx.framework.sms.core.client.SmsCommonResult;
import com.unicom.swdx.framework.sms.core.client.dto.LtMessageVo;
import com.unicom.swdx.framework.sms.core.client.dto.SmsReceiveRespDTO;
import com.unicom.swdx.framework.sms.core.client.dto.SmsSendRespDTO;
import com.unicom.swdx.framework.sms.core.client.dto.SmsTemplateRespDTO;
import com.unicom.swdx.framework.sms.core.client.impl.AbstractSmsClient;
import com.unicom.swdx.framework.sms.core.property.SmsChannelProperties;
import lombok.extern.slf4j.Slf4j;
import javax.net.ssl.*;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 消息中心客户端的实现类
 *
 * <AUTHOR>
 * @since 2023/5/30 15:14
 */
@Slf4j
public class LtxxzxSmsClient extends AbstractSmsClient {




    public LtxxzxSmsClient(SmsChannelProperties properties) {
        super(properties, new LtxxzxSmsCodeMapping());
        Assert.notEmpty(properties.getApiKey(), "apiKey 不能为空");
        Assert.notEmpty(properties.getApiSecret(), "apiSecret 不能为空");
    }

    @Override
    protected void doInit() {

    }




    @Override
    protected SmsCommonResult<SmsSendRespDTO> doSendSms(Long sendLogId, String mobile, String content, List<KeyValue<String, Object>> templateParams) throws Throwable {






        String param = "CpName=" + "fzgj" + "&CpPassword=" + "220324" + "&DesMobile=" + mobile
                + "&Content=" + content + "&ExtCode=" + "10010";

        String request =  HttpUtil.post("http://qxt.fungo.cn/Recv_center" , param);

        LtMessageVo  tubanInforVo = JSONUtil.toBean(request , LtMessageVo.class);

        if(tubanInforVo==null){
            return SmsCommonResult.build("500",null,null,null,codeMapping);
        }

        return  SmsCommonResult.build(tubanInforVo.getCode(),null,null,null,codeMapping);

    }

    @Override
    protected List<SmsReceiveRespDTO> doParseSmsReceiveStatus(String text) throws Throwable {
        return null;
    }

    @Override
    protected SmsCommonResult<SmsTemplateRespDTO> doGetSmsTemplate(String apiTemplateId) throws Throwable {
        return null;
    }

    public static SSLSocketFactory getSSLSocketFactory() {
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, getTrustManager(), new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    //获取TrustManager
    private static TrustManager[] getTrustManager() {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
        return trustAllCerts;
    }

    //获取HostnameVerifier
    private static HostnameVerifier getHostnameVerifier() {
        return (s, sslSession)->true;
    }

    private static X509TrustManager getX509TrustManager() {
        X509TrustManager trustManager=null;
        try {
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init((KeyStore) null);
            TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
            if (trustManagers.length !=1 || !(trustManagers[0] instanceof X509TrustManager)) {
                throw new IllegalStateException("Unexpected default trust managers:" + Arrays.toString(trustManagers));
            }
            trustManager = (X509TrustManager) trustManagers[0];
        } catch (Exception e) {
            log.error("错误为：", e);
        }
        return trustManager;
    }






}
