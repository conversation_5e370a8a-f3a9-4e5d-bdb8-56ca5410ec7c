package com.unicom.swdx.framework.idempotent.config;

import com.unicom.swdx.framework.idempotent.core.aop.IdempotentAspect;
import com.unicom.swdx.framework.idempotent.core.keyresolver.impl.DefaultIdempotentKeyResolver;
import com.unicom.swdx.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import com.unicom.swdx.framework.idempotent.core.keyresolver.IdempotentKeyResolver;
import com.unicom.swdx.framework.idempotent.core.redis.IdempotentRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import com.unicom.swdx.framework.redis.config.UnicomRedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(UnicomRedisAutoConfiguration.class)
public class UnicomIdempotentConfiguration {

    @Bean
    public IdempotentAspect idempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
        return new IdempotentAspect(keyResolvers, idempotentRedisDAO);
    }

    @Bean
    public IdempotentRedisDAO idempotentRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new IdempotentRedisDAO(stringRedisTemplate);
    }

    // ========== 各种 IdempotentKeyResolver Bean ==========

    @Bean
    public DefaultIdempotentKeyResolver defaultIdempotentKeyResolver() {
        return new DefaultIdempotentKeyResolver();
    }

    @Bean
    public ExpressionIdempotentKeyResolver expressionIdempotentKeyResolver() {
        return new ExpressionIdempotentKeyResolver();
    }

}
