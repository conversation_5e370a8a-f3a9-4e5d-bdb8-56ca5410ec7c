package com.unicom.swdx.module.infra.controller.admin.file;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import sun.font.FontDesignMetrics;


import java.awt.*;
import java.awt.image.BufferedImage;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
public class WaterMakeUtils {

    /**
     * 加图片水印
     *
     * @param bufImg  --BufferedImage  用来画图的宽高跟需要加水印的图片一样的空白图
     * @param img --需要加水印的图片
     * @param markImg --水印图片
     * @param width	--水印图片宽
     * @param height --水印图片高
     * @param x	--水印相对于底片的x轴坐标(PS:左上角为(0,0))
     * @param y  --水印相对于底片的y轴坐标(PS:左上角为(0,0))
     */
    public static void markPic(BufferedImage bufImg, Image img, Image markImg, int width, int height, int x, int y) {
        //取到画笔
        Graphics2D g = bufImg.createGraphics();
        //画底片
        g.drawImage(img, 0, 0, bufImg.getWidth(), bufImg.getHeight(), null);
        //画水印位置
        g.drawImage(markImg, x, y, width, height, null);
        g.dispose();
    }


    /**
     * 加文字水印
     * @param bufImg --BufferedImage  用来画图的宽高跟需要加水印的图片一样的空白图
     * @param img --需要加水印的图片
     * @param pretext --水印文字
     * @param font --字体
     * @param color --颜色
     * @param x  --水印相对于底片的x轴坐标(PS:左上角为(0,0))
     */
    public static void markWord(BufferedImage bufImg, Image img, List<String> pretext, Font font, Color color, int x) {
        //取到画笔
        Graphics2D g = bufImg.createGraphics();
        //画底片
        g.drawImage(img, 0, 0, bufImg.getWidth(), bufImg.getHeight(), null);

        g.setColor(new Color(255, 255, 255, 0)); // 透明白色
        g.fillRect(0, bufImg.getWidth() - 50, bufImg.getHeight(), 50);
        g.setColor(color);
        g.setFont(font);

        FontMetrics fm=  g.getFontMetrics();
        int width=0 , height =0;



        List<String> text = new ArrayList();


        pretext.forEach(s -> {
            text.addAll(countlenAndSplit(s , fm , bufImg.getWidth() -2* x - font.getSize() ));
        });

        for (int i = 0; i < text.size(); i++) {
            width = Math.max(width, g.getFontMetrics().stringWidth(text.get(i)));
            height = height + font.getSize();
        }
        //位置
        width =width +font.getSize();
        height =height +font.getSize()  +  font.getSize()/2  * (text.size()-1);


        int y = bufImg.getHeight() - height -font.getSize();

        g.setColor(new Color(0,0,0 ,88));
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.fillRoundRect(x,  y, width, height , 10, 10);


        g.setColor(color);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        for (int i = 0; i < text.size(); i++) {

            float textBaseY = font.getSize()/2+(Math.abs(fm.getAscent())-fm.getDescent())/2;
            //调用画文字水印的方法
            int left = 0;

            left = (int) (y + (font.getSize()+ font.getSize()/2) *i + font.getSize()/2 + textBaseY) ;//字宽一半做为行间距

            g.drawString(text.get(i), x+font.getSize()/2, left);



        }

        g.dispose();
    }

    private static List<String> countlenAndSplit(String s, FontMetrics fm, int width) {

        List<String> strs = new ArrayList<>();
        if(StrUtil.isEmpty(s)){
            return strs;
        }



        int begin =0;
        int index =0;

        while(index != s.length()  ){

            String temp = s.substring(begin ,index );
            if(fm.stringWidth(temp)> width){
                strs.add( s.substring(begin ,index-1));
                begin =index-1;
            }
            index ++ ;
        }

        strs.add(s.substring(begin));

        return strs;
    }





}
