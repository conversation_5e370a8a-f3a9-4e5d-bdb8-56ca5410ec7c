package com.unicom.swdx.module.infra.mq.producer.file;

import com.unicom.swdx.framework.mq.core.bus.AbstractBusProducer;
import com.unicom.swdx.module.infra.mq.message.file.FileConfigRefreshMessage;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 文件配置相关消息的 Producer
 */
@Component
public class FileConfigProducer extends AbstractBusProducer {

    /**
     * 发送 {@link FileConfigRefreshMessage} 消息
     */
    @Async
    public void sendFileConfigRefreshMessage() {
        publishEvent(new FileConfigRefreshMessage(this, selfDestinationService(), selfDestinationService()));
    }

}
