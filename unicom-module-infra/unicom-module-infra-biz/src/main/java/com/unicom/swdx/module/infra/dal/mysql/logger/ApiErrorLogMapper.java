package com.unicom.swdx.module.infra.dal.mysql.logger;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.infra.controller.admin.logger.vo.apierrorlog.ApiErrorLogExportReqVO;
import com.unicom.swdx.module.infra.controller.admin.logger.vo.apierrorlog.ApiErrorLogPageReqVO;
import com.unicom.swdx.module.infra.dal.dataobject.logger.ApiErrorLogDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * API 错误日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiErrorLogMapper extends BaseMapperX<ApiErrorLogDO> {

    default PageResult<ApiErrorLogDO> selectPage(ApiErrorLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ApiErrorLogDO>()
                .eqIfPresent(ApiErrorLogDO::getUserType, reqVO.getUserType())
                .betweenIfPresent(ApiErrorLogDO::getExceptionTime, reqVO.getExceptionTime())
                .eqIfPresent(ApiErrorLogDO::getProcessStatus, reqVO.getProcessStatus())
                .and(StrUtil.isNotBlank(reqVO.getApplicationName()), l ->
                        l.like(ApiErrorLogDO::getApplicationName, reqVO.getApplicationName())
                                .or().like(ApiErrorLogDO::getUserId,reqVO.getApplicationName())
                                .or().like(ApiErrorLogDO::getRequestUrl,reqVO.getApplicationName()))
                .eq(ApiErrorLogDO::getTenantId , SecurityFrameworkUtils.getTenantId())
                .orderByDesc(ApiErrorLogDO::getId)
        );
    }

    default List<ApiErrorLogDO> selectList(ApiErrorLogExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ApiErrorLogDO>()
                .eqIfPresent(ApiErrorLogDO::getUserType, reqVO.getUserType())
                .betweenIfPresent(ApiErrorLogDO::getExceptionTime, reqVO.getExceptionTime())
                .eqIfPresent(ApiErrorLogDO::getProcessStatus, reqVO.getProcessStatus())
                .and(StrUtil.isNotBlank(reqVO.getApplicationName()), l ->
                    l.like(ApiErrorLogDO::getApplicationName, reqVO.getApplicationName())
                            .or().eq(ApiErrorLogDO::getUserId,reqVO.getApplicationName())
                            .or().like(ApiErrorLogDO::getRequestUrl,reqVO.getApplicationName())
                )
                .eq(ApiErrorLogDO::getTenantId , SecurityFrameworkUtils.getTenantId())
                .orderByDesc(ApiErrorLogDO::getId)
        );
    }

}
