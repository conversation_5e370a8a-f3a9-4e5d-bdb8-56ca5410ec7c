package com.unicom.swdx.module.infra;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 项目的启动类
 *
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
public class InfraServerApplication {

    public static void main(String[] args) {


        SpringApplication.run(InfraServerApplication.class, args);


    }

}
