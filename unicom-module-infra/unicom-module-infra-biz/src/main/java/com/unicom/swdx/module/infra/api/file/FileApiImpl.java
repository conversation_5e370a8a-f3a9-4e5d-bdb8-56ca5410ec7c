package com.unicom.swdx.module.infra.api.file;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.infra.api.file.dto.FileCreateReqDTO;
import com.unicom.swdx.module.infra.api.file.dto.FileUpateReqDTO;
import com.unicom.swdx.module.infra.api.file.dto.FileUploadReqDTO;
import com.unicom.swdx.module.infra.dal.dataobject.file.FileDO;
import com.unicom.swdx.module.infra.service.file.FileService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;



    @Override
    public CommonResult<String> createFile(FileCreateReqDTO createReqDTO) {
        return success(fileService.createFile(createReqDTO.getName(), createReqDTO.getPath(),
                createReqDTO.getContent()));
    }

    @Override
    public CommonResult<String> uploadFile(FileUploadReqDTO uploadReqDTO) {
        return success(fileService.createFile(uploadReqDTO.getName(), null,
                uploadReqDTO.getContent(),uploadReqDTO.getModule()));
    }


    @Override
    public CommonResult<Boolean> uploadUpdateUploadFile(@Valid FileUploadReqDTO uploadReqDTO) {
        return success(fileService.updateFile(uploadReqDTO.getName(), uploadReqDTO.getPath(),
                uploadReqDTO.getContent(),uploadReqDTO.getModule()));
    }

    @Override
    public CommonResult<Long> getConfigId(String path) {
        return success(fileService.getFileConfigId(path));
    }

    @Override
    public CommonResult<byte[]> downloadFile(String path,String configId) {
        try {
            return success(fileService.getFileContent(Long.parseLong(configId), path));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public CommonResult<byte[]> getByte(String url) {
        return success(getByteFromUrl(url));
    }

    @Override
    public CommonResult<List<byte[]>> getByteList(List<String> urls) {
        List<byte[]> list = new ArrayList<>();
        urls.forEach(url->{
            list.add(getByteFromUrl(url));
        });
        return success(list);
    }

    @Override
    public CommonResult<Map<Integer, List<byte[]>>> getByteMap(Map<String, List<String>> urlMap) {
        Map<Integer, List<byte[]>> map = new HashMap<>();
        urlMap.forEach((k,v)->{
            List<byte[]> list = new ArrayList<>();
            v.forEach(url->{
                list.add(getByteFromUrl(url));
            });
            map.put(Integer.parseInt(k),list);
        });
        return success(map);
    }

    public byte[] getByteFromUrl(String url){
        String path = StrUtil.subAfter(url, "/get/", false);
        String configId = StrUtil.subBetween(url, "/get/", "/file/");
        if(StrUtil.isEmpty(path)){
            return null;
        }
        if(StrUtil.isEmpty(configId)){
            configId = "1";
        }
        try {
            return fileService.getFileContent(Long.parseLong(configId), path);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public CommonResult<String> getFileUrl(String path) {
        try {
            String fileUrl = fileService.getFileUrl(path);
            return success(fileUrl);
        } catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public CommonResult<String> getFilesByPath(String path) {
        try {
            String fileUrl = fileService.getFilesByPath(path);
            return success(fileUrl);
        } catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public CommonResult<Boolean> uploadUpdateUploadFileName(@Valid FileUpateReqDTO uploadReqDTO) {
        FileDO fileDO = fileService.getFileByPath(uploadReqDTO.getPath());
        fileDO.setName(uploadReqDTO.getName());
        fileService.updateFile(fileDO);
        return success(true);
    }
}
