package com.unicom.swdx.module.infra.service.logger;

import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.infra.api.logger.dto.ApiErrorLogCreateReqDTO;
import com.unicom.swdx.module.infra.controller.admin.logger.vo.apierrorlog.ApiErrorLogExportReqVO;
import com.unicom.swdx.module.infra.controller.admin.logger.vo.apierrorlog.ApiErrorLogPageReqVO;
import com.unicom.swdx.module.infra.convert.logger.ApiErrorLogConvert;
import com.unicom.swdx.module.infra.dal.dataobject.logger.ApiErrorLogDO;
import com.unicom.swdx.module.infra.dal.mysql.logger.ApiErrorLogMapper;
import com.unicom.swdx.module.infra.enums.ErrorCodeConstants;
import com.unicom.swdx.module.infra.enums.logger.ApiErrorLogProcessStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API 错误日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApiErrorLogServiceImpl implements ApiErrorLogService {

    @Resource
    private ApiErrorLogMapper apiErrorLogMapper;

    @Override
    public void createApiErrorLog(ApiErrorLogCreateReqDTO createDTO) {
        ApiErrorLogDO apiErrorLog = ApiErrorLogConvert.INSTANCE.convert(createDTO);
        apiErrorLog.setProcessStatus(ApiErrorLogProcessStatusEnum.INIT.getStatus());
        apiErrorLog.setTenantId(SecurityFrameworkUtils.getTenantId());
        apiErrorLogMapper.insert(apiErrorLog);
    }

    @TenantIgnore
    @Override
    public PageResult<ApiErrorLogDO> getApiErrorLogPage(ApiErrorLogPageReqVO pageReqVO) {
        return apiErrorLogMapper.selectPage(pageReqVO);
    }

    @TenantIgnore
    @Override
    public List<ApiErrorLogDO> getApiErrorLogList(ApiErrorLogExportReqVO exportReqVO) {
        return apiErrorLogMapper.selectList(exportReqVO);
    }

    @TenantIgnore
    @Override
    public void updateApiErrorLogProcess(Long id, Integer processStatus, Long processUserId) {
        ApiErrorLogDO errorLog = apiErrorLogMapper.selectById(id);
        if (errorLog == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.API_ERROR_LOG_NOT_FOUND);
        }
        if (!ApiErrorLogProcessStatusEnum.INIT.getStatus().equals(errorLog.getProcessStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.API_ERROR_LOG_PROCESSED);
        }
        // 标记处理
        apiErrorLogMapper.updateById(ApiErrorLogDO.builder().id(id).processStatus(processStatus)
                .processUserId(processUserId).processTime(LocalDateTime.now()).build());
    }

}
