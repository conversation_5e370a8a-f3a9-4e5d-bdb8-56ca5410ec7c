package com.unicom.swdx.module.infra.controller.admin.file.vo.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "管理后台 - 下载文件 Request VO")
@Data
public class FileDownloadReqVO {

    @ApiModelProperty(value = "文件配置", required = true)
    private Long configId;

    @ApiModelProperty(value = "文件附件路径数组")
    private List<String> path;

}
