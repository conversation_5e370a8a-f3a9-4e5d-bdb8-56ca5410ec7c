package com.unicom.swdx.module.infra.controller.admin.file.vo.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value = "管理后台 - 文件 Response VO", description = "不返回 content 字段，太大")
@Data
public class FileRespVO {

    @ApiModelProperty(value = "文件编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "原文件名", required = true, example = "unicom.png")
    private String name;

    @ApiModelProperty(value = "文件路径", required = true, example = "unicom.jpg")
    private String path;

    @ApiModelProperty(value = "文件 URL", required = true, example = "https://www.iocoder.cn/unicom.jpg")
    private String url;

    @ApiModelProperty(value = "文件类型", example = "jpg")
    private String type;

    @ApiModelProperty(value = "文件大小", example = "2048", required = true)
    private Integer size;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

}
