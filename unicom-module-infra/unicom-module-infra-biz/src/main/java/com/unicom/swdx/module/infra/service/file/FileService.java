package com.unicom.swdx.module.infra.service.file;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import com.unicom.swdx.module.infra.dal.dataobject.file.FileDO;

import java.io.InputStream;

/**
 * 文件 Service 接口
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 获得文件分页
     *
     * @param pageReqVO 分页查询
     * @return 文件分页
     */
    PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO);

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 原文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content);

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 原文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @param module 模块
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content,String module);


    FileDO createMutilFile(String name, String path, byte[] content,String module);

    /**
     * 更新文件
     * @param name
     * @param path
     * @param content
     * @param module
     * @return
     */
    Boolean updateFile(String name, String path, byte[] content,String module);

    /**
     * 删除文件
     *
     * @param id 编号
     */
    void deleteFile(Long id) throws Exception;

    /**
     * 获得文件内容
     *
     * @param configId 配置编号
     * @param path 文件路径
     * @return 文件内容
     */
    byte[] getFileContent(Long configId, String path) throws Exception;

    String getFileType(String path);

    String getFileName(String path);

    Long getFileConfigId(String path);

    String getFileUrl(String path);

    String getFilesByPath(String path);

    InputStream getFileByteContent(Long configId, String path);

    String getFileOriginalName(Long configId, String path);

    FileDO getFileByPath(String path);

    void updateFile(FileDO fileDO);
}
