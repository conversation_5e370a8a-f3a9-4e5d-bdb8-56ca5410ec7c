package com.unicom.swdx.module.infra.controller.admin.minio;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.file.config.BucketPolicyConfigDto;
import com.unicom.swdx.framework.file.config.MinioUploadDto;
import com.unicom.swdx.framework.file.core.utils.FileTypeUtils;
import io.minio.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;


/**
 * MinIO对象存储管理
 * Created by macro on 2019/12/25.
 */

@Api(tags = "MinioController", description = "MinIO对象存储管理" ,hidden = true)
@RequestMapping("/infra/minio")
@RestController
@Validated
@Slf4j
public class MinioController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MinioController.class);
    @Value("${oss.endpoint}")
    private String ENDPOINT;
    @Value("${oss.bucket-name}")
    private String BUCKET_NAME;
    @Value("${oss.access-key}")
    private String ACCESS_KEY;
    @Value("${oss.secret-key}")
    private String SECRET_KEY;

    @Value("${oss.proxy_addr}")
    private String proxy_addr;


    private static final String[] ALLOWED_EXTENSIONS = {
            // 图像文件
            "jpg", "png", "jpeg", "bmp", "svg", "gif",
            // 文档文件
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            // 文本文件
            "txt",

            // GIF 相关类型
            "image/gif", // 标准 GIF MIME 类型
            "image/x-xbitmap", // XBM 格式的 GIF 变种
            "image/x-xpixmap", // XPM 格式的 GIF 变种

            "text/plain", "text/csv" , "image/jpeg" ,"image/png","application/pdf" ,"application/msword" ,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",

            "application/vnd.ms-excel" ,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint" ,"application/vnd.openxmlformats-officedocument.presentationml.presentation",

            "application/rtf",

            "application/zip" ,"application/vnd.rar","application/x-7z-compressed" ,"application/gzip" , "application/x-tar"
    };

    private boolean isAllowedExtension(String fileExtension) {
        for (String allowedExtension : ALLOWED_EXTENSIONS) {
            if (allowedExtension.equals(fileExtension)) {
                return true;
            }
        }
        return false;
    }


    @ApiOperation("文件上传")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<MinioUploadDto> upload(@RequestParam("file") MultipartFile file , @RequestParam(required = false) String type) {

        String fileName = file.getOriginalFilename();




        int dotIndex = fileName.lastIndexOf('.');
        String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();



        try {
            fileExtension = FileTypeUtils.TIKA.get().detect(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (!isAllowedExtension(fileExtension)) {
            return CommonResult.error(500,"文件类型不合法");
        }


        try {
//            if(StrUtil.isNotEmpty(type)){
//                file = addWorkMarkToMutipartFile(file , type);
//            }


            //创建一个MinIO的Java客户端
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(ENDPOINT)
                    .credentials(ACCESS_KEY,SECRET_KEY)
                    .build();
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(BUCKET_NAME).build());
            if (isExist) {
                LOGGER.info("存储桶已经存在！");
            } else {
                //创建存储桶并设置只读权限
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(BUCKET_NAME).build());
                BucketPolicyConfigDto bucketPolicyConfigDto = createBucketPolicyConfigDto(BUCKET_NAME);
                SetBucketPolicyArgs setBucketPolicyArgs = SetBucketPolicyArgs.builder()
                        .bucket(BUCKET_NAME)
                        .config(JSONUtil.toJsonStr(bucketPolicyConfigDto))
                        .build();
                minioClient.setBucketPolicy(setBucketPolicyArgs);
            }
            String filename = file.getOriginalFilename();

            // 设置存储对象名称
            String objectName = UUID.randomUUID() + "/" + filename;

            // 使用putObject上传一个文件到存储桶中
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(BUCKET_NAME)
                    .object(objectName)
                    .contentType(file.getContentType())
                    .stream(file.getInputStream(), file.getSize(), ObjectWriteArgs.MIN_MULTIPART_SIZE).build();
            minioClient.putObject(putObjectArgs);
            LOGGER.info("文件上传成功!");
            MinioUploadDto minioUploadDto = new MinioUploadDto();
            minioUploadDto.setName(filename);
            minioUploadDto.setUrl( proxy_addr + "/" + BUCKET_NAME + "/" + objectName);
            return CommonResult.success(minioUploadDto);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("上传发生错误: {}！", e.getMessage());
        }
        return CommonResult.error(500 , "文件上传失败" );
    }

    private BucketPolicyConfigDto createBucketPolicyConfigDto(String bucketName) {
        BucketPolicyConfigDto.Statement statement = BucketPolicyConfigDto.Statement.builder()
                .Effect("Allow")
                .Principal("*")
                .Action("s3:GetObject")
                .Resource("arn:aws:s3:::"+bucketName+"/*.**").build();
        return BucketPolicyConfigDto.builder()
                .Version("2012-10-17")
                .Statement(CollUtil.toList(statement))
                .build();
    }

    @ApiOperation("文件删除")
        @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult delete(@RequestParam("objectName") String objectName) {
        try {
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(ENDPOINT)
                    .credentials(ACCESS_KEY,SECRET_KEY)
                    .build();
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(BUCKET_NAME).object(objectName).build());
            return CommonResult.success(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CommonResult.error(500 ,"删除失败");
    }



//    /**
//     * 直接给multipartFile加上文字水印再进行保存图片的操作方便省事
//     *
//     * @param multipartFile
//     *            文件上传的对象
//     * @param
//     *             如果是相对路径请使用相对路径new Image的方法,此处用的是url
//     * @return
//     * @throws IOException
//     */
//    public static MultipartFile addWorkMarkToMutipartFile(MultipartFile multipartFile,
//                                                          String word) throws IOException {
//        // 获取图片文件名 xxx.png xxx
//        String originFileName = multipartFile.getOriginalFilename();
//        // 获取原图片后缀 png
//        int lastSplit = originFileName.lastIndexOf(".");
//        String suffix = originFileName.substring(lastSplit + 1);
//        // 获取图片原始信息
//        String dOriginFileName = multipartFile.getOriginalFilename();
//        String dContentType = multipartFile.getContentType();
//        // 是图片且不是gif才加水印
//        if (!suffix.equalsIgnoreCase("gif") && dContentType.contains("image")) {
//            // 获取水印图片
//            InputStream inputImg = multipartFile.getInputStream();
//            Image img = ImageIO.read(inputImg);
//            // 加图片水印
//            int imgWidth = img.getWidth(null);
//            int imgHeight = img.getHeight(null);
//
//            LOGGER.info("图片宽度: {}！", imgWidth);
//            LOGGER.info("图片高度: {}！", imgHeight);
//            BufferedImage bufImg = new BufferedImage(imgWidth, imgHeight,
//                    BufferedImage.TYPE_INT_RGB);
//
//            int strsize =20;
//
//            if((imgHeight>1000&&imgHeight<=3000) || (imgWidth>1000&&imgWidth<=3000)){
//                 strsize =27;
//            }else if(imgHeight>3000&&imgHeight<=5000||imgWidth>3000&&imgWidth<=5000 ){
//                strsize =64;
//            }else if(imgHeight>5000 || imgWidth>5000){
//                strsize =74;
//            }
//            LOGGER.info("字宽: {}！", strsize);
//            //设置字体
//            Font font = new Font(null, Font.PLAIN, strsize);
//
//            String keyword ="网络异常，暂无地址信息";
////            if(word.contains(keyword)){
////
////                String latitude =subString(word , "经度：" , ", 纬度").trim();
////                String  longtitude=subString(word , "纬度：" , "N").trim();
////
////                if(StrUtil.isNotEmpty(latitude)&&StrUtil.isNotEmpty(latitude)){
////                    String url = "https://apis.map.qq.com/ws/geocoder/v1?location=";
////                    String urlend ="&get_poi=0&key=OA4BZ-FX43U-E5VV2-45M6S-C4HD3-NIFFI&output=json";
////                    RestTemplate restTemplate=new RestTemplate();
////                    AddressDto result = null;
////                    try {
////                        result = restTemplate.getForObject(new URI(url+ longtitude+","+ latitude + urlend   ) ,
////                                AddressDto.class );
////                    } catch (URISyntaxException e) {
////                        throw new RuntimeException(e);
////                    }
////
////                    word =  word.replace(keyword, result.getResult().getAddress());
////
////                    log.info(word);
////
////
////                }
////
////
////            }
//
//            String[] wordsplit = word.split("/");
//
//            List temp = Stream.of(wordsplit).collect(Collectors.toList());
//
//
//
////            WaterMakeUtils.markWord(bufImg, img,  temp , font , Color.white, strsize);
//
//            ByteArrayOutputStream bs = new ByteArrayOutputStream();
//            ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
//            ImageIO.write(bufImg, suffix, imOut);
//            InputStream is = new ByteArrayInputStream(bs.toByteArray());
//
//            // 加水印后的文件上传
//            multipartFile = new MockMultipartFile(dOriginFileName, dOriginFileName, dContentType,
//                    is);
//        }
//        //返回加了水印的上传对象
//        return multipartFile;
//    }



//    public static String subString(String str, String strStart, String strEnd) {
//
//        /* 找出指定的2个字符在 该字符串里面的 位置 */
//        int strStartIndex = str.indexOf(strStart);
//        int strEndIndex = str.indexOf(strEnd);
//
//        /* index 为负数 即表示该字符串中 没有该字符 */
//        if (strStartIndex < 0) {
//            return null;
//        }
//        if (strEndIndex < 0) {
//            return null;
//        }
//        /* 开始截取 */
//        String result = str.substring(strStartIndex, strEndIndex).substring(strStart.length());
//        return result;
//    }

}
