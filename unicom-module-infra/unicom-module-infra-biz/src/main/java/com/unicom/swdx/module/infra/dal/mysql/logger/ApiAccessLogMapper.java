package com.unicom.swdx.module.infra.dal.mysql.logger;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.infra.controller.admin.logger.vo.apiaccesslog.ApiAccessLogExportReqVO;
import com.unicom.swdx.module.infra.controller.admin.logger.vo.apiaccesslog.ApiAccessLogPageReqVO;
import com.unicom.swdx.module.infra.dal.dataobject.logger.ApiAccessLogDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * API 访问日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiAccessLogMapper extends BaseMapperX<ApiAccessLogDO> {

    default PageResult<ApiAccessLogDO> selectPage(ApiAccessLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ApiAccessLogDO>()
                .eqIfPresent(ApiAccessLogDO::getUserType, reqVO.getUserType())
                .betweenIfPresent(ApiAccessLogDO::getBeginTime, reqVO.getBeginTime())
                .and(StrUtil.isNotBlank(reqVO.getApplicationName()), i -> {i
                        .like(ApiAccessLogDO::getUserId,reqVO.getApplicationName())
                        .or()
                        .like(ApiAccessLogDO::getApplicationName,reqVO.getApplicationName())
                        .or()
                        .like(ApiAccessLogDO::getRequestUrl,reqVO.getApplicationName())
                        .or()
                        .ge(reqVO.getApplicationName().matches("[0-9]+ms"),ApiAccessLogDO::getDuration,reqVO.getApplicationName().split("m")[0]);
                        })
                .eq(ApiAccessLogDO::getTenantId , SecurityFrameworkUtils.getTenantId())
                .orderByDesc(ApiAccessLogDO::getId)



        );


    }

    default List<ApiAccessLogDO> selectList(ApiAccessLogExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ApiAccessLogDO>()
                .eqIfPresent(ApiAccessLogDO::getUserType, reqVO.getUserType())
                .betweenIfPresent(ApiAccessLogDO::getBeginTime, reqVO.getBeginTime())
                .and(StrUtil.isNotBlank(reqVO.getApplicationName()), i -> {i
                        .eq(ApiAccessLogDO::getUserId,reqVO.getApplicationName())
                        .or()
                        .like(ApiAccessLogDO::getApplicationName,reqVO.getApplicationName())
                        .or()
                        .like(ApiAccessLogDO::getRequestUrl,reqVO.getApplicationName())
                        .or()
                        .ge(reqVO.getApplicationName().matches("[0-9]+ms"),ApiAccessLogDO::getDuration,reqVO.getApplicationName().split("m")[0]);
                })
                .eq(ApiAccessLogDO::getTenantId , SecurityFrameworkUtils.getTenantId())
                .orderByDesc(ApiAccessLogDO::getId)
        );
    }

}
