package com.unicom.swdx.module.infra.api.file.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel("RPC 服务 - 文件创建 Request DTO")
@Data
public class FileUploadReqDTO {

    @ApiModelProperty(value = "原文件名称", example = "xxx.png")
    private String name;

    @ApiModelProperty(value = "原文件路径", example = "xxx.png")
    private String path;

    @ApiModelProperty(value = "模块名",notes = "相当于增加一级路径", example = "xxx.png")
//    @NotBlank(message = "模块名不能为空")
    private String module;

    @ApiModelProperty(value = "文件内容", required = true)
    @NotEmpty(message = "文件内容不能为空")
    private byte[] content;

}
