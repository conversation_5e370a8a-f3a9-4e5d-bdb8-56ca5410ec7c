package com.unicom.swdx.module.infra.api.file.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("RPC 服务 - 文件创建 Request DTO")
@Data
public class FileUpateReqDTO {

    @ApiModelProperty(value = "原文件名称", example = "xxx.png")
    private String name;

    @ApiModelProperty(value = "原文件路径", example = "xxx.png")
    private String path;

}
