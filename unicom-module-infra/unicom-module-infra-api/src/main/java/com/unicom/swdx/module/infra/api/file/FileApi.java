package com.unicom.swdx.module.infra.api.file;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.infra.api.file.dto.FileCreateReqDTO;
import com.unicom.swdx.module.infra.api.file.dto.FileUpateReqDTO;
import com.unicom.swdx.module.infra.api.file.dto.FileUploadReqDTO;
import com.unicom.swdx.module.infra.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - 文件")
public interface FileApi {

    String PREFIX = ApiConstants.PREFIX + "/file";

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 原文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(@RequestParam("name") String name,
                              @RequestParam("path") String path,
                              @RequestParam("content") byte[] content) {
        return createFile(new FileCreateReqDTO().setName(name).setPath(path).setContent(content)).getCheckedData();
    }

    @PostMapping(PREFIX + "/create")
    @ApiOperation("保存文件，并返回文件的访问路径")
    CommonResult<String> createFile(@Valid @RequestBody FileCreateReqDTO createReqDTO);

    @PostMapping(PREFIX + "/upload")
    //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<String> uploadFile(@Valid @RequestBody FileUploadReqDTO uploadReqDTO);

    /**
     * 更新文档
     * @param uploadReqDTO
     * @return
     */
    @PostMapping(PREFIX + "/updateUpload")
    CommonResult<Boolean> uploadUpdateUploadFile(@Valid @RequestBody FileUploadReqDTO uploadReqDTO);

    /**
     * 获得文件服务配置ID
     * @param path
     * @return
     */
    @GetMapping(PREFIX + "/getConfigId")
    CommonResult<Long> getConfigId(@RequestParam("path") String path);


    @GetMapping(PREFIX + "/download")
        //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<byte[]> downloadFile(@RequestParam("path") String path,@RequestParam("configId") String configId);

    @GetMapping(PREFIX + "/getByte")
        //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<byte[]> getByte(@RequestParam("url") String url);

    @GetMapping(PREFIX + "/getByteList")
        //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<List<byte[]>> getByteList(@RequestParam("urls") List<String> urls);

    @PostMapping(PREFIX + "/getByteMap")
        //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<Map<Integer, List<byte[]>>> getByteMap(@RequestBody Map<String, List<String>> urlMap);

    @GetMapping(PREFIX + "/selectFileDO")
        //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<String> getFileUrl(@RequestParam("path") String path);


    @GetMapping(PREFIX + "/selectFiles")
        //@ApiOperation("上传文件，并返回文件的访问路径")//避免记录日志
    CommonResult<String> getFilesByPath(@RequestParam("path") String path);

    /**
     * 更新文档
     * @param uploadReqDTO
     * @return
     */
    @PostMapping(PREFIX + "/updateUploadName")
    CommonResult<Boolean> uploadUpdateUploadFileName(@Valid @RequestBody FileUpateReqDTO uploadReqDTO);

}
