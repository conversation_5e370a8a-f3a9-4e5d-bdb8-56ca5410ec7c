package com.unicom.swdx.module.system.api.common.dto;


import com.unicom.swdx.module.system.enums.common.HolidayTypeEnum;
import lombok.Data;

import java.time.LocalDate;

/**
 * 万年历 Response DTO
 *
 */
@Data
public class CalendarWnlRespDTO {
    /**
     * 日期
     */
    private LocalDate gregorianDate;
    /**
     * 公历年
     */
    private Integer gregorianYear;
    /**
     * 公历月
     */
    private Integer gregorianMonth;
    /**
     * 公历日
     */
    private Integer gregorianDay;
    /**
     * 星期
     */
    private String weekDay;
    /**
     * 阴历年
     */
    private Integer lunarYear;
    /**
     * 阴历月
     */
    private String lunarMonth;
    /**
     * 阴历日
     */
    private String lunarDay;
    /**
     * 生肖
     */
    private String zodiac;
    /**
     * 闰月
     */
    private Integer leapMonth;
    /**
     * 年干支
     */
    private String yearBranch;
    /**
     * 月干支
     */
    private String monthBranch;
    /**
     * 日干支
     */
    private String dayBranch;
    /**
     * 节气
     */
    private String solarTerm;
    /**
     * 节气时间
     */
    private String solarTermTime;
    /**
     * 公历节日
     */
    private String gregorianFestival;
    /**
     * 农历节日
     */
    private String lunarFestival;
    /**
     * 特殊节日
     */
    private String specialFestivals;
    /**
     * 节假日类型，enum(0, 1, 2, 3),分别表示 工作日、周末、节日、调休
     * 枚举 {@link HolidayTypeEnum}
     */
    private Integer holidayType;
    /**
     * 节假日名称
     */
    private String holidayName;
}
