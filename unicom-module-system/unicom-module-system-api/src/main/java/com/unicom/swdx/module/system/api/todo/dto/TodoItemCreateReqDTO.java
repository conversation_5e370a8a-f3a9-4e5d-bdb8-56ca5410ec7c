package com.unicom.swdx.module.system.api.todo.dto;

import com.unicom.swdx.module.system.enums.home.SubSystemEnum;
import com.unicom.swdx.module.system.enums.home.TodoType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Set;

@ApiModel("业务中台 - 待办事项创建 Request VO")
@Data
@ToString(callSuper = true)
public class TodoItemCreateReqDTO {

    /**
     * {@link SubSystemEnum}
     */
    @ApiModelProperty(value = "发起子系统id（见枚举SubSystemEnum）", required = true)
    @NotNull(message = "发起子系统id不能为空")
    private Integer subsystemId;
    /**
     * {@link TodoType}
     */
    @ApiModelProperty(value = "待办类型（见枚举TodoType）", required = true)
    @NotNull(message = "待办类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "紧急程度")
    private String urgencyLevel;

    @ApiModelProperty(value = "跳转路径")
    private String path;

    @ApiModelProperty(value = "流程id", required = true)
    @NotNull(message = "流程id")
    private String processId;

    @ApiModelProperty(value = "流程类型")
    @NotBlank(message = "流程类型不能为空")
    private String processType;

    @ApiModelProperty(value = "任务节点")
    @NotBlank(message = "任务节点不能为空")
    private String taskCode;

    @ApiModelProperty(value = "流程状态")
    @NotNull(message = "流程状态不能为空")
    private Integer processStatus;

    @ApiModelProperty(value = "发起人userId", required = true)
    @NotNull(message = "发起人不能为空")
    private Long submitter;

    @ApiModelProperty(value = "处理人", required = true)
    @NotEmpty(message = "处理人不能为空")
    private Set<Long> todoUsers;

    @ApiModelProperty(value = "参会人是否需要回执 0:是 1:否")
    //@NotNull(message = "是否需要回执不能为空")
    private Boolean receipt;

    @ApiModelProperty(value = "开始时间", required = true)
    //@NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    //@NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "参会地点", required = true)
    //@NotBlank(message = "参会地点不能为空")
    private String meetingAddress;

    @ApiModelProperty(value = "标题", required = true)
    @NotBlank(message = "标题不能为空")
    private String title;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起时间", required = true)
    @NotNull(message = "发起时间不能为空")
    private LocalDateTime submitTime;

    @ApiModelProperty("收文来源机构id")
    private Long receivingSourceTenantId;

    @ApiModelProperty("政民互动来源")
    private String peopleIneractionSource;

}
