package com.unicom.swdx.module.system.api.region;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.region.dto.RegionDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/12 14:28
 **/
@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - 地区")
public interface RegionApi {

    String PREFIX = ApiConstants.PREFIX + "/region";

    @GetMapping(PREFIX + "/list")
    @ApiOperation("获得所有地区信息")
    CommonResult<List<RegionDTO>> getList();

}
