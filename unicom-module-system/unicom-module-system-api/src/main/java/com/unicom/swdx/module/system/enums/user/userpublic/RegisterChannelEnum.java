package com.unicom.swdx.module.system.enums.user.userpublic;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 注册渠道
 * <AUTHOR>
 * @date 2023/3/20 15:38
 **/
@Getter
@AllArgsConstructor
public enum RegisterChannelEnum {

    WX(0), // 微信小程序
    PC(1); // 公众PC端
    private final Integer channel;

    public boolean equals(Integer channel) {
        if (Objects.isNull(channel)) return false;
        return Objects.equals(this.getChannel(),channel);
    }

}
