package com.unicom.swdx.module.system.enums.sms;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/5/30 11:22
 **/
@Getter
@AllArgsConstructor
public enum SmsVerificationTypeEnum {

    REGISTER(0, "系统注册"), // 注册
    RESET_PASSWORD(1, "重置密码"), // 重置密码
    RESET_MOBILE(2, "重置手机号"), // 重置手机号
    LOGIN(3,"系统登录"); // 公众用户登录

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 模版id
     */
    private final String item;

    /**
     * 根据type获取枚举
     * @param code 类型
     * @return 枚举
     */
    public static SmsVerificationTypeEnum get(Integer code) {
        return Arrays.stream(SmsVerificationTypeEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst().orElse(null);
    }

}
