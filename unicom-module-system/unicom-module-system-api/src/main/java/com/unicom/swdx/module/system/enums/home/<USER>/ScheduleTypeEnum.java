package com.unicom.swdx.module.system.enums.home.schedule;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ScheduleTypeEnum {

    DEFAULT_TYPE(0,"");
//    MEETING(0,0),
//    ACTIVITY(3,1),
//    AFFAIRS(2,2),
//    OTHER(4,3),
//    CUSTOMIZE_MEETING(1,4);

    //类型
    private final Integer type;

    private final String name;

    public static ScheduleTypeEnum getByType(Integer type) {
        return ArrayUtil.firstMatch(scheduleTypeEnum -> scheduleTypeEnum.getType().equals(type),
                values());
    }
}
