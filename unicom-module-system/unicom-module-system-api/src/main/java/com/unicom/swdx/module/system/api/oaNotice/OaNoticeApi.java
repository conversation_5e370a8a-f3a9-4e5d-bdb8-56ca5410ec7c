package com.unicom.swdx.module.system.api.oaNotice;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - 一周工作安排")
public interface OaNoticeApi {
    String PREFIX = ApiConstants.PREFIX + "/oaNotice";

    @PostMapping(PREFIX + "/sendNotice")
    @ApiOperation(value = "发送单条短信给 Admin 用户", notes = "在 mobile 为空时，使用 userId 加载对应 Admin 的手机号")
    CommonResult<Long> sendNotice(@RequestParam("title") String title,
                                  @RequestParam("message") String message,
                                  @RequestParam("it") List<String> it);
}
