package com.unicom.swdx.module.system.api.todo;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemCreateReqDTO;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemUpdateReqDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


/**
 * 待办事项 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - 待办事项")
public interface TodoItemServiceApi {

    String PREFIX = ApiConstants.PREFIX + "/todo";

    /**
     * 创建待办事项
     *
     * @param reqDTO 创建信息
     * @return
     */
    @PostMapping(PREFIX + "/create")
    @ApiOperation("创建待办事项")
    CommonResult<Boolean> createTodoItem(@RequestBody TodoItemCreateReqDTO reqDTO);

    /**
     * 修改事项备注
     *
     * @param reqDTO 待办信息
     * @return
     */
    @PostMapping(PREFIX + "/update-remark")
    @ApiOperation("修改事项备注")
    CommonResult<Boolean> updateTodoItemRemark(@RequestBody TodoItemUpdateReqDTO reqDTO);

    /**
     * 已办事项
     *
     * @param reqDTO 待办信息
     * @return
     */
    @PostMapping(PREFIX + "/update")
    @ApiOperation("已办事项")
    CommonResult<Boolean> updateTodoItem(@RequestBody TodoItemUpdateReqDTO reqDTO);

    /**
     * 删除待办事项
     *
     * @param reqDTO 待办信息
     * @return
     */
    @PostMapping(PREFIX + "/delete")
    @ApiOperation("删除待办事项")
    CommonResult<Boolean> deleteTodoItem(@RequestBody TodoItemUpdateReqDTO reqDTO);

    /**
     * 删除待办事项
     *
     * @param reqDTO 待办信息
     * @return
     */
    @PostMapping(PREFIX + "/delete-without-user")
    @ApiOperation("删除待办事项 without todo user")
    CommonResult<Boolean> deleteTodoItemWithoutTodoUser(@RequestBody TodoItemUpdateReqDTO reqDTO);

    /**
     * 获取待办事项数目
     * @param userId 用户ID
     * @return
     */
    @GetMapping(PREFIX + "/num")
    @ApiOperation("获得待办事项数量")
    CommonResult<Integer> getOfficialdocNum(@RequestParam("id") Long userId);
}
