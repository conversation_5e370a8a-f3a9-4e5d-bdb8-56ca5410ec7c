package com.unicom.swdx.module.system.api.kafka.dto;

import lombok.Data;

@Data
public class OldKafkaPersonDTO extends OldKafkaBodyDTO{

    // "education":"大学本科毕业",
    // "mobile_phone":"15243670643",
    // "employee_id":"003c73fd7b1f26511eab285d39f7c63a093",
    // "sex":"男",
    // "edu_degree":"教育学学士学位",
    // "name":"罗松",
    // "dept_name":"离退休人员工作办公室",
    // "duty":"",
    // "staff_type_":"工勤技能岗位人员",
    // "dept_id":"001001017000",
    // "status":"在职"

    private Long userId;

    private String employee_id;

    private String name;

    private String mobile_phone;

    private String sex;

    private String education;

    private String edu_degree;

    private String dept_id;

    private Long new_dept_id;

    private String dept_name;

    private String duty;

    private String staff_type_;

    private String status;

}
