package com.unicom.swdx.module.system.api.permission;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 角色")
public interface RoleApi {

    String PREFIX = ApiConstants.PREFIX + "/role";

    @GetMapping(PREFIX + "/valid")
    @ApiOperation("校验角色是否合法")
    @ApiImplicitParam(name = "ids", value = "角色编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<Boolean> validRoles(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/getRoleIdByName")
    @ApiOperation("通过角色名称获取角色id")
    @ApiImplicitParam(name = "roleName", value = "角色名称", example = "2", required = true,dataTypeClass = String.class)
    CommonResult<Long> getRoleIdByName(@RequestParam("roleName") String roleName);

    @GetMapping(PREFIX + "/getInnerRoleIdByCode")
    @ApiOperation("通过角色code获得内置角色id")
    @ApiImplicitParam(name = "roleCode", value = "角色名称", example = "2", required = true,dataTypeClass = String.class)
    CommonResult<Long> getInnerRoleIdByCode(@RequestParam("roleCode") String roleCode);

    @GetMapping(PREFIX + "/getCustomRoleIdByCode")
    @ApiOperation("通过角色code获得内置角色id")
    @ApiImplicitParam(name = "roleCode", value = "角色名称", example = "2", required = true,dataTypeClass = String.class)
    CommonResult<Long> getCustomRoleIdByCode(@RequestParam("roleCode") String roleCode);

    @GetMapping(PREFIX + "/assignUserRole")
    @ApiOperation("分配用户角色")
//    @ApiImplicitParam(name = "reqVO", value = "职工账号信息",  required = true, dataTypeClass = AdminUserReqDTO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", example = "1", required = true,dataTypeClass = Long.class),
            @ApiImplicitParam(name = "roleIds", value = "角色ID", example = "1，2", required = true,dataTypeClass = Set.class)
    })
    CommonResult<Boolean> createRole(@RequestParam("userId") Long userId, @RequestParam("roleIds") Set<Long> roleIds);


}
