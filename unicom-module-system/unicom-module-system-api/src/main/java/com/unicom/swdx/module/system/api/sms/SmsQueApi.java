package com.unicom.swdx.module.system.api.sms;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 队列短信发送")
public interface SmsQueApi {

    String PREFIX = ApiConstants.PREFIX + "/sms/que";

    @PostMapping(PREFIX + "/sendque")
    @ApiOperation(value = "发送单条短信给 Admin 用户", notes = "在 mobile 为空时，使用 userId 加载对应 Admin 的手机号")
    CommonResult<Long> sendque(@Valid @RequestBody SmsSendReq req);

    @PostMapping(PREFIX + "/cancelQue")
    @ApiOperation(value = "取消短信发送")
    CommonResult<Long> cancelQue(@RequestParam("title") String title);

}
