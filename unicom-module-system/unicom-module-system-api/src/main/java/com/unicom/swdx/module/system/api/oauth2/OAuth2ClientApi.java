package com.unicom.swdx.module.system.api.oauth2;

import com.unicom.swdx.module.system.api.oauth2.dto.OAuth2ClientDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 16:24
 **/
@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - OAuth2.0 应用")
public interface OAuth2ClientApi {

    String PREFIX = ApiConstants.PREFIX + "/oauth2/client";

    @GetMapping(PREFIX + "/all")
    //@ApiOperation("获取所有的应用")
    List<OAuth2ClientDTO> getAll();

}
