package com.unicom.swdx.module.system.api.todo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

@ApiModel("业务中台 - 待办事项更新 Request VO")
@Data
@ToString(callSuper = true)
public class TodoItemUpdateReqDTO {

    @ApiModelProperty(value = "发起子系统id（见字典midoffice-subsystem）", required = true)
    @NotNull(message = "发起子系统id不能为空")
    private Integer subsystemId;

    @ApiModelProperty(value = "跳转路径", required = true)
    //@NotBlank(message = "跳转路径不能为空")
    private String path;

    @ApiModelProperty(value = "待办流程id", required = true)
    @NotBlank(message = "待办流程id")
    private String processId;

    @ApiModelProperty(value = "流程类型")
    @NotBlank(message = "流程类型不能为空")
    private String processType;

    @ApiModelProperty(value = "任务节点")
//    @NotBlank(message = "任务节点不能为空")
    private String taskCode;

    @ApiModelProperty(value = "流程状态")
//    @NotNull(message = "流程状态不能为空")
    private Integer processStatus;

    @ApiModelProperty(value = "处理人", required = true)
    @NotEmpty(message = "处理人不能为空")
    private Set<Long> todoUsers;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "紧急程度")
    private String urgencyLevel;

}
