package com.unicom.swdx.module.system.api.businesscenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.enums.GenderEnum;
import com.unicom.swdx.framework.common.enums.NationEnum;
import com.unicom.swdx.framework.common.enums.PoliticalOutlookEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Objects;

@Data
public class PersonnalRespDTO {
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotNull(message = "姓名不能为空")
    private String name;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idNumber;


    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    /**
     * 性别
     * 枚举 {@link GenderEnum}
     */
    @ApiModelProperty(value = "性别")
    private Integer gender;

    /**
     * 民族
     * 枚举 {@link NationEnum}
     */
    @ApiModelProperty(value = "民族")
    private Integer nation;

    /**
     * 政治面貌
     * 枚举 {@link PoliticalOutlookEnum}
     */
    @ApiModelProperty(value = "政治面貌")
    private Integer politicalStatus;

    /**
     * 来校年月
     */
    @ApiModelProperty(value = "来校年月")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate arrivalTime;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactInformation;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    private String mailingAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    private String email;

    /**
     * 所在单位
     */
    @ApiModelProperty(value = "所在单位")
    private String workUnit;

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private Integer education;

    /**
     * 学位
     */
    @ApiModelProperty(value = "学位")
    private Integer degree;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    private String professionalTitle;

    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    private String rank;

    /**
     * 行政级别
     */
    @ApiModelProperty(value = "行政级别")
    private String administrativeLevel;

    /**
     * 所属部门id
     */
    @ApiModelProperty(value = "所属部门id")
    private String deptIds;

    @ApiModelProperty(value = "所属部门名称")
    private String deptNames;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    private String otherSystemId;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "业中用户id")
    private Long userId;

    /**
     * 业中人事id
     */
    @ApiModelProperty(value = "业中人事id")
    private Long hrId;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PersonnalRespDTO that = (PersonnalRespDTO) o;
        return Objects.equals(otherSystemId, that.otherSystemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(otherSystemId);
    }
}
