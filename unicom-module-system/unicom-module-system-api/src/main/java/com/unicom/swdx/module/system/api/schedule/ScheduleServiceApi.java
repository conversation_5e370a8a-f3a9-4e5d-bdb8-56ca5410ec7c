package com.unicom.swdx.module.system.api.schedule;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - 日程Api")
public interface ScheduleServiceApi {

    String PREFIX = ApiConstants.PREFIX + "/schedule";

    @PostMapping(PREFIX + "/createScheduleOther")
    @ApiOperation("创建日程")
    CommonResult<Long> createScheduleOther(@RequestBody ScheduleDto scheduleDto);


    @PostMapping(PREFIX + "/createScheduleInfor")
    @ApiOperation("创建中心工作部分日程")
    CommonResult<Long> createScheduleInfor(@RequestBody ScheduleDto scheduleDto);

    @PostMapping(PREFIX + "/createSchedulesOther")
    @ApiOperation("创建为参与人和抄送人日程")
    CommonResult<Long> createSchedulesOther(@RequestBody ScheduleDto scheduleDto);

    @PostMapping(PREFIX + "/deleteScheduleOther")
    @ApiOperation("删除日程")
    CommonResult<Long> deleteScheduleOther(@RequestBody ScheduleDto scheduleDto);

    @PostMapping(PREFIX + "/deleteScheduleInfor")
    @ApiOperation("删除中心工作日程")
    CommonResult<Long> deleteScheduleInfor(@RequestBody ScheduleDto scheduleDto);
//
//    @PostMapping(PREFIX + "/delete-meeting-schedule")
//    @ApiOperation("删除会议日程")
//    @ApiImplicitParam(name = "processInstanceId", value = "编号", required = true, dataTypeClass = String.class)
//    CommonResult<Boolean> deleteSchedule(@RequestParam("processInstanceId") String processInstanceId);
//
//    @PostMapping(PREFIX + "/cancel-meeteing-schedule")
//    @ApiOperation("取消会议日程")
//    @ApiImplicitParam(name = "processInstanceId", value = "编号", required = true, dataTypeClass = String.class)
//    CommonResult<Integer> cancelMeetingSchedule(@RequestParam("processInstanceId") String processInstanceId);
//
//    @PostMapping(PREFIX + "/update-schedule-meeting-status")
//    @ApiOperation("更新日程会议状态")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "processInstanceId", value = "编号", required = true, dataTypeClass = String.class),
//            @ApiImplicitParam(name = "meetingStatus", value = "会议状态", required = true, dataTypeClass = Long.class)
//    })
//    CommonResult<Integer> updateScheduleMeetingStatus(@RequestParam("processInstanceId") String processInstanceId,
//                                                      @RequestParam("meetingStatus") String meetingStatus);
//
//    @PostMapping(PREFIX + "/update-meeteing-schedule")
//    @ApiOperation("更新会议日程")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "processInstanceId", value = "编号", required = true, dataTypeClass = String.class),
//            @ApiImplicitParam(name = "attendeeId", value = "参会人", required = true, dataTypeClass = Long.class),
//            @ApiImplicitParam(name = "isAttend", value = "参会回执", required = true, dataTypeClass = String.class)
//    })
//    CommonResult<Integer> updateMeetingSchedule(@RequestParam("processInstanceId") String processInstanceId,
//                                                @RequestParam("attendeeId") Long attendeeId,
//                                                @RequestParam("isAttend") String isAttend);
}
