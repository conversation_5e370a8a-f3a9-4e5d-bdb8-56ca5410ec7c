package com.unicom.swdx.module.system.api.sensitiveword;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFacx`tory =
@Api(tags = "RPC 服务 - 敏感词")
public interface SensitiveWordApi {

    String PREFIX = ApiConstants.PREFIX + "/sensitive-word";

    @GetMapping(PREFIX + "/validate-text")
    @ApiOperation("获得文本所包含的不合法的敏感词数组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "text", value = "文本", example = "傻瓜", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "tags", value = "标签数组", example = "product,life", required = true, allowMultiple = true,dataTypeClass = List.class)
    })
    CommonResult<List<String>> validateText(@RequestParam("text") String text,
                                            @RequestParam("tags") List<String> tags);

    @GetMapping(PREFIX + "/is-text-valid")
    @ApiOperation("判断文本是否包含敏感词")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "text", value = "文本", example = "傻瓜", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "tags", value = "标签数组", example = "product,life", required = true, allowMultiple = true,dataTypeClass = List.class)
    })
    CommonResult<Boolean> isTextValid(@RequestParam("text") String text,
                                      @RequestParam("tags") List<String> tags);

}
