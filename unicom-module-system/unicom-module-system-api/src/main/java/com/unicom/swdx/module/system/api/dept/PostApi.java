package com.unicom.swdx.module.system.api.dept;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 岗位")
public interface PostApi {

    String PREFIX = ApiConstants.PREFIX + "/post";

    @GetMapping(PREFIX + "/valid")
    @ApiOperation("校验岗位是否合法")
    @ApiImplicitParam(name = "ids", value = "岗位编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<Boolean> validPosts(@RequestParam("ids") Collection<Long> ids);

    /**
     * 获取用户对应岗位标识的排序最大的岗位
     * @param userId 用户id
     * @param codes 岗位标识
     * @return 最大岗位
     */
    @GetMapping("/getMaxSort")
    PostRespDTO getMaxSortPostByUser(@RequestParam("userId") Long userId, @RequestParam("codes") Collection<String> codes);

    /**
     * 获取用户对应岗位标识的排序最小（前）的岗位
     * @param userId 用户id
     * @param codes 岗位标识
     * @return 最大岗位
     */
    @GetMapping("/getMinSort")
    PostRespDTO getMinSortPostByUser(@RequestParam("userId") Long userId, @RequestParam("codes") Collection<String> codes);

    @GetMapping("/getUserByPost")
    List<Long> getUserByPost(@RequestParam("code") String code, @RequestParam(value = "tenantId",required = false) Long tenantId);

    @GetMapping("/isHRDirector")
    CommonResult<Boolean> isHRDirector(@RequestParam("loginUserId") Long loginUserId);

    @GetMapping("/getPostByCode")
    CommonResult<PostRespDTO> getPostByCode(@RequestParam("postCode")String postCode, @RequestParam(value = "tenantId",required = false)Long tenantId);

    @GetMapping("/getPostById")
    CommonResult<PostRespDTO> getPostById(@RequestParam("postId")Long postId);

    @GetMapping("/saveUserPost")
    void saveUserPost(@RequestParam("userId") Long userId, @RequestBody Set<Long> postIds);
}

