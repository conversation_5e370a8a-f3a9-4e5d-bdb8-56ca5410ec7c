package com.unicom.swdx.module.system.enums.sms;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum SmsMessageEnum {
    // 【湖南省委党校】 您的短信验证码是{},用于系统登录，10分钟之内有效

    REGISTER(0, "【湖南省委党校】 您的短信验证码是{},用于系统注册，10分钟之内有效"), // 注册
    RESET_PASSWORD(1, "【湖南省委党校】 您的短信验证码是{},用于重置密码，10分钟之内有效"), // 重置密码
    RESET_MOBILE(2, "【湖南省委党校】 您的短信验证码是{},用于重置手机号，10分钟之内有效"), // 重置手机号
    LOGIN(3,"【湖南省委党校】 您的短信验证码是{},用于系统登录，10分钟之内有效"); // 公众用户登录

    private final Integer code;

    private final String msgContent;

    /**
     * 根据type获取枚举
     * @param code 类型
     * @return 枚举
     */
    public static SmsMessageEnum get(Integer code) {
        return Arrays.stream(SmsMessageEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst().orElse(null);
    }

}
