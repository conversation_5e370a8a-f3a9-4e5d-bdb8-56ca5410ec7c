package com.unicom.swdx.module.system.api.user.dto;

import lombok.Data;

@Data
public class KafkaPersonDTO {
    private Integer personnalStatus;
    private Integer peronClassification;
    private String name;
    private String mobile;
    private Integer gender;
    private Long department;
    private Integer education =17 ; //不填的时候默认本科毕业
    private Integer academicDegree;
    private String administrativePositionName = ""; //默认没有
    private Long userId;
    private String tenantCode;
    private Long tenantId;
}
