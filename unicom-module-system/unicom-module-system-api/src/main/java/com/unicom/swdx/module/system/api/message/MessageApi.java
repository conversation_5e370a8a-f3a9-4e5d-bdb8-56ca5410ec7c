package com.unicom.swdx.module.system.api.message;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.message.dto.MessageAuthorityUpdateReqDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 消息授权")
public interface MessageApi {

    String PREFIX = ApiConstants.PREFIX + "/messageAuthority";

    @PostMapping(PREFIX + "/update")
    @ApiOperation("更新消息授权")
    CommonResult<Boolean> updateMessage(@RequestBody MessageAuthorityUpdateReqDTO updateReqDTO);

    @GetMapping(PREFIX + "/get")
    @ApiOperation("获得消息权限id")
    Long getMessage(@RequestParam("user") Long userId);


}
