package com.unicom.swdx.module.system.enums;

/**
 * System 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    String USER_TYPE = "user_type"; // 用户类型
    String COMMON_STATUS = "common_status"; // 系统状态

    // ========== SYSTEM 模块 ==========

    String USER_SEX = "system_user_sex"; // 用户性别

    String OPERATE_TYPE = "system_operate_type"; // 操作类型

    String LOGIN_TYPE = "system_login_type"; // 登录日志的类型
    String LOGIN_RESULT = "system_login_result"; // 登录结果

    String ERROR_CODE_TYPE = "system_error_code_type"; // 错误码的类型枚举

    String SMS_CHANNEL_CODE = "system_sms_channel_code"; // 短信渠道编码
    String SMS_TEMPLATE_TYPE = "system_sms_template_type"; // 短信模板类型
    String SMS_SEND_STATUS = "system_sms_send_status"; // 短信发送状态
    String SMS_RECEIVE_STATUS = "system_sms_receive_status"; // 短信接收状态
    String SYSTEM_COMPANY_TYPE = "system_company_type"; // 单位类型
    String SYSTEM_TENANT_TYPE = "system_tenant_type"; // 机构类型
    String SYSTEM_ROLE_TYPE = "system_role_type"; // 角色类型
    String SYSTEM_PUBLIC_USER_REGISTER_CHANNEL = "system_public_user_register_channel";
    String SYSTEM_USERS_PUBLIC_AUTHENTICATE_IDENTITY = "system_users_public_authenticate_identity";

    // ========== hr 模块 ==========
    String PERSONNEL_NATION = "nation"; // 民族
    String PERSONNEL_ID_TYPE = "id_type"; // 证件类型
    String PERSONNEL_POLITICAL_OUTLOOK = "political_outlook"; // 政治面貌
    String PERSONNEL_PERSON_CLASSIFICATION = "peron_classification"; // 人员分类
    String PERSONNEL_STATUS = "peron_status"; // 人员状态

    String PERSON_GENDER = "person_gender"; // 人事性别
    String PERSONNEL_TECHNICAL_NAME = "person_technical_name"; // 专业技术职务
    String PERSONNEL_TECHNICAL_RANK = "person_technical_rank"; // 专业技术岗位级别
    String PERSONNEL_EDUCATION = "person_education"; // 获得学历（最高学历）
    String PERSONNEL_ACADEMIC_DEGREE = "person_academic_degree"; // 获得学位（最高学位）
    String PERSONNEL_PERSON_RANK = "person_rank"; // 人员职级
    String PERSONNEL_ADMINISTRATIVE_POSITION_RANK = "person_administrative_position_rank"; // 行政职务级别
    String PERSONNEL_MARRYL_STATUS = "person_marry_status"; // 婚姻状态
    String PERSONNEL_PARTY_DEPT = "person_affiliated_party"; // 所属党支部
    String PERSONNEL_PARTY_HIGH_ORGANIZATION = "person_higher_party_organizations"; // 上级党组织
    String RECRUITMENT_METHOD = "recruitment_method"; // 录用方式

    String EDU_COURSE_CHANGE_TYPE = "edu_course_change_type"; //教务系统调课类型
    String EDU_TRAINEE_LEAVE_TYPE = "edu_leave_type"; //教务系统学员请假类型
    String EDU_TRAINEE_LEAVE_STATUS = "edu_leave_status"; //教务系统学员请假流程状态
}
