package com.unicom.swdx.module.system.api.schedule.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class ScheduleDto {
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date endTime;

    @ApiModelProperty(value = "地点")
    private String address;

    @ApiModelProperty(value = "类型（0=会议，1=活动，2=事务，3=其他）")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "参会回执 0:不参会 1:参会 NULL:未回应/不需回执/会议已结束")
    private String isAttend;

    @ApiModelProperty(value = "会议状态 0:审批不通过 1:待审批 2:待开始 3:会议中 4:已结束 5:已取消")
    private String meetingStatus;

    @ApiModelProperty(value = "申请类型  0-会议申请  1-会议室预订")
    private String applyType;

    @ApiModelProperty(value = "抄送人和参与人")
    private List<Long> userIds;

    @ApiModelProperty(value = "汇总年份")
    private Integer year;

    @ApiModelProperty(value = "汇总周")
    private Integer week;

    @ApiModelProperty(value = "中心工作id")
    private Integer inforId;

    //@NotEmpty(message = "任务名称不能为空")
    @ApiModelProperty(value = "任务名称")
    private String taskname;

    @ApiModelProperty(value = "是否是年度的")
    private Integer yeartag;

}
