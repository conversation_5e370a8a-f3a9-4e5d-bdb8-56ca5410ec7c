package com.unicom.swdx.module.system.enums.permission;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 菜单类型枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MenuTypeEnum {

    DIR(1), // 目录
    MENU(2), // 菜单
    BUTTON(3) // 按钮
    ;

    /**
     * 类型
     */
    private final Integer type;

    public boolean equals(Integer type) {
        return Objects.equals(this.getType(),type);
    }

}
