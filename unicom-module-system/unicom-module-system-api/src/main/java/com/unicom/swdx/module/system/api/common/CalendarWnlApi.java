package com.unicom.swdx.module.system.api.common;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.common.dto.CalendarWnlRespDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 万年历")
public interface CalendarWnlApi {
    String PREFIX = ApiConstants.PREFIX + "/calendar";

//    @GetMapping(PREFIX + "/getAnnualCalendarWnl")
//    @ApiOperation("获得年度日历信息")
//    CommonResult<List<CalendarWnlRespDTO>> getAnnual(@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate date,
//                                                     @RequestParam("timeScale") String timeScale);
//
    @GetMapping(PREFIX + "/getCalendarInRange")
    @ApiOperation("获得指定范围的日历信息")
    CommonResult<List<CalendarWnlRespDTO>> getCalendarInRange(@RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate startDate,
                                                              @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate endDate);

    @GetMapping(PREFIX + "/calculateWorkDay")
    @ApiOperation("计算指定时间范围内的工作日天数")
    CommonResult<Integer> calculateWorkDay(@RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate startDate,
                                           @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate endDate);

}
