package com.unicom.swdx.module.system.api.sms.dto.que;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 短信消息发送参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsSendReq {
    private Long sendId;
    private Long taskId;
    /**
     * 发送类型id 1=短信 2=通告
     */
    private Integer type;
    /**
     * 短信文本内容
     */
    private String message;

    /**
     * 手机号
     */
//    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> phone;

    /**
     * 发功时间（yyyy-MM-dd HH:mm:ss）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;
    private Boolean status;
    private Integer success;
    private Integer fail;
    private String title;
    private String time;
}
