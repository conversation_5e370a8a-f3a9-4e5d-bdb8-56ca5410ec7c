package com.unicom.swdx.module.system.api.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserReqDTO {

    @ApiModelProperty(value = "用户名", required = true, example = "unicom")
//    @NotBlank(message = "用户名不能为空")
//    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
//    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @ApiModelProperty(value = "用户昵称", required = true, example = "芋艿")
//    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickname;

    @ApiModelProperty(value = "组织ID", example = "我是一个用户")
//    @NotNull(message = "组织不能为空")
    private Long deptId;

    @ApiModelProperty(value = "岗位数组")
    private Set<Long> postIds;

    @ApiModelProperty(value = "手机号码", example = "15601691300")
//    @Mobile
//    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "用户状态", example = "0",notes = "0=开启，1=关闭")
//    @NotNull(message = "用户状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "显示排序", required = true, example = "1024")
//    @NotNull(message = "显示排序不能为空")
    private Integer sort;

    @ApiModelProperty(value = "密码", required = true, example = "123456")
//    @NotBlank(message = "密码不能为空")
//    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
//    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
//    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String password;
}
