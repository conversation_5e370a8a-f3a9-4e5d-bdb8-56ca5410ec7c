package com.unicom.swdx.module.system.enums.tenant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/17 9:30
 **/
@Getter
@AllArgsConstructor
public enum CompanyTypeEnum {

    LEGAL_ENTITY(1), // 法人单位

    INDIVIDUAL_BUSINESS(2); // 个体工商户


    private final Integer type;

    public boolean equals(Integer type) {
        return Objects.equals(this.getType(),type);
    }

}
