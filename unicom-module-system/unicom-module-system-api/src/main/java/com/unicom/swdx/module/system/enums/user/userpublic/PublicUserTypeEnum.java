package com.unicom.swdx.module.system.enums.user.userpublic;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公众用户类型
 * <AUTHOR>
 * @date 2023/3/20 15:12
 **/
@Getter
@AllArgsConstructor
public enum PublicUserTypeEnum {

    VISITOR(0), // 游客用户
    COMMON(1), // 普通用户
    REAL_NAME(2), // 实名用户
    AUTHENTICATION(3); // 认证用户

    private final Integer type;

    public boolean equals(Integer type) {
        return this.getType().equals(type);
    }

}
