package com.unicom.swdx.module.system.enums.kafka.dept;

import lombok.Data;

@Data
public class DeptEventType {

    public static final String deptSender = "003";

    //新增部门事件类型
    public static final String addDeptEventType = "00300001";
//    public static final String addDeptEventType = "00300001100";

    //修改部门事件类型
//    public static final String editDeptEventType = "00300002100";
    public static final String editDeptEventType = "00300002";

    //删除部门事件类型
    public static final String delDeptEventType = "00300003";
//    public static final String delDeptEventType = "00300003100";

    //新增机构事件类型
    public static final String addTenantEventType = "00400001";

    //修改机构事件类型
    public static final String editTenantEventType = "00400002";

    //删除机构事件类型
    public static final String delTenantEventType = "00400003";

}
