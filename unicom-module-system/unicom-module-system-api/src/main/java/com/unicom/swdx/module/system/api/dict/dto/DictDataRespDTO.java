package com.unicom.swdx.module.system.api.dict.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("RPC 服务 - 字典数据 Response DTO")
@Data
public class DictDataRespDTO {

    @ApiModelProperty(value = "字典标签", required = true, example = "sk")
    private String label;
    @ApiModelProperty(value = "父字典编码")
    private Long parentId;
    @ApiModelProperty(value = "字典值", required = true, example = "iocoder")
    private String value;
    @ApiModelProperty(value = "字典类型", required = true, example = "sys_common_sex")
    private String dictType;
    @ApiModelProperty(value = "备注", required = true, example = "remark")
    private String remark;
    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "见 CommonStatusEnum 枚举")
    private Integer status;
    @ApiModelProperty(value = "ID", required = true, example = "1", notes = "ID")
    private Long id;
    @ApiModelProperty(value = "imageUrl图片地址", example = "1", notes = "ID")
    private String imageUrl;
    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;

}
