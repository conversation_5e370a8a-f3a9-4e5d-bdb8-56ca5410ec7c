package com.unicom.swdx.module.system.api.kafka.dto;

import lombok.Data;

public class OldKafkaTenantDTO extends OldKafkaBodyDTO{

    private Long id;

    private String companyName;

    private String contactPhone;

    private String address;

    private Long admin_id;

    private String contactUserName;

    private String password;

    private String username;

    private String dept_num;

    private String dept_name;

    private Long new_dept_id;

    private Long new_parent_id;

    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public OldKafkaTenantDTO() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getAdmin_id() {
        return admin_id;
    }

    public void setAdmin_id(Long admin_id) {
        this.admin_id = admin_id;
    }

    public String getContactUserName() {
        return contactUserName;
    }

    public void setContactUserName(String contactUserName) {
        this.contactUserName = contactUserName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getDept_num() {
        return dept_num;
    }

    public void setDept_num(String dept_num) {
        this.dept_num = dept_num;
    }

    public String getDept_name() {
        return dept_name;
    }

    public void setDept_name(String dept_name) {
        this.dept_name = dept_name;
    }

    public Long getNew_dept_id() {
        return new_dept_id;
    }

    public void setNew_dept_id(Long new_dept_id) {
        this.new_dept_id = new_dept_id;
    }

    public Long getNew_parent_id() {
        return new_parent_id;
    }

    public void setNew_parent_id(Long new_parent_id) {
        this.new_parent_id = new_parent_id;
    }
}
