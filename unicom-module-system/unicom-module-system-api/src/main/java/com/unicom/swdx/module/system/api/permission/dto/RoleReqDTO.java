package com.unicom.swdx.module.system.api.permission.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.Set;

/**
 * 部门的数据权限 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class RoleReqDTO {

    @ApiModelProperty(value = "角色编号", required = true, example = "1")
    private Long userId;

    @ApiModelProperty(value = "角色编号列表", example = "1,3,5")
    private Set<Long> roleIds = Collections.emptySet(); // 兜底

}
