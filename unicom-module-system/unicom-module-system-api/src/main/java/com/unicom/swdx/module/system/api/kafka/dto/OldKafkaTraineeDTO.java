package com.unicom.swdx.module.system.api.kafka.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class OldKafkaTraineeDTO extends OldKafkaBodyDTO{

//    "body": {
//        "student": {
//            "birthday": "1984-01-07",
//             "flag": 1,
//             "nation": "汉族",
//             "idCard": "Pz5e68CarA7cSoeR3J6l9Vq8wjg3Fzo7",
//             "sex": "2",
//             "currentClassId": 1842456067104075778,
//             "currentJigouId": 1797439329509175298,
//             "personnelDepName": "省委宣传部",
//             "post": "省委宣传部意识形态工作处四级调研员",
//             "phone": "13467575668",
//             "culture": "硕士研究生",
//             "name": "陈瑶",
//             "rank": "18",
//             "zzmm": "中共党员",
//             "id": 1842457927128997890,
//             "tenant_code": "410000"
//        },
//        "trainClassStudent": {
//            "studentId": 1842457927128997890,
//             "upTime": "",
//             "classId": 1842456067104075778,
//             "id": "1842457927128997890",
//             "upStatus": "",
//             "status": "0"
//        }
//    }

    @JsonProperty("source")
    private String source;

    @JsonProperty("student")
    private StudentDTO student;

    @JsonProperty("trainClassStudent")
    private TrainClassStudentDTO trainClassStudent;

    @NoArgsConstructor
    @Data
    public static class StudentDTO {

        @JsonProperty("birthday")
        private String birthday;

        @JsonProperty("flag")
        private String flag;

        @JsonProperty("nation")
        private String nation;

        @JsonProperty("idCard")
        private String idCard;

        @JsonProperty("sex")
        private String sex;

        @JsonProperty("currentClassId")
        private String currentClassId;

        @JsonProperty("currentJigouId")
        private String currentJigouId;

        @JsonProperty("personnelDepName")
        private String personnelDepName;

        @JsonProperty("post")
        private String post;

        @JsonProperty("phone")
        private String phone;

        @JsonProperty("culture")
        private String culture;

        @JsonProperty("name")
        private String name;

        @JsonProperty("rank")
        private String rank;

        @JsonProperty("zzmm")
        private String zzmm;

        @JsonProperty("id")
        private String id;

        @JsonProperty("tenant_code")
        private String tenantCode;
    }

    @NoArgsConstructor
    @Data
    public static class TrainClassStudentDTO {

        @JsonProperty("studentId")
        private String studentId;

        @JsonProperty("upTime")
        private String upTime;

        @JsonProperty("classId")
        private String classId;

        @JsonProperty("id")
        private String id;

        @JsonProperty("upStatus")
        private String upStatus;

        @JsonProperty("status")
        private String status;
    }
}
