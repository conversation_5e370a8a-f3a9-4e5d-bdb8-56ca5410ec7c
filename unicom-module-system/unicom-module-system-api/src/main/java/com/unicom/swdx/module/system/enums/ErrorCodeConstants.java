package com.unicom.swdx.module.system.enums;

import com.unicom.swdx.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 *
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== AUTH 模块 1002000000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1002000000, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1002000001, "登录失败，账号被禁用");
    ErrorCode AUTH_LOGIN_DEPT_DISABLED = new ErrorCode(1002000002, "登录失败，{}部门已被禁用");
    ErrorCode AUTH_LOGIN_ROLE_DISABLED = new ErrorCode(1002000003, "登录失败，账号没有可用权限");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1002000004, "验证码不正确，原因：{}");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1002000005, "未绑定账号，需要进行绑定");
    ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(1002000006, "Token 已经过期");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1002000007, "手机号不存在");

    ErrorCode SIGN_UP_UNIT_NOT_BIND = new ErrorCode(1002000007, "手机号未和调训单位绑定");
    ErrorCode AUTH_USERNAME_AND_MOBILE_NOT_MATCH = new ErrorCode(1002000008, "用户名和手机号不匹配");
    ErrorCode AUTH_MOBILE_VERIFICATION_CODE_ERROR = new ErrorCode(1002000009, "短信验证码不正确");
    ErrorCode AUTH_USERNAME_NOT_EXISTS = new ErrorCode(1002000010, "用户名不存在");
    ErrorCode AUTH_VERIFICATION_EXPIRED = new ErrorCode(1002000011, "验证码已过期");
    ErrorCode AUTH_UUID_VALID_FAILED = new ErrorCode(1002000012, "uuid校验失败");
    ErrorCode AUTH_TENANT_EXPIRED = new ErrorCode(1002000013, "用户所属机构已过期，请联系管理员");
    ErrorCode AUTH_TENANT_DISABLED = new ErrorCode(1002000014, "用户所属机构已禁用");
    ErrorCode AUTH_LOGIN_BAD_PASSWORD = new ErrorCode(1002000015, "用户输错密码超过5次，请30分钟后重试");
    ErrorCode AUTH_LOGIN_PASSWORD_EXPIRED = new ErrorCode(1002000016, "密码已过期，请点击【忘记密码】重置");

    ErrorCode AUTH_TOKEN_ERROR = new ErrorCode(1002000017, "token不能为空或解析错误");
    ErrorCode AUTH_TOKEN_NOT_EXISTS = new ErrorCode(1002000018, "token不存在或已过期");
    ErrorCode AUTH_LOGIN_BAD_VERIFICATION = new ErrorCode(1002000019, "登录失败，手机验证码不正确");
    ErrorCode AUTH_LOGIN_USER_NOT_APPLY = new ErrorCode(1002000020, "登录失败，不是应聘人员");
    ErrorCode AUTH_VERIFICATION_TOO_MANY_FAILURES = new ErrorCode(1002000021, "用户输错验证码超过10次，请10分钟后重试");


    // ========== 菜单模块 1002001000 ==========
    ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1002001000, "已经存在该名字的菜单");
    ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1002001001, "父菜单不存在");
    ErrorCode MENU_PARENT_ERROR = new ErrorCode(1002001002, "不能设置自己为父菜单");
    ErrorCode MENU_NOT_EXISTS = new ErrorCode(1002001003, "菜单不存在");
    ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1002001004, "存在子菜单，无法删除");
    ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode(1002001005, "父菜单的类型必须是目录或者菜单");

    // ========== 角色模块 1002002000 ==========
    ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1002002000, "角色不存在");
    ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1002002001, "已经存在名为【{}】的角色");
    ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1002002002, "已经存在编码为【{}】的角色");
    ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode(1002002003, "不能操作类型为系统内置的角色");
    ErrorCode ROLE_IS_DISABLE = new ErrorCode(1002002004, "名字为【{}】的角色已被禁用");
    ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1002002005, "编码【{}】不能使用");
    ErrorCode ROLE_IS_EMPTY = new ErrorCode(1002002006, "该用户无角色数据");

    ErrorCode ROLE_GROUP_NOT_EXISTS = new ErrorCode(1002002007, "角色组不存在");

    ErrorCode ROLE_GROUP_NAME_DUPLICATE = new ErrorCode(1002002008, "已经存在名为【{}】的角色组");

    ErrorCode ROLE_GROUP_HAS_USER = new ErrorCode(1002002009, "无法删除，该角色组还有已分配的用户");



    // ========== 用户模块 1002003000 ==========
    ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1002003000, "用户名已经存在");
    ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1002003001, "手机号已经存在");
    ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1002003002, "邮箱已经存在");
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1002003003, "用户不存在");
    ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1002003004, "导入用户数据不能为空！");
    ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1002003005, "用户密码校验失败");
    ErrorCode USER_IS_DISABLE = new ErrorCode(1002003006, "名字为【{}】的用户已被禁用");
    ErrorCode USER_COUNT_MAX = new ErrorCode(1002003008, "创建用户失败，原因：超过租户最大租户配额({})！");
    ErrorCode USER_EXITS_TODO = new ErrorCode(1002003009, "该用户仍有待办事项，请办理后再删除");
    ErrorCode USER_NEW_MOBILE_ERROR = new ErrorCode(1002003010, "新手机号不能和旧手机号一样");
    ErrorCode USER_IMPORT_DEPT_ID_IS_EMPTY = new ErrorCode(1002003011, "导入用户所属部门编号不能为空！");
    ErrorCode USER_TENANT_NOT_MATCH_DEPT_TENANT = new ErrorCode(1002003012, "用户机构和所属部门机构不匹配！");
    ErrorCode USER_MOBILE_NOT_EXISTS = new ErrorCode(1002003013, "该用户未绑定手机号");
    ErrorCode USER_EXIST_UNFINISHED_TASK = new ErrorCode(1002003014, "该用户存在未完成的事项");
    // ========== 部门模块 1002004000 ==========
    ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1002004000, "已经存在该名字的部门");
    ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1002004001,"父级部门不存在");
    ErrorCode DEPT_NOT_FOUND = new ErrorCode(1002004002, "当前部门不存在");
    ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1002004003, "将同步删除{}个下属部门，确认要删除该部门吗？");
    ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1002004004, "不能设置自己为父部门");
    ErrorCode DEPT_EXISTS_USER = new ErrorCode(1002004005, "该部门及下属部门共包含{}个用户。需将用户调离部门后再删除。");
    ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1002004006, "部门不处于开启状态，不允许选择");
    ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1002004007, "不能设置自己的子部门为父部门");
    ErrorCode DEPT_All_EXISTS_USER = new ErrorCode(1002004008, "该部门及下属部门中存在员工，请将用户调离部门后再关闭");
    ErrorCode DEPT_NOT_AUTH = new ErrorCode(1002004009, "部门 {} 没有权限或不存在");

    ErrorCode DEPT_NOT_LEADER = new ErrorCode(1002004010, "没有指定部门负责人");

    ErrorCode DEPT_NOT_LEADER1 = new ErrorCode(1002004010, "没有指定办公室管理员");

    ErrorCode DEPT_NOT_LEADER2 = new ErrorCode(1002004012, "没有指定分管日常工作的副校长(副院长)");

    ErrorCode DEPT_TIME_ERROR = new ErrorCode(1002004011, "结束时间不能大于开始时间");


    // ========== 岗位模块 1002005000 ==========
    ErrorCode POST_NOT_FOUND = new ErrorCode(1002005000, "当前岗位不存在");
    ErrorCode POST_NOT_ENABLE = new ErrorCode(1002005001, "岗位({}) 不处于开启状态，不允许选择");
    ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1002005002, "已经存在该名字的岗位");
    ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1002005003, "已经存在该标识的岗位");
    ErrorCode POST_NOT_IN_TENANT = new ErrorCode(1002005004, "机构下没有对应岗位");
    ErrorCode POST_IMPORT_LIST_IS_EMPTY = new ErrorCode(1002005005, "导入用户数据不能为空");
    ErrorCode POST_IMPORT_OUT_MEMORY = new ErrorCode(1002005006, "导入文件不能超过16M");
//    ErrorCode POST_IMPORT_LIST_MEMORY = new ErrorCode(1002005006, "导入用户数据不能超过100条");
    // ========== 字典类型 1002006000 ==========
    ErrorCode DICT_TYPE_NOT_EXISTS = new ErrorCode(1002006001, "当前字典类型不存在");
    ErrorCode DICT_TYPE_NOT_ENABLE = new ErrorCode(1002006002, "字典类型不处于开启状态，不允许选择");
    ErrorCode DICT_TYPE_NAME_DUPLICATE = new ErrorCode(1002006003, "已经存在该名字的字典类型");
    ErrorCode DICT_TYPE_TYPE_DUPLICATE = new ErrorCode(1002006004, "已经存在该类型的字典类型");
    ErrorCode DICT_TYPE_HAS_CHILDREN = new ErrorCode(1002006005, "无法删除，该字典类型还有字典数据");
    ErrorCode DICT_TYPE_UNAUTH = new ErrorCode(1002006006, "未授权的字典查询");

    // ========== 字典数据 1002007000 ==========
    ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1002007001, "当前字典数据不存在");
    ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1002007002, "字典数据({})不处于开启状态，不允许选择");
    ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1002007003, "已经存在该值的字典数据");
    ErrorCode DICT_DATA_PARENT_IS_SELF = new ErrorCode(1002007004, "不能设置自己为父字典");
    ErrorCode DICT_DATA_HAS_CHILDREN = new ErrorCode(1002007005, "无法删除，该字典数据还有子字典数据");

    // ========== 通知公告 1002008000 ==========
    ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1002008001, "当前通知公告不存在");

    // ========== 短信渠道 1002011000 ==========
    ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1002011000, "短信渠道不存在");
    ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1002011001, "短信渠道不处于开启状态，不允许选择");
    ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1002011002, "无法删除，该短信渠道还有短信模板");

    // ========== 短信模板 1002012000 ==========
    ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1002012000, "短信模板不存在");
    ErrorCode SMS_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1002012001, "已经存在编码为【{}】的短信模板");

    // ========== 短信发送 1002013000 ==========
    ErrorCode SMS_SEND_MOBILE_NOT_EXISTS = new ErrorCode(1002013000, "手机号不存在");
    ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS = new ErrorCode(1002013001, "模板参数({})缺失");
    ErrorCode SMS_SEND_TEMPLATE_NOT_EXISTS = new ErrorCode(1002013002, "短信模板不存在");
    //ErrorCode SMS_SEND_OVER_MAX_NUM = new ErrorCode(1002013003, "同一手机号一天限制发送10条短信");


    // ========== 短信验证码 1002014000 ==========
    ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1002014000, "验证码不存在");
    ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1002014001, "验证码已过期");
    ErrorCode SMS_CODE_USED = new ErrorCode(1002014002, "验证码已使用");
    ErrorCode SMS_CODE_NOT_CORRECT = new ErrorCode(1002014003, "验证码不正确");
    ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode(1002014004, "超过每日短信发送数量");
    ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1002014005, "短信发送过于频率");
    ErrorCode SMS_CODE_SEND_TOO_ERROR = new ErrorCode(1002014010, "今日错误次数已超限，请明日再试或使用账号密码登录。");
    ErrorCode SMS_CODE_IS_EXISTS = new ErrorCode(1002014006, "手机号已被使用");
    ErrorCode SMS_CODE_IS_UNUSED = new ErrorCode(1002014007, "验证码未被使用");
    ErrorCode MASSAGE_TIMES_OVER_10 = new ErrorCode(1002014008, "同一手机号一天限制发送10条短信");
    ErrorCode MASSAGE_TIMES_OVER_1 = new ErrorCode(1002014009, "验证码发送太频繁了，请1分钟后重试");
    ErrorCode SMS_CODE_ERROR = new ErrorCode(1002014011, "验证码输入有误");


    // ========== 租户信息 1002015000 ==========
    ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1002015000, "租户不存在");
    ErrorCode TENANT_DISABLE = new ErrorCode(1002015001, "名字为【{}】的租户已被禁用");
    ErrorCode TENANT_EXPIRE = new ErrorCode(1002015002, "名字为【{}】的租户已过期");
    ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode(1002015003, "系统机构不能进行修改、删除、变更等操作！");
    ErrorCode TENANT_SOCIAL_CREDIT_CODE_EXISTS = new ErrorCode(1002015004, "统一社会信用代码【{}】已存在");
    ErrorCode TENANT_REGISTER_INFO_NOT_EXISTS = new ErrorCode(1002015005, "法人机构注册地址或注册区划缺失");
    ErrorCode TENANT_HAS_USERS = new ErrorCode(1002015006, "存在除管理员外的用户，无法删除!");
    ErrorCode TENANT_CODE_EXISTS = new ErrorCode(1002015007, "机构编码已存在");


    // ========== 租户套餐 1002016000 ==========
    ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1002016000, "租户套餐不存在");
    ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1002016001, "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除");
    ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1002016002, "名字为【{}】的租户套餐已被禁用");

    // ========== 错误码模块 1002017000 ==========
    ErrorCode ERROR_CODE_NOT_EXISTS = new ErrorCode(1002017000, "错误码不存在");
    ErrorCode ERROR_CODE_DUPLICATE = new ErrorCode(1002017001, "已经存在编码为【{}】的错误码");

    // ========== 社交用户 1002018000 ==========
    ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(1002018000, "社交授权失败，原因是：{}");
    ErrorCode SOCIAL_USER_UNBIND_NOT_SELF = new ErrorCode(1002018001, "社交解绑失败，非当前用户绑定");
    ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(1002018002, "社交授权失败，找不到对应的用户");

    // ========== 系统敏感词 1002019000 =========
    ErrorCode SENSITIVE_WORD_NOT_EXISTS = new ErrorCode(1002019000, "系统敏感词在所有标签中都不存在");
    ErrorCode SENSITIVE_WORD_EXISTS = new ErrorCode(1002019001, "系统敏感词已在标签中存在");

    // ========== OAuth2 客户端 1002020000 =========
    ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1002020000, "OAuth2 客户端不存在");
    ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1002020001, "OAuth2 客户端编号已存在");
    ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1002020002, "OAuth2 客户端已禁用");
    ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode(1002020003, "不支持该授权类型");
    ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1002020004, "授权范围过大");
    ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode(1002020005, "无效 redirect_uri: {}");
    ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode(1002020006, "无效 client_secret: {}");
    ErrorCode OAUTH2_CLIENT_NAME_EXISTS = new ErrorCode(1002020007, "应用名称已存在");
    ErrorCode OAUTH2_CLIENT_CODE_EXISTS = new ErrorCode(1002020008, "应用标识已存在");
    ErrorCode OAUTH2_CLIENT_SYSTEM = new ErrorCode(1002020009, "系统应用，不允许更改状态!!");
    ErrorCode OAUTH2_CLIENT_ROLE_BIND_EXISTS = new ErrorCode(1002020010, "该应用已有绑定的角色!");
    ErrorCode OAUTH2_CLIENT_MENU_BIND_EXISTS = new ErrorCode(1002020011, "该应用已有绑定的菜单!");

    // ========== OAuth2 授权 1002021000 =========
    ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode(1002021000, "client_id 不匹配");
    ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode(1002021001, "redirect_uri 不匹配");
    ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(1002021002, "state 不匹配");
    ErrorCode OAUTH2_GRANT_CODE_NOT_EXISTS = new ErrorCode(1002021003, "code 不存在");

    // ========== OAuth2 授权 1002022000 =========
    ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(1002022000, "code 不存在");
    ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(1002022001, "code 已过期");
    ErrorCode IMPORT_VALID_FAILED = new ErrorCode(1002023000, "导入校验失败\n{}");


    ErrorCode TENANT_TYPE_NOT_EXISTS = new ErrorCode(1002024000, "机构用户类型不存在");
    ErrorCode TENANT_TYPE_NAME_EXISTED = new ErrorCode(1002024001, "机构用户类型名称已存在");

    ErrorCode USERS_PUBLIC_NOT_EXISTS = new ErrorCode(1002025000, "公众用户不存在");
    ErrorCode USERS_PUBLIC_NEED_REGISTER = new ErrorCode(1002025001, "请先注册");
    ErrorCode OPENID_EMPTY = new ErrorCode(1002025002, "openid不能为空");
    ErrorCode PUBLIC_USER_EXISTS = new ErrorCode(1002025003, "该用户已注册，请直接登录");
    ErrorCode PUBLIC_USER_NOT_EXISTED = new ErrorCode(1002025004, "用户未注册");
    ErrorCode VERIFICATION_CODE_EMPTY = new ErrorCode(1002026000, "验证码不能为空");
    ErrorCode MOBILE_ILLEGAL = new ErrorCode(1002026001, "手机号非法");



    // ========== 消息中心模块 1112028000 =========
    ErrorCode TEMPLATE_NAME_DUPLICATE = new ErrorCode(1112028000, "已经存在该系统的模板名称");
    ErrorCode TEMPLATE_NOT_EXISTS = new ErrorCode(1112028001, "消息模板不存在");
    ErrorCode SEND_TIME_BEFOR = new ErrorCode(1112028002, "发送时间不能小于当前时间");
    ErrorCode DELAYQUE_PHONE_EMPTY = new ErrorCode(1112028003, "加入队列手机号为空");
    ErrorCode DELAYQUE_TIME_EMPTY = new ErrorCode(1112028004, "加入队列发送时间为空");



    // 工作台
    ErrorCode TODO_NOT_EXISTS = new ErrorCode(1017001001, "待办事项不存在");
    ErrorCode TODO_EXISTED = new ErrorCode(1017001002, "待办事项已存在");
    ErrorCode NOTICE_NOT_EXISTS = new ErrorCode(1017002001, "公告不存在");
    ErrorCode SCHEDULE_NOT_EXISTS = new ErrorCode(1017003001, "日程不存在");
    ErrorCode SHORTCUT_NOT_EXISTS = new ErrorCode(1017004001, "快捷入口不存在");

    // 小程序
    ErrorCode ACCESS_TOKEN_ERROR = new ErrorCode(1002027001, "获取小程序token失败");
    ErrorCode LOGIN_WAITE = new ErrorCode(1002, "获取token等待中...");
    ErrorCode CODE_TO_PHONE_ERROR = new ErrorCode(1002027002, "获取用户手机号失败");
    ErrorCode MOBILE_USER_NOT_EXIST = new ErrorCode(1002027003, "手机号用户不存在!");
    ErrorCode OPENID_NOT_EXIST = new ErrorCode(1002027004, "获取openid失败!");
    ErrorCode USER_TICKET_NOT_EXIST = new ErrorCode(1002027005, "获取user_ticket失败!");
    ErrorCode GET_TOKEN_FAIL = new ErrorCode(1002027006, "获取token失败!");
    ErrorCode GET_URL_LINK_FAIL = new ErrorCode(1002027006, "获取short link失败!");
    ErrorCode OPENID_NOT_BIND = new ErrorCode(1003, "openid未绑定用户");

    // ========== 日志管理 1002028000 =========
    ErrorCode OPERATOR_TIME_ILLEGAL = new ErrorCode(1002028000, "操作时间区间大于一周");


    //招聘系统
    ErrorCode CARD_NO_DUPLICATE = new ErrorCode(1112128000, "不可重复提交一个证件号！");

    /**
     * 手机号登录
     */
    ErrorCode TIMEOUT_ERROR = new ErrorCode(1112138000, "请求超时，请重新发送请求。");

}
