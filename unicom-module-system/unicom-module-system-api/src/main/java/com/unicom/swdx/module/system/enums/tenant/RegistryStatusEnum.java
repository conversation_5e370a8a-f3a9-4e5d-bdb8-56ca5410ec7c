package com.unicom.swdx.module.system.enums.tenant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/6 10:34
 **/
@Getter
@AllArgsConstructor
public enum RegistryStatusEnum {

    REGISTERED(0), // 注册成功
    REGISTERING(1); // 注册中
    private Integer status;

    public boolean equals(Integer status) {
        return Objects.equals(this.getStatus(),status);
    }
}
