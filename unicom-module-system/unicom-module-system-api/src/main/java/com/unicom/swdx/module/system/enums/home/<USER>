package com.unicom.swdx.module.system.enums.home;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum IsAttendRankEnum {
    _0(2,"0","不参会"),
    _1(0,"1","参会"),
    _NULL(1,null,"未回应");



    //排序优先级
    private final Integer rank;
    //类型
    private final String type;
    //名称
    private final String name;

    public static IsAttendRankEnum getByType(String type) {

        if(Objects.isNull(type)) {
            return _NULL;
        }else {
            if(type.equals("0")){
                return _0;
            }else{
                return _1;
            }
        }
    }
}
