package com.unicom.swdx.module.system.enums.home;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum MeetingStatusRankEnum {

    _0(3,"0","审批不通过"),
    _1(3,"1","待审批"),
    _2(0,"2","待开始"),
    _3(1,"3","会议中"),
    _4(2,"4","已结束"),
    _5(3,"5","已取消"),
    _NULL(4,null,"null");



    //排序优先级
    private final Integer rank;
    //类型
    private final String type;
    //名称
    private final String name;

    public static MeetingStatusRankEnum getByType(String type) {
        if(Objects.isNull(type)){
            return _NULL;
        }
        return ArrayUtil.firstMatch(meetingStatusRankEnum -> meetingStatusRankEnum.getType().equals(type),
                values());
    }
}
