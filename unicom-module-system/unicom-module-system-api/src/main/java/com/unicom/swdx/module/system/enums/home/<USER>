package com.unicom.swdx.module.system.enums.home;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 待办具体事项枚举
 * <AUTHOR>
 * @date 2023/6/15 14:56
 **/
@Getter
@AllArgsConstructor
public enum TodoItemEnum {

    TYZZ_HYSH(1,"organizationMemberAudit"), // 体育组织-会员审核
    TYZZ_PLSH(2,"organizationCommentAudit"), // 体育组织-评论审核
    RC_SHRCSH(3,""), // 人才-社会人才审核
    ZX_PLSH(4,"informCommonAudit"),   // 资讯-评论审核
    ZX_ZXSH(5,"informInformationAudit"),   // 资讯-资讯审核
    ZX_SPSH(6,"informCompetitionAudit"),   // 资讯-视频审核
    ZX_KPZSSH(7,"informScientificAudit"), // 资讯-科普知识审核
    ZX_ZBSH(8,"informLiveAudit"),   // 资讯-直播审核
    CY_SFXMSP(9,"industry:pilotProject:add"), // 资讯-示范项目审批
    ;

    private final Integer item;

    private final String permission;

    public static Set<String> getAllPermission() {
        return Arrays.stream(values()).map(TodoItemEnum::getPermission).collect(Collectors.toSet());
    }

}
