package com.unicom.swdx.module.system.enums.home;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/12  9:44
 */
@Getter
@AllArgsConstructor
public enum ProcessTypeEnum {
    DOCRECEIVING("receiving", "公文收文一级路由"),
    DOCSEND("sensitPost", "公文发文一级路由"),
    DOCRECEIVINGLIST("receivingList", "公文收文列表一级路由"),
    ;

    /**
     * 任务code
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;
}
