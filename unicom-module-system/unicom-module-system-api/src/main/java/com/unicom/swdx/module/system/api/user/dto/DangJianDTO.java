package com.unicom.swdx.module.system.api.user.dto;

import lombok.Data;

import java.util.Date;

@Data
public class DangJianDTO {
    private Long id; // 假设user_id是Long类型
    private String userName; // 假设user_name是String类型
    private Date joinPartyDate; // 假设join_party_date是Date类型
    private Date officialDate; // 假设official_date是Date类型
    private String combinedIntroducers; // 合并后的介绍人
    private String partyOrgName; // 党组织名称
    private String parentPartyOrgName; // 上级党组织名称
}
