package com.unicom.swdx.module.system.api.dept;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dept.dto.OldDeptDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.*;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 部门")
public interface DeptApi {

    String PREFIX = ApiConstants.PREFIX + "/dept";

    @GetMapping(PREFIX + "/get")
    @ApiOperation("获得部门信息")
    @ApiImplicitParam(name = "id", value = "部门编号", example = "1024", required = true, dataTypeClass = Long.class)
    CommonResult<DeptRespDTO> getDept(@RequestParam("id") Long id);


    @GetMapping(PREFIX + "/getByTenantAndName")
    @ApiOperation("获得部门信息通过租户和名字")
    CommonResult<DeptRespDTO> getDeptByTenantAndName(@RequestParam("id") Long tenantid , @RequestParam("deptname") String deptname);

    @GetMapping(PREFIX + "/list")
    @ApiOperation("获得部门信息数组")
    @ApiImplicitParam(name = "ids", value = "部门编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<List<DeptRespDTO>> getDepts(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/valid")
    @ApiOperation("校验部门是否合法")
    @ApiImplicitParam(name = "ids", value = "部门编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<Boolean> validDepts(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/get-all-children")
    @ApiOperation("获得子部门信息")
    @ApiImplicitParam(name = "id", value = "部门编号", example = "1024", required = true, dataTypeClass = Long.class)
    CommonResult<List<DeptRespDTO>> getAllChildrenDept(@RequestParam("id") Long id);


    @GetMapping(PREFIX + "/get-all-children-deleted")
    @ApiOperation("获得子部门信息已删除")
    @ApiImplicitParam(name = "deptIds", value = "部门编号数组", example = "1024", required = true,dataTypeClass = List.class)
    CommonResult<List<DeptRespDTO>> getAllChildrenDeletedDept(@RequestParam("deptIds") List<Long> deptIds);


    @GetMapping(PREFIX + "/all-dept")
    @ApiOperation("获得部门信息数组")
    @ApiImplicitParam(name = "tenantId", value = "部门编号数组", example = "1", dataTypeClass = Long.class)
    CommonResult<List<DeptRespDTO>> getAllDepts(@RequestParam("tenantId") Long tenantId);

    @GetMapping(PREFIX + "/getByLeaderUserId")
    @ApiOperation("通过负责人Id获取部门")
    @ApiImplicitParam(name = "leaderUserId", value = "部门负责人id", example = "1", dataTypeClass = Long.class)
    CommonResult<Boolean> isLeaderUser(@RequestParam("leaderUserId") Long leaderUserId);

    @GetMapping(PREFIX + "/getByLeaderUser")
    @ApiOperation("通过负责人Id获取部门")
    @ApiImplicitParam(name = "leaderUserId", value = "部门负责人id", example = "1", dataTypeClass = Long.class)
    CommonResult<List<DeptRespDTO>> getByLeaderUser(@RequestParam("leaderUserId") Long leaderUserId);

    @GetMapping(PREFIX + "/getOldDeptByDeptId")
    @ApiOperation("获得老系统部门")
    CommonResult<OldDeptDTO> getOldDeptByDeptId(@RequestParam("deptId") Long deptId);

    /**
     * 获得指定编号的部门 Map
     *
     * @param ids 部门编号数组
     * @return 部门 Map
     */
    default Map<Long, DeptRespDTO> getDeptMap(Set<Long> ids) {
        return CollectionUtils.convertMap(getDepts(ids).getCheckedData(), DeptRespDTO::getId);
    }


    @GetMapping(PREFIX + "/SendMessageRefresh")
    @ApiOperation("删除延时发送的短信")
    CommonResult<String> sendMessageRefresh(@RequestParam("code") String code);

    @GetMapping(PREFIX + "/SendMessageRefreshList")
    @ApiOperation("删除延时发送的多条短信")
    CommonResult<String> sendMessageRefreshList(@RequestParam("codes") List<String> codes);


    @GetMapping(PREFIX + "/asynTest")
    @ApiOperation("异步测试DubboService")
    void asynTest(@RequestParam("code") String code);


    @GetMapping(PREFIX + "/syncTest")
    @ApiOperation("同步测试DubboService")
    CommonResult<String> syncTest(@RequestParam("code") String code);


    @GetMapping(PREFIX + "/timeTest")
    @ApiOperation("时间测试")
    void timeTest(@RequestParam("code") LocalDateTime time);

    @GetMapping(PREFIX + "/SendMessageRefreshListById")
    @ApiOperation("删除延时发送的多条短信")
    CommonResult<String> sendMessageRefreshListById(@RequestParam("ids") List<Long> ids);
}
