package com.unicom.swdx.module.system.api.errorcode;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.errorcode.dto.ErrorCodeAutoGenerateReqDTO;
import com.unicom.swdx.module.system.api.errorcode.dto.ErrorCodeRespDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 错误码")
public interface ErrorCodeApi {

    String PREFIX = ApiConstants.PREFIX + "/error-code";

    @PostMapping(PREFIX + "/auto-generate")
    @ApiOperation("自动创建错误码")
    CommonResult<Boolean> autoGenerateErrorCodes(@Valid @RequestBody List<ErrorCodeAutoGenerateReqDTO> autoGenerateDTOs);

    @GetMapping(PREFIX + "/list")
    @ApiOperation(value = "增量获得错误码数组", notes = "如果 minUpdateTime 为空时，则获取所有错误码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "applicationName", value = "应用名", example = "system-server", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "minUpdateTime", value = "最小更新时间", dataTypeClass = LocalDateTime.class)
    })
    CommonResult<List<ErrorCodeRespDTO>> getErrorCodeList(@RequestParam(value = "applicationName") String applicationName,
                                                          @RequestParam(value = "minUpdateTime", required = false) LocalDateTime minUpdateTime);

}
