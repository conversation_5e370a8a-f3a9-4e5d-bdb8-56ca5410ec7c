package com.unicom.swdx.module.system.enums.kafka.person;

import lombok.Data;

@Data
public class PersonEventType {

    public static final String personSender = "003";

    //新增教职工事件类型
//    public static final String addPersonEventType = "00300004100";
    public static final String addPersonEventType = "00300004";

    //修改教职工事件类型
//    public static final String editPersonEventType = "00300005100";
    public static final String editPersonEventType = "00300005";

//    //删除教职工事件类型
//    public static final String delPersonEventType = "00300006100";
public static final String deletePersonEventType = "00300100";

}
