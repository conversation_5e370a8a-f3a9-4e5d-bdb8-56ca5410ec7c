package com.unicom.swdx.module.system.api.tenant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/26 9:31
 **/
@Data
@ApiModel("RPC - 机构信息 Response DTO")
public class TenantInfoRespDTO {

    @ApiModelProperty(value = "机构id")
    private Long id;

    @ApiModelProperty(value = "机构类型")
    private Integer tenantType;

    @ApiModelProperty(value = "机构级别")
    private Integer tenantLevel;

    @ApiModelProperty(value = "机构名称")
    private String name;

    @ApiModelProperty(value = "机构管理员的用户id")
    private Long contactUserId;

}
