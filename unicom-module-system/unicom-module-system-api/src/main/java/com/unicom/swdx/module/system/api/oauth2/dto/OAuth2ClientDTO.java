package com.unicom.swdx.module.system.api.oauth2.dto;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/18 16:26
 **/
@Data
public class OAuth2ClientDTO {

    /**
     * 编号
     */
    private Long id;
    /**
     * 应用标识
     */
    private String code;
    /**
     * 客户端编号
     */
    private String clientId;
    /**
     * 客户端密钥
     */
    private String secret;
    /**
     * 应用名
     */
    private String name;
    /**
     * 应用图标
     */
    private String logo;
    /**
     * 应用地址
     */
    private String path;
    /**
     * 应用描述
     */
    private String description;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 显示顺序
     */
    private Integer sort;

}
