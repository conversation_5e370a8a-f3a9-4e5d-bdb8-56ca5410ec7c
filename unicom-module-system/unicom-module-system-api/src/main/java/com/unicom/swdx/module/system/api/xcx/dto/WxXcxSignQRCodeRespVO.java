package com.unicom.swdx.module.system.api.xcx.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/23 11:30
 **/
@Data
@ApiModel(value = "管理后台 - 微信小程序二维码 Response VO")
public class WxXcxSignQRCodeRespVO {

    @ApiModelProperty("二维码链接携带的唯一码")
    private String uniqueCode;

    @ApiModelProperty("二维码Base64格式")
    private String qrCodeBase64;

}
