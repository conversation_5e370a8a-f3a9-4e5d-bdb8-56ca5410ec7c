package com.unicom.swdx.module.system.api.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("人事调动调出部门信息")
@Data
@ToString(callSuper = true)
public class HrDeptDTO {

    @ApiModelProperty(value = "调出部门",example = "1")
    private Long deptId;

    @ApiModelProperty(value = "调出部门名称")
    private String name;

}
