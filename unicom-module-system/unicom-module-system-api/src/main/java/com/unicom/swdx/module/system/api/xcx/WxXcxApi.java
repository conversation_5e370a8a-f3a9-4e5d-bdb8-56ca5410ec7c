package com.unicom.swdx.module.system.api.xcx;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.xcx.dto.WxXcxSignQRCodeRespVO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = ApiConstants.NAME)
@Api(tags = "RPC 服务 - 管理员用户")
public interface WxXcxApi {

    String PREFIX = ApiConstants.PREFIX + "/xcx";

    @GetMapping(PREFIX + "/getSignQRCode")
    @ApiOperation("查询移动端签字的小程序二维码")
    CommonResult<WxXcxSignQRCodeRespVO> getSignQRCode();

}
