package com.unicom.swdx.module.system.api.businesscenter;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.businesscenter.dto.DeptSimpleRespDTO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> Zhe
 * @Description: 业中的一些Api调用
 * @date 2024-10-15
 */
@FeignClient(name = ApiConstants.NAME)
@Api(tags = "业中数据服务")
public interface BusinessCenterApi {

    String PREFIX = ApiConstants.PREFIX + "/businesscenter";

    @GetMapping(PREFIX + "/edu-get-dept")
    @ApiOperation("获取业中部门列表")
    CommonResult<List<DeptSimpleRespDTO>> getDept(@RequestParam(value = "tenantId", required = false) Long tenantId,
                                                  @RequestParam(value = "type", required = false) Integer type,
                                                  @RequestParam(value = "token") String token);

    @GetMapping(PREFIX + "/edu-get-dept-all-children")
    @ApiOperation("获取业中某个部门的所有子子部门")
    CommonResult<List<DeptSimpleRespDTO>> getDeptAllChildren(@RequestParam(value = "tenantId", required = false) Long tenantId,
                                                              @RequestParam(value = "type", required = false) Integer type,
                                                              @RequestParam(value = "deptId") Long deptId,
                                                              @RequestParam(value = "token") String token);

    @GetMapping(PREFIX + "/get-user-dept-permission-by-token")
    @ApiOperation("获取业中某个用户的权限部门ID列表（拿不到用户多部门）")
    CommonResult<List<Long>> getUserDeptPermissionByToken(@RequestParam(value = "tenantId", required = false) Long tenantId,
                                                             @RequestParam(value = "type", required = false) Integer type,
                                                             @RequestParam(value = "token") String token);

    @GetMapping(PREFIX + "/get-user-dept-permission-by-userId")
    @ApiOperation("获取教务某个用户id的权限部门ID列表（拿得到用户多部门）")
    CommonResult<List<Long>> getUserDeptPermissionByUserId(@RequestParam(value = "tenantId", required = false) Long tenantId,
                                                   @RequestParam(value = "type", required = false) Integer type,
                                                   @RequestParam(value = "userId") Long userId,
                                                   @RequestParam(value = "token") String token);

    @GetMapping(PREFIX + "/getTokenOfBusinessCenter")
    @ApiOperation("获取业中部门列表")
    CommonResult<String> getTokenOfBusinessCenter(@RequestParam(value = "tokenOfEdu") String tokenOfEdu);

    @GetMapping(PREFIX + "/getPersonnal")
    @ApiOperation("获取业中教职工列表")
    CommonResult<List<PersonnalRespDTO>> getPersonnal(@RequestParam(value = "tenantId", required = false) Long tenantId,
                                                      @RequestParam(value = "token" , required = false) String token);

    @GetMapping(PREFIX + "/getRoleInfo")
    @ApiOperation("获取业中角色信息")
    CommonResult<List<Long>> getAdminInfo(@RequestParam(value = "roleId", required = false) Long roleId,
                                          @RequestParam(value = "token", required = false)String token);
}
