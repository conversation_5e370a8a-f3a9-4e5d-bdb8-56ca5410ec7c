<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.unicom.cloud</groupId>
        <artifactId>unicom-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>unicom-module-system-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <properties>
<!--        <project.mainClass>com.unicom.swdx.module.system.SystemServerApplication</project.mainClass>-->
    </properties>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-module-oa-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-banner</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-operatelog</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-sms</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-dict</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-social</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-biz-error-code</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-captcha</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-spring-boot-starter-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-module-bpm-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.unicom</groupId>
            <artifactId>unicom-module-hr-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.unicom.cloud</groupId>
            <artifactId>unicom-module-edu-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
