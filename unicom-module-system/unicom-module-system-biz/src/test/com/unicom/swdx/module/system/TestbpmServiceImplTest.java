package com.unicom.swdx.module.system;


import com.unicom.swdx.framework.test.core.ut.BaseDbUnitTest;
import com.unicom.swdx.module.system.mq.producer.dept.DeptProducer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;


@SpringBootTest(classes = {SystemServerApplication.class} , webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TestbpmServiceImplTest  extends BaseDbUnitTest {



    @Resource
    private DeptProducer deptProducer;

    @Test
    public void testCreateInfor_success() throws InterruptedException {


        while (true){

            log.info("deptProducer.sendRefreshMessage()");
            deptProducer.sendRefreshMessage();

            Thread.sleep(1000*10);


        }


    }



}
