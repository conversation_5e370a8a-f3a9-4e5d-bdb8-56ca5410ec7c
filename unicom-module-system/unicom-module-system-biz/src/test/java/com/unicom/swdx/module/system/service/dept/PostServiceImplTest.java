package com.unicom.swdx.module.system.service.dept;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserPostDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.PostMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserPostMapper;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PostServiceImplTest {

    @Mock
    private PostMapper mockPostMapper;
    @Mock
    private PermissionService mockPermissionService;
    @Mock
    private DeptMapper mockDeptMapper;
    @Mock
    private AdminUserService mockUserService;
    @Mock
    private UserPostMapper mockUserPostMapper;
    @Mock
    private ReceiveApi mockReceiveApi;

    @InjectMocks
    private PostServiceImpl postServiceImplUnderTest;

    @Test
    public void testCreatePost() {
        // Setup
        final PostCreateReqVO reqVO = new PostCreateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure PostMapper.selectByName(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByName("name", 0L)).thenReturn(postDO1);

        // Configure PostMapper.selectByCode(...).
        final PostDO postDO2 = new PostDO();
        postDO2.setId(0L);
        postDO2.setTenant_id(0L);
        postDO2.setName("name");
        postDO2.setCode("code");
        postDO2.setSort(0);
        postDO2.setStatus(0);
        postDO2.setDisplayState(false);
        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(postDO2);

        // Run the test
        final Long result = postServiceImplUnderTest.createPost(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);

        // Confirm PostMapper.insert(...).
        final PostDO entity = new PostDO();
        entity.setId(0L);
        entity.setTenant_id(0L);
        entity.setName("name");
        entity.setCode("code");
        entity.setSort(0);
        entity.setStatus(0);
        entity.setDisplayState(false);
        verify(mockPostMapper).insert(entity);
    }

    @Test
    public void testCreatePost_PostMapperSelectByIdReturnsNull() {
        // Setup
        final PostCreateReqVO reqVO = new PostCreateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");

        when(mockPostMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> postServiceImplUnderTest.createPost(reqVO));
    }

    @Test
    public void testCreatePost_PostMapperSelectByNameReturnsNull() {
        // Setup
        final PostCreateReqVO reqVO = new PostCreateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        when(mockPostMapper.selectByName("name", 0L)).thenReturn(null);

        // Configure PostMapper.selectByCode(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(postDO1);

        // Run the test
        final Long result = postServiceImplUnderTest.createPost(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);

        // Confirm PostMapper.insert(...).
        final PostDO entity = new PostDO();
        entity.setId(0L);
        entity.setTenant_id(0L);
        entity.setName("name");
        entity.setCode("code");
        entity.setSort(0);
        entity.setStatus(0);
        entity.setDisplayState(false);
        verify(mockPostMapper).insert(entity);
    }

    @Test
    public void testCreatePost_PostMapperSelectByCodeReturnsNull() {
        // Setup
        final PostCreateReqVO reqVO = new PostCreateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure PostMapper.selectByName(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByName("name", 0L)).thenReturn(postDO1);

        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(null);

        // Run the test
        final Long result = postServiceImplUnderTest.createPost(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);

        // Confirm PostMapper.insert(...).
        final PostDO entity = new PostDO();
        entity.setId(0L);
        entity.setTenant_id(0L);
        entity.setName("name");
        entity.setCode("code");
        entity.setSort(0);
        entity.setStatus(0);
        entity.setDisplayState(false);
        verify(mockPostMapper).insert(entity);
    }

    @Test
    public void testUpdatePost() {
        // Setup
        final PostUpdateReqVO reqVO = new PostUpdateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");
        reqVO.setId(0L);

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure PostMapper.selectByName(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByName("name", 0L)).thenReturn(postDO1);

        // Configure PostMapper.selectByCode(...).
        final PostDO postDO2 = new PostDO();
        postDO2.setId(0L);
        postDO2.setTenant_id(0L);
        postDO2.setName("name");
        postDO2.setCode("code");
        postDO2.setSort(0);
        postDO2.setStatus(0);
        postDO2.setDisplayState(false);
        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(postDO2);

        // Run the test
        postServiceImplUnderTest.updatePost(reqVO);

        // Verify the results
        // Confirm PostMapper.updateById(...).
        final PostDO entity = new PostDO();
        entity.setId(0L);
        entity.setTenant_id(0L);
        entity.setName("name");
        entity.setCode("code");
        entity.setSort(0);
        entity.setStatus(0);
        entity.setDisplayState(false);
        verify(mockPostMapper).updateById(entity);
    }

    @Test
    public void testUpdatePost_PostMapperSelectByIdReturnsNull() {
        // Setup
        final PostUpdateReqVO reqVO = new PostUpdateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");
        reqVO.setId(0L);

        when(mockPostMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> postServiceImplUnderTest.updatePost(reqVO));
    }

    @Test
    public void testUpdatePost_PostMapperSelectByNameReturnsNull() {
        // Setup
        final PostUpdateReqVO reqVO = new PostUpdateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");
        reqVO.setId(0L);

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        when(mockPostMapper.selectByName("name", 0L)).thenReturn(null);

        // Configure PostMapper.selectByCode(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(postDO1);

        // Run the test
        postServiceImplUnderTest.updatePost(reqVO);

        // Verify the results
        // Confirm PostMapper.updateById(...).
        final PostDO entity = new PostDO();
        entity.setId(0L);
        entity.setTenant_id(0L);
        entity.setName("name");
        entity.setCode("code");
        entity.setSort(0);
        entity.setStatus(0);
        entity.setDisplayState(false);
        verify(mockPostMapper).updateById(entity);
    }

    @Test
    public void testUpdatePost_PostMapperSelectByCodeReturnsNull() {
        // Setup
        final PostUpdateReqVO reqVO = new PostUpdateReqVO();
        reqVO.setName("name");
        reqVO.setCode("code");
        reqVO.setId(0L);

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure PostMapper.selectByName(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByName("name", 0L)).thenReturn(postDO1);

        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(null);

        // Run the test
        postServiceImplUnderTest.updatePost(reqVO);

        // Verify the results
        // Confirm PostMapper.updateById(...).
        final PostDO entity = new PostDO();
        entity.setId(0L);
        entity.setTenant_id(0L);
        entity.setName("name");
        entity.setCode("code");
        entity.setSort(0);
        entity.setStatus(0);
        entity.setDisplayState(false);
        verify(mockPostMapper).updateById(entity);
    }

    @Test
    public void testDeletePost() {
        // Setup
        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Run the test
        postServiceImplUnderTest.deletePost(0L);

        // Verify the results
        verify(mockPostMapper).deleteById(0L);
    }

    @Test
    public void testDeletePost_PostMapperSelectByIdReturnsNull() {
        // Setup
        when(mockPostMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> postServiceImplUnderTest.deletePost(0L));
    }

    @Test
    public void testGetPosts1() {
        // Setup
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        final List<PostDO> expectedResult = Arrays.asList(postDO);
        when(mockDeptMapper.selectTenantIdByDeptId(0L)).thenReturn(0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectList(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        final List<PostDO> postDOS = Arrays.asList(postDO1);
        when(mockPostMapper.selectList(Arrays.asList(0L), Arrays.asList(0), 0L, false)).thenReturn(postDOS);

        // Run the test
        final List<PostDO> result = postServiceImplUnderTest.getPosts(Arrays.asList(0L), Arrays.asList(0), 0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts1_PostMapperReturnsNoItems() {
        // Setup
        when(mockDeptMapper.selectTenantIdByDeptId(0L)).thenReturn(0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);
        when(mockPostMapper.selectList(Arrays.asList(0L), Arrays.asList(0), 0L, false))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<PostDO> result = postServiceImplUnderTest.getPosts(Arrays.asList(0L), Arrays.asList(0), 0L);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetPostPage() {
        // Setup
        final PostPageReqVO reqVO = new PostPageReqVO();
        reqVO.setId(0L);
        reqVO.setTenantId(0L);
        reqVO.setCode("code");
        reqVO.setName("name");
        reqVO.setTenant("tenant");

        final PostRespVO postRespVO = new PostRespVO();
        postRespVO.setName("name");
        postRespVO.setCode("code");
        postRespVO.setId(0L);
        postRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final PageResult<PostRespVO> expectedResult = new PageResult<>(Arrays.asList(postRespVO), 0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectPostPage(...).
        final PostRespVO postRespVO1 = new PostRespVO();
        postRespVO1.setName("name");
        postRespVO1.setCode("code");
        postRespVO1.setId(0L);
        postRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PostRespVO> postRespVOS = Arrays.asList(postRespVO1);
        final PostPageReqVO reqVO1 = new PostPageReqVO();
        reqVO1.setId(0L);
        reqVO1.setTenantId(0L);
        reqVO1.setCode("code");
        reqVO1.setName("name");
        reqVO1.setTenant("tenant");
        when(mockPostMapper.selectPostPage(any(IPage.class), eq(reqVO1))).thenReturn(postRespVOS);

        // Run the test
        final PageResult<PostRespVO> result = postServiceImplUnderTest.getPostPage(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPostPage_PostMapperReturnsNoItems() {
        // Setup
        final PostPageReqVO reqVO = new PostPageReqVO();
        reqVO.setId(0L);
        reqVO.setTenantId(0L);
        reqVO.setCode("code");
        reqVO.setName("name");
        reqVO.setTenant("tenant");

        final PostRespVO postRespVO = new PostRespVO();
        postRespVO.setName("name");
        postRespVO.setCode("code");
        postRespVO.setId(0L);
        postRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final PageResult<PostRespVO> expectedResult = new PageResult<>(Arrays.asList(postRespVO), 0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectPostPage(...).
        final PostPageReqVO reqVO1 = new PostPageReqVO();
        reqVO1.setId(0L);
        reqVO1.setTenantId(0L);
        reqVO1.setCode("code");
        reqVO1.setName("name");
        reqVO1.setTenant("tenant");
        when(mockPostMapper.selectPostPage(any(IPage.class), eq(reqVO1))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<PostRespVO> result = postServiceImplUnderTest.getPostPage(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts2() {
        // Setup
        final PostExportReqVO reqVO = new PostExportReqVO();
        reqVO.setId(0L);
        reqVO.setCode("code");
        reqVO.setName("name");
        reqVO.setSort(0);
        reqVO.setTenant("tenant");

        final PostExcelVO postExcelVO = new PostExcelVO();
        postExcelVO.setId(0L);
        postExcelVO.setCode("code");
        postExcelVO.setName("name");
        postExcelVO.setSort(0);
        postExcelVO.setStatus("status");
        final List<PostExcelVO> expectedResult = Arrays.asList(postExcelVO);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectPostList(...).
        final PostExcelVO postExcelVO1 = new PostExcelVO();
        postExcelVO1.setId(0L);
        postExcelVO1.setCode("code");
        postExcelVO1.setName("name");
        postExcelVO1.setSort(0);
        postExcelVO1.setStatus("status");
        final List<PostExcelVO> postExcelVOS = Arrays.asList(postExcelVO1);
        final PostExportReqVO reqVO1 = new PostExportReqVO();
        reqVO1.setId(0L);
        reqVO1.setCode("code");
        reqVO1.setName("name");
        reqVO1.setSort(0);
        reqVO1.setTenant("tenant");
        when(mockPostMapper.selectPostList(reqVO1, 0L)).thenReturn(postExcelVOS);

        // Run the test
        final List<PostExcelVO> result = postServiceImplUnderTest.getPosts(reqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts2_PostMapperReturnsNoItems() {
        // Setup
        final PostExportReqVO reqVO = new PostExportReqVO();
        reqVO.setId(0L);
        reqVO.setCode("code");
        reqVO.setName("name");
        reqVO.setSort(0);
        reqVO.setTenant("tenant");

        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectPostList(...).
        final PostExportReqVO reqVO1 = new PostExportReqVO();
        reqVO1.setId(0L);
        reqVO1.setCode("code");
        reqVO1.setName("name");
        reqVO1.setSort(0);
        reqVO1.setTenant("tenant");
        when(mockPostMapper.selectPostList(reqVO1, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PostExcelVO> result = postServiceImplUnderTest.getPosts(reqVO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetPost() {
        // Setup
        final PostDO expectedResult = new PostDO();
        expectedResult.setId(0L);
        expectedResult.setTenant_id(0L);
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setSort(0);
        expectedResult.setStatus(0);
        expectedResult.setDisplayState(false);

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Run the test
        final PostDO result = postServiceImplUnderTest.getPost(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testValidPosts() {
        // Setup
        // Configure PostMapper.selectBatchIds(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        final List<PostDO> postDOS = Arrays.asList(postDO);
        when(mockPostMapper.selectBatchIds(Arrays.asList("value"))).thenReturn(postDOS);

        // Run the test
        postServiceImplUnderTest.validPosts(Arrays.asList(0L));

        // Verify the results
    }

    @Test
    public void testValidPosts_PostMapperReturnsNoItems() {
        // Setup
        when(mockPostMapper.selectBatchIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        postServiceImplUnderTest.validPosts(Arrays.asList(0L));

        // Verify the results
    }

    @Test
    public void testGetPostList() {
        // Setup
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        final List<PostDO> expectedResult = Arrays.asList(postDO);

        // Configure PostMapper.selectList(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        final List<PostDO> postDOS = Arrays.asList(postDO1);
        when(mockPostMapper.selectList(0L)).thenReturn(postDOS);

        // Run the test
        final List<PostDO> result = postServiceImplUnderTest.getPostList(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPostList_PostMapperReturnsNoItems() {
        // Setup
        when(mockPostMapper.selectList(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PostDO> result = postServiceImplUnderTest.getPostList(0L);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testImportPost() {
        // Setup
        final List<PostImportExcelVO> importPosts = Arrays.asList(PostImportExcelVO.builder().build());
        final PostImportRespVO expectedResult = PostImportRespVO.builder()
                .createPostnames(Arrays.asList("value"))
                .build();

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure PostMapper.selectByName(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByName("name", 0L)).thenReturn(postDO1);

        // Configure PostMapper.selectByCode(...).
        final PostDO postDO2 = new PostDO();
        postDO2.setId(0L);
        postDO2.setTenant_id(0L);
        postDO2.setName("name");
        postDO2.setCode("code");
        postDO2.setSort(0);
        postDO2.setStatus(0);
        postDO2.setDisplayState(false);
        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(postDO2);

        // Run the test
        final PostImportRespVO result = postServiceImplUnderTest.importPost(importPosts);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm PostMapper.insertBatch(...).
        final PostDO postDO3 = new PostDO();
        postDO3.setId(0L);
        postDO3.setTenant_id(0L);
        postDO3.setName("name");
        postDO3.setCode("code");
        postDO3.setSort(0);
        postDO3.setStatus(0);
        postDO3.setDisplayState(false);
        final List<PostDO> entities = Arrays.asList(postDO3);
        verify(mockPostMapper).insertBatch(entities);
    }

    @Test
    public void testImportPost_PostMapperSelectByIdReturnsNull() {
        // Setup
        final List<PostImportExcelVO> importPosts = Arrays.asList(PostImportExcelVO.builder().build());
        when(mockPostMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> postServiceImplUnderTest.importPost(importPosts));
    }

    @Test
    public void testImportPost_PostMapperSelectByNameReturnsNull() {
        // Setup
        final List<PostImportExcelVO> importPosts = Arrays.asList(PostImportExcelVO.builder().build());
        final PostImportRespVO expectedResult = PostImportRespVO.builder()
                .createPostnames(Arrays.asList("value"))
                .build();

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        when(mockPostMapper.selectByName("name", 0L)).thenReturn(null);

        // Configure PostMapper.selectByCode(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(postDO1);

        // Run the test
        final PostImportRespVO result = postServiceImplUnderTest.importPost(importPosts);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm PostMapper.insertBatch(...).
        final PostDO postDO2 = new PostDO();
        postDO2.setId(0L);
        postDO2.setTenant_id(0L);
        postDO2.setName("name");
        postDO2.setCode("code");
        postDO2.setSort(0);
        postDO2.setStatus(0);
        postDO2.setDisplayState(false);
        final List<PostDO> entities = Arrays.asList(postDO2);
        verify(mockPostMapper).insertBatch(entities);
    }

    @Test
    public void testImportPost_PostMapperSelectByCodeReturnsNull() {
        // Setup
        final List<PostImportExcelVO> importPosts = Arrays.asList(PostImportExcelVO.builder().build());
        final PostImportRespVO expectedResult = PostImportRespVO.builder()
                .createPostnames(Arrays.asList("value"))
                .build();

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure PostMapper.selectByName(...).
        final PostDO postDO1 = new PostDO();
        postDO1.setId(0L);
        postDO1.setTenant_id(0L);
        postDO1.setName("name");
        postDO1.setCode("code");
        postDO1.setSort(0);
        postDO1.setStatus(0);
        postDO1.setDisplayState(false);
        when(mockPostMapper.selectByName("name", 0L)).thenReturn(postDO1);

        when(mockPostMapper.selectByCode("code", 0L)).thenReturn(null);

        // Run the test
        final PostImportRespVO result = postServiceImplUnderTest.importPost(importPosts);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm PostMapper.insertBatch(...).
        final PostDO postDO2 = new PostDO();
        postDO2.setId(0L);
        postDO2.setTenant_id(0L);
        postDO2.setName("name");
        postDO2.setCode("code");
        postDO2.setSort(0);
        postDO2.setStatus(0);
        postDO2.setDisplayState(false);
        final List<PostDO> entities = Arrays.asList(postDO2);
        verify(mockPostMapper).insertBatch(entities);
    }

    @Test
    public void testGetMaxSortPostByUser() {
        // Setup
        final PostDO expectedResult = new PostDO();
        expectedResult.setId(0L);
        expectedResult.setTenant_id(0L);
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setSort(0);
        expectedResult.setStatus(0);
        expectedResult.setDisplayState(false);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        when(mockDeptMapper.selectTenantIdByDeptId(0L)).thenReturn(0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectList(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        final List<PostDO> postDOS = Arrays.asList(postDO);
        when(mockPostMapper.selectList(Arrays.asList(0L), Arrays.asList(0), 0L, false)).thenReturn(postDOS);

        // Run the test
        final PostDO result = postServiceImplUnderTest.getMaxSortPostByUser(0L, Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMaxSortPostByUser_PostMapperReturnsNoItems() {
        // Setup
        final PostDO expectedResult = new PostDO();
        expectedResult.setId(0L);
        expectedResult.setTenant_id(0L);
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setSort(0);
        expectedResult.setStatus(0);
        expectedResult.setDisplayState(false);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        when(mockDeptMapper.selectTenantIdByDeptId(0L)).thenReturn(0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);
        when(mockPostMapper.selectList(Arrays.asList(0L), Arrays.asList(0), 0L, false))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PostDO result = postServiceImplUnderTest.getMaxSortPostByUser(0L, Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMinSortPostByUser() {
        // Setup
        final PostDO expectedResult = new PostDO();
        expectedResult.setId(0L);
        expectedResult.setTenant_id(0L);
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setSort(0);
        expectedResult.setStatus(0);
        expectedResult.setDisplayState(false);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        when(mockDeptMapper.selectTenantIdByDeptId(0L)).thenReturn(0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure PostMapper.selectList(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        final List<PostDO> postDOS = Arrays.asList(postDO);
        when(mockPostMapper.selectList(Arrays.asList(0L), Arrays.asList(0), 0L, false)).thenReturn(postDOS);

        // Run the test
        final PostDO result = postServiceImplUnderTest.getMinSortPostByUser(0L, Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMinSortPostByUser_PostMapperReturnsNoItems() {
        // Setup
        final PostDO expectedResult = new PostDO();
        expectedResult.setId(0L);
        expectedResult.setTenant_id(0L);
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setSort(0);
        expectedResult.setStatus(0);
        expectedResult.setDisplayState(false);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        when(mockDeptMapper.selectTenantIdByDeptId(0L)).thenReturn(0L);
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);
        when(mockPostMapper.selectList(Arrays.asList(0L), Arrays.asList(0), 0L, false))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PostDO result = postServiceImplUnderTest.getMinSortPostByUser(0L, Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetUserByPost() {
        // Setup
        when(mockUserPostMapper.selectUsersByPostCode(Arrays.asList("value"), 0L)).thenReturn(Arrays.asList(0L));

        // Run the test
        final List<Long> result = postServiceImplUnderTest.getUserByPost("code", 0L);

        // Verify the results
        assertEquals(Arrays.asList(0L), result);
    }

    @Test
    public void testGetUserByPost_UserPostMapperReturnsNoItems() {
        // Setup
        when(mockUserPostMapper.selectUsersByPostCode(Arrays.asList("value"), 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = postServiceImplUnderTest.getUserByPost("code", 0L);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testJudgeHRDirector() {
        // Setup
        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        when(mockUserPostMapper.selectUsersByPostCode(Arrays.asList("value"), 0L)).thenReturn(Arrays.asList(0L));

        // Run the test
        final Boolean result = postServiceImplUnderTest.judgeHRDirector(0L);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testJudgeHRDirector_UserPostMapperReturnsNoItems() {
        // Setup
        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        when(mockUserPostMapper.selectUsersByPostCode(Arrays.asList("value"), 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = postServiceImplUnderTest.judgeHRDirector(0L);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetPosts3() {
        // Setup
        final PostSimpleUserRespVO postSimpleUserRespVO = new PostSimpleUserRespVO();
        postSimpleUserRespVO.setId(0L);
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setTenantId(0L);
        postSimpleUserRespVO.setUsers(Arrays.asList(userSimpleRespVO));
        final List<PostSimpleUserRespVO> expectedResult = Arrays.asList(postSimpleUserRespVO);
        when(mockReceiveApi.getReceivedUserIds("processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure UserPostMapper.selectListByPostIds(...).
        final UserPostDO userPostDO = new UserPostDO();
        userPostDO.setId(0L);
        userPostDO.setUserId(0L);
        userPostDO.setPostId(0L);
        final List<UserPostDO> userPostDOS = Arrays.asList(userPostDO);
        when(mockUserPostMapper.selectListByPostIds(Arrays.asList(0L))).thenReturn(userPostDOS);

        // Configure AdminUserService.getUsers(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build());
        when(mockUserService.getUsers(Arrays.asList(0L))).thenReturn(adminUserDOS);

        // Run the test
        final List<PostSimpleUserRespVO> result = postServiceImplUnderTest.getPosts("processInstanceId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts3_ReceiveApiReturnsNoItems() {
        // Setup
        final PostSimpleUserRespVO postSimpleUserRespVO = new PostSimpleUserRespVO();
        postSimpleUserRespVO.setId(0L);
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setTenantId(0L);
        postSimpleUserRespVO.setUsers(Arrays.asList(userSimpleRespVO));
        final List<PostSimpleUserRespVO> expectedResult = Arrays.asList(postSimpleUserRespVO);
        when(mockReceiveApi.getReceivedUserIds("processInstanceId"))
                .thenReturn(CommonResult.success(Collections.emptyList()));

        // Configure UserPostMapper.selectListByPostIds(...).
        final UserPostDO userPostDO = new UserPostDO();
        userPostDO.setId(0L);
        userPostDO.setUserId(0L);
        userPostDO.setPostId(0L);
        final List<UserPostDO> userPostDOS = Arrays.asList(userPostDO);
        when(mockUserPostMapper.selectListByPostIds(Arrays.asList(0L))).thenReturn(userPostDOS);

        // Configure AdminUserService.getUsers(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build());
        when(mockUserService.getUsers(Arrays.asList(0L))).thenReturn(adminUserDOS);

        // Run the test
        final List<PostSimpleUserRespVO> result = postServiceImplUnderTest.getPosts("processInstanceId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts3_ReceiveApiReturnsError() {
        // Setup
        final PostSimpleUserRespVO postSimpleUserRespVO = new PostSimpleUserRespVO();
        postSimpleUserRespVO.setId(0L);
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setTenantId(0L);
        postSimpleUserRespVO.setUsers(Arrays.asList(userSimpleRespVO));
        final List<PostSimpleUserRespVO> expectedResult = Arrays.asList(postSimpleUserRespVO);
        when(mockReceiveApi.getReceivedUserIds("processInstanceId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure UserPostMapper.selectListByPostIds(...).
        final UserPostDO userPostDO = new UserPostDO();
        userPostDO.setId(0L);
        userPostDO.setUserId(0L);
        userPostDO.setPostId(0L);
        final List<UserPostDO> userPostDOS = Arrays.asList(userPostDO);
        when(mockUserPostMapper.selectListByPostIds(Arrays.asList(0L))).thenReturn(userPostDOS);

        // Configure AdminUserService.getUsers(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build());
        when(mockUserService.getUsers(Arrays.asList(0L))).thenReturn(adminUserDOS);

        // Run the test
        final List<PostSimpleUserRespVO> result = postServiceImplUnderTest.getPosts("processInstanceId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts3_UserPostMapperReturnsNoItems() {
        // Setup
        final PostSimpleUserRespVO postSimpleUserRespVO = new PostSimpleUserRespVO();
        postSimpleUserRespVO.setId(0L);
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setTenantId(0L);
        postSimpleUserRespVO.setUsers(Arrays.asList(userSimpleRespVO));
        final List<PostSimpleUserRespVO> expectedResult = Arrays.asList(postSimpleUserRespVO);
        when(mockReceiveApi.getReceivedUserIds("processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));
        when(mockUserPostMapper.selectListByPostIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure AdminUserService.getUsers(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .postIds(new HashSet<>(Arrays.asList(0L)))
                .build());
        when(mockUserService.getUsers(Arrays.asList(0L))).thenReturn(adminUserDOS);

        // Run the test
        final List<PostSimpleUserRespVO> result = postServiceImplUnderTest.getPosts("processInstanceId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPosts3_AdminUserServiceReturnsNoItems() {
        // Setup
        final PostSimpleUserRespVO postSimpleUserRespVO = new PostSimpleUserRespVO();
        postSimpleUserRespVO.setId(0L);
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setTenantId(0L);
        postSimpleUserRespVO.setUsers(Arrays.asList(userSimpleRespVO));
        final List<PostSimpleUserRespVO> expectedResult = Arrays.asList(postSimpleUserRespVO);
        when(mockReceiveApi.getReceivedUserIds("processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure UserPostMapper.selectListByPostIds(...).
        final UserPostDO userPostDO = new UserPostDO();
        userPostDO.setId(0L);
        userPostDO.setUserId(0L);
        userPostDO.setPostId(0L);
        final List<UserPostDO> userPostDOS = Arrays.asList(userPostDO);
        when(mockUserPostMapper.selectListByPostIds(Arrays.asList(0L))).thenReturn(userPostDOS);

        when(mockUserService.getUsers(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PostSimpleUserRespVO> result = postServiceImplUnderTest.getPosts("processInstanceId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPostByCode() {
        // Setup
        final PostDO expectedResult = new PostDO();
        expectedResult.setId(0L);
        expectedResult.setTenant_id(0L);
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setSort(0);
        expectedResult.setStatus(0);
        expectedResult.setDisplayState(false);

        // Run the test
        final PostDO result = postServiceImplUnderTest.getPostByCode("postCode", 0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveUserPosts() {
        // Setup
        // Run the test
        postServiceImplUnderTest.saveUserPosts(0L, new HashSet<>(Arrays.asList(0L)));

        // Verify the results
        // Confirm UserPostMapper.insertBatch(...).
        final UserPostDO userPostDO = new UserPostDO();
        userPostDO.setId(0L);
        userPostDO.setUserId(0L);
        userPostDO.setPostId(0L);
        final List<UserPostDO> entities = Arrays.asList(userPostDO);
        verify(mockUserPostMapper).insertBatch(entities);
    }

    @Test
    public void testGetPostUsers() {
        // Setup
        final PostUsersPageReqVO pageReqVO = new PostUsersPageReqVO();
        pageReqVO.setPostId(0L);

        final PostUsersRespVO expectedResult = new PostUsersRespVO();
        expectedResult.setName("name");
        expectedResult.setCode("code");
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setTenantId(0L);
        expectedResult.setUsers(new PageResult<>(Arrays.asList(userSimpleRespVO), 0L));

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure AdminUserService.getUsersByPostUsers(...).
        final UserSimpleRespVO userSimpleRespVO1 = new UserSimpleRespVO();
        userSimpleRespVO1.setId(0L);
        userSimpleRespVO1.setUid("uid");
        userSimpleRespVO1.setUsername("username");
        userSimpleRespVO1.setNickname("nickname");
        userSimpleRespVO1.setTenantId(0L);
        final PageResult<UserSimpleRespVO> userSimpleRespVOPageResult = new PageResult<>(
                Arrays.asList(userSimpleRespVO1), 0L);
        final PostUsersPageReqVO pageReqVO1 = new PostUsersPageReqVO();
        pageReqVO1.setPostId(0L);
        when(mockUserService.getUsersByPostUsers(pageReqVO1)).thenReturn(userSimpleRespVOPageResult);

        // Run the test
        final PostUsersRespVO result = postServiceImplUnderTest.getPostUsers(pageReqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPostUsers_AdminUserServiceReturnsNoItem() {
        // Setup
        final PostUsersPageReqVO pageReqVO = new PostUsersPageReqVO();
        pageReqVO.setPostId(0L);

        final PostUsersRespVO expectedResult = new PostUsersRespVO();
        expectedResult.setName("name");
        expectedResult.setCode("code");
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setTenantId(0L);
        expectedResult.setUsers(new PageResult<>(Arrays.asList(userSimpleRespVO), 0L));

        // Configure PostMapper.selectById(...).
        final PostDO postDO = new PostDO();
        postDO.setId(0L);
        postDO.setTenant_id(0L);
        postDO.setName("name");
        postDO.setCode("code");
        postDO.setSort(0);
        postDO.setStatus(0);
        postDO.setDisplayState(false);
        when(mockPostMapper.selectById(0L)).thenReturn(postDO);

        // Configure AdminUserService.getUsersByPostUsers(...).
        final PostUsersPageReqVO pageReqVO1 = new PostUsersPageReqVO();
        pageReqVO1.setPostId(0L);
        when(mockUserService.getUsersByPostUsers(pageReqVO1)).thenReturn(PageResult.empty());

        // Run the test
        final PostUsersRespVO result = postServiceImplUnderTest.getPostUsers(pageReqVO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
