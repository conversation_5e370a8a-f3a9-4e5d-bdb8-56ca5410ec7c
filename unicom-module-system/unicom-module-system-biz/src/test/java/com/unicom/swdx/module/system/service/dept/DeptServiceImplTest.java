package com.unicom.swdx.module.system.service.dept;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.dept.dto.DeptDTO;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.mq.producer.dept.DeptProducer;
import com.unicom.swdx.module.system.mq.producer.tenant.TenantProducer;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DeptServiceImplTest {

    @Mock
    private DeptMapper mockDeptMapper;
    @Mock
    private TenantService mockTenantService;
    @Mock
    private TenantProducer mockTenantProducer;
    @Mock
    private DeptProducer mockDeptProducer;
    @Mock
    private AdminUserMapper mockUserMapper;
    @Mock
    private DeptService mockSelf;
    @Mock
    private PermissionService mockPermissionService;

    @InjectMocks
    private DeptServiceImpl deptServiceImplUnderTest;

    @Test
    public void testInitLocalCachefixtime() {
        // Setup
        // Run the test
        deptServiceImplUnderTest.initLocalCachefixtime();

        // Verify the results
    }

    @Test
    public void testInitLocalCache() {
        // Setup
        // Run the test
        deptServiceImplUnderTest.initLocalCache();

        // Verify the results
    }

    @Test
    public void testCreateDept() {
        // Setup
        final DeptCreateReqVO reqVO = new DeptCreateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);

        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        // Configure DeptMapper.selectByParentIdAndName(...).
        final DeptDO deptDO1 = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectByParentIdAndName(0L, "name")).thenReturn(deptDO1);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO);

        // Run the test
        final Long result = deptServiceImplUnderTest.createDept(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
        verify(mockDeptMapper).insert(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300001", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testCreateDept_DeptMapperSelectByIdReturnsNull() {
        // Setup
        final DeptCreateReqVO reqVO = new DeptCreateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);

        when(mockDeptMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> deptServiceImplUnderTest.createDept(reqVO));
    }

    @Test
    public void testCreateDept_DeptMapperSelectByParentIdAndNameReturnsNull() {
        // Setup
        final DeptCreateReqVO reqVO = new DeptCreateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);

        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        when(mockDeptMapper.selectByParentIdAndName(0L, "name")).thenReturn(null);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO);

        // Run the test
        final Long result = deptServiceImplUnderTest.createDept(reqVO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
        verify(mockDeptMapper).insert(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300001", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testUpdateDept() {
        // Setup
        final DeptUpdateReqVO reqVO = new DeptUpdateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);
        reqVO.setId(0L);

        // Configure DeptMapper.selectChildrenDept(...).
        final List<DeptDO> deptDOS = Arrays.asList(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(deptDOS);

        when(mockUserMapper.selectCountByDeptId(0L)).thenReturn(0L);
        when(mockUserMapper.selectCountByDeptIdList(Arrays.asList(0L))).thenReturn(0L);

        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        // Configure DeptMapper.selectByParentIdAndName(...).
        final DeptDO deptDO1 = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectByParentIdAndName(0L, "name")).thenReturn(deptDO1);

        // Configure TenantService.getTenant(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenant(0L)).thenReturn(tenantDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO1 = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO1);

        // Run the test
        deptServiceImplUnderTest.updateDept(reqVO);

        // Verify the results
        verify(mockDeptMapper).updateStatusByDeptIdList(Arrays.asList(0L), 0);
        verify(mockDeptMapper).updateById(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        verify(mockTenantService).updateById(TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build());
        verify(mockTenantProducer).sendTenantRefreshMessage();
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300002", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testUpdateDept_DeptMapperSelectChildrenDeptReturnsNoItems() {
        // Setup
        final DeptUpdateReqVO reqVO = new DeptUpdateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);
        reqVO.setId(0L);

        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(Collections.emptyList());
        when(mockUserMapper.selectCountByDeptId(0L)).thenReturn(0L);
        when(mockUserMapper.selectCountByDeptIdList(Arrays.asList(0L))).thenReturn(0L);

        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        // Configure DeptMapper.selectByParentIdAndName(...).
        final DeptDO deptDO1 = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectByParentIdAndName(0L, "name")).thenReturn(deptDO1);

        // Configure TenantService.getTenant(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenant(0L)).thenReturn(tenantDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO1 = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO1);

        // Run the test
        deptServiceImplUnderTest.updateDept(reqVO);

        // Verify the results
        verify(mockDeptMapper).updateStatusByDeptIdList(Arrays.asList(0L), 0);
        verify(mockDeptMapper).updateById(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        verify(mockTenantService).updateById(TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build());
        verify(mockTenantProducer).sendTenantRefreshMessage();
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300002", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testUpdateDept_DeptMapperSelectByIdReturnsNull() {
        // Setup
        final DeptUpdateReqVO reqVO = new DeptUpdateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);
        reqVO.setId(0L);

        // Configure DeptMapper.selectChildrenDept(...).
        final List<DeptDO> deptDOS = Arrays.asList(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(deptDOS);

        when(mockUserMapper.selectCountByDeptId(0L)).thenReturn(0L);
        when(mockUserMapper.selectCountByDeptIdList(Arrays.asList(0L))).thenReturn(0L);
        when(mockDeptMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> deptServiceImplUnderTest.updateDept(reqVO));
        verify(mockDeptMapper).updateStatusByDeptIdList(Arrays.asList(0L), 0);
    }

    @Test
    public void testUpdateDept_DeptMapperSelectByParentIdAndNameReturnsNull() {
        // Setup
        final DeptUpdateReqVO reqVO = new DeptUpdateReqVO();
        reqVO.setName("name");
        reqVO.setParentId(0L);
        reqVO.setStatus(0);
        reqVO.setId(0L);

        // Configure DeptMapper.selectChildrenDept(...).
        final List<DeptDO> deptDOS = Arrays.asList(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(deptDOS);

        when(mockUserMapper.selectCountByDeptId(0L)).thenReturn(0L);
        when(mockUserMapper.selectCountByDeptIdList(Arrays.asList(0L))).thenReturn(0L);

        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        when(mockDeptMapper.selectByParentIdAndName(0L, "name")).thenReturn(null);

        // Configure TenantService.getTenant(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenant(0L)).thenReturn(tenantDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO1 = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO1);

        // Run the test
        deptServiceImplUnderTest.updateDept(reqVO);

        // Verify the results
        verify(mockDeptMapper).updateStatusByDeptIdList(Arrays.asList(0L), 0);
        verify(mockDeptMapper).updateById(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        verify(mockTenantService).updateById(TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build());
        verify(mockTenantProducer).sendTenantRefreshMessage();
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300002", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testDeleteDept() {
        // Setup
        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO);

        // Configure DeptMapper.selectChildrenDept(...).
        final List<DeptDO> deptDOS = Arrays.asList(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(deptDOS);

        // Run the test
        deptServiceImplUnderTest.deleteDept(0L);

        // Verify the results
        verify(mockDeptMapper).deleteBatchIds(Arrays.asList("value"));
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300003", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testDeleteDept_DeptMapperSelectByIdReturnsNull() {
        // Setup
        when(mockDeptMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> deptServiceImplUnderTest.deleteDept(0L));
    }

    @Test
    public void testDeleteDept_DeptMapperSelectChildrenDeptReturnsNoItems() {
        // Setup
        // Configure DeptMapper.selectById(...).
        final DeptDO deptDO = DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build();
        when(mockDeptMapper.selectById(0L)).thenReturn(deptDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO = TenantDO.builder()
                .name("name")
                .tenantCode("tenantCode")
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO);

        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(Collections.emptyList());

        // Run the test
        deptServiceImplUnderTest.deleteDept(0L);

        // Verify the results
        verify(mockDeptMapper).deleteBatchIds(Arrays.asList("value"));
        verify(mockDeptProducer).sendRefreshMessage();
        verify(mockDeptProducer).sendDeptData("00300003", DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build(), "tenantCode");
    }

    @Test
    public void testGetChildrenDeptCount() {
        // Setup
        // Configure DeptMapper.selectChildrenDept(...).
        final List<DeptDO> deptDOS = Arrays.asList(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(deptDOS);

        // Run the test
        final Integer result = deptServiceImplUnderTest.getChildrenDeptCount(0L);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testGetChildrenDeptCount_DeptMapperReturnsNoItems() {
        // Setup
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = deptServiceImplUnderTest.getChildrenDeptCount(0L);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testGetAllDeptUsers() {
        // Setup
        // Configure DeptMapper.selectChildrenDept(...).
        final List<DeptDO> deptDOS = Arrays.asList(DeptDO.builder()
                .id(0L)
                .name("name")
                .parentId(0L)
                .sort(0)
                .leaderUserId(0L)
                .status(0)
                .build());
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(deptDOS);

        when(mockUserMapper.selectCountByDeptIdList(Arrays.asList(0L))).thenReturn(0L);

        // Run the test
        final Long result = deptServiceImplUnderTest.getAllDeptUsers(0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testGetAllDeptUsers_DeptMapperReturnsNoItems() {
        // Setup
        when(mockDeptMapper.selectChildrenDept(0L)).thenReturn(Collections.emptyList());
        when(mockUserMapper.selectCountByDeptIdList(Arrays.asList(0L))).thenReturn(0L);

        // Run the test
        final Long result = deptServiceImplUnderTest.getAllDeptUsers(0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }






}
