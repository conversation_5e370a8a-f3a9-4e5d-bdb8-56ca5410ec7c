package com.unicom.swdx.module.system.controller.admin.message;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;
import com.unicom.swdx.module.system.service.message.MessageAuthorityService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
//import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(MessageAuthorityController.class)
public class MessageAuthorityControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MessageAuthorityService mockMessageAuthorityService;

    @Test
    public void testCreateMessage() throws Exception {
        // Setup
        // Configure MessageAuthorityService.createMessageAuthority(...).
        final MessageAuthorityCreateReqVO createReqVO = new MessageAuthorityCreateReqVO();
        createReqVO.setUserId(0L);
        createReqVO.setDataScopeIds("dataScopeIds");
        createReqVO.setDataScopeNames("dataScopeNames");
        createReqVO.setName("name");
        createReqVO.setPhone("phone");
        when(mockMessageAuthorityService.createMessageAuthority(createReqVO)).thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/system/messageAuthority/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
//                      .with(user("username"))
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
//        assertEquals(HttpStatus.OK.value(), response.getStatus());
//        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetMessagePage() throws Exception {
        // Setup
        // Configure MessageAuthorityService.getMessageAuthorityPage(...).
        final PageResult<MessageAuthorityDO> messageAuthorityDOPageResult = new PageResult<>(
                Arrays.asList(MessageAuthorityDO.builder().build()), 0L);
        final MessageAuthorityPageReqVO pageReqVO = new MessageAuthorityPageReqVO();
        pageReqVO.setName("name");
        when(mockMessageAuthorityService.getMessageAuthorityPage(pageReqVO)).thenReturn(messageAuthorityDOPageResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/system/messageAuthority/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        //                        .with(user("username"))
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
//        assertEquals(HttpStatus.OK.value(), response.getStatus());
//        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetMessagePage_MessageAuthorityServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure MessageAuthorityService.getMessageAuthorityPage(...).
        final MessageAuthorityPageReqVO pageReqVO = new MessageAuthorityPageReqVO();
        pageReqVO.setName("name");
        when(mockMessageAuthorityService.getMessageAuthorityPage(pageReqVO)).thenReturn(PageResult.empty());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/system/messageAuthority/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        //                        .with(user("username"))
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
//        assertEquals(HttpStatus.OK.value(), response.getStatus());
//        assertEquals("", response.getContentAsString());
    }

    @Test
    public void testGetMessage() throws Exception {
        // Setup
        when(mockMessageAuthorityService.getMessageAuthority(0L)).thenReturn(MessageAuthorityDO.builder().build());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/system/messageAuthority/get")
                        .param("id", "0")
                        //                        .with(user("username"))
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
//        assertEquals(HttpStatus.OK.value(), response.getStatus());
//        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateMessage() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/system/messageAuthority/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        //                        .with(user("username"))
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
//        assertEquals(HttpStatus.OK.value(), response.getStatus());
//        assertEquals("expectedResponse", response.getContentAsString());

        // Confirm MessageAuthorityService.updateMessageAuthority(...).
        final MessageAuthorityUpdateReqVO updateReqVO = new MessageAuthorityUpdateReqVO();
        updateReqVO.setDataScopeIds("dataScopeIds");
        updateReqVO.setDataScopeNames("dataScopeNames");
        updateReqVO.setUserId(0L);
        updateReqVO.setId(0L);
        updateReqVO.setName("name");
//        verify(mockMessageAuthorityService).updateMessageAuthority(updateReqVO);
    }

    @Test
    public void testDeleteMessage() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/system/messageAuthority/delete")
                        .param("id", "0")
                        //                        .with(user("username"))
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
//        assertEquals(HttpStatus.OK.value(), response.getStatus());
//        assertEquals("expectedResponse", response.getContentAsString());
//        verify(mockMessageAuthorityService).deleteMessageAuthority(0L);
    }
}
