package com.unicom.swdx.module.system.service.permission;

import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleMenuDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.UserRoleDO;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMenuBatchInsertMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMenuMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.UserRoleBatchInsertMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.UserRoleMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantTypeMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantTypeRoleMapper;
import com.unicom.swdx.module.system.mq.producer.permission.PermissionProducer;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import com.unicom.swdx.module.system.service.subsystem.SubsystemService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.AsyncListenableTaskExecutor;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PermissionServiceImplTest {

    @Mock
    private RoleMenuMapper mockRoleMenuMapper;
    @Mock
    private RoleMenuBatchInsertMapper mockRoleMenuBatchInsertMapper;
    @Mock
    private UserRoleMapper mockUserRoleMapper;
    @Mock
    private UserRoleBatchInsertMapper mockUserRoleBatchInsertMapper;
    @Mock
    private RoleService mockRoleService;
    @Mock
    private RoleGroupService mockRoleGroupService;
    @Mock
    private MenuService mockMenuService;
    @Mock
    private DeptService mockDeptService;
    @Mock
    private AdminUserService mockUserService;
    @Mock
    private PermissionProducer mockPermissionProducer;
    @Mock
    private TenantService mockTenantService;
    @Mock
    private PermissionService mockSelf;
    @Mock
    private TenantTypeRoleMapper mockTenantTypeRoleMapper;
    @Mock
    private TenantTypeMapper mockTenantTypeMapper;
    @Mock
    private OAuth2ClientService mockClientService;
    @Mock
    private SubsystemService mockSubsystemService;
    @Mock
    private AsyncListenableTaskExecutor mockTaskExecutor;

    @InjectMocks
    private PermissionServiceImpl permissionServiceImplUnderTest;

    @BeforeEach
    void 初始化() {
        permissionServiceImplUnderTest.subsystemService = mockSubsystemService;
    }

    @Test
    void testInitRoleMenuLocalCache() {
        // 初始化
        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS);

        // 运行测试
        permissionServiceImplUnderTest.initRoleMenuLocalCache();
    }

    @Test
    void testInitRoleMenuLocalCache_RoleMenuMapperReturnsNoItems() {
        // 初始化
        when(mockRoleMenuMapper.selectList()).thenReturn(Collections.emptyList());

        // 运行测试
        permissionServiceImplUnderTest.initRoleMenuLocalCache();

    }

    @Test
    void testInitUserRoleLocalCache() {
        // 初始化
        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS);

        // 运行测试
        permissionServiceImplUnderTest.initUserRoleLocalCache();
        
    }

   

    @Test
    void testGetRoleMenuIds_RoleServiceReturnsTrue() {
        // 初始化
        when(mockRoleService.hasAnySuperAdmin(new HashSet<>(Arrays.asList(0L)))).thenReturn(true);

        // 配置 MenuService.list(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setClientId(0L);
        menuDO.setName("name");
        menuDO.setPermission("permission");
        menuDO.setType(0);
        final List<MenuDO> menuDOS = Arrays.asList(menuDO);
        when(mockMenuService.list()).thenReturn(menuDOS);

        // 运行测试
        final Set<Long> result = permissionServiceImplUnderTest.getRoleMenuIds(0L);

        // 验证结果
        assertThat(result).isEqualTo(new HashSet<>(Arrays.asList(0L)));
    }

    @Test
    void testGetRoleMenuIds_MenuServiceReturnsNoItems() {
        // 初始化
        when(mockRoleService.hasAnySuperAdmin(new HashSet<>(Arrays.asList(0L)))).thenReturn(true);
        when(mockMenuService.list()).thenReturn(Collections.emptyList());

        // 运行测试
        final Set<Long> result = permissionServiceImplUnderTest.getRoleMenuIds(0L);

        // 验证结果
        assertThat(result).isEqualTo(Collections.emptySet());
    }

    @Test
    void testGetRoleMenuIds_RoleMenuMapperReturnsNoItems() {
        // 初始化
        when(mockRoleService.hasAnySuperAdmin(new HashSet<>(Arrays.asList(0L)))).thenReturn(false);
        when(mockRoleMenuMapper.selectListByRoleId(0L)).thenReturn(Collections.emptyList());

        // 运行测试
        final Set<Long> result = permissionServiceImplUnderTest.getRoleMenuIds(0L);

        // 验证结果
        assertThat(result).isEqualTo(Collections.emptySet());
    }

    @Test
    void testAssignRoleMenu() {
        // 初始化
        // 配置 RoleMenuMapper.selectListByRoleId(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectListByRoleId(0L)).thenReturn(roleMenuDOS);

        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS);

        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO1 = new RoleMenuDO();
        roleMenuDO1.setId(0L);
        roleMenuDO1.setRoleId(0L);
        roleMenuDO1.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS1 = Arrays.asList(roleMenuDO1);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS1);

        // 运行测试
        permissionServiceImplUnderTest.assignRoleMenu(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 RoleMenuBatchInsertMapper.saveBatch(...).
        final RoleMenuDO roleMenuDO2 = new RoleMenuDO();
        roleMenuDO2.setId(0L);
        roleMenuDO2.setRoleId(0L);
        roleMenuDO2.setMenuId(0L);
        final List<RoleMenuDO> entityList = Arrays.asList(roleMenuDO2);
        verify(mockRoleMenuBatchInsertMapper).saveBatch(entityList);
        verify(mockRoleMenuMapper).deleteListByRoleIdAndMenuIds(0L, Arrays.asList(0L));
        verify(mockPermissionProducer).sendRoleMenuRefreshMessage();
    }

    @Test
    void testAssignRoleMenu_RoleMenuMapperSelectListByRoleIdReturnsNoItems() {
        // 初始化
        when(mockRoleMenuMapper.selectListByRoleId(0L)).thenReturn(Collections.emptyList());

        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS);

        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS);

        // 运行测试
        permissionServiceImplUnderTest.assignRoleMenu(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 RoleMenuBatchInsertMapper.saveBatch(...).
        final RoleMenuDO roleMenuDO1 = new RoleMenuDO();
        roleMenuDO1.setId(0L);
        roleMenuDO1.setRoleId(0L);
        roleMenuDO1.setMenuId(0L);
        final List<RoleMenuDO> entityList = Arrays.asList(roleMenuDO1);
        verify(mockRoleMenuBatchInsertMapper).saveBatch(entityList);
        verify(mockRoleMenuMapper).deleteListByRoleIdAndMenuIds(0L, Arrays.asList(0L));
        verify(mockPermissionProducer).sendRoleMenuRefreshMessage();
    }

    @Test
    void testAssignRoleMenu_UserRoleMapperReturnsNoItems() {
        // 初始化
        // 配置 RoleMenuMapper.selectListByRoleId(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectListByRoleId(0L)).thenReturn(roleMenuDOS);

        when(mockUserRoleMapper.selectList()).thenReturn(Collections.emptyList());

        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO1 = new RoleMenuDO();
        roleMenuDO1.setId(0L);
        roleMenuDO1.setRoleId(0L);
        roleMenuDO1.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS1 = Arrays.asList(roleMenuDO1);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS1);

        // 运行测试
        permissionServiceImplUnderTest.assignRoleMenu(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 RoleMenuBatchInsertMapper.saveBatch(...).
        final RoleMenuDO roleMenuDO2 = new RoleMenuDO();
        roleMenuDO2.setId(0L);
        roleMenuDO2.setRoleId(0L);
        roleMenuDO2.setMenuId(0L);
        final List<RoleMenuDO> entityList = Arrays.asList(roleMenuDO2);
        verify(mockRoleMenuBatchInsertMapper).saveBatch(entityList);
        verify(mockRoleMenuMapper).deleteListByRoleIdAndMenuIds(0L, Arrays.asList(0L));
        verify(mockPermissionProducer).sendRoleMenuRefreshMessage();
    }

    @Test
    void testAssignRoleMenu_RoleMenuMapperSelectListReturnsNoItems() {
        // 初始化
        // 配置 RoleMenuMapper.selectListByRoleId(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectListByRoleId(0L)).thenReturn(roleMenuDOS);

        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS);

        when(mockRoleMenuMapper.selectList()).thenReturn(Collections.emptyList());

        // 运行测试
        permissionServiceImplUnderTest.assignRoleMenu(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 RoleMenuBatchInsertMapper.saveBatch(...).
        final RoleMenuDO roleMenuDO1 = new RoleMenuDO();
        roleMenuDO1.setId(0L);
        roleMenuDO1.setRoleId(0L);
        roleMenuDO1.setMenuId(0L);
        final List<RoleMenuDO> entityList = Arrays.asList(roleMenuDO1);
        verify(mockRoleMenuBatchInsertMapper).saveBatch(entityList);
        verify(mockRoleMenuMapper).deleteListByRoleIdAndMenuIds(0L, Arrays.asList(0L));
        verify(mockPermissionProducer).sendRoleMenuRefreshMessage();
    }

    @Test
    void testAssignRoleDataScope() {
        // 初始化
        // 运行测试
        permissionServiceImplUnderTest.assignRoleDataScope(0L, 0, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        verify(mockRoleService).updateRoleDataScope(0L, 0, new HashSet<>(Arrays.asList(0L)));
    }

    @Test
    void testAssignUserRole() {
        // 初始化
        // 配置 UserRoleMapper.selectListByUserId(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectListByUserId(0L)).thenReturn(userRoleDOS);

        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO1 = new UserRoleDO();
        userRoleDO1.setId(0L);
        userRoleDO1.setUserId(0L);
        userRoleDO1.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS1 = Arrays.asList(userRoleDO1);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS1);

        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // 运行测试
        permissionServiceImplUnderTest.assignUserRole(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 UserRoleBatchInsertMapper.saveBatch(...).
        final UserRoleDO userRoleDO2 = new UserRoleDO();
        userRoleDO2.setId(0L);
        userRoleDO2.setUserId(0L);
        userRoleDO2.setRoleId(0L);
        final List<UserRoleDO> entityList = Arrays.asList(userRoleDO2);
        verify(mockUserRoleBatchInsertMapper).saveBatch(entityList);
        verify(mockUserRoleMapper).deleteListByUserIdAndRoleIdIds(0L, Arrays.asList(0L));
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockPermissionProducer).sendUserRoleRefreshMessage();
    }

    @Test
    void testAssignUserRole_UserRoleMapperSelectListByUserIdReturnsNoItems() {
        // 初始化
        when(mockUserRoleMapper.selectListByUserId(0L)).thenReturn(Collections.emptyList());

        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS);

        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // 运行测试
        permissionServiceImplUnderTest.assignUserRole(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 UserRoleBatchInsertMapper.saveBatch(...).
        final UserRoleDO userRoleDO1 = new UserRoleDO();
        userRoleDO1.setId(0L);
        userRoleDO1.setUserId(0L);
        userRoleDO1.setRoleId(0L);
        final List<UserRoleDO> entityList = Arrays.asList(userRoleDO1);
        verify(mockUserRoleBatchInsertMapper).saveBatch(entityList);
        verify(mockUserRoleMapper).deleteListByUserIdAndRoleIdIds(0L, Arrays.asList(0L));
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockPermissionProducer).sendUserRoleRefreshMessage();
    }

    @Test
    void testAssignUserRole_UserRoleMapperSelectListReturnsNoItems() {
        // 初始化
        // 配置 UserRoleMapper.selectListByUserId(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectListByUserId(0L)).thenReturn(userRoleDOS);

        when(mockUserRoleMapper.selectList()).thenReturn(Collections.emptyList());

        // 配置 RoleMenuMapper.selectList(...).
        final RoleMenuDO roleMenuDO = new RoleMenuDO();
        roleMenuDO.setId(0L);
        roleMenuDO.setRoleId(0L);
        roleMenuDO.setMenuId(0L);
        final List<RoleMenuDO> roleMenuDOS = Arrays.asList(roleMenuDO);
        when(mockRoleMenuMapper.selectList()).thenReturn(roleMenuDOS);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // 运行测试
        permissionServiceImplUnderTest.assignUserRole(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 UserRoleBatchInsertMapper.saveBatch(...).
        final UserRoleDO userRoleDO1 = new UserRoleDO();
        userRoleDO1.setId(0L);
        userRoleDO1.setUserId(0L);
        userRoleDO1.setRoleId(0L);
        final List<UserRoleDO> entityList = Arrays.asList(userRoleDO1);
        verify(mockUserRoleBatchInsertMapper).saveBatch(entityList);
        verify(mockUserRoleMapper).deleteListByUserIdAndRoleIdIds(0L, Arrays.asList(0L));
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockPermissionProducer).sendUserRoleRefreshMessage();
    }

    @Test
    void testAssignUserRole_RoleMenuMapperReturnsNoItems() {
        // 初始化
        // 配置 UserRoleMapper.selectListByUserId(...).
        final UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setId(0L);
        userRoleDO.setUserId(0L);
        userRoleDO.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS = Arrays.asList(userRoleDO);
        when(mockUserRoleMapper.selectListByUserId(0L)).thenReturn(userRoleDOS);

        // 配置 UserRoleMapper.selectList(...).
        final UserRoleDO userRoleDO1 = new UserRoleDO();
        userRoleDO1.setId(0L);
        userRoleDO1.setUserId(0L);
        userRoleDO1.setRoleId(0L);
        final List<UserRoleDO> userRoleDOS1 = Arrays.asList(userRoleDO1);
        when(mockUserRoleMapper.selectList()).thenReturn(userRoleDOS1);

        when(mockRoleMenuMapper.selectList()).thenReturn(Collections.emptyList());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // 运行测试
        permissionServiceImplUnderTest.assignUserRole(0L, new HashSet<>(Arrays.asList(0L)));

        // 验证结果
        // 配置 UserRoleBatchInsertMapper.saveBatch(...).
        final UserRoleDO userRoleDO2 = new UserRoleDO();
        userRoleDO2.setId(0L);
        userRoleDO2.setUserId(0L);
        userRoleDO2.setRoleId(0L);
        final List<UserRoleDO> entityList = Arrays.asList(userRoleDO2);
        verify(mockUserRoleBatchInsertMapper).saveBatch(entityList);
        verify(mockUserRoleMapper).deleteListByUserIdAndRoleIdIds(0L, Arrays.asList(0L));
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockPermissionProducer).sendUserRoleRefreshMessage();
    }
}
