package com.unicom.swdx.module.system.service.message;

//import org.junit.runner.RunWith;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityCreateReqVO;
import org.junit.jupiter.api.Test;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.context.TestExecutionListeners;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

//@RunWith(MockitoJUnitRunner.class)
//@TestExecutionListeners
public class MessageAuthorityServiceImplTest {



    @Resource
    private MessageAuthorityService messageAuthorityService;



    @Test
    void createMessageAuthorityTest(){

        MessageAuthorityCreateReqVO createReqVO = new MessageAuthorityCreateReqVO();
        createReqVO.setName("志");
        createReqVO.setPhone("159****0000");
        createReqVO.setUserId(9522L);
        createReqVO.setDataScopeNames("");
        createReqVO.setDataScopeIds("");

        Long id = messageAuthorityService.createMessageAuthority(createReqVO);
        assertNotNull(id);

    }



    @Test
    void getMessageAuthorityPageTest(){

        MessageAuthorityCreateReqVO createReqVO = new MessageAuthorityCreateReqVO();
        createReqVO.setName("志");
        createReqVO.setPhone("159****0000");
        createReqVO.setUserId(9522L);
        createReqVO.setDataScopeNames("");
        createReqVO.setDataScopeIds("");

        Long id = messageAuthorityService.createMessageAuthority(createReqVO);
        assertNotNull(id);

    }



    @Test
    void getMessageAuthorityTest(){

        MessageAuthorityCreateReqVO createReqVO = new MessageAuthorityCreateReqVO();
        createReqVO.setName("志");
        createReqVO.setPhone("159****0000");
        createReqVO.setUserId(9522L);
        createReqVO.setDataScopeNames("");
        createReqVO.setDataScopeIds("");

        Long id = messageAuthorityService.createMessageAuthority(createReqVO);
        assertNotNull(id);
    }



    @Test
    void updateMessageAuthorityTest(){

        MessageAuthorityCreateReqVO createReqVO = new MessageAuthorityCreateReqVO();
        createReqVO.setName("志");
        createReqVO.setPhone("159****0000");
        createReqVO.setUserId(9522L);
        createReqVO.setDataScopeNames("");
        createReqVO.setDataScopeIds("");

        Long id = messageAuthorityService.createMessageAuthority(createReqVO);
        assertNotNull(id);

    }



    @Test
    void deleteMessageAuthorityTest(){

        MessageAuthorityCreateReqVO createReqVO = new MessageAuthorityCreateReqVO();
        createReqVO.setName("志");
        createReqVO.setPhone("159****0000");
        createReqVO.setUserId(9522L);
        createReqVO.setDataScopeNames("");
        createReqVO.setDataScopeIds("");

        Long id = messageAuthorityService.createMessageAuthority(createReqVO);
        assertNotNull(id);

    }



}
