package com.unicom.swdx.module.system.service.user;

import cn.hutool.core.bean.BeanUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserPageAuthorityVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserPageReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.test.UserTestReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.test.UserUpdateTestReqVO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static org.junit.jupiter.api.Assertions.*;

class AdminUserServiceImplTest {

    @Resource
    private AdminUserService userService;


    @Test
    void createUser() {

    }

    @Test
    void createApplyUser() {

    }

    @Test
    void createTraineeUser() {

    }

    @Test
    void updateUser() {

    }

    @Test
    void updateUserPassword() {

    }

    @Test
    void updateUserLogin() {

    }

    @Test
    void updateUserProfile() {

    }

    @Test
    void testUpdateUserPassword() {

    }

    @Test
    void updateUserById() {

    }


    @Test
    void updateUserMobile() {

    }

    @Test
    void updateAppUserMobile() {

    }

    @Test
    void updateUserAvatar() {

    }

    @Test
    void updateUserAvatarApp() {

    }

    @Test
    void updateUserStatus() {

    }

    @Test
    void updateUserAppCid() {

    }

    @Test
    void deleteUser() {

    }

    @Test
    void deleteAdminUser() {

    }

    @Test
    void getUserByUsername() {

    }

    @Test
    void getByUsername() {

    }

    @Test
    void getUserPage() {

    }

    @Test
    void getUserPageManageAuthority() {
        UserUpdateTestReqVO userUpdateTestReqVO =new UserUpdateTestReqVO();
        UserPageAuthorityVO userPageAuthorityVO = new UserPageAuthorityVO();
        userPageAuthorityVO.setPageNo(1);
        userPageAuthorityVO.setPageSize(99);
        //该字段为true是表示需要筛选授权范围
        userPageAuthorityVO.setIsAuthority(true);
        //该字段为false是表示不需要进行校领导等人员信息筛选
        userPageAuthorityVO.setIsScreen(false);
        PageResult<AdminUserDO> pageResult = new PageResult<>();
        UserPageReqVO userPageReqVO = new UserPageReqVO();
        BeanUtil.copyProperties(userPageReqVO,userPageReqVO);
        BeanUtil.copyProperties(userUpdateTestReqVO,userPageReqVO);
        pageResult = userService.getUserPageManageAuthority(userPageReqVO);
        getLoginUserId();
        assertNotNull(pageResult);

    }

    @Test
    void getUserPageManage() {

        UserPageReqVO userPageReqVO = new UserPageReqVO();
        userPageReqVO.setPageNo(1);
        userPageReqVO.setPageSize(99);
        PageResult<AdminUserDO> pageResult = new PageResult<>();
        pageResult = userService.getUserPageManage(userPageReqVO);
        getLoginUserId();
        assertNotNull(pageResult);

    }

    @Test
    void getUserExport() {

    }

    @Test
    void getUserContactPage() {

        UserPageReqVO userPageReqVO = new UserPageReqVO();
        UserTestReqVO userTestReqVO    = new UserTestReqVO();
        userPageReqVO.setPageNo(1);
        userPageReqVO.setPageSize(99);
        //该字段为true是表示需要筛选授权范围
        userPageReqVO.setIsAuthority(true);
        //该字段为false是表示不需要进行校领导等人员信息筛选
        userPageReqVO.setIsScreen(false);
        PageResult<AdminUserDO> pageResult = new PageResult<>();

        pageResult = userService.getUserPageManageAuthority(userPageReqVO);
        getLoginUserId();
        BeanUtil.copyProperties(userPageReqVO,userTestReqVO);
       // assertNotNull(pageResult);

    }

    @Test
    void getUser() {

    }


    @Test
    void getHistoryUser() {

    }

    @Test
    void getUsersByDeptIds() {

    }

    @Test
    void getUsersByPostIds() {

    }

    @Test
    void getUsers() {

    }

    @Test
    void validUsers() {

    }

    @Test
    void getUsersByNickname() {

    }

    @Test
    void checkUserExists() {

    }

    @Test
    void checkUsernameUnique() {

    }

    @Test
    void checkEmailUnique() {

    }

    @Test
    void checkOldPassword() {

    }

    @Test
    void checkOldMobile() {

    }

    @Test
    void importUsers() {

    }

    @Test
    void getUsersByStatus() {

    }

    @Test
    void getUsersByStatusAndDeptId() {

    }

    @Test
    void getUsersByStatusAndTenantId() {

    }

    @Test
    void getUsersByUserIds() {

    }

    @Test
    void getUsersDeptId() {

    }

    @Test
    void isPasswordMatch() {

    }

    @Test
    void isVerificationMatch() {

    }

    @Test
    void isRegisterVerificationMatch() {

    }

    @Test
    void testIsVerificationMatch() {

    }

    @Test
    void getUsersByDeptRoleIds() {

    }

    @Test
    void getUserIdResp() {

    }

    @Test
    void updateAuthUserPassword() {

    }


    @Test
    void selectIdListByTenantId() {

    }

    @Test
    void getListByTenantId() {

    }

    @Test
    void getUserTenantByNameLike() {

    }

    @Test
    void getPasswordUpdateTime() {

    }

    @Test
    void validPasswordExpired() {

    }

    @Test
    void verifyPassword() {

    }

    @Test
    void getUserByMobile() {

    }

    @Test
    void getByUnionid() {

    }

    @Test
    void getByOpenid() {

    }

    @Test
    void updateOpenidAndUnionid() {

    }

    @Test
    void getUsersByPostCode() {

    }

    @Test
    void getUsersByPostDeptCode() {

    }

    @Test
    void getUsersByDeptName() {

    }

    @Test
    void getUsersByTenantId() {

    }
}