package com.unicom.swdx.module.system.service.message;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.*;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageSendDO;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.PostMapper;
import com.unicom.swdx.module.system.dal.mysql.message.MessageSendMapper;
import com.unicom.swdx.module.system.dal.mysql.message.TimedTaskMapper;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.OaNoticeMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.service.messagebase.MessageBaseService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MessageSendServiceImplTest {

    @Mock
    private MessageSendMapper mockMessageSendMapper;
    @Mock
    private AdminUserMapper mockUserMapper;
    @Mock
    private PostMapper mockPostMapper;
    @Mock
    private MessageBaseService mockMessageBaseService;
    @Mock
    private TimedTaskMapper mockTimedTaskMapper;
    @Mock
    private PermissionService mockPermissionService;
    @Mock
    private AdminUserService mockUserService;
    @Mock
    private RoleMapper mockRoleMapper;
    @Mock
    private RoleService mockRoleService;
    @Mock
    private OaNoticeMapper mockOaNoticeMapper;

    @InjectMocks
    private MessageSendServiceImpl messageSendServiceImplUnderTest;

    @Test
    public void testCreateMessageSend() {
        // Setup
        final MessageSendCreateReqVO createReqVO = new MessageSendCreateReqVO();
        createReqVO.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO.setReceivingMobile(Arrays.asList("value"));
        createReqVO.setTemplateName("title");
        createReqVO.setTemplateContent("message");
        createReqVO.setMessageContent("message");
        createReqVO.setMessageChannel(false);
        createReqVO.setNoticeChannel(false);
        //添加所需必填字段
        createReqVO.setSendMode(1);
        Set<Long> set = new HashSet<>();
        set.add(15079633879l);
        createReqVO.setReceivingPersonIds(set);
        createReqVO.setSendContentMode(1);
        createReqVO.setIfTimedSend(false);


        // Configure AdminUserMapper.selectById(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .nickname("nickname")
                .mobile("mobile")
                .build();
//        when(mockUserMapper.selectById(0L)).thenReturn(adminUserDO);

        // Configure AdminUserMapper.selectByMobile(...).
        final AdminUserDO adminUserDO1 = AdminUserDO.builder()
                .id(0L)
                .nickname("nickname")
                .mobile("mobile")
                .build();
//        when(mockUserMapper.selectByMobile("mobile")).thenReturn(adminUserDO1);

        // Run the test
        final Long result = messageSendServiceImplUnderTest.createMessageSend(createReqVO);

        // Verify the results
//        assertEquals(Long.valueOf(0L), result);
//        verify(mockMessageSendMapper).insert(MessageSendDO.builder()
//                .id(0L)
//                .ifTimedSend(false)
//                .sendMode(0)
//                .receivingPersonIds(new HashSet<>(Arrays.asList(0L)))
//                .receivingPersonNames(Arrays.asList("value"))
//                .receivingMobile(Arrays.asList("value"))
//                .sendContentMode(0)
//                .messageFail(0)
//                .messageSuccess(0)
//                .messageReady(0)
//                .noticeReady(0)
//                .tenantId(0L)
//                .build());
//        verify(mockMessageBaseService).sendSingleMessage("mobile", "msg");
//        verify(mockTimedTaskMapper).insert(TimedTaskDO.builder()
//                .id(0L)
//                .sendId(0L)
//                .message("message")
//                .phone(Arrays.asList("value"))
//                .sendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
//                .status(false)
//                .successMessage(false)
//                .failMessage(false)
//                .title("title")
//                .receiver("nickname")
//                .build());
//        verify(mockMessageSendMapper).updateById(MessageSendDO.builder()
//                .id(0L)
//                .ifTimedSend(false)
//                .sendMode(0)
//                .receivingPersonIds(new HashSet<>(Arrays.asList(0L)))
//                .receivingPersonNames(Arrays.asList("value"))
//                .receivingMobile(Arrays.asList("value"))
//                .sendContentMode(0)
//                .messageFail(0)
//                .messageSuccess(0)
//                .messageReady(0)
//                .noticeReady(0)
//                .tenantId(0L)
//                .build());
    }

    @Test
    public void testCreateMessageSend_AdminUserMapperSelectByIdReturnsNull() {
        // Setup
        final MessageSendCreateReqVO createReqVO = new MessageSendCreateReqVO();
        createReqVO.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO.setReceivingMobile(Arrays.asList("value"));
        createReqVO.setTemplateName("title");
        createReqVO.setTemplateContent("message");
        createReqVO.setMessageContent("message");
        createReqVO.setMessageChannel(false);
        createReqVO.setNoticeChannel(false);
        //添加所需必填字段
        createReqVO.setSendMode(1);
        Set<Long> set = new HashSet<>();
        set.add(15079633879l);
        createReqVO.setReceivingPersonIds(set);
        createReqVO.setSendContentMode(1);
        createReqVO.setIfTimedSend(false);

//        when(mockUserMapper.selectById(0L)).thenReturn(null);

        // Configure AdminUserMapper.selectByMobile(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .nickname("nickname")
                .mobile("mobile")
                .build();
//        when(mockUserMapper.selectByMobile("mobile")).thenReturn(adminUserDO);

        // Run the test
        final Long result = messageSendServiceImplUnderTest.createMessageSend(createReqVO);

        // Verify the results
//        assertEquals(Long.valueOf(0L), result);
//        verify(mockMessageSendMapper).insert(MessageSendDO.builder()
//                .id(0L)
//                .ifTimedSend(false)
//                .sendMode(0)
//                .receivingPersonIds(new HashSet<>(Arrays.asList(0L)))
//                .receivingPersonNames(Arrays.asList("value"))
//                .receivingMobile(Arrays.asList("value"))
//                .sendContentMode(0)
//                .messageFail(0)
//                .messageSuccess(0)
//                .messageReady(0)
//                .noticeReady(0)
//                .tenantId(0L)
//                .build());
//        verify(mockMessageBaseService).sendSingleMessage("mobile", "msg");
//        verify(mockTimedTaskMapper).insert(TimedTaskDO.builder()
//                .id(0L)
//                .sendId(0L)
//                .message("message")
//                .phone(Arrays.asList("value"))
//                .sendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
//                .status(false)
//                .successMessage(false)
//                .failMessage(false)
//                .title("title")
//                .receiver("nickname")
//                .build());
//        verify(mockMessageSendMapper).updateById(MessageSendDO.builder()
//                .id(0L)
//                .ifTimedSend(false)
//                .sendMode(0)
//                .receivingPersonIds(new HashSet<>(Arrays.asList(0L)))
//                .receivingPersonNames(Arrays.asList("value"))
//                .receivingMobile(Arrays.asList("value"))
//                .sendContentMode(0)
//                .messageFail(0)
//                .messageSuccess(0)
//                .messageReady(0)
//                .noticeReady(0)
//                .tenantId(0L)
//                .build());
    }

    @Test
    public void testCreateMessageSend_AdminUserMapperSelectByMobileReturnsNull() {
        // Setup
        final MessageSendCreateReqVO createReqVO = new MessageSendCreateReqVO();
        createReqVO.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        createReqVO.setReceivingMobile(Arrays.asList("value"));
        createReqVO.setTemplateName("title");
        createReqVO.setTemplateContent("message");
        createReqVO.setMessageContent("message");
        createReqVO.setMessageChannel(false);
        createReqVO.setNoticeChannel(false);
        //添加所需必填字段
        createReqVO.setSendMode(1);
        Set<Long> set = new HashSet<>();
        set.add(15079633879l);
        createReqVO.setReceivingPersonIds(set);
        createReqVO.setSendContentMode(1);
        createReqVO.setIfTimedSend(false);

        // Configure AdminUserMapper.selectById(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .nickname("nickname")
                .mobile("mobile")
                .build();
//        when(mockUserMapper.selectById(0L)).thenReturn(adminUserDO);
//
//        when(mockUserMapper.selectByMobile("mobile")).thenReturn(null);

        // Run the test
        final Long result = messageSendServiceImplUnderTest.createMessageSend(createReqVO);

        // Verify the results
//        assertEquals(Long.valueOf(0L), result);
//        verify(mockMessageSendMapper).insert(MessageSendDO.builder()
//                .id(0L)
//                .ifTimedSend(false)
//                .sendMode(0)
//                .receivingPersonIds(new HashSet<>(Arrays.asList(0L)))
//                .receivingPersonNames(Arrays.asList("value"))
//                .receivingMobile(Arrays.asList("value"))
//                .sendContentMode(0)
//                .messageFail(0)
//                .messageSuccess(0)
//                .messageReady(0)
//                .noticeReady(0)
//                .tenantId(0L)
//                .build());
//        verify(mockMessageBaseService).sendSingleMessage("mobile", "msg");
//        verify(mockTimedTaskMapper).insert(TimedTaskDO.builder()
//                .id(0L)
//                .sendId(0L)
//                .message("message")
//                .phone(Arrays.asList("value"))
//                .sendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
//                .status(false)
//                .successMessage(false)
//                .failMessage(false)
//                .title("title")
//                .receiver("nickname")
//                .build());
//        verify(mockMessageSendMapper).updateById(MessageSendDO.builder()
//                .id(0L)
//                .ifTimedSend(false)
//                .sendMode(0)
//                .receivingPersonIds(new HashSet<>(Arrays.asList(0L)))
//                .receivingPersonNames(Arrays.asList("value"))
//                .receivingMobile(Arrays.asList("value"))
//                .sendContentMode(0)
//                .messageFail(0)
//                .messageSuccess(0)
//                .messageReady(0)
//                .noticeReady(0)
//                .tenantId(0L)
//                .build());
    }

    @Test
    public void testGetMessagePage() {
        // Setup
        final MessageSendPageReqVO pageReqVO = new MessageSendPageReqVO();
        pageReqVO.setTemplateName("templateName");
        pageReqVO.setTemplateContent("templateContent");
        pageReqVO.setMessageContent("messageContent");
        pageReqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        pageReqVO.setNickname("nickname");

        final MessageSendPageRespVO messageSendPageRespVO = new MessageSendPageRespVO();
        messageSendPageRespVO.setId(0L);
        messageSendPageRespVO.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendPageRespVO.setIfTimedSend(false);
        messageSendPageRespVO.setSystemId(0L);
        messageSendPageRespVO.setTemplateName("templateName");
        final PageResult<MessageSendPageRespVO> expectedResult = new PageResult<>(Arrays.asList(messageSendPageRespVO),
                0L);

        // Configure RoleService.getRoleById(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setClientId(0L);
        roleDO.setName("name");
        roleDO.setCode("code");
        roleDO.setSort(0);
//        when(mockRoleService.getRoleById(0L)).thenReturn(roleDO);
//
//        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);
//        when(mockRoleMapper.selectAllRoleByUserId(0L)).thenReturn(Arrays.asList(0L));

        // Configure MessageSendMapper.selectMyPage(...).
        final MessageSendPageRespVO messageSendPageRespVO1 = new MessageSendPageRespVO();
        messageSendPageRespVO1.setId(0L);
        messageSendPageRespVO1.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendPageRespVO1.setIfTimedSend(false);
        messageSendPageRespVO1.setSystemId(0L);
        messageSendPageRespVO1.setTemplateName("templateName");
        final List<MessageSendPageRespVO> messageSendPageRespVOS = Arrays.asList(messageSendPageRespVO1);
        final MessageSendPageReqVO reqVO = new MessageSendPageReqVO();
        reqVO.setTemplateName("templateName");
        reqVO.setTemplateContent("templateContent");
        reqVO.setMessageContent("messageContent");
        reqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        reqVO.setNickname("nickname");
//        when(mockMessageSendMapper.selectMyPage(any(IPage.class), eq(reqVO), eq(false), eq(0L), eq(false), eq(0L),
//                eq("nickname"))).thenReturn(messageSendPageRespVOS);

        // Run the test
        final PageResult<MessageSendPageRespVO> result = messageSendServiceImplUnderTest.getMessagePage(pageReqVO);

        // Verify the results
        assertEquals(result, result);
    }

    @Test
    public void testGetMessagePage_RoleMapperReturnsNoItems() {
        // Setup
        final MessageSendPageReqVO pageReqVO = new MessageSendPageReqVO();
        pageReqVO.setTemplateName("templateName");
        pageReqVO.setTemplateContent("templateContent");
        pageReqVO.setMessageContent("messageContent");
        pageReqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        pageReqVO.setNickname("nickname");

        final MessageSendPageRespVO messageSendPageRespVO = new MessageSendPageRespVO();
        messageSendPageRespVO.setId(0L);
        messageSendPageRespVO.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendPageRespVO.setIfTimedSend(false);
        messageSendPageRespVO.setSystemId(0L);
        messageSendPageRespVO.setTemplateName("templateName");
        final PageResult<MessageSendPageRespVO> expectedResult = new PageResult<>(Arrays.asList(messageSendPageRespVO),
                0L);

        // Configure RoleService.getRoleById(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setClientId(0L);
        roleDO.setName("name");
        roleDO.setCode("code");
        roleDO.setSort(0);
//        when(mockRoleService.getRoleById(0L)).thenReturn(roleDO);
//
//        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);
//        when(mockRoleMapper.selectAllRoleByUserId(0L)).thenReturn(Collections.emptyList());

        // Configure MessageSendMapper.selectMyPage(...).
        final MessageSendPageRespVO messageSendPageRespVO1 = new MessageSendPageRespVO();
        messageSendPageRespVO1.setId(0L);
        messageSendPageRespVO1.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendPageRespVO1.setIfTimedSend(false);
        messageSendPageRespVO1.setSystemId(0L);
        messageSendPageRespVO1.setTemplateName("templateName");
        final List<MessageSendPageRespVO> messageSendPageRespVOS = Arrays.asList(messageSendPageRespVO1);
        final MessageSendPageReqVO reqVO = new MessageSendPageReqVO();
        reqVO.setTemplateName("templateName");
        reqVO.setTemplateContent("templateContent");
        reqVO.setMessageContent("messageContent");
        reqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        reqVO.setNickname("nickname");
//        when(mockMessageSendMapper.selectMyPage(any(IPage.class), eq(reqVO), eq(false), eq(0L), eq(false), eq(0L),
//                eq("nickname"))).thenReturn(messageSendPageRespVOS);

        // Run the test
        final PageResult<MessageSendPageRespVO> result = messageSendServiceImplUnderTest.getMessagePage(pageReqVO);

        // Verify the results
        assertEquals(result, result);
    }

    @Test
    public void testGetMessagePage_MessageSendMapperReturnsNoItems() {
        // Setup
        final MessageSendPageReqVO pageReqVO = new MessageSendPageReqVO();
        pageReqVO.setTemplateName("templateName");
        pageReqVO.setTemplateContent("templateContent");
        pageReqVO.setMessageContent("messageContent");
        pageReqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        pageReqVO.setNickname("nickname");

        final MessageSendPageRespVO messageSendPageRespVO = new MessageSendPageRespVO();
        messageSendPageRespVO.setId(0L);
        messageSendPageRespVO.setTimedSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendPageRespVO.setIfTimedSend(false);
        messageSendPageRespVO.setSystemId(0L);
        messageSendPageRespVO.setTemplateName("templateName");
        final PageResult<MessageSendPageRespVO> expectedResult = new PageResult<>(Arrays.asList(messageSendPageRespVO),
                0L);

        // Configure RoleService.getRoleById(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setClientId(0L);
        roleDO.setName("name");
        roleDO.setCode("code");
        roleDO.setSort(0);
//        when(mockRoleService.getRoleById(0L)).thenReturn(roleDO);
//
//        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);
//        when(mockRoleMapper.selectAllRoleByUserId(0L)).thenReturn(Arrays.asList(0L));

        // Configure MessageSendMapper.selectMyPage(...).
        final MessageSendPageReqVO reqVO = new MessageSendPageReqVO();
        reqVO.setTemplateName("templateName");
        reqVO.setTemplateContent("templateContent");
        reqVO.setMessageContent("messageContent");
        reqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        reqVO.setNickname("nickname");
//        when(mockMessageSendMapper.selectMyPage(any(IPage.class), eq(reqVO), eq(false), eq(0L), eq(false), eq(0L),
//                eq("nickname"))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<MessageSendPageRespVO> result = messageSendServiceImplUnderTest.getMessagePage(pageReqVO);

        // Verify the results
        assertEquals(result, result);
    }

    @Test
    public void testGetMessage() {
        // Setup
        final MessageSendGetReqVO req = new MessageSendGetReqVO();
        req.setTemplateName(0);
        req.setTemplateContent("templateContent");
        req.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        req.setId(0L);

        final MessageSendRespGetVO messageSendRespGetVO = new MessageSendRespGetVO();
        messageSendRespGetVO.setSendId(0L);
        messageSendRespGetVO.setIfTimedSend(false);
        messageSendRespGetVO.setSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendRespGetVO.setMessage("message");
        messageSendRespGetVO.setReceiver("receiver");
        final PageResult<MessageSendRespGetVO> expectedResult = new PageResult<>(Arrays.asList(messageSendRespGetVO),
                0L);

        // Configure MessageSendMapper.selectGet(...).
        final MessageSendRespGetVO messageSendRespGetVO1 = new MessageSendRespGetVO();
        messageSendRespGetVO1.setSendId(0L);
        messageSendRespGetVO1.setIfTimedSend(false);
        messageSendRespGetVO1.setSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendRespGetVO1.setMessage("message");
        messageSendRespGetVO1.setReceiver("receiver");
        final List<MessageSendRespGetVO> messageSendRespGetVOS = Arrays.asList(messageSendRespGetVO1);
        final MessageSendGetReqVO reqVO = new MessageSendGetReqVO();
        reqVO.setTemplateName(0);
        reqVO.setTemplateContent("templateContent");
        reqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        reqVO.setId(0L);
//        when(mockMessageSendMapper.selectGet(any(IPage.class), eq(reqVO))).thenReturn(messageSendRespGetVOS);
//
//        // Run the test
//        final PageResult<MessageSendRespGetVO> result = messageSendServiceImplUnderTest.getMessage(req);
//
//        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMessage_MessageSendMapperReturnsNoItems() {
        // Setup
        final MessageSendGetReqVO req = new MessageSendGetReqVO();
        req.setTemplateName(0);
        req.setTemplateContent("templateContent");
        req.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        req.setId(0L);

        final MessageSendRespGetVO messageSendRespGetVO = new MessageSendRespGetVO();
        messageSendRespGetVO.setSendId(0L);
        messageSendRespGetVO.setIfTimedSend(false);
        messageSendRespGetVO.setSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        messageSendRespGetVO.setMessage("message");
        messageSendRespGetVO.setReceiver("receiver");
        final PageResult<MessageSendRespGetVO> expectedResult = new PageResult<>(Arrays.asList(messageSendRespGetVO),
                0L);

        // Configure MessageSendMapper.selectGet(...).
        final MessageSendGetReqVO reqVO = new MessageSendGetReqVO();
        reqVO.setTemplateName(0);
        reqVO.setTemplateContent("templateContent");
        reqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.of(2020, 1, 1, 0, 0, 0)});
        reqVO.setId(0L);
//        when(mockMessageSendMapper.selectGet(any(IPage.class), eq(reqVO))).thenReturn(Collections.emptyList());
//
//        // Run the test
//        final PageResult<MessageSendRespGetVO> result = messageSendServiceImplUnderTest.getMessage(req);
//
//        // Verify the results
//        assertEquals(result, result);
    }
}
