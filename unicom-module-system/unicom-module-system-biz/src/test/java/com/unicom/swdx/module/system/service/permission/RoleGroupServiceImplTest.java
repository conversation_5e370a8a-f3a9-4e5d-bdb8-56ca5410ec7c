package com.unicom.swdx.module.system.service.permission;

import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleClientSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupBindDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleGroupBindMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleGroupMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.UserRoleGroupMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantMapper;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RoleGroupServiceImplTest {

    @Mock
    private RoleGroupMapper mockRoleGroupMapper;
    @Mock
    private RoleGroupBindMapper mockRoleGroupBindMapper;
    @Mock
    private UserRoleGroupMapper mockUserRoleGroupMapper;
    @Mock
    private PermissionService mockPermissionService;
    @Mock
    private TenantMapper mockTenantMapper;
    @Mock
    private TenantService mockTenantService;
    @Mock
    private RoleService mockRoleService;
    @Mock
    private AdminUserService mockUserService;

    @InjectMocks
    private RoleGroupServiceImpl roleGroupServiceImplUnderTest;

    @Test
    void testCreateRoleGroup() {
        // Setup
        final RoleGroupCreateReqVO reqVO = new RoleGroupCreateReqVO();
        reqVO.setName("name");
        reqVO.setStatus(0);
        reqVO.setRoleIds(new HashSet<>(Arrays.asList(0L)));

        // Configure RoleGroupMapper.selectByNameAndApplication(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectByNameAndApplication("name", 0L)).thenReturn(roleGroupDO);

        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Run the test
        final Long result = roleGroupServiceImplUnderTest.createRoleGroup(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);

        // Confirm RoleGroupMapper.insert(...).
        final RoleGroupDO entity = new RoleGroupDO();
        entity.setTenantId(0L);
        entity.setId(0L);
        entity.setName("name");
        entity.setSort(0);
        entity.setStatus(0);
        verify(mockRoleGroupMapper).insert(entity);

        // Confirm RoleGroupBindMapper.insertBatch(...).
        final RoleGroupBindDO roleGroupBindDO = new RoleGroupBindDO();
        roleGroupBindDO.setTenantId(0L);
        roleGroupBindDO.setId(0L);
        roleGroupBindDO.setRoleId(0L);
        roleGroupBindDO.setGroupId(0L);
        final Set<RoleGroupBindDO> entities = new HashSet<>(Arrays.asList(roleGroupBindDO));
        verify(mockRoleGroupBindMapper).insertBatch(entities);
    }

    @Test
    void testUpdateRoleGroup() {
        // Setup
        final RoleGroupUpdateReqVO reqVO = new RoleGroupUpdateReqVO();
        reqVO.setName("name");
        reqVO.setId(0L);
        reqVO.setStatus(0);
        reqVO.setRoleIds(new HashSet<>(Arrays.asList(0L)));

        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        // Configure RoleGroupMapper.selectByNameAndApplication(...).
        final RoleGroupDO roleGroupDO1 = new RoleGroupDO();
        roleGroupDO1.setTenantId(0L);
        roleGroupDO1.setId(0L);
        roleGroupDO1.setName("name");
        roleGroupDO1.setSort(0);
        roleGroupDO1.setStatus(0);
        when(mockRoleGroupMapper.selectByNameAndApplication("name", 0L)).thenReturn(roleGroupDO1);

        // Run the test
        roleGroupServiceImplUnderTest.updateRoleGroup(reqVO);

        // Verify the results
        // Confirm RoleGroupMapper.updateById(...).
        final RoleGroupDO entity = new RoleGroupDO();
        entity.setTenantId(0L);
        entity.setId(0L);
        entity.setName("name");
        entity.setSort(0);
        entity.setStatus(0);
        verify(mockRoleGroupMapper).updateById(entity);
        verify(mockRoleGroupBindMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm RoleGroupBindMapper.insertBatch(...).
        final RoleGroupBindDO roleGroupBindDO = new RoleGroupBindDO();
        roleGroupBindDO.setTenantId(0L);
        roleGroupBindDO.setId(0L);
        roleGroupBindDO.setRoleId(0L);
        roleGroupBindDO.setGroupId(0L);
        final Set<RoleGroupBindDO> entities = new HashSet<>(Arrays.asList(roleGroupBindDO));
        verify(mockRoleGroupBindMapper).insertBatch(entities);
    }

    @Test
    void testUpdateRoleGroup_RoleGroupMapperSelectByIdReturnsNull() {
        // Setup
        final RoleGroupUpdateReqVO reqVO = new RoleGroupUpdateReqVO();
        reqVO.setName("name");
        reqVO.setId(0L);
        reqVO.setStatus(0);
        reqVO.setRoleIds(new HashSet<>(Arrays.asList(0L)));

        when(mockRoleGroupMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> roleGroupServiceImplUnderTest.updateRoleGroup(reqVO))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testDeleteRoleGroup() {
        // Setup
        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        when(mockUserRoleGroupMapper.getUsersByRoleGroupIds(0L)).thenReturn(0L);

        // Run the test
        roleGroupServiceImplUnderTest.deleteRoleGroup(0L);

        // Verify the results
        verify(mockRoleGroupMapper).deleteById(0L);
        verify(mockRoleGroupBindMapper).delete(any(LambdaQueryWrapperX.class));
    }

    @Test
    void testDeleteRoleGroup_RoleGroupMapperSelectByIdReturnsNull() {
        // Setup
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> roleGroupServiceImplUnderTest.deleteRoleGroup(0L))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testGetRoleGroupPage() {
        // Setup
        final RoleGroupPageReqVO reqVO = new RoleGroupPageReqVO();
        reqVO.setName("name");
        reqVO.setTenantName("tenantName");
        reqVO.setStatus(0);

        final RoleGroupRespVO roleGroupRespVO = new RoleGroupRespVO();
        roleGroupRespVO.setName("name");
        roleGroupRespVO.setId(0L);
        roleGroupRespVO.setStatus(0);
        roleGroupRespVO.setRoleIds(new HashSet<>(Arrays.asList(0L)));
        roleGroupRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final PageResult<RoleGroupRespVO> expectedResult = new PageResult<>(Arrays.asList(roleGroupRespVO), 0L);
        when(mockTenantMapper.selectIdListByNameLike("tenantName")).thenReturn(Arrays.asList(0L));
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure RoleGroupMapper.selectRoleGroupPage(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        final PageResult<RoleGroupDO> roleGroupDOPageResult = new PageResult<>(Arrays.asList(roleGroupDO), 0L);
        final RoleGroupPageReqVO reqVO1 = new RoleGroupPageReqVO();
        reqVO1.setName("name");
        reqVO1.setTenantName("tenantName");
        reqVO1.setStatus(0);
        when(mockRoleGroupMapper.selectRoleGroupPage(reqVO1, Arrays.asList(0L), 0L)).thenReturn(roleGroupDOPageResult);

        // Run the test
        final PageResult<RoleGroupRespVO> result = roleGroupServiceImplUnderTest.getRoleGroupPage(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroupPage_TenantMapperReturnsNoItems() {
        // Setup
        final RoleGroupPageReqVO reqVO = new RoleGroupPageReqVO();
        reqVO.setName("name");
        reqVO.setTenantName("tenantName");
        reqVO.setStatus(0);

        final RoleGroupRespVO roleGroupRespVO = new RoleGroupRespVO();
        roleGroupRespVO.setName("name");
        roleGroupRespVO.setId(0L);
        roleGroupRespVO.setStatus(0);
        roleGroupRespVO.setRoleIds(new HashSet<>(Arrays.asList(0L)));
        roleGroupRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final PageResult<RoleGroupRespVO> expectedResult = new PageResult<>(Arrays.asList(roleGroupRespVO), 0L);
        when(mockTenantMapper.selectIdListByNameLike("tenantName")).thenReturn(Collections.emptyList());
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure RoleGroupMapper.selectRoleGroupPage(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        final PageResult<RoleGroupDO> roleGroupDOPageResult = new PageResult<>(Arrays.asList(roleGroupDO), 0L);
        final RoleGroupPageReqVO reqVO1 = new RoleGroupPageReqVO();
        reqVO1.setName("name");
        reqVO1.setTenantName("tenantName");
        reqVO1.setStatus(0);
        when(mockRoleGroupMapper.selectRoleGroupPage(reqVO1, Arrays.asList(0L), 0L)).thenReturn(roleGroupDOPageResult);

        // Run the test
        final PageResult<RoleGroupRespVO> result = roleGroupServiceImplUnderTest.getRoleGroupPage(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroupPage_RoleGroupMapperReturnsNoItem() {
        // Setup
        final RoleGroupPageReqVO reqVO = new RoleGroupPageReqVO();
        reqVO.setName("name");
        reqVO.setTenantName("tenantName");
        reqVO.setStatus(0);

        final RoleGroupRespVO roleGroupRespVO = new RoleGroupRespVO();
        roleGroupRespVO.setName("name");
        roleGroupRespVO.setId(0L);
        roleGroupRespVO.setStatus(0);
        roleGroupRespVO.setRoleIds(new HashSet<>(Arrays.asList(0L)));
        roleGroupRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final PageResult<RoleGroupRespVO> expectedResult = new PageResult<>(Arrays.asList(roleGroupRespVO), 0L);
        when(mockTenantMapper.selectIdListByNameLike("tenantName")).thenReturn(Arrays.asList(0L));
        when(mockPermissionService.isSuperAdmin(0L)).thenReturn(false);

        // Configure RoleGroupMapper.selectRoleGroupPage(...).
        final RoleGroupPageReqVO reqVO1 = new RoleGroupPageReqVO();
        reqVO1.setName("name");
        reqVO1.setTenantName("tenantName");
        reqVO1.setStatus(0);
        when(mockRoleGroupMapper.selectRoleGroupPage(reqVO1, Arrays.asList(0L), 0L)).thenReturn(PageResult.empty());

        // Run the test
        final PageResult<RoleGroupRespVO> result = roleGroupServiceImplUnderTest.getRoleGroupPage(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroup() {
        // Setup
        final RoleGroupRespVO expectedResult = new RoleGroupRespVO(0L, 0, new HashSet<>(Arrays.asList(0L)),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        when(mockRoleGroupBindMapper.selectRoleId(0L)).thenReturn(new HashSet<>(Arrays.asList(0L)));

        // Run the test
        final RoleGroupRespVO result = roleGroupServiceImplUnderTest.getRoleGroup(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroup_RoleGroupBindMapperReturnsNoItems() {
        // Setup
        final RoleGroupRespVO expectedResult = new RoleGroupRespVO(0L, 0, new HashSet<>(Arrays.asList(0L)),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        when(mockRoleGroupBindMapper.selectRoleId(0L)).thenReturn(Collections.emptySet());

        // Run the test
        final RoleGroupRespVO result = roleGroupServiceImplUnderTest.getRoleGroup(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroupUsersPage() {
        // Setup
        final RoleGroupUsersPageReqVO pageReqVO = new RoleGroupUsersPageReqVO();
        pageReqVO.setGroupId(0L);

        final RoleGroupUsersRespVO expectedResult = new RoleGroupUsersRespVO();
        expectedResult.setName("name");
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setUsername("username");
        expectedResult.setUsers(new PageResult<>(Arrays.asList(userSimpleRespVO), 0L));

        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        // Configure AdminUserService.getUsersByGroupIds(...).
        final UserSimpleRespVO userSimpleRespVO1 = new UserSimpleRespVO();
        userSimpleRespVO1.setId(0L);
        userSimpleRespVO1.setUid("uid");
        userSimpleRespVO1.setUsername("username");
        userSimpleRespVO1.setNickname("nickname");
        userSimpleRespVO1.setTenantId(0L);
        final PageResult<UserSimpleRespVO> userSimpleRespVOPageResult = new PageResult<>(
                Arrays.asList(userSimpleRespVO1), 0L);
        final RoleGroupUsersPageReqVO pageReqVO1 = new RoleGroupUsersPageReqVO();
        pageReqVO1.setGroupId(0L);
        when(mockUserService.getUsersByGroupIds(pageReqVO1)).thenReturn(userSimpleRespVOPageResult);

        // Run the test
        final RoleGroupUsersRespVO result = roleGroupServiceImplUnderTest.getRoleGroupUsersPage(pageReqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroupUsersPage_AdminUserServiceReturnsNoItem() {
        // Setup
        final RoleGroupUsersPageReqVO pageReqVO = new RoleGroupUsersPageReqVO();
        pageReqVO.setGroupId(0L);

        final RoleGroupUsersRespVO expectedResult = new RoleGroupUsersRespVO();
        expectedResult.setName("name");
        final UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();
        userSimpleRespVO.setId(0L);
        userSimpleRespVO.setUid("uid");
        userSimpleRespVO.setUsername("username");
        expectedResult.setUsers(new PageResult<>(Arrays.asList(userSimpleRespVO), 0L));

        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        // Configure AdminUserService.getUsersByGroupIds(...).
        final RoleGroupUsersPageReqVO pageReqVO1 = new RoleGroupUsersPageReqVO();
        pageReqVO1.setGroupId(0L);
        when(mockUserService.getUsersByGroupIds(pageReqVO1)).thenReturn(PageResult.empty());

        // Run the test
        final RoleGroupUsersRespVO result = roleGroupServiceImplUnderTest.getRoleGroupUsersPage(pageReqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetRoleGroupUsers() {
        // Setup
        when(mockUserService.getUserIdsByGroupId(0L)).thenReturn(Arrays.asList(0L));

        // Run the test
        final List<Long> result = roleGroupServiceImplUnderTest.getRoleGroupUsers(0L);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList(0L));
    }

    @Test
    void testGetRoleGroupUsers_AdminUserServiceReturnsNoItems() {
        // Setup
        when(mockUserService.getUserIdsByGroupId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = roleGroupServiceImplUnderTest.getRoleGroupUsers(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateRoleGroupStatus() {
        // Setup
        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        when(mockUserRoleGroupMapper.getUsersByRoleGroupIds(0L)).thenReturn(0L);

        // Run the test
        roleGroupServiceImplUnderTest.updateRoleGroupStatus(0L, 0);

        // Verify the results
        // Confirm RoleGroupMapper.updateById(...).
        final RoleGroupDO entity = new RoleGroupDO();
        entity.setTenantId(0L);
        entity.setId(0L);
        entity.setName("name");
        entity.setSort(0);
        entity.setStatus(0);
        verify(mockRoleGroupMapper).updateById(entity);
    }

    @Test
    void testUpdateRoleGroupStatus_RoleGroupMapperSelectByIdReturnsNull() {
        // Setup
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> roleGroupServiceImplUnderTest.updateRoleGroupStatus(0L, 0))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testCheckDuplicateRoleGroup() {
        // Setup
        final RoleGroupBaseVO roleGroupVO = new RoleGroupBaseVO();
        roleGroupVO.setTenantId(0L);
        roleGroupVO.setName("name");
        roleGroupVO.setSort(0);
        roleGroupVO.setRemark("remark");

        // Configure RoleGroupMapper.selectByNameAndApplication(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectByNameAndApplication("name", 0L)).thenReturn(roleGroupDO);

        // Run the test
        roleGroupServiceImplUnderTest.checkDuplicateRoleGroup(roleGroupVO, 0L);

        // Verify the results
    }

    @Test
    void testCheckUpdateRoleGroup() {
        // Setup
        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        // Run the test
        roleGroupServiceImplUnderTest.checkUpdateRoleGroup(0L);

        // Verify the results
    }

    @Test
    void testCheckUpdateRoleGroup_RoleGroupMapperReturnsNull() {
        // Setup
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> roleGroupServiceImplUnderTest.checkUpdateRoleGroup(0L))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testCheckDeleteRoleGroup() {
        // Setup
        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        when(mockUserRoleGroupMapper.getUsersByRoleGroupIds(0L)).thenReturn(0L);

        // Run the test
        roleGroupServiceImplUnderTest.checkDeleteRoleGroup(0L);

        // Verify the results
    }

    @Test
    void testCheckDeleteRoleGroup_RoleGroupMapperReturnsNull() {
        // Setup
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> roleGroupServiceImplUnderTest.checkDeleteRoleGroup(0L))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testGetSimpleRoleList() {
        // Setup
        final List<RoleClientSimpleRespVO> expectedResult = Arrays.asList(RoleClientSimpleRespVO.builder().build());

        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO = TenantDO.builder()
                .contactUserId(0L)
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO);

        // Configure RoleService.getSimpleRoleList(...).
        final List<RoleClientSimpleRespVO> roleClientSimpleRespVOS = Arrays.asList(
                RoleClientSimpleRespVO.builder().build());
        when(mockRoleService.getSimpleRoleList(0L)).thenReturn(roleClientSimpleRespVOS);

        // Run the test
        final List<RoleClientSimpleRespVO> result = roleGroupServiceImplUnderTest.getSimpleRoleList(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetSimpleRoleList_RoleServiceReturnsNoItems() {
        // Setup
        // Configure RoleGroupMapper.selectById(...).
        final RoleGroupDO roleGroupDO = new RoleGroupDO();
        roleGroupDO.setTenantId(0L);
        roleGroupDO.setId(0L);
        roleGroupDO.setName("name");
        roleGroupDO.setSort(0);
        roleGroupDO.setStatus(0);
        when(mockRoleGroupMapper.selectById(0L)).thenReturn(roleGroupDO);

        // Configure TenantService.getTenantFromCache(...).
        final TenantDO tenantDO = TenantDO.builder()
                .contactUserId(0L)
                .build();
        when(mockTenantService.getTenantFromCache(0L)).thenReturn(tenantDO);

        when(mockRoleService.getSimpleRoleList(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<RoleClientSimpleRespVO> result = roleGroupServiceImplUnderTest.getSimpleRoleList(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
