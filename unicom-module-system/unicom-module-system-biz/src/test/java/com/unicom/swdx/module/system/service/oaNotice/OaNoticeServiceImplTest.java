package com.unicom.swdx.module.system.service.oaNotice;

import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.NoticeReadRecordMapper;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.OaNoticeMapper;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OaNoticeServiceImplTest {

    @Mock
    private OaNoticeMapper mockOaNoticeMapper;
    @Mock
    private NoticeReadRecordMapper mockNoticeReadRecordMapper;
    @Mock
    private AdminUserService mockUserService;

    @InjectMocks
    private OaNoticeServiceImpl oaNoticeServiceImplUnderTest;

    @Test
    void testCreateNotice() {
        // Setup
        final OaNoticeCreateReqVO reqVO = new OaNoticeCreateReqVO();
        reqVO.setType(0);
        reqVO.setContent("content");
        reqVO.setCreatorId(0L);
        reqVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setRecipientId("recipientId");
        reqVO.setFileUrl("fileUrl");
        reqVO.setIsRecruit(0);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        // Run the test
        final Long result = oaNoticeServiceImplUnderTest.createNotice(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);

        // Confirm OaNoticeMapper.updateById(...).
        final OaNoticeDO entity = new OaNoticeDO();
        entity.setId(0L);
        entity.setTitle("title");
        entity.setType(0);
        entity.setReadNumber(0L);
        entity.setTenantId(0L);
        verify(mockOaNoticeMapper).updateById(entity);

        // Confirm OaNoticeMapper.insert(...).
        final OaNoticeDO entity1 = new OaNoticeDO();
        entity1.setId(0L);
        entity1.setTitle("title");
        entity1.setType(0);
        entity1.setReadNumber(0L);
        entity1.setTenantId(0L);
        verify(mockOaNoticeMapper).insert(entity1);
    }

    @Test
    void testUpdateNotice() {
        // Setup
        final OaNoticeUpdateReqVO reqVO = new OaNoticeUpdateReqVO();
        reqVO.setType(0);
        reqVO.setContent("content");
        reqVO.setCreatorId(0L);
        reqVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setRecipientId("recipientId");
        reqVO.setFileUrl("fileUrl");
        reqVO.setIsRecruit(0);
        reqVO.setId(0L);

        // Configure OaNoticeMapper.selectById(...).
        final OaNoticeDO oaNoticeDO = new OaNoticeDO();
        oaNoticeDO.setId(0L);
        oaNoticeDO.setTitle("title");
        oaNoticeDO.setType(0);
        oaNoticeDO.setReadNumber(0L);
        oaNoticeDO.setTenantId(0L);
        when(mockOaNoticeMapper.selectById(0L)).thenReturn(oaNoticeDO);

        // Run the test
        oaNoticeServiceImplUnderTest.updateNotice(reqVO);

        // Verify the results
        // Confirm OaNoticeMapper.updateById(...).
        final OaNoticeDO entity = new OaNoticeDO();
        entity.setId(0L);
        entity.setTitle("title");
        entity.setType(0);
        entity.setReadNumber(0L);
        entity.setTenantId(0L);
        verify(mockOaNoticeMapper).updateById(entity);
    }

    @Test
    void testUpdateNotice_OaNoticeMapperSelectByIdReturnsNull() {
        // Setup
        final OaNoticeUpdateReqVO reqVO = new OaNoticeUpdateReqVO();
        reqVO.setType(0);
        reqVO.setContent("content");
        reqVO.setCreatorId(0L);
        reqVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setRecipientId("recipientId");
        reqVO.setFileUrl("fileUrl");
        reqVO.setIsRecruit(0);
        reqVO.setId(0L);

        when(mockOaNoticeMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> oaNoticeServiceImplUnderTest.updateNotice(reqVO)).isInstanceOf(ServiceException.class);
    }

    @Test
    void testUpdatePublishNotice() {
        // Setup
        final OaNoticeUpdateReqVO reqVO = new OaNoticeUpdateReqVO();
        reqVO.setType(0);
        reqVO.setContent("content");
        reqVO.setCreatorId(0L);
        reqVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setRecipientId("recipientId");
        reqVO.setFileUrl("fileUrl");
        reqVO.setIsRecruit(0);
        reqVO.setId(0L);

        // Configure OaNoticeMapper.selectById(...).
        final OaNoticeDO oaNoticeDO = new OaNoticeDO();
        oaNoticeDO.setId(0L);
        oaNoticeDO.setTitle("title");
        oaNoticeDO.setType(0);
        oaNoticeDO.setReadNumber(0L);
        oaNoticeDO.setTenantId(0L);
        when(mockOaNoticeMapper.selectById(0L)).thenReturn(oaNoticeDO);

        // Run the test
        oaNoticeServiceImplUnderTest.updatePublishNotice(reqVO);

        // Verify the results
        // Confirm OaNoticeMapper.updateById(...).
        final OaNoticeDO entity = new OaNoticeDO();
        entity.setId(0L);
        entity.setTitle("title");
        entity.setType(0);
        entity.setReadNumber(0L);
        entity.setTenantId(0L);
        verify(mockOaNoticeMapper).updateById(entity);
        verify(mockNoticeReadRecordMapper).emptyReadRecord(0L);
    }

    @Test
    void testUpdatePublishNotice_OaNoticeMapperSelectByIdReturnsNull() {
        // Setup
        final OaNoticeUpdateReqVO reqVO = new OaNoticeUpdateReqVO();
        reqVO.setType(0);
        reqVO.setContent("content");
        reqVO.setCreatorId(0L);
        reqVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setRecipientId("recipientId");
        reqVO.setFileUrl("fileUrl");
        reqVO.setIsRecruit(0);
        reqVO.setId(0L);

        when(mockOaNoticeMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> oaNoticeServiceImplUnderTest.updatePublishNotice(reqVO))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testDeleteNotice() {
        // Setup
        // Configure OaNoticeMapper.selectById(...).
        final OaNoticeDO oaNoticeDO = new OaNoticeDO();
        oaNoticeDO.setId(0L);
        oaNoticeDO.setTitle("title");
        oaNoticeDO.setType(0);
        oaNoticeDO.setReadNumber(0L);
        oaNoticeDO.setTenantId(0L);
        when(mockOaNoticeMapper.selectById(0L)).thenReturn(oaNoticeDO);

        // Run the test
        oaNoticeServiceImplUnderTest.deleteNotice(0L);

        // Verify the results
        verify(mockOaNoticeMapper).deleteById(0L);
    }

    @Test
    void testDeleteNotice_OaNoticeMapperSelectByIdReturnsNull() {
        // Setup
        when(mockOaNoticeMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> oaNoticeServiceImplUnderTest.deleteNotice(0L)).isInstanceOf(ServiceException.class);
    }

    @Test
    void testPageNotices() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeDO oaNoticeDO = new OaNoticeDO();
        oaNoticeDO.setId(0L);
        oaNoticeDO.setTitle("title");
        oaNoticeDO.setType(0);
        oaNoticeDO.setReadNumber(0L);
        oaNoticeDO.setTenantId(0L);
        final PageResult<OaNoticeDO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeDO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPage(...).
        final OaNoticeDO oaNoticeDO1 = new OaNoticeDO();
        oaNoticeDO1.setId(0L);
        oaNoticeDO1.setTitle("title");
        oaNoticeDO1.setType(0);
        oaNoticeDO1.setReadNumber(0L);
        oaNoticeDO1.setTenantId(0L);
        final PageResult<OaNoticeDO> oaNoticeDOPageResult = new PageResult<>(Arrays.asList(oaNoticeDO1), 0L);
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPage(reqVO1, Arrays.asList(0L))).thenReturn(oaNoticeDOPageResult);

        // Run the test
        final PageResult<OaNoticeDO> result = oaNoticeServiceImplUnderTest.pageNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPageNotices_AdminUserServiceReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeDO oaNoticeDO = new OaNoticeDO();
        oaNoticeDO.setId(0L);
        oaNoticeDO.setTitle("title");
        oaNoticeDO.setType(0);
        oaNoticeDO.setReadNumber(0L);
        oaNoticeDO.setTenantId(0L);
        final PageResult<OaNoticeDO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeDO), 0L);
        when(mockUserService.getUsersByNickname("title")).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPage(...).
        final OaNoticeDO oaNoticeDO1 = new OaNoticeDO();
        oaNoticeDO1.setId(0L);
        oaNoticeDO1.setTitle("title");
        oaNoticeDO1.setType(0);
        oaNoticeDO1.setReadNumber(0L);
        oaNoticeDO1.setTenantId(0L);
        final PageResult<OaNoticeDO> oaNoticeDOPageResult = new PageResult<>(Arrays.asList(oaNoticeDO1), 0L);
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPage(reqVO1, Arrays.asList(0L))).thenReturn(oaNoticeDOPageResult);

        // Run the test
        final PageResult<OaNoticeDO> result = oaNoticeServiceImplUnderTest.pageNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPageNotices_OaNoticeMapperReturnsNoItem() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPage(...).
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPage(reqVO1, Arrays.asList(0L))).thenReturn(PageResult.empty());

        // Run the test
        final PageResult<OaNoticeDO> result = oaNoticeServiceImplUnderTest.pageNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(PageResult.empty());
    }

    @Test
    void testPagePublishedNotices() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPageByStatusList(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusList(reqVO1, Arrays.asList(0L), 1, 0L, 0L))
                .thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPageByStatusNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusNum(reqVO2, Arrays.asList(0L), 1, 0L, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPageByStatusUserIdList(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePageReqVO reqVO3 = new OaNoticePageReqVO();
        reqVO3.setTitle("title");
        reqVO3.setType(0);
        reqVO3.setStatus(0);
        reqVO3.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO3.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdList(reqVO3, 1, 0L, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPageByStatusUserIdNum(...).
        final OaNoticePageReqVO reqVO4 = new OaNoticePageReqVO();
        reqVO4.setTitle("title");
        reqVO4.setType(0);
        reqVO4.setStatus(0);
        reqVO4.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO4.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdNum(reqVO4, 1, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pagePublishedNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPagePublishedNotices_AdminUserServiceReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);
        when(mockUserService.getUsersByNickname("title")).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPageByStatusList(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusList(reqVO1, Arrays.asList(0L), 1, 0L, 0L))
                .thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPageByStatusNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusNum(reqVO2, Arrays.asList(0L), 1, 0L, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPageByStatusUserIdList(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePageReqVO reqVO3 = new OaNoticePageReqVO();
        reqVO3.setTitle("title");
        reqVO3.setType(0);
        reqVO3.setStatus(0);
        reqVO3.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO3.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdList(reqVO3, 1, 0L, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPageByStatusUserIdNum(...).
        final OaNoticePageReqVO reqVO4 = new OaNoticePageReqVO();
        reqVO4.setTitle("title");
        reqVO4.setType(0);
        reqVO4.setStatus(0);
        reqVO4.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO4.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdNum(reqVO4, 1, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pagePublishedNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPagePublishedNotices_OaNoticeMapperSelectPageByStatusListReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPageByStatusList(...).
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusList(reqVO1, Arrays.asList(0L), 1, 0L, 0L))
                .thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPageByStatusNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusNum(reqVO2, Arrays.asList(0L), 1, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pagePublishedNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPagePublishedNotices_OaNoticeMapperSelectPageByStatusUserIdListReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPageByStatusUserIdList(...).
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdList(reqVO1, 1, 0L, 0L)).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPageByStatusUserIdNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdNum(reqVO2, 1, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pagePublishedNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPageDraftNotices() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPageByStatusList(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusList(reqVO1, Arrays.asList(0L), 2, 0L, 0L))
                .thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPageByStatusNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusNum(reqVO2, Arrays.asList(0L), 2, 0L, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPageByStatusUserIdList(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePageReqVO reqVO3 = new OaNoticePageReqVO();
        reqVO3.setTitle("title");
        reqVO3.setType(0);
        reqVO3.setStatus(0);
        reqVO3.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO3.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdList(reqVO3, 2, 0L, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPageByStatusUserIdNum(...).
        final OaNoticePageReqVO reqVO4 = new OaNoticePageReqVO();
        reqVO4.setTitle("title");
        reqVO4.setType(0);
        reqVO4.setStatus(0);
        reqVO4.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO4.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdNum(reqVO4, 2, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pageDraftNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPageDraftNotices_AdminUserServiceReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);
        when(mockUserService.getUsersByNickname("title")).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPageByStatusList(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusList(reqVO1, Arrays.asList(0L), 2, 0L, 0L))
                .thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPageByStatusNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusNum(reqVO2, Arrays.asList(0L), 2, 0L, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPageByStatusUserIdList(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePageReqVO reqVO3 = new OaNoticePageReqVO();
        reqVO3.setTitle("title");
        reqVO3.setType(0);
        reqVO3.setStatus(0);
        reqVO3.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO3.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdList(reqVO3, 2, 0L, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPageByStatusUserIdNum(...).
        final OaNoticePageReqVO reqVO4 = new OaNoticePageReqVO();
        reqVO4.setTitle("title");
        reqVO4.setType(0);
        reqVO4.setStatus(0);
        reqVO4.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO4.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdNum(reqVO4, 2, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pageDraftNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPageDraftNotices_OaNoticeMapperSelectPageByStatusListReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPageByStatusList(...).
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusList(reqVO1, Arrays.asList(0L), 2, 0L, 0L))
                .thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPageByStatusNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusNum(reqVO2, Arrays.asList(0L), 2, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pageDraftNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testPageDraftNotices_OaNoticeMapperSelectPageByStatusUserIdListReturnsNoItems() {
        // Setup
        final OaNoticePageReqVO reqVO = new OaNoticePageReqVO();
        reqVO.setTitle("title");
        reqVO.setType(0);
        reqVO.setStatus(0);
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUsersByNickname(...).
        final List<AdminUserDO> adminUserDOS = Arrays.asList(AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build());
        when(mockUserService.getUsersByNickname("title")).thenReturn(adminUserDOS);

        // Configure OaNoticeMapper.selectPageByStatusUserIdList(...).
        final OaNoticePageReqVO reqVO1 = new OaNoticePageReqVO();
        reqVO1.setTitle("title");
        reqVO1.setType(0);
        reqVO1.setStatus(0);
        reqVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdList(reqVO1, 2, 0L, 0L)).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPageByStatusUserIdNum(...).
        final OaNoticePageReqVO reqVO2 = new OaNoticePageReqVO();
        reqVO2.setTitle("title");
        reqVO2.setType(0);
        reqVO2.setStatus(0);
        reqVO2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOaNoticeMapper.selectPageByStatusUserIdNum(reqVO2, 2, 0L, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.pageDraftNotices(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetNotice() {
        // Setup
        final OaNoticeRespVO expectedResult = new OaNoticeRespVO();
        expectedResult.setType(0);
        expectedResult.setContent("content");
        expectedResult.setCreatorId(0L);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRecipientId("recipientId");
        expectedResult.setFileUrl("fileUrl");
        expectedResult.setIsRecruit(0);

        // Configure OaNoticeMapper.selectRespById(...).
        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        when(mockOaNoticeMapper.selectRespById(0L)).thenReturn(oaNoticeRespVO);

        // Run the test
        final OaNoticeRespVO result = oaNoticeServiceImplUnderTest.getNotice(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testReadNotice() {
        // Setup
        final OaNoticeRespVO expectedResult = new OaNoticeRespVO();
        expectedResult.setType(0);
        expectedResult.setContent("content");
        expectedResult.setCreatorId(0L);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRecipientId("recipientId");
        expectedResult.setFileUrl("fileUrl");
        expectedResult.setIsRecruit(0);

        when(mockNoticeReadRecordMapper.hadRecord(0L, 0L)).thenReturn(0);

        // Configure OaNoticeMapper.selectRespById(...).
        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        when(mockOaNoticeMapper.selectRespById(0L)).thenReturn(oaNoticeRespVO);

        // Run the test
        final OaNoticeRespVO result = oaNoticeServiceImplUnderTest.readNotice(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockOaNoticeMapper).updateReadNum(0L);
        verify(mockNoticeReadRecordMapper).insertOne(0L, 0L);
    }

    @Test
    void testCheckNoticeExists() {
        // Setup
        // Configure OaNoticeMapper.selectById(...).
        final OaNoticeDO oaNoticeDO = new OaNoticeDO();
        oaNoticeDO.setId(0L);
        oaNoticeDO.setTitle("title");
        oaNoticeDO.setType(0);
        oaNoticeDO.setReadNumber(0L);
        oaNoticeDO.setTenantId(0L);
        when(mockOaNoticeMapper.selectById(0L)).thenReturn(oaNoticeDO);

        // Run the test
        oaNoticeServiceImplUnderTest.checkNoticeExists(0L);

        // Verify the results
    }

    @Test
    void testCheckNoticeExists_OaNoticeMapperReturnsNull() {
        // Setup
        when(mockOaNoticeMapper.selectById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> oaNoticeServiceImplUnderTest.checkNoticeExists(0L))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testGetPersonalNotice() {
        // Setup
        final OaNoticePersonalReqVO reqVO = new OaNoticePersonalReqVO();
        reqVO.setTitle("title");
        reqVO.setCreator("creator");
        reqVO.setRead(0);
        reqVO.setDeptId(0L);
        reqVO.setUserId(0L);

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        // Configure OaNoticeMapper.selectPersonalNotice(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePersonalReqVO pageReqVO = new OaNoticePersonalReqVO();
        pageReqVO.setTitle("title");
        pageReqVO.setCreator("creator");
        pageReqVO.setRead(0);
        pageReqVO.setDeptId(0L);
        pageReqVO.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNotice(pageReqVO, 0L)).thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPersonalNoticeNum(...).
        final OaNoticePersonalReqVO pageReqVO1 = new OaNoticePersonalReqVO();
        pageReqVO1.setTitle("title");
        pageReqVO1.setCreator("creator");
        pageReqVO1.setRead(0);
        pageReqVO1.setDeptId(0L);
        pageReqVO1.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNum(pageReqVO1, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeRead(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePersonalReqVO pageReqVO2 = new OaNoticePersonalReqVO();
        pageReqVO2.setTitle("title");
        pageReqVO2.setCreator("creator");
        pageReqVO2.setRead(0);
        pageReqVO2.setDeptId(0L);
        pageReqVO2.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeRead(pageReqVO2, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPersonalNoticeNumRead(...).
        final OaNoticePersonalReqVO pageReqVO3 = new OaNoticePersonalReqVO();
        pageReqVO3.setTitle("title");
        pageReqVO3.setCreator("creator");
        pageReqVO3.setRead(0);
        pageReqVO3.setDeptId(0L);
        pageReqVO3.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumRead(pageReqVO3, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeNotRead(...).
        final OaNoticeRespVO oaNoticeRespVO3 = new OaNoticeRespVO();
        oaNoticeRespVO3.setType(0);
        oaNoticeRespVO3.setContent("content");
        oaNoticeRespVO3.setCreatorId(0L);
        oaNoticeRespVO3.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO3.setRecipientId("recipientId");
        oaNoticeRespVO3.setFileUrl("fileUrl");
        oaNoticeRespVO3.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS2 = Arrays.asList(oaNoticeRespVO3);
        final OaNoticePersonalReqVO pageReqVO4 = new OaNoticePersonalReqVO();
        pageReqVO4.setTitle("title");
        pageReqVO4.setCreator("creator");
        pageReqVO4.setRead(0);
        pageReqVO4.setDeptId(0L);
        pageReqVO4.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNotRead(pageReqVO4, 0L)).thenReturn(oaNoticeRespVOS2);

        // Configure OaNoticeMapper.selectPersonalNoticeNumNotRead(...).
        final OaNoticePersonalReqVO pageReqVO5 = new OaNoticePersonalReqVO();
        pageReqVO5.setTitle("title");
        pageReqVO5.setCreator("creator");
        pageReqVO5.setRead(0);
        pageReqVO5.setDeptId(0L);
        pageReqVO5.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumNotRead(pageReqVO5, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.getPersonalNotice(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonalNotice_AdminUserServiceReturnsNull() {
        // Setup
        final OaNoticePersonalReqVO reqVO = new OaNoticePersonalReqVO();
        reqVO.setTitle("title");
        reqVO.setCreator("creator");
        reqVO.setRead(0);
        reqVO.setDeptId(0L);
        reqVO.setUserId(0L);

        when(mockUserService.getUser(0L)).thenReturn(null);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.getPersonalNotice(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(PageResult.empty());
    }

    @Test
    void testGetPersonalNotice_OaNoticeMapperSelectPersonalNoticeReturnsNoItems() {
        // Setup
        final OaNoticePersonalReqVO reqVO = new OaNoticePersonalReqVO();
        reqVO.setTitle("title");
        reqVO.setCreator("creator");
        reqVO.setRead(0);
        reqVO.setDeptId(0L);
        reqVO.setUserId(0L);

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        // Configure OaNoticeMapper.selectPersonalNotice(...).
        final OaNoticePersonalReqVO pageReqVO = new OaNoticePersonalReqVO();
        pageReqVO.setTitle("title");
        pageReqVO.setCreator("creator");
        pageReqVO.setRead(0);
        pageReqVO.setDeptId(0L);
        pageReqVO.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNotice(pageReqVO, 0L)).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPersonalNoticeNum(...).
        final OaNoticePersonalReqVO pageReqVO1 = new OaNoticePersonalReqVO();
        pageReqVO1.setTitle("title");
        pageReqVO1.setCreator("creator");
        pageReqVO1.setRead(0);
        pageReqVO1.setDeptId(0L);
        pageReqVO1.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNum(pageReqVO1, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeRead(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePersonalReqVO pageReqVO2 = new OaNoticePersonalReqVO();
        pageReqVO2.setTitle("title");
        pageReqVO2.setCreator("creator");
        pageReqVO2.setRead(0);
        pageReqVO2.setDeptId(0L);
        pageReqVO2.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeRead(pageReqVO2, 0L)).thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPersonalNoticeNumRead(...).
        final OaNoticePersonalReqVO pageReqVO3 = new OaNoticePersonalReqVO();
        pageReqVO3.setTitle("title");
        pageReqVO3.setCreator("creator");
        pageReqVO3.setRead(0);
        pageReqVO3.setDeptId(0L);
        pageReqVO3.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumRead(pageReqVO3, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeNotRead(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePersonalReqVO pageReqVO4 = new OaNoticePersonalReqVO();
        pageReqVO4.setTitle("title");
        pageReqVO4.setCreator("creator");
        pageReqVO4.setRead(0);
        pageReqVO4.setDeptId(0L);
        pageReqVO4.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNotRead(pageReqVO4, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPersonalNoticeNumNotRead(...).
        final OaNoticePersonalReqVO pageReqVO5 = new OaNoticePersonalReqVO();
        pageReqVO5.setTitle("title");
        pageReqVO5.setCreator("creator");
        pageReqVO5.setRead(0);
        pageReqVO5.setDeptId(0L);
        pageReqVO5.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumNotRead(pageReqVO5, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.getPersonalNotice(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonalNotice_OaNoticeMapperSelectPersonalNoticeReadReturnsNoItems() {
        // Setup
        final OaNoticePersonalReqVO reqVO = new OaNoticePersonalReqVO();
        reqVO.setTitle("title");
        reqVO.setCreator("creator");
        reqVO.setRead(0);
        reqVO.setDeptId(0L);
        reqVO.setUserId(0L);

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        // Configure OaNoticeMapper.selectPersonalNotice(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePersonalReqVO pageReqVO = new OaNoticePersonalReqVO();
        pageReqVO.setTitle("title");
        pageReqVO.setCreator("creator");
        pageReqVO.setRead(0);
        pageReqVO.setDeptId(0L);
        pageReqVO.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNotice(pageReqVO, 0L)).thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPersonalNoticeNum(...).
        final OaNoticePersonalReqVO pageReqVO1 = new OaNoticePersonalReqVO();
        pageReqVO1.setTitle("title");
        pageReqVO1.setCreator("creator");
        pageReqVO1.setRead(0);
        pageReqVO1.setDeptId(0L);
        pageReqVO1.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNum(pageReqVO1, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeRead(...).
        final OaNoticePersonalReqVO pageReqVO2 = new OaNoticePersonalReqVO();
        pageReqVO2.setTitle("title");
        pageReqVO2.setCreator("creator");
        pageReqVO2.setRead(0);
        pageReqVO2.setDeptId(0L);
        pageReqVO2.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeRead(pageReqVO2, 0L)).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPersonalNoticeNumRead(...).
        final OaNoticePersonalReqVO pageReqVO3 = new OaNoticePersonalReqVO();
        pageReqVO3.setTitle("title");
        pageReqVO3.setCreator("creator");
        pageReqVO3.setRead(0);
        pageReqVO3.setDeptId(0L);
        pageReqVO3.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumRead(pageReqVO3, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeNotRead(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePersonalReqVO pageReqVO4 = new OaNoticePersonalReqVO();
        pageReqVO4.setTitle("title");
        pageReqVO4.setCreator("creator");
        pageReqVO4.setRead(0);
        pageReqVO4.setDeptId(0L);
        pageReqVO4.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNotRead(pageReqVO4, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPersonalNoticeNumNotRead(...).
        final OaNoticePersonalReqVO pageReqVO5 = new OaNoticePersonalReqVO();
        pageReqVO5.setTitle("title");
        pageReqVO5.setCreator("creator");
        pageReqVO5.setRead(0);
        pageReqVO5.setDeptId(0L);
        pageReqVO5.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumNotRead(pageReqVO5, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.getPersonalNotice(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPersonalNotice_OaNoticeMapperSelectPersonalNoticeNotReadReturnsNoItems() {
        // Setup
        final OaNoticePersonalReqVO reqVO = new OaNoticePersonalReqVO();
        reqVO.setTitle("title");
        reqVO.setCreator("creator");
        reqVO.setRead(0);
        reqVO.setDeptId(0L);
        reqVO.setUserId(0L);

        final OaNoticeRespVO oaNoticeRespVO = new OaNoticeRespVO();
        oaNoticeRespVO.setType(0);
        oaNoticeRespVO.setContent("content");
        oaNoticeRespVO.setCreatorId(0L);
        oaNoticeRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO.setRecipientId("recipientId");
        oaNoticeRespVO.setFileUrl("fileUrl");
        oaNoticeRespVO.setIsRecruit(0);
        final PageResult<OaNoticeRespVO> expectedResult = new PageResult<>(Arrays.asList(oaNoticeRespVO), 0L);

        // Configure AdminUserService.getUser(...).
        final AdminUserDO adminUserDO = AdminUserDO.builder()
                .id(0L)
                .deptId(0L)
                .build();
        when(mockUserService.getUser(0L)).thenReturn(adminUserDO);

        // Configure OaNoticeMapper.selectPersonalNotice(...).
        final OaNoticeRespVO oaNoticeRespVO1 = new OaNoticeRespVO();
        oaNoticeRespVO1.setType(0);
        oaNoticeRespVO1.setContent("content");
        oaNoticeRespVO1.setCreatorId(0L);
        oaNoticeRespVO1.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO1.setRecipientId("recipientId");
        oaNoticeRespVO1.setFileUrl("fileUrl");
        oaNoticeRespVO1.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS = Arrays.asList(oaNoticeRespVO1);
        final OaNoticePersonalReqVO pageReqVO = new OaNoticePersonalReqVO();
        pageReqVO.setTitle("title");
        pageReqVO.setCreator("creator");
        pageReqVO.setRead(0);
        pageReqVO.setDeptId(0L);
        pageReqVO.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNotice(pageReqVO, 0L)).thenReturn(oaNoticeRespVOS);

        // Configure OaNoticeMapper.selectPersonalNoticeNum(...).
        final OaNoticePersonalReqVO pageReqVO1 = new OaNoticePersonalReqVO();
        pageReqVO1.setTitle("title");
        pageReqVO1.setCreator("creator");
        pageReqVO1.setRead(0);
        pageReqVO1.setDeptId(0L);
        pageReqVO1.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNum(pageReqVO1, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeRead(...).
        final OaNoticeRespVO oaNoticeRespVO2 = new OaNoticeRespVO();
        oaNoticeRespVO2.setType(0);
        oaNoticeRespVO2.setContent("content");
        oaNoticeRespVO2.setCreatorId(0L);
        oaNoticeRespVO2.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        oaNoticeRespVO2.setRecipientId("recipientId");
        oaNoticeRespVO2.setFileUrl("fileUrl");
        oaNoticeRespVO2.setIsRecruit(0);
        final List<OaNoticeRespVO> oaNoticeRespVOS1 = Arrays.asList(oaNoticeRespVO2);
        final OaNoticePersonalReqVO pageReqVO2 = new OaNoticePersonalReqVO();
        pageReqVO2.setTitle("title");
        pageReqVO2.setCreator("creator");
        pageReqVO2.setRead(0);
        pageReqVO2.setDeptId(0L);
        pageReqVO2.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeRead(pageReqVO2, 0L)).thenReturn(oaNoticeRespVOS1);

        // Configure OaNoticeMapper.selectPersonalNoticeNumRead(...).
        final OaNoticePersonalReqVO pageReqVO3 = new OaNoticePersonalReqVO();
        pageReqVO3.setTitle("title");
        pageReqVO3.setCreator("creator");
        pageReqVO3.setRead(0);
        pageReqVO3.setDeptId(0L);
        pageReqVO3.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumRead(pageReqVO3, 0L)).thenReturn(0L);

        // Configure OaNoticeMapper.selectPersonalNoticeNotRead(...).
        final OaNoticePersonalReqVO pageReqVO4 = new OaNoticePersonalReqVO();
        pageReqVO4.setTitle("title");
        pageReqVO4.setCreator("creator");
        pageReqVO4.setRead(0);
        pageReqVO4.setDeptId(0L);
        pageReqVO4.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNotRead(pageReqVO4, 0L)).thenReturn(Collections.emptyList());

        // Configure OaNoticeMapper.selectPersonalNoticeNumNotRead(...).
        final OaNoticePersonalReqVO pageReqVO5 = new OaNoticePersonalReqVO();
        pageReqVO5.setTitle("title");
        pageReqVO5.setCreator("creator");
        pageReqVO5.setRead(0);
        pageReqVO5.setDeptId(0L);
        pageReqVO5.setUserId(0L);
        when(mockOaNoticeMapper.selectPersonalNoticeNumNotRead(pageReqVO5, 0L)).thenReturn(0L);

        // Run the test
        final PageResult<OaNoticeRespVO> result = oaNoticeServiceImplUnderTest.getPersonalNotice(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
