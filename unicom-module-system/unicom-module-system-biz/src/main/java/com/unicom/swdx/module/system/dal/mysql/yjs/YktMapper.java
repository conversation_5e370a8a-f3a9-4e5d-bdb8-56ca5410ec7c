package com.unicom.swdx.module.system.dal.mysql.yjs;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.dal.dataobject.yjs.CardDO;
import com.unicom.swdx.module.system.dal.dataobject.yjs.ClassInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


/**
 * 一卡通 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sqlserver")
public interface YktMapper extends BaseMapperX<CardDO>{

    @Select("SELECT SUM(cardValue) from T_PT_CardBags tpcb WHERE userId = (SELECT userId from\n" +
            " T_PT_User WHERE isDelete = 0 and otherSysId = #{othersystemid}) and bagCode in ('1','3')")
    Float selectMoney(@Param("othersystemid") String othersystemid);

}
