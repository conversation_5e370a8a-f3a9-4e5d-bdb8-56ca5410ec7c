package com.unicom.swdx.module.system.mq.producer.infor;

import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Dept 部门相关消息的 Producer
 */
@Component
@Slf4j
public class InforProducer extends AbstractProducer {

    @Async
    public void sendRefreshMessage(String code) {
        log.info("[send][ infor 发送刷新消息]");

        try {
            kafkaTemplate.send("refresh-service","SendMessageRefresh",code);
        }catch (Exception e){
            log.info("[send][ infor 发送刷新消息]  失败 ：{}" ,e.getMessage() );
        }

    }

    @Async
    public void sendRefreshMessage(Long sendId) {
        log.info("[send][ infor 发送刷新消息]");

        try {
            kafkaTemplate.send("refresh-service","SendMessageRefreshId", String.valueOf(sendId));
        }catch (Exception e){
            log.info("[send][ infor 发送刷新消息]  失败 ：{}" ,e.getMessage() );
        }

    }
}
