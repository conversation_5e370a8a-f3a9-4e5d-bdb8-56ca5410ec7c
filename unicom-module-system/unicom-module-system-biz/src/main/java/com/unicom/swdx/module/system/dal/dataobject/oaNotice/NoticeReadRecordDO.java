package com.unicom.swdx.module.system.dal.dataobject.oaNotice;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 通知公告阅读记录表
 *
 * <AUTHOR>
 */
@TableName("notice_read_record")
@KeySequence("notice_read_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
public class NoticeReadRecordDO {

    /**
     * 阅读记录ID
     */
    @TableId(type= IdType.AUTO)
    private Long id;
    /**
     * 公告ID
     *
     * 枚举 {1为通知，2为公告}
     */
    private Long noticeId;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 阅读人id
     */
    private Long userId;
    /**
     * 阅读时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
    /**
     * 阅读量
     */

}
