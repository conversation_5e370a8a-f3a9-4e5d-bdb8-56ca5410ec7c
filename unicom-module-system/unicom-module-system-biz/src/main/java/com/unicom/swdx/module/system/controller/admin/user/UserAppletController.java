package com.unicom.swdx.module.system.controller.admin.user;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.user.vo.applet.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserCreateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.service.applet.UserAppletService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static com.unicom.swdx.module.system.enums.user.UserCategoryEnum.MIDDLE_PLAT;

@Api(tags = "小程序 - 我的")
@RestController
@RequestMapping("/system/user/applet")
@Validated
public class UserAppletController {

    @Resource
    private UserAppletService userAppletService;

    @PostMapping("get-dashboard")
    @ApiOperation("仪表盘")
    public CommonResult<List<UserAppletDashboardVO>> getDashboard(@RequestBody UserAppletBaseVO userAppletBaseVO) {
        UserAppletTopVO result = userAppletService.getDashboard(userAppletBaseVO);
        return success(result.getData());
    }

    @PostMapping("get-teaching-methods")
    @ApiOperation("教学方式")
    public CommonResult<List<UserAppletTeachingMethodsVO>> getTeachingMethods(@RequestBody UserAppletBaseVO userAppletBaseVO) {
        UserAppletTeacherVO result = userAppletService.getTeachingMethods(userAppletBaseVO);
        return success(result.getData());
    }

    @PostMapping("get-course-situation")
    @ApiOperation("课程情况")
    public CommonResult<UserAppletReultVO> getCourseSituation(@RequestBody UserAppletBaseVO userAppletBaseVO) {
        UserAppletReultVO result = userAppletService.getCourseSituation(userAppletBaseVO);
        return success(result);
    }

    @PostMapping("get-course-total")
    @ApiOperation("课程情况total")
    public CommonResult<Integer> getCourseSituationTotal(@RequestBody UserAppletBaseVO userAppletBaseVO) {
        Integer result = userAppletService.getCourseSituationTotal(userAppletBaseVO);
        return success(result);
    }


    @PostMapping("get-type")
    @ApiOperation("获取用户类型")
    public CommonResult<Integer> getUserAppletType() {
        Integer result = userAppletService.getUserAppletType();
        return success(result);
    }
    @PostMapping("get-leader-depts")
    @ApiOperation("获取处长的部门")
    public CommonResult<List<DeptDO>> getDepts() {
        List<DeptDO> result = userAppletService.getDepts();
        return success(result);
    }
}
