package com.unicom.swdx.module.system.mq.producer.user;

import com.alibaba.fastjson.JSON;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaHeaderDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaPersonDTO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.UUID;

import static com.unicom.swdx.module.system.enums.kafka.person.PersonEventType.addPersonEventType;
import static com.unicom.swdx.module.system.enums.kafka.person.PersonEventType.personSender;

@Slf4j
@Component
public class ExternalPersonProducer extends AbstractProducer {

    @Value("${sendTopic.oldKafka}")
    private String oldKafkaTopic;

    @Async
    public void sendExternalPersonData(String eventType, AdminUserDO user) {
        log.info("[send][向一卡通发送新增党校外部人员信息数据]");
        OldKafkaHeaderDTO header = new OldKafkaHeaderDTO();
        header.setSender(personSender);
        header.setEventId(UUID.randomUUID().toString());
        header.setEventType(addPersonEventType);
        header.setTimestamp(System.currentTimeMillis());

        OldKafkaPersonDTO body = new OldKafkaPersonDTO();
        body.setEmployee_id(personSender+user.getId().toString());
        body.setName(user.getNickname());
        body.setMobile_phone(user.getMobile());
        body.setSex("男");
        if(Objects.equals(user.getSex(), 2)) {
            body.setSex("女");
        }
        body.setDept_id("001003000000");
        body.setDept_name("服务企业");
        body.setStatus("在职");

        OldKafkaMessageDTO message = new OldKafkaMessageDTO();
        message.setHeader(header);
        message.setBody(body);
        kafkaTemplate.send(oldKafkaTopic, JSON.toJSONString(message));
    }

}
