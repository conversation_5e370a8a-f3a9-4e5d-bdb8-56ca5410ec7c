package com.unicom.swdx.module.system.controller.admin.oauth2.vo.client;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.util.json.JsonUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* OAuth2 客户端 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class OAuth2ClientBaseVO {

    @ApiModelProperty(value = "应用标识", required = true, example = "zwfw")
    @NotBlank(message = "应用标识不能为空")
    private String code;

    @ApiModelProperty(value = "客户端编号", required = true, example = "tudou")
    @NotBlank(message = "客户端编号不能为空")
    private String clientId;

    @ApiModelProperty(value = "客户端密钥", required = true, example = "fan")
    @NotBlank(message = "客户端密钥不能为空")
    private String secret;

    @ApiModelProperty(value = "应用名", required = true, example = "土豆")
    @NotBlank(message = "应用名不能为空")
    private String name;

    @ApiModelProperty(value = "应用图标", required = true, example = "https://www.iocoder.cn/xx.png")
    @NotBlank(message = "应用图标不能为空")
    @URL(message = "应用图标的地址不正确")
    private String logo;

    @ApiModelProperty(value = "系统提供商")
    @NotBlank(message = "系统提供商不能为空")
    private String systemProvider;

    @ApiModelProperty(value = "联系人")
    @NotBlank(message = "联系人不能为空")
    private String contacts;

    @ApiModelProperty(value = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactNumber;

    @ApiModelProperty(value = "重定向授权地址")
    @NotBlank(message = "重定向授权地址不能为空")
    private String redirectAuthAddr;

    @ApiModelProperty(value = "应用描述", example = "我是一个应用")
    private String description;

    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "参见 CommonStatusEnum 枚举")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "访问令牌的有效期", required = true, example = "8640")
    @NotNull(message = "访问令牌的有效期不能为空")
    private Integer accessTokenValiditySeconds;

    @ApiModelProperty(value = "刷新令牌的有效期", required = true, example = "8640000")
    @NotNull(message = "刷新令牌的有效期不能为空")
    private Integer refreshTokenValiditySeconds;

    @ApiModelProperty(value = "应用地址", required = true, example = "https://www.iocoder.cn")
    @NotBlank(message = "应用地址不能为空")
    private String path;

    @ApiModelProperty(value = "授权类型", required = true, example = "password", notes = "参见 OAuth2GrantTypeEnum 枚举")
    //@NotEmpty(message = "授权类型不能为空")
    private List<String> authorizedGrantTypes;

    @ApiModelProperty(value = "显示顺序", required = true, example = "1")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @ApiModelProperty(value = "授权范围", example = "user_info")
    private List<String> scopes;

    @ApiModelProperty(value = "自动通过的授权范围", example = "user_info")
    private List<String> autoApproveScopes;

    @ApiModelProperty(value = "权限", example = "system:user:query")
    private List<String> authorities;

    @ApiModelProperty(value = "资源", example = "1024")
    private List<String> resourceIds;

    @ApiModelProperty(value = "附加信息", example = "{yunai: true}")
    private String additionalInformation;

    @AssertTrue(message = "附加信息必须是 JSON 格式")
    public boolean isAdditionalInformationJson() {
        return StrUtil.isEmpty(additionalInformation) || JsonUtils.isJson(additionalInformation);
    }

    @ApiModelProperty(value = "应用可见性（0为全可见，1为pc可见，2为小程序可见，3为不可见）")
    private Integer visibility = 0;

}
