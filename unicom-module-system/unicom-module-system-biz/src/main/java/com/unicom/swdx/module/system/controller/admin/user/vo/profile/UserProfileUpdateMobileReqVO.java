package com.unicom.swdx.module.system.controller.admin.user.vo.profile;

import com.unicom.swdx.framework.common.validation.Mobile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


@ApiModel("管理后台 - 用户更新App手机号 Request VO")
@Data
public class UserProfileUpdateMobileReqVO {
    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "旧手机号", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @Mobile
    private String oldMobile;

    @ApiModelProperty(value = "新手机号码", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @Mobile
    private String newMobile;

}
