package com.unicom.swdx.module.system.controller.admin.sms.vo.log;

import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.framework.excel.core.convert.JsonConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 短信日志 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SmsLogExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("手机号")
    private String mobile;

    @ExcelProperty(value = "短信内容", converter = JsonConvert.class)
    private Map<String, Object> templateParams;

    @ExcelProperty(value = "发送状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SMS_SEND_STATUS)
    private Integer sendStatus;

    @ExcelProperty("发送时间")
    private LocalDateTime sendTime;

    @ExcelProperty("模板编号")
    private String templateCode;

}
