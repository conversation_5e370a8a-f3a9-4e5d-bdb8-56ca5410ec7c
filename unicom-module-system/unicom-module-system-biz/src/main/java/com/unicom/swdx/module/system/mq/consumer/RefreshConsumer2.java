package com.unicom.swdx.module.system.mq.consumer;

import com.unicom.swdx.module.system.job.smsjob.QueueTask;
import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import com.unicom.swdx.module.system.service.permission.MenuService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.sensitiveword.SensitiveWordService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.tenant.TenantTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * Refresh 的消费者
 *
 * <AUTHOR>
 * @date 2023/7/6 15:53
 **/
@Component
@Slf4j
public class RefreshConsumer2 {

    @Resource
    private DeptService deptService;
    @Resource
    private OAuth2ClientService oauth2ClientService;
    @Resource
    private SensitiveWordService sensitiveWordService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantTypeService tenantTypeService;

    @KafkaListener(topics = "refresh-service", groupId = "${group-id.all}")
    public void onRefreshMessage(ConsumerRecord<String, String> record) {



        String refreshTag = record.value();
        log.info("[execute][收到 {} 刷新消息]", refreshTag);

        //单独处理inforProducer
        if(Objects.nonNull(record.key()) && Objects.equals("SendMessageRefresh",record.key())){
            log.info("[execute][收到 {} 刷新消息]", record.key());

            QueueTask.remove(refreshTag);

        } else if (Objects.nonNull(record.key()) && Objects.equals("SendMessageRefreshId",record.key())) {
            log.info("[execute][收到 {} 刷新消息]", record.key());

            QueueTask.remove(Long.parseLong(refreshTag));

        } else {
            log.info("[execute][收到 {} 刷新消息]", refreshTag);

            switch (refreshTag) {
                case "DeptRefresh":
                    // 部门
                    deptService.initLocalCache();
                    break;
                case "ClientRefresh":
                    // 应用
                    oauth2ClientService.initLocalCache();
                    break;
                case "SensitiveWordRefresh":
                    // 敏感词
                    sensitiveWordService.initLocalCache();
                    break;
                case "MenuRefresh":
                    // 菜单
                    menuService.deletecache();
                    break;
                case "PermissionRefresh":
                    // 权限
                    permissionService.initLocalCache();
                    break;
                case "RoleRefresh":
                    // 角色
                    roleService.initLocalCache();
                    break;
                case "TenantRefresh":
                    // 机构
                    tenantService.init();
                    break;
                case "TenantTypeRefresh":
                    // 机构用户类型
                    tenantTypeService.init();
                    break;
                default:
                    log.error("没有配置Consumer");
            }
        }
    }
}
