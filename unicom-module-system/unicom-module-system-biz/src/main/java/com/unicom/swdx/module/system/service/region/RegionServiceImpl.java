package com.unicom.swdx.module.system.service.region;


import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.api.region.dto.RegionDTO;
import com.unicom.swdx.module.system.convert.region.RegionConvert;
import com.unicom.swdx.module.system.dal.dataobject.region.RegionDO;
import com.unicom.swdx.module.system.dal.mysql.region.RegionMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 地区 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RegionServiceImpl implements RegionService {

    private volatile Map<Long,RegionDO> regionCache;

    @Resource
    private RegionMapper regionMapper;

    //@PostConstruct
    public void initCache() {
        List<RegionDO> regionList = regionMapper.selectList();
        regionCache = CollectionUtils.convertMap(regionList,RegionDO::getId);
    }

    @Override
    public List<RegionDO> getRegionListByParentId(Long id, Integer level) {
        return regionMapper.selectListByParentId(id, level);
    }

    /**
     * 根据id获取地区名
     * @param id id
     * @return 地区名
     */
    @Override
    public String getRegionName(Long id) {
        RegionDO regionDO = regionCache.get(id);
        return Objects.nonNull(regionDO)?regionDO.getName():"";
    }

    /**
     * 获取所有的地区（供rpc接口使用）
     * @return 地区
     */
    @Override
    public List<RegionDTO> getAllRegion() {
        List<RegionDO> regions = regionMapper.selectList();
        return RegionConvert.INSTANCE.convertList01(regions);
    }

    /**
     * 根据id获取行政区划代码
     * @param id id
     * @return 行政区划代码
     */
    @Override
    public Integer getRegionCode(Long id) {
        RegionDO regionDO = regionCache.get(id);
        return Objects.nonNull(regionDO)?regionDO.getCode():null;
    }

}
