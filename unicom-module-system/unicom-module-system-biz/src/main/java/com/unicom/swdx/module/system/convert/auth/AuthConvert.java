package com.unicom.swdx.module.system.convert.auth;

import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.unicom.swdx.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.unicom.swdx.module.system.api.social.dto.SocialUserBindReqDTO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.*;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginRespVO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.enums.permission.MenuIdEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;

@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    AuthLoginRespVO convert(OAuth2AccessTokenDO bean);

    default AuthPermissionInfoRespVO convert(AdminUserDO user, List<RoleDO> roleList, List<MenuDO> menuList) {
        return AuthPermissionInfoRespVO.builder()
            .user(AuthPermissionInfoRespVO.UserVO.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .nickname(user.getNickname())
                    .avatar(user.getAvatar())
                    .sex(user.getSex())
                    .tenantId(user.getTenantId())
                    .mobile(user.getMobile())
                    .postIds(user.getPostIds())
                    .departments(user.getDepartments())
                    .othersystemid(user.getOthersystemid())
                    .build())
            .roles(CollectionUtils.convertSet(roleList, RoleDO::getCode))
            .permissions(CollectionUtils.convertSet(menuList, MenuDO::getPermission))
            .build();
    }

    AuthMenuRespVO convertTreeNode(MenuDO menu);

    /**
     * 将菜单列表，构建成菜单树
     *
     * @param menuList 菜单列表
     * @return 菜单树
     */
    default List<AuthMenuRespVO> buildMenuTree(List<MenuDO> menuList) {
        // 排序，保证菜单的有序性
        menuList.sort(Comparator.comparing(MenuDO::getSort));
        // 构建菜单树
        // 使用 LinkedHashMap 的原因，是为了排序 。实际也可以用 Stream API ，就是太丑了。
        Map<Long, AuthMenuRespVO> treeNodeMap = new LinkedHashMap<>();
        menuList.forEach(menu -> treeNodeMap.put(menu.getId(), AuthConvert.INSTANCE.convertTreeNode(menu)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getParentId().equals(MenuIdEnum.ROOT.getId())).forEach(childNode -> {
            // 获得父节点
            AuthMenuRespVO parentNode = treeNodeMap.get(childNode.getParentId());
            if (parentNode == null) {
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });
        // 获得到所有的根节点
        return CollectionUtils.filterList(treeNodeMap.values(), node -> MenuIdEnum.ROOT.getId().equals(node.getParentId()));
    }

    SocialUserBindReqDTO convert(Long userId, Integer userType, AuthSocialBindLoginReqVO reqVO);

    SmsCodeSendReqDTO convert(AuthSmsSendReqVO reqVO);

    SmsCodeUseReqDTO convert(AuthSmsLoginReqVO reqVO, Integer scene, String usedIp);

    WxXcxAuthLoginRespVO convertWxXcx(AuthLoginRespVO bean);

}
