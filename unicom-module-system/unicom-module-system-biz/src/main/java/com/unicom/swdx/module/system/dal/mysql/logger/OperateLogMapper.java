package com.unicom.swdx.module.system.dal.mysql.logger;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.logger.vo.operatelog.OperateLogExportReqVO;
import com.unicom.swdx.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.logger.OperateLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface OperateLogMapper extends BaseMapperX<OperateLogDO> {

    PageResult<OperateLogDO> selectOperateLogPage(@Param("param") OperateLogPageReqVO reqVO,
                                                  @Param("userIds") Collection<Long> userIds);
    default PageResult<OperateLogDO> selectPage(OperateLogPageReqVO reqVO, Collection<Long> userIds) {
        LambdaQueryWrapperX<OperateLogDO> query = new LambdaQueryWrapperX<OperateLogDO>()
                .eqIfPresent(OperateLogDO::getType, reqVO.getType())
                .betweenIfPresent(OperateLogDO::getStartTime, reqVO.getStartTime())
                .and(StrUtil.isNotBlank(reqVO.getOperName()),qw ->
                        qw.in(CollUtil.isNotEmpty(userIds),OperateLogDO::getUserId, userIds)
                                .or().like(OperateLogDO::getModule, reqVO.getOperName()));

        if (Boolean.TRUE.equals(reqVO.getSuccess())) {
            query.eq(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        } else if (Boolean.FALSE.equals(reqVO.getSuccess())) {
            query.gt(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        }

        query.eq(OperateLogDO::getTenantId , SecurityFrameworkUtils.getTenantId());
        query.orderByDesc(OperateLogDO::getId); // 降序
        return selectPage(reqVO, query);
    }

    default List<OperateLogDO> selectList(OperateLogExportReqVO reqVO, Collection<Long> userIds) {
        LambdaQueryWrapperX<OperateLogDO> query = new LambdaQueryWrapperX<OperateLogDO>()
                .eqIfPresent(OperateLogDO::getType, reqVO.getType())
                .betweenIfPresent(OperateLogDO::getStartTime, reqVO.getStartTime())
                .and(StrUtil.isNotBlank(reqVO.getOperName()),qw ->
                        qw.in(CollUtil.isNotEmpty(userIds),OperateLogDO::getUserId, userIds)
                                .or().like(OperateLogDO::getModule, reqVO.getOperName()));

        if (Boolean.TRUE.equals(reqVO.getSuccess())) {
            query.eq(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        } else if (Boolean.FALSE.equals(reqVO.getSuccess())) {
            query.gt(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        }
        query.eq(OperateLogDO::getTenantId , SecurityFrameworkUtils.getTenantId());
        query.orderByDesc(OperateLogDO::getId); // 降序
        query.last("LIMIT 10000");
        return selectList(query);
    }

}
