package com.unicom.swdx.module.system.dal.dataobject.yjs;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;


/**
 * 班级 DO
 *
 * <AUTHOR>
 */
@TableName("t_class_info")
@KeySequence("t_class_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassInfoDO {

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 年份
     */
    private String years;
    /**
     * 学期
     */
    private String semester;
    /**
     * 所属校区
     */
    private String belongingToCampus;
    /**
     * 所属学院
     */
    private String belongingToCollege;
    /**
     * 所属系
     */
    private String belongingToFaculty;
    /**
     * 专业名称
     */
    private String majorName;
    /**
     * 专业类型
     */
    private String majorType;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 班级简称
     */
    private String classAbbreviation;
    /**
     * 辅导员id
     */
    private String instructorId;
    /**
     * 辅导员名称
     */
    private String instructorName;
    /**
     * 班主任ID
     */
    private String masterId;
    /**
     * 班主任名称
     */
    private String masterName;
    /**
     * 教室id
     */
    private String roomId;
    /**
     * 教室名称
     */
    private String roomName;
    /**
     * 状态
     */
    private String status;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 创建用户id
     */
    private String createUserId;
    /**
     * 创建用户名
     */
    private String createUserName;
    /**
     * 更新用户
     */
    private String updateUserId;
    /**
     * 更新用户名
     */
    private String updateUserName;
    /**
     * 开班时间
     */
    private LocalDateTime openingTime;
    /**
     * 结束时间
     */
    private LocalDateTime closeTime;
    /**
     * 身份id
     */
    private String commonId;

}
