package com.unicom.swdx.module.system.service.user.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: TraineeDO
 * @Author: lty
 * @Date: 2024/10/9 11:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class TraineeDTO extends BaseDO {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 学员名称
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 身份证
     */
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    private String cardNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 单位名称
     */
    private String unitName;


    /**
     * 单位分类
     */
    private Long unitClassification;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 文化程度
     */
    private Integer educationalLevel;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    /**
     * 种族
     */
    private Integer ethnic;

    /**
     * 职务
     */
    private String position;

    /**
     * 职级
     */
    private Integer jobLevel;

    /**
     * 毕业院校
     */
    private String graduationSchool;

    /**
     * 政治面貌
     */
    private Integer politicalIdentity;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;


    /**
     * 学员状态
     */
    private Integer status;

    /**
     * 学员照片
     */
    private String photo;

    /**
     * 小组id
     */
    private Long groupId;

    /**
     * 小组排序
     */
    private Integer groupSort;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 单位id
     */
    private Long unitId;

    /**
     * 报到时间
     */
    private LocalDateTime reportTime;

    /**
     * 结业时间
     */
    private LocalDate graduateDate;

    /**
     * 班委id
     */
    private Long classCommitteeId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 退学原因
     */
    private String dropoutReason;

    /**
     * 退学日期
     */
    private LocalDate dropoutDate;

    @TableField(exist = false)
    private boolean async = true;
}
