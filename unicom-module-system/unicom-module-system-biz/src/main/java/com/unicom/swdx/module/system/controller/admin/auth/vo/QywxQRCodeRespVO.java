package com.unicom.swdx.module.system.controller.admin.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/23 11:30
 **/
@Data
@ApiModel(value = "管理后台 - 企业微信二维码 Response VO")
public class QywxQRCodeRespVO {

    @ApiModelProperty("二维码链接")
    private String qrCodeUrl;

    @ApiModelProperty("二维码链接携带的唯一码")
    private String uniqueCode;

}
