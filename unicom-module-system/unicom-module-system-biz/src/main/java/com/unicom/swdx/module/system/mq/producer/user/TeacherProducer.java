package com.unicom.swdx.module.system.mq.producer.user;

import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaHeaderDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaPersonDTO;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
//@Component
public class TeacherProducer extends AbstractProducer {

//    @Resource
//    protected KafkaTemplate<Object, Object> kafkaTemplate;

    /**
     * {"header":{
     * "eventId":"10864e52-79f9-4c14-8532-6a81d88417ca",
     * "sender":"003",
     * "eventType":"00300005",
     * "timestamp":"1711012122483"
     * },
     * "body":{
     * "education":"大学本科毕业",
     * "mobile_phone":"15243670643",
     * "employee_id":"003c73fd7b1f26511eab285d39f7c63a093",
     * "sex":"男",
     * "edu_degree":"教育学学士学位",
     * "name":"罗松",
     * "dept_name":"离退休人员工作办公室",
     * "duty":"",
     * "staff_type_":"工勤技能岗位人员",
     * "dept_id":"001001017000",
     * "status":"在职"}
     * }
     */

    @Async
    public void sendTeacherData() {
        log.info("[send][向kafka发送老系统教职工更新数据]");

        OldKafkaHeaderDTO header = new OldKafkaHeaderDTO();
        header.setSender("003");
        header.setEventId("10864e52-79f9-4c14-8532-6a81d88417ca");
        header.setEventType("00300005");
        header.setTimestamp(System.currentTimeMillis());

        OldKafkaPersonDTO body = new OldKafkaPersonDTO();
        body.setEmployee_id("003c73fd7b1f26511eab285d39f7c63a093");
        body.setName("罗松");
        body.setMobile_phone("15243670643");
        body.setSex("男");
        body.setEducation("大学本科毕业");
        body.setEdu_degree("教育学学士学位");
        body.setDept_id("001001017000");
        body.setDept_name("离退休人员工作办公室");
        body.setDuty("");
        body.setStaff_type_("工勤技能岗位人员");
        body.setStatus("在职");

        OldKafkaMessageDTO message = new OldKafkaMessageDTO();
        message.setHeader(header);
        message.setBody(body);

//        kafkaTemplate.send("old-kafka", JSON.toJSONString(message));
    }

}
