package com.unicom.swdx.module.system.convert.message;

import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TimedTaskConvert {

    TimedTaskConvert INSTANCE = Mappers.getMapper(TimedTaskConvert.class);

    TimedTaskDO convert(SmsSendReq bean);
    SmsSendReq convert1(TimedTaskDO bean);

}
