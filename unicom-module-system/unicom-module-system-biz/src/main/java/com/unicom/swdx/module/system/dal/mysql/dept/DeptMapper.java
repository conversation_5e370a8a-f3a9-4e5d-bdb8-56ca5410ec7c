package com.unicom.swdx.module.system.dal.mysql.dept;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.dept.dto.DeptDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptListReqVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface DeptMapper extends BaseMapperX<DeptDO> {
    default List<DeptDO> selectList01(DeptListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeptDO>()
                .likeIfPresent(DeptDO::getName, reqVO.getName())
                .eqIfPresent(DeptDO::getStatus, reqVO.getStatus())
                .orderByAsc(DeptDO::getSort)
                .orderByDesc(DeptDO::getUpdateTime));
    }default List<DeptDO> selectList02(DeptListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeptDO>()
                .likeIfPresent(DeptDO::getName, reqVO.getName())
                .eqIfPresent(DeptDO::getTenantId,reqVO.getTenantId())
                .eqIfPresent(DeptDO::getStatus, reqVO.getStatus())
                .orderByAsc(DeptDO::getSort)
                .orderByDesc(DeptDO::getUpdateTime));
    }
    default List<DeptDO> selectList(DeptListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeptDO>()
                .likeIfPresent(DeptDO::getName, reqVO.getName())
                .eqIfPresent(DeptDO::getTenantId,reqVO.getTenantId())
                .eqIfPresent(DeptDO::getStatus, reqVO.getStatus())
                .orderByAsc(DeptDO::getSort,DeptDO::getId));
    }


    DeptDO selectByIdIgnoDeleted(Long id) ;

    default List<DeptDO> selectListInTenant(DeptListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeptDO>()
                .likeIfPresent(DeptDO::getName, reqVO.getName())
                .eqIfPresent(DeptDO::getTenantId,reqVO.getTenantId())
                .eqIfPresent(DeptDO::getStatus, reqVO.getStatus())
                .inIfPresent(DeptDO::getTenantId,reqVO.getTenantIds()));
    }

    default DeptDO selectByParentIdAndName(Long parentId, String name) {
        return selectOne(new LambdaQueryWrapper<DeptDO>()
                .eq(DeptDO::getParentId, parentId)
                .eq(DeptDO::getName, name));
    }

    default List<DeptDO> getChildrenDeptByDeptId(Long parentId) {
        return selectList(new LambdaQueryWrapper<DeptDO>()
                .eq(DeptDO::getParentId, parentId)
                .eq(DeptDO::getStatus,"0"));
    }

    @Select("SELECT id FROM system_dept WHERE tenant_id = #{tenantId} and parent_id = 0 and deleted = 0")
    Long selectDeptIdByTenantId(Long tenantId);

    @Select("SELECT tenant_id FROM system_dept WHERE id = #{ParentId}")
    Long selectTenantIdByDeptId(Long ParentId);
    @Select("SELECT COUNT(*) FROM system_dept WHERE update_time > #{maxUpdateTime}")
    Long selectCountByUpdateTimeGt(LocalDateTime maxUpdateTime);

    @Update("update system_dept set leader_user_id = null where leader_user_id = #{userId}")
    void updateLeaderByUserId(Long userId);

    void updateStatusByDeptIdList(@Param("idList") List idList,@Param("status") Integer status);

    @Select(" select * from\n" +
            "        (SELECT * FROM system_dept START WITH Id = #{deptId} CONNECT BY  PRIOR ID = PARENT_ID)\n" +
            "        where DELETED = 0;")
    List<DeptDO> selectChildrenDept(Long deptId);

    @Select(" <script> with recursive t as\n" +
            "(\t\n" +
            "\tselect * from system_dept where id in" +
            "   <foreach collection='deptIds' item='deptId' open='(' close=')' separator=','>" +
            "       #{deptId}" +
            "   </foreach>" +
            "  and deleted='0'\n" +
            "\tunion all\n" +
            "\tselect a.* from system_dept a join t on a.parent_id = t.id WHERE a.deleted='1'\n" +
            ")\n" +
            "select * from t where id not in " +
            "   <foreach collection='deptIds' item='deptId' open='(' close=')' separator=','>" +
            "       #{deptId}" +
            "   </foreach>" +
            "</script>")
    List<DeptDO> selectChildrenDeletedDept(@Param("deptIds") List<Long> deptIds);


    default void updateRootNameByTenantId(Long tenantId, String name) {
        update(DeptDO.builder().name(name).build(),new LambdaQueryWrapperX<DeptDO>()
                .eq(DeptDO::getTenantId,tenantId)
                .eq(DeptDO::getParentId,0L)
        );
    }

    default Long selectCountByTenantId(Long id) {
        return selectCount(DeptDO::getTenantId,id);
    }

    default void deleteByTenantId(Long tenantId) {
        delete(new LambdaQueryWrapperX<DeptDO>().eq(DeptDO::getTenantId,tenantId));
    }

    @Select("SELECT * FROM system_dept WHERE leader_user_id = #{leaderUserId} limit 1")
    DeptDO selectByLeaderUserId(Long leaderUserId);

    default DeptDO selectByDeptNameAndTenantId(String deptName, Long tenantId) {
        return selectOne(DeptDO::getName,deptName, DeptDO::getTenantId,tenantId);
    }

    Long getDeptIdByUserId(Long id);

    List<DeptDTO> selectSimpleDeptInfoList();

    //
    List<Long> selectLeaderIdsByDeptId(@Param("leaderDeptIds") List<Long> leaderDeptIds);

    Long getIdByName(@Param("name")String name ,@Param("tenantId")Long tenantId);

}
