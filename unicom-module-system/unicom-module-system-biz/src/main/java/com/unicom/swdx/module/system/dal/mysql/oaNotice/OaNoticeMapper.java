package com.unicom.swdx.module.system.dal.mysql.oaNotice;

import cn.hutool.core.text.CharSequenceUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticePageReqVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticePersonalReqVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface OaNoticeMapper extends BaseMapperX<OaNoticeDO> {

    default PageResult<OaNoticeDO> selectPage(OaNoticePageReqVO reqVO,Collection<Long>userIds) {
        LambdaQueryWrapperX<OaNoticeDO> query = new LambdaQueryWrapperX<OaNoticeDO>()
                .eqIfPresent(OaNoticeDO::getType,reqVO.getType())
                .eqIfPresent(OaNoticeDO::getStatus, reqVO.getStatus())
                .eq(OaNoticeDO::getTenantId , SecurityFrameworkUtils.getTenantId());

        if (CharSequenceUtil.isNotBlank(reqVO.getTitle())) {
            query.inIfPresent(OaNoticeDO::getCreatorId, userIds).or(qw -> qw.like(OaNoticeDO::getTitle, reqVO.getTitle()));
        }
        query.orderByDesc(OaNoticeDO::getId); // 降序
        return selectPage(reqVO, query);
    }

    default PageResult<OaNoticeDO> selectPageByStatus(OaNoticePageReqVO reqVO,Collection<Long>userIds,Integer status) {
        LambdaQueryWrapperX<OaNoticeDO> query = new LambdaQueryWrapperX<OaNoticeDO>()
                .eq(OaNoticeDO::getStatus,status)
                .eqIfPresent(OaNoticeDO::getType,reqVO.getType())
                .eqIfPresent(OaNoticeDO::getStatus, reqVO.getStatus())
                .eqIfPresent(OaNoticeDO::getRemoved, reqVO.getRemoved())
                //大于等于开始时间，小于等于截止时间
                .geIfPresent(OaNoticeDO::getCreateTime, reqVO.getStartTime())
                .leIfPresent(OaNoticeDO::getCreateTime, reqVO.getEndTime());

        if (CharSequenceUtil.isNotBlank(reqVO.getTitle())) {
            if(userIds.isEmpty()){
                query.like(OaNoticeDO::getTitle, reqVO.getTitle());
            }else {
                query.inIfPresent(OaNoticeDO::getCreatorId, userIds).or(qw -> qw.like(OaNoticeDO::getTitle, reqVO.getTitle()));
            }
        }
        query.orderByDesc(OaNoticeDO::getId); // 降序

        return selectPage(reqVO, query);
    }

    List<OaNoticeRespVO> selectPageByStatusList(@Param("param")OaNoticePageReqVO reqVO,@Param("userIds")Collection<Long>userIds
            ,@Param("status")Integer status,@Param("userId")Long userId ,@Param("tenantid")Long tenantid);

    OaNoticeRespVO selectRespById(@Param("id")Long id);

    Long selectPageByStatusNum(@Param("param")OaNoticePageReqVO reqVO,@Param("userIds")Collection<Long>userIds
            ,@Param("status")Integer status,@Param("userId")Long userId ,@Param("tenantid")Long tenantid);

    List<OaNoticeRespVO> selectPageByStatusUserIdList(@Param("param")OaNoticePageReqVO reqVO
            ,@Param("status")Integer status,@Param("userId")Long userId ,@Param("tenantid")Long tenantid);

    Long selectPageByStatusUserIdNum(@Param("param")OaNoticePageReqVO reqVO,@Param("status")Integer status,@Param("userId")Long userId ,@Param("tenantid")Long tenantid);

    List<OaNoticeDO> selectNoticePage(@Param("param") OaNoticePageReqVO pageReqVO ,@Param("tenantid")Long tenantid);

    List<OaNoticeRespVO> selectPersonalNotice(@Param("param") OaNoticePersonalReqVO pageReqVO ,@Param("tenantid")Long tenantid);

    Long selectPersonalNoticeNum(@Param("param") OaNoticePersonalReqVO pageReqVO  ,@Param("tenantid")Long tenantid);

    void updateReadNum(@Param("id")Long id);

    void emptyReadNum(@Param("id")Long id);

    List<OaNoticeRespVO> selectPersonalNoticeRead(@Param("param") OaNoticePersonalReqVO pageReqVO  ,@Param("tenantid")Long tenantid);

    Long selectPersonalNoticeNumRead(@Param("param") OaNoticePersonalReqVO pageReqVO  ,@Param("tenantid")Long tenantid);

    List<OaNoticeRespVO> selectPersonalNoticeNotRead(@Param("param") OaNoticePersonalReqVO pageReqVO  ,@Param("tenantid")Long tenantid);

    Long selectPersonalNoticeNumNotRead(@Param("param") OaNoticePersonalReqVO pageReqVO  ,@Param("tenantid")Long tenantid);

}

