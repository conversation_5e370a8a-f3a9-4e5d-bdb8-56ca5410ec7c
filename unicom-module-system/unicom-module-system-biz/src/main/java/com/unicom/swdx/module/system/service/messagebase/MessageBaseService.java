package com.unicom.swdx.module.system.service.messagebase;

import com.unicom.swdx.module.system.controller.admin.message.vo.MassageVO;

import java.util.Map;

/**
 * 短信通知 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageBaseService {

    Map<String, Object>sendSms(MassageVO massageVO);

    void sendSingleMessage(String mobile,String msg);

    void uplinkMessage(String mobile,String msg,String cpMoblie);


}
