package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/28 13:59
 **/
@ApiModel("首页 - 按月获取日程简介 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendarWnlScheduleRespVO {

    @ApiModelProperty(value = "阳历日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate solarCalendar;

    @ApiModelProperty(value = "阴历日期")
    private String lunarCalendar;

    @ApiModelProperty(value = "日期类型",notes = "enum(0, 1, 2, 3),分别表示 工作日、周末、节日、调休")
    private Integer dateType;

    private List<ScheduleRespVO> schedules;

    @ApiModelProperty(value = "阳历年")
    private int year;

    @ApiModelProperty(value = "阳历月")
    private int month;

    @ApiModelProperty(value = "阳历日")
    private int dayOfMonth;

    @ApiModelProperty(value = "是否是今天")
    private boolean isToday;

    @ApiModelProperty(value = "是否是本月")
    private boolean isThisMonth;
}
