package com.unicom.swdx.module.system.dal.mysql.tenant;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeRoleDO;

import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 机构用户类型角色 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantTypeRoleMapper extends BaseMapperX<TenantTypeRoleDO> {

    default void deleteByTypeId(Long tenantTypeId) {
        delete(new LambdaQueryWrapperX<TenantTypeRoleDO>().eq(TenantTypeRoleDO::getTenantTypeId,tenantTypeId));
    }

    default List<TenantTypeRoleDO> selectListByTypeIdList(Collection<Long> typeIdList) {
        return selectList(new LambdaQueryWrapperX<TenantTypeRoleDO>()
                .inIfPresent(TenantTypeRoleDO::getTenantTypeId,typeIdList)
        );
    }
}
