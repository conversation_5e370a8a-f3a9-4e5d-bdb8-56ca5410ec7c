package com.unicom.swdx.module.system.controller.admin.permission.vo.role;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel("管理后台 - 角色分配用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleUsersPageReqVO extends PageParam {

    @ApiModelProperty(value = "角色id", example = "1", required = true)
    private Long roleId;
}
