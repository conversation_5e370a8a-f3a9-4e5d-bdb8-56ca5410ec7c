package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TodoListPageAllVO  {
    @ApiModelProperty(value = "待办理事项", required = true)
    private String task;

    @ApiModelProperty(value = "事项发起人", required = true)
    private String name;

    @ApiModelProperty(value = "部门", required = true)
    private String deptName;

    @ApiModelProperty(value = "发起时间", required = true)
    private String initiationTime;

//    @ApiModelProperty(value = "事项来源", required = true)
//    private String source;
    @ApiModelProperty(value = "流程实例的编号", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "类别", required = true)
    private String category;

    @ApiModelProperty(value = "状态", required = true)
    private String status;

    @ApiModelProperty(value = "年度标签", required = true)
    private String yeartag;

    @ApiModelProperty(value = "任务名", required = true)
    private String bteName;

    @ApiModelProperty(value = "任务状态", required = true)
    private String bteStatus;

    @ApiModelProperty(value = "催办状态", required = true)
    private int superviseStatus;

    @ApiModelProperty(value = "催办状态", required = true)
    private Integer shandle;

    @ApiModelProperty(value = "发起时间", required = true)
    private String endtime;

    @ApiModelProperty(value = "创建时间", required = true)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private String createTime;

    @ApiModelProperty(value = "填报审批", required = true)
    private String hhandle;

    @ApiModelProperty(value = "可办次数", required = true)
    private String handleCount;

    private List<UserinforToDoVO> userinforDOList;

    @ApiModelProperty(value = "发起人id", required = true)
    private Long suserid;

}
