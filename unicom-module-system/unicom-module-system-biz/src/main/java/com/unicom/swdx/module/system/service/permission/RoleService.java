package com.unicom.swdx.module.system.service.permission;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.*;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 角色 Service 接口
 *
 * <AUTHOR>
 */
public interface RoleService {

    /**
     * 初始化角色的本地缓存
     */
    void initLocalCache();

    /**
     * 创建角色
     *
     * @param reqVO 创建角色信息
     * @return 角色编号
     */
    Long createRole(@Valid RoleCreateReqVO reqVO);

    /**
     * 更新角色
     *
     * @param reqVO 更新角色信息
     */
    void updateRole(@Valid RoleUpdateReqVO reqVO);

    /**
     * 删除角色
     *
     * @param id 角色编号
     */
    void deleteRole(Long id);

    /**
     * 更新角色状态
     *
     * @param id 角色编号
     * @param status 状态
     */
    void updateRoleStatus(Long id, Integer status);

    /**
     * 设置角色的数据权限
     *
     * @param id 角色编号
     * @param dataScope 数据范围
     * @param dataScopeDeptIds 部门编号数组
     */
    void updateRoleDataScope(Long id, Integer dataScope, Set<Long> dataScopeDeptIds);

    /**
     * 获得角色，从缓存中
     * （已废弃，推荐用getRoleById()）
     *
     * @param id 角色编号
     * @return 角色
     */
    @Deprecated
    RoleDO getRoleFromCache(Long id);

    /**
     * 获得角色，优先从缓存中获取
     *
     * @param id 角色编号
     * @return 角色
     */
    RoleDO getRoleById(Long id);

    /**
     * 获得角色集合，优先从缓存中获取
     *
     * @param ids 角色编号集合
     * @return 角色集合
     */
    List<RoleDO> getRolesByIds(Collection<Long> ids);

    /**
     * 根据用户id获得角色列表下拉框
     *
     * @param userId 用户id
     * @return 角色列表
     */
    List<RoleClientSimpleRespVO> getSimpleRoleList(Long userId);

    /**
     * 根据角色类型查询角色列表下拉框
     * @param type 角色类型
     * @return 角色列表
     */
    List<RoleClientSimpleRespVO> getClientSimpleRoles(Integer type);

    /**
     * 判断角色数组中，是否有超级管理员
     *
     * @param roleList 角色数组
     * @return 是否有管理员
     */
    boolean hasAnySuperAdmin(Collection<RoleDO> roleList);

    /**
     * 判断角色编号数组中，是否有管理员
     *
     * @param ids 角色编号数组
     * @return 是否有管理员
     */
    default boolean hasAnySuperAdmin(Set<Long> ids) {
        return hasAnySuperAdmin(getRolesByIds(ids));
    }

    /**
     * 获得角色
     *
     * @param id 角色编号
     * @return 角色
     */
    RoleRespVO getRole(Long id);

    /**
     * 获得角色已分配用户信息
     * @param pageReqVO 角色用户信息
     * @return 角色已分配用户信息
     */
    RoleUsersRespVO getRoleUsers(RoleUsersPageReqVO pageReqVO);

    /**
     * 获得角色分页
     *
     * @param reqVO 角色分页查询
     * @return 角色分页结果
     */
    PageResult<RoleRespVO> getRolePage(RolePageReqVO reqVO);

    /**
     * 获得角色列表
     *
     * @param reqVO 列表查询
     * @return 角色列表
     */
    List<RoleDO> getRoleList(RoleExportReqVO reqVO);

    /**
     * 校验角色们是否有效。如下情况，视为无效：
     * 1. 角色编号不存在
     * 2. 角色被禁用
     *
     * @param ids 角色编号数组
     */
    void validRoles(Collection<Long> ids);

    /**
     *
     * @param name
     * @return
     */
    Long getRoleByName(String name);

    Long getInnerRoleIdByCode(String code);

    Long getCustomRoleIdByCode(String code);

    List<RoleDO> getRolesByClientId(Long id);

}
