package com.unicom.swdx.module.system.api.todo;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemCreateReqDTO;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemUpdateReqDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import com.unicom.swdx.module.system.service.todo.TodoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@DubboService(version = ApiConstants.VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Service
@Slf4j
public class TodoItemServiceImpl implements TodoItemServiceApi {

    @Resource
    private TodoService todoService;

    @Override
    @OperateLog(module = "工作台",name = "创建待办事项",type = OperateTypeEnum.CREATE)
    public CommonResult<Boolean> createTodoItem(TodoItemCreateReqDTO reqDTO) {
        log.info(String.valueOf(reqDTO));
        todoService.createTodo(reqDTO);
        return success(true);
    }

    @Override
    @OperateLog(module = "工作台",name = "更新待办事项备注",type = OperateTypeEnum.UPDATE)
    public CommonResult<Boolean> updateTodoItemRemark(TodoItemUpdateReqDTO reqDTO) {
        log.info(String.valueOf(reqDTO));
        todoService.updateTodoRemark(reqDTO);
        return success(true);
    }

    @Override
    @OperateLog(module = "工作台",name = "已办事项",type = OperateTypeEnum.UPDATE)
    public CommonResult<Boolean> updateTodoItem(TodoItemUpdateReqDTO updateReqVO) {
        log.info(String.valueOf(updateReqVO));
        todoService.updateTodo(updateReqVO);
        return success(true);
    }

    @Override
    @OperateLog(module = "工作台",name = "删除事项",type = OperateTypeEnum.DELETE)
    public CommonResult<Boolean> deleteTodoItem(TodoItemUpdateReqDTO reqDTO) {
        log.info(String.valueOf(reqDTO));
        todoService.deleteTodo(reqDTO);
        return null;
    }

    @Override
    @OperateLog(module = "工作台",name = "删除事项 without todo user",type = OperateTypeEnum.DELETE)
    public CommonResult<Boolean> deleteTodoItemWithoutTodoUser(TodoItemUpdateReqDTO reqDTO) {
        log.info(String.valueOf(reqDTO));
        todoService.deleteTodoWithoutTodoUser(reqDTO);
        return null;
    }

    @Override
    @OperateLog(module = "工作台",name = "获取事项数目",type = OperateTypeEnum.GET)
    public CommonResult<Integer> getOfficialdocNum(Long userId) {
        log.info(String.valueOf(userId));
        Integer todoNum = todoService.getTodoNum(userId);
        return success(todoNum);
    }

}
