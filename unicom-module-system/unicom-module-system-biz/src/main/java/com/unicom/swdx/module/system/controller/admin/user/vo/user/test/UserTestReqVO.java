package com.unicom.swdx.module.system.controller.admin.user.vo.user.test;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 用户分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserTestReqVO  {

    @ApiModelProperty(value = "用户账号", example = "unicom", notes = "模糊匹配")
    private String username;

    @ApiModelProperty(value = "姓名", example = "张三", notes = "模糊匹配")
    private String nickname;

    @ApiModelProperty(value = "手机号码", example = "unicom", notes = "模糊匹配")
    private String mobile;

    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @ApiModelProperty(value = "机构ID", example = "25")
    private Long tenantId;

    @ApiModelProperty(value = "组织编号", example = "1024", notes = "同时筛选子组织")
    private Long deptId;

    @ApiModelProperty(value = "组织名称", example = "机构", notes = "同时筛选子组织")
    private String deptName;

    @ApiModelProperty(value = "是否需要筛选授权")
    private Boolean isAuthority;
    @ApiModelProperty(value = "是否展示全部")
    private Boolean isAll;
    @ApiModelProperty(value = "是否需要过滤")
    private Boolean isScreen;

    @ApiModelProperty(value = "负责人部门编号")
    private List<Long> leaderDeptId;

    @ApiModelProperty(value = "是否外部人员管理")
    private Boolean isExternal;

    private static final Integer PAGE_NO = 1;
    private static final Integer PAGE_SIZE = 10;

    @ApiModelProperty(value = "页码，从 1 开始", required = true,example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    private Integer pageNo = PAGE_NO;

    @ApiModelProperty(value = "每页条数，最大值为 100", required = true, example = "10")
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    @Max(value = 100, message = "每页条数最大值为 100")
    private Integer pageSize = PAGE_SIZE;



    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime[] getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime[] createTime) {
        this.createTime = createTime;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Boolean getIsAuthority() {
        return isAuthority;
    }

    public void setIsAuthority(Boolean authority) {
        isAuthority = authority;
    }

    public Boolean getIsAll() {
        return isAll;
    }

    public void setIsAll(Boolean all) {
        isAll = all;
    }

    public Boolean getIsScreen() {
        return isScreen;
    }

    public void setIsScreen(Boolean screen) {
        isScreen = screen;
    }

    public List<Long> getLeaderDeptId() {
        return leaderDeptId;
    }

    public void setLeaderDeptId(List<Long> leaderDeptId) {
        this.leaderDeptId = leaderDeptId;
    }

    public Boolean getIsExternal() {
        return isExternal;
    }

    public void setIsExternal(Boolean external) {
        isExternal = external;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    // 生成toString方法
    @Override
    public String toString() {
        return "YourClass{" +
                "username='" + username + '\'' +
                ", nickname='" + nickname + '\'' +
                ", mobile='" + mobile + '\'' +
                ", status=" + status +
                ", createTime=" + Arrays.toString(createTime) +
                ", tenantId=" + tenantId +
                ", deptId=" + deptId +
                ", deptName='" + deptName + '\'' +
                ", isAuthority=" + isAuthority +
                ", isAll=" + isAll +
                ", isScreen=" + isScreen +
                ", leaderDeptId=" + leaderDeptId +
                ", isExternal=" + isExternal +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                '}';
    }
//    @Override
//    public String toString() {
//        return "YourClass{" +
//                "username='" + username + '\'' +
//                ", nickname='" + nickname + '\'' +
//                ", mobile='" + mobile + '\'' +
//                ", status=" + status +
//                ", createTime=" + Arrays.toString(createTime) +
//                ", tenantId=" + tenantId +
//                ", deptId=" + deptId +
//                ", deptName='" + deptName + '\'' +
//                ", isAuthority=" + isAuthority +
//                ", isAll=" + isAll +
//                ", isScreen=" + isScreen +
//                ", leaderDeptId=" + leaderDeptId +
//                ", isExternal=" + isExternal +
//                ", pageNo=" + pageNo +
//                ", pageSize=" + pageSize +
//                '}';
//    }
//
//    @ApiModelProperty(value = "用户账号", example = "unicom", notes = "模糊匹配")
//    private String username;
//
//    @ApiModelProperty(value = "姓名", example = "张三", notes = "模糊匹配")
//    private String nickname;
//
//    @ApiModelProperty(value = "手机号码", example = "unicom", notes = "模糊匹配")
//    private String mobile;
//
//    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
//    private Integer status;
//
//    @ApiModelProperty(value = "创建时间", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] createTime;
//
//    @ApiModelProperty(value = "机构ID", example = "unicom")
//    private Long tenantId;
//
//    @ApiModelProperty(value = "组织编号", example = "1024", notes = "同时筛选子组织")
//    private Long deptId;
//
//    @ApiModelProperty(value = "组织名称", example = "机构", notes = "同时筛选子组织")
//    private String deptName;
//
//    @ApiModelProperty(value = "是否需要筛选授权")
//    private Boolean isAuthority;
//    @ApiModelProperty(value = "是否展示全部")
//    private Boolean isAll;
//    @ApiModelProperty(value = "是否需要过滤")
//    private Boolean isScreen;
//
//    @ApiModelProperty(value = "负责人部门编号")
//    private List<Long> leaderDeptId;
//
//    @ApiModelProperty(value = "是否外部人员管理")
//    private Boolean isExternal;
//
//    private static final Integer PAGE_NO = 1;
//    private static final Integer PAGE_SIZE = 10;
//
//    @ApiModelProperty(value = "页码，从 1 开始", required = true,example = "1")
//    @NotNull(message = "页码不能为空")
//    @Min(value = 1, message = "页码最小值为 1")
//    private Integer pageNo = PAGE_NO;
//
//    @ApiModelProperty(value = "每页条数，最大值为 100", required = true, example = "10")
//    @NotNull(message = "每页条数不能为空")
//    @Min(value = 1, message = "每页条数最小值为 1")
//    @Max(value = 100, message = "每页条数最大值为 100")
//    private Integer pageSize = PAGE_SIZE;
//
//
//
//    public String getUsername() {
//        return username;
//    }
//
//    public void setUsername(String username) {
//        this.username = username;
//    }
//
//    public String getNickname() {
//        return nickname;
//    }
//
//    public void setNickname(String nickname) {
//        this.nickname = nickname;
//    }
//
//    public String getMobile() {
//        return mobile;
//    }
//
//    public void setMobile(String mobile) {
//        this.mobile = mobile;
//    }
//
//    public Integer getStatus() {
//        return status;
//    }
//
//    public void setStatus(Integer status) {
//        this.status = status;
//    }
//
//    public LocalDateTime[] getCreateTime() {
//        return createTime;
//    }
//
//    public void setCreateTime(LocalDateTime[] createTime) {
//        this.createTime = createTime;
//    }
//
//    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }
//
//    public Long getDeptId() {
//        return deptId;
//    }
//
//    public void setDeptId(Long deptId) {
//        this.deptId = deptId;
//    }
//
//    public String getDeptName() {
//        return deptName;
//    }
//
//    public void setDeptName(String deptName) {
//        this.deptName = deptName;
//    }
//
//    public Boolean getIsAuthority() {
//        return isAuthority;
//    }
//
//    public void setIsAuthority(Boolean authority) {
//        isAuthority = authority;
//    }
//
//    public Boolean getIsAll() {
//        return isAll;
//    }
//
//    public void setIsAll(Boolean all) {
//        isAll = all;
//    }
//
//    public Boolean getIsScreen() {
//        return isScreen;
//    }
//
//    public void setIsScreen(Boolean screen) {
//        isScreen = screen;
//    }
//
//    public List<Long> getLeaderDeptId() {
//        return leaderDeptId;
//    }
//
//    public void setLeaderDeptId(List<Long> leaderDeptId) {
//        this.leaderDeptId = leaderDeptId;
//    }
//
//    public Boolean getIsExternal() {
//        return isExternal;
//    }
//
//    public void setIsExternal(Boolean external) {
//        isExternal = external;
//    }
//
//    public Integer getPageNo() {
//        return pageNo;
//    }
//
//    public void setPageNo(Integer pageNo) {
//        this.pageNo = pageNo;
//    }
//
//    public Integer getPageSize() {
//        return pageSize;
//    }
//
//    public void setPageSize(Integer pageSize) {
//        this.pageSize = pageSize;
//    }
//
//    // 生成toString方法
//    @Override
//    public String toString() {
//        return "YourClass{" +
//                "username='" + username + '\'' +
//                ", nickname='" + nickname + '\'' +
//                ", mobile='" + mobile + '\'' +
//                ", status=" + status +
//                ", createTime=" + Arrays.toString(createTime) +
//                ", tenantId=" + tenantId +
//                ", deptId=" + deptId +
//                ", deptName='" + deptName + '\'' +
//                ", isAuthority=" + isAuthority +
//                ", isAll=" + isAll +
//                ", isScreen=" + isScreen +
//                ", leaderDeptId=" + leaderDeptId +
//                ", isExternal=" + isExternal +
//                ", pageNo=" + pageNo +
//                ", pageSize=" + pageSize +
//                '}';
//    }
}
