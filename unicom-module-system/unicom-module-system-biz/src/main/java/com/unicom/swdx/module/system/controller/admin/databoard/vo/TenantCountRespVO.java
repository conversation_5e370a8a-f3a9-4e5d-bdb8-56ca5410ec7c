package com.unicom.swdx.module.system.controller.admin.databoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("管理后台 - 数据看板机构统计 Response VO")
public class TenantCountRespVO {

    @ApiModelProperty(value = "机构总数")
    private Long allTenant;

    @ApiModelProperty(value = "今日新增机构")
    private Long newTenant;

    @ApiModelProperty(value = "待审批机构")
    private Long registeringTenant;

}
