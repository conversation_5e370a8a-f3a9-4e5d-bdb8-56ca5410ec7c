package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel("管理后台 - 岗位分配用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PostUsersPageReqVO extends PageParam {
    @ApiModelProperty(value = "岗位id", example = "1", required = true)
    private Long postId;
}
