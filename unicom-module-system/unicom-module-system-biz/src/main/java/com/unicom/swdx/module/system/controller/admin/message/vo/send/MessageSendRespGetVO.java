package com.unicom.swdx.module.system.controller.admin.message.vo.send;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveMobileSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 消息发送 Response VO")
@Data
@ToString(callSuper = true)
public class MessageSendRespGetVO {

    @ApiModelProperty(value = "发送id", required = true, example = "1")
    private Long sendId;

    @ApiModelProperty(value = "是否定时发送")
    private Boolean ifTimedSend;

    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "消息内容")
    private String message;

    @ApiModelProperty(value = "接收人员姓名")
    private String receiver;

    @JsonSerialize(using = SensitiveMobileSerializer.class)
    @ApiModelProperty(value = "接收人员手机号")
    private String phone;


    @ApiModelProperty(value = "短信发送成功")
    private boolean successMessage;

    @ApiModelProperty(value = "短信发送失败")
    private boolean failMessage;

    @ApiModelProperty(value = "是否发送")
    private boolean status;

}
