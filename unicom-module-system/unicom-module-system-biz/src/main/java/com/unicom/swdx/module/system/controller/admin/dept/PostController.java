package com.unicom.swdx.module.system.controller.admin.dept;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.*;
import com.unicom.swdx.module.system.convert.dept.PostConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.service.dept.PostService;
import com.unicom.swdx.module.system.util.ExcelValidator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.POST_IMPORT_OUT_MEMORY;

@Api(tags = "管理后台 - 岗位")
@RestController
@RequestMapping("/system/post")
@Validated
public class PostController {

    @Resource
    private PostService postService;
    @PostMapping("/create")
    @ApiOperation("新增岗位")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:post:create')")
    public CommonResult<Long> createPost(@Valid @RequestBody PostCreateReqVO reqVO) {
        Long postId = postService.createPost(reqVO);
        return success(postId);
    }

    @PostMapping("/update")
    @ApiOperation("修改岗位")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:post:update')")
    public CommonResult<Boolean> updatePost(@Valid @RequestBody PostUpdateReqVO reqVO) {
        postService.updatePost(reqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除岗位")
    @OperateLog(type = DELETE)
    @PreAuthorize("@ss.hasPermission('system:post:delete')")
    public CommonResult<Boolean> deletePost(@RequestParam("id") Long id) {
        postService.deletePost(id);
        return success(true);
    }

    @GetMapping(value = "/get")
    @ApiOperation("获得岗位信息")
    @ApiImplicitParam(name = "id", value = "岗位编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<PostRespVO> getPost(@RequestParam("id") Long id) {
        return success(PostConvert.INSTANCE.convert(postService.getPost(id)));
    }

    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获取岗位精简信息列表", notes = "只包含被开启的岗位，主要用于前端的下拉选项")
    @ApiImplicitParam(name = "deptId", value = "所选组织ID",  example = "1024", dataTypeClass = Long.class)
    public CommonResult<List<PostSimpleRespVO>> getSimplePosts(@RequestParam(value = "deptId",required = false) Long deptId) {
        // 获得岗位列表，只要开启状态的
        List<PostDO> list = postService.getPosts(null, Collections.singleton(CommonStatusEnum.ENABLE.getStatus()),deptId);
        // 排序后，返回给前端
        list.sort((Comparator.comparing(PostDO::getSort)).thenComparing(PostDO::getId));
        return success(PostConvert.INSTANCE.convertList02(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得岗位分页列表")
    @OperateLog(type = GET)
    public CommonResult<PageResult<PostRespVO>> getPostPage(@Validated PostPageReqVO reqVO) {
        return success(postService.getPostPage(reqVO));
    }

    @GetMapping("/export")
    @ApiOperation("导出岗位列表")
    @PreAuthorize("@ss.hasPermission('system:post:export')")
    @OperateLog(type = EXPORT)
    public void export(HttpServletResponse response, @Validated PostExportReqVO reqVO) throws IOException {
        List<PostExcelVO> posts = postService.getPosts(reqVO);
        // 输出
        ExcelUtils.write(response, "岗位信息.xls", "岗位列表", PostExcelVO.class, posts);
    }
    @GetMapping("/get-import-template")
    @ApiOperation("获得导入岗位模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        ExcelUtils.write(response, "岗位导入模板.xls", "岗位列表", PostImportExcelVO.class,Collections.emptyList());
    }

    @PostMapping("/import")
    @ApiOperation("导入岗位")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
//    @PreAuthorize("@ss.hasPermission('system:user:import')")
    public CommonResult<PostImportRespVO> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        long fileSizeInBytes = file.getSize();
        long maxSizeInBytes = 16 * 1024 * 1024; // 16MB
        if (fileSizeInBytes > maxSizeInBytes) {
            throw exception(POST_IMPORT_OUT_MEMORY);
        }
        List<PostImportExcelVO> list = ExcelUtils.read(file, PostImportExcelVO.class);
        ExcelValidator.valid(list,1);
        return success(postService.importPost(list));
    }

    @GetMapping("/list-users")
    @ApiOperation(value = "获取岗位用户信息列表", notes = "只包含被开启的岗位，主要用于前端的下拉选项")
    public CommonResult<List<PostSimpleUserRespVO>> getPostListUses(@RequestParam(value = "processInstanceId",required = false) String processInstanceId) {
        return success(postService.getPosts(processInstanceId));
    }

    @GetMapping("/get-post-users")
    @ApiOperation("获得岗位分配用户信息")
    public CommonResult<PostUsersRespVO> getPostUsers(PostUsersPageReqVO pageReqVO) {
        return success(postService.getPostUsers(pageReqVO));
    }

}
