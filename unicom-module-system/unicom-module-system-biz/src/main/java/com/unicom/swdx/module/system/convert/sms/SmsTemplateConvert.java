package com.unicom.swdx.module.system.convert.sms;

import com.unicom.swdx.module.system.controller.admin.sms.vo.template.SmsTemplateCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.sms.vo.template.SmsTemplateExcelVO;
import com.unicom.swdx.module.system.controller.admin.sms.vo.template.SmsTemplateRespVO;
import com.unicom.swdx.module.system.controller.admin.sms.vo.template.SmsTemplateUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.sms.SmsTemplateDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SmsTemplateConvert {

    SmsTemplateConvert INSTANCE = Mappers.getMapper(SmsTemplateConvert.class);

    SmsTemplateDO convert(SmsTemplateCreateReqVO bean);

    SmsTemplateDO convert(SmsTemplateUpdateReqVO bean);

    SmsTemplateRespVO convert(SmsTemplateDO bean);

    List<SmsTemplateRespVO> convertList(List<SmsTemplateDO> list);

    PageResult<SmsTemplateRespVO> convertPage(PageResult<SmsTemplateDO> page);

    List<SmsTemplateExcelVO> convertList02(List<SmsTemplateDO> list);

}
