package com.unicom.swdx.module.system.dal.dataobject.message;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 租户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_message_template", autoResultMap = true)
@KeySequence("system_message_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageDO extends BaseDO {

    /**
     * 编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long systemId;

    private String name;

    private String content;

    private String remark;

    /**
     * 租户ID
     */
    private Long tenantId;

}
