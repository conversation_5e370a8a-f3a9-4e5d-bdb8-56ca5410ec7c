package com.unicom.swdx.module.system.dal.mysql.shortcut;


import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.dal.dataobject.shortcut.ShortcutDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 快捷入口 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ShortcutMapper extends BaseMapperX<ShortcutDO> {

    default List<ShortcutDO> selectList() {
        return selectList(new LambdaQueryWrapperX<ShortcutDO>()
                .orderByAsc(ShortcutDO::getSort));
    }

    default void deleteByMenuIds(Collection<Long> deleteIds) {
        delete(new LambdaQueryWrapperX<ShortcutDO>().inIfPresent(ShortcutDO::getMenuId,deleteIds));
    }
}
