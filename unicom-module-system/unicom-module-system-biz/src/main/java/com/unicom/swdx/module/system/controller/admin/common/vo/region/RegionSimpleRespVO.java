package com.unicom.swdx.module.system.controller.admin.common.vo.region;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("管理后台 - 地区 Response VO")
@Data
@ToString(callSuper = true)
public class RegionSimpleRespVO {

    @ApiModelProperty(value = "地区id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "上级id")
    private Long parentId;

}
