package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Collection;

@ApiModel("业务中台 - 快捷入口用户关联更新 Request VO")
@Data
@ToString(callSuper = true)
public class ShortcutUserUpdateReqVO {

    @ApiModelProperty("快捷入口id")
    Collection<Long> ids;

}
