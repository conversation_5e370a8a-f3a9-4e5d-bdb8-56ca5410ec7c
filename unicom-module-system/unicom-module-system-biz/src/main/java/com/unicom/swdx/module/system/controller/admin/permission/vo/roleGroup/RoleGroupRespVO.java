package com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

@ApiModel("管理后台 - 角色组信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RoleGroupRespVO extends RoleGroupBaseVO{

    @ApiModelProperty(value = "角色组编号", example = "1")
    private Long id;

    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "角色", example = "[1,2]")
    private Set<Long> roleIds;

    @ApiModelProperty(value = "创建时间", example = "时间戳格式")
    private LocalDateTime createTime;
}
