package com.unicom.swdx.module.system.controller.admin.databoard;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.controller.admin.databoard.vo.*;
import com.unicom.swdx.module.system.service.databoard.DataboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 数据看板")
@RestController
@RequestMapping("/system/data-board")
public class DataboardControler {

    @Resource
    private DataboardService databoardService;


    @GetMapping("/get-tenant-counts")
    @ApiOperation("获得机构计数")
    public CommonResult<TenantCountRespVO> getTenantCount() {
        return success(databoardService.getTenantCount());
    }

    @GetMapping("/get-users-counts")
    @ApiOperation("获得用户计数")
    public CommonResult<UserCountRespVO> getUsersCount() {
        return success(databoardService.getUserCount());
    }


    @GetMapping("/get-last-6month-tenant")
    @ApiOperation("近六个月机构入驻趋势")
    public CommonResult<List<LastSixMonthTenantRespVO>> getLast6MonthTenant() {
        return success(databoardService.getLastSixMonthTenant());
    }

    @GetMapping("/get-last-6month-users")
    @ApiOperation("近六个月用户注册趋势")
    public CommonResult<List<LastSixMonthUsersRespVO>> getLast6MonthUsers() {
        return success(databoardService.getLastSixMonthUsers());
    }

    @GetMapping("/get-real-name-users-count")
    @ApiOperation("实名用户统计")
    public CommonResult<RealNameUsersCountRespVO> getRealNameUsersCount() {
        return success(databoardService.getRealNameUsersCount());
    }

    @GetMapping("/get-users-attestation-type-count")
    @ApiOperation("用户认证类型统计")
    public CommonResult<Map<String,Object>> getUsersAttestationTypeCount() {

        List<UsersAttestationTypeCountRespVO> list = databoardService.getUserAttestationTypeCount();
        Long allNum = 0L;
        for(UsersAttestationTypeCountRespVO u : list)
        {
            allNum += u.getNum();
        }

        Map<String,Object> result = new HashMap<>();
        result.put("countList",list);
        result.put("allNum",allNum);

        return success(result);
    }

}
