package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 快捷入口 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ShortcutBaseVO {

    @ApiModelProperty(value = "所属子系统id")
    private Integer clientId;

    @ApiModelProperty(value = "快捷入口名称", required = true)
    @NotNull(message = "快捷入口名称不能为空")
    private String name;

    @ApiModelProperty(value = "快捷入口链接")
    private String linkUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "图标")
    private String svgIcon;

}
