package com.unicom.swdx.module.system.dal.dataobject.yjs;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("old_yzpersonnalinfor_base")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OldYzPersonnalInforDO {


    private String name;
    private String sex;
    private String nation;
    private String mobilePhone;
    private String birthday;
    private String idCode;
    private String nativePlace;
    private String staffRank;
    private String education;
    private String polity;
    private String staffType;
    private String post;
    private String employeeId;
    private String deptName;
    private String deptId;
    private String zyjsryzylb;
    private String status;


    private Long userId;


}
