package com.unicom.swdx.module.system.controller.admin.home.shortcut;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutRespVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserUpdateReqVO;
import com.unicom.swdx.module.system.service.shortcut.ShortcutUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.OTHER;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Api(tags = "业务中台 - 快捷入口用户关联")
@RestController
@RequestMapping("/system/shortcut-user")
@Validated
public class ShortcutUserController {

    @Resource
    private ShortcutUserService shortcutUserService;

    @PostMapping("/update")
    @ApiOperation("更新快捷入口用户关联")
    @OperateLog(type = UPDATE)
//    @PreAuthorize("@ss.hasPermission('midoffice:shortcut-user:update')")
    public CommonResult<Boolean> updateShortcutUser(@RequestBody ShortcutUserUpdateReqVO updateReqVO) {
        shortcutUserService.updateShortcutUser(getLoginUserId(),updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("根据用户获得快捷入口")
    public CommonResult<List<ShortcutRespVO>> getShortcutUser(@RequestParam(required = false) boolean isDefault) {
        List<ShortcutRespVO> shortcutUser = shortcutUserService.getShortcutUser(getLoginUserId(),isDefault);
        return success(shortcutUser);
    }

}
