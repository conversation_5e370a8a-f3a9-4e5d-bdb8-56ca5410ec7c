package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveIDCardSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 变更记录分页 Response VO")
@Data
@ToString(callSuper = true)
public class TenantChangePageRespVO {


    @ApiModelProperty(value = "申请id",required = true)
    private Long id;

    @ApiModelProperty(value = "关联机构id",required = true)
    private Long linkedTenantId;

    @ApiModelProperty(value = "机构全称", required = true)
    private String tenantName;

    @ApiModelProperty(value = "认证类型", required = true, notes = "0 注册 1 变更")
    private Integer approvalType;

    @ApiModelProperty(value = "变更内容", required = true)
    private String changeContent;

    @ApiModelProperty(value = "申请人",required = true)
    private String applicant;

    @ApiModelProperty(value = "申请时间",required = true)
    private LocalDateTime approvalTime;

    @ApiModelProperty(value = "审批人",required = true)
    private String approver;

    @ApiModelProperty(value = "审批时间",required = true)
    private LocalDateTime examineTime;

    @ApiModelProperty(value = "审批状态",required = true, notes = "0 未审批 1 通过 2 驳回")
    private Integer approvalStatus;

    @ApiModelProperty(value = "机构类型", required = true, example = "1")
    private Integer tenantType;

    @ApiModelProperty(value = "机构级别", required = false, example = "1")
    private Integer tenantLevel;

    @ApiModelProperty(value = "范围", example = "[10000216,10002038]")
    private List<Long> levelRange;

    @ApiModelProperty(value = "业务指导单位")
    private Integer guideTenantId;

    @ApiModelProperty(value = "单位类型", required = true, example = "1")
    private Integer companyType;

    @ApiModelProperty(value = "统一社会信用代码",required = true,example = "sport5454514")
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "注册地区划",required = false)
    private List<Long> registerRegion;

    @ApiModelProperty(value = "注册地地址",required = false,example = "湖南长沙")
    private String registerAddress;

    @ApiModelProperty(value = "所在地区划",required = true)
    private List<Long> locationRegion;


    @ApiModelProperty(value = "所在地地址",required = true,example = "湖南长沙")
    private String locationAddress;


    @ApiModelProperty(value = "法定代表人姓名",required = true,example = "张三")
    private String legalRepresentativeName;


    @ApiModelProperty(value = "法人代表身份证号码",required = true,example = "******************")
    @JsonSerialize(using = SensitiveIDCardSerializer.class)
    private String legalRepresentativeIdCard;

    @ApiModelProperty(value = "机构用户类型", required = true)
    private List<Long> instUserType;

    @ApiModelProperty(value = "营业执照", required = true, example = "funsprot.club")
    private String businessLicenseUrl;

    @ApiModelProperty(value = "机构管理人姓名",required = true,example = "张三")
    private String contactNickname;

    @ApiModelProperty(value = "机构管理员手机号码（联系手机）",required = true,example = "***********")
    private String contactMobile;

    @ApiModelProperty(value = "用户名（原联系人）", required = true,example = "skyline")
    private String contactName;

    @ApiModelProperty(value = "审批意见",required = false)
    private String remark;

    @ApiModelProperty(value = "过期时间",required = false)
    private LocalDateTime expireTime;
}
