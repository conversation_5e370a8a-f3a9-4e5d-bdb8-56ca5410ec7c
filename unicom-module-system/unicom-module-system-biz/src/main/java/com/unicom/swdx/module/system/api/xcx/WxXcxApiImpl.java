package com.unicom.swdx.module.system.api.xcx;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.system.api.xcx.dto.WxXcxSignQRCodeRespVO;
import com.unicom.swdx.module.system.wx.xcx.WxXcxApiService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class WxXcxApiImpl implements WxXcxApi{

    private final String SIGN_REDIS_KEY = "xcx:sign:url:";

    private final String USER_ID = "user_id";

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private WxXcxApiService xcxApiService;

    @Override
    public CommonResult<WxXcxSignQRCodeRespVO> getSignQRCode() {
        WxXcxSignQRCodeRespVO respVO = xcxApiService.getSignQRCodeBase64();
        // uniqueCode绑定用户
        redisUtil.hSet(SIGN_REDIS_KEY + respVO.getUniqueCode(), USER_ID, getLoginUserId(), 3600);
        return CommonResult.success(respVO);
    }
}
