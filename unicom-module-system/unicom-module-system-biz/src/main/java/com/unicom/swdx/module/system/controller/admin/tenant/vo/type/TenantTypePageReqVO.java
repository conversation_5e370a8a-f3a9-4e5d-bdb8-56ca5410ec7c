package com.unicom.swdx.module.system.controller.admin.tenant.vo.type;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
@ApiModel("管理后台 - 机构用户类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantTypePageReqVO extends PageParam {

    @ApiModelProperty(value = "机构用户类型名称")
    @Length(min = 0,max = 30,message = "机构名称不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "状态")
    private Boolean status;

}
