package com.unicom.swdx.module.system.mq.producer;

//import org.springframework.cloud.stream.function.StreamBridge;

import org.springframework.kafka.core.KafkaTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/6 16:39
 **/
public class AbstractProducer {

    @Resource
    protected KafkaTemplate<Object, Object> kafkaTemplate;

//    @Resource
//    protected StreamBridge streamBridge;

}
