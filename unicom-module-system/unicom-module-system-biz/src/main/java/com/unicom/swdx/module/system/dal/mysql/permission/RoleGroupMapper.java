package com.unicom.swdx.module.system.dal.mysql.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleExportReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RolePageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupPageReqVO;
import com.unicom.swdx.module.system.convert.permission.RoleConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Mapper
public interface RoleGroupMapper extends BaseMapperX<RoleGroupDO> {
    RoleGroupDO selectByNameAndApplication(@Param("name") String name,@Param("tenantId") Long tenantId);

    default PageResult<RoleGroupDO> selectRoleGroupPage(RoleGroupPageReqVO reqVO, Collection<Long> tenantIds, Long tenantId) {
        LambdaQueryWrapperX<RoleGroupDO> query = getQuery(reqVO, tenantIds, tenantId);
        return selectPage(reqVO, query);
    }

    default LambdaQueryWrapperX<RoleGroupDO> getQuery(RoleGroupPageReqVO reqVO, Collection<Long> tenantIds, Long tenantId) {
        LambdaQueryWrapperX<RoleGroupDO> query = new LambdaQueryWrapperX<RoleGroupDO>()
                .likeIfPresent(RoleGroupDO::getName, reqVO.getName())
                .eqIfPresent(RoleGroupDO::getStatus, reqVO.getStatus())
                .orderByAsc(RoleGroupDO::getSort)
                .orderByDesc(RoleGroupDO::getId);
        if (Objects.nonNull(tenantId)) {
            // 除了当前机构自定义的角色，还包括机构的角色
            query.and(q -> q.eq(RoleGroupDO::getTenantId,tenantId));
        }
        if (StrUtil.isNotBlank(reqVO.getTenantName())) {
            // 防止报错，插入一条不存在数据去查
            ArrayList<Long> list = CollUtil.newArrayList(-1L);
            list.addAll(tenantIds);
            query.in(RoleGroupDO::getTenantId,list);
        }
        return query;
    }
}
