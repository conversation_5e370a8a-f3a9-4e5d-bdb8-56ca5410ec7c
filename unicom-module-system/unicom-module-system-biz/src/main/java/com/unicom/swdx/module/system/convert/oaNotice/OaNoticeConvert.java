package com.unicom.swdx.module.system.convert.oaNotice;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticeRespVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OaNoticeConvert {

    OaNoticeConvert INSTANCE = Mappers.getMapper(OaNoticeConvert.class);

    PageResult<OaNoticeRespVO> convertPage(PageResult<OaNoticeDO> page);

    List<NoticeRespVO> convertList(List<OaNoticeDO> list);

    OaNoticeRespVO convert(OaNoticeDO bean);

    OaNoticeDO convert(OaNoticeUpdateReqVO bean);

    OaNoticeDO convert(OaNoticeCreateReqVO bean);

}
