package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.module.system.enums.common.SexEnum;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/5/17 14:21
 **/
@Data
public class UserInfoVo {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 备注
     */
    private String remark;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 岗位编号数组
     */
    private Set<Long> postIds;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 用户性别
     *
     * 枚举类 {@link SexEnum}
     */
    private Integer sex;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 微信小程序openid
     */
    private String wxxcxOpenid;

    /**
     * 微信unionid
     */
    private String wxUnionid;

    //全局子系统id

    private String othersystemid;


}
