package com.unicom.swdx.module.system.dal.dataobject.calendarwnl;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.module.system.enums.common.HolidayTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 万年历 DO
 *
 * <AUTHOR>
 */
@TableName("system_calendar_wnl")
@KeySequence("system_calendar_wnl_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendarWnlDO {

    /**
     * 日期
     */
    @TableId
    private LocalDate gregorianDate;
    /**
     * 公历年
     */
    private Integer gregorianYear;
    /**
     * 公历月
     */
    private Integer gregorianMonth;
    /**
     * 公历日
     */
    private Integer gregorianDay;
    /**
     * 星期
     */
    private String weekDay;
    /**
     * 阴历年
     */
    private Integer lunarYear;
    /**
     * 阴历月
     */
    private String lunarMonth;
    /**
     * 阴历日
     */
    private String lunarDay;
    /**
     * 生肖
     */
    private String zodiac;
    /**
     * 闰月
     */
    private Integer leapMonth;
    /**
     * 年干支
     */
    private String yearBranch;
    /**
     * 月干支
     */
    private String monthBranch;
    /**
     * 日干支
     */
    private String dayBranch;
    /**
     * 节气
     */
    private String solarTerm;
    /**
     * 节气时间
     */
    private String solarTermTime;
    /**
     * 公历节日
     */
    private String gregorianFestival;
    /**
     * 农历节日
     */
    private String lunarFestival;
    /**
     * 特殊节日
     */
    private String specialFestivals;
    /**
     * 节假日类型，enum(0, 1, 2, 3),分别表示 工作日、周末、节日、调休
     * 枚举 {@link HolidayTypeEnum}
     */
    private Integer holidayType;
    /**
     * 节假日名称
     */
    private String holidayName;

}
