package com.unicom.swdx.module.system.controller.admin.subsystem;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.controller.admin.subsystem.vo.ClientRespVO;
import com.unicom.swdx.module.system.service.subsystem.SubsystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Api(tags = "业务中台 - 子系统")
@RestController
@RequestMapping("/system/subsystem")
@Validated
public class SubsystemController {

    @Resource
    private SubsystemService subsystemService;

    @GetMapping("/list")
    @ApiOperation("获得子系统列表")
    public CommonResult<List<ClientRespVO>> getSubsystemList() {
        List<Integer> visibility = new ArrayList<>(Arrays.asList(0, 1));
        List<ClientRespVO> subsystemList = subsystemService.getSubsystemList(getLoginUserId(),visibility);
        return success(subsystemList);
    }

}
