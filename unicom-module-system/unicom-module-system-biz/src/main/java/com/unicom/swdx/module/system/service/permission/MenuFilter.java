package com.unicom.swdx.module.system.service.permission;

import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class MenuFilter {

    public static List<MenuDO> filterMenu(List<MenuDO> allNodes, List<MenuDO> selectedLeafNodes) {
        Map<Long, List<MenuDO>> parentToChildrenMap = buildParentToChildrenMap(allNodes);
        Map<Long, MenuDO> idToNodeMap = buildIdToNodeMap(allNodes);

        List<MenuDO> filteredNodes = new ArrayList<>();
        for (MenuDO leafNode : selectedLeafNodes) {
            addNodeAndAncestors(leafNode, filteredNodes, parentToChildrenMap, idToNodeMap);
        }
        return filteredNodes;
    }

    private static Map<Long, List<MenuDO>> buildParentToChildrenMap(List<MenuDO> allNodes) {
        Map<Long, List<MenuDO>> parentToChildrenMap = new HashMap<>();
        for (MenuDO node : allNodes) {
            parentToChildrenMap.computeIfAbsent(node.getParentId(), k -> new ArrayList<>()).add(node);
        }
        return parentToChildrenMap;
    }

    private static Map<Long, MenuDO> buildIdToNodeMap(List<MenuDO> allNodes) {
        Map<Long, MenuDO> idToNodeMap = new HashMap<>();
        for (MenuDO node : allNodes) {
            idToNodeMap.put(node.getId(), node);
        }
        return idToNodeMap;
    }

    private static void addNodeAndAncestors(MenuDO node, List<MenuDO> filteredNodes,
                                            Map<Long, List<MenuDO>> parentToChildrenMap,
                                            Map<Long, MenuDO> idToNodeMap) {
        // 如果节点已经在filteredNodes中，直接返回
        if (filteredNodes.contains(node)) {
            return;
        }

        // 添加当前节点
        filteredNodes.add(node);

        // 递归添加父节点
        Long parentId = node.getParentId();
        if (parentId != null) {
            MenuDO parentNode = idToNodeMap.get(parentId);
            if (parentNode != null) {
                addNodeAndAncestors(parentNode, filteredNodes, parentToChildrenMap, idToNodeMap);
            }
        }
    }
}

