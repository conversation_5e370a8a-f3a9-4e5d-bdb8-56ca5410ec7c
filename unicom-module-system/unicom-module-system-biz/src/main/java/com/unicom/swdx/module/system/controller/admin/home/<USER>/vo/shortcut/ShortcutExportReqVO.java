package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "业务中台 - 快捷入口 Excel 导出 Request VO", description = "参数和 ShortcutPageReqVO 是一致的")
@Data
public class ShortcutExportReqVO {

    @ApiModelProperty(value = "所属子系统id")
    private Integer subsystemId;

    @ApiModelProperty(value = "快捷入口名称")
    private String name;

    @ApiModelProperty(value = "快捷入口链接")
    private String linkUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}
