package com.unicom.swdx.module.system.dal.dataobject.message;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 租户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_timed_task", autoResultMap = true)
@KeySequence("system_timed_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimedTaskDO extends BaseDO {

    /**
     * 编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long sendId;

    private String message;

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> phone;

    private Date sendTime;

    private Boolean status;
    private Boolean successMessage;
    private Boolean failMessage;
    private Boolean successNotice;
    private Boolean failNotice;
    /**
     * 发送类型id 1=短信 2=通告
     */
    private Integer type;
    private String title;
    private String receiver;

}
