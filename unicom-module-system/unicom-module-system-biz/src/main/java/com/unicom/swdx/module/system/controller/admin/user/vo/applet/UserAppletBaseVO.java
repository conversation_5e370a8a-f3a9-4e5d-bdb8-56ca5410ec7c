package com.unicom.swdx.module.system.controller.admin.user.vo.applet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class UserAppletBaseVO {

    @ApiModelProperty(value = "资源类型编码，第一位0表示全部，1表示部门，2表示个人；第二位0表示所有学年，1表示单个学年；第三位0表示不限制学期，1表示单个学期；第四位0表示不限制起止时间，1表示限制起止时间", example = "1100")
    private String res_type;

    @ApiModelProperty(value = "学年，当resType第二位为1时有效", example = "2023")
    private String year;

    @ApiModelProperty(value = "学期（1表示上学期，2表示下学期），当resType第三位为1时有效", example = "1")
    private String term;

    @ApiModelProperty(value = "部门ID，当resType第一位为1时有效", example = "D001")
    private String dept_id;

    @ApiModelProperty(value = "师资ID，当resType为特定值时可能有效，具体取决于业务逻辑", example = "T001")
    private String sz_id;

    @ApiModelProperty(value = "查询的起始日期，当resType第四位为1时有效，格式为yyyy-MM-dd", example = "2023-01-01")
    private String s_date;

    @ApiModelProperty(value = "查询的结束日期，当resType第四位为1时有效，格式为yyyy-MM-dd", example = "2023-12-31")
    private String e_date;

    @ApiModelProperty(value = "分页查询的偏移量，计算方式为（页数-1）*每页数量", example = "0")
    private int offs;

    @ApiModelProperty(value = "课程类型0必修课1选修课")
    private String course_type;

}
