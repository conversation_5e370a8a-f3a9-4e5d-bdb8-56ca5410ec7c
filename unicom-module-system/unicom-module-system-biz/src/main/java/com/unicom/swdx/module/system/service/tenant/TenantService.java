package com.unicom.swdx.module.system.service.tenant;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.tenant.core.context.TenantContextHolder;
import com.unicom.swdx.module.system.api.tenant.dto.TenantInfoRespDTO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserPageAuthorityVO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.service.tenant.handler.TenantInfoHandler;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantService extends IService<TenantDO> {

    /**
     * 初始化机构缓存信息
     */
    void init();

    /**
     * 创建机构
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenant(@Valid TenantCreateReqVO createReqVO);

    /**
     * 更新机构信息
     *
     * @param updateReqVO 更新信息
     */
    void updateTenant(@Valid TenantUpdateReqVO updateReqVO);

    /**
     * 更新机构状态
     * @param updateReqVO 更新信息
     */
    void updateTenantStatus(TenantStatusUpdateReqVO updateReqVO);

    /**
     * 删除机构
     *
     * @param id 编号
     */
    void deleteTenant(Long id);

    /**
     * 获得机构
     *
     * @param id 编号
     * @return 机构
     */
    TenantDO getTenant(Long id);

    /**
     * 根据机构id从缓存中获得机构信息
     *
     * @param id 机构id
     * @return 机构信息
     */
    TenantDO getTenantFromCache(Long id);


    List<TenantDO>  getTenantsFromCache() ;

    /**
     * 获得机构分页
     *
     * @param pageReqVO 分页查询
     * @return 机构分页
     */
    PageResult<TenantDO> getTenantPage(TenantPageReqVO pageReqVO);

    /**
     * 获得机构列表, 用于 Excel 导出
     *
     * @param reqVO 查询条件
     * @return 机构列表
     */
    List<TenantDO> getTenantExcelList(TenantPageReqVO reqVO);

    /**
     * 判断机构下是否存在除机构管理员外的用户
     * @param id 机构
     * @return 是否
     */
    Boolean checkUserNum(Long id);

    /**
     * 获取机构下组织数量
     * @param id 机构id
     * @return 组织数量
     */
    Long getDeptNum(Long id);

    /**
     * 进行机构的信息处理逻辑
     * 其中，机构编号从 {@link TenantContextHolder} 上下文中获取
     * （勿删）
     * @param handler 处理器
     */
    void handleTenantInfo(TenantInfoHandler handler);

    /**
     * 获得所有机构id
     *
     * @return 机构编号数组
     */
    List<Long> getTenantIds();

    /**
     * 校验机构是否合法
     *
     * @param id 机构编号
     */
    void validTenant(Long id);

    /**
     * 根据机构管理员id获取机构信息
     * @param userId 用户id
     * @return 机构
     */
    TenantDO getTenantByContactUserId(Long userId);


    /**
     * 根据机构管理员用户id获取机构信息
     * @param contactUserIds 机构管理员用户id集合
     * @return 机构信息集合
     */
    List<TenantInfoRespDTO> getTenantByContactUserIds(Collection<Long> contactUserIds);

    TenantDO getTenantByUserId(Long userId);

    /**
     * 从缓存中校验用户是否机构管理员
     * @param userId 用户id
     * @return 是否机构管理员
     */
    boolean isTenantAdmin(Long userId);

    /**
     * 根据机构用户类型获取该机构用户类型绑定的机构集
     * @param tenantTypeId 机构用户类型
     * @return 机构集
     */
    List<TenantDO> getListByTenantType(Long tenantTypeId);

    /**
     * 是否系统机构
     * @param id 机构id
     * @return 是否
     */
    boolean isSystemTenant(Long id);

    /**
     * 根据机构id获取机构绑定的机构用户类型绑定的角色id集合
     * @param tenantId 机构id
     * @return 角色id集合
     */
    Set<Long> getAllRoleIdsByTenantId(Long tenantId);


    // todo 待删除
    /**
     * 获得名字对应的机构
     *
     * @param name 组户名
     * @return 机构
     */
    TenantDO getTenantByName(String name);

    /**
     * 根据id集获取机构信息
     * @param ids id集
     * @return 机构信息
     */
    List<TenantDO> getTenantListByIds(Collection<Long> ids);

    void saveTenantIdToRedis(Long tenantId);

    TenantDO getTenantByCode(String tenantCode);

    List<Long> getTenants();

    /**
     * 更新机构考勤规则
     * @param updateReqVO 更新信息
     * @return 是否更新成功
     */
    Boolean updateTenantCheckRule(TenantCheckRuleUpdateReqVO updateReqVO);

    /**
     * 获取机构考勤规则
     * @param tenantId 机构id
     * @return 是否开启
     */
    Boolean getTenantCheckRule(Long tenantId);

    /**
     * 更新机构考勤保护规则
     * @param updateReqVO 更新信息
     * @return 是否更新成功
     */
    Boolean updateAttendanceProtection(TenantAttendanceProtectionUpdateReqVO updateReqVO);
}
