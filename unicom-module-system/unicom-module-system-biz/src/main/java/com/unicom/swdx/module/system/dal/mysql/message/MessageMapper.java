package com.unicom.swdx.module.system.dal.mysql.message;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessagePageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

@Mapper
public interface MessageMapper extends BaseMapperX<MessageDO> {
    @Select("        SELECT name FROM system_message_template " +
            "        where deleted = 0 and system_id = #{systemId};")
    List<String> selectBySystemId(Long systemId);
    default PageResult<MessageDO> selectPage(MessagePageReqVO reqVO,Boolean admin,Boolean hasSuperAdmin) {
        LambdaQueryWrapperX<MessageDO> query = new LambdaQueryWrapperX<MessageDO>()
                .likeIfPresent(MessageDO::getName,reqVO.getName())
                .likeIfPresent(MessageDO::getContent,reqVO.getContent())
                .eqIfPresent(MessageDO::getSystemId,reqVO.getSystemId())
                .orderByDesc(MessageDO::getId);
        if(!hasSuperAdmin){
            query.eqIfPresent(MessageDO::getTenantId,getTenantId());
        }
        return selectPage(reqVO, query);
    }

}
