package com.unicom.swdx.module.system.controller.admin.tenant.vo.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("管理后台 - 机构用户类型 Response VO")
@Data
@ToString(callSuper = true)
public class TenantTypeSimpleRespVO {

    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    @ApiModelProperty(value = "类型名称", required = true)
    private String name;

}
