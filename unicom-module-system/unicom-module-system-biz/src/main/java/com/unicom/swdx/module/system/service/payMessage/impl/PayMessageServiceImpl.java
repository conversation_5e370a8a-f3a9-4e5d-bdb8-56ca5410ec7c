package com.unicom.swdx.module.system.service.payMessage.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.DwResult;
import com.unicom.swdx.framework.common.pojo.OneCardResult;
import com.unicom.swdx.framework.common.util.http.OkHttpTrustUtil;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.dal.dataobject.payMessage.PayMessageDO;
import com.unicom.swdx.module.system.dal.mysql.payMessage.PayMessageMapper;
import com.unicom.swdx.module.system.service.payMessage.PayMessageService;
import com.unicom.swdx.framework.common.util.http.UnzippingInterceptorUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class PayMessageServiceImpl extends ServiceImpl<PayMessageMapper, PayMessageDO> implements PayMessageService {

    @Resource
    private StringRedisTemplate sRedisTemplate;

    @Resource
    private RedissonClient redissonClient;

    private static final OkHttpClient client = OkHttpTrustUtil.getTrustOkHttpClient();

    @Override
    @Async
    public void handlePayNotify(String payMessageStr) {
        Map<String, String> payMessage = mapStringToMap(payMessageStr);
        String outTradeNo = payMessage.get("out_trade_no");
        String lockKey = "Lock:WxPayOutTradeNo:"+outTradeNo;
//        log.info("------------lockKey:{}-----------",lockKey);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 加锁并获取超时时间
            lock.lock(1, TimeUnit.MINUTES);
            if(!alreadyHandle(outTradeNo)){
                log.info("------------未处理----------");
                // 通知一卡通
                handleNotify(payMessage);
            }
        } catch (Exception e){
            log.info(e.getMessage());
        } finally {
            // 释放锁
            lock.unlock();
        }
    }

    private void handleNotify(Map<String,String> payMessage){

        log.info("--------------微信支付订单入库成功------------");
        String callBackUrl = sRedisTemplate.opsForValue().get("Key:WxPayOutTradeNo:"+payMessage.get("out_trade_no"));

        PayMessageDO msg = new PayMessageDO();
        msg.setCreateTime(LocalDateTime.now());
        msg.setWxMessage(payMessage.toString());
        msg.setOutTradeNo(payMessage.get("out_trade_no"));
        msg.setReceiveWxSuccess(true);

        if(StrUtil.isBlank(callBackUrl)){
            msg.setNotifyurl("系统/redis异常，第三方支付回调地址缺失");
            return;
        }
        String replace = "";
        if(StrUtil.isNotEmpty(callBackUrl)){

            replace = callBackUrl.replace("s://ykt.hnswdx.gov.cn", "://ykt.hnswdx.gov.cn:44300");
            //党建不用替换
        }
        log.info("----------一卡通callbackUrl:{}----------",replace);
        String notifyUrl = replace + "/" + payMessage.get("transaction_id");
        msg.setNotifyurl(notifyUrl);
        this.save(msg);

        notifyOneCard(msg.getId(), notifyUrl);

    }

    @Async
    protected void notifyOneCard(Long msgId, String url) {

        PayMessageDO msg = new PayMessageDO();
        msg.setId(msgId);

        Request request = null;


        if(url.contains("dw.hnswdx.gov.cn")){

            request =  new Request
                    .Builder()
                    .url(url)
                    .addHeader("appId","9c99058d33aa11eb886600163e081727")
                    .addHeader("appSec","eaa472fe4933462495df7f6a1d55db2a")
                    .get()
                    .build();

        }else{
            request = new Request
                    .Builder()
                    .url(url)
                    .get()
                    .build();
        }


        Response response = null;
        String returnCode = "";
        try {
            response = client.newCall(request).execute();
            log.info("----------回调一卡通返回:{}",response);
            log.info(response.body().string());
            if (response.isSuccessful()) {
                returnCode = response.body().string();
            }else {
                handOneCardFailure(msgId, url);
            }
        } catch (Exception e) {
            log.info("notifyOneCard-wrong:" + e.getMessage());
            handOneCardFailure(msgId, url);
        } finally {
            if (null != response) {
                response.close();
            }
        }

        if(StrUtil.isNotEmpty(returnCode)){

            if(url.contains("dw.hnswdx.gov.cn")){

                DwResult commonResult  =  JSONUtil.toBean(returnCode , DwResult.class);

                if(ObjectUtil.isNotEmpty(commonResult)){
                    msg.setOneCardMessage(returnCode);
                    if( "0".equals(commonResult.getResponseEntity().getResultCode())){
                        //成功处理
                        log.info("微信支付回调处理成功  党建接收成功 处理成功 {}"    ,returnCode );
                        msg.setReceiveOneCardSuccess(true);
                        this.updateById(msg);
                    }else {
                        log.info("微信支付回调处理成功  党建接收成功 处理失败 {}"    ,returnCode );
                        msg.setReceiveOneCardSuccess(false);
                        this.updateById(msg);
                    }
                }

            }else{

                OneCardResult commonResult  =  JSONUtil.toBean(returnCode , OneCardResult.class);

                if(ObjectUtil.isNotEmpty(commonResult)){
                    msg.setOneCardMessage(returnCode);
                    if("1".equals(commonResult.getCode()) && "0".equals(commonResult.getData().getCode())){
                        //成功处理
                        log.info("微信支付回调处理成功  一卡通接收成功 处理成功 {}"    ,returnCode );
                        msg.setReceiveOneCardSuccess(true);
                        this.updateById(msg);
                    }else {
                        log.info("微信支付回调处理成功  一卡通接收成功 处理失败 {}"    ,returnCode );
                        msg.setReceiveOneCardSuccess(false);
                        this.updateById(msg);
                    }
                }
            }


        }
    }

    private void handOneCardFailure(Long msgId, String url) {

        Request request = null;


        if(url.contains("dw.hnswdx.gov.cn")){

            request =  new Request
                    .Builder()
                    .url(url)
                    .addHeader("appId","9c99058d33aa11eb886600163e081727")
                    .addHeader("appSec","eaa472fe4933462495df7f6a1d55db2a")
                    .get()
                    .build();

        }else{
            request = new Request
                    .Builder()
                    .url(url)
                    .get()
                    .build();
        }

        // 重试，最多5次，5次之后未收到一卡通成功处理则短信通知开发人员
        int i = 0;
        String returnCode = "";
        Response response = null;
        while (i < 5) {
            i++;
            try {
                Thread.sleep(50000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            try {
                response = client.newCall(request).execute();
                log.info("----------回调一卡通返回:{}",response);
                if (response.isSuccessful()) {
                    returnCode = response.body().string();
                }else {
                    log.info("第" + i + "次通知一卡通失败");
                }
            } catch (Exception e) {
                log.info("----------回调一卡通异常:{}，第{}次通知一卡通失败",e.getMessage(),i);
            } finally {
                if (null != response) {
                    response.close();
                }
            }
            if(StrUtil.isNotEmpty(returnCode)){

                if(url.contains("dw.hnswdx.gov.cn")){

                    DwResult commonResult  =  JSONUtil.toBean(returnCode , DwResult.class);

                    if(ObjectUtil.isNotEmpty(commonResult)){
                        PayMessageDO msg = new PayMessageDO();
                        msg.setId(msgId);
                        msg.setOneCardMessage(returnCode);
                        if( "0".equals(commonResult.getResponseEntity().getResultCode())){
                            //成功处理
                            log.info("微信支付回调处理成功  一卡通接收成功 处理成功 {}"    ,returnCode );
                            msg.setReceiveOneCardSuccess(true);
                            this.updateById(msg);
                        }else {
                            log.info("微信支付回调处理成功  一卡通接收成功 处理失败 {}"    ,returnCode );
                            msg.setReceiveOneCardSuccess(false);
                            this.updateById(msg);
                        }
                    }

                }else{
                    OneCardResult commonResult  =  JSONUtil.toBean(returnCode , OneCardResult.class);

                    if(ObjectUtil.isNotEmpty(commonResult)){
                        PayMessageDO msg = new PayMessageDO();
                        msg.setId(msgId);
                        msg.setOneCardMessage(returnCode);
                        if("1".equals(commonResult.getCode()) && "0".equals(commonResult.getData().getCode())){
                            //成功处理
                            log.info("微信支付回调处理成功  一卡通接收成功 处理成功 {}"    ,returnCode );
                            msg.setReceiveOneCardSuccess(true);
                            this.updateById(msg);
                        }else {
                            log.info("微信支付回调处理成功  一卡通接收成功 处理失败 {}"    ,returnCode );
                            msg.setReceiveOneCardSuccess(false);
                            this.updateById(msg);
                        }
                    }
                }


                break;
            }
        }
        if (i == 5) {
            log.info("短信发送给开发人员，通知一卡通失败");
        }
    }

    public Boolean alreadyHandle(String outTradeNo) {
        //收到微信回调通知，先判断是否已处理
        PayMessageDO entity = this.getOne(new LambdaQueryWrapperX<PayMessageDO>()
                .eqIfPresent(PayMessageDO::getOutTradeNo, outTradeNo));
        return !Objects.isNull(entity);
    }

    private Map<String, String> mapStringToMap(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        str = str.substring(1, str.length() - 1);
        String[] strs = str.split(",");
        Map<String, String> map = new HashMap<String, String>();
        for (String string : strs) {
            String[] strSplit = string.split("=");
            if (strSplit.length <= 1) {
                return map;
            }
            String key = string.split("=")[0];
            String value = string.split("=")[1];
            // 去除空格
            String key1 = key.trim();
            String value1 = value.trim();
            map.put(key1, value1);
        }
        return map;
    }
}
