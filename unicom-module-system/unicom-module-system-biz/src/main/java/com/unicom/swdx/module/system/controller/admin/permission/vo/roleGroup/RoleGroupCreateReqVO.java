package com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

@ApiModel("管理后台 - 角色组创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleGroupCreateReqVO extends RoleGroupBaseVO{
    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "角色", example = "[1,2]")
    private Set<Long> roleIds;
}
