package com.unicom.swdx.module.system.controller.admin.user.vo.applet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class UserAppletDashboardVO {

    @ApiModelProperty(value = "教学总工时")
    private Integer workHourTotal;

    @ApiModelProperty(value = "专题课次数")
    private Integer coreCourseTimes;

    @ApiModelProperty(value = "选修课占比")
    private BigDecimal alterCourseProp; // 注意这里将变量名从alter_course_prop改为更规范的命名

    @ApiModelProperty(value = "选修课次数")
    private Integer alterCourseTimes;

    @ApiModelProperty(value = "均分")
    private BigDecimal avgScore;

    @ApiModelProperty(value = "专题课占比")
    private BigDecimal coreCourseProp;

}
