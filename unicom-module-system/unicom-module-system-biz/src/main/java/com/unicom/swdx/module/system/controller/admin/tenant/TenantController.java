package com.unicom.swdx.module.system.controller.admin.tenant;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.*;
import com.unicom.swdx.module.system.convert.tenant.TenantConvert;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "管理后台 - 机构管理")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;

    @GetMapping("/refresh")
    @ApiOperation("刷新机构缓存")
    @PreAuthorize("@ss.hasPermission('system:tenant:init')")
    public CommonResult<Boolean> refresh() {
        tenantService.init();
        return success(true);
    }

    @PostMapping("/create")
    @ApiOperation("创建机构")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:create')")
    public CommonResult<Long> createTenant(@Valid @RequestBody TenantCreateReqVO createReqVO) {
        return success(tenantService.createTenant(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新机构")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenant(@Valid @RequestBody TenantUpdateReqVO updateReqVO) {
        tenantService.updateTenant(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-status")
    @ApiOperation("更新机构状态")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenantStatus(@RequestBody TenantStatusUpdateReqVO updateReqVO) {
        tenantService.updateTenantStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除机构")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:tenant:delete')")
    public CommonResult<Boolean> deleteTenant(@RequestParam("id") Long id) {
        tenantService.deleteTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得机构")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
//    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<TenantRespGetVO> getTenant(@RequestParam("id") Long id) {
        TenantDO tenant = tenantService.getTenant(id);
        return success(TenantConvert.INSTANCE.convert01(tenant));
    }

    @GetMapping("/page")
    @ApiOperation("获得机构分页")
    public CommonResult<PageResult<TenantRespVO>> getTenantPage(@Valid TenantPageReqVO pageVO) {
        PageResult<TenantDO> pageResult = tenantService.getTenantPage(pageVO);
        return success(TenantConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出机构 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant:export')")
    @OperateLog(type = EXPORT)
    public void exportTenantExcel(@Valid TenantPageReqVO reqVO,
                                  HttpServletResponse response) throws IOException {
        List<TenantDO> list = tenantService.getTenantExcelList(reqVO);
        // 导出 Excel
        List<TenantExcelVO> datas = TenantConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "机构信息.xls", "数据", TenantExcelVO.class, datas);
    }

    @GetMapping("/check-user-num")
    @ApiOperation("判断机构下是否存在除机构管理员外的用户")
    public CommonResult<Boolean> checkUserNum(@RequestParam Long id) {
        return success(tenantService.checkUserNum(id));
    }

    @GetMapping("/dept-num")
    @ApiOperation(value = "获取机构下组织数量",notes = "不包含该机构的根组织")
    public CommonResult<Long> getDeptNum(@RequestParam Long id) {
        return success(tenantService.getDeptNum(id));
    }

    @GetMapping("/list-all")
    @ApiOperation("重置获取机构列表")
    public CommonResult<List<TenantSimpleRespVO>> getAllList() {
        return success(TenantConvert.INSTANCE.convertList01(tenantService.list(
                new LambdaQueryWrapperX<>())));
    }

    // todo 待删除
//    @GetMapping("/list-all")
//    @ApiOperation("重置获取机构列表")
//    @PermitAll
//    public CommonResult<List<TenantSimpleRespVO>> getAllList() {
//        return success(TenantConvert.INSTANCE.convertList01(tenantService.list()));
//    }

    // todo 待删除
//    @GetMapping("/get-id-by-name")
//    @PermitAll
//    @ApiOperation(value = "使用机构名，获得机构编号", notes = "登录界面，根据用户的机构名，获得机构编号")
//    @ApiImplicitParam(name = "name", value = "机构名", required = true, example = "1024", dataTypeClass = Long.class)
//    public CommonResult<Long> getTenantIdByName(@RequestParam("name") String name) {
//        TenantDO tenantDO = tenantService.getTenantByName(name);
//        return success(tenantDO != null ? tenantDO.getId() : null);
//    }

    // todo 待删除
//    @GetMapping("/list-by-ids")
//    @ApiOperation(value = "根据机构id集合查询机构")
//    @PermitAll
//    public CommonResult<List<TenantInfoVO>> getListByIds(@RequestParam(value = "ids",required = false) Collection<Long> ids) {
//        return success(TenantConvert.INSTANCE.convertList04(tenantService.getTenantListByIds(ids)));
//    }

    @GetMapping("/save")
    @ApiOperation("将传入的tenantId存储到缓存")
//    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<Boolean> saveTenantIdToRedis(@RequestParam("tenantId") Long tenantId) {
        tenantService.saveTenantIdToRedis(tenantId);
        return success(true);
    }


    @PostMapping("/update-check-rule")
    @ApiOperation("更新机构午别考勤规则开启状态")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenantCheckRule(@RequestBody TenantCheckRuleUpdateReqVO updateReqVO) {
        updateReqVO.setId(SecurityFrameworkUtils.getTenantId());
        return success(tenantService.updateTenantCheckRule(updateReqVO));
    }

    @PostMapping("/update-attendance-protection")
    @ApiOperation("更新考勤保护开启状态")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateAttendanceProtection(@RequestBody TenantAttendanceProtectionUpdateReqVO updateReqVO) {
        updateReqVO.setId(SecurityFrameworkUtils.getTenantId());
        return success(tenantService.updateAttendanceProtection(updateReqVO));
    }

    @GetMapping("/get-check-rule")
    @ApiOperation("更新机构午别考勤规则开启状态")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> getTenantCheckRule(@RequestParam("tenantId") Long tenantId) {
        return success(tenantService.getTenantCheckRule(SecurityFrameworkUtils.getTenantId()));
    }

}
