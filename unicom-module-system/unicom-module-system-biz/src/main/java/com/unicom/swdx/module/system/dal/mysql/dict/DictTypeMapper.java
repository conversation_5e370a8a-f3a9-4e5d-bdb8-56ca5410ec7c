package com.unicom.swdx.module.system.dal.mysql.dict;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.dict.vo.type.DictTypeExportReqVO;
import com.unicom.swdx.module.system.controller.admin.dict.vo.type.DictTypePageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DictTypeMapper extends BaseMapperX<DictTypeDO> {

    default PageResult<DictTypeDO> selectPage(DictTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DictTypeDO>()
                .eqIfPresent(DictTypeDO::getStatus, reqVO.getStatus())
                .and(StrUtil.isNotBlank(reqVO.getName()), l -> l.like(DictTypeDO::getName,
                        reqVO.getName()).or().like(DictTypeDO::getType,reqVO.getName()))
                .orderByDesc(DictTypeDO::getId));
    }

    default List<DictTypeDO> selectList(DictTypeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DictTypeDO>()
                .eqIfPresent(DictTypeDO::getStatus, reqVO.getStatus())
                .and(StrUtil.isNotBlank(reqVO.getName()), l -> l.like(DictTypeDO::getName,
                        reqVO.getName()).or().like(DictTypeDO::getType,reqVO.getName()))
                );
    }

    default DictTypeDO selectByType(String type) {
        return selectOne(DictTypeDO::getType, type);
    }

    default DictTypeDO selectByName(String name) {
        return selectOne(DictTypeDO::getName, name);
    }

}
