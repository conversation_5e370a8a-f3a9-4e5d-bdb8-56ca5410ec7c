package com.unicom.swdx.module.system.controller.admin.permission.vo.role;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import lombok.Data;

import java.time.LocalDateTime;

import static com.unicom.swdx.module.system.enums.DictTypeConstants.COMMON_STATUS;
import static com.unicom.swdx.module.system.enums.DictTypeConstants.SYSTEM_ROLE_TYPE;

/**
 * 角色 Excel 导出响应 VO
 */
@Data
public class RoleExcelVO {

//    @ExcelProperty("角色编号")
//    private Long id;

    @ExcelProperty("角色名称")
    private String name;

    @ExcelProperty("角色标识")
    private String code;

    @ExcelProperty(value = "角色类型", converter = DictConvert.class)
    @DictFormat(SYSTEM_ROLE_TYPE)
    private Integer type;

    @ExcelProperty("所属应用")
    private String client;

    @ExcelProperty("所属机构")
    private String tenant;

//    @ExcelProperty("显示顺序")
//    private Integer sort;

    @ExcelProperty(value = "角色状态", converter = DictConvert.class)
    @DictFormat(COMMON_STATUS)
    private String status;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
