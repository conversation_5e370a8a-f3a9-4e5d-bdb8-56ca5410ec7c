package com.unicom.swdx.module.system.convert.tenant;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.type.*;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构用户类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantTypeConvert {

    TenantTypeConvert INSTANCE = Mappers.getMapper(TenantTypeConvert.class);

    TenantTypeDO convert(TenantTypeCreateReqVO bean);

    TenantTypeDO convert(TenantTypeUpdateReqVO bean);

    TenantTypeDO convert(TenantTypeUpdateStatusReqVO bean);

    TenantTypeRespVO convert(TenantTypeDO bean);

    List<TenantTypeRespVO> convertList(List<TenantTypeDO> list);

    PageResult<TenantTypeRespVO> convertPage(PageResult<TenantTypeDO> page);

    List<TenantTypeSimpleRespVO> convertList01(List<TenantTypeDO> list);

}
