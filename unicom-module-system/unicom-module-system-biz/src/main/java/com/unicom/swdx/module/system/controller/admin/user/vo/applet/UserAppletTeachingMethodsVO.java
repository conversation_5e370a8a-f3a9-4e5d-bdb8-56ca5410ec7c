package com.unicom.swdx.module.system.controller.admin.user.vo.applet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
public class UserAppletTeachingMethodsVO {

    @ApiModelProperty(value = "课程名称", required = true)
    private String EDUCATE_FORM_NAME;


    @ApiModelProperty(value = "百分比", required = true)
    private BigDecimal prop;


    @ApiModelProperty(value = "工时", required = true)
    private Integer work_hour;

}
