package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel(value = "管理后台 - 租户 Excel 导出 Request VO", description = "参数和 TenantPageReqVO 是一致的")
@Data
public class TenantExportReqVO {

    @ApiModelProperty(value = "机构全称", example = "湖南省体育局")
    private String name;

    @ApiModelProperty(value = "统一社会信用代码")
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "管理员联系手机", example = "15600001111")
    private String contactMobile;

    @ApiModelProperty(value = "单位类型")
    private Integer companyType;

    @ApiModelProperty(value = "租户状态（0正常 1停用）", example = "0")
    private Integer status;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime[] createTime;

//    @ApiModelProperty(value = "备注")
//    private String notes;
}
