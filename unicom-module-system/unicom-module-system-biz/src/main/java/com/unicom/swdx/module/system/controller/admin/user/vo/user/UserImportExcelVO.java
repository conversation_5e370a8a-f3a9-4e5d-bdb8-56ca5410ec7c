package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class UserImportExcelVO {

    @ExcelProperty("姓名\n（必填）")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickname;

    @ExcelProperty("所属组织编号\n（必填，可到组织管理菜单查询组织编号）")
    @NotNull(message = "所属组织编号不能为空")
    private Long deptId;

    @ExcelProperty("手机号码\n（必填）")
    @Mobile
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ExcelProperty("显示排序\n（必填）")
    @NotNull(message = "显示排序不能为空")
    private Integer sort;

    @ExcelProperty("邮箱\n（选填）")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    @ExcelProperty(value = "用户性别\n（选填，填写“男”或“女”）", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @ExcelProperty(value = "账号状态\n（选填，填写“启用”、“禁用”）", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @ExcelProperty("用户名\n（必填，4-30位数字、字母，区分大小写）")
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @ExcelProperty("用户密码\n（必填，8-16位，需同时包含大小写字母、数字、特殊字符）")
    @NotBlank(message = "密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    private String password;

    @ExcelProperty("备注\n（选填）")
    @Size(max = 50, message = "备注长度不能超过50个字符")
    private String remark;

    public void setSort(Integer sort) {
        this.sort = sort<0? 0:sort;
    }
}
