package com.unicom.swdx.module.system.dal.mysql.user;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.dal.dataobject.user.TraineeUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TraineeUserMapper extends BaseMapperX<TraineeUserDO> {

    @Select("select user_id from system_trainee where deleted != true")
    List<Long> getTraineeList();

    @Select("select user_id from hr_personnel_basic_information")
    List<Long> getHrList();

    @Select("select count(*) from hr_personnel_basic_information where user_id = #{userId} and deleted != true")
    Integer getHr(@Param("userId") Long userId);
}
