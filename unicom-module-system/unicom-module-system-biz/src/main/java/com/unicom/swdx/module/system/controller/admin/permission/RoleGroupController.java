package com.unicom.swdx.module.system.controller.admin.permission;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.*;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.*;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleGroupService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "管理后台 - 角色组")
@RestController
@RequestMapping("/system/roleGroup")
@Validated
@Slf4j
public class RoleGroupController {

    @Resource
    RoleGroupService roleGroupService;

    @Resource
    PermissionService permissionService;

    @PostMapping("/create")
    @ApiOperation("创建角色组")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<Long> createRoleGroup(@Valid @RequestBody RoleGroupCreateReqVO reqVO) {
        return success(roleGroupService.createRoleGroup(reqVO));
    }


    @PostMapping("/update")
    @ApiOperation("修改角色组")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:role:update')")
    public CommonResult<Boolean> updateRoleGroup(@Valid @RequestBody RoleGroupUpdateReqVO reqVO) {
        RoleGroupRespVO roleGroup = roleGroupService.getRoleGroup(reqVO.getId());
        //角色组之前有的角色id
        Set<Long> roleIds = roleGroup.getRoleIds();
        //角色组已分配用户id
        List<Long> userIds = roleGroupService.getRoleGroupUsers(reqVO.getId());
        //角色组新的角色id
        Set<Long> newRoleIds = reqVO.getRoleIds();
        roleGroupService.updateRoleGroup(reqVO);
        //同步修改已有分配用户的相关权限
        permissionService.updateUserRoles(roleIds, newRoleIds, userIds);
        return success(true);
    }

    @PostMapping("/update-status")
    @ApiOperation("修改角色组状态")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:role:update')")
    public CommonResult<Boolean> updateRoleGroupStatus(@Valid @RequestBody RoleGroupUpdateStatusReqVO reqVO) {
        roleGroupService.updateRoleGroupStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除角色组")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "角色组编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:role:delete')")
    public CommonResult<Boolean> deleteRoleGroup(@RequestParam("id") Long id) {
        roleGroupService.deleteRoleGroup(id);
        return success(true);
    }

    @GetMapping("/page")
    @ApiOperation("获得角色组分页")
    public CommonResult<PageResult<RoleGroupRespVO>> getRoleGroupPage(RoleGroupPageReqVO reqVO) {
        return success(roleGroupService.getRoleGroupPage(reqVO));
    }


    @GetMapping("/get")
    @ApiOperation("获得角色组信息")
    public CommonResult<RoleGroupRespVO> getRoleGroup(@RequestParam("id") Long id) {
        return success(roleGroupService.getRoleGroup(id));
    }

    @GetMapping("/get-roleGroup-users")
    @ApiOperation("获得角色组分配用户信息")
    public CommonResult<RoleGroupUsersRespVO> getRoleGroupUsers(RoleGroupUsersPageReqVO pageReqVO) {
        return success(roleGroupService.getRoleGroupUsersPage(pageReqVO));
    }

    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获取角色精简信息列表", notes = "只包含被开启的角色，展示用户所属机构的所有角色")
    public CommonResult<List<RoleClientSimpleRespVO>> getSimpleRoles(Long groupId) {
        return success(roleGroupService.getSimpleRoleList(groupId));
    }
}
