package com.unicom.swdx.module.system.service.dept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptDTO;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.*;
import com.unicom.swdx.module.system.convert.dept.DeptConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.enums.dept.DeptEnum;
import com.unicom.swdx.module.system.enums.dept.DeptIdEnum;
import com.unicom.swdx.module.system.enums.kafka.dept.DeptEventType;
import com.unicom.swdx.module.system.mq.producer.dept.DeptProducer;
import com.unicom.swdx.module.system.mq.producer.tenant.TenantProducer;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.ApiConstants.CLIENT_CODE;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * 部门 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeptServiceImpl extends ServiceImpl<DeptMapper, DeptDO> implements DeptService {

    /**
     * 部门缓存
     * key：部门编号 {@link DeptDO#getId()}
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    @SuppressWarnings("FieldCanBeLocal")
    private volatile Map<Long, DeptDO> deptCache;
    /**
     * 父部门缓存
     * key：部门编号 {@link DeptDO#getParentId()}
     * value: 直接子部门列表
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private volatile Multimap<Long, DeptDO> parentDeptCache;

    @Resource
    private DeptMapper deptMapper;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantProducer tenantProducer;
    @Resource
    private DeptProducer deptProducer;

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    @Lazy // 注入自己，所以延迟加载
    private DeptService self;

    @Resource
    @Lazy
    private PermissionService permissionService;



    @Scheduled(fixedRate = 1000*300)//从部署时间开始每5分钟触发
    public void initLocalCachefixtime() {
        initLocalCache();
    }



    @Override
    @PostConstruct
    @TenantIgnore // 初始化缓存，无需租户过滤
    public synchronized void initLocalCache() {
        // 获取部门列表，如果有更新
        List<DeptDO> deptList = list();
        if (CollUtil.isEmpty(deptList)) {
            return;
        }
        // 构建缓存
        ImmutableMap.Builder<Long, DeptDO> builder = ImmutableMap.builder();
        ImmutableMultimap.Builder<Long, DeptDO> parentBuilder = ImmutableMultimap.builder();
        deptList.forEach(deptDO -> {
            builder.put(deptDO.getId(), deptDO);
            parentBuilder.put(deptDO.getParentId(), deptDO);
        });
        // 设置缓存
        deptCache = builder.build();
        parentDeptCache = parentBuilder.build();
        log.info("[initLocalCache][初始化 Dept 数量为 {}]", deptList.size());
    }

    /**
     * 创建部门
     *
     * @param reqVO 部门信息
     * @return 部门编号
     */
    @Override
    @TenantIgnore
    public Long createDept(DeptCreateReqVO reqVO) {
        Long tenantId = null;
        // 校验正确性
        Long parentId = reqVO.getParentId();
        if (parentId == null) {
            reqVO.setParentId(DeptIdEnum.ROOT.getId());
            tenantId = getTenantId();
        } else {
            tenantId = deptCache.get(parentId).getTenantId();
        }
        checkCreateOrUpdate(null, parentId, reqVO.getName());
        // 插入组织
        DeptDO dept = DeptConvert.INSTANCE.convert(reqVO);
        dept.setTenantId(tenantId);
        deptMapper.insert(dept);
        // 发送刷新消息
        initLocalCache();
        deptProducer.sendRefreshMessage();
        String tenantCode = tenantService.getTenantFromCache(tenantId).getTenantCode();
        deptProducer.sendDeptData(DeptEventType.addDeptEventType, dept, tenantCode);
        return dept.getId();
    }

    @Override
    public void updateDept(DeptUpdateReqVO reqVO) {
        // 校验正确性
        if (reqVO.getParentId() == null) {
            reqVO.setParentId(DeptIdEnum.ROOT.getId());
        }
        //更改状态
        List<DeptDO> deptDOS = deptMapper.selectChildrenDept(reqVO.getId());
        if(CommonStatusEnum.ENABLE.getStatus().equals(reqVO.getStatus())){
            //该组织存在子组织，二次确认全部开启
            deptMapper.updateStatusByDeptIdList(deptDOS.stream().map(DeptDO::getId).collect(Collectors.toList()),0);
        }
        if (CommonStatusEnum.DISABLE.getStatus().equals(reqVO.getStatus())) {
            //该组织存在用户，无法关闭
            if (userMapper.selectCountByDeptId(reqVO.getId()) > 0) {
                throw ServiceExceptionUtil.exception(DEPT_All_EXISTS_USER);
            } else {
                //子组织存在用户，无法关闭
                if (userMapper.selectCountByDeptIdList(deptDOS.stream().map(DeptDO::getId).collect(Collectors.toList())) > 0) {
                    throw ServiceExceptionUtil.exception(DEPT_All_EXISTS_USER);
                }//不存在用户,二次确认关闭该组织及子组织
                else {
                    deptMapper.updateStatusByDeptIdList(deptDOS.stream().map(DeptDO::getId).collect(Collectors.toList()), 1);
                }
            }
        }
        checkCreateOrUpdate(reqVO.getId(), reqVO.getParentId(), reqVO.getName());
        // 更新组织
        DeptDO updateObj = DeptConvert.INSTANCE.convert(reqVO);
        deptMapper.updateById(updateObj);

        updateObj = checkDeptExistsDao(updateObj.getId());

        if (Objects.equals(DeptEnum.ROOT.getId(),updateObj.getParentId())) {
            // 如果是根组织，则更新机构信息
            TenantDO tenant = tenantService.getTenant(this.getDept(reqVO.getId()).getTenantId());
            tenant.setName(updateObj.getName());
            tenantService.updateById(tenant);
            tenantProducer.sendTenantRefreshMessage();
        }
        // 发送刷新消息
        initLocalCache();
        deptProducer.sendRefreshMessage();
        String tenantCode = tenantService.getTenantFromCache(this.getDept(reqVO.getId()).getTenantId()).getTenantCode();
        deptProducer.sendDeptData(DeptEventType.editDeptEventType, updateObj, tenantCode);
        // 推送更新部门
//        self.pushUpdateToDataTop(updateObj);
    }

    @Override
    public void deleteDept(Long id) {
        // 校验是否存在
        DeptDO deptDO =  checkDeptExistsDao(id);
        String tenantCode = tenantService.getTenantFromCache(this.getDept(id).getTenantId()).getTenantCode();
        //该组织及子组织
        List<DeptDO> children = deptMapper.selectChildrenDept(id);
        Set<Long> deletedIds = new HashSet<>();
        deletedIds.add(id);
        if (CollUtil.isNotEmpty(children)) {
            CollUtil.addAll(deletedIds,children.stream().map(DeptDO::getId).collect(Collectors.toSet()));
        }
        // 删除组织
        deptMapper.deleteBatchIds(deletedIds);
        // 发送刷新消息
        initLocalCache();
        deptProducer.sendRefreshMessage();

        deptProducer.sendDeptData(DeptEventType.delDeptEventType, deptDO, tenantCode);
        // 推送删除部门
//        self.pushDeleteToDataTop(id);
    }

    @Override
    public Integer getChildrenDeptCount(Long id) {
        List<DeptDO> children = deptMapper.selectChildrenDept(id);
        return children.size() - 1;
    }

    @Override
    public Long getAllDeptUsers(Long id) {
        // 获取组织及其子组织
        List<DeptDO> deptDOS = deptMapper.selectChildrenDept(id);
        return userMapper.selectCountByDeptIdList(deptDOS.stream().map(DeptDO::getId).collect(Collectors.toList()));
    }

    @Override
    public List<DeptDO> getSimpleDepts(DeptListReqVO reqVO) {
        List<DeptDO> deptList = getDataPermissionDeptList(reqVO);
        repairParentsDept(deptList);
        return deptList;
    }

    @Override
    @TenantIgnore
    public List<DeptRespVO> getDeptsList(DeptListReqVO reqVO) {
        List<DeptDO> deptDOS = null;
        // 超管查看全部机构的部门
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            deptDOS = deptMapper.selectList01(reqVO);
        } else {
            reqVO.setTenantId(getTenantId());
            deptDOS = deptMapper.selectList02(reqVO);
        }
        repairParentsDept(deptDOS);
        // 肯定是有部门的
        List<DeptRespVO> deptRespVOS = DeptConvert.INSTANCE.convertList(deptDOS);
        deptRespVOS.forEach(d -> {
            d.setAddable(true);
            d.setEditable(true);
        });
        return deptRespVOS;
    }

    /**
     * 获取经过数据权限筛选的组织
     * @param reqVO 请求参数
     * @return 组织集合
     */
    private List<DeptDO> getDataPermissionDeptList(DeptListReqVO reqVO) {
        List<DeptDO> deptList = deptMapper.selectList(reqVO);
        // 获取系统标识
        String clientCode = StrUtil.blankToDefault(WebFrameworkUtils.getClientCode(),CLIENT_CODE);
        // 本部门和个人，需要加上下属组织
        if (permissionService.checkDeptDataPermissionDeptOnlyOrSelf(getLoginUserId(),clientCode)) {
            AdminUserDO user = userMapper.selectById(getLoginUserId());
            Long deptId = user.getDeptId();
            if (Objects.nonNull(deptId)) {
                List<DeptDO> depts = getDeptsByParentIdFromCache(deptId, true);
                // 加上用户所在的部门
                depts.add(getDept(deptId));
                Set<Long> deptDOIds = deptList.stream().map(DeptDO::getId).collect(Collectors.toSet());
                // 对部门进行条件过滤
                depts = depts.stream().filter(d -> {
                    boolean get = true;
                    if (deptDOIds.contains(d.getId())) {
                        get = false;
                    }
                    if (StrUtil.isNotBlank(reqVO.getName()) && !StrUtil.contains(d.getName(),reqVO.getName())) {
                        get = false;
                    }
                    if (Objects.nonNull(reqVO.getStatus()) && !Objects.equals(d.getStatus(),reqVO.getStatus())) {
                        get = false;
                    }
                    return get;
                }).collect(Collectors.toList());
                CollUtil.addAll(deptList,depts);
            }
        }
        // 临时组织，不展示给用户
        deptList.removeIf(d -> Objects.equals(d.getId(),DeptIdEnum.TEMP.getId()));
        return deptList;
    }

    @Override
    public List<DeptRespVO> getChildrenDeptByDeptId(Long parentId) {
        List<DeptDO> deptDOS = deptMapper.getChildrenDeptByDeptId(parentId);
        deptDOS.sort(Comparator.comparing(DeptDO::getSort));
        List<DeptRespVO> deptRespVOS = DeptConvert.INSTANCE.convertList(deptDOS);
        deptRespVOS.forEach(deptRespVO -> {
            deptRespVO.setDeptUserCounts(this.getAllDeptUsers(deptRespVO.getId()));
        });
        return deptRespVOS;
    }

    @Override
    public List<DeptDO> getDeptsByParentIdFromCache(Long parentId, boolean recursive) {
        if (parentId == null) {
            return Collections.emptyList();
        }
        List<DeptDO> result = new ArrayList<>(); // TODO 芋艿：待优化，新增缓存，避免每次遍历的计算
        // 递归，简单粗暴
        this.getDeptsByParentIdFromCache(result, parentId,
                recursive ? Integer.MAX_VALUE : 1, // 如果递归获取，则无限；否则，只递归 1 次
                parentDeptCache);
        return result;
    }

    private DeptDO getParentFromCache(DeptDO deptDO) {
        Map<Long, Collection<DeptDO>> map = parentDeptCache.asMap();
        AtomicReference<Long> parentIdTemp = new AtomicReference<>();
        map.forEach((parentId,deptDOS) -> {
            Optional<DeptDO> any = deptDOS.stream().filter(deptDOTemp -> deptDOTemp.getId().compareTo(deptDO.getId()) == 0).findAny();
            if (any.isPresent()) {
                parentIdTemp.set(parentId);
            }
        });
        if (Objects.nonNull(parentIdTemp.get())) {
            return deptCache.get(parentIdTemp.get());
        }
        return null;
    }

    /**
     * 递归获取所有的子部门，添加到 result 结果
     *
     * @param result 结果
     * @param parentId 父编号
     * @param recursiveCount 递归次数
     * @param parentDeptMap 父部门 Map，使用缓存，避免变化
     */
    private void getDeptsByParentIdFromCache(List<DeptDO> result, Long parentId, int recursiveCount,
                                             Multimap<Long, DeptDO> parentDeptMap) {
        // 递归次数为 0，结束！
        if (recursiveCount == 0) {
            return;
        }
        // 获得子部门
        Collection<DeptDO> depts = parentDeptMap.get(parentId);
        if (CollUtil.isEmpty(depts)) {
            return;
        }
        result.addAll(depts);
        // 继续递归
        depts.forEach(dept -> getDeptsByParentIdFromCache(result, dept.getId(),
                recursiveCount - 1, parentDeptMap));
    }

    private void checkCreateOrUpdate(Long id, Long parentId, String name) {
        // 校验自己存在
        checkDeptExists(id);
        // 校验父部门的有效性
        checkParentDeptEnable(id, parentId);
        // 校验部门名的唯一性
        checkDeptNameUnique(id, parentId, name);
    }

    private void checkParentDeptEnable(Long id, Long parentId) {
        if (parentId == null || DeptIdEnum.ROOT.getId().equals(parentId) || parentId == 0) {
            return;
        }
        // 不能设置自己为父部门
        if (parentId.equals(id)) {
            throw ServiceExceptionUtil.exception(DEPT_PARENT_ERROR);
        }
        // 父部门不存在
        DeptDO dept = deptCache.get(parentId);
        if (dept == null) {
            throw ServiceExceptionUtil.exception(DEPT_PARENT_NOT_EXITS);
        }
        // 父部门被禁用
        if (!CommonStatusEnum.ENABLE.getStatus().equals(dept.getStatus())) {
            throw ServiceExceptionUtil.exception(DEPT_NOT_ENABLE);
        }
        // 父部门不能是原来的子部门
        List<DeptDO> children = this.getDeptsByParentIdFromCache(id, true);
        if (children.stream().anyMatch(dept1 -> dept1.getId().equals(parentId))) {
            throw ServiceExceptionUtil.exception(DEPT_PARENT_IS_CHILD);
        }
    }

    private void checkDeptExists(Long id) {
        if (id == null) {
            return;
        }
        DeptDO dept = deptMapper.selectById(id);
        if (dept == null) {
            throw ServiceExceptionUtil.exception(DEPT_NOT_FOUND);
        }
    }


    private DeptDO checkDeptExistsDao(Long id) {
        if (id == null) {
            return null;
        }
        DeptDO dept = deptMapper.selectById(id);
        if (dept == null) {
            throw ServiceExceptionUtil.exception(DEPT_NOT_FOUND);
        }
        return  dept;
    }


    private void checkDeptNameUnique(Long id, Long parentId, String name) {
        DeptDO menu = deptMapper.selectByParentIdAndName(parentId, name);
        if (menu == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的岗位
        if (id == null) {
            throw ServiceExceptionUtil.exception(DEPT_NAME_DUPLICATE);
        }
        if (!menu.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(DEPT_NAME_DUPLICATE);
        }
    }

    @Override
    public List<DeptDO> getDepts(Collection<Long> ids) {

        List<DeptDO> deptDOList = getCacheDepts(new ArrayList<>(ids));

        if(ids!=null){

            if(CollUtil.isNotEmpty(deptDOList)){
                return deptDOList;
            }
        }
        return deptMapper.selectBatchIds(ids);
    }

    @Override
    public DeptDO getDept(Long id) {
        DeptDO deptDO = deptCache.get(id);
        if (Objects.isNull(deptDO)) {
            return deptMapper.selectById(id);
        }
        return deptDO;
    }

    @Override
    public List<DeptDO> getCacheDepts(List<Long> ids) {

        List<DeptDO>  deptDOList = new ArrayList<>();
        for (Long id : ids) {
            DeptDO deptDO = deptCache.get(id);
            if (Objects.isNull(deptDO)) {
                deptDO =  deptMapper.selectById(id);
            }

            deptDOList.add(deptDO);
        }


        return deptDOList;
    }

    @Override
    public DeptDO getDeptIgnoreDeleted(Long id) {
        DeptDO deptDO = deptCache.get(id);
        if (Objects.isNull(deptDO)) {
            return deptMapper.selectByIdIgnoDeleted(id);
        }
        return deptDO;
    }


    @Override
    public DeptDO getDeptByTenantAndName(Long tenantid ,String deptname) {
        LambdaQueryWrapperX<DeptDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(DeptDO::getName, deptname);
        lambdaQueryWrapperX.eq(DeptDO::getTenantId , tenantid);
        lambdaQueryWrapperX.last(" limit 1 ");
        DeptDO deptDO = deptMapper.selectOne(lambdaQueryWrapperX);
        return deptDO;
    }

    @Override
    public void validDepts(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得科室信息
        List<DeptDO> depts = deptMapper.selectBatchIds(ids);
        Map<Long, DeptDO> deptMap = CollectionUtils.convertMap(depts, DeptDO::getId);
        // 校验
        ids.forEach(id -> {
            DeptDO dept = deptMap.get(id);
            if (dept == null) {
                throw exception(DEPT_NOT_FOUND);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(dept.getStatus())) {
                throw exception(DEPT_NOT_ENABLE, dept.getName());
            }
        });
    }

    @Override
    public List<DeptDO> getSimpleDepts(Collection<Long> ids) {
        return deptMapper.selectBatchIds(ids);
    }

    @Override
    public void clearLeader(Long userId) {
        deptMapper.updateLeaderByUserId(userId);
    }

    @Override
    public List<DeptDO> getAllParentDeptByDeptId(Long deptId) {
        Map<Long, Collection<DeptDO>> parentDeptMap = parentDeptCache.asMap();
        Map<Long,Collection<Long>> map = new HashMap<>();
        parentDeptMap.forEach((parentId,deptDOS) -> {
            map.put(parentId,deptDOS.stream().map(DeptDO::getId).collect(Collectors.toList()));
        });
        List<DeptDO> result = new ArrayList<>();
        this.getParentDeptFromCache(map,deptId,result);
        return result;
    }

    @Override
    public List<DeptDO> getAllChildrenDeptByDeptId(Long deptId) {
        return deptMapper.selectChildrenDept(deptId);
    }

    @Override
    public List<DeptDO> getAllChildrenDeptByDeletedDeptId(List<Long> deptIds) {
        return deptMapper.selectChildrenDeletedDept(deptIds);
    }

    @Override
    public List<DeptSimpleRespVO> convertPath(List<DeptSimpleRespVO> deptSimpleRespVOS) {
        for (DeptSimpleRespVO deptSimpleRespVO : deptSimpleRespVOS) {
            List<DeptDO>  deptDOS = this.getAllParentDeptByDeptId(deptSimpleRespVO.getId());
            deptDOS.sort(Comparator.comparing(DeptDO::getId));
            List<String> deptNames = new ArrayList<>();
            deptDOS.stream().forEach(deptDO -> {
                deptNames.add(deptDO.getName());
            });
            deptNames.add(deptSimpleRespVO.getName());
            deptSimpleRespVO.setParentDeptName(String.join("/", deptNames));
        }
        return deptSimpleRespVOS;
    }

    @Override
    public List<DeptTreeVO> getTreeDept() {
        DeptListReqVO reqVO = new DeptListReqVO();
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> allDept = deptMapper.selectList(reqVO);
        // 补齐父级组织
        repairParentsDept(allDept);

        // 排序，保证组织的有序性
        allDept.sort(Comparator.comparing(DeptDO::getSort));
        // 构建组织树
        // 使用 LinkedHashMap 的原因，是为了排序 。实际也可以用 Stream API ，就是太丑了。
        Map<Long, DeptTreeVO> treeNodeMap = new LinkedHashMap<>();
        allDept.forEach(dept -> treeNodeMap.put(dept.getId(), DeptConvert.INSTANCE.convertTree(dept)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getParentId().equals(0L)).forEach(childNode -> {
            // 获得父节点
            DeptTreeVO parentNode = treeNodeMap.get(childNode.getParentId());
            if (parentNode == null) {
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });
        // 获得到所有的根节点
        return CollectionUtils.filterList(treeNodeMap.values(), node -> DeptEnum.ROOT.getId().equals(node.getParentId()));
    }


    /**
     * 获得指定机构下的组织树（用于获取组织树）
     * @return
     */
    @Override
    public List<DeptTreeVO> getTreeDeptInTenant(Set<Long> tenantIds) {
        DeptListReqVO reqVO = new DeptListReqVO();
        reqVO.setTenantIds(tenantIds);
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> allDept = deptMapper.selectListInTenant(reqVO);
        allDept = allDept.stream().filter(deptDO -> !deptDO.getId().equals(1L)).collect(Collectors.toList());
        // 补齐父级组织
        repairParentsDept(allDept);
        // 排序，保证组织的有序性
        allDept.sort(Comparator.comparing(DeptDO::getSort));
        // 构建组织树
        DeptTreeVO root = buildTree(allDept);
        root = limitMaxDepth(root,0,4);
        if (root != null){
            return root.getChildren();
        }
        else {
            return Collections.emptyList();
        }
    }

    /**
     * 构建组织树
     * @param allDept
     * @return
     */
    private DeptTreeVO buildTree(List<DeptDO> allDept){
        Map<Long, DeptTreeVO> treeNodeMap = new LinkedHashMap<>();
        allDept.forEach(dept -> treeNodeMap.put(dept.getId(), DeptConvert.INSTANCE.convertTree(dept)));
        DeptTreeVO root = new DeptTreeVO();//先构建一个虚拟的根节点
        root.setId(DeptEnum.ROOT.getId());
        root.setChildren(new ArrayList<>());
        //准备一个需要修改可选性的分支的根节点集合
        List<DeptTreeVO> changeBranchStatus = new ArrayList<>();
        //准备一个需要修改可选性的的节点集合
        List<DeptTreeVO> changeNodeStatus = new ArrayList<>();
        // 处理父子关系
        treeNodeMap.values().forEach( node -> {
            if(node.getParentId().equals(DeptEnum.ROOT.getId())){
                root.getChildren().add(node);
            }
            else {
                DeptTreeVO parent = treeNodeMap.get(node.getParentId());
                if (parent != null){
                    if (parent.getChildren() == null){
                        parent.setChildren(new ArrayList<>());
                    }
//                    if (parent.getParentId() == 0){
//                        //根据需求要对把内设机构及其分支的可选性设为禁用
//                        if ("内设机构".equals(node.getName())){
//                            changeBranchStatus.add(node);
//                        }
//                        //根据需求，把直属机构这一节点的可选性设为禁用
//                        if ("直属机构".equals(node.getName())){
//                            changeNodeStatus.add(node);
//                        }
//                    }
                    //todo 现在又改为直接取消节点的展示，不再是提供禁用方案。且目前仍然只能通过组织名字来判断组织的类型
                    if (parent.getParentId() == 0){
                        //非内设机构和非机构领导才会被加入到树中
                        if ("内设机构".equals(node.getName()) || "机构领导".equals(node.getName()) || "直属机构".equals(node.getName())){
                            log.info("节点{}下的子节点{}被过滤",Optional.ofNullable(parent.getName()).orElse(""),node.getName());
                        }
                        else {
                            parent.getChildren().add(node);
                        }
                    }
                    else {
                        parent.getChildren().add(node);
                    }
                }
            }
        });
        //需要修改状态的分支
//        if (!CollectionUtils.isAnyEmpty(changeBranchStatus)){
//            changeBranchStatus.forEach( node -> DeptTreeVO.setSelfAndChildrenSelectable(node,false));
//        }
//        //需要修改状态的节点
//        if (!CollectionUtils.isAnyEmpty(changeNodeStatus)){
//            changeNodeStatus.forEach( node -> node.setIsSelectable(false));
//        }//todo 产品又说这里先不做可选禁用了
        return root;
    }

    /**
     * 获得最大层级限制的组织数
     * @param node
     * @param currentLevel
     * @param maxLevel
     * @return
     */
    public static DeptTreeVO limitMaxDepth(DeptTreeVO node, int currentLevel, int maxLevel) {
        if (currentLevel >= maxLevel || node == null) {
            return null;
        }
        DeptTreeVO newNode = new DeptTreeVO();
        newNode.setName(node.getName());
        newNode.setId(node.getId());
        newNode.setSort(node.getSort());
        newNode.setParentId(node.getParentId());
        newNode.setIsSelectable(node.getIsSelectable());
        newNode.setChildren(new ArrayList<>());
        newNode.setUser(true);
        if (node.getChildren() != null){
            node.getChildren().forEach( child -> {
                DeptTreeVO newChild = limitMaxDepth(child, currentLevel + 1, maxLevel);
                if (newChild != null) {
                    newNode.getChildren().add(newChild);
                    newNode.setUser(false);
                }
            });
        }
        return newNode;
    }

    @Override
    public List<Long> getDeptTreeId(List<Long> result,List<DeptTreeVO> treeDept) {
        for(DeptTreeVO deptTreeVO:treeDept){
            result.add(deptTreeVO.getId());
            if(Objects.nonNull(deptTreeVO.getChildren())){
                getDeptTreeId(result,deptTreeVO.getChildren());
            }
        }
        return result;
    }

    @Override
    public List<DeptDO> selectChildrenDeptByDeptName(String deptName) {
        DeptDO deptDO = deptMapper.selectOne(DeptDO::getName, deptName);
        if (Objects.isNull(deptDO)) {
            return Collections.emptyList();
        }
        List<DeptDO> deptDOS = deptMapper.selectChildrenDept(deptDO.getId());
        return deptDOS;
    }

    /**
     * 补全这些部门id的所有子部门
     * @param deptIds 部门id集合
     * @return 包含所有子部门的部门id集合
     */
    @Override
    public Set<Long> completeChildDeptIdFromCache(Set<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return deptIds;
        }
        List<Long> deptIdList = CollUtil.list(false, deptIds);
        for (int i = 0; i < deptIdList.size(); i++) {
            Long deptId = deptIdList.get(i);
            Collection<DeptDO> temps = parentDeptCache.get(deptId);
            if (CollUtil.isNotEmpty(temps)) {
                deptIdList.addAll(temps.stream().map(DeptDO::getId).collect(Collectors.toList()));
            }
        }
        return CollUtil.newHashSet(deptIdList);
    }

    /**
     * 获取用户有数据权限的组织id列表
     * @return 组织id列表
     */
    @Override
    @DataPermission(enable = false)
    public Set<Long> getDataPermissionDepts() {
        // 1、本部门数据权限或仅本人数据权限，返回本部门及以下数据权限的组织
        // 获取系统标识
        String clientCode = StrUtil.blankToDefault(WebFrameworkUtils.getClientCode(),CLIENT_CODE);
        if (permissionService.checkDeptDataPermissionDeptOnlyOrSelf(getLoginUserId(),clientCode)) {
            AdminUserDO userDO = userMapper.selectById(getLoginUserId());
            Set<Long> result = completeChildDeptIdFromCache(CollUtil.newHashSet(userDO.getDeptId()));
            // 过滤掉已经禁用的组织
            result.removeIf(r -> Objects.equals(CommonStatusEnum.DISABLE.getStatus(),deptCache.get(r).getStatus()));
            return result;
        }

        // 2、其他数据权限，正常获取
        // 获得组织列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> deptList = deptMapper.selectList(reqVO);
        // 临时组织，不展示给用户
        deptList.removeIf(d -> Objects.equals(d.getId(),DeptIdEnum.TEMP.getId()));
        return deptList.stream().map(DeptDO::getId).collect(Collectors.toSet());
    }

    /**
     * 递归查询部门id的所有父部门信息
     * @param map 修改后的父部门缓存
     * @param deptId 部门id
     * @param result 结果集
     * @return 结果集
     */
    private void getParentDeptFromCache(Map<Long,Collection<Long>> map,Long deptId,List<DeptDO> result) {
        if (deptId == 0) {
            return;
        }
        map.forEach((parentId,deptIdList) -> {
            if (deptIdList.contains(deptId) && parentId != 0) {
                result.add(deptCache.get(parentId));
                getParentDeptFromCache(map,parentId,result);
            }
        });
    }

    /**
     * 补齐父级组织到根组织
     * @param deptList 组织集合
     */
    private void repairParentsDept(List<DeptDO> deptList) {
        // 补全父级组织
        if (CollUtil.isNotEmpty(deptList)) {
            for (int i = 0; i < deptList.size(); i++) {
                DeptDO deptDO = deptList.get(i);
                if (Objects.equals(DeptIdEnum.ROOT.getId(),deptDO.getParentId())) {
                    // 已经是根组织了
                    continue;
                }
                DeptDO parent = getParentFromCache(deptDO);
                // 避免插入重复的组织
                if (Objects.nonNull(parent) && deptList.stream().noneMatch(d -> Objects.equals(d.getId(),parent.getId()))) {
                    deptList.add(parent);
                }
            }
        }
    }

    @Override
    public Long getTenantIdByDeptId(Long deptId){
        return getDept(deptId).getTenantId();
    }

    @Override
    public DeptDO getByLeaderUserId(Long leaderUserId) {

        return deptMapper.selectByLeaderUserId(leaderUserId);

    }

    @Override
    public Long getDeptIdByDeptName(String deptName) {
        LambdaQueryWrapperX<DeptDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(DeptDO::getName, deptName);
        lambdaQueryWrapperX.eq(DeptDO::getTenantId, 25L);
        lambdaQueryWrapperX.last(" limit 1 ");
        DeptDO deptDO = deptMapper.selectOne(lambdaQueryWrapperX);
        return deptDO == null ? null : deptDO.getId();
    }

    @Override
    public Long getDeptIdByUserId(Long id) {

        return deptMapper.getDeptIdByUserId(id);
    }

    @Override
    public List<DeptRespDTO> getListByLeader(Long leaderUserId) {
        LambdaQueryWrapperX<DeptDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(DeptDO::getLeaderUserId, leaderUserId);
        List<DeptDO> deptList = deptMapper.selectList(lambdaQueryWrapperX);
        return DeptConvert.INSTANCE.convertList03(deptList);
    }

    @Override
    public void sendAllDeptInfo(String url) {

        List<DeptDTO> list = getalllistdept(url);

//        List<DeptDTO> list = deptMapper.selectSimpleDeptInfoList();
        log.info("打印全量发送的部门数据-------"+JSONUtil.toJsonStr(list));
        String resp = HttpUtil.post(url+"/system/yzAddition/department", JSONUtil.toJsonStr(list));
        log.info("全量发送部门数据完毕-------"+resp);
    }

    @Override
    public List<DeptDTO> getalllistdept(String url){
        DeptListReqVO reqVO = new DeptListReqVO();
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> allDept = deptMapper.selectList(reqVO);
        // 补齐父级组织
        repairParentsDept(allDept);

        // 排序，保证组织的有序性
        allDept.sort(Comparator.comparing(DeptDO::getSort));
        // 构建组织树
        // 使用 LinkedHashMap 的原因，是为了排序 。实际也可以用 Stream API ，就是太丑了。
        Map<Long, DeptTreeVO> treeNodeMap = new LinkedHashMap<>();
        allDept.forEach(dept -> treeNodeMap.put(dept.getId(), DeptConvert.INSTANCE.convertTree(dept)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getParentId().equals(0L)).forEach(childNode -> {
            // 获得父节点
            DeptTreeVO parentNode = treeNodeMap.get(childNode.getParentId());
            if (parentNode == null) {
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });

        List<DeptDTO> list = new ArrayList<>();

        // 获得到所有的根节点
        List<DeptTreeVO> treeList = CollectionUtils.filterList(treeNodeMap.values(), node -> DeptEnum.ROOT.getId().equals(node.getParentId()));
        treeList.forEach(d->{
            DeptDTO deptDTO = DeptConvert.INSTANCE.convertDeptDTO(d);
            list.add(deptDTO);
            getDeptDTOList(list, d);
        });

        list.forEach(d->{
            Long tenantId = getDept(Long.parseLong(d.getNew_dept_id())).getTenantId();
            d.setTenant_id(tenantId.toString());
            TenantDO tenant = tenantService.getTenant(tenantId);
            if(Objects.nonNull(tenant)){
                d.setTenant_code(tenant.getTenantCode());
            }
        });
        return list;
    }

    private void getDeptDTOList(List<DeptDTO> list, DeptTreeVO dept) {
        if(CollUtil.isNotEmpty(dept.getChildren())){
            dept.getChildren().forEach(child->{
                DeptDTO childDTO = DeptConvert.INSTANCE.convertDeptDTO(child);
                list.add(childDTO);
                getDeptDTOList(list, child);
            });
        }
    }
}
