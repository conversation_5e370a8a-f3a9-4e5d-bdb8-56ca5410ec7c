package com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Set;
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("管理后台 - 角色组更新 Request VO")

public class RoleGroupUpdateReqVO extends RoleGroupBaseVO{

    @ApiModelProperty(value = "角色组编号", required = true, example = "1024")
    @NotNull(message = "角色组编号不能为空")
    private Long id;


    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "角色", example = "[1,2]")
    private Set<Long> roleIds;
}
