package com.unicom.swdx.module.system.dal.redis.businesscenter;

import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.module.system.dal.redis.RedisKeyConstants.BUSINESS_CENTER_DATA;

/**
 * 业务中台信息 的 RedisDAO
 *
 * <AUTHOR>
 */
@Repository
public class BusinessCenterRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // 缓存默认过期时间 180s（同步延迟时间）
    @Value("${businesscenter.default-cache-expire-time:180}")
    private long DEFAULT_EXPIRE_SECONDS;

    // 默认过期时间单位秒
    private static final TimeUnit DEFAULT_EXPIRE_UNIT = TimeUnit.SECONDS;

    public String get(String key) {
        String redisKey = formatKey(key);
        return stringRedisTemplate.opsForValue().get(redisKey);
    }

    public void set(String key, String value) {
        String redisKey = formatKey(key);
        stringRedisTemplate.opsForValue().set(redisKey, value, DEFAULT_EXPIRE_SECONDS, DEFAULT_EXPIRE_UNIT);
    }

    public void set(String key, String value, long timeout) {
        String redisKey = formatKey(key);
        stringRedisTemplate.opsForValue().setIfAbsent(redisKey, value, timeout, DEFAULT_EXPIRE_UNIT);
    }

    public void set(String key, String value, long timeout, TimeUnit unit) {
        String redisKey = formatKey(key);
        stringRedisTemplate.opsForValue().set(redisKey, value, timeout, unit);
    }

    public void delete(String key) {
        String redisKey = formatKey(key);
        stringRedisTemplate.delete(redisKey);
    }

    public void deleteList(Collection<String> keyList) {
        List<String> redisKeys = CollectionUtils.convertList(keyList, BusinessCenterRedisDAO::formatKey);
        stringRedisTemplate.delete(redisKeys);
    }

    private static String formatKey(String key) {
        return String.format(BUSINESS_CENTER_DATA.getKeyTemplate(), key);
    }

}
