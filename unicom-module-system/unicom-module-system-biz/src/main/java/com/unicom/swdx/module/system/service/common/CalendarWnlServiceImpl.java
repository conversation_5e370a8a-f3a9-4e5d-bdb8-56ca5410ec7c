package com.unicom.swdx.module.system.service.common;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.system.dal.dataobject.calendarwnl.CalendarWnlDO;
import com.unicom.swdx.module.system.dal.mysql.calendarwnl.CalendarWnlMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
@Slf4j
public class CalendarWnlServiceImpl extends ServiceImpl<CalendarWnlMapper, CalendarWnlDO> implements CalendarWnlService {

    /**
     * 获得指定日期范围的日历信息
     * @param startDate 起始日期
     * @param endDate 结束日期
     */
    @Override
    public List<CalendarWnlDO> getCalendarInRange(LocalDate startDate, LocalDate endDate) {
        return lambdaQuery().between(CalendarWnlDO::getGregorianDate,startDate,endDate).list();
    }
}
