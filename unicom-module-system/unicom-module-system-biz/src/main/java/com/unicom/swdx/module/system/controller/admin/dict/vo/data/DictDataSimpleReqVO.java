package com.unicom.swdx.module.system.controller.admin.dict.vo.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/2/13 16:58
 **/
@Data
public class DictDataSimpleReqVO {

    @ApiModelProperty(value = "字典类型", example = "gender", required = true)
    @NotBlank(message = "字典类型不能为空")
    private String dictType;

}
