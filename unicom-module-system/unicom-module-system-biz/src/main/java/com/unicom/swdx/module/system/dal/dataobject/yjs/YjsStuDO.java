package com.unicom.swdx.module.system.dal.dataobject.yjs;


import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;


/**
 * 学生基本信息 DO
 *
 * <AUTHOR>
 */
@TableName("t_student_basic_info")
@KeySequence("t_student_basic_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YjsStuDO  {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private String id;
    /**
     * 考生号
     */
    private String examineNumber;
    /**
     * 年级
     */
    private String grade;
    /**
     * 学号
     */
    private String studentNumber;
    /**
     * 姓名
     */
    private String stuName;
    /**
     * 性别(1:男, 0女)
     */
    private String sex;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 电子信箱
     */
    private String email;
    /**
     * 出生日期
     */
    private LocalDateTime dateOfBirth;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 国家地区
     */
    private String country;
    /**
     * 籍贯
     */
    private String nativePlace;
    /**
     * 个人照片
     */
    private String personalPhoto;
    /**
     * 政治面貌
     */
    private String politicalOutlook;
    /**
     * 民族
     */
    private String nation;
    /**
     * 入团时间
     */
    private LocalDateTime leagueTime;
    /**
     * 入党时间
     */
    private LocalDateTime communistPartyTime;
    /**
     * 所属党支部
     */
    private String partyBranch;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * QQ
     */
    private String penguin;
    /**
     * 微信
     */
    private String wechat;
    /**
     * 联系电话
     */
    private String contactNumber;
    /**
     * 专业代码
     */
    private String professionalCode;
    /**
     * 专业
     */
    private String majorType;
    /**
     * 院系所
     */
    private String collegeFrom;
    /**
     * 系所号
     */
    private String collegeFromNumber;
    /**
     * 班级id
     */
    private String classId;
    /**
     * 班号
     */
    private String classNumber;
    /**
     * 层次
     */
    private String arrangement;
    /**
     * 学习形式
     */
    private String learningForm;
    /**
     * 学制
     */
    private String schoolSystem;
    /**
     * 入学日期
     */
    private LocalDateTime admissionDate;
    /**
     * 预计毕业时间
     */
    private LocalDateTime expectedCompletionDate;
    /**
     * 毕业证书编号
     */
    private String diplomaNo;
    /**
     * 学生类型
     */
    private String stuType;
    /**
     * 毕业审查结果
     */
    private String examineResult;
    /**
     * 学籍状态（ZC:正常,LJ:留级,TX:退学,XX:休学,BY:毕业）
     */
    private String studentStatus;
    /**
     * 批次编号
     */
    private String batchNo;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 审核id
     */
    private String bpmId;
    /**
     * 审核状态(1:审核中,3:未通过5:已通过 '9':暂无审核)
     */
    private String bpmStatus;
    /**
     * 创建用户id
     */
    private String createUserId;
    /**
     * 创建用户名
     */
    private String createUserName;
    /**
     * 更新用户
     */
    private String updateUserId;
    /**
     * 更新用户名
     */
    private String updateUserName;
    /**
     * 毕业院校
     */
    private String graduateSchool;
    /**
     * 是否农村学生(1:是,0:否)
     */
    private String isRural;
    /**
     * 培养方案
     */
    private String trainingId;
    /**
     * 培养方式(XS:学术型 ZY:专业型)
     */
    private String trMode;
    /**
     * 编号
     */
    private String stuNo;
    /**
     * 户口所在地
     */
    private String huKou;
    /**
     * 毕业年月
     */
    private LocalDateTime graduationDate;
    /**
     * 毕业专业名称
     */
    private String graduationMajorName;
    /**
     * 毕业专业代码
     */
    private String graduationMajorCode;
    /**
     * 毕业院校代码
     */
    private String graduateSchoolCode;
    /**
     * 文化程度
     */
    private String culture;
    /**
     * 状态(ZC:正常，Y:启用【学生能修改字段】，N:禁用【学生不能修改字段】)
     */
    private String status;
    /**
     * 学籍状态
     */
    private String unusualStatus;
    /**
     * 学籍异动
     */
    private String logoutStatus;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 国家或地区码
     */
    private String gbm;
    /**
     * 国家或地区
     */
    private String gb;
    /**
     * 考试方式码
     */
    private String ksfsm;
    /**
     * 考试方式
     */
    private String ksfs;
    /**
     * 学院单位
     */
    private String collegeCompany;
    /**
     * 身份id
     */
    private String commonId;
    /**
     * 大学几年级
     */
    private String universityGrade;
    /**
     * 学生类型
     */
    private String studentType;
    /**
     * 学习阶段
     */
    private String studyStage;
    /**
     * 专业大类
     */
    private String majorBroadHeading;
    /**
     * 学位类型
     */
    private String academicType;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 银行卡号
     */
    private String bankCard;
    /**
     * 身份证件类型
     */
    private String idCardType;
    /**
     * 导师id
     */
    private String tutorId;
    /**
     * 录取信息表的id
     */
    private String examInfoId;

}
