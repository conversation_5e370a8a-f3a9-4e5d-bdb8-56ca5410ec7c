package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

@ApiModel("管理后台 - 消息授权分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MessageAuthorityPageReqVO extends PageParam {

    @ApiModelProperty(value = "姓名", example = "sk")
    @Length(min = 0,max = 30,message = "模板名称字数限制")
    private String name;

}
