package com.unicom.swdx.module.system.dal.mysql.shortcut;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutRespVO;
import com.unicom.swdx.module.system.dal.dataobject.shortcut.ShortcutUserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 快捷入口用户关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ShortcutUserMapper extends BaseMapperX<ShortcutUserDO> {
    List<ShortcutRespVO> selectListByUserId(Long userId);

    default void deleteByShortcutIds(Collection<Long> shortcutIds) {
        delete(new LambdaQueryWrapperX<ShortcutUserDO>()
                .inIfPresent(ShortcutUserDO::getShortcutId,shortcutIds));
    }

    default void deleteByUserAndShortcutIds(Long userId, Collection<Long> deleteIds) {
        delete(new LambdaQueryWrapperX<ShortcutUserDO>()
                .eq(ShortcutUserDO::getUserId,userId)
                .in(ShortcutUserDO::getShortcutId,deleteIds)
        );
    }

}
