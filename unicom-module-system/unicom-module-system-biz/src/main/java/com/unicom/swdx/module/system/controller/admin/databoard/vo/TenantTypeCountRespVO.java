package com.unicom.swdx.module.system.controller.admin.databoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("管理后台 - 机构类型统计 Response VO")
public class TenantTypeCountRespVO {
    @ApiModelProperty(value = "机构类型名")
    private String typeName;

    @ApiModelProperty(value = "总数")
    private Long num;

    @ApiModelProperty(value = "百分比")
    private Float percent;
}
