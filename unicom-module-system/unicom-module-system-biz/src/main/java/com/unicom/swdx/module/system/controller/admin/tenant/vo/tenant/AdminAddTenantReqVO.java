package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

@ApiModel("管理后台 - 管理员添加机构 Request VO")
@Data
public class AdminAddTenantReqVO {
    @ApiModelProperty(value = "单位类型", required = true, example = "1")
    @NotNull(message = "单位类型不能为空")
    private Integer companyType;

    @ApiModelProperty(value = "机构全称", required = true, example = "测试体育俱乐部")
    @NotNull(message = "机构全称不能为空")
    @Length(min = 0,max = 30,message = "机构全称不能超过30个字符")
    private String tenantName;

    @ApiModelProperty(value = "统一社会信用代码",required = true,example = "sport4541210144")
    @NotNull(message = "统一社会信用代码不能为空")
    @Length(min = 0,max = 100,message = "统一社会信用代码不能超过100个字符")
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "注册地区划",required = false)
    //@Length(min = 0,max = 100,message = "注册地区划不能超过100个字符")
    private List<Long> registerRegion;

    @ApiModelProperty(value = "注册地地址",required = false,example = "湖南长沙")
    @Length(min = 0,max = 200,message = "注册地地址不能超过200个字符")
    private String registerAddress;


    @ApiModelProperty(value = "所在地区划",required = true)
    @NotNull(message = "所在地划不能为空")
    //@Length(min = 0,max = 100,message = "所在地区划不能超过100个字符")
    private List<Long> locationRegion;


    @ApiModelProperty(value = "所在地地址",required = true,example = "湖南长沙")
    @NotNull(message = "所在地地址不能为空")
    @Length(min = 0,max = 100,message = "所在地地址不能超过100个字符")
    private String locationAddress;


    @ApiModelProperty(value = "法定代表人姓名",required = true,example = "张三")
    @NotNull(message = "法定代表人姓名不能为空")
    @Length(min = 0,max = 100,message = "法定代表人姓名不能超过100个字符")
    private String legalRepresentativeName;


    @ApiModelProperty(value = "法人代表身份证号码",required = true,example = "****************")
    @NotNull(message = "法人代表身份证号码不能为空")
    @Length(min = 0,max = 100,message = "法人代表身份证号码不能超过100个字符")
    private String legalRepresentativeIdCard;


    @ApiModelProperty(value = "机构用户类型", required = true, example = "1")
    @NotNull(message = "机构用户类型不能为空")
    private List<Long> instUserType;

    @ApiModelProperty(value = "营业执照", required = true, example = "soccer.club/index")
    @NotNull(message = "营业执照不能为空")
    @Length(min = 0,max = 300,message = "营业执照不能超过300个字符")
    private String businessLicenseUrl;


    @ApiModelProperty(value = "机构管理人姓名",required = true,example = "张三")
    @NotNull(message = "机构管理人姓名不能为空")
    @Length(min = 0,max = 100,message = "机构管理人姓名姓名不能超过100个字符")
    private String contactNickname;


    @ApiModelProperty(value = "机构管理员手机号码（联系手机）",required = true,example = "***********")
    @NotNull(message = "机构管理员手机号码（联系手机）不能为空")
    @Length(min = 0,max = 100,message = "机构管理员手机号码（联系手机）不能超过100个字符")
    private String contactMobile;


    @ApiModelProperty(value = "用户名（原联系人）", required = true,example = "skyline")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String contactName;

    @ApiModelProperty(value = "用户密码", required = true,example = "Az_7788@444d")
    @NotNull(message = "用户密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String contactPassword;

    @ApiModelProperty(value = "机构状态",required = true,example = "1")
    @NotNull(message = "机构状态不能为空")
    private Integer status;


    @ApiModelProperty(value = "过期时间",required = false)
    private LocalDateTime expireTime;



}
