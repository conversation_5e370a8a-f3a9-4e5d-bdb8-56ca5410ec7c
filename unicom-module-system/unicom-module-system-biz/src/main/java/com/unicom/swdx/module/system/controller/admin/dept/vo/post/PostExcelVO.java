package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.framework.excel.core.convert.DisplayStateConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 岗位 Excel 导出响应 VO
 */
@Data
public class PostExcelVO {

    @ExcelProperty("岗位编号")
    private Long id;

    @ExcelProperty("岗位标识")
    private String code;

    @ExcelProperty("岗位名称")
    private String name;

    @ExcelProperty("岗位排序")
    private Integer sort;

//    @ExcelProperty(value = "所属机构名称")
//    private String tenant;

    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private String status;

    @ExcelProperty(value = "是否展示", converter = DisplayStateConvert.class)
    private Boolean displayState;

    @ExcelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ExcelProperty(value = "备注")
    private String remark;
}
