package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveMobileSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@ApiModel(value = "管理后台 - 用户分页时的信息 Response VO", description = "相比用户基本信息来说，会多部门信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserPageItemRespVO extends UserRespVO {

    /**
     * 所在组织
     */
    private Dept dept;

    @ApiModel("组织")
    @Data
    public static class Dept {

        @ApiModelProperty(value = "组织编号", required = true, example = "1")
        private Long id;

        @ApiModelProperty(value = "组织名称", required = true, example = "研发部")
        private String name;

    }
}
