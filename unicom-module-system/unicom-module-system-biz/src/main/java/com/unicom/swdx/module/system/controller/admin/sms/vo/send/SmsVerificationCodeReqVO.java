package com.unicom.swdx.module.system.controller.admin.sms.vo.send;

import com.unicom.swdx.framework.common.validation.Mobile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/7/28 15:38
 **/
@Data
@ApiModel("管理后台 - 验证码短信 Request VO")
public class SmsVerificationCodeReqVO {

    @ApiModelProperty("手机号")
    @Mobile
    private String mobile;

    @ApiModelProperty("验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;

    @ApiModelProperty("用于具体事项")
    @NotBlank(message = "用于具体事项不能为空")
    private String item;

}
