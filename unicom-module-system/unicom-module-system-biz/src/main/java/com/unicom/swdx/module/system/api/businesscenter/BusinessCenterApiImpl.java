package com.unicom.swdx.module.system.api.businesscenter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.system.api.businesscenter.dto.DeptSimpleRespDTO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalRespVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import com.unicom.swdx.module.system.convert.businesscenter.BusinessCenterConvert;
import com.unicom.swdx.module.system.service.businesscenter.BusinessCenterService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.connection.RedisServer;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class BusinessCenterApiImpl implements BusinessCenterApi {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private BusinessCenterService businessCenterService;

    /**
     * 获取业中部门列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    @Override
    public CommonResult<List<DeptSimpleRespDTO>> getDept(Long tenantId, Integer type, String token) {
        try {
            List<DeptSimpleRespVO> deptSimpleRespDTOS = businessCenterService.getDept(tenantId, type, token);
            return success(BusinessCenterConvert.INSTANCE.convertList(deptSimpleRespDTOS));
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<List<DeptSimpleRespDTO>> getDeptAllChildren(Long tenantId, Integer type, Long deptId, String token) {
        try {
            List<DeptSimpleRespVO> deptSimpleRespDTOS = businessCenterService.getDeptAllChildren(tenantId, type, deptId, token);
            return success(BusinessCenterConvert.INSTANCE.convertList(deptSimpleRespDTOS));
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<List<Long>> getUserDeptPermissionByToken(Long tenantId, Integer type, String token) {
        try {
            List<Long> deptIds = businessCenterService.getUserDeptPermissionByToken(tenantId, type, token);
            return success(deptIds);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<List<Long>> getUserDeptPermissionByUserId(Long tenantId, Integer type, Long userId, String token) {
        try {
            List<Long> deptIds = businessCenterService.getUserDeptPermissionByUserId(tenantId, type, userId, token);
            return success(deptIds);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<String> getTokenOfBusinessCenter(String tokenOfEdu) {
        return success(businessCenterService.getOldToken(tokenOfEdu));
    }

    @Override
    public CommonResult<List<PersonnalRespDTO>> getPersonnal(Long tenantId, String token) {

//        if(StrUtil.isEmpty(token)){
            try {
                List<BusinessCenterPersonnalRespVO> personRespDTOS = businessCenterService.getPersonnalList(tenantId, token);
                return success(BusinessCenterConvert.INSTANCE.convertPersonList(personRespDTOS));
            } catch (Exception e) {

            }
//        }

        String json = redisUtil.get("BusinessCenter::getPersonnal:" + tenantId).toString();

        JSONArray jsonArray = JSONUtil.parseArray(json);

        List<BusinessCenterPersonnalRespVO> list = JSONUtil.toList(jsonArray, BusinessCenterPersonnalRespVO.class);

        return success(BusinessCenterConvert.INSTANCE.convertPersonList(list));
    }

    @Override
    public CommonResult<List<Long>> getAdminInfo(Long roleId,String token) {
        try {
            List<Long> userIds = businessCenterService.getAdminInfo(roleId,token);
            return success(userIds);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
