package com.unicom.swdx.module.system.convert.logger;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.logger.dto.LoginLogCreateReqDTO;
import com.unicom.swdx.module.system.controller.admin.logger.vo.loginlog.LoginLogExcelVO;
import com.unicom.swdx.module.system.controller.admin.logger.vo.loginlog.LoginLogRespVO;
import com.unicom.swdx.module.system.dal.dataobject.logger.LoginLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface LoginLogConvert {

    LoginLogConvert INSTANCE = Mappers.getMapper(LoginLogConvert.class);

    PageResult<LoginLogRespVO> convertPage(PageResult<LoginLogDO> page);

    List<LoginLogRespVO> convertList(List<LoginLogDO> list);
    List<LoginLogExcelVO> convertExcel(List<LoginLogDO> list);

    LoginLogDO convert(LoginLogCreateReqDTO bean);

}
