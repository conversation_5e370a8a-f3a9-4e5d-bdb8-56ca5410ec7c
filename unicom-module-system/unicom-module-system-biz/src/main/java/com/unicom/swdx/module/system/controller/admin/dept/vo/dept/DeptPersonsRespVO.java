package com.unicom.swdx.module.system.controller.admin.dept.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("管理后台 - 组织人员精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeptPersonsRespVO {

    @ApiModelProperty(value = "编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "名称", required = true, example = "sk")
    private String name;

    @ApiModelProperty(value = "父 ID", required = true, example = "1024")
    private Long parentId;

    @ApiModelProperty(value = "编号", required = true, example = "1024")
    private Boolean isPerson;

    @ApiModelProperty(value = "唯一标识", example = "1024")
    private String uniqueSerial;

    @ApiModelProperty(value = "唯一标识父", example = "1024")
    private String uniqueSerialParent;

    private boolean hasChildren;
}
