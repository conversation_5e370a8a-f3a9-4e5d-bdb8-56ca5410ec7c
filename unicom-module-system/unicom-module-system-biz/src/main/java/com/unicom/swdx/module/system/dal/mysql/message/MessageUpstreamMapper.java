package com.unicom.swdx.module.system.dal.mysql.message;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageRespVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.*;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageUpstreamDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

@Mapper
public interface MessageUpstreamMapper extends BaseMapperX<MessageUpstreamDO> {



    List<MessageUpstreamPageVO> selectMyPage(IPage page, @Param("param") MessageUpstreamPageReqVO reqVO);


    List<MessageUpstreamExportVO> selectExport(@Param("param") MessageUpstreamExportReqVO reqVO);

}
