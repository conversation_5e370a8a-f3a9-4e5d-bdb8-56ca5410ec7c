package com.unicom.swdx.module.system.mq.producer.shortcut;


import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import com.unicom.swdx.module.system.mq.producer.RefreshMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/22 16:51
 **/
@Component
@Slf4j
public class ShortcutRefreshProducer extends AbstractProducer {

    @Async
    public void sendRefreshMessage() {
        log.info("[send][ shortcut 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "shortcutRefresh");
    }

//    @Async
//    public void sendRefreshMessage() {
//        log.info("[send][ shortcut 发送刷新消息]");
////        streamBridge.send("shortcutRefreshNew-out-0", new RefreshMessage("shortcutRefresh"));
//    }
}
