package com.unicom.swdx.module.system.service.message;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageRespVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.*;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageUpstreamDO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageUpstreamDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageUpstreamService extends IService<MessageUpstreamDO> {


    void uplinkMessage(String mobile,String content,String cpMoblie);

    String forwardMessage(String userMoblie,String content,String cpMoblie);

    PageResult<MessageUpstreamPageVO> getMessageUpstreamPage(MessageUpstreamPageReqVO pageReqVO);

    /**
     * 导出人事信息
     */
    List<MessageUpstreamExportVO> getUpstreamExcel(MessageUpstreamExportReqVO reqVO);




//    /**
//     * 创建消息权限
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createMessageAuthority(@Valid MessageAuthorityCreateReqVO createReqVO) ;
//
//    PageResult<MessageUpstreamDO> getMessageAuthorityPage(MessageAuthorityPageReqVO pageReqVO);
//    /**
//     * 获得消息权限
//     *
//     * @param id 编号
//     * @return 消息权限
//     */
//    MessageUpstreamDO getMessageAuthority(Long id);
//
//    /**
//     * 获得消息权限编号
//     *
//     * @param userId 编号
//     * @return 消息权限
//     */
//    Long getMessageAuthorityId(Long userId);
//
//    /**
//     * 更新消息权限
//     *
//     */
//    void updateMessageAuthority(@Valid MessageAuthorityUpdateReqVO updateReqVO);
//    /**
//     * 删除消息权限
//     *
//     * @param id 编号
//     */
//    void deleteMessageAuthority(Long id);



}
