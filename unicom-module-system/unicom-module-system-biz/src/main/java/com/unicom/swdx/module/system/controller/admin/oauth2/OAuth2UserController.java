package com.unicom.swdx.module.system.controller.admin.oauth2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.user.OAuth2UserInfosRespVO;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.user.OAuth2UserUpdateReqVO;
import com.unicom.swdx.module.system.convert.oauth2.OAuth2UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.dept.PostService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.service.user.TraineeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 提供给外部应用调用为主
 *
 * 1. 在 getUserInfo 方法上，添加 @PreAuthorize("@ss.hasScope('user.read')") 注解，声明需要满足 scope = user.read
 * 2. 在 updateUserInfo 方法上，添加 @PreAuthorize("@ss.hasScope('user.write')") 注解，声明需要满足 scope = user.write
 *
 * <AUTHOR>
 */
@Api(tags = "管理后台 - OAuth2.0 用户")
@RestController
@RequestMapping("/system/oauth2/user")
@Validated
@Slf4j
public class OAuth2UserController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private TraineeService traineeService;

    @Resource
    private TenantService tenantService;

    @Resource
    private PermissionService permissionService;

    @GetMapping("/get")
    @ApiOperation("获得用户基本信息")
//    @PreAuthorize("@ss.hasScope('user.read')") //
    public CommonResult<OAuth2UserInfosRespVO> getUserInfo() {
        // 获得用户基本信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        OAuth2UserInfosRespVO resp = OAuth2UserConvert.INSTANCE.convert1(user);
        //增加赋值wxxcxOpenid和wxUnionid
        resp.setWxxcxOpenid(user.getWxxcxOpenid());
        resp.setWxUnionid(user.getOthersystemid());
        resp.setOthersystemid(user.getOthersystemid());
        resp.setUserId(resp.getId().toString());
        // 获得部门信息
        if (user.getDeptId() != null) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            resp.setDeptId(dept.getId());
            resp.setDeptName(dept.getName());
        }
        // 获得租户信息
        if (Objects.nonNull(user.getTenantId())) {
            TenantDO tenant = tenantService.getTenant(user.getTenantId());
            resp.setTenantId(tenant.getId());
            resp.setTenantName(tenant.getName());
            resp.setIsTenantAdmin(Objects.equals(user.getId(),tenant.getContactUserId()));
            resp.setTenantCode(tenant.getTenantCode());
        }
        //如果是教务子系统的学员，还要返回学员的id
        String traineeId=null;
        String othersystemid=user.getOthersystemid();
        if(traineeService.getTraineeUserByUserId(user.getId(),othersystemid)!=null){

            traineeId = traineeService.getTraineeUserByUserId(user.getId(),othersystemid).getTraineeId();
        }else {
            if(traineeService.getTraineeUserByUserId(user.getId())!=null){
                traineeId = traineeService.getTraineeUserByUserId(user.getId()).getTraineeId();
            }
        }
        if(Objects.nonNull(traineeId)){
            //返回学员的id
            resp.setTraineeId(traineeId);
        }

//        // 获得部门信息
//        if (user.getDeptId() != null) {
//            DeptDO dept = deptService.getDept(user.getDeptId());
//            resp.setDept(OAuth2UserConvert.INSTANCE.convert(dept));
//        }
//        // 获得岗位信息
//        if (CollUtil.isNotEmpty(user.getPostIds())) {
//            List<PostDO> posts = postService.getPosts(user.getPostIds());
//            resp.setPosts(OAuth2UserConvert.INSTANCE.convertList(posts));
//        }
        return success(resp);
    }

    @PostMapping("/update")
    @ApiOperation("更新用户基本信息")
    @PreAuthorize("@ss.hasScope('user.write')")
    public CommonResult<Boolean> updateUserInfo(@Valid @RequestBody OAuth2UserUpdateReqVO reqVO) {
        // 这里将 UserProfileUpdateReqVO =》UserProfileUpdateReqVO 对象，实现接口的复用。
        // 主要是，AdminUserService 没有自己的 BO 对象，所以复用只能这么做
        userService.updateUserProfile(getLoginUserId(), OAuth2UserConvert.INSTANCE.convert(reqVO));
        return success(true);
    }

    @GetMapping("/users")
    @ApiOperation("获得租户的所有用户基本信息")
    public CommonResult<List<OAuth2UserInfosRespVO>> getUsers(@RequestParam(value = "client_id",required = false) String clientId) {
        Long tenantId = SecurityFrameworkUtils.getTenantId();
        TenantDO tenant = tenantService.getTenant(tenantId);
        List<AdminUserDO> userDOS = userService.getUsersByTenantId(tenantId);
        if (CollUtil.isEmpty(userDOS)) {
            return success(Collections.emptyList());
        }
        if (StrUtil.isNotBlank(clientId)) {
            userDOS.removeIf(u ->
                    !permissionService.hasAnyRoleByClient(u.getId(),clientId)
            );
        }
        List<OAuth2UserInfosRespVO> resp = OAuth2UserConvert.INSTANCE.convertList1(userDOS);
        resp.forEach(r -> {
            DeptDO dept = deptService.getDept(r.getDeptId());
            r.setDeptId(dept.getId());
            r.setDeptName(dept.getName());
            r.setTenantId(tenant.getId());
            r.setTenantName(tenant.getName());
        });
        return success(resp);
    }


}
