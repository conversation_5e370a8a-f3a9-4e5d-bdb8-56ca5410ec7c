package com.unicom.swdx.module.system.dal.mysql.message;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendGetReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageRespVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendRespGetVO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageSendDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessageSendMapper extends BaseMapperX<MessageSendDO> {

    List<MessageSendRespGetVO> selectGet(@Param("param") MessageSendGetReqVO reqVO);
//List<MessageSendRespGetVO> selectGet(IPage page, @Param("param") MessageSendGetReqVO reqVO);
    List<MessageSendPageRespVO> selectMyPage(IPage page, @Param("param") MessageSendPageReqVO reqVO,
                                             @Param("isAdmin") Boolean isAdmin,@Param("creator") Long creator,
                                             @Param("hasSuperAdmin") Boolean hasSuperAdmin,
                                             @Param("tenantId") Long tenantId,
                                             @Param("nickname")String nickname);
}
