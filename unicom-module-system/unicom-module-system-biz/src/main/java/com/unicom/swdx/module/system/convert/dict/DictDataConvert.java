package com.unicom.swdx.module.system.convert.dict;

import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.system.controller.admin.dict.vo.data.*;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;

@Mapper
public interface DictDataConvert {

    DictDataConvert INSTANCE = Mappers.getMapper(DictDataConvert.class);

    List<DictDataSimpleRespVO> convertList(List<DictDataDO> list);

    DictDataRespVO convert(DictDataDO bean);

    DictDataDO convert(DictDataUpdateReqVO bean);

    DictDataDO convert(DictDataCreateReqVO bean);

    DictDataExcelVO convert(DictDataRespVO bean);

    List<DictDataExcelVO> convertList02(List<DictDataDO> bean);

    DictDataRespDTO convert02(DictDataDO bean);

    List<DictDataRespDTO> convert03(List<DictDataDO> beans);

    DictDataSimpleTreeRespVO convertTreeNode(DictDataDO bean);

    default List<DictDataSimpleTreeRespVO> buildDictDataTree(List<DictDataDO> list) {
        // 排序，保证字典的有序性
        list.sort(Comparator.comparing(DictDataDO::getSort));
        // 构建菜单树
        Map<Long, DictDataSimpleTreeRespVO> treeNodeMap = new LinkedHashMap<>();
        list.forEach(dict -> treeNodeMap.put(dict.getId(), DictDataConvert.INSTANCE.convertTreeNode(dict)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getParentId().equals(0L)).forEach(childNode -> {
            // 获得父节点
            DictDataSimpleTreeRespVO parentNode = treeNodeMap.get(childNode.getParentId());
            if (parentNode == null) {
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });
        // 获得到所有的根节点
        return CollectionUtils.filterList(treeNodeMap.values(), node -> Objects.equals(node.getParentId(),0L));
    }
}
