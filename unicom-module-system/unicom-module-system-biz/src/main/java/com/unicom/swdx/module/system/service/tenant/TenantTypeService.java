package com.unicom.swdx.module.system.service.tenant;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.type.*;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeDO;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantTypeMapper;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 机构用户类型 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantTypeService extends IService<TenantTypeDO> {

    void init();

    /**
     * 创建机构用户类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantType(@Valid TenantTypeCreateReqVO createReqVO);

    /**
     * 更新机构用户类型
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantType(@Valid TenantTypeUpdateReqVO updateReqVO);

    /**
     * 更新机构用户类型状态
     * @param updateReqVO 更新信息
     */
    void updateTenantTypeStatus(TenantTypeUpdateStatusReqVO updateReqVO);

    /**
     * 删除机构用户类型
     *
     * @param id 编号
     */
    void deleteTenantType(Long id);

    /**
     * 获得机构用户类型分页
     *
     * @param pageReqVO 分页查询
     * @return 机构用户类型分页
     */
    PageResult<TenantTypeRespVO> getTenantTypePage(TenantTypePageReqVO pageReqVO);

    /**
     * 获得机构用户类型列表
     * @return 机构用户类型列表
     */
    List<TenantTypeDO> getTenantTypeList();

    /**
     * 根据机构用户类型集合获取角色id集合
     * @param tenantTypeIds 机构用户类型集合
     * @return 角色id集合
     */
    Set<Long> getRoleIdsByTenantTypes(Collection<Long> tenantTypeIds);

    /**
     * 根据机构用户类型id集合获得角色id集合
     * @return 机构用户类型列表
     */
    Set<Long> getRoleIdsByIds(Collection<Long> ids);

}
