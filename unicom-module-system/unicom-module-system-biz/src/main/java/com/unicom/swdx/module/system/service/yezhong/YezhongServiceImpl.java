package com.unicom.swdx.module.system.service.yezhong;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.ObjectUtils;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictTypeDO;
import com.unicom.swdx.module.system.dal.dataobject.message.YzuCreateUserMessage;
import com.unicom.swdx.module.system.service.dict.DictDataService;
import com.unicom.swdx.module.system.service.dict.DictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.unicom.swdx.module.system.dal.dataobject.message.YzuCreateUserMessage.BodyDTO.*;
import com.unicom.swdx.module.system.dal.dataobject.message.YzuCreateUserMessage.*;

/**
 * 调用业务中台创建学员用户
 */
@Service
@Validated
@Slf4j
public class YezhongServiceImpl implements YezhongService {

    @Value("${unicom.mid-base-uri}")
    private String MID_BASE_URI;

    @Value("${unicom.mid-admin-username}")
    private String ADMIN_USERNAME;

    @Value("${unicom.mid-admin-password}")
    private String ADMIN_PASSWORD;

    @Value("${unicom.mid-admin-captcha}")
    private String ADMIN_CAPCHA_VERIFICATION;

    @Resource
    private DictTypeService dictTypeService;

    @Resource
    private DictDataService dictDataService;


    @Resource
    private RedisUtil redisUtil;

    private static  String ADMINTOKEN  = "ADMINTOKEN";

    @Override
    public boolean createStudentUser(StudentDTO studentDTO) {

        //查null
        try {
            ObjectUtils.checkFields(studentDTO);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        HeaderDTO headerDTO = new HeaderDTO();


        TrainClassStudentDTO trainClassStudentDTO = new TrainClassStudentDTO();
        trainClassStudentDTO.setStudentId(studentDTO.getId());
        trainClassStudentDTO.setId(studentDTO.getId());
        trainClassStudentDTO.setClassId(studentDTO.getCurrentClassId());
        trainClassStudentDTO.setStatus("1");


        BodyDTO bodyDTO =  BodyDTO.builder().student(studentDTO).trainClassStudent(trainClassStudentDTO).build();

        YzuCreateUserMessage yzuCreateUserMessage  = YzuCreateUserMessage.builder().header(headerDTO).body(bodyDTO).build();


        AuthLoginRespVO authLoginRespVO =  loginUserAdmin(false);


        HttpResponse response = HttpRequest.get(MID_BASE_URI + "/prod-api/system/user/createStudentUser")
                .header("authorization" , "Bearer "+ authLoginRespVO.getAccessToken())
                .contentType("application/json")  // 指定Content-Type为application/json
                .body(JSONUtil.toJsonStr(yzuCreateUserMessage))
                .execute();


        CommonResult<Boolean> authLoginResp = null;
        try {
            authLoginResp = JSON.parseObject(response.body(),  new TypeReference<CommonResult<Boolean>>() {});
        } catch (Exception e) {
            log.error("获取业中createStudentUser失败：" + e.getMessage());
        }


        if(authLoginResp==null){
            authLoginRespVO = loginUserAdmin(true);

            response = HttpRequest.get(MID_BASE_URI + "/prod-api/system/user/createStudentUser")
                    .header("authorization" , "Bearer "+ authLoginRespVO.getAccessToken())
                    .contentType("application/json")  // 指定Content-Type为application/json
                    .body(JSONUtil.toJsonStr(yzuCreateUserMessage))
                    .execute();

        }

        return true;
    }

    @Override
    public AuthLoginRespVO loginUserAdmin(Boolean refresh) {


        if(refresh==false){
            Object token =  redisUtil.get(ADMINTOKEN);

            if(token!=null){
                return  JSON.parseObject(token.toString(), new TypeReference<CommonResult<AuthLoginRespVO>>() {}).getCheckedData();
            }

        }
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("username", ADMIN_USERNAME);
        paramsMap.put("password", ADMIN_PASSWORD);
        paramsMap.put("captchaVerification", ADMIN_CAPCHA_VERIFICATION);
        String parameter = JSONUtil.toJsonStr(paramsMap);
        HttpResponse response = HttpRequest.post(MID_BASE_URI + "/prod-api/system/auth/login")
                .body(parameter)  // 设置请求体为原始数据
                .contentType("application/json")  // 指定Content-Type为application/json
                .execute();
        CommonResult<AuthLoginRespVO> authLoginResp = null;
        try {
            authLoginResp = JSON.parseObject(response.body(), new TypeReference<CommonResult<AuthLoginRespVO>>() {});
        } catch (Exception e) {
            log.error("获取业中admin token失败：" + e.getMessage());
        }

        redisUtil.set(ADMINTOKEN, response.body() , 60 * 10);
        assert authLoginResp != null;
        AuthLoginRespVO checkedData = authLoginResp.getCheckedData();
        log.info("获取业中admin数据：：{}", checkedData);
        return authLoginResp.getCheckedData();
    }

    @Override
    public void getDictType() {


        AuthLoginRespVO authLoginRespVO =  loginUserAdmin(false);


        HttpResponse response = HttpRequest.get(MID_BASE_URI + "/prod-api/system/dict-type/getallType")
                .header("authorization" , "Bearer "+ authLoginRespVO.getAccessToken())
                .contentType("application/json")  // 指定Content-Type为application/json
                .execute();






        CommonResult<List<DictTypeDO>> authLoginResp = null;
        try {
            authLoginResp = JSON.parseObject(response.body(),  new TypeReference<CommonResult<List<DictTypeDO>>>() {});
        } catch (Exception e) {
            log.error("获取业中getDictTtpe失败：" + e.getMessage());
        }


        if(authLoginResp==null){
            authLoginRespVO = loginUserAdmin(true);

            response = HttpRequest.get(MID_BASE_URI + "/prod-api/system/dict-type/getallType")
                    .header("authorization" , "Bearer "+ authLoginRespVO.getAccessToken())
                    .contentType("application/json")  // 指定Content-Type为application/json
                    .execute();


            authLoginResp = JSON.parseObject(response.body(),  new TypeReference<CommonResult<List<DictTypeDO>>>() {});
        }





        List<DictTypeDO> dictTypeDOList =   authLoginResp.getCheckedData();


        List<DictTypeDO> dictTypeOld =  dictTypeService .getDictTypeList();


        // 计算插入的差集: dictTypeDOList - dictTypeOld
        List<DictTypeDO> toInsert = dictTypeDOList.stream()
                .filter(dict -> dictTypeOld.stream().noneMatch(oldDict -> oldDict.getId().equals(dict.getId())  ))
                .collect(Collectors.toList());

        // 计算删除的差集: dictTypeOld - dictTypeDOList
        List<DictTypeDO> toDelete = dictTypeOld.stream()
                .filter(oldDict -> dictTypeDOList.stream().noneMatch(dict -> dict.getId().equals(oldDict.getId())))
                .collect(Collectors.toList());


        // 计算需要更新的记录：ID相同，但其他字段不同
        List<DictTypeDO> toUpdate = dictTypeDOList.stream()
                .filter(dict -> dictTypeOld.stream()
                        .anyMatch(oldDict -> oldDict.getId().equals(dict.getId()) && !oldDict.equals(dict)))  // 检查非ID字段的差异
                .collect(Collectors.toList());


        // 插入新数据到数据库
        if (!toInsert.isEmpty()) {
            dictTypeService.createDictTypeBatch(toInsert);  // 批量插入操作
        }

        // 删除旧数据从数据库
        if (!toDelete.isEmpty()) {
            List<Long> idsToDelete = toDelete.stream()
                    .map(DictTypeDO::getId)  // 提取ID进行删除
                    .collect(Collectors.toList());
            dictTypeService.deleteDictTypeBatch(idsToDelete);  // 批量删除操作
        }

        // 更新需要更新的数据到数据库
        if (!toUpdate.isEmpty()) {
            dictTypeService.updateDictTypeBatch(toUpdate);  // 批量更新操作
        }

    }

    @Override
    public void getDictData() {

        AuthLoginRespVO authLoginRespVO =  loginUserAdmin(false);

        HttpResponse response = HttpRequest.get(MID_BASE_URI + "/prod-api/system/dict-data/getallData")
                .header("authorization" , "Bearer "+ authLoginRespVO.getAccessToken())
                .contentType("application/json")  // 指定Content-Type为application/json
                .execute();


        CommonResult<List<DictDataDO>> authLoginResp = null;
        try {
            authLoginResp = JSON.parseObject(response.body(),  new TypeReference<CommonResult<List<DictDataDO>>>() {});
        } catch (Exception e) {
            log.error("获取业中getDictData失败：" + e.getMessage());
        }

        if(authLoginResp==null){


            response = HttpRequest.get(MID_BASE_URI + "/prod-api/system/dict-data/getallData")
                    .header("authorization" , "Bearer "+ authLoginRespVO.getAccessToken())
                    .contentType("application/json")  // 指定Content-Type为application/json
                    .execute();

            authLoginResp = JSON.parseObject(response.body(),  new TypeReference<CommonResult<List<DictDataDO>>>() {});

        }


        List<DictDataDO> dictTypeDOList =   authLoginResp.getCheckedData();


        List<DictDataDO> dictDatasOld =  dictDataService.getAllDictDatas();


        // 计算插入的差集: dictTypeDOList - dictTypeOld
        List<DictDataDO> toInsert = dictTypeDOList.stream()
                .filter(dict -> dictDatasOld.stream().noneMatch(oldDict -> oldDict.getId().equals(dict.getId())  ))
                .collect(Collectors.toList());

        // 计算删除的差集: dictTypeOld - dictTypeDOList
        List<DictDataDO> toDelete = dictDatasOld.stream()
                .filter(oldDict -> dictTypeDOList.stream().noneMatch(dict -> dict.getId().equals(oldDict.getId())))
                .collect(Collectors.toList());


        // 计算需要更新的记录：ID相同，但其他字段不同
        List<DictDataDO> toUpdate = dictTypeDOList.stream()
                .filter(dict -> dictDatasOld.stream()
                        .anyMatch(oldDict -> oldDict.getId().equals(dict.getId()) && !oldDict.equals(dict)))  // 检查非ID字段的差异
                .collect(Collectors.toList());


        // 插入新数据到数据库
        if (!toInsert.isEmpty()) {
            dictDataService.createDictTypeBatch(toInsert);  // 批量插入操作
        }

        // 删除旧数据从数据库
        if (!toDelete.isEmpty()) {
            List<Long> idsToDelete = toDelete.stream()
                    .map(DictDataDO::getId)  // 提取ID进行删除
                    .collect(Collectors.toList());
            dictDataService.deleteDictTypeBatch(idsToDelete);  // 批量删除操作
        }

        // 更新需要更新的数据到数据库
        if (!toUpdate.isEmpty()) {
            dictDataService.updateDictTypeBatch(toUpdate);  // 批量更新操作
        }


    }
}
