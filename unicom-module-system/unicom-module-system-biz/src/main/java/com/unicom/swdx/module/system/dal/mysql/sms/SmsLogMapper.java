package com.unicom.swdx.module.system.dal.mysql.sms;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.sms.vo.log.SmsLogExportReqVO;
import com.unicom.swdx.module.system.controller.admin.sms.vo.log.SmsLogPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.sms.SmsLogDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SmsLogMapper extends BaseMapperX<SmsLogDO> {

    default PageResult<SmsLogDO> selectPage(SmsLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SmsLogDO>()
                .betweenIfPresent(SmsLogDO::getCreateTime,reqVO.getCreateTime())
                .likeIfPresent(SmsLogDO::getTemplateCode,reqVO.getTemplateId())
                .eqIfPresent(SmsLogDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(SmsLogDO::getSendStatus, reqVO.getSendStatus())
                .betweenIfPresent(SmsLogDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(SmsLogDO::getReceiveStatus, reqVO.getReceiveStatus())
                .betweenIfPresent(SmsLogDO::getReceiveTime, reqVO.getReceiveTime())
                .and(StrUtil.isNotBlank(reqVO.getMobile()), l -> l.like(SmsLogDO::getMobile,
                        reqVO.getMobile()).or().like(SmsLogDO::getTemplateId,reqVO.getMobile()))
                .orderByDesc(SmsLogDO::getId));
    }

    default List<SmsLogDO> selectList(SmsLogExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SmsLogDO>()
                .betweenIfPresent(SmsLogDO::getCreateTime,reqVO.getCreateTime())
                .likeIfPresent(SmsLogDO::getTemplateCode,reqVO.getTemplateId())
                .eqIfPresent(SmsLogDO::getChannelId, reqVO.getChannelId())
                .likeIfPresent(SmsLogDO::getMobile, reqVO.getMobile())
                .eqIfPresent(SmsLogDO::getSendStatus, reqVO.getSendStatus())
                .betweenIfPresent(SmsLogDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(SmsLogDO::getReceiveStatus, reqVO.getReceiveStatus())
                .betweenIfPresent(SmsLogDO::getReceiveTime, reqVO.getReceiveTime())
                .and(StrUtil.isNotBlank(reqVO.getMobile()), l -> l.like(SmsLogDO::getMobile,
                        reqVO.getMobile()).or().like(SmsLogDO::getTemplateId,reqVO.getMobile()))
                .orderByDesc(SmsLogDO::getId));
    }

}
