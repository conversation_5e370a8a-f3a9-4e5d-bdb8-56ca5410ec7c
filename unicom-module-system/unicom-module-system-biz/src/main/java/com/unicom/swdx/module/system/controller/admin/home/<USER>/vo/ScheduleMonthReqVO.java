package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import com.unicom.swdx.framework.common.util.date.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@ApiModel("业务中台 - 按月获取日程简介 Request VO")
@Data
public class ScheduleMonthReqVO {

    @ApiModelProperty(value = "年月")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate yearMonth;

}
