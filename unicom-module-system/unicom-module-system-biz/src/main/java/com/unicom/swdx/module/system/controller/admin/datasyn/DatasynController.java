package com.unicom.swdx.module.system.controller.admin.datasyn;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.datasyn.vo.DataDeptRespVO;
import com.unicom.swdx.module.system.controller.admin.datasyn.vo.OldDeptVo;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptListReqVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptRespVO;
import com.unicom.swdx.module.system.convert.dept.DeptConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.OldDeptDO;
import com.unicom.swdx.module.system.dal.mysql.dept.OldDeptMapper;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.dept.OldDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.GET;

@Api(tags = "管理后台 - 全量数据同步")
@RestController
@RequestMapping("/system/datasyn")
@Validated
@Slf4j
public class DatasynController {

    @Resource
    private DeptService deptService;


    @Resource
    private OldDeptService oldDeptService;

    @GetMapping("/dept")
    @ApiOperation("获取全量数据部门列表")
    @OperateLog(type = GET)
    public CommonResult<List<OldDeptVo>> getList(DeptListReqVO reqVO) {

        //新的部门集合
        List<DeptRespVO> deptsList = deptService.getDeptsList(reqVO);

        List<DataDeptRespVO> result=  DeptConvert.INSTANCE.convert13(deptsList);







        if(reqVO.getTenantId().intValue()==25){

            DeptRespVO xueyuan =   deptsList.stream().filter(it->it.getName().equals("学员")).findFirst().orElse(null);
            DeptRespVO yanjiusheng =   deptsList.stream().filter(it->it.getName().equals("研究生")).findFirst().orElse(null);
            //去掉业务中台的学员和研究生






            //老的部门集合
            List<OldDeptDO>  oldDeptDOList =  oldDeptService.list();


            result.forEach(it->{

                OldDeptDO oldDeptDO =  oldDeptDOList.stream().filter(a-> a.getDeptId() ==  it.getId() ).findFirst().orElse(null);

                if(oldDeptDO!=null){
                    it.setOldid(oldDeptDO.getOldDeptId());
                    it.setOldparentId(oldDeptDO.getParentDeptId());
                }else{
                    it.setOldid(it.getId()+"");

                    if(it.getParentId().equals("106") ){
                        it.setOldparentId("001001000000"); //新增的机构放入党校下边的放到在职人员
                    }else{
                        it.setOldparentId(it.getParentId()+""); //新增的机构放入到在职人员
                    }


                }


            });


            //补齐父级元素 在职人员
            DataDeptRespVO temp=  new DataDeptRespVO();
            temp.setName("在职人员");
            temp.setOldid("001001000000");
            temp.setOldparentId("001000000000");


        }



        List<OldDeptVo> testresult=  DeptConvert.INSTANCE.convert14(result);



        return success(testresult);


    }











}
