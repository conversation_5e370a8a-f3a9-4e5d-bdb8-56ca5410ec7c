package com.unicom.swdx.module.system.controller.admin.home.shortcut;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutClientRespVo;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutUpdateReqVO;
import com.unicom.swdx.module.system.service.shortcut.ShortcutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "业务中台 - 快捷入口")
@RestController
@RequestMapping("/system/shortcut")
@Validated
public class ShortcutController {

    @Resource
    private ShortcutService shortcutService;

    @PostMapping("/create")
    @ApiOperation("创建快捷入口")
//    @PreAuthorize("@ss.hasPermission('midoffice:shortcut:create')")
    public CommonResult<Long> createShortcut(@Valid @RequestBody ShortcutCreateReqVO createReqVO) {
        return success(shortcutService.createShortcut(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新快捷入口")
//    @PreAuthorize("@ss.hasPermission('midoffice:shortcut:update')")
    public CommonResult<Boolean> updateShortcut(@Valid @RequestBody ShortcutUpdateReqVO updateReqVO) {
        shortcutService.updateShortcut(updateReqVO);
        return success(true);
    }

    @GetMapping("/list")
    @ApiOperation("获得快捷入口列表")
//    @PreAuthorize("@ss.hasPermission('midoffice:shortcut:query')")
    public CommonResult<List<ShortcutClientRespVo>> getShortcutList() {
        List<ShortcutClientRespVo> list = shortcutService.getShortcutList();
        return success(list);
    }

}
