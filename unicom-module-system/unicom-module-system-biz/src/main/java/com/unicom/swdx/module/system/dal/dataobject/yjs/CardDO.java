package com.unicom.swdx.module.system.dal.dataobject.yjs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;


/**
 * 班级 DO
 *
 * <AUTHOR>
 */
@TableName("T_PT_CardBags")
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardDO {

    private String cardBagId;

    private String createUser;
    private short isDelete;
    private int orderIndex;

    private String updateUser;
    private int version;
    private String bagCode;
    private Short bagStatus;

    private byte[] cardValueEncrypt;
    private int czCount;

    private String userId;
    private int xfCount;


}
