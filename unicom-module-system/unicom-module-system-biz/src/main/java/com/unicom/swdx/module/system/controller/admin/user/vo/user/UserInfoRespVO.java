package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 用户信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoRespVO  {

    @ApiModelProperty(value = "用户信息")
    private AdminUserDO userDO;

    @ApiModelProperty(value = "用户岗位")
    private List<String> posts;
}
