package com.unicom.swdx.module.system.controller.admin.auth.vo;

import com.unicom.swdx.framework.common.validation.Mobile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@ApiModel("管理后台 - 发送手机验证码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthSmsSendReqVO {

    @ApiModelProperty(value = "手机号", required = true, example = "12345678910")
    @NotBlank(message = "手机号不能为空")
    @Mobile
    private String mobile;

    @ApiModelProperty(value = "短信编号标志（0=系统注册，1=重置密码，2=重置手机号，3=系统登录）",  example = "1")
    private Integer flag;



}
