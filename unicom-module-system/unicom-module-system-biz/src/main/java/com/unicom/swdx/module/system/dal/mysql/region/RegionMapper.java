package com.unicom.swdx.module.system.dal.mysql.region;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.dal.dataobject.region.RegionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RegionMapper extends BaseMapperX<RegionDO> {

    /**
     * 根据父地区id递归获取所有子地区
     * level <= 3
     * @param id 父地区id
     * @param level 等级
     * @return 子地区
     */
    List<RegionDO> selectListByParentId(@Param("id") Long id, @Param("level") Integer level);
}
