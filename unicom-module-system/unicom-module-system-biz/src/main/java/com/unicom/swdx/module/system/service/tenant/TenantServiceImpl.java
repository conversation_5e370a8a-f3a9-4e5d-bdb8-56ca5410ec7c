package com.unicom.swdx.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.tenant.config.TenantProperties;
import com.unicom.swdx.framework.tenant.core.context.TenantContextHolder;
import com.unicom.swdx.framework.tenant.core.util.TenantUtils;
import com.unicom.swdx.module.system.api.tenant.dto.TenantInfoRespDTO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserCreateReqVO;
import com.unicom.swdx.module.system.convert.tenant.TenantConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.mq.producer.dept.DeptProducer;
import com.unicom.swdx.module.system.mq.producer.tenant.TenantProducer;
import com.unicom.swdx.module.system.service.oauth2.OAuth2TokenService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.tenant.handler.TenantInfoHandler;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.boot.autoconfigure.klock.model.LockType;
import org.springframework.boot.autoconfigure.klock.model.ReleaseTimeoutStrategy;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.system.enums.kafka.dept.DeptEventType.*;
import static com.unicom.swdx.module.system.enums.permission.RoleIdEnum.SUPER_ADMIN;
import static com.unicom.swdx.module.system.enums.user.UserCategoryEnum.TENANT_ADMIN;

/**
 * 机构 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl extends ServiceImpl<TenantMapper, TenantDO> implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 unicom.tenant.enable 配置项，可以关闭多机构的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;

    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantTypeService tenantTypeService;

    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;

    @Resource
    @Lazy
    private PermissionService permissionService;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private TenantProducer tenantProducer;

    @Resource
    private DeptProducer deptProducer;

    @Resource
    private OAuth2TokenService oAuth2TokenService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private StringRedisTemplate sRedisTemplate;

    private ConcurrentHashMap<Long, TenantDO> tenantCache = new ConcurrentHashMap<>();

    public static final Long DEFAULT_DEPT_ID = 100L;
    @Autowired
    private TenantMapper tenantMapper;

    /**
     * 初始化机构缓存信息
     */
    @PostConstruct
    @Override
//    @Klock(
//            keys = {"tenant" + "${HOSTNAME}"},
//            lockType = LockType.Reentrant, // 使用可重入锁
//            waitTime = 1, // 获取锁的等待时间
//            leaseTime = 5, // 锁的持续时间，单位是秒
//            lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST, // 获取锁失败时的策略
//            releaseTimeoutStrategy = ReleaseTimeoutStrategy.FAIL_FAST // 释放锁失败时的策略
//    )
    public void init() {
        tenantCache.clear();
        List<TenantDO> tenantList = this.list();

        tenantList.forEach(it->{
            tenantCache.put(it.getId() , it) ;
        });

        log.info("[initLocalCache][初始化 tenant 数量为 {}]", tenantList.size());
    }

    /**
     * 创建机构
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTenant(TenantCreateReqVO createReqVO) {
        // 校验机构
        validCreateTenant(createReqVO);
        //创建机构
        TenantDO tenant = TenantConvert.INSTANCE.convert(createReqVO);



        this.save(tenant);

        TenantUtils.execute(tenant.getId(), () -> {
            // 1、创建机构管理员账号
            UserCreateReqVO userCreateReqVO = new UserCreateReqVO();
            userCreateReqVO.setUsername(createReqVO.getContactName());
            userCreateReqVO.setPassword(createReqVO.getContactPassword());
            userCreateReqVO.setMobile(createReqVO.getContactMobile());
            userCreateReqVO.setNickname(createReqVO.getContactNickname());
            userCreateReqVO.setDeptId(DEFAULT_DEPT_ID);//临时默认部门
            Long userId = userService.createUser(userCreateReqVO, tenant.getId(), TENANT_ADMIN.getCategory());

            // 2、分配机构管理员角色
            permissionService.addUserRole(userId, tenantTypeService.getRoleIdsByTenantTypes(createReqVO.getInstUserType()));

            // 3、新增机构的根部门
            DeptDO deptDO = DeptDO.builder()
                    .name(createReqVO.getName()) // 根部门名称为机构名称
                    .leaderUserId(tenant.getContactUserId()) // 根部门负责人为机构管理员id
                    .status(CommonStatusEnum.ENABLE.getStatus())
                    .build();
            deptDO.setTenantId(tenant.getId());

            tenant.setContactUserId(userId);

            deptMapper.insert(deptDO);
            // 4、更新机构表的管理员id和用户表的部门id
            // 更新根部门的负责人id
            adminUserMapper.updateById(AdminUserDO.builder().id(userId).deptId(deptDO.getId()).build());
            // 更新机构的管理员id
            baseMapper.updateById(TenantDO.builder().id(tenant.getId()).contactUserId(userId).build());
        });

        Long deptId = deptMapper.selectByDeptNameAndTenantId(tenant.getName(),tenant.getId()).getId();

        // 事务提交后发送刷新缓存消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantProducer.sendTenantRefreshMessage();
                tenantProducer.sendTenantData(addTenantEventType, tenant, deptId);
            }
        });
        return tenant.getId();
    }

    /**
     * 更新机构信息
     *
     * @param updateReqVO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenant(TenantUpdateReqVO updateReqVO) {
        // 校验机构
        TenantDO oldTenant = checkUpdateTenant(updateReqVO.getId());
        checkTenantCode(updateReqVO.getId(),updateReqVO.getTenantCode());
        // 更新机构信息
        TenantDO tenant = TenantConvert.INSTANCE.convert(updateReqVO);
        this.updateById(tenant);

        // 更新机构后操作
        // 1、更新机构管理员昵称和手机号
        AdminUserDO userDO = AdminUserDO.builder()
                .id(oldTenant.getContactUserId())
                .nickname(updateReqVO.getContactNickname())
                .mobile(updateReqVO.getContactMobile().contains("*") ? null : updateReqVO.getContactMobile())
                .build();
        userDO.setTenantId(updateReqVO.getId());
        userService.updateUserById(userDO);

        // 2、更新管理员的角色权限
//        permissionService.resetUserRoleAsync(oldTenant.getInstUserType(), tenantTypeService.getRoleIdsByTenantTypes(updateReqVO.getInstUserType()), oldTenant.getId());

        // 3、禁用机构，需要删除该机构下的所有token
        if (Objects.equals(updateReqVO.getStatus(), CommonStatusEnum.DISABLE.getStatus())) {
            oAuth2TokenService.removeAccessToken(oldTenant.getId());
        }
        // 4、修改机构的根部门名称
        if(!oldTenant.getName().equals(updateReqVO.getName())){
            deptMapper.updateRootNameByTenantId(oldTenant.getId(), updateReqVO.getName());
            deptProducer.sendRefreshMessage();
        }

        Long deptId = deptMapper.selectDeptIdByTenantId(tenant.getId());

        // 事务提交后发送刷新缓存消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantProducer.sendTenantRefreshMessage();
                tenantProducer.sendTenantData(editTenantEventType, tenant, deptId);
                // 统一刷新机构管理员及其普通用户的角色权限
                permissionService.resetAllTenantUserRoleAsync(CollUtil.newArrayList(updateReqVO.getId()));
            }
        });
    }

    /**
     * 更新机构状态
     *
     * @param updateReqVO 更新状态信息
     */
    @Override
    public void updateTenantStatus(TenantStatusUpdateReqVO updateReqVO) {
        checkUpdateTenant(updateReqVO.getId());
        this.updateById(TenantDO.builder()
                .id(updateReqVO.getId())
                .status(updateReqVO.getStatus())
                .build());

        // 禁用机构，需要删除该机构下的所有token
        if (Objects.equals(updateReqVO.getStatus(), CommonStatusEnum.DISABLE.getStatus())) {
            oAuth2TokenService.removeAccessToken(updateReqVO.getId());
        }

        // 发送刷新缓存消息
        tenantProducer.sendTenantRefreshMessage();
    }

    /**
     * 删除机构
     *
     * @param id 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTenant(Long id) {
        // 校验机构id是否存在
        TenantDO tenantDO = checkUpdateTenant(id);
        // 校验是否有除管理员外的用户
        if (checkUserNum(id)) {
            throw exception(TENANT_HAS_USERS);
        }

        // 开始删除
        // 1、删除机构信息
        this.removeById(id);
        // 2、删除机构管理员用户信息
        userService.deleteAdminUser(tenantDO.getContactUserId());
        // 3、删除组织
        deptMapper.deleteByTenantId(id);
        // 4、删除该机构绑定的角色
        roleMapper.deleteByTenantId(id);
        // 5、删除该机构下所有用户的token
        oAuth2TokenService.removeAccessToken(id);

        // 事务提交后发送刷新缓存消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantProducer.sendTenantRefreshMessage();
                tenantProducer.sendTenantData(delTenantEventType, tenantDO, null);
            }
        });
    }

    /**
     * 获得机构
     *
     * @param id 编号
     * @return 机构
     */
    @Override
    public TenantDO getTenant(Long id) {
        return this.getById(id);
    }

    /**
     * 根据机构id从缓存中获得机构信息
     *
     * @param id 机构id
     * @return 机构信息
     */
    @Override
    public TenantDO getTenantFromCache(Long id) {
        TenantDO tenantDO  =  tenantCache.get(id);
        if(tenantDO==null){
            init();
            tenantDO = tenantCache.get(id);
        }
        return tenantDO;
    }

    @Override
    public List<TenantDO> getTenantsFromCache() {
        return new ArrayList<>(tenantCache.values());
    }

    /**
     * 获得机构分页
     *
     * @param pageReqVO 分页查询
     * @return 机构分页
     */
    @Override
    public PageResult<TenantDO> getTenantPage(TenantPageReqVO pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TenantDO> getTenantExcelList(TenantPageReqVO reqVO) {
        return baseMapper.selectExcelList(reqVO);
    }

    /**
     * 判断机构下是否存在除机构管理员外的用户
     *
     * @param id 机构
     * @return 是否
     */
    @Override
    public Boolean checkUserNum(Long id) {
        List<AdminUserDO> adminUserList = adminUserMapper.selectListByTenantId(id);
        if (CollUtil.isEmpty(adminUserList)) {
            return false;
        }
        if (adminUserList.size() >= 2) {
            return true;
        }
        return false;
    }

    /**
     * 获取机构下组织数量
     *
     * @param id 机构id
     * @return 组织数量
     */
    @Override
    public Long getDeptNum(Long id) {
        Long num = deptMapper.selectCountByTenantId(id);
        return num > 0 ? (num - 1) : num;
    }

    /**
     * 进行机构的信息处理逻辑
     * 其中，机构编号从 {@link TenantContextHolder} 上下文中获取
     * （勿删）
     * @param handler 处理器
     */
    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得机构
        TenantDO tenant = getTenantFromCache(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    /**
     * 获得所有机构id
     *
     * @return 机构编号数组
     */
    @Override
    public List<Long> getTenantIds() {
        List<TenantDO> tenants = this.list();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    /**
     * 校验机构是否合法
     *
     * @param id 机构编号
     */
    @Override
    public void validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        if (tenant.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenant.getName());
        }
    }

    /**
     * 根据机构管理员id获取机构信息
     * @param userId 用户id
     * @return 机构
     */
    @Override
    public TenantDO getTenantByContactUserId(Long userId) {
        return baseMapper.selectByContactUserId(userId);
    }

    /**
     * 根据机构管理员用户id获取机构信息
     *
     * @param contactUserIds 机构管理员用户id集合
     * @return 机构信息集合
     */
    @Override
    public List<TenantInfoRespDTO> getTenantByContactUserIds(Collection<Long> contactUserIds) {
        return TenantConvert.INSTANCE.convertList04(baseMapper.selectByContactUserIds(contactUserIds));
    }

    @Override
    public TenantDO getTenantByUserId(Long userId) {
        Long tenantId = userService.getUser(userId).getTenantId();
        return getTenantFromCache(tenantId);
    }

    /**
     * 从缓存中校验用户是否机构管理员
     *
     * @param userId 用户id
     * @return 是否机构管理员
     */
    @Override
    public boolean isTenantAdmin(Long userId) {
        return tenantCache.values().stream().anyMatch(t -> Objects.equals(t.getContactUserId(), userId));
    }

    /**
     * 从缓存中根据机构用户类型获取该机构用户类型绑定的机构集
     *
     * @param tenantTypeId 机构用户类型
     * @return 机构集
     */
    @Override
    public List<TenantDO> getListByTenantType(Long tenantTypeId) {
        return tenantCache.values().stream()
                .filter(t -> CollUtil.contains(t.getInstUserType(), tenantTypeId))
                .collect(Collectors.toList());
    }

    /**
     * 是否系统机构
     *
     * @param id 机构id
     * @return 是否
     */
    @Override
    public boolean isSystemTenant(Long id) {
        return id == 1L;
    }

    /**
     * 根据机构id获取机构绑定的机构用户类型绑定的角色id集合
     * （只用考虑机构用户类型是否禁用）
     * @param tenantId 机构id
     * @return 角色id集合
     */
    @Override
    public Set<Long> getAllRoleIdsByTenantId(Long tenantId) {
        TenantDO tenant = getTenantFromCache(tenantId);
        if (tenant.getId() == 1L) {
            // 超管的机构，直接返回超管角色
            return CollUtil.newHashSet(SUPER_ADMIN.getId());
        }
        return tenantTypeService.getRoleIdsByIds(tenant.getInstUserType());
    }

    // 本类调用
    /**
     * 校验新增的机构
     *
     * @param createReqVO 新增的机构信息
     */
    private void validCreateTenant(TenantCreateReqVO createReqVO) {
        checkContactName(null, createReqVO.getContactName());
        checkTenantCode(null,createReqVO.getTenantCode());
    }

    /**
     * 校验更新的机构是否存在，是否可修改
     *
     * @param id 机构id
     * @return 机构
     */
    private TenantDO checkUpdateTenant(Long id) {
        TenantDO tenant = this.getById(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 内置机构，不允许删除或修改
        if (isSystemTenant(tenant.getId())) {
            throw exception(TENANT_CAN_NOT_UPDATE_SYSTEM);
        }
        return tenant;
    }

    /**
     * 是否开启机构隔离
     *
     * @return 是否
     */
    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

    /**
     * 校验用户名是否重复
     *
     * @param id          id 用户id
     * @param contactName 用户名
     */
    private void checkContactName(Long id, String contactName) {
        if (StrUtil.isBlank(contactName)) {
            return;
        }
        AdminUserDO adminUserDO = adminUserMapper.selectByUsername(contactName);
        if (Objects.isNull(adminUserDO)) {
            return;
        }
        if (Objects.isNull(id) || !Objects.equals(adminUserDO.getId(), id)) {
            throw exception(USER_USERNAME_EXISTS);
        }
    }

    private void checkTenantCode(Long id, String tenantCode){
        TenantDO tenantDO = this.getBaseMapper().selectByTenantCode(tenantCode);
        if (Objects.isNull(tenantDO)) {
            return;
        }
        if (Objects.isNull(id) || !Objects.equals(tenantDO.getId(), id)) {
            throw exception(TENANT_CODE_EXISTS);
        }
    }

    // todo 以下都需要检查是否可以删除

    /**
     * 获得名字对应的机构
     *
     * @param name 组户名
     * @return 机构
     */
    @Override
    public TenantDO getTenantByName(String name) {
        return baseMapper.selectByName(name);
    }

    @Override
    public TenantDO getTenantByCode(String tenantCode) {
        return baseMapper.selectByCode(tenantCode);
    }

    @Override
    public List<Long> getTenants() {
        return Collections.emptyList();
    }

    /**
     * 根据id集获取机构信息
     *
     * @param ids id集
     * @return 机构信息
     */
    @Override
    public List<TenantDO> getTenantListByIds(Collection<Long> ids) {
        return this.listByIds(ids);
    }

    @Override
    public void saveTenantIdToRedis(Long tenantId) {
        String key="tempTenantId:"+getLoginUserId();
        sRedisTemplate.opsForValue().set(key,String.valueOf(tenantId),1, TimeUnit.HOURS);
        String value=sRedisTemplate.opsForValue().get(key);
        log.info(value);
    }

    @Override
    public Boolean updateTenantCheckRule(TenantCheckRuleUpdateReqVO updateReqVO){
        LambdaUpdateWrapper<TenantDO> tenantDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        tenantDOLambdaUpdateWrapper.eq(TenantDO::getId,updateReqVO.getId());
        tenantDOLambdaUpdateWrapper.set(TenantDO::getCheckByPeriodRule,updateReqVO.getCheckByPeriodRule());
        boolean result = tenantMapper.update(null, tenantDOLambdaUpdateWrapper) > 0;
        //手动刷新缓存
        init();
        // 事务提交后发送刷新缓存消息
        try{
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    tenantProducer.sendTenantRefreshMessage();
                }
            });
        }catch (Exception e){
            log.error("更新其他应用机构缓存失败",e);
        }
        return result;
    }

    /**
     * 更新机构考勤保护规则
     *
     * @param updateReqVO 更新信息
     * @return 是否更新成功
     */
    @Override
    public Boolean updateAttendanceProtection(TenantAttendanceProtectionUpdateReqVO updateReqVO) {
        TenantDO tenantDO = tenantMapper.selectById(updateReqVO.getId());
        if(tenantDO == null){
            return false;
        }
        TenantDO updateDO = TenantDO.builder()
                .id(tenantDO.getId())
                .enableAttendanceProtection(updateReqVO.getEnable()).build();
        Boolean result = tenantMapper.updateById(updateDO) > 0;
        //手动刷新缓存
        init();
        // 事务提交后发送刷新缓存消息
        try{
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    tenantProducer.sendTenantRefreshMessage();
                }
            });
        }catch (Exception e){
            log.error("更新其他应用机构缓存失败",e);
        }
        return result;
    }

    @Override
    public Boolean getTenantCheckRule(Long tenantId){
        TenantDO tenantFromCache = getTenantFromCache(tenantId);
        if(tenantFromCache == null){
            throw exception(TENANT_NOT_EXISTS);
        }
        return tenantFromCache.getCheckByPeriodRule();
    }
}
