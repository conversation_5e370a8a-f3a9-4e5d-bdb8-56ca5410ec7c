package com.unicom.swdx.module.system.service.subsystem;

import com.unicom.swdx.module.system.controller.admin.subsystem.vo.ClientRespVO;

import java.util.List;

/**
 * 子系统 Service 接口
 *
 * <AUTHOR>
 */
public interface SubsystemService {


    /**
     * 获得子系统列表
     *
     * @param userId 用户id
     * @return 子系统列表
     */
    List<ClientRespVO> getSubsystemList(Long userId,List<Integer> visibility);

}
