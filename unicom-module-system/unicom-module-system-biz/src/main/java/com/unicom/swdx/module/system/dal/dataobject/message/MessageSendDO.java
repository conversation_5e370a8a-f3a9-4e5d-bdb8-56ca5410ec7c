package com.unicom.swdx.module.system.dal.dataobject.message;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.JsonLongSetTypeHandler;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 租户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_message_send", autoResultMap = true)
@KeySequence("system_message_send_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageSendDO extends BaseDO {

    /**
     * 编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Boolean ifTimedSend;

    private LocalDateTime timedSendTime;

    private Integer sendMode;

    @TableField(typeHandler = JsonLongSetTypeHandler.class)
    private Set<Long> receivingPersonIds;

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> receivingPersonNames;

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> receivingMobile;

    private Integer sendContentMode;

    private Integer templateId;

    private String messageContent;

    private Boolean messageChannel;

    private Boolean noticeChannel;

    private Integer noticeFail;

    private Integer noticeSuccess;

    private Integer messageFail;

    private Integer messageSuccess;

    private String templateContent;

    private String templateName;

    private Long systemId;

    private Integer messageReady;

    private Integer noticeReady;

    private Long tenantId;
}
