package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("业务中台 - 快捷入口分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShortcutPageReqVO extends PageParam {

    @ApiModelProperty(value = "所属子系统id")
    private Integer subsystemId;

    @ApiModelProperty(value = "快捷入口名称")
    private String name;

    @ApiModelProperty(value = "快捷入口链接")
    private String linkUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

}
