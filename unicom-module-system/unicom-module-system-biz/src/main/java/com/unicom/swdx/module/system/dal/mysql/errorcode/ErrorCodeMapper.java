package com.unicom.swdx.module.system.dal.mysql.errorcode;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.errorcode.vo.ErrorCodeExportReqVO;
import com.unicom.swdx.module.system.controller.admin.errorcode.vo.ErrorCodePageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.errorcode.ErrorCodeDO;
import org.apache.ibatis.annotations.Mapper;
import cn.hutool.core.util.StrUtil;
import java.util.Collection;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ErrorCodeMapper extends BaseMapperX<ErrorCodeDO> {

    default PageResult<ErrorCodeDO> selectPage(ErrorCodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ErrorCodeDO>()
                .eqIfPresent(ErrorCodeDO::getType, reqVO.getType())
                .betweenIfPresent(ErrorCodeDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getMessage()), l ->
                        l.like(ErrorCodeDO::getApplicationName, reqVO.getMessage())
                                .or().like(ErrorCodeDO::getMessage,reqVO.getMessage())
                                .or().like(ErrorCodeDO::getCode,reqVO.getMessage()))
                .orderByDesc(ErrorCodeDO::getCode)
        );
    }

    default List<ErrorCodeDO> selectList(ErrorCodeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ErrorCodeDO>()
                .eqIfPresent(ErrorCodeDO::getType, reqVO.getType())
                .betweenIfPresent(ErrorCodeDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getMessage()), l ->
                        l.like(ErrorCodeDO::getApplicationName, reqVO.getMessage())
                                .or().like(ErrorCodeDO::getMessage,reqVO.getMessage())
                                .or().like(ErrorCodeDO::getCode,reqVO.getMessage()))
                .orderByDesc(ErrorCodeDO::getCode)
        );
    }

    default List<ErrorCodeDO> selectListByCodes(Collection<Integer> codes) {
        return selectList(new LambdaQueryWrapperX<ErrorCodeDO>().in(ErrorCodeDO::getCode, codes));
    }

    default ErrorCodeDO selectByCode(Integer code) {
        return selectOne(new LambdaQueryWrapperX<ErrorCodeDO>().eq(ErrorCodeDO::getCode, code));
    }

    default List<ErrorCodeDO> selectListByApplicationNameAndUpdateTimeGt(String applicationName, LocalDateTime minUpdateTime) {
        return selectList(new LambdaQueryWrapperX<ErrorCodeDO>().eq(ErrorCodeDO::getApplicationName, applicationName)
                .gtIfPresent(ErrorCodeDO::getUpdateTime, minUpdateTime));
    }

}
