package com.unicom.swdx.module.system.service.applet;


import com.unicom.swdx.module.system.controller.admin.user.vo.applet.UserAppletBaseVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.applet.UserAppletReultVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.applet.UserAppletTeacherVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.applet.UserAppletTopVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;

import java.util.List;

public interface UserAppletService {


    UserAppletTopVO getDashboard(UserAppletBaseVO userAppletBaseVO);
    UserAppletTeacherVO getTeachingMethods(UserAppletBaseVO userAppletBaseVO);
    UserAppletReultVO getCourseSituation(UserAppletBaseVO userAppletBaseVO);
    Integer getCourseSituationTotal(UserAppletBaseVO userAppletBaseVO);
    Integer getUserAppletType();
    List<DeptDO> getDepts();
}
