package com.unicom.swdx.module.system.service.sms;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.module.system.api.sms.dto.code.SmsCodeCheckReqDTO;
import com.unicom.swdx.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.unicom.swdx.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.unicom.swdx.module.system.dal.dataobject.sms.SmsCodeDO;
import com.unicom.swdx.module.system.dal.mysql.sms.SmsCodeMapper;
import com.unicom.swdx.module.system.enums.sms.SmsSceneEnum;
import com.unicom.swdx.module.system.mq.message.sms.SmsCodeProperties;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.util.RandomUtil.randomInt;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;


/**
 * 短信验证码 Service 实现类
 *
 * <AUTHOR>
@Service
@Validated
public class SmsCodeServiceImpl implements SmsCodeService {

    @Resource
    private SmsCodeProperties smsCodeProperties;

    @Resource
    private SmsCodeMapper smsCodeMapper;

    @Resource // 保证 aj-captcha 的 SPI 创建时的注入
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private SmsSendService smsSendService;
    @Resource
    private RedissonClient redissonClient;
    @Override
    public void sendSmsCode(SmsCodeSendReqDTO reqDTO) {
        SmsSceneEnum sceneEnum = SmsSceneEnum.getCodeByScene(reqDTO.getScene());
        Assert.notNull(sceneEnum, "验证码场景({}) 查找不到配置", reqDTO.getScene());
        // 创建验证码
        String code = createSmsCode(reqDTO.getMobile(), reqDTO.getScene(), reqDTO.getCreateIp());
//
//       //测试
//       String code = "999999" ;
        // 发送验证码
        smsSendService.sendSingleSms(reqDTO.getMobile(), null, null,
                sceneEnum.getTemplateCode(), MapUtil.of("arg1", code));
    }

    private String createSmsCode(String mobile, Integer scene, String ip) {
        // 校验是否可以发送验证码，不用筛选场景
  //      SmsCodeDO lastSmsCode = smsCodeMapper.selectLastByMobile(mobile, null,null);
//        if (lastSmsCode != null) {
//            if (System.currentTimeMillis() - lastSmsCode.getCreateTime().getTime()
//                    < smsCodeProperties.getSendFrequency().toMillis()) { // 发送过于频繁
//                throw ServiceExceptionUtil.exception(SMS_CODE_SEND_TOO_FAST);
//            }





            List<SmsCodeDO> lastSmsCodes = smsCodeMapper.selectThreeLastByMobile(mobile, null,null);
        SmsCodeDO lastSmsCode=null;
//        if(lastSmsCodes!=null && lastSmsCodes.size()>0) {
//             lastSmsCode = lastSmsCodes.get(0);
//            if (DateUtils.isToday(lastSmsCode.getCreateTime()) && // 必须是今天，才能计算超过当天的上限
//                    lastSmsCode.getTodayIndex() >= smsCodeProperties.getSendMaximumQuantityPerDay()) { // 超过当天发送的上限。
//                throw ServiceExceptionUtil.exception(SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY);
//            }
//        }
        if(stringRedisTemplate.hasKey("sms10error"+mobile)){
            throw ServiceExceptionUtil.exception(SMS_CODE_SEND_TOO_FAST);
        }

        if(stringRedisTemplate.hasKey("sms24error"+mobile)){
            throw ServiceExceptionUtil.exception(SMS_CODE_SEND_TOO_ERROR);
        }
            if(lastSmsCodes!=null && lastSmsCodes.size()>2){
                SmsCodeDO first=lastSmsCodes.get(0);
                SmsCodeDO two = lastSmsCodes.get(1);
                SmsCodeDO three = lastSmsCodes.get(2);

                if(System.currentTimeMillis() - Timestamp.valueOf(three.getCreateTime()) .getTime()<600000&&System.currentTimeMillis() -  Timestamp.valueOf(first.getCreateTime()).getTime()<600000&&System.currentTimeMillis() - Timestamp.valueOf(two.getCreateTime()).getTime()<600000){
                    stringRedisTemplate.opsForValue().set("sms10error"+mobile, "1", 600, TimeUnit.SECONDS);
                    throw ServiceExceptionUtil.exception(SMS_CODE_SEND_TOO_FAST);

                }
            }
            // TODO 芋艿：提升，每个 IP 每天可发送数量
            // TODO 芋艿：提升，每个 IP 每小时可发送数量


        // 创建验证码记录
        String code = String.valueOf(randomInt(smsCodeProperties.getBeginCode(), smsCodeProperties.getEndCode() + 1));

        //code = "999999" ;
//        if (lastSmsCode != null) {
//            DateUtils.isToday(lastSmsCode.getCreateTime());
//        }
        SmsCodeDO newSmsCode = SmsCodeDO.builder().mobile(mobile).code(code).scene(scene)
                .todayIndex(1)
                .createIp(ip).used(false).build();
        smsCodeMapper.insert(newSmsCode);
        return code;
    }

    @Override
    public void useSmsCode(SmsCodeUseReqDTO reqDTO) {
        // 检测验证码是否有效
        SmsCodeDO lastSmsCode = this.checkSmsCode0(reqDTO.getMobile(), reqDTO.getCode(), reqDTO.getScene());
        // 使用验证码
        smsCodeMapper.updateById(SmsCodeDO.builder().id(lastSmsCode.getId())
                .used(true).usedTime(new Date()).usedIp(reqDTO.getUsedIp()).build());
    }

    @Override
    public void checkSmsCode(SmsCodeCheckReqDTO reqDTO) {
        checkSmsCode0(reqDTO.getMobile(), reqDTO.getCode(), reqDTO.getScene());
    }

    public SmsCodeDO checkSmsCode0(String mobile, String code, Integer scene) {
        // 校验验证码
        SmsCodeDO lastSmsCode = smsCodeMapper.selectLastByMobile(mobile,code,scene);
        String smsKey = "sms24count"+mobile;

        if(stringRedisTemplate.hasKey("sms24error"+mobile)){
            throw ServiceExceptionUtil.exception(SMS_CODE_SEND_TOO_ERROR);
        }
        // 若验证码不存在，抛出异常
        if (lastSmsCode == null||!lastSmsCode.getCode().equals(code) ) {

            long num =  redissonClient.getAtomicLong(smsKey).addAndGet(1);
            if(num>=5){
                stringRedisTemplate.delete(smsKey);
                stringRedisTemplate.opsForValue().set("sms24error"+mobile, "1", 24, TimeUnit.HOURS);
            }
          //  stringRedisTemplate.opsForValue().set("sms24count"+mobile, "1", 600, TimeUnit.SECONDS);
            throw ServiceExceptionUtil.exception(SMS_CODE_ERROR);
        }
        // 超过时间
        if (System.currentTimeMillis() - Timestamp.valueOf(lastSmsCode.getCreateTime()).getTime()
                >= smsCodeProperties.getExpireTimes().toMillis()) { // 验证码已过期
            long num =  redissonClient.getAtomicLong(smsKey).addAndGet(1);
            if(num>=5){
                stringRedisTemplate.delete(smsKey);

                stringRedisTemplate.opsForValue().set("sms24error"+mobile, "1", 24, TimeUnit.HOURS);
            }
            throw ServiceExceptionUtil.exception(SMS_CODE_ERROR);
        }
        // 判断验证码是否已被使用
        if (Boolean.TRUE.equals(lastSmsCode.getUsed())) {
            long num =  redissonClient.getAtomicLong(smsKey).addAndGet(1);
            if(num>=5){
                stringRedisTemplate.delete(smsKey);

                stringRedisTemplate.opsForValue().set("sms24error"+mobile, "1", 24, TimeUnit.HOURS);
            }
            throw ServiceExceptionUtil.exception(SMS_CODE_ERROR);
        }
        redissonClient.getAtomicLong(smsKey).set(0l);
        return lastSmsCode;
    }

}
