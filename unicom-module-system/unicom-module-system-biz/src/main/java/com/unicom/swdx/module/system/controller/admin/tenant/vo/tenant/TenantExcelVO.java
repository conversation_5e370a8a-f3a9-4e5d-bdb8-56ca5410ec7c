package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.unicom.swdx.module.system.enums.DictTypeConstants.*;


/**
 * 租户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TenantExcelVO {

    @ExcelProperty("机构编号")
    private Long id;

    @ExcelProperty(value = "机构名称")
    private String name;

    @ExcelProperty(value = "管理员姓名")
    private String contactNickname;

    @ExcelProperty(value = "管理员手机号码")
    private String contactMobile;

    @ExcelProperty(value = "用户名")
    private String contactName;

    @ExcelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(COMMON_STATUS)
    private Integer status;

}
