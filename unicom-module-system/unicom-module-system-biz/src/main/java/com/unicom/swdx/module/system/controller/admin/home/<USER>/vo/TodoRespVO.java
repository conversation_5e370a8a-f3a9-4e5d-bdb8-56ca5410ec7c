package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 待办事项 Response VO")
@Data
@ToString(callSuper = true)
public class TodoRespVO {

    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    @ApiModelProperty(value = "类型", required = true)
    private Integer type;

    @ApiModelProperty(value = "缓急性", required = true)
    private String urgencyLevel;

    @ApiModelProperty(value = "标题", required = true)
    private String title;

    @ApiModelProperty(value = "发起人", required = true)
    private String submitter;

    @ApiModelProperty(value = "备注")
    private List<String> remark;

    @ApiModelProperty(value = "发起时间", required = true)
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "发起子系统id", required = true)
    private Integer subsystemId;

    @ApiModelProperty(value = "跳转链接", required = true)
    private String linkUrl;

    @ApiModelProperty(value = "待办人", required = true)
    private String todoUserId;

    @ApiModelProperty(value = "参会人是否需要回执 0:是 1:否")
    private Boolean receipt;

    @ApiModelProperty(value = "开始时间", required = true)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "参会地点", required = true)
    private String meetingAddress;

    @ApiModelProperty(value = "状态（0=待办，1=已办）", required = true)
    private Integer status;

    @ApiModelProperty(value = "待办流程id", required = true)
    private String processId;

    @ApiModelProperty(value = "流程类型", required = true)
    private String processType;

    @ApiModelProperty(value = "流程状态", required = true)
    private Integer processStatus;

    @ApiModelProperty(value = "任务节点")
    private String taskCode;

    @ApiModelProperty(value = "收文来源机构id")
    private Long receivingSourceTenantId;

    @ApiModelProperty(value = "政民互动来源")
    private String peopleIneractionSource;
}
