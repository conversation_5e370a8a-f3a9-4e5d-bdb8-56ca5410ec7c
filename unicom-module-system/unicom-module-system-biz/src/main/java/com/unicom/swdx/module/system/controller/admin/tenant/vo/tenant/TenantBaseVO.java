package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import lombok.*;
import io.swagger.annotations.*;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;
import static com.unicom.swdx.module.system.enums.common.CommonConstants.USERNAME_REGEX;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TenantBaseVO {

    @ApiModelProperty(value = "机构名称", required = true, example = "测试机构")
    @NotBlank(message = "机构名称不能为空")
    @Length(min = 0,max = 30,message = "机构名称不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "所在地区划",required = true)
    @NotEmpty(message = "所在地划不能为空")
    private List<Long> locationRegion;

    @ApiModelProperty(value = "详细地址",required = true,example = "湖南长沙")
    @NotBlank(message = "详细地址不能为空")
    @Length(min = 0,max = 50,message = "详细地址不能超过50个字符")
    private String locationAddress;

    @ApiModelProperty(value = "机构管理人姓名",required = true,example = "张三")
    @NotBlank(message = "机构管理人姓名不能为空")
    @Length(min = 0,max = 10,message = "机构管理人姓名姓名不能超过10个字符")
    private String contactNickname;

    @ApiModelProperty(value = "机构管理员手机号码（联系手机）",required = true,example = "13112345678")
    @NotBlank(message = "机构管理员手机号码（联系手机）不能为空")
    @Mobile()
    private String contactMobile;

    @ApiModelProperty(value = "机构用户类型", required = true, example = "1")
    @NotEmpty(message = "机构用户类型不能为空")
    private List<Long> instUserType;

    @ApiModelProperty(value = "机构状态",required = true,example = "1")
    @NotNull(message = "机构状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "机构编码",required = true,example = "1")
//    @NotBlank(message = "机构编码不能为空")
    private String tenantCode;

}
