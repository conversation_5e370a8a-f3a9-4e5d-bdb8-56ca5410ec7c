package com.unicom.swdx.module.system.controller.admin.databoard.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("管理后台 - 实名用户统计 Response VO")
public class RealNameUsersCountRespVO {

    @ApiModelProperty(value = "实名用户数")
    private Long realNameNum;

    @ApiModelProperty(value = "普通用户数")
    private Long normalNum;

    @ApiModelProperty(value = "实名百分比")
    private Float realNamePercent;

    @ApiModelProperty(value = "普通百分比")
    private Float normalPercent;
}
