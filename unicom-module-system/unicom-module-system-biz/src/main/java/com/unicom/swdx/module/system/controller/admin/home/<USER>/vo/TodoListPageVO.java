package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class TodoListPageVO {
    @ApiModelProperty(value = "事项发起人", required = true)
    private String name;

    @ApiModelProperty(value = "待办理事项", required = true)
    private String task;

//    @ApiModelProperty(value = "发起时间戳", required = true)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
//    private String initiationTimeStamp;

    @ApiModelProperty(value = "发起时间", required = true)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private String initiationTime;

    @ApiModelProperty(value = "流程实例的编号", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "类别", required = true)
    private String category;

    @ApiModelProperty(value = "状态", required = true)
    private String status;

    @ApiModelProperty(value = "年度标签", required = true)
    private String yeartag;

    @ApiModelProperty(value = "任务名", required = true)
    private String bteName;

    @ApiModelProperty(value = "任务状态", required = true)
    private String bteStatus;

    @ApiModelProperty(value = "催办状态", required = true)
    private int superviseStatus;

    @ApiModelProperty(value = "催办状态", required = true)
    private Integer shandle;

    @ApiModelProperty(value = "结束时间", required = true)
    private String endtime;

    @ApiModelProperty(value = "创建时间", required = true)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private String createTime;

}
