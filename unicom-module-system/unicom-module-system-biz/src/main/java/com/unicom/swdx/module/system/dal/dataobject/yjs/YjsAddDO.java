package com.unicom.swdx.module.system.dal.dataobject.yjs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组织表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("yanjiusheng_user")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YjsAddDO {


    @TableField(value = "nodeText")
    private String nodeText;

    @TableField(value = "otherSysId")
    private String otherSysId;
    private String name;
    private String phone;
    private String ceshi;
    private String adduser;


}
