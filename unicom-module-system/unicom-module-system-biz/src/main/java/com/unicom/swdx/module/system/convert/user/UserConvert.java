package com.unicom.swdx.module.system.convert.user;

import cn.hutool.core.lang.UUID;
import cn.hutool.extra.spring.SpringUtil;
import com.unicom.swdx.module.system.api.user.dto.AdminUserReqDTO;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import com.unicom.swdx.module.system.api.user.dto.CreateAdminForUnitDTO;
import com.unicom.swdx.module.system.api.user.dto.UserDeptDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptPersonsRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.UserProfileRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.social.SocialUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserVO;
import com.unicom.swdx.module.system.service.dept.DeptService;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    DeptService deptService = SpringUtil.getBean(DeptService.class);

    UserPageItemRespVO convert(AdminUserDO bean);

    UserCreateReqVO convert(AdminUserReqDTO bean);
    UserUpdateReqVO convert(AdminUserRespDTO bean);
    UserContactVO convert01(AdminUserDO bean);
    UserContactVO convert05(AdminUserVO bean);
    UserPageItemRespVO.Dept convert(DeptDO bean);

    AdminUserDO convert(UserCreateReqVO bean);

    AdminUserDO convert(ApplyUserCreateReqVO bean);

    AdminUserDO convert(UserUpdateReqVO bean);

    UserExcelVO convert02(AdminUserDO bean);

    AdminUserDO convert(UserImportExcelVO bean);

    UserProfileRespVO convert03(AdminUserDO bean);

    List<UserDeptDTO> convertToDTO(List<UserDeptDO> bean);

    List<UserProfileRespVO.Role> convertList(List<RoleDO> list);
    List<UserContactVO> convertList01(List<AdminUserDO> list);
    //
    List<UserContactVO> convertList05(List<AdminUserVO> list);

    //服务企业VO
    List<UserImportExcelVO> convertServiceList(List<UserImportServiceExcelVO> list);

    UserProfileRespVO.Dept convert02(DeptDO bean);

    AdminUserDO convert(UserProfileUpdateReqVO bean);

    AdminUserDO convert(UserProfileUpdatePasswordReqVO bean);

    List<UserProfileRespVO.Post> convertList02(List<PostDO> list);

    List<UserProfileRespVO.SocialUser> convertList03(List<SocialUserDO> list);

    List<UserSimpleRespVO> convertList04(List<AdminUserDO> list);

    AdminUserRespDTO convert4(AdminUserDO bean);

    List<AdminUserRespDTO> convertList4(List<AdminUserDO> users);

    default List<DeptPersonsRespVO> convertList5(List<AdminUserDO> users, Long deptId){
        List<DeptPersonsRespVO> personsRespVOS = new ArrayList<>();
        if (users == null){
            return personsRespVOS;
        }
        for (AdminUserDO adminUserDo : users) {
            DeptPersonsRespVO deptPersonsRespVO = new DeptPersonsRespVO();

            deptPersonsRespVO.setId( adminUserDo.getId() );
            deptPersonsRespVO.setName( adminUserDo.getNickname() );
            deptPersonsRespVO.setIsPerson(true);
            deptPersonsRespVO.setParentId( adminUserDo.getDeptId() );
            deptPersonsRespVO.setUniqueSerialParent( "dept" + adminUserDo.getDeptId() );

            personsRespVOS.add(deptPersonsRespVO);
        }
        return personsRespVOS;
    }
    List<UserRespVO> convertList6(List<AdminUserDO> list);

    List<UserInfoVo> convertList7(List<AdminUserDO> list);

    List<UserSimpleRespVO> covertList8(List<AdminUserDO> list);

    default UserSimpleRespVO adminUserDOToUserSimpleRespVO(AdminUserDO adminUserDO) {
        if ( adminUserDO == null ) {
            return null;
        }

        UserSimpleRespVO userSimpleRespVO = new UserSimpleRespVO();

        userSimpleRespVO.setUid(UUID.fastUUID().toString());
        userSimpleRespVO.setId( adminUserDO.getId() );
        userSimpleRespVO.setUsername( adminUserDO.getUsername() );
        userSimpleRespVO.setNickname( adminUserDO.getNickname() );
        userSimpleRespVO.setTenantId( adminUserDO.getTenantId() );
        try {
            userSimpleRespVO.setDeptName(deptService.getDept(adminUserDO.getDeptId()).getName());
        }catch (Exception e){

        }
        return userSimpleRespVO;
    }

    List<AdminUserDO> convertList09(List<CreateAdminForUnitDTO> userInfoList);
}
