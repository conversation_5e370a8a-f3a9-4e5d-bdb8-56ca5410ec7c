package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.*;

@ApiModel("管理后台 - 机构创建 Request VO")
@Data
public class TenantCreateReqVO extends TenantBaseVO{

    @ApiModelProperty(value = "用户名（原联系人）", required = true,example = "skyline")
    @Pattern(regexp = USERNAME_REGEX, message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String contactName;

    @ApiModelProperty(value = "用户密码", required = true,example = "Az_7788@444d")
    @NotNull(message = "用户密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String contactPassword;

}
