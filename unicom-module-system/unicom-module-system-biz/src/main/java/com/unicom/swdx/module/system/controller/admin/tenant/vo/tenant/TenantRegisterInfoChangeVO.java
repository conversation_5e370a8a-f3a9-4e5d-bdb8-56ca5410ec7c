package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("管理后台 - 机构注册信息变更 Request VO")
@Data
@ToString(callSuper = true)
public class TenantRegisterInfoChangeVO {

    @ApiModelProperty(value = "关联机构id", required = true, example = "1")
    @NotNull(message = "机构id不能为空")
    private Long linkedTenantId;

    @ApiModelProperty(value = "机构类型", required = true, example = "1")
    @NotNull(message = "机构类型不能为空")
    private Integer tenantType;

    @ApiModelProperty(value = "机构级别", required = false, example = "1")
    private Integer tenantLevel;

    @ApiModelProperty(value = "范围", example = "[10000216,10002038]")
    private List<Long> levelRange;

    @ApiModelProperty(value = "业务指导单位")
    private Integer guideTenantId;

    @ApiModelProperty(value = "单位状态", required = true, example = "1")
    @NotNull(message = "单位状态不能为空")
    private Integer companyType;

    @ApiModelProperty(value = "机构全称", required = true, example = "体育测试机构")
    @NotNull(message = "机构全称不能为空")
    @Length(min = 0,max = 30,message = "机构全称不能超过30个字符")
    private String tenantName;

    @ApiModelProperty(value = "统一社会信用代码",required = true,example = "sport4541415184")
    @NotNull(message = "统一社会信用代码不能为空")
    @Length(min = 0,max = 100,message = "统一社会信用代码不能超过100个字符")
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "注册地区划",required = false)
    //@Length(min = 0,max = 100,message = "注册地区划不能超过100个字符")
    private List<Long> registerRegion;

    @ApiModelProperty(value = "注册地地址",required = false,example = "湖南长沙")
    @Length(min = 0,max = 200,message = "注册地地址不能超过200个字符")
    private String registerAddress;

    @ApiModelProperty(value = "所在地区划",required = true)
    @NotNull(message = "所在地划不能为空")
    //@Length(min = 0,max = 100,message = "所在地区划不能超过100个字符")
    private List<Long> locationRegion;


    @ApiModelProperty(value = "所在地地址",required = true,example = "湖南长沙")
    @NotNull(message = "所在地地址不能为空")
    @Length(min = 0,max = 100,message = "所在地地址不能超过100个字符")
    private String locationAddress;


    @ApiModelProperty(value = "法定代表人姓名",required = true,example = "张三")
    @NotNull(message = "法定代表人姓名不能为空")
    @Length(min = 0,max = 100,message = "法定代表人姓名不能超过100个字符")
    private String legalRepresentativeName;


    @ApiModelProperty(value = "法人代表身份证号码",required = true,example = "45415412411845415134")
    @NotNull(message = "法人代表身份证号码不能为空")
    @Length(min = 0,max = 100,message = "法人代表身份证号码不能超过100个字符")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String legalRepresentativeIdCard;

    @ApiModelProperty(value = "机构用户类型", required = true)
    @NotNull(message = "机构用户类型不能为空")
    private List<Long> instUserType;

    @ApiModelProperty(value = "营业执照", required = true, example = "sportfun.club")
    @NotNull(message = "营业执照不能为空")
    @Length(min = 0,max = 300,message = "营业执照不能超过300个字符")
    private String businessLicenseUrl;

    @ApiModelProperty(value = "变更内容", example = "营业执照;用户类型")
    //@NotNull(message = "变更内容不能为空")
    private String changeContent;
}
