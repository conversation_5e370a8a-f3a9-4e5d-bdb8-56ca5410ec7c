package com.unicom.swdx.module.system.controller.admin.databoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("管理后台 - 数据看板用户统计 Response VO")
public class UserCountRespVO {

    @ApiModelProperty(value = "用户总数")
    private Long allUsers;

    @ApiModelProperty(value = "今日新增用户")
    private Long newUsers;

    @ApiModelProperty(value = "今日活跃用户")
    private Long activeUsers;
}
