package com.unicom.swdx.module.system.service.shortcut;


import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutRespVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserUpdateReqVO;
import java.util.List;

/**
 * 快捷入口用户关联 Service 接口
 *
 * <AUTHOR>
 */
public interface ShortcutUserService {


    /**
     * 更新快捷入口用户关联
     *
     * @param userId 用户id
     * @param updateReqVO 快捷入口id
     */
    void updateShortcutUser(Long userId, ShortcutUserUpdateReqVO updateReqVO);

    /**
     * 获得快捷入口用户关联
     *
     * @param userId 编号
     * @param isDefault 是否返回默认的快捷入口
     * @return 快捷入口用户关联
     */
    List<ShortcutRespVO> getShortcutUser(Long userId, boolean isDefault);

}
