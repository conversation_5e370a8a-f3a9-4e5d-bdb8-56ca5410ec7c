package com.unicom.swdx.module.system.dal.mysql.oaNotice;

import cn.hutool.core.text.CharSequenceUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticePageReqVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticePersonalReqVO;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.NoticeReadRecordDO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface NoticeReadRecordMapper extends BaseMapperX<NoticeReadRecordDO> {


    Integer hadRecord(@Param("userId")Long userId,@Param("recordId")Long recordId);

    void insertOne(@Param("userId")Long userId,@Param("recordId")Long recordId);

    void updateReadNum(@Param("id")Long id);

    void emptyReadRecord(@Param("id")Long id);

}

