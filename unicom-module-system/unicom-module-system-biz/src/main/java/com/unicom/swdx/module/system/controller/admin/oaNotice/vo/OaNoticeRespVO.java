package com.unicom.swdx.module.system.controller.admin.oaNotice.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 通知公告信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OaNoticeRespVO extends OaNoticeBaseVO {

    @ApiModelProperty(value = "通知公告序号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "发送人部门名称", required = true, example = "这是个部门")
    private String deptName;

}
