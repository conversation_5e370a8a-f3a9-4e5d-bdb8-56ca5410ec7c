package com.unicom.swdx.module.system.service.yezhong;

import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.dal.dataobject.message.YzuCreateUserMessage.BodyDTO.*;
import javax.validation.Valid;

public interface YezhongService {



    /**
     * 创建业务中台学员账号
     * {
     *  "header": {
     *   "sender": "002",
     *   "timestamp": "1710991107452",
     *   "eventType": "00200004",
     *   "eventId": "42e1a7b7d51a4ba4b3ed591b01bc23e1"
     *  },
     *  "body": {
     *   "student": {
     *    "id": "002403689050afc41619b50198acb351cde",
     *    "name": "王璋华",
     *    "sex": "1",
     *    "nation": "01",
     *    "birthday": "Jan 20, 1974 12:00:00 AM",
     *    "idCard": "EYVwNF1kJHdheoV0CyHOICyh9KUo/DN8",
     *    "phone": "15974211616",
     *    "post": "省卫生健康委医疗应急处处长",
     *    "culture": "3",
     *    "personnelDepName": "省卫生健康委人事处",
     *    "flag": "1",
     *    "currentJigouId": "90fc26a3140442a2b983a0b1e8023001",
     *    "currentJigouName": "省卫生健康委员会",
     *    "currentClassId": "d5de9104d45c4ec8b6e1a27c1f66f005",
     *    "rank": "正处级",
     *    "zzmm": "1"
     *   }
     * @param studentInfor
     * @return
     */

    boolean createStudentUser(@Valid StudentDTO studentInfor);


    /**
     * {
     *     "code": 0,
     *     "data": {
     *         "userId": 1,
     *         "accessToken": "a45d3a26b2cf408bb66e6c683ea38ec5",
     *         "refreshToken": "e9e30acf0a87453da84ce5b19970a94b",
     *         "expiresTime": 1728893750400,
     *         "userName": null,
     *         "deptName": null,
     *         "tenantName": null,
     *         "authLoginRespVOList": null,
     *         "unionid": null,
     *         "logindefualt": null
     *     },
     *     "msg": ""
     * }
     * @return
     */

    AuthLoginRespVO loginUserAdmin(Boolean refresh);

    void  getDictType();

    void  getDictData();

}
