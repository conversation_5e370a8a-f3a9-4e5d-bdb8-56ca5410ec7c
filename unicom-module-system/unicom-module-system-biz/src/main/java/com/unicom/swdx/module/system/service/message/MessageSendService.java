package com.unicom.swdx.module.system.service.message;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.*;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageSendDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageSendService extends IService<MessageSendDO> {

    /**
     * 创建消息模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMessageSend(@Valid MessageSendCreateReqVO createReqVO);
    PageResult<MessageSendPageRespVO> getMessagePage(MessageSendPageReqVO pageReqVO);
//    PageResult<MessageDO> getMessagePage(MessagePageReqVO pageReqVO);

    List<MessageSendGetVO> getMessage(MessageSendGetReqVO req);

//    PageResult<MessageSendRespGetVO> getMessage(MessageSendGetReqVO req);
//    /**
//     * 更新消息模板
//     *
//     */
//    void updateMessage(@Valid MessageUpdateReqVO updateReqVO);
//    /**
//     * 删除消息模板
//     *
//     * @param id 编号
//     */
//    void deleteMessage(Long id);



}
