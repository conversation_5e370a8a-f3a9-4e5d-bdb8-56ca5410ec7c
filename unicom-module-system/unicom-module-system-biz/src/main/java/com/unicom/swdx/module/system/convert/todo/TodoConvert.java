package com.unicom.swdx.module.system.convert.todo;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemCreateReqDTO;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemUpdateReqDTO;
import com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoCreateSimpleReqVO;
import com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoExcelVO;
import com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoRespVO;
import com.unicom.swdx.module.system.dal.dataobject.todo.TodoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 待办事项 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TodoConvert {

    TodoConvert INSTANCE = Mappers.getMapper(TodoConvert.class);

    default TodoDO convert(TodoItemCreateReqDTO bean) {
        if ( bean == null ) {
            return null;
        }

        TodoDO.TodoDOBuilder todoDO = TodoDO.builder();

        todoDO.type( bean.getType() );
        todoDO.title( bean.getTitle() );
        if ( bean.getSubmitter() != null ) {
            todoDO.submitter( String.valueOf( bean.getSubmitter() ) );
        }
        if (StrUtil.isNotBlank(bean.getRemark())) {
            todoDO.remark(bean.getRemark());
        }
        todoDO.submitTime( bean.getSubmitTime() );
        todoDO.subsystemId( bean.getSubsystemId() );
        todoDO.processId( bean.getProcessId() );
        todoDO.processType(bean.getProcessType());
        todoDO.processStatus(bean.getProcessStatus());
        todoDO.taskCode(bean.getTaskCode());
        todoDO.linkUrl(bean.getPath());
        todoDO.status(0);

        todoDO.receipt(bean.getReceipt());
        todoDO.startTime(bean.getStartTime());
        todoDO.endTime(bean.getEndTime());
        todoDO.meetingAddress(bean.getMeetingAddress());
        todoDO.receivingSourceTenantId(bean.getReceivingSourceTenantId());
        return todoDO.build();
    }

    TodoDO convert(TodoItemUpdateReqDTO bean);

    default TodoRespVO convert(TodoDO bean) {
        if ( bean == null ) {
            return null;
        }

        TodoRespVO todoRespVO = new TodoRespVO();

        todoRespVO.setType( bean.getType() );
        todoRespVO.setUrgencyLevel(bean.getUrgencyLevel());
        todoRespVO.setTitle( bean.getTitle() );
        todoRespVO.setSubmitter( bean.getSubmitter() );
        if (StrUtil.isNotBlank(bean.getRemark())) {
            todoRespVO.setRemark(StrUtil.split(bean.getRemark(),","));
        }
        todoRespVO.setSubmitTime( bean.getSubmitTime() );
        todoRespVO.setSubsystemId( bean.getSubsystemId() );
        todoRespVO.setLinkUrl( bean.getLinkUrl() );
        todoRespVO.setTodoUserId( bean.getTodoUserId() );
        todoRespVO.setStatus( bean.getStatus() );
        todoRespVO.setProcessId( bean.getProcessId() );
        todoRespVO.setProcessType(bean.getProcessType());
        todoRespVO.setProcessStatus(bean.getProcessStatus());
        todoRespVO.setTaskCode(bean.getTaskCode());
        todoRespVO.setId( bean.getId() );
        //会议相关
        todoRespVO.setReceipt(bean.getReceipt());
        todoRespVO.setStartTime(bean.getStartTime());
        todoRespVO.setEndTime(bean.getEndTime());
        todoRespVO.setMeetingAddress(bean.getMeetingAddress());
        todoRespVO.setReceivingSourceTenantId(bean.getReceivingSourceTenantId());
        todoRespVO.setPeopleIneractionSource(bean.getPeopleIneractionSource());
        return todoRespVO;
    }

    List<TodoRespVO> convertList(List<TodoDO> list);

    PageResult<TodoRespVO> convertPage(PageResult<TodoDO> page);

    List<TodoExcelVO> convertList02(List<TodoDO> list);
    TodoDO convert(TodoCreateSimpleReqVO reqDTO);
}
