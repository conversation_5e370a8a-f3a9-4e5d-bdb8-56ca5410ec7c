package com.unicom.swdx.module.system.controller.admin.user.vo.profile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("管理后台 - 用户个人中心信息 Response VO")
public class UserProfileReqDTO {

    @ApiModelProperty(value = "文件base64", required = true, example = "1")
    private String imageBase64;

    @ApiModelProperty(value = "文件类型", required = true, example = "1")
    private String fileType;

}
