package com.unicom.swdx.module.system.controller.admin.oaNotice.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.annotations.Param;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 用户首页通知公告分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OaNoticePersonalReqVO extends PageParam {

    @ApiModelProperty(value = "标题", example = "100")
    private String title;

    @ApiModelProperty(value = "创建人姓名", example = "1")
    private String creator;

    @ApiModelProperty(value = "创建时间", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @ApiModelProperty(value = "已读状态，0未读，1已读，2全部", example = "1")
    private Integer read;

    private Long deptId;
    
    private Long userId;

}
