package com.unicom.swdx.module.system.mq.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Refresh 的消费者
 *
 * <AUTHOR>
 * @date 2023/7/6 15:53
 **/
@Component
@Slf4j
public class RefreshConsumer {

//    @Resource
//    private DeptService deptService;
//    @Resource
//    private OAuth2ClientService oauth2ClientService;
//    @Resource
//    private SensitiveWordService sensitiveWordService;
//    @Resource
//    private MenuService menuService;
//    @Resource
//    private PermissionService permissionService;
//    @Resource
//    private RoleService roleService;
//    @Resource
//    private TenantService tenantService;
//    @Resource
//    private TenantTypeService tenantTypeService;
//
//    @Bean
//    public Consumer<RefreshMessage> refresh() {
//        return refreshMessage -> {
//            String refreshTag = refreshMessage.getTag();
//            log.info("[execute][收到 {} 刷新消息]",refreshTag);
//            switch (refreshTag) {
//                case "DeptRefresh":
//                    // 部门
//                    deptService.initLocalCache();
//                    break;
//                case "ClientRefresh":
//                    // 应用
//                    oauth2ClientService.initLocalCache();
//                    break;
//                case "SensitiveWordRefresh":
//                    // 敏感词
//                    sensitiveWordService.initLocalCache();
//                    break;
//                case "MenuRefresh":
//                    // 菜单
//                    menuService.initLocalCache();
//                    break;
//                case "PermissionRefresh":
//                    // 权限
//                    permissionService.initLocalCache();
//                    break;
//                case "RoleRefresh":
//                    // 角色
//                    roleService.initLocalCache();
//                    break;
//                case "TenantRefresh":
//                    // 机构
//                    tenantService.init();
//                    break;
//                case "TenantTypeRefresh":
//                    // 机构用户类型
//                    tenantTypeService.init();
//                    break;
//                default:
//                    log.error("没有配置Consumer");
//            }
//        };
//    }

//    @Override
//    public void accept(RefreshMessage refreshMessage) {
//        String refreshTag = refreshMessage.getTag();
//        log.info("[execute][收到 {} 刷新消息]",refreshTag);
//        switch (refreshTag) {
//            case "DeptRefresh":
//                // 部门
//                deptService.initLocalCache();
//                break;
//            case "ClientRefresh":
//                // 应用
//                oauth2ClientService.initLocalCache();
//                break;
//            case "SensitiveWordRefresh":
//                // 敏感词
//                sensitiveWordService.initLocalCache();
//                break;
//            case "MenuRefresh":
//                // 菜单
//                menuService.initLocalCache();
//                break;
//            case "PermissionRefresh":
//                // 权限
//                permissionService.initLocalCache();
//                break;
//            case "RoleRefresh":
//                // 角色
//                roleService.initLocalCache();
//                break;
//            case "TenantRefresh":
//                // 机构
//                tenantService.init();
//                break;
//            case "TenantTypeRefresh":
//                // 机构用户类型
//                tenantTypeService.init();
//                break;
//            default:
//                log.error("没有配置Consumer");
//        }
//
//    }
}
