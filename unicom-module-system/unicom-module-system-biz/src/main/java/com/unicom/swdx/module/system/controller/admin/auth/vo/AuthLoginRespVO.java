package com.unicom.swdx.module.system.controller.admin.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 登录 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginRespVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    private Long userId;

    @ApiModelProperty(value = "访问令牌", required = true, example = "happy")
    private String accessToken;

    @ApiModelProperty(value = "刷新令牌", required = true, example = "nice")
    private String refreshToken;

    @ApiModelProperty(value = "过期时间", required = true)
    private LocalDateTime expiresTime;

    @ApiModelProperty(value = "初始密码是否被修改")
    private Boolean initPasswordIsChange;

    @ApiModelProperty(value = "调训单位id")
    private Long unitId;

    private String userName;
    private String deptName;

    private String tenantName;


    List<AuthLoginRespVO> authLoginRespVOList;


    private  String unionid;

    private Boolean logindefualt;

    private  String employeeId;



}
