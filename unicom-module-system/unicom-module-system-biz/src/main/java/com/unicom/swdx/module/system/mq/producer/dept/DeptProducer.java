package com.unicom.swdx.module.system.mq.producer.dept;

import com.alibaba.fastjson.JSON;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaDeptDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaHeaderDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.OldDeptDO;
import com.unicom.swdx.module.system.enums.kafka.dept.DeptEventType;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import com.unicom.swdx.module.system.service.dept.OldDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * Dept 部门相关消息的 Producer
 */
@Component
@Slf4j
public class DeptProducer extends AbstractProducer {

    @Value("${sendTopic.oldKafka}")
    private String oldKafkaTopic;

    @Value("${sendTopic.newKafka}")
    private String newKafkaTopic;

    @Resource
    private OldDeptService oldDeptService;

    @Async
    public void sendRefreshMessage() {
        log.info("[send][ Dept 发送刷新消息 ]");
        try {
            kafkaTemplate.send("refresh-service", "DeptRefresh");
        }catch (Exception e){
            log.info("[send][ Dept 发送刷新消息]  失败 ：{}" ,e.getMessage() );
        }
    }

    @Async
    public void sendDeptData(String eventType, DeptDO dept, String tenantCode) {
        log.info("[send][ 更改部门信息成功，向kafka发送老系统部门更新数据 ]");

        OldKafkaHeaderDTO header = new OldKafkaHeaderDTO();
        OldKafkaDeptDTO body = new OldKafkaDeptDTO();
        OldKafkaMessageDTO message = new OldKafkaMessageDTO();

        header.setSender("003");
        header.setEventId(UUID.randomUUID().toString());
        header.setEventType(eventType);
        header.setTimestamp(System.currentTimeMillis());

        if(Objects.equals(eventType, DeptEventType.addDeptEventType)){
            //新增部门
            if(Objects.nonNull(dept.getSort())){
                body.setDept_num(dept.getSort().toString());
            }
            body.setDept_id(dept.getId().toString());
            body.setNew_dept_id(dept.getId());
            //todo 暂时写死
            body.setParent_id(  dept.getOldparentcode()==null?"001001000000":dept.getOldparentcode());  //默认放入到在职人员的部门下面
            body.setNew_parent_id(dept.getParentId());
            body.setDept_name(dept.getName());
            body.setTenant_id(dept.getTenantId());

        } else if (Objects.equals(eventType, DeptEventType.delDeptEventType)) {
            //删除部门
            OldDeptDO oldDept = oldDeptService.getOldDeptByDeptId(dept.getId());
            if(Objects.nonNull(oldDept)){
                body.setDept_id(oldDept.getOldDeptId());
            }else {
                body.setDept_id(dept.getId().toString());
            }
            body.setNew_dept_id(dept.getId());

        } else if (Objects.equals(eventType, DeptEventType.editDeptEventType)) {
            //修改部门
            OldDeptDO oldDept = oldDeptService.getOldDeptByDeptId(dept.getId());
            if(Objects.nonNull(oldDept)){
                body.setDept_id(oldDept.getOldDeptId());
                body.setParent_id(oldDept.getParentDeptId());
                oldDept.setOldDeptName(dept.getName());
                oldDeptService.updateById(oldDept);
            }else {
                body.setDept_id(dept.getId().toString());
//                body.setParent_id(dept.getParentId().toString());
                body.setParent_id(dept.getOldparentcode()==null?"001001000000":dept.getOldparentcode());
            }
            body.setNew_dept_id(dept.getId());
            body.setNew_parent_id(dept.getParentId());
            body.setDept_name(dept.getName());
            if(Objects.nonNull(dept.getSort())){
                body.setDept_num(dept.getSort().toString());
            }
        }
        message.setBody(body);

        if(Objects.nonNull(tenantCode)){
            header.setTenant_code(tenantCode);
        }
        message.setHeader(header);

        log.info("打印----------"+JSON.toJSONString(message)+"---------------");

        if(Objects.equals(dept.getTenantId(),25L)){
            kafkaTemplate.send(oldKafkaTopic, JSON.toJSONString(message));
        }

        kafkaTemplate.send(newKafkaTopic, JSON.toJSONString(message));

    }


//    @Async
//    public void sendRefreshMessage() {
//        log.info("[send][ Dept 发送刷新消息]");
//        streamBridge.send("refresh-out-0", new RefreshMessage("DeptRefresh"));
//    }
}
