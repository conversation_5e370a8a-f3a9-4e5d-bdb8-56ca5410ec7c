package com.unicom.swdx.module.system.controller.admin.user.vo.profile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "管理后台 - 用户个人信息校验密码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserProfileVerificationPasswordReqVO {

    @ApiModelProperty(value = "密码", required = true, example = "password")
    @NotBlank(message = "密码不能为空")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String password;

}
