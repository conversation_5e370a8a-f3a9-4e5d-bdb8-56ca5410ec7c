package com.unicom.swdx.module.system.dal.dataobject.schedule;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 日程 DO
 *
 * <AUTHOR>
 */
@TableName("home_schedule")
@KeySequence("home_schedule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 日期（需加索引）
     */
    private LocalDate scheduleDate;
    /**
     * 日程内容
     */
    private String content;
    /**
     * 类型（0=默认）()
     */
    private Integer type;
    /**
     * 日程拓展内容（json存储）
     */
    private String extContent;
    /**
     * 日程拓展内容（json存储）
     */
    private String processInstanceId;

    /**
     * 日程拓展内容（json存储）
     */
    private Integer inforId;


//    /**
//     * 标题
//     */
//    private String title;
//    /**
//     * 开始时间
//     */
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
//    private Date startTime;
//    /**
//     * 结束时间
//     */
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
//    private Date endTime;
//    /**
//     * 地点
//     */
//    private String address;


//    /**
//     * 来源（0=自定义，1=指派）
//     */
//    private Integer source;

}
