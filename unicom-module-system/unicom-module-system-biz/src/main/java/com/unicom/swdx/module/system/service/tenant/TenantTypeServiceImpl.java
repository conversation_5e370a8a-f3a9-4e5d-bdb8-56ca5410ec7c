package com.unicom.swdx.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.type.*;
import com.unicom.swdx.module.system.convert.tenant.TenantTypeConvert;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeRoleDO;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantTypeMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantTypeRoleBatchMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantTypeRoleMapper;
import com.unicom.swdx.module.system.mq.producer.tenant.TenantTypeProducer;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.TENANT_TYPE_NAME_EXISTED;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.TENANT_TYPE_NOT_EXISTS;

/**
 * 机构用户类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantTypeServiceImpl extends ServiceImpl<TenantTypeMapper, TenantTypeDO> implements TenantTypeService {

    private volatile Map<Long,Set<Long>> tenantTypeRolesCache;

    @Resource
    private TenantTypeRoleMapper tenantTypeRoleMapper;
    @Resource
    private TenantTypeRoleBatchMapper tenantTypeRoleBatchMapper;
    @Resource
    private TenantTypeProducer tenantTypeProducer;
    @Resource
    @Lazy
    private PermissionService permissionService;
    @Resource
    private TenantService tenantService;

    @PostConstruct
    @Override
    public void init() {
        tenantTypeRolesCache = new HashMap<>();
        List<TenantTypeDO> tenantTypes = list();
        List<Long> tenantTypeIds = tenantTypes.stream().map(TenantTypeDO::getId).collect(Collectors.toList());
        List<TenantTypeRoleDO> typeRoleList = tenantTypeRoleMapper.selectListByTypeIdList(tenantTypeIds);
        for (Long tenantTypeId : tenantTypeIds) {
            tenantTypeRolesCache.put(tenantTypeId, typeRoleList.stream()
                    .filter(tr -> Objects.equals(tenantTypeId,tr.getTenantTypeId()))
                    .map(TenantTypeRoleDO::getRoleId)
                    .collect(Collectors.toSet()));
        }
    }

    /**
     * 创建机构用户类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTenantType(TenantTypeCreateReqVO createReqVO) {
        // 校验
        validateTenantType(createReqVO,null);
        // 插入
        TenantTypeDO tenantType = TenantTypeConvert.INSTANCE.convert(createReqVO);
        baseMapper.insert(tenantType);
        // 写入机构用户类型角色绑定表
        List<TenantTypeRoleDO> typeRoleDOList = createReqVO.getRoleIds().stream()
                .map(r -> TenantTypeRoleDO.builder()
                        .tenantTypeId(tenantType.getId())
                        .roleId(r)
                        .build())
                .collect(Collectors.toList());
        tenantTypeRoleBatchMapper.saveBatch(typeRoleDOList);

        // 发送刷新消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantTypeProducer.sendTenantTypeRefreshMessage();
            }
        });
        // 返回
        return tenantType.getId();
    }

    /**
     * 更新机构用户类型
     *
     * @param updateReqVO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantType(TenantTypeUpdateReqVO updateReqVO) {
        // 校验存在
        validateTenantTypeExists(updateReqVO.getId());
        // 校验
        validateTenantType(updateReqVO,updateReqVO.getId());

        // 更新
        TenantTypeDO updateObj = TenantTypeConvert.INSTANCE.convert(updateReqVO);
        baseMapper.updateById(updateObj);
        // 更新角色绑定表
        tenantTypeRoleMapper.deleteByTypeId(updateReqVO.getId());
        List<TenantTypeRoleDO> typeRoleDOList = updateReqVO.getRoleIds().stream()
                .map(r -> TenantTypeRoleDO.builder()
                        .tenantTypeId(updateReqVO.getId())
                        .roleId(r)
                        .build())
                .collect(Collectors.toList());
        tenantTypeRoleBatchMapper.saveBatch(typeRoleDOList);

        // 发送刷新消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantTypeProducer.sendTenantTypeRefreshMessage();
                // 刷新机构下所有用户的权限
                List<Long> tenantIds = tenantService.getListByTenantType(updateReqVO.getId()).stream().map(TenantDO::getId).collect(Collectors.toList());
                permissionService.resetAllTenantUserRoleAsync(tenantIds);
            }
        });
    }

    /**
     * 更新机构用户类型状态
     * @param updateReqVO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantTypeStatus(TenantTypeUpdateStatusReqVO updateReqVO) {
        // 校验存在
        this.validateTenantTypeExists(updateReqVO.getId());
        // 如果该机构用户类型已经被机构关联了，则不能停用

        // 更新状态
        TenantTypeDO updateObj = TenantTypeConvert.INSTANCE.convert(updateReqVO);
        baseMapper.updateById(updateObj);

        // 发送刷新消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantTypeProducer.sendTenantTypeRefreshMessage();
                // 刷新机构下所有用户的权限
                List<Long> tenantIds = tenantService.getListByTenantType(updateReqVO.getId()).stream().map(TenantDO::getId).collect(Collectors.toList());
                permissionService.resetAllTenantUserRoleAsync(tenantIds);
            }
        });
    }

    /**
     * 删除机构用户类型
     *
     * @param id 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTenantType(Long id) {
        // 校验存在
        this.validateTenantTypeExists(id);

        // 删除
        baseMapper.deleteById(id);
        // 删除角色绑定表
        tenantTypeRoleMapper.deleteByTypeId(id);

        // 发送刷新消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                tenantTypeProducer.sendTenantTypeRefreshMessage();
                // 刷新机构下所有用户的权限
                List<Long> tenantIds = tenantService.getListByTenantType(id).stream().map(TenantDO::getId).collect(Collectors.toList());
                permissionService.resetAllTenantUserRoleAsync(tenantIds);
            }
        });
    }

    /**
     * 获得机构用户类型分页
     *
     * @param pageReqVO 分页查询
     * @return 机构用户类型分页
     */
    @Override
    public PageResult<TenantTypeRespVO> getTenantTypePage(TenantTypePageReqVO pageReqVO) {
        PageResult<TenantTypeDO> typeDOPageResult = baseMapper.selectPage(pageReqVO);
        PageResult<TenantTypeRespVO> pageResult = TenantTypeConvert.INSTANCE.convertPage(typeDOPageResult);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        // 封装角色
        for (TenantTypeRespVO typeRespVO : pageResult.getList()) {
            typeRespVO.setRoleIds(tenantTypeRolesCache.get(typeRespVO.getId()));
        }
        return pageResult;
    }

    @Override
    public List<TenantTypeDO> getTenantTypeList() {
        return baseMapper.selectEnableList();
    }

    /**
     * 根据机构用户类型集合获取角色id集合
     * @param tenantTypeIds 机构用户类型集合
     * @return 角色id集合
     */
    @Override
    public Set<Long> getRoleIdsByTenantTypes(Collection<Long> tenantTypeIds) {
        return tenantTypeRoleMapper.selectListByTypeIdList(tenantTypeIds).stream().map(TenantTypeRoleDO::getRoleId).collect(Collectors.toSet());
    }

    /**
     * 根据机构用户类型id集合获得角色id集合
     * @return 机构用户类型列表
     */
    @Override
    public Set<Long> getRoleIdsByIds(Collection<Long> ids) {
        // 获取所有的启用的机构用户类型
        List<TenantTypeDO> tenantTypes = lambdaQuery()
                .eq(TenantTypeDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .in(TenantTypeDO::getId, ids)
                .list();
        if(CollectionUtil.isEmpty(tenantTypes)){
            return Collections.emptySet();
        }
        List<Long> tenantTypeIdList = tenantTypes.stream().map(TenantTypeDO::getId).collect(Collectors.toList());
        // 获取机构用户类型绑定的角色
        return getRoleIdsByTenantTypes(tenantTypeIdList);
    }

    private void validateTenantTypeExists(Long id) {
        TenantTypeDO tenantTypeDO = baseMapper.selectById(id);
        if (tenantTypeDO == null) {
            throw exception(TENANT_TYPE_NOT_EXISTS);
        }
    }

    private void validateTenantType(TenantTypeBaseVO reqVO,Long id) {
        validateTenantTypeName(reqVO.getName(),id);
    }

    private void validateTenantTypeName(String name, Long id) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        TenantTypeDO tenantTypeDO = baseMapper.selectByName(name);
        if (Objects.isNull(tenantTypeDO)) {
            return;
        }
        if (Objects.isNull(id) || !Objects.equals(tenantTypeDO.getId(),id)) {
            throw exception(TENANT_TYPE_NAME_EXISTED);
        }
    }

}
