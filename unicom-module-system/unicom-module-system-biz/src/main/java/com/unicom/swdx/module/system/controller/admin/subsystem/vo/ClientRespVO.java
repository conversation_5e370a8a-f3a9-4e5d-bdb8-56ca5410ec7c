package com.unicom.swdx.module.system.controller.admin.subsystem.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;

@ApiModel("业务中台 - 子系统 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClientRespVO {

    @ApiModelProperty(value = "应用id",required = true)
    private Long id;

    @ApiModelProperty(value = "应用标识", required = true, example = "zwfw")
    @NotBlank(message = "应用标识不能为空")
    private String code;

    @ApiModelProperty(value = "应用名", required = true, example = "土豆")
    @NotBlank(message = "应用名不能为空")
    private String name;

    @ApiModelProperty(value = "应用图标", required = true, example = "https://www.iocoder.cn/xx.png")
    @NotBlank(message = "应用图标不能为空")
    @URL(message = "应用图标的地址不正确")
    private String logo;

    @ApiModelProperty(value = "应用地址", required = true, example = "https://www.iocoder.cn")
    @NotBlank(message = "应用地址不能为空")
    private String path;

    @JsonIgnore
    private Integer sort;

}
