package com.unicom.swdx.module.system.service.todo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemCreateReqDTO;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemUpdateReqDTO;
import com.unicom.swdx.module.system.controller.admin.home.todo.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.todo.TodoDO;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 待办事项 Service 接口
 *
 * <AUTHOR>
 */
public interface TodoService {

    /**
     * 创建待办事项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createTodo(@Valid TodoItemCreateReqDTO createReqVO);

    /**
     * 创建标准的待办事项
     * @param reqVO 待办信息
     */
    void createSimpleTodo(TodoCreateSimpleReqVO reqVO);

    /**
     * 已办事项
     *
     * @param updateReqVO 更新信息
     */
    void updateTodo(@Valid TodoItemUpdateReqDTO updateReqVO);

    /**
     * 已办标准的待办事项
     * @param reqVO
     */
    void updateSimpleTodo(TodoUpdateSimpleReqVO reqVO);

    /**
     * 删除待办事项
     *
     * @param reqDTO 删除信息
     */
    void deleteTodo(@Valid TodoItemUpdateReqDTO reqDTO);

    /**
     * 删除待办事项
     *
     * @param reqDTO 删除信息
     */
    void deleteTodoWithoutTodoUser(TodoItemUpdateReqDTO reqDTO);

    /**
     * 删除待办事项
     * @param reqVO 删除信息
     */
    void deleteSimpleTodo(TodoUpdateSimpleReqVO reqVO);

    /**
     * 获得待办事项
     *
     * @param id 编号
     * @return 待办事项
     */
    TodoDO getTodo(Long id);

    /**
     * 获得待办事项列表
     *
     * @param ids 编号
     * @return 待办事项列表
     */
    List<TodoDO> getTodoList(Collection<Long> ids);

    /**
     * 获得待办事项分页
     *
     * @param pageReqVO 分页查询
     * @return 待办事项分页
     */
    PageResult<TodoDO> getTodoPage(TodoPageReqVO pageReqVO, Long loginUserId);

    /**
     * 获得待办事项列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 待办事项列表
     */
    List<TodoDO> getTodoList(TodoExportReqVO exportReqVO);

    Integer getTodoNum(Long loginUserId);

    void updateTodoRemark(@Valid TodoItemUpdateReqDTO reqDTO);

    PageResult<TodoListPageVO> getTodoListPage(TodoListPageReqVO todoListPageReqVO,Long loginUserId);

    PageResult<TodoListPageVO> getDoneListPage(TodoListPageReqVO todoListPageReqVO,Long loginUserId);

    PageResult<AppTodoListPageVO> getAppTodoListPage(TodoListPageReqVO todoListPageReqVO,Long loginUserId);

    PageResult<TodoListPageAllVO> getTodoListPageALL(TodoListPageAllReqVO todoListPageAllReqVO,Long loginUserId);

    PageResult<DoneListPageAllVO> getDoneListPageALL(DoneListPageAllReqVO doneListPageAllReqVO,Long loginUserId);

    String  getToken();

    String savaDataToServerByHutool();

}
