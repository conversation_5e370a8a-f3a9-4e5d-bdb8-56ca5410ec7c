package com.unicom.swdx.module.system.convert.dept;

import com.unicom.swdx.module.system.api.dept.dto.DeptDTO;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.controller.admin.datasyn.vo.DataDeptRespVO;
import com.unicom.swdx.module.system.controller.admin.datasyn.vo.OldDeptVo;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Objects;

@Mapper
public interface DeptConvert {

    DeptConvert INSTANCE = Mappers.getMapper(DeptConvert.class);

    List<DeptRespVO> convertList(List<DeptDO> list);

    List<DeptSimpleRespVO> convertList02(List<DeptDO> list);

    List<DeptPersonsRespVO> convertList04(List<DeptDO> list);

    DeptRespVO convert(DeptDO bean);

    DeptDO convert(DeptCreateReqVO bean);

    DeptDO convert(DeptUpdateReqVO bean);

    List<DeptRespDTO> convertList03(List<DeptDO> list);

    DeptRespDTO convert03(DeptDO bean);


    List<DataDeptRespVO> convert13(  List<DeptRespVO>  bean);

    List<OldDeptVo> convert14(List<DataDeptRespVO>  bean);

    DeptTreeVO convertTree(DeptSimpleVO bean);

    DeptTreeVO convertTree(DeptDO dept);

    List<DeptTreeVO> convertTree(List<DeptRespVO> dept);

    default DeptDTO convertDeptDTO(DeptTreeVO dept){
        DeptDTO deptDTO = new DeptDTO();

        deptDTO.setDept_name(dept.getName());

        deptDTO.setNew_dept_id(dept.getId().toString());

        deptDTO.setNew_parent_id(dept.getParentId().toString());

        if(Objects.nonNull(dept.getSort())){
            deptDTO.setDept_num(dept.getSort().toString());
        }
        return deptDTO;
    }
}
