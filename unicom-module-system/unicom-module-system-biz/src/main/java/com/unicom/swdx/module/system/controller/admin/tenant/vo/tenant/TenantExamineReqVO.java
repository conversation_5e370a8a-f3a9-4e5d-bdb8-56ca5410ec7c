package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value = "管理后台 - 机构审批 Request VO")
@Data
public class TenantExamineReqVO {

    @ApiModelProperty(value = "申请id",required = true, example = "5")
    private Long id;

    @ApiModelProperty(value = "审批状态",required = true,notes = "1 通过,2 驳回")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审批意见",required = false)
    private String remark;

    @ApiModelProperty(value = "过期时间",required = false)
    private LocalDateTime expireTime;

}
