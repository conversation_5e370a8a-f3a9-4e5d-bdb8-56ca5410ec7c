package com.unicom.swdx.module.system.service.shortcut;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutClientRespVo;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutRespVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.shortcut.ShortcutUserDO;
import com.unicom.swdx.module.system.dal.mysql.shortcut.ShortcutUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 快捷入口用户关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShortcutUserServiceImpl implements ShortcutUserService {

    @Resource
    private ShortcutUserMapper shortcutUserMapper;

    @Resource
    private ShortcutService shortcutService;

    @Override
    public void updateShortcutUser(Long userId, ShortcutUserUpdateReqVO updateReqVO) {
        Collection<Long> ids = updateReqVO.getIds();
        if (CollUtil.isEmpty(ids)) {
            ids = Collections.emptyList();
        }
        List<ShortcutRespVO> oldList = getShortcutUser(userId,false);
        Set<Long> oldIdSet = new HashSet<>();
        if (CollUtil.isNotEmpty(oldList)) {
            oldIdSet = oldList.stream().map(ShortcutRespVO::getId).collect(Collectors.toSet());
        }
        // 需要删除的快捷入口，这样操作避免删除已禁用的快捷入口
        Collection<Long> deleteIds = CollUtil.subtract(oldIdSet, ids);
        if (CollUtil.isNotEmpty(deleteIds)) {
            shortcutUserMapper.deleteByUserAndShortcutIds(userId,deleteIds);
        }
        // 需要新增的快捷入口
        Collection<Long> createIds = CollUtil.subtract(ids,oldIdSet);
        // 新增该用户的快捷入口
        List<ShortcutUserDO> shortcutUserDOList = createIds.stream()
                .map(id -> ShortcutUserDO.builder().userId(userId).shortcutId(id).build())
                .collect(Collectors.toList());
        shortcutUserMapper.insertBatch(shortcutUserDOList);
    }

    /**
     * 获得快捷入口用户关联
     *
     * @param userId 编号
     * @param isDefault 是否返回默认的快捷入口
     * @return 快捷入口用户关联
     */
    @Override
    public List<ShortcutRespVO> getShortcutUser(Long userId, boolean isDefault) {
        List<ShortcutRespVO> result = shortcutUserMapper.selectListByUserId(userId);
        // 对以上结果筛选出有权限的快捷入口
        List<ShortcutClientRespVo> shortcutList = shortcutService.getShortcutList();
        if (CollUtil.isEmpty(shortcutList)) {
            return Collections.emptyList();
        }
        Set<Long> all = new HashSet<>();
        List<ShortcutRespVO> allShortcut = new ArrayList<>();
        shortcutList.forEach(c -> {
            if (CollUtil.isNotEmpty(c.getShortcutRespVOList())) {
                all.addAll(CollectionUtils.convertSet(c.getShortcutRespVOList(),ShortcutRespVO::getId));
                allShortcut.addAll(c.getShortcutRespVOList());
            }
        });
        result = result.stream().filter(r -> all.contains(r.getId())).collect(Collectors.toList());
        // 如果用户没有绑定快捷入口，显示前10个默认的快捷入口
        if (CollUtil.isEmpty(result) && isDefault) {
            if (allShortcut.size() > 10) {
                result = CollUtil.sub(allShortcut,0,10);
            } else {
                result = allShortcut;
            }
        }
        result.sort(Comparator.comparing(r -> {
            if (r.getClientId() == 61) {
                return -1;
            }
            return 1;
        }));
        return result;
    }

}
