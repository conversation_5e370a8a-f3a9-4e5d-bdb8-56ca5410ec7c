package com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 角色 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */

@Data
public class RoleGroupBaseVO {

    @ApiModelProperty(value = "机构id", required = true, example = "1")
    private Long tenantId;

    @ApiModelProperty(value = "角色组名称", required = true, example = "管理员")
    @NotBlank(message = "角色组名称不能为空")
    @Size(max = 30, message = "角色组名称长度不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "显示顺序不能为空", required = true, example = "1024")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @ApiModelProperty(value = "备注", example = "我是一个角色")
    private String remark;

}
