package com.unicom.swdx.module.system.controller.admin.user.vo.profile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/3 9:56
 **/
@Data
@ApiModel("管理后台 - 用户密码过期剩余时间 Response VO")
public class UserPasswordUpdateTimeRespVO {

    @ApiModelProperty("是否提醒")
    private Boolean isWarn;

    @ApiModelProperty("剩余过期天数")
    private Long expiredDaysLeft;

}
