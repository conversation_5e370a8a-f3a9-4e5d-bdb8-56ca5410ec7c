package com.unicom.swdx.module.system.controller.admin.dept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.enums.NationEnum;
import com.unicom.swdx.framework.common.enums.PersonnalStatusEnum;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.hr.api.dto.PersonnalApiDO;
import com.unicom.swdx.module.hr.api.dto.PersonnalBasicVO;
import com.unicom.swdx.module.system.api.dept.dto.DeptDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.trainee.TraineeUserReqVO;
import com.unicom.swdx.module.system.convert.dept.DeptConvert;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.OldDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.yjs.*;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.OldDeptMapper;
import com.unicom.swdx.module.system.dal.mysql.message.MessageAuthorityMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.dal.mysql.todo.TodoMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserDeptMapper;
import com.unicom.swdx.module.system.dal.mysql.yjs.*;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.ApiConstants.CLIENT_CODE;

@Api(tags = "管理后台 - 部门")
@RestController
@RequestMapping("/system/dept")
@Validated
@Slf4j
public class DeptController {

    @Resource
    private KafkaTemplate<Object, Object> kafkaTemplate;

    @Value("${sendTopic.oldKafka}")
    private String oldKafkaTopic;

    @Resource
    private DeptService deptService;
    @Resource
    private AdminUserService adminUserService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private AdminUserService userService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private MessageAuthorityMapper messageAuthorityMapper;

    @Resource
    private UserDeptMapper userDeptMapper;

    @Resource
    private TodoMapper todoMapper;

    @Resource
    private DeptMapper deptMapper;


    @PostMapping("create")
    @ApiOperation("创建部门")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:dept:create')")
    public CommonResult<Long> createDept(@Valid @RequestBody DeptCreateReqVO reqVO) {
        Long deptId = deptService.createDept(reqVO);
        return success(deptId);
    }

    @PostMapping("update")
    @ApiOperation("更新部门")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:dept:update')")
    public CommonResult<Boolean> updateDept(@Valid @RequestBody DeptUpdateReqVO reqVO) {
        deptService.updateDept(reqVO);
        return success(true);
    }

    @PostMapping("delete")
    @ApiOperation("删除部门")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:dept:delete')")
    public CommonResult<Boolean> deleteDept(@RequestParam("id") Long id) {
        deptService.deleteDept(id);
        return success(true);
    }

    @GetMapping("/list")
    @ApiOperation("获取部门列表")
    @OperateLog(type = GET)
    public CommonResult<List<DeptRespVO>> getList(DeptListReqVO reqVO) {
        List<DeptRespVO> deptsList = deptService.getDeptsList(reqVO);
        return success(deptsList);
    }

    @GetMapping("/childrenDept-count")
    @ApiOperation("获取子部门个数")
    public CommonResult<Integer> getChildrenDeptCount(Long parentId) {
        return success(deptService.getChildrenDeptCount(parentId));
    }

    @GetMapping("/allDept-users")
    @ApiOperation("获取部门及子部门用户个数")
    public CommonResult<Long> getAllDeptUsers(Long parentId) {
        return success(deptService.getAllDeptUsers(parentId));
    }



    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获取部门精简信息列表", notes = "只包含被开启的部门，主要用于前端的下拉选项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "区分是否用户管理的部门列表查询", required = false, example = "1", dataTypeClass = Long.class)
    })
    @DataPermission(enable = false)
    public CommonResult<List<DeptSimpleRespVO>> getSimpleDepts(@RequestParam(required = false) Long tenantId,
                                                               @RequestParam(required = false) Integer type,
                                                               @RequestParam(required = false) Boolean isExternal) {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        // 适配前端主动传机构id
        if (Objects.nonNull(tenantId)) {
            reqVO.setTenantId(tenantId);
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);

        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        List<DeptSimpleRespVO> result = DeptConvert.INSTANCE.convertList02(list);
        Set<Long> dataPermissionDepts = deptService.getDataPermissionDepts();
        if (Objects.nonNull(type) && type == 1) {
            // 获取系统标识
            String clientCode = StrUtil.blankToDefault(WebFrameworkUtils.getClientCode(),CLIENT_CODE);
            if (permissionService.checkDeptDataPermissionDeptOnly(getLoginUserId(),clientCode)) {
                AdminUserDO userDO = adminUserService.getUser(getLoginUserId());
                dataPermissionDepts = CollUtil.newHashSet(userDO.getDeptId());
            }
        }
        for (DeptSimpleRespVO respVO : result) {
            respVO.setOptional(dataPermissionDepts.contains(respVO.getId()));
        }
        if (Boolean.TRUE.equals(isExternal)) {
            result = result.stream().filter(r -> ((Objects.equals(r.getName(), "招聘网站")) || (Objects.equals(r.getName(), "服务企业")))).collect(Collectors.toList());
        } else {
            result.removeIf(r -> Objects.equals(r.getName(), "招聘网站"));
            result.removeIf(r -> Objects.equals(r.getName(), "服务企业"));
        }
        return success(result);
    }

    @GetMapping("/list-all-simple-unlimited")
    @ApiOperation(value = "获取部门精简信息列表-通讯录专用", notes = "只包含被开启的部门，主要用于前端的下拉选项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "区分是否用户管理的部门列表查询", required = false, example = "1", dataTypeClass = Long.class)
    })
    @DataPermission(enable = false)
    public CommonResult<List<DeptSimpleRespVO>> getSimpleDeptsUnlimited(@RequestParam(required = false) Long tenantId,
                                                                        @RequestParam(required = false) Integer type,
                                                                        @RequestParam(required = false) Boolean isScreen,
                                                                        @RequestParam(required = false) Boolean isAuthority,
                                                                        @RequestParam(required = false) Integer personStatus,
                                                                        @RequestParam(required = false) Boolean hasXcx,
                                                                        @RequestParam(required = false) Boolean hasOA) {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
            if(hasXcx!=null && hasXcx){
                reqVO.setTenantId(getTenantId());
            }
        }else{
            reqVO.setTenantId(getTenantId());
        }
        // 适配前端主动传机构id
        if (Objects.nonNull(tenantId)) {
            reqVO.setTenantId(tenantId);
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        //根据这个返回各部门数量
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        //防止邮箱其他添加一个逻辑
        if(hasOA!=null){
            list.removeIf(dept -> dept.getType() != null && dept.getType()>= 100);
        }
//// 移除type不为空且包含大于100的数据（作为字符串表示的整数）的DeptDO对象
        if(isScreen !=null ) {
            list.removeIf(dept -> dept.getType() != null && dept.getType()>= 100);
            //小程序通讯录在职筛选不显示这两个部门
            if(personStatus !=null && personStatus == 1){
                list.removeIf(r -> Objects.equals(r.getName(), "在职人员"));
                list.removeIf(r -> Objects.equals(r.getName(), "退休人员"));
            }
            if(personStatus !=null && personStatus == 3){
                list.removeIf(r -> Objects.equals(r.getName(), "在职人员"));
            }
        }
        //不显示招聘网站和服务企业
        list.removeIf(r -> Objects.equals(r.getName(), "招聘网站"));
        list.removeIf(r -> Objects.equals(r.getName(), "服务企业"));
        list.removeIf(r -> Objects.equals(r.getName(), "学员"));
        list.removeIf(r -> Objects.equals(r.getName(), "研究生"));


        List<Long> rloes = roleMapper.selectAllRoleByUserId(getLoginUserId());
//        Boolean a = permissionService.isSuperAdmin(getLoginUserId());
//        Boolean b =rloes.contains(6L);
        //获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导",getTenantId());
        Long inspectorId = deptMapper.getIdByName("一、二级巡视员",getTenantId());
        //如果不为党校管理员或者超级管理员才执行或者为授权消息接口
        if(!(permissionService.isSuperAdmin(getLoginUserId())|| rloes.contains(6L) || isScreen==null)) {
            // 使用流操作过滤掉指定 ID 的数据(236为校领导部门，238为一、二级巡视员部门)
            // 假设 idsToRemove 是要删除的 ID 列表
            List<Long> idsToRemove = new ArrayList<>();
//            Long de = userService.getUser(getLoginUserId()).getDeptId();
            if (leaderId !=null && !userService.getUser(getLoginUserId()).getDeptId().equals(leaderId)) {
                idsToRemove.add(leaderId);
                if (inspectorId !=null && !userService.getUser(getLoginUserId()).getDeptId().equals(inspectorId)) {
                    idsToRemove.add(inspectorId);
                }
            }
            List<DeptDO> filteredList = list.stream()
                    .filter(deptDO -> !idsToRemove.contains(deptDO.getId())) // 过滤掉指定 ID 的数据
                    .collect(Collectors.toList()); // 将过滤后的结果收集为列表

            // 将过滤后的列表赋值给原始列表，达到删除特定 ID 数据的目的
            list.clear();
            list.addAll(filteredList);
        }

        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }
        List<Long> deptIds = new ArrayList<>();

//        for (DeptDO deptDO : list) {
//            deptIds.add(deptDO.getId());
//        }
        List<AdminUserDO> countByDeptIdAdminUserDos = userService.countByDeptIdAdminUserDosCache(deptIds,reqVO.getTenantId(),personStatus);
        HashMap<Long,Long> deptsCount = new HashMap<>();
        HashMap<Long,Long> deptCount = new HashMap<>();
        //统计多部门补充人数
        List<DeptCountVO> deptCountVOS= userService.countByDeptsCache(reqVO.getTenantId(),personStatus);
        for (DeptCountVO deptCountVO :deptCountVOS){
            deptsCount.put(deptCountVO.getDeptId(),deptCountVO.getCount());
        }
        for (AdminUserDO adminUserDO :countByDeptIdAdminUserDos){
            if(deptsCount.containsKey(adminUserDO.getDeptId())){
                adminUserDO.setId(adminUserDO.getId()+deptsCount.get(adminUserDO.getDeptId()));
            }
        }
        for (AdminUserDO c : countByDeptIdAdminUserDos){
            deptCount.put(c.getDeptId(),c.getId());
        }

        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        List<DeptSimpleRespVO> result = DeptConvert.INSTANCE.convertList02(list);
        Set<Long> dataPermissionDepts = deptService.getDataPermissionDepts();
        if (Objects.nonNull(type) && type == 1) {
            // 获取系统标识
            String clientCode = StrUtil.blankToDefault(WebFrameworkUtils.getClientCode(),CLIENT_CODE);
            if (permissionService.checkDeptDataPermissionDeptOnly(getLoginUserId(),clientCode)) {
                AdminUserDO userDO = adminUserService.getUser(getLoginUserId());
                dataPermissionDepts = CollUtil.newHashSet(userDO.getDeptId());
            }
        }

        for (DeptSimpleRespVO respVO : result) {
            respVO.setOptional(dataPermissionDepts.contains(respVO.getId()));
            respVO.setCount(deptCount.get(respVO.getId()));
        }

        //判断授权范围
        String idSt = null;
        Boolean flag = false;
        List<Long> ids = new ArrayList<>();
        if (isAuthority != null) {
            idSt = messageAuthorityMapper.getIdsById(getLoginUserId());
            flag = true;
        }
        if (idSt != null && !idSt.isEmpty()) {
            String[] idParts = idSt.split(",");
            for (String idPart : idParts) {
                // 去除字符串两端的空白字符
                idPart = idPart.trim();

                // 检查idPart是否可以转换为Long
                if (!idPart.isEmpty()) {
                    try {
                        Long id = Long.parseLong(idPart);
                        ids.add(id);
                    } catch (NumberFormatException e) {
                        System.out.println(idParts);
                        // 如果转换失败，可以记录日志或进行其他错误处理
                        e.printStackTrace(); // 例如，打印堆栈跟踪
                    }
                }
            }
            //查出相应部门
            if(ids!=null) {
                List<Long> authorityDeptIds = userMapper.getDeptIdsByUserIds(ids);
                //多部门适配
                List<Long> deptListIds = userDeptMapper.getDeptListByUserIds(ids);
                if(deptIds!=null){
// 计算 authorityDeptIds 和 deptListIds 的并集
                    List<Long> unionIds = Stream.concat(authorityDeptIds.stream(), deptListIds.stream())
                            .distinct()
                            .collect(Collectors.toList());
                    result = result.stream()
                            .filter(dept -> unionIds.contains(dept.getId()))
                            .collect(Collectors.toList());
                }else {
                    result = result.stream()
                            .filter(dept -> authorityDeptIds.contains(dept.getId()))
                            .collect(Collectors.toList());
                }
            }

        }

        if(!(permissionService.isSuperAdmin(getLoginUserId())|| rloes.contains(6L))) {
            //范围为空且存在数据就置为-1什么也查不到
            if ((idSt == null && flag)
                // || (idSt == null && messageAuthorityMapper.getById(getLoginUserId())>0)
            ) {
                result = new ArrayList<>();

            }
        }
        //统计总数
        Long sum =  result.stream()
                .filter(vo -> vo.getCount() != null) // 过滤出 count 不为 null 的元素
                .mapToLong(DeptSimpleRespVO::getCount) // 提取每个元素的 count 属性值
                .sum(); // 对所有 count 值求和
        result.stream()
                .forEach(vo -> vo.setSum(sum));

        return success(result);
    }

    @GetMapping("/list-all-simple-nodirect")
    @ApiOperation(value = "获取部门精简信息列表不包含直属机构", notes = "只包含被开启的部门，主要用于前端的下拉选项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class)
    })
    @DataPermission(enable = true)
    public CommonResult<List<DeptSimpleRespVO>> getSimpleDeptsNoDirect(@RequestParam(required = false) Long tenantId) {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        // 适配前端主动传机构id
        if (Objects.nonNull(tenantId)) {
            reqVO.setTenantId(tenantId);
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);

        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        List<DeptDO> directs = deptService.selectChildrenDeptByDeptName("直属机构");
        if (!CollectionUtils.isAnyEmpty(directs)) {
            List<Long> ids = directs.stream().map(DeptDO::getId).collect(Collectors.toList());
            list = list.stream().filter(deptDO -> !ids.contains(deptDO.getId())).collect(Collectors.toList());
        }
        return success(DeptConvert.INSTANCE.convertList02(list));
    }


    @GetMapping("/list-all-simple-path")
    @ApiOperation(value = "获取部门精简信息列表-路径", notes = "只包含被开启的部门，主要用于前端的下拉选项")
    public CommonResult<List<DeptSimpleRespVO>> getSimplePathDepts() {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        List<DeptSimpleRespVO> deptSimpleRespVOS = DeptConvert.INSTANCE.convertList02(list);
        return success(deptService.convertPath(deptSimpleRespVOS));
    }

    @GetMapping("/list-all-with-person")
    @ApiOperation(value = "获取部门人员信息列表", notes = "只包含被开启的部门，主要用于前端的下拉选项")
    public CommonResult<List<DeptPersonsRespVO>> getAllDeptPersons() {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        Map<Long, List<DeptDO>> mapDept =  list.stream().collect(Collectors.groupingBy(DeptDO::getParentId));
        List<DeptPersonsRespVO> datas =  DeptConvert.INSTANCE.convertList04(list);
        List<DeptPersonsRespVO> result = new ArrayList<>();
        datas.forEach(deptPersonsRespVO -> {
            deptPersonsRespVO.setIsPerson(false);
            deptPersonsRespVO.setHasChildren(false);
            List<DeptDO> deptDOS = mapDept.get(deptPersonsRespVO.getId());
            if (!CollectionUtils.isAnyEmpty(deptDOS)) {
                deptPersonsRespVO.setHasChildren(true);
            }
            List<Long> arrays = new ArrayList<>();
            arrays.add(deptPersonsRespVO.getId());
            deptPersonsRespVO.setUniqueSerial("dept"+ deptPersonsRespVO.getId());
            deptPersonsRespVO.setUniqueSerialParent("dept"+ deptPersonsRespVO.getParentId());
            List<AdminUserDO> doList = adminUserService.getUsersByDeptIds(arrays);
            List<DeptPersonsRespVO> personsRespVOS = UserConvert.INSTANCE.convertList5(doList, deptPersonsRespVO.getId());
            if (!CollectionUtils.isAnyEmpty(personsRespVOS)) {
                personsRespVOS.forEach(personsRespVO -> {
                    personsRespVO.setUniqueSerial("person" + personsRespVO.getId());
                });
            }
            result.addAll(personsRespVOS);
        });
        result.addAll(datas);
        result.sort(Comparator.comparing(DeptPersonsRespVO::getId));
        return success(result);
    }

    @GetMapping("/list-all-with-person-nodirect")
    @ApiOperation(value = "获取部门人员信息列表不包含直属机构", notes = "只包含被开启的部门，主要用于前端的下拉选项")
    public CommonResult<List<DeptPersonsRespVO>> getAllDeptPersonsNodirects() {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        // 通过名字找到直属机构以及子部门
        List<DeptDO> directs = deptService.selectChildrenDeptByDeptName("直属机构");
        if (!CollectionUtils.isAnyEmpty(directs)) {
            List<Long> ids = directs.stream().map(DeptDO::getId).collect(Collectors.toList());
            list = list.stream().filter(deptDO -> !ids.contains(deptDO.getId())).collect(Collectors.toList());
        }
        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        Map<Long, List<DeptDO>> mapDept =  list.stream().collect(Collectors.groupingBy(DeptDO::getParentId));
        List<DeptPersonsRespVO> datas =  DeptConvert.INSTANCE.convertList04(list);
        List<DeptPersonsRespVO> result = new ArrayList<>();
        datas.forEach(deptPersonsRespVO -> {
            deptPersonsRespVO.setIsPerson(false);
            deptPersonsRespVO.setHasChildren(false);
            List<DeptDO> deptDOS = mapDept.get(deptPersonsRespVO.getId());
            if (!CollectionUtils.isAnyEmpty(deptDOS)) {
                deptPersonsRespVO.setHasChildren(true);
            }
            List<Long> arrays = new ArrayList<>();
            arrays.add(deptPersonsRespVO.getId());
            deptPersonsRespVO.setUniqueSerial("dept"+ deptPersonsRespVO.getId());
            deptPersonsRespVO.setUniqueSerialParent("dept"+ deptPersonsRespVO.getParentId());
            List<AdminUserDO> doList = adminUserService.getUsersByDeptIds(arrays);
            List<DeptPersonsRespVO> personsRespVOS = UserConvert.INSTANCE.convertList5(doList, deptPersonsRespVO.getId());
            if (!CollectionUtils.isAnyEmpty(personsRespVOS)) {
                personsRespVOS.forEach(personsRespVO -> {
                    personsRespVO.setUniqueSerial("person" + personsRespVO.getId());
                });
            }
            result.addAll(personsRespVOS);
        });
        result.addAll(datas);
        result.sort(Comparator.comparing(DeptPersonsRespVO::getId));
        return success(result);
    }

    @GetMapping("/get")
    @ApiOperation("获得部门信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<DeptRespVO> getDept(@RequestParam("id") Long id) {
        DeptRespVO deptRespVO = DeptConvert.INSTANCE.convert(deptService.getDept(id));
        if(deptRespVO.getParentId()==0){
            deptRespVO.setIsRoot(1);
        }
        return success(deptRespVO);
    }

    @GetMapping("/getChildrenDept")
    @ApiOperation("通过上级部门获取部门列表")
    public CommonResult<List<DeptRespVO>> getChildrenDeptByDeptId(@RequestParam("parentId") Long parentId) {
        List<DeptRespVO> deptsList = deptService.getChildrenDeptByDeptId(parentId);
        return success(deptsList);
    }


    @GetMapping("/get-alldept-child")
    @ApiOperation("获取当前机关单位所有部门以及本部门ID")
    public CommonResult<Map<String, Object>> getAllDeptAndChildren(@RequestParam("deptId") Long deptId) {
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if(permissionService.isSuperAdmin(getLoginUserId())){
            reqVO.setTenantId(null);
        }else{
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        Map<String,Object> map = new HashMap<>();
        List<String> allDepts = list.stream().map(deptDO -> String.valueOf(deptDO.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isAnyEmpty(allDepts)) {
            map.put("allDepts", allDepts);
        } else {
            map.put("allDepts", new ArrayList<>());
        }
        List<DeptRespVO> deptsList = deptService.getChildrenDeptByDeptId(deptId);
        List<String> childDepts = new ArrayList<>();
        childDepts.add(String.valueOf(deptId));
        if (CollectionUtils.isAnyEmpty(deptsList)) {
            childDepts.addAll(deptsList.stream().map(deptDO -> String.valueOf(deptDO.getId())).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isAnyEmpty(childDepts)) {
            map.put("childDepts", childDepts);
        } else {
            map.put("childDepts", new ArrayList<>());
        }
        return success(map);
    }

    @GetMapping("/getDeptTree")
    @ApiOperation("获取所有部门树形结构")
    @DataPermission(enable = true)
    public CommonResult<List<DeptTreeVO>> getAllDeptTree() {
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        return success(treeDept);
    }
    @GetMapping("/getDeptRoot")
    @ApiOperation("判断是否获取用户部门")
    public CommonResult<List<DeptDO>> getDeptRoot() {
        Long id =getLoginUserId();
        List<Long> deptList = userDeptMapper.getDeptList(id);
        List<DeptDO> deptDOList = new ArrayList<>(); // 创建一个用于存储DeptDO的列表

        for (Long deptId : deptList) {
            DeptDO deptDO = deptService.getDept(deptId); // 根据每个deptId查询DeptDO
            if (deptDO != null) { // 假设getDept方法可能返回null，你可能需要检查这一点
                deptDOList.add(deptDO); // 将查询到的DeptDO添加到列表中
            }
        }
// 现在deptDOList包含了根据deptList中每个deptId查询到的DeptDO对象
        return success(deptDOList);
    }


    @GetMapping("/getDeptTreeRoot")
    @ApiOperation("获取一个部门和下级所有部门树形结构")
    @DataPermission(enable = true)
    public CommonResult<DeptTreeVO> getAllDeptTreeroot() {
        Long id =getLoginUserId();

        Long parentId=userService.getUser(id).getDeptId();

        DeptDO deptDO =  deptService.getDept(parentId);
        List<Long> roletempDOList = todoMapper.selectByTenantIdBpm(deptDO.getTenantId());
//        Long tenantId = deptDO.getTenantId();
//        int type = 1;
        //判断是否为管理员
        if(roletempDOList!=null&&roletempDOList.contains(id)){
            List<DeptTreeVO> treeDept = deptService.getTreeDept();
            if(!treeDept.isEmpty()) {
                return success(treeDept.get(0));
            }
        }

//        ////判断是否为管理员(roletempDOList!=null&&roletempDOList.contains(id))
//        if(
//                (roletempDOList!=null&&roletempDOList.contains(id))
//                        //判断是否为处长(deptDO.getLeaderUserId()!=null&&(deptDO.getLeaderUserId().intValue()==id)
//                        || (deptDO.getLeaderUserId()!=null&&(deptDO.getLeaderUserId().intValue()==id)
//                )
//            //判断是否为管理员
////        || permissionService.isSuperAdmin(SecurityFrameworkUtils.getLoginUserId())
//        ) {
//            List<DeptTreeVO> treeDept = deptService.getTreeDept();
//            return success(treeDept.get(0));
//        }

        DeptTreeVO  deptTreeVO = DeptConvert.INSTANCE.convertTree(deptDO);

//        if(NumberUtil.equals(parentId, 106)){

        List<DeptRespVO> deptsList = deptService.getChildrenDeptByDeptId(parentId);
        deptsList = deptsList.stream()
                .sorted(Comparator.comparing(DeptRespVO::getSort).thenComparing(DeptRespVO::getId))
                .collect(Collectors.toList());

        if(deptTreeVO==null){
            return success(null);
        }
        deptTreeVO.setChildren(DeptConvert.INSTANCE.convertTree(deptsList));


//        }

        return success(deptTreeVO);
    }


    @GetMapping("/getDeptTreeRoot1")
    @ApiOperation("获取一个部门和下级所有部门树形结构")
    @DataPermission(enable = true)
    public CommonResult<DeptTreeVO> getAllDeptTreeroot1() {
        Long id =getLoginUserId();





        Long parentId=deptService.getDeptIdByUserId(getLoginUserId());
        DeptDO deptDO =  deptService.getDept(parentId);

        DeptTreeVO  deptTreeVO = DeptConvert.INSTANCE.convertTree(deptDO);

//        if(NumberUtil.equals(parentId, 106)){

            List<DeptRespVO> deptsList = deptService.getChildrenDeptByDeptId(parentId);
            deptsList = deptsList.stream()
                .sorted(Comparator.comparing(DeptRespVO::getSort).thenComparing(DeptRespVO::getId))
                .collect(Collectors.toList());

            if(deptTreeVO==null){
                return success(null);
            }
            deptTreeVO.setChildren(DeptConvert.INSTANCE.convertTree(deptsList));


//        }

        return success(deptTreeVO);
    }






    @GetMapping("/getDeptTreeId")
    @ApiOperation("获取所有部门树id")
    @DataPermission(enable = true)
    public CommonResult<List<Long>> getAllDeptTreeId() {
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptTreeId = deptService.getDeptTreeId(new ArrayList<>(),treeDept);
        return success(deptTreeId);
    }

    @GetMapping("/getDeptList")
    @ApiOperation("相应所有部门根据userId")
    @DataPermission(enable = true)
    public CommonResult<List<Long>> getDeptList(Long userId) {
        List<Long> deptList = userDeptMapper.getDeptList(userId);
        return success(deptList);
    }

    @GetMapping("/sendAllDeptInfo")
    @ApiOperation("发送全量的部门数据到教务系统")
    public CommonResult<Boolean> sendAllPersonInfo(@RequestParam("url") String url) {
        deptService.sendAllDeptInfo(url);
        return success(true);
    }


    @GetMapping("/getAllDeptInfo")
    @ApiOperation("得到全量的部门数据")
    public CommonResult<List<DeptDTO>> getAllDeptInfo(@RequestParam("url") String url) {
        List<DeptDTO> dtoList = deptService.getalllistdept(url);
        return success(dtoList);
    }


    @PostMapping("/testpermision")
    @ApiOperation("permision测试")
    @PermitAll
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 5000)
    @TenantIgnore
    public CommonResult<Boolean> testpermision(@Valid @RequestBody   String code ) {

        permissionService.testassignRoleMenu();

        return success(true);
    }


        @GetMapping("/sendKafkaMessage")
    @ApiOperation("发送kafka")
    public CommonResult<Boolean> sendKafkaMessage(@RequestBody JSONObject jsonObject) {
        log.info( "sendKafkaMessage  == {}"   , JSON.toJSONString(jsonObject));
        kafkaTemplate.send(oldKafkaTopic, JSON.toJSONString(jsonObject));
        return success(true);
    }


    @GetMapping("/getDeptIgnoreDeleted")
    @ApiOperation("getDeptIgnoreDeleted")
    public CommonResult<DeptDO> getDeptIgnoreDeleted() {
       DeptDO deptDO =  deptService.getDeptIgnoreDeleted(135L);
        return success(deptDO);
    }

    @Resource
    private ConfigurableEnvironment env;


    @Resource
    private ApplicationContext applicationContext;

    @GetMapping("/printBean")
    public CommonResult<String> printBean() {

        Map<String, Object> all = env.getSystemProperties();

        for (Map.Entry<String, Object> entry : all.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
        }

        return success("0");
    }


    @Resource
    YjsStuMapper yjsStuMapper;


    @Resource
    YjsAddMapper yjsAddMapper;


    @Resource
    TongXunLuMapper tongXunLuMapper;

    @Resource
    YjsStuYZMapper yjsStuYZMapper;


    @Resource
    ClassInfoMapper classInfoMapper;

    @Resource
    ClassInfoYjsMapper classInfoYjsMapper;

    @Resource
    ClassInfoDeptMapper classInfoDeptMapper;


    @Resource
    OldPersonnalInforMapper oldPersonnalInforMapper;


    @Resource
    OldPersonnalInfoYzMapper oldPersonnalInfoYzMapper;

    @Resource
    PersonnalApi personnalApi;


    @GetMapping("/yjs")
    public CommonResult< List<YjsStuDO>> yjs() {





        List<YjsStuDO> yjsStuDOList = yjsStuMapper.selectList();

        List<YjsStuDO> yjsyzDOList =   yjsStuYZMapper.selectList();


//        List<YjsStuDO> intersection = (List<YjsStuDO>) CollUtil.intersection(yjsStuDOList, yjsyzDOList);

        // Difference (yjsStuDOList - yjsyzDOList)
        List<YjsStuDO> difference = (List<YjsStuDO>) CollUtil.subtract(yjsStuDOList, yjsyzDOList);

        try {
            difference.forEach(it->{

                YjsStuDO yjsStuDO =  yjsStuYZMapper.selectById(it.getId());
                if(ObjectUtil.isNotEmpty(yjsStuDO)){
                    yjsStuYZMapper.updateById(it);
                }else{
                    yjsStuYZMapper.insert(it);

                    TraineeUserReqVO reqVO = new TraineeUserReqVO();
                    reqVO.setTraineeId("009" + it.getId());
                    reqVO.setMobile(it.getPhone());
                    reqVO.setNickname(it.getStuName());
                    reqVO.setTenantId("25");
                    try {
                        reqVO.setSex(Integer.parseInt(it.getSex()));
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    userService.createYJSUser(reqVO);
                }





            });
        }catch (Exception e){
            e.printStackTrace();
        }


        List<AdminUserDO> adminUserDOList =  userService.getListByTenantId(25L);



        //把需要新增的用户同步一下
        LambdaQueryWrapper<YjsAddDO> yjsAddDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        yjsAddDOLambdaQueryWrapper.eq(YjsAddDO::getAdduser , 0 );
        yjsAddDOLambdaQueryWrapper.eq(YjsAddDO::getCeshi , 0 );
        List<YjsAddDO>  yjsAddDOS =  yjsAddMapper.selectList(yjsAddDOLambdaQueryWrapper);


        List<YjsAddDO> filteredYjsAddDOS = yjsAddDOS.stream()
                .filter(yjsAdd -> adminUserDOList.stream()
                        .noneMatch(adminUser -> adminUser.getMobile().equals(yjsAdd.getPhone())))
                .collect(Collectors.toList());


        System.out.println(filteredYjsAddDOS);

        for (int i = 0; i < filteredYjsAddDOS.size(); i++) {

            YjsAddDO it = filteredYjsAddDOS.get(i);

            TraineeUserReqVO reqVO = new TraineeUserReqVO();
            reqVO.setTraineeId(it.getOtherSysId());
            reqVO.setMobile(it.getPhone());
            reqVO.setNickname(it.getName());
            reqVO.setTenantId("25");
            reqVO.setSex(3);
            userService.createYJSUser(reqVO);

            LambdaUpdateWrapper<YjsAddDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(YjsAddDO::getPhone , it.getPhone());
            updateWrapper.set(YjsAddDO::getAdduser , 1);
            yjsAddMapper.update(null, updateWrapper);
        }






        //把通讯录需要新增的用户同步一下
        LambdaQueryWrapper<TongXunLuDO> tongXunLuDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tongXunLuDOLambdaQueryWrapper.eq(TongXunLuDO::getAdduser , 0 );

        List<TongXunLuDO>  tongXunLuDOS =  tongXunLuMapper.selectList(tongXunLuDOLambdaQueryWrapper);



        List<TongXunLuDO> filtered = tongXunLuDOS.stream()
                .filter(yjsAdd -> adminUserDOList.stream()
                        .noneMatch(adminUser -> adminUser.getMobile().equals(yjsAdd.getMobile())))
                .collect(Collectors.toList());


        for (int i = 0; i < filtered.size(); i++) {

            TongXunLuDO it = filtered.get(i);


            TraineeUserReqVO reqVO = new TraineeUserReqVO();
            reqVO.setMobile(it.getMobile());
            reqVO.setNickname(it.getName());
            reqVO.setTenantId("25");
            reqVO.setSex(3);
            userService.createYJSUser(reqVO);

            LambdaUpdateWrapper<TongXunLuDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TongXunLuDO::getMobile , it.getMobile());
            updateWrapper.set(TongXunLuDO::getAdduser , 1);
            tongXunLuMapper.update(null, updateWrapper);

        }

        return success(yjsStuDOList);
    }


    /**
     * 同步研究生班级到 业中并创建部门
     * @return
     */
    @GetMapping("/yjsClass")
    public CommonResult< List<ClassInfoDO>> yjsclass() {

        List<ClassInfoDO> yjsStuDOList = classInfoYjsMapper.selectList();

        List<ClassInfoDO> yjsyzDOList =   classInfoMapper.selectList();

        List<ClassInfoDO> difference = (List<ClassInfoDO>) CollUtil.subtract(yjsStuDOList, yjsyzDOList);

        try {
            difference.forEach(it->{

                ClassInfoDO yjsStuDO =  classInfoMapper.selectById(it.getId());
                if(ObjectUtil.isNotEmpty(yjsStuDO)){
                    classInfoMapper.updateById(it);
                }else{
                    classInfoMapper.insert(it);

                    DeptCreateReqVO deptCreateReqVO = new DeptCreateReqVO();

                    deptCreateReqVO.setParentId(495L); //挂在党校研究生下边
//                    deptCreateReqVO.
                    deptCreateReqVO.setName(it.getClassName());
                    Long id = deptService.createDept(deptCreateReqVO);


                    LambdaUpdateWrapper<ClassInfoYzDO> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(ClassInfoYzDO::getId , it.getId());
                    updateWrapper.set(ClassInfoYzDO::getDeptId , id);
                    classInfoDeptMapper.update(null, updateWrapper);

                }

            });
        }catch (Exception e){
            e.printStackTrace();
        }

        return success(yjsStuDOList);
    }


    /**
     *  教职工信息同步到业中
     * @return
     */
    @GetMapping("/studentInfor")
    public CommonResult< List<ClassInfoDO>> studentInfor() {




        // Create the JSON body
        JSONObject json = new JSONObject();
        JSONObject requestParam = new JSONObject();
        requestParam.put("Condition", "1='1'");

        // DataItems array
        JSONObject[] dataItems = new JSONObject[13];
        String[] names = {"name", "sex", "nation", "phone", "birthday", "id_card", "jiguan", "_rank", "culture", "zzmm", "post", "work_time", "id"};

        for (int i = 0; i < names.length; i++) {
            JSONObject item = new JSONObject();
            item.put("Fmt", "");
            item.put("Name", names[i]);
            dataItems[i] = item;
        }

        // ResourceInfos
        JSONObject resourceInfo = new JSONObject();
        resourceInfo.put("DataItems", dataItems);
        resourceInfo.put("ResourceName", "R-13010005-00000048");

        // Adding resourceInfo to an array and then to requestParam
        requestParam.put("ResourceInfos", new JSONObject[]{resourceInfo});

        // OtherCondition
        JSONObject otherCondition = new JSONObject();
        otherCondition.put("AsyncOnceReturnNum", "2");
        otherCondition.put("CallbackID", "");
        otherCondition.put("AsyncIdentity", "0");
        otherCondition.put("MaxReturnNum", "5000000");
        otherCondition.put("AsynQuery", "");
        otherCondition.put("CodeMode", "0");
        otherCondition.put("SortResults", "");

        // Adding OtherCondition to requestParam
        requestParam.put("OtherCondition", otherCondition);

        // Adding requestParam to main JSON
        json.put("RequestParam", requestParam);
        json.put("From", "A-130100-0001");
        json.put("To", "110000000001");
        json.put("MessageSequence", "2019010714141200001");

        // Sending the POST request
        HttpResponse response = HttpRequest.post("http://59.231.14.20:8687/DtStudio/daweb/daportal/apiController/hndxSjztExt")
                .header("SenderID", "A-130100-0001")
                .header("serviceResourceId", "130100-0100-00001")
                .header("userId", "ezmap")
                .header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .body(json.toString())
                .execute();


        OldUsersResponse yzUsers = JSON.parseObject(response.body(), OldUsersResponse.class);


        if(ObjectUtil.isNotEmpty(yzUsers)){

            if(ObjectUtil.isNotEmpty( yzUsers.getResponseParam() )){



                if(CollectionUtil.isNotEmpty( yzUsers.getResponseParam().getResourceInfos() )){


                    List<List<String>> dataInfo   = yzUsers.getResponseParam().getResourceInfos().get(0).getDataInfo();


                    for (int i = 0; i < dataInfo.size(); i++) {

                        List<String>  tempdata = dataInfo.get(i);

                        LambdaQueryWrapperX<OldPersonnalInforDO> queryWrapperX = new LambdaQueryWrapperX<>();
                        queryWrapperX.eq(OldPersonnalInforDO::getId , tempdata.get(12));



                        OldPersonnalInforDO exit =  oldPersonnalInforMapper.selectOne(queryWrapperX);


                        if(ObjectUtil.isEmpty(exit)){





                            OldPersonnalInforDO temp = new OldPersonnalInforDO();
                            temp.setName(tempdata.get(0));

                            temp.setSex(tempdata.get(1));

                            temp.setNation(tempdata.get(2));

                            temp.setPhone(tempdata.get(3));

                            temp.setBirthday(tempdata.get(4));
                            temp.setIdCard(tempdata.get(5));
                            temp.setJiguan(tempdata.get(6));

                            temp.setRank(tempdata.get(7));

                            temp.setCulture(tempdata.get(8));
                            temp.setZzmm(tempdata.get(9));
                            temp.setPost(tempdata.get(10));

                            temp.setWorkTime(tempdata.get(11));
                            temp.setId(tempdata.get(12));




                            //查看系统里边的 教职工是否存在
                            PersonnalApiDO personnalApiDO = personnalApi.getHrUserByMobileorName(temp.getPhone() , temp.getName());


                            if(ObjectUtil.isEmpty(personnalApiDO)){



                                //原来业中不存在这个教职工 添加教职工
                                PersonnalBasicVO personnalBasicVO = new PersonnalBasicVO();
                                personnalApiDO.setName(temp.getName());
                                personnalApiDO.setPhoto(temp.getPhone());
                                personnalApiDO.setGender(Integer.valueOf(temp.getSex()));

                                personnalApiDO.setNation(Integer.valueOf(temp.getNation()));

                                personnalApiDO.setBirthday( LocalDateTime.parse(temp.getBirthday(), DatePattern.NORM_DATETIME_FORMATTER) );



//                                personnalApi.createUser()








                            }else{
                                //原来业中存在这个教职工
                                LambdaUpdateWrapper<OldPersonnalInforDO> updateWrapper = new LambdaUpdateWrapper<>();
                                updateWrapper.eq(OldPersonnalInforDO::getId , temp.getId());
                                updateWrapper.set(OldPersonnalInforDO::getUserId , personnalApiDO.getUserId());
                                oldPersonnalInforMapper.update(temp , updateWrapper);

                            }










                            oldPersonnalInforMapper.insert(temp);





                        }








                    }













                }











            }















        }



        return null;


    }


    @Resource
    OldDeptMapper oldDeptMapper;

    /**
     *  教职工信息同步到业中
     * @return
     */
    @GetMapping("/personnalInfor")
    public OldUsersResponse personnalInfor() {




        // Create the JSON body
        JSONObject json = new JSONObject();
        JSONObject requestParam = new JSONObject();
        requestParam.put("Condition", "1='1'");

        // DataItems array
        JSONObject[] dataItems = new JSONObject[17];
        String[] names = {"NAME", "SEX", "NATION", "mobile_phone", "BIRTHDAY", "ID_CODE", "NATIVE_PLACE", "STAFF_RANK", "EDUCATION", "POLITY", "staff_type_", "POST", "EMPLOYEE_ID", "dept_name", "DEPT_ID", "ZYJSRYZYLB_", "STATUS"};

        for (int i = 0; i < names.length; i++) {
            JSONObject item = new JSONObject();
            item.put("Fmt", "");
            item.put("Name", names[i]);
            dataItems[i] = item;
        }

        // ResourceInfos
        JSONObject resourceInfo = new JSONObject();
        resourceInfo.put("DataItems", dataItems);
        resourceInfo.put("ResourceName", "R-13010005-00000013");

        // Adding resourceInfo to an array and then to requestParam
        requestParam.put("ResourceInfos", new JSONObject[]{resourceInfo});

        // OtherCondition
        JSONObject otherCondition = new JSONObject();
        otherCondition.put("AsyncOnceReturnNum", "2");
        otherCondition.put("CallbackID", "");
        otherCondition.put("AsyncIdentity", "0");
        otherCondition.put("MaxReturnNum", "5000000");
        otherCondition.put("AsynQuery", "");
        otherCondition.put("CodeMode", "0");
        otherCondition.put("SortResults", "");

        // Adding OtherCondition to requestParam
        requestParam.put("OtherCondition", otherCondition);

        // Adding requestParam to main JSON
        json.put("RequestParam", requestParam);
        json.put("From", "A-130100-0001");
        json.put("To", "110000000001");
        json.put("MessageSequence", "2019010714141200001");

        // Sending the POST request
        HttpResponse response = HttpRequest.post("http://59.231.14.20:8687/DtStudio/daweb/daportal/apiController/hndxSjztExt")
                .header("SenderID", "A-130100-0001")
                .header("serviceResourceId", "130100-0100-00001")
                .header("userId", "ezmap")
                .header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .body(json.toString())
                .execute();


        OldUsersResponse yzUsers = JSON.parseObject(response.body(), OldUsersResponse.class);


        if(ObjectUtil.isNotEmpty(yzUsers)){

            if(ObjectUtil.isNotEmpty( yzUsers.getResponseParam() )){



                if(CollectionUtil.isNotEmpty( yzUsers.getResponseParam().getResourceInfos() )){


                    List<List<String>> dataInfo   = yzUsers.getResponseParam().getResourceInfos().get(0).getDataInfo();


                    for (int i = 0; i < dataInfo.size(); i++) {

                        List<String>  tempdata = dataInfo.get(i);

                        LambdaQueryWrapperX<OldYzPersonnalInforDO> queryWrapperX = new LambdaQueryWrapperX<>();
                        queryWrapperX.eq(OldYzPersonnalInforDO::getEmployeeId , tempdata.get(12));



                        OldYzPersonnalInforDO exit =  oldPersonnalInfoYzMapper.selectOne(queryWrapperX);


                        if(ObjectUtil.isEmpty(exit) || exit.getUserId()==null){





                            OldYzPersonnalInforDO temp = new OldYzPersonnalInforDO();
                            temp.setName(tempdata.get(0));

                            temp.setSex(tempdata.get(1));

                            temp.setNation(tempdata.get(2));

                            temp.setMobilePhone(tempdata.get(3));

                            temp.setBirthday(tempdata.get(4));
                            temp.setIdCode(tempdata.get(5));
                            temp.setNativePlace(tempdata.get(6));

                            temp.setStaffRank(tempdata.get(7));

                            temp.setEducation(tempdata.get(8));
                            temp.setPolity(tempdata.get(9));

                            temp.setStaffType(tempdata.get(10));



                            temp.setPost(tempdata.get(11));


                            temp.setEmployeeId(tempdata.get(12));

                            temp.setDeptName(tempdata.get(13));

                            temp.setDeptId(tempdata.get(14));


                            temp.setZyjsryzylb(tempdata.get(15));
                            temp.setStatus(tempdata.get(16));



                            if(exit==null){
                                oldPersonnalInfoYzMapper.insert(temp);
                                exit =  oldPersonnalInfoYzMapper.selectOne(queryWrapperX);
                            }


                            //查看系统里边的 教职工是否存在
                            PersonnalApiDO personnalApiDO = personnalApi.getHrUserByMobileorName(temp.getMobilePhone() , temp.getName());


                            if(ObjectUtil.isEmpty(personnalApiDO) || exit.getUserId()==null){



                                //原来业中不存在这个教职工 添加教职工
                                PersonnalBasicVO personnalBasicVO = new PersonnalBasicVO();
                                personnalBasicVO.setName(temp.getName());
                                personnalBasicVO.setMobile(StrUtil.isEmpty(temp.getMobilePhone())?"0" :temp.getMobilePhone());


                                personnalBasicVO.setPeronClassification(8);

                                personnalBasicVO.setPersonnalStatus(PersonnalStatusEnum.getCodeByName(temp.getStatus()));



                                if("女".equals(temp.getSex())){
                                    temp.setSex("2");
                                }

                                if("男".equals(temp.getSex())){
                                    temp.setSex("1");
                                }

                                personnalBasicVO.setGender(temp.getSex()==null?3:Integer.valueOf(temp.getSex()));



                                personnalBasicVO.setNation( NationEnum.getCodeByName(temp.getNation()) );

                                if(temp.getBirthday()!=null){
                                    // 补充日期和时间信息，例如默认补充为当月的第一天和午夜时间
                                    String dateTimeStr = temp.getBirthday() + "-01T00:00:00";


                                    personnalBasicVO.setBirthday( LocalDateTime.parse(dateTimeStr , DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")) );
                                }


                                personnalBasicVO.setIdType(1);
                                personnalBasicVO.setIdNumber(temp.getIdCode()==null?"":temp.getIdCode());

                                personnalBasicVO.setNativePlace(temp.getNativePlace());


                                OldDeptDO oldDeptDO  =  oldDeptMapper.selectOne("old_dept_id" ,    temp.getDeptId());


                                if(ObjectUtil.isNotEmpty(oldDeptDO)){
                                    personnalBasicVO.setDepartment(oldDeptDO.getDeptId());
                                }



                                PersonnalApiDO personnalApiDO1 =  personnalApi.createUser(personnalBasicVO);

                                LambdaUpdateWrapper<OldYzPersonnalInforDO> updateWrapper = new LambdaUpdateWrapper<>();
                                updateWrapper.eq(OldYzPersonnalInforDO::getEmployeeId , temp.getEmployeeId());
                                updateWrapper.set(OldYzPersonnalInforDO::getUserId , personnalApiDO1.getUserId());
                                oldPersonnalInfoYzMapper.update(temp , updateWrapper);

                            }else{
                                //原来业中存在这个教职工
                                LambdaUpdateWrapper<OldYzPersonnalInforDO> updateWrapper = new LambdaUpdateWrapper<>();
                                updateWrapper.eq(OldYzPersonnalInforDO::getEmployeeId , temp.getEmployeeId());
                                updateWrapper.set(OldYzPersonnalInforDO::getUserId , personnalApiDO.getUserId());
                                oldPersonnalInfoYzMapper.update(temp , updateWrapper);

                            }


                        }

                    }


                }











            }















        }



        return yzUsers;

    }


}
