package com.unicom.swdx.module.system.job.yezhong;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.edu.api.teacherinformation.TeacherinformationApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalRespVO;
import com.unicom.swdx.module.system.service.businesscenter.BusinessCenterService;
import com.unicom.swdx.module.system.service.yezhong.YezhongService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Zhe
 * @Description: 业中同步相关定时 Job
 * @date 2024-12-18
 */
@Service
@Slf4j
public class YeZhongJob {

    @Resource
    YezhongService yezhongService;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private BusinessCenterService businessCenterService;

    @Resource
    private TeacherinformationApi teacherinformationApi;



    // @Scheduled(fixedRate = 300000) // 300000 毫秒 = 5 分钟
    @XxlJob("SyncDictJob")
    public void performTask() {
        log.info("同步业中字典数据 - 开始定时任务");
        try {
            yezhongService.getDictType();
            yezhongService.getDictData();
        } catch (Exception e) {
            log.error("同步业中字典数据 - 定时任务执行失败！原因：{}", e.getMessage(), e);
            return;
        }
        log.info("同步业中字典数据 - 定时任务执行成功！");
    }

    // @Scheduled(fixedRate = 100000) // 300000 毫秒 = 5 分钟
    @XxlJob("SyncTeacherJob")
    public void performTaskteacher(Long mytenantId) {


        log.info("同步教师 - 开始定时任务");
        List<Long> tenantIds = tenantApi.getTenantIds().getCheckedData();
        for (int i = 0; i < tenantIds.size(); i++) {
            long tenantid = tenantIds.get(i);

            if(mytenantId!=null && mytenantId!=tenantid){
                continue;//指定了租户 这里不相等就跳过
            }


            log.info("同步教师 - 租户id：" + tenantid);
            try {
                AuthLoginRespVO authLoginRespVO = yezhongService.loginUserAdmin(true);
                List<BusinessCenterPersonnalRespVO> personRespDTOS = businessCenterService.getPersonnalList(tenantid, authLoginRespVO.getAccessToken());

                if (CollectionUtil.isNotEmpty(personRespDTOS)) {
                    redisUtil.set("BusinessCenter::getPersonnal:" + tenantid, JSONUtil.toJsonStr(personRespDTOS) ,24*60*60*1000);
                }

            } catch (Exception e) {
                log.error("同步教师 - 定时任务执行失败！原因：{}", e.getMessage(), e);
                return;
            }

            // 使用 Hutool 进行接口调用
            String apiUrl = "http://localhost:48087/admin-api/edu/teacherInformation/sync"; // 这里是调用的接口地址
            String response = HttpRequest.post(apiUrl)
                    .form("tenantId", tenantid)  // 传递 tenantId 参数
                    .execute()
                    .body();
        }
        log.info("同步教师 - 定时任务执行成功！");
    }

    public void syncTeacher(Long tenantId) {
        log.info("同步教师 - 开始任务");
        log.info("同步教师 - 租户id：" + tenantId);
        try {
            AuthLoginRespVO authLoginRespVO = yezhongService.loginUserAdmin(true);
            List<BusinessCenterPersonnalRespVO> personRespDTOS = businessCenterService.getPersonnalList(tenantId, authLoginRespVO.getAccessToken());

            if (CollectionUtil.isNotEmpty(personRespDTOS)) {
                redisUtil.set("BusinessCenter::getPersonnal:" + tenantId, JSONUtil.toJsonStr(personRespDTOS));
            }

        } catch (Exception e) {
            log.error("同步教师 - 定时任务执行失败！原因：{}", e.getMessage(), e);
            return;
        }

        teacherinformationApi.convertTeacherAndInsert(tenantId);



        log.info("同步教师 - 任务执行成功！");
    }
}
