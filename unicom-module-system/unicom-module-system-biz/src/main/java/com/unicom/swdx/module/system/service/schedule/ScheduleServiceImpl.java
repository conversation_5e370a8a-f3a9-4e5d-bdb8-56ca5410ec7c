package com.unicom.swdx.module.system.service.schedule;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.home.schedule.vo.*;
import com.unicom.swdx.module.system.convert.schedule.ScheduleConvert;
import com.unicom.swdx.module.system.dal.dataobject.calendarwnl.CalendarWnlDO;
import com.unicom.swdx.module.system.dal.dataobject.schedule.ScheduleDO;
import com.unicom.swdx.module.system.dal.mysql.schedule.ScheduleMapper;
import com.unicom.swdx.module.system.enums.home.schedule.ScheduleTypeEnum;
import com.unicom.swdx.module.system.service.common.CalendarWnlService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.*;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.SCHEDULE_NOT_EXISTS;

/**
 * 日程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ScheduleServiceImpl extends ServiceImpl<ScheduleMapper,ScheduleDO> implements ScheduleService {

    @Resource
    private CalendarWnlService calendarWnlService;

    @Resource
    private ScheduleMapper scheduleMapper;

    /**
     * 创建日程
     * @param createReqVO 创建信息
     */
    @Override
    public void createSchedule(ScheduleCreateReqVO createReqVO) {
        ScheduleDO schedule = ScheduleConvert.INSTANCE.convert(createReqVO);
        // 默认日程绑定用户为登录用户
        schedule.setUserId(getLoginUserId());
        // 日程类型默认
        schedule.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
        baseMapper.insert(schedule);
    }

    /**
     * 创建日程
     * @param createReqVO 创建信息
     */
    @Override
    public void createScheduleByUser(ScheduleCreateReqVO createReqVO) {
        ScheduleDO schedule = ScheduleConvert.INSTANCE.convert(createReqVO);
        // 默认日程绑定用户为登录用户
        schedule.setUserId(createReqVO.getUserId());
        // 日程类型默认
        schedule.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
        baseMapper.insert(schedule);
    }

    /**
     * 创建日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Integer createScheduleOther(ScheduleCreateReqVO createReqVO) {
        ScheduleDO schedule = ScheduleConvert.INSTANCE.convert(createReqVO);
        //开始日期和结束日期
        Date startTime = createReqVO.getStartTime();
        Date endTime = createReqVO.getEndTime();
        Instant instant = startTime.toInstant();
        Instant endInstant = endTime.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
        LocalDate startDate = instant.atZone(zoneId).toLocalDate();
        LocalDate endDate = endInstant.atZone(zoneId).toLocalDate();
        // 使用String.format来格式化字符串并插入日期
        String content = null;
        if(createReqVO.getType().equals(12)) {
            content = String.format("外出报告\n开始时间：%s 结束时间：%s", startDate, endDate);
        }else if(createReqVO.getType().equals(11)){
            content = String.format("外出讲学\n开始时间：%s 结束时间：%s", startDate, endDate);
        }else if(createReqVO.getType().equals(10)){
            content = String.format("请假\n开始时间：%s 结束时间：%s", startDate, endDate);
        }
        //创建一个列表
        List<ScheduleDO> scheduleDOList = new ArrayList<>();
        // 使用日期范围进行循环
        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
            // 为每个日期创建一个新的日程对象（假设Schedule有一个构造函数）
            ScheduleDO scheduleOne = new ScheduleDO();
            scheduleOne.setProcessInstanceId(createReqVO.getProcessInstanceId());
            scheduleOne.setType(createReqVO.getType());
            scheduleOne.setScheduleDate(currentDate);
            // 默认日程绑定用户为自选
            scheduleOne.setUserId(createReqVO.getUserId());
            //日程内容
            scheduleOne.setContent(content);
//            // 日程类型默认
//            scheduleOne.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
            scheduleDOList.add(scheduleOne);
        }
//            //要创建列表插入先插入一条
//        schedule.setScheduleDate(startDate);
//        // 默认日程绑定用户为自选
//        schedule.setUserId(createReqVO.getUserId());
//        //日程内容
//        schedule.setContent(content);
//        // 日程类型默认
//        schedule.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
//        System.out.println(scheduleDOList);
        baseMapper.insertBatch(scheduleDOList);
        return 1;
    }

    /**
     * 创建中心工作相关日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Integer createScheduleInfor(ScheduleCreateReqVO createReqVO) {
        ScheduleDO schedule = ScheduleConvert.INSTANCE.convert(createReqVO);
        //开始日期和结束日期
        Date startTime = createReqVO.getStartTime();
        Date endTime = createReqVO.getEndTime();
        Instant instant = startTime.toInstant();
        Instant endInstant = endTime.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
        LocalDate startDate = instant.atZone(zoneId).toLocalDate();
        LocalDate endDate = endInstant.atZone(zoneId).toLocalDate();
        //任务名称
        String taskname = createReqVO.getTaskname();
        // 使用String.format来格式化字符串并插入日期
        String content = String.format("【%s】 \n开始日期：%s 结束日期：%s", taskname,startDate, endDate);
        //月度中心工作101 年度102 ，临时103
        if(createReqVO.getYeartag() == 2) {
            createReqVO.setType(101);
        }else if(createReqVO.getYeartag() == 1){
            createReqVO.setType(102);
        }else {
            createReqVO.setType(103);
        }
        //创建一个列表
        List<ScheduleDO> scheduleDOList = new ArrayList<>();
        // 使用日期范围进行循环
        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
            // 为每个日期创建一个新的日程对象（假设Schedule有一个构造函数）
            ScheduleDO scheduleOne = new ScheduleDO();
//            scheduleOne.setProcessInstanceId(createReqVO.getProcessInstanceId());
            //设置
            scheduleOne.setInforId(createReqVO.getInforId());
            scheduleOne.setType(createReqVO.getType());
            scheduleOne.setScheduleDate(currentDate);
            // 默认日程绑定用户为自选
            scheduleOne.setUserId(createReqVO.getUserId());
            //日程内容
            scheduleOne.setContent(content);
//            // 日程类型默认
//            scheduleOne.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
            scheduleDOList.add(scheduleOne);
        }
        baseMapper.insertBatch(scheduleDOList);
        return 1;
    }

    /**
     * 创建多人日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Integer createSchedulesOther(ScheduleCreateReqVO createReqVO) {
        ScheduleDO schedule = ScheduleConvert.INSTANCE.convert(createReqVO);
        //第几周和年份
        Integer year = createReqVO.getYear();
        Integer week = createReqVO.getWeek();
        //开始日期和结束日期
        Date startTime = createReqVO.getStartTime();
        Date endTime = createReqVO.getEndTime();
        Instant instant = startTime.toInstant();
        Instant endInstant = endTime.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
        LocalDate startDate = instant.atZone(zoneId).toLocalDate();
        LocalDate endDate = endInstant.atZone(zoneId).toLocalDate();
        // 使用String.format来格式化字符串并插入日期
        String content = String.format("%s年第%s周主要活动安排\n开始日期：%s 结束日期：%s",
                year,week,startDate,endDate);

        //创建一个列表
        List<ScheduleDO> scheduleDOList = new ArrayList<>();
        //用户列表
        List<Long> users = createReqVO.getUserIds();
        // 使用日期范围进行循环
        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
            // 为每个日期创建一个新的日程对象（假设Schedule有一个构造函数）
            for (Long userId : users) {
                ScheduleDO scheduleOne = new ScheduleDO();
                scheduleOne.setProcessInstanceId(createReqVO.getProcessInstanceId());
                scheduleOne.setType(createReqVO.getType());
                scheduleOne.setScheduleDate(currentDate);
                // 默认日程绑定用户为自选
                scheduleOne.setUserId(userId);
                //日程内容
                scheduleOne.setContent(content);
//            // 日程类型默认
//            scheduleOne.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
                scheduleDOList.add(scheduleOne);
            }
        }
        baseMapper.insertBatch(scheduleDOList);
        return 1;
    }

    /**
     * 删除日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Integer deleteScheduleOther(ScheduleCreateReqVO createReqVO) {
        ScheduleDO schedule = ScheduleConvert.INSTANCE.convert(createReqVO);
        //process_instance_id和schedule_date和user_id流程id和日程时间和用户id
        //先查出所有的日程再删除相应的日程
        List<ScheduleDO> scheduleDOLists = scheduleMapper.getScheduleByUserIdPId(createReqVO);
        Date oldDate = createReqVO.getEndTime();
        // 将Date转换为Instant
        Instant instant = oldDate.toInstant();

        // 使用系统默认时区将Instant转换为ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());

        // 从ZonedDateTime中获取LocalDate
        LocalDate localDate = zonedDateTime.toLocalDate();
        List<ScheduleDO> filteredSchedules = scheduleDOLists.stream().filter(scheduleDO ->
                scheduleDO.getScheduleDate().isAfter(localDate))
                .collect(Collectors.toList());
        List<Long> scheduleIds = filteredSchedules.stream() // 假设scheduleDOLists是List<ScheduleDO>
                .map(scheduleDO -> scheduleDO.getId()) // 获取每个ScheduleDO的id
                .collect(Collectors.toList()); // 收集到List<Long>中
        if(!scheduleIds.isEmpty()) {
            baseMapper.deleteBatchIds(scheduleIds);
        }
        //设置新的结束时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String oldDateString = dateFormat.format(oldDate);
        String content = scheduleDOLists.get(0).getContent();
        // 替换字符串中的结束时间
        String newContent = content
                .replaceAll("结束时间：\\d{4}-\\d{2}-\\d{2}", "结束时间：" + oldDateString);
        //        //更新结束时间
        List<ScheduleDO> updateScheduleDOLists = scheduleMapper.getScheduleByUserIdPId(createReqVO);
        if(updateScheduleDOLists!=null){
            for (ScheduleDO scheduleDO : updateScheduleDOLists){
                scheduleDO.setContent(newContent);
                scheduleMapper.updateById(scheduleDO);
            }
            // 解析日期字符串中的开始时间和结束时间
        }
//
//        //开始日期和结束日期
//        Date startTime = createReqVO.getStartTime();
//        Date endTime = createReqVO.getEndTime();
//        instant = startTime.toInstant();
//        Instant endInstant = endTime.toInstant();
//        ZoneId zoneId = ZoneId.systemDefault();
//        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
//        LocalDate startDate = instant.atZone(zoneId).toLocalDate();
//        LocalDate endDate = endInstant.atZone(zoneId).toLocalDate();
//        // 使用String.format来格式化字符串并插入日期
//        String content = null;
//        if(createReqVO.getType().equals(12)) {
//            content = String.format("外出报告\n开始时间：%s 结束时间：%s", startDate, endDate);
//        }else if(createReqVO.getType().equals(11)){
//            content = String.format("外出讲学\n开始时间：%s 结束时间：%s", startDate, endDate);
//        }else if(createReqVO.getType().equals(10)){
//            content = String.format("请假\n开始时间：%s 结束时间：%s", startDate, endDate);
//        }
//        //创建一个列表
//        List<ScheduleDO> scheduleDOList = new ArrayList<>();
//        // 使用日期范围进行循环
//        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
//            // 为每个日期创建一个新的日程对象（假设Schedule有一个构造函数）
//            ScheduleDO scheduleOne = new ScheduleDO();
//            scheduleOne.setProcessInstanceId(createReqVO.getProcessInstanceId());
//            scheduleOne.setType(createReqVO.getType());
//            scheduleOne.setScheduleDate(currentDate);
//            // 默认日程绑定用户为自选
//            scheduleOne.setUserId(createReqVO.getUserId());
//            //日程内容
//            scheduleOne.setContent(content);
////            // 日程类型默认
////            scheduleOne.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
//            scheduleDOList.add(scheduleOne);
//        }
////            //要创建列表插入先插入一条
////        schedule.setScheduleDate(startDate);
////        // 默认日程绑定用户为自选
////        schedule.setUserId(createReqVO.getUserId());
////        //日程内容
////        schedule.setContent(content);
////        // 日程类型默认
////        schedule.setType(ScheduleTypeEnum.DEFAULT_TYPE.getType());
//        baseMapper.insertBatch(scheduleDOList);
        return 1;
    }
    /**
     * 删除日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Integer deleteScheduleInfor(ScheduleCreateReqVO createReqVO) {
        if(createReqVO.getInforId()!=null) {
            LambdaQueryWrapperX<ScheduleDO> scheduleDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            scheduleDOLambdaQueryWrapperX.eq(ScheduleDO::getInforId, createReqVO.getInforId());
            baseMapper.delete(scheduleDOLambdaQueryWrapperX);
            return 1;
        }else {
            return 0;
        }
    }
    /**
     * 更新日程
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateSchedule(ScheduleUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateScheduleExists(updateReqVO.getId());
        // 更新
        ScheduleDO updateObj = ScheduleConvert.INSTANCE.convert(updateReqVO);
        baseMapper.updateById(updateObj);
    }

    /**
     * 删除日程
     * @param id 编号
     */
    @Override
    public void deleteSchedule(Long id) {
        // 校验存在
        this.validateScheduleExists(id);
        // 删除
        baseMapper.deleteById(id);
    }

    /**
     * 根据查询条件获取日程列表
     * @param reqVO 查询条件
     * @return 日程列表
     */
    @Override
    public List<ScheduleDO> getList(ScheduleQueryReqVO reqVO) {
        // 1. 查询type为14的记录
        List<ScheduleDO> type14Schedules = lambdaQuery()
                .eq(ScheduleDO::getType, 14)
                .eq(ScheduleDO::getScheduleDate, reqVO.getDate())
                .eq(ScheduleDO::getUserId, getLoginUserId())
                .list();
        // 2. 查询其他记录（即type不为14的记录）
        List<ScheduleDO> otherSchedules = lambdaQuery()
                .ne(ScheduleDO::getType, 14)
                .eq(ScheduleDO::getScheduleDate, reqVO.getDate())
                .eq(ScheduleDO::getUserId, getLoginUserId())
                .orderByDesc(ScheduleDO::getCreateTime)
                .list();
        // 3. 合并结果，type为14的在前
        List<ScheduleDO> allSchedules = new ArrayList<>();
        allSchedules.addAll(type14Schedules);
        allSchedules.addAll(otherSchedules);

// 现在allSchedules就是按照你的要求排序的列表
        return allSchedules;
    }

    /**
     * 获取一个月的日程
     * @param reqVO 查询条件
     * @return 一个月日程
     */
    @Override
    public List<CalendarWnlScheduleRespVO> getListByMonth(ScheduleQueryReqVO reqVO) {
        LocalDate startDate = this.getMonthStartDate(reqVO.getDate());
        LocalDate endDate = this.getMonthEndDate(reqVO.getDate());
        // 1、获取当前一个月的万年历
        List<CalendarWnlDO> calendarList = calendarWnlService.getCalendarInRange(startDate,endDate);
        // 2、获取当月万年历日期范围内的所有日程
        List<ScheduleDO> scheduleList = lambdaQuery()
                .between(ScheduleDO::getScheduleDate, startDate, endDate)
                .eq(ScheduleDO::getUserId,getLoginUserId())
                .orderByDesc(ScheduleDO::getId)
                .list();
        // 3、封装结果
        List<CalendarWnlScheduleRespVO> result = new ArrayList<>();
        calendarList.forEach(c -> {
            result.add(CalendarWnlScheduleRespVO.builder()
                    .solarCalendar(c.getGregorianDate())
                    .lunarCalendar(CharSequenceUtil.equals("初一",c.getLunarDay())?c.getLunarMonth():c.getLunarDay())
                    .dateType(c.getHolidayType())
                    .year(c.getGregorianDate().getYear())
                    .month(c.getGregorianDate().getMonthValue())
                    .dayOfMonth(c.getGregorianDate().getDayOfMonth())
                    .isToday(c.getGregorianDate().equals(LocalDate.now()))
                    .isThisMonth(isInThisMonth(c.getGregorianDate(),reqVO.getDate()))
                    // 获取日期的日程
                    .schedules(ScheduleConvert.INSTANCE.convertList(scheduleList.stream()
                            .filter(s -> s.getScheduleDate().isEqual(c.getGregorianDate()))
                            .collect(Collectors.toList())))
                    .build());
        });
        return result;
    }

    /**
     * 判断时间是否在本月之内
     * @param time 日期
     * @return 是否是本月的
     */
    public static boolean isInThisMonth(LocalDate time,LocalDate now) {
        return time.isAfter(now.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth())) &&
                time.isBefore(now.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()));
    }

    /**
     * 获取万年历当月的第一天，从星期日开始
     * 例如：2024-01-01，当天是星期一，则返回星期日2023-12-31
     * @param date 日期
     * @return 开始日期
     */
    private LocalDate getMonthStartDate(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfMonth()).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
    }

    /**
     * 获取万年历当月的最后一天，从星期六结束
     * 例如：2024-01-31，当天是星期三，则返回星期六2024-02-03
     * @param date 日期
     * @return 结束日期
     */
    private LocalDate getMonthEndDate(LocalDate date) {
        return date.with(TemporalAdjusters.lastDayOfMonth()).with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY));
    }

    /**
     * 校验日程的id是否存在
     * @param id 日程id
     */
    private void validateScheduleExists(Long id) {
        if (Objects.isNull(baseMapper.selectById(id))) {
            throw exception(SCHEDULE_NOT_EXISTS);
        }
    }

}
