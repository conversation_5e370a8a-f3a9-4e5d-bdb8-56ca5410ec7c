package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 租户更新 Request VO")
@Data
@ToString(callSuper = true)
public class TenantCheckRuleUpdateReqVO {

    @ApiModelProperty(value = "机构id",required = true,example = "205")
    @NotNull(message = "机构id不能为空")
    private Long id;

    @ApiModelProperty(value = "是否开启午别考勤规则",required = true,example = "true")
    @NotNull(message = "是否开启午别考勤规则")
    private Boolean checkByPeriodRule;


}
