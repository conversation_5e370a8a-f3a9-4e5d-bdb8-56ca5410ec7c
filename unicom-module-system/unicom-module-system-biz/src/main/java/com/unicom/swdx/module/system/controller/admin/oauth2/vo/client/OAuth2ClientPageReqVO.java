package com.unicom.swdx.module.system.controller.admin.oauth2.vo.client;

import lombok.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;

@ApiModel("管理后台 - OAuth2 客户端分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OAuth2ClientPageReqVO extends PageParam {

    @ApiModelProperty(value = "应用名", example = "土豆", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "应用标识", example = "system", notes = "模糊匹配")
    private String code;

    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举")
    private Integer status;

}
