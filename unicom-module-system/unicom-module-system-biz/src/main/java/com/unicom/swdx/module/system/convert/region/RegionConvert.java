package com.unicom.swdx.module.system.convert.region;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.region.dto.RegionDTO;
import com.unicom.swdx.module.system.controller.admin.common.vo.region.RegionSimpleRespVO;
import com.unicom.swdx.module.system.dal.dataobject.region.RegionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 地区 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RegionConvert {

    RegionConvert INSTANCE = Mappers.getMapper(RegionConvert.class);

    RegionSimpleRespVO convert(RegionDO bean);

    List<RegionSimpleRespVO> convertList(List<RegionDO> list);

    List<RegionDTO> convertList01(List<RegionDO> list);

    PageResult<RegionSimpleRespVO> convertPage(PageResult<RegionDO> page);

}
