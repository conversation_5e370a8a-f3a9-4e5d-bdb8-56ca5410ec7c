package com.unicom.swdx.module.system.dal.redis.sms;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

import static com.unicom.swdx.module.system.dal.redis.RedisKeyConstants.MOBILE_MESSAGE_TIMES_DAY;


@Repository
@Slf4j
public class MessageTimesRedisDAO {

    @Resource
    private  StringRedisTemplate stringRedisTemplate;

    public Long get(String mobile) {
        String redisKey = formatKey(mobile);
        String times = stringRedisTemplate.opsForValue().get(redisKey);
        return StrUtil.isBlank(times)?0:Long.parseLong(times);
    }

    public void set(String mobile) {
        String redisKey = formatKey(mobile);
        Long increment = stringRedisTemplate.opsForValue().increment(redisKey);
        log.info("手机号为{}, 当天已发送次数：{}",mobile,increment);
        // 设置过期时间为当天的最后一秒
        Assert.notNull(increment);
        if (increment <= 1) {
            stringRedisTemplate.expireAt(redisKey,DateUtil.endOfDay(new Date()));
        }
    }
    private static String formatKey(String mobile) {
        return String.format(MOBILE_MESSAGE_TIMES_DAY.getKeyTemplate(), mobile);
    }

}
