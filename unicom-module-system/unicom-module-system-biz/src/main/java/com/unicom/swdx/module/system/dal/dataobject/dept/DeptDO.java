package com.unicom.swdx.module.system.dal.dataobject.dept;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import lombok.*;

import java.util.List;

/**
 * 组织表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("system_dept")
@KeySequence("system_dept_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptDO extends TenantBaseDO {

    /**
     * 组织ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 组织名称
     */
    private String name;
    /**
     * 父组织ID
     *
     * 关联 {@link #id}
     */
    private Long parentId;
    /**
     * 显示顺序
     */
    private Integer sort;
    /**
     * 负责人
     *
     * 关联 {@link AdminUserDO#getId()}
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long leaderUserId;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 组织状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;


    private String code;


//    @TableField(typeHandler = StringListTypeHandler.class)
//    private List<String> type;

    private Integer type;

    @TableField(value = "opcode")
    private String oldparentcode;



}
