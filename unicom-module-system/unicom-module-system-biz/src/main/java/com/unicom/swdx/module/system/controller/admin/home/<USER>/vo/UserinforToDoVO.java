package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;


/**
 * 重点任务关系 DO
 *
 * <AUTHOR>
 */

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserinforToDoVO {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 办理人id
     */
    @TableField(value = "user_id")
    private Integer uid;
    /**
     * 办理人名称
     */
    @TableField(value = "user_name")
    private String uname;
    /**
     * 办理人类型
     */
    @TableField(value = "user_type")
    private Integer utype;
    /**
     * 任务id
     */
    @TableField(value = "infor_id")
    private Integer inforid;
    /**
     * 办理人部门id
     */
    @TableField(value = "dept_id")
    private Integer dptid;
    /**
     * 办理人部门名称
     */
    @TableField(value = "dept_name")
    private String dptname;

    private Integer treecode;


    private Long tenantId;




}
