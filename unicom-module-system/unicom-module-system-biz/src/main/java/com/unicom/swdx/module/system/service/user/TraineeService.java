package com.unicom.swdx.module.system.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.system.dal.dataobject.user.TraineeUserDO;

import java.util.List;

public interface TraineeService extends IService<TraineeUserDO> {
    void createTrainee(TraineeUserDO traineeUserDO);

    TraineeUserDO getTraineeUserByTraineeId(String traineeId);

    TraineeUserDO getTraineeUserByUserId(Long userId);

    TraineeUserDO getTraineeUserByUserId(Long userId,String othersystemid);

    List<TraineeUserDO> getTraineeUsersByUserId(Long userId);

}
