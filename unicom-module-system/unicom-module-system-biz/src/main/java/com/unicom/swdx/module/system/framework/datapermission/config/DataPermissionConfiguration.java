package com.unicom.swdx.module.system.framework.datapermission.config;

import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(AdminUserDO.class, "dept_id");
            rule.addDeptColumn(DeptDO.class, "id");
            // user
            // rule.addUserColumn(AdminUserDO.class, "id");
            //rule.addUserColumn(AdminUserDO.class, "creator");
            rule.addUserColumn(DeptDO.class,"creator");
        };
    }

}
