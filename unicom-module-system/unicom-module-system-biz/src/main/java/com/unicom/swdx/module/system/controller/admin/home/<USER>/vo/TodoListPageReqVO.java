package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.bind.DefaultValue;

@Data
public class TodoListPageReqVO extends PageParam {
    @ApiModelProperty(value = "事项发起人", required = false)
    private Long loginUserId;

    @ApiModelProperty(value = "是否为App", required = false)
    private Integer isApp=0;


}
