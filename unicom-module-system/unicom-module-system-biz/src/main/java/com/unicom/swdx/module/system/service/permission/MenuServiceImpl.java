package com.unicom.swdx.module.system.service.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.collection.SetUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.MenuCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.MenuListReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.MenuUpdateReqVO;
import com.unicom.swdx.module.system.convert.permission.MenuConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.mysql.permission.MenuMapper;
import com.unicom.swdx.module.system.enums.permission.MenuIdEnum;
import com.unicom.swdx.module.system.enums.permission.MenuTypeEnum;
import com.unicom.swdx.module.system.mq.producer.permission.MenuProducer;
import com.unicom.swdx.module.system.service.shortcut.ShortcutService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;
import static java.util.Collections.singleton;

/**
 * 菜单 Service 实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MenuServiceImpl extends ServiceImpl<MenuMapper, MenuDO> implements MenuService {

    /**
     * 菜单缓存
     * key：菜单编号
     * <p>
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private  ConcurrentHashMap<Long, MenuDO> menuCache = new ConcurrentHashMap<>();
    /**
     * 权限与菜单缓存
     * key：权限 {@link MenuDO#getPermission()}
     * value：MenuDO  对象
     * <p>
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    private  ConcurrentHashMap<String, MenuDO> permissionMenuCache = new ConcurrentHashMap<>();

    @Resource
    @Lazy
    private PermissionService permissionService;
    @Resource
    private MenuProducer menuProducer;
    @Resource
    private RoleService roleService;
    @Resource
    private ShortcutService shortcutService;

    public synchronized void deletecache() {

        menuCache.clear();
        permissionMenuCache.clear();
    }

    /**
     * 初始化 {@link #menuCache} 和 {@link #permissionMenuCache} 缓存
     */
    @Override
    @PostConstruct
    public synchronized void initLocalCache() {
        // 获取菜单列表，如果有更新
        List<MenuDO> menuList = list();
        if (CollUtil.isEmpty(menuList)) {
            return;
        }

        menuList.forEach(menuDO -> {
            menuCache.put(menuDO.getId(), menuDO);
            if (StrUtil.isNotEmpty(menuDO.getPermission())) { // 会存在 permission 为 null 的情况，导致 put 报 NPE 异常
                permissionMenuCache.put(menuDO.getPermission(), menuDO);
            }
        });

        log.info("[initLocalCache][缓存菜单，数量为:{}]", menuList.size());
    }

    /**
     * 创建菜单
     *
     * @param reqVO 菜单信息
     * @return 创建出来的菜单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMenu(MenuCreateReqVO reqVO) {
        // 校验父菜单存在
        checkParentResource(reqVO.getParentId(), null);
        // 校验菜单（自己），分应用
        checkResource(reqVO.getParentId(), reqVO.getName(), null, reqVO.getClientId());
        // 插入数据库
        MenuDO menu = MenuConvert.INSTANCE.convert(reqVO);
        initMenuProperty(menu);
        baseMapper.insert(menu);
        // 发送刷新消息. 注意，需要事务提交后，在进行发送刷新消息。不然 db 还未提交，结果缓存先刷新了
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                menuProducer.sendRefreshMessage();
                shortcutService.refreshShortcut();
            }

        });
        // 返回
        return menu.getId();
    }

    /**
     * 更新菜单
     *
     * @param reqVO 菜单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMenu(MenuUpdateReqVO reqVO) {
        // 校验更新的菜单是否存在
        if (this.getById(reqVO.getId()) == null) {
            throw ServiceExceptionUtil.exception(MENU_NOT_EXISTS);
        }
        // 校验父菜单存在
        checkParentResource(reqVO.getParentId(), reqVO.getId());
        // 校验菜单（自己）
        checkResource(reqVO.getParentId(), reqVO.getName(), reqVO.getId(), reqVO.getClientId());
        // 更新到数据库
        MenuDO updateObject = MenuConvert.INSTANCE.convert(reqVO);
        initMenuProperty(updateObject);
        this.updateById(updateObject);
        // 发送刷新消息. 注意，需要事务提交后，在进行发送刷新消息。不然 db 还未提交，结果缓存先刷新了
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                menuProducer.sendRefreshMessage();
                shortcutService.refreshShortcut();
            }

        });
    }

    /**
     * 删除菜单
     *
     * @param menuId 菜单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(Long menuId) {
        // 校验是否还有子菜单
        if (baseMapper.selectCountByParentId(menuId) > 0) {
            throw ServiceExceptionUtil.exception(MENU_EXISTS_CHILDREN);
        }
        // 校验删除的菜单是否存在
        if (this.getById(menuId) == null) {
            throw ServiceExceptionUtil.exception(MENU_NOT_EXISTS);
        }
        // 标记删除
        baseMapper.deleteById(menuId);
        // 删除授予给角色的权限
        permissionService.processMenuDeleted(menuId);
        // 发送刷新消息. 注意，需要事务提交后，在进行发送刷新消息。不然 db 还未提交，结果缓存先刷新了
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                menuProducer.sendRefreshMessage();
                shortcutService.refreshShortcut();
            }

        });
    }


    @Resource
    TenantService tenantService;
    /**
     * 筛选菜单列表
     *
     * @param reqVO 筛选条件请求 VO
     * @return 菜单列表
     */
    @Override
    public List<MenuDO> getMenus(MenuListReqVO reqVO) {


        List<MenuDO> allmenu =  baseMapper.selectList(reqVO);

        if (permissionService.isSuperAdmin(WebFrameworkUtils.getLoginUserId())) {
            return  allmenu;
        }



        TenantDO tenantDO  = tenantService.getTenant(SecurityFrameworkUtils.getLoginUser().getTenantId());

        Long admin_userId  =  tenantDO.getContactUserId();

        //获得dx管理员用户拥有的角色编号集合
        Set<Long> adminrols =   permissionService.getUserRoleIdListByUserId(admin_userId);


        Set<Long> menuids = new HashSet<>();
        adminrols.forEach(it->{
            menuids.addAll(permissionService.getRoleMenuIds(it));
        });



        List<MenuDO> allmenufilter = allmenu.stream()
                .filter(menu -> menuids.contains(menu.getId()))
                .collect(Collectors.toList());


        return MenuFilter.filterMenu(allmenu , allmenufilter);
    }

    /**
     * 获得菜单
     *
     * @param id 菜单编号
     * @return 菜单
     */
    @Override
    public MenuDO getMenu(Long id) {

        if(menuCache==null || menuCache.isEmpty()){
            initLocalCache();
        }

        MenuDO menuDO = menuCache.get(id);
        if (Objects.isNull(menuDO)) {
            menuDO = getById(id);
        }
        return menuDO;
    }

    /**
     * 获取角色拥有的所有菜单权限
     *
     * @param roleId 角色id
     * @return 菜单列表
     */
    @Override
    public List<MenuDO> getClientMenusByRoleId(Long roleId) {
        RoleDO role = roleService.getRoleById(roleId);
        // 获得角色列表
        Set<Long> roleIds;
        // 角色所在机构拥有的角色
        if (Objects.nonNull(role.getTenantId()) && !Objects.equals(role.getTenantId(), 0L) && !Objects.equals(role.getTenantId(), 1L)) {
            // 非超管机构，筛选出机构拥有的角色
            roleIds = permissionService.getRoleIdListByTenantId(role.getTenantId());
        } else {
            roleIds = permissionService.getAllEnableRoleIdListFromCache(getLoginUserId());
        }
        // 获得用户拥有的菜单列表
        List<MenuDO> menuList = permissionService.getRoleMenuListIncludeParentsFromCache(roleIds,
                SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(), MenuTypeEnum.BUTTON.getType()), // 只要目录和菜单类型
                singleton(CommonStatusEnum.ENABLE.getStatus())); // 只要开启的
        // 只查询出当前应用的有权限的菜单列表
        menuList = menuList.stream().filter(menuDO -> menuDO.getClientId().equals(role.getClientId())).collect(Collectors.toList());
        return menuList;
    }


//    /**
//     * 根据应用id获取菜单列表
//     * @param clientId 应用id
//     * @return 菜单集合
//     */
//    @Override
//    public List<MenuDO> getClientMenus(Long clientId) {
//        // 过滤菜单为当前用户所在机构有权限的菜单
//        // 获得角色列表
//        Set<Long> roleIds = permissionService.getAllEnableRoleIdListFromCache(getLoginUserId());
//        // 获得用户拥有的菜单列表
//        List<MenuDO> menuList = permissionService.getRoleMenuListFromCache(roleIds,
//                SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(),MenuTypeEnum.BUTTON.getType()), // 只要目录和菜单类型
//                singleton(CommonStatusEnum.ENABLE.getStatus())); // 只要开启的
//        // 只查询出当前应用的有权限的菜单列表
//        menuList = menuList.stream().filter(menuDO -> menuDO.getClientId().equals(clientId)).collect(Collectors.toList());
//
//        return menuList;
//    }


//    @Override
//    public List<MenuDO> getTenantMenus(MenuListReqVO reqVO) {
//        List<MenuDO> menus = getMenus(reqVO);
//        // 开启多租户的情况下，需要过滤掉未开通的菜单
//        //tenantService.handleTenantMenu(menuIds -> menus.removeIf(menu -> !CollUtil.contains(menuIds, menu.getId())));
//        return menus;
//    }


    /**
     * 获得所有菜单，从缓存中
     * 任一参数为空时，则返回为空
     *
     * @param menuTypes     菜单类型数组
     * @param menusStatuses 菜单状态数组
     * @return 菜单列表
     */
    @Override
    public List<MenuDO> getMenuListFromCache(Collection<Integer> menuTypes, Collection<Integer> menusStatuses) {

        if(menuCache==null ||  menuCache.isEmpty()){
            initLocalCache();
        }

        // 任一一个参数为空，则返回空
        if (CollectionUtils.isAnyEmpty(menuTypes, menusStatuses)) {
            return Collections.emptyList();
        }
        // 创建新数组，避免缓存被修改
        return menuCache.values().stream().filter(menu -> menuTypes.contains(menu.getType())
                        && menusStatuses.contains(menu.getStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 获得指定编号的菜单数组，从缓存中
     * <p>
     * 任一参数为空时，则返回为空
     *
     * @param menuIds       菜单编号数组
     * @param menuTypes     菜单类型数组
     * @param menusStatuses 菜单状态数组
     * @return 菜单数组
     */
    @Override
    public List<MenuDO> getMenuListFromCache(Collection<Long> menuIds, Collection<Integer> menuTypes,
                                             Collection<Integer> menusStatuses) {

        if(menuCache==null ||  menuCache.isEmpty()){
            initLocalCache();
        }

        // 任一一个参数为空，则返回空
        if (CollectionUtils.isAnyEmpty(menuIds, menuTypes, menusStatuses)) {
            return Collections.emptyList();
        }
        return menuCache.values().stream().filter(menu -> menuIds.contains(menu.getId())
                        && menuTypes.contains(menu.getType())
                        && menusStatuses.contains(menu.getStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 获得权限标识对应的菜单数组
     *
     * @param permission 权限标识
     * @return 数组
     */
    @Override
    public List<MenuDO> getMenuListByPermissionFromCache(String permission) {


        if(permissionMenuCache==null || permissionMenuCache.isEmpty()){
            initLocalCache();
        }

        MenuDO menuDO  = permissionMenuCache.get(permission);
        ArrayList arrayList  = new ArrayList<>();
        arrayList.add(menuDO);
        return arrayList;
    }

    /**
     * 补全菜单的父级菜单
     *
     * @param menuIds 菜单
     * @return 菜单
     */
    @Override
    public Set<Long> completeMenuIdParents(List<Long> menuIds) {
        if (CollUtil.isEmpty(menuIds)) {
            return Collections.emptySet();
        }
        Set<Long> menuIdSet = new HashSet<>(menuIds);
        for (Long menuId : menuIds) {
            Set<Long> parentsId = getAllParentsId(menuId);
            if (CollUtil.isNotEmpty(parentsId)) {
                menuIdSet.addAll(parentsId);
            }
        }
        return menuIdSet;
    }


    /**
     * 获取所有的快捷入口
     *
     * @return 菜单
     */
    @Override
    public List<MenuDO> getAllShortcutMenus() {

        if(menuCache==null ||  menuCache.isEmpty()){
            initLocalCache();
        }

        return menuCache.values().stream().filter(MenuDO::getShortcut).collect(Collectors.toList());
    }

    /**
     * 获取菜单的快捷入口路径
     *
     * @param id 菜单jid
     * @return 快捷入口路径
     */
    @Override
    public String getShortcutPath(Long id) {

        if(menuCache==null ||   menuCache.isEmpty()){
            initLocalCache();
        }


        String path = "";
        for (int i = 0; i < 10; i++) {
            MenuDO menuDO = menuCache.get(id);
            if (Objects.isNull(menuDO)) {
                break;
            }
            if (Objects.equals(id, 0L)) {
                break;
            }
            path = formatPath(menuDO.getPath() + path);
            id = menuDO.getParentId();
        }
        return path;
    }

    /* ---------------------------------------------------- */

    /**
     * 格式化路由
     *
     * @param path 路由
     * @return 路由
     */
    private String formatPath(String path) {
        if (!StrUtil.startWith(path, "/")) {
            return "/" + path;
        }
        return path;
    }

    /**
     * 获取单个菜单id的所有父级菜单，限制10级菜单
     *
     * @param menuId 菜单id
     * @return 父级菜单id
     */
    private Set<Long> getAllParentsId(Long menuId) {

        if(menuCache==null ||   menuCache.isEmpty()){
            initLocalCache();
        }

        Set<Long> result = new HashSet<>();
        Long tempId = menuId;
        for (int i = 0; i < 10; i++) {
            // 最多循环10次
            MenuDO menuDO = menuCache.get(tempId);
            if (Objects.isNull(menuDO)) {
                break;
            }
            if (Objects.equals(tempId, 0L)) {
                break;
            }
            result.add(menuDO.getParentId());
            tempId = menuDO.getParentId();
        }
        return result;
    }

    /**
     * 初始化菜单的通用属性。
     * <p>
     * 例如说，只有目录或者菜单类型的菜单，才设置 icon
     *
     * @param menu 菜单
     */
    private void initMenuProperty(MenuDO menu) {
        // 菜单为按钮类型时，无需 component、icon、path 属性，进行置空
        if (MenuTypeEnum.BUTTON.getType().equals(menu.getType())) {
            menu.setComponent("");
            menu.setIcon("");
        }
        if (MenuTypeEnum.DIR.equals(menu.getType())) {
            menu.setShortcut(false);
            menu.setShortcutIcon("");
        }
    }

    /**
     * 校验父菜单是否合法
     * <p>
     * 1. 不能设置自己为父菜单
     * 2. 父菜单不存在
     * 3. 父菜单必须是 {@link MenuTypeEnum#MENU} 菜单类型
     *
     * @param parentId 父菜单编号
     * @param childId  当前菜单编号
     */
    private void checkParentResource(Long parentId, Long childId) {
        if (parentId == null || MenuIdEnum.ROOT.getId().equals(parentId)) {
            return;
        }
        // 不能设置自己为父菜单
        if (parentId.equals(childId)) {
            throw ServiceExceptionUtil.exception(MENU_PARENT_ERROR);
        }
        MenuDO menu = getMenu(parentId);
        // 父菜单不存在
        if (menu == null) {
            throw ServiceExceptionUtil.exception(MENU_PARENT_NOT_EXISTS);
        }
        // 父菜单必须是目录或者菜单类型
        if (!MenuTypeEnum.DIR.getType().equals(menu.getType())
                && !MenuTypeEnum.MENU.getType().equals(menu.getType())) {
            throw ServiceExceptionUtil.exception(MENU_PARENT_NOT_DIR_OR_MENU);
        }
    }

    /**
     * 校验菜单是否合法
     * <p>
     * 1. 校验相同应用，相同父菜单编号下，是否存在相同的菜单名
     *
     * @param name     菜单名字
     * @param parentId 父菜单编号
     * @param id       菜单编号
     * @param clientId 应用id
     */
    private void checkResource(Long parentId, String name, Long id, Long clientId) {
        MenuDO menu = baseMapper.selectByParentIdAndNameAndClient(parentId, name, clientId);
        if (menu == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的菜单
        if (id == null) {
            throw ServiceExceptionUtil.exception(MENU_NAME_DUPLICATE);
        }
        if (!menu.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(MENU_NAME_DUPLICATE);
        }
    }

}
