package com.unicom.swdx.module.system.dal.mysql.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleExportReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RolePageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleUsersRespVO;
import com.unicom.swdx.module.system.convert.permission.RoleConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.lang.Nullable;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Mapper
public interface RoleMapper extends BaseMapperX<RoleDO> {

    default PageResult<RoleDO> selectPage(RolePageReqVO reqVO, Collection<Long> tenantIds, Long tenantId, Collection<Long> tenantRoles) {
        LambdaQueryWrapperX<RoleDO> query = getQuery(RoleConvert.INSTANCE.covertReqVO(reqVO), tenantIds, tenantId, tenantRoles);
        return selectPage(reqVO, query);
    }

    default List<RoleDO> selectList(RoleExportReqVO reqVO, Collection<Long> tenantIds, Long tenantId, Collection<Long> tenantRoles) {
        LambdaQueryWrapperX<RoleDO> query = getQuery(reqVO, tenantIds, tenantId, tenantRoles);
        return selectList(query);
    }

    // 列表查询封装
    default LambdaQueryWrapperX<RoleDO> getQuery(RoleExportReqVO reqVO, Collection<Long> tenantIds, Long tenantId, Collection<Long> tenantRoles) {
        LambdaQueryWrapperX<RoleDO> query = new LambdaQueryWrapperX<RoleDO>()
                .likeIfPresent(RoleDO::getName, reqVO.getName())
                .likeIfPresent(RoleDO::getCode, reqVO.getCode())
                .eqIfPresent(RoleDO::getClientId, reqVO.getClientId())
                .eqIfPresent(RoleDO::getType, reqVO.getType())
                .eqIfPresent(RoleDO::getStatus, reqVO.getStatus())
                .orderByAsc(RoleDO::getSort)
                .orderByDesc(RoleDO::getId);
        if (Objects.nonNull(tenantId)) {
            // 除了当前机构自定义的角色，还包括机构的角色
            query.and(q -> q.eq(RoleDO::getTenantId,tenantId).or().in(RoleDO::getId,tenantRoles));
        }
        if (StrUtil.isNotBlank(reqVO.getTenantName())) {
            // 防止报错，插入一条不存在数据去查
            ArrayList<Long> list = CollUtil.newArrayList(-1L);
            list.addAll(tenantIds);
            query.in(RoleDO::getTenantId,list);
        }
        return query;
    }

    default RoleDO selectByNameAndApplication(String name,Long clientId) {
        return selectOne(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getName,name)
                .eq(RoleDO::getClientId,clientId)
                .last("limit 1")
        );
    }

    default RoleDO selectByCodeAndApplicationAndTenant(String code, Long clientId, Long tenantId) {
        return selectOne(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getCode,code)
                .eq(RoleDO::getClientId,clientId)
                .eqIfPresent(RoleDO::getTenantId,tenantId)
                .isNull(Objects.isNull(tenantId),RoleDO::getTenantId)
                .last("limit 1")
        );
    }

    default List<RoleDO> selectListByStatus(@Nullable Collection<Integer> status) {
        return selectList(RoleDO::getStatus, status);
    }

    @Select("SELECT COUNT(*) FROM system_role WHERE update_time > #{maxUpdateTime}")
    Long selectCountByUpdateTimeGt(LocalDateTime maxUpdateTime);

    default List<RoleDO> selectListEnabled(Integer type) {
        return selectList(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .eqIfPresent(RoleDO::getType,type)
        );
    }

    default List<RoleDO> selectListByClientId(Long clientId) {
        return selectList(RoleDO::getClientId,clientId);
    }

    default RoleDO selectByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getCode,code)
                .last("limit 1")
        );
    }

    default RoleDO selectByNameAndApplicationAndTenantId(String name, Long clientId, Long tenantId) {
        return selectOne(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getName,name)
                .eq(RoleDO::getClientId,clientId)
                .eqIfPresent(RoleDO::getTenantId,tenantId)
                .isNull(Objects.isNull(tenantId),RoleDO::getTenantId)
                .last("limit 1")
        );
    }

    default void deleteByTenantId(Long tenantId) {
        delete(new LambdaQueryWrapperX<RoleDO>()
                .eq(RoleDO::getTenantId,tenantId));
    }

    RoleUsersRespVO selectBaseRole(@Param("id") Long roleId);
    @Select("select USER_ID from SYSTEM_USER_ROLE where DELETED = 0 and ROLE_ID = #{id}")
    List<Long> selectAllUserIdByRole(@Param("id") Long roleId);


    List<Long> selectAllRoleByUserId(@Param("uid") Long uid);


}
