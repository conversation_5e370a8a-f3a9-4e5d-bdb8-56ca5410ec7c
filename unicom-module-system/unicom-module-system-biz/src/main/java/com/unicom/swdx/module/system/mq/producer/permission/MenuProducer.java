package com.unicom.swdx.module.system.mq.producer.permission;

import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import com.unicom.swdx.module.system.mq.producer.RefreshMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Menu 菜单相关消息的 Producer
 */
@Component
@Slf4j
public class MenuProducer extends AbstractProducer{

    @Async
    public void sendRefreshMessage() {
        log.info("[send][ Menu 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "MenuRefresh");
    }

}
