package com.unicom.swdx.module.system.controller.admin.message;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageRespVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpstreamExportReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpstreamExportVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpstreamPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpstreamPageVO;
import com.unicom.swdx.module.system.service.message.MessageUpstreamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Api(tags = "管理后台 - 收件箱")
@RestController
@RequestMapping("/system/messageUpstream")
@Validated
public class MessageUpstreamController {

    @Resource
    private MessageUpstreamService messageUpstreamService;


    @GetMapping("/uplinkMessage")
    @PermitAll
    public CommonResult<Boolean> uplinkMessage(@RequestParam String userMoblie, @RequestParam String content,
                                               @RequestParam(required = false) String cpMoblie
    ) {
        messageUpstreamService.uplinkMessage(userMoblie,content,cpMoblie);
        return CommonResult.success(true);
    }
    @GetMapping("/forwardMessage")
    public CommonResult<String> forwardMessage(@RequestParam String userMoblie, @RequestParam String content,
                                               @RequestParam(required = false) String cpMoblie
    ) {
        String result = messageUpstreamService.forwardMessage(userMoblie,content,cpMoblie);
        return CommonResult.success(result);
    }



    @GetMapping("/page")
    @ApiOperation("上行短信分页")
    public CommonResult<PageResult<MessageUpstreamPageVO>> getMessageUpstreamPage(@Valid MessageUpstreamPageReqVO pageVO) {
        PageResult<MessageUpstreamPageVO> pageResult = messageUpstreamService.getMessageUpstreamPage(pageVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/export")
    @ApiOperation("导出上行短信")
//    @OperateLog(type = EXPORT)
    public void export(HttpServletResponse response, @Validated MessageUpstreamExportReqVO reqVO) throws IOException {
        List<MessageUpstreamExportVO> messageUpstreamExportVOS = messageUpstreamService.getUpstreamExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "上行短信.xls", "上行短信列表", MessageUpstreamExportVO.class, messageUpstreamExportVOS);
    }
}
