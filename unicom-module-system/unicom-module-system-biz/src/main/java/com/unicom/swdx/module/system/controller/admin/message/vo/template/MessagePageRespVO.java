package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 机构 Response VO")
@Data
@ToString(callSuper = true)
public class MessagePageRespVO {

    @ApiModelProperty(value = "模板id", required = true, example = "1")
    private Long id;

    @ApiModelProperty(value = "系统id", required = true, example = "1024")
    private Long systemId;

    @ApiModelProperty(value = "模板名称", example = "测试体育俱乐部")
    private String name;

    @ApiModelProperty(value = "模板内容",example = "张三")
    private String content;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "备注",example = "张三")
    private String remark;
}
