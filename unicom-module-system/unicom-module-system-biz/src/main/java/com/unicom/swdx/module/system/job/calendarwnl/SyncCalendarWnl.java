package com.unicom.swdx.module.system.job.calendarwnl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.module.system.dal.dataobject.calendarwnl.CalendarWnlDO;
import com.unicom.swdx.module.system.dal.enums.HolidayTypeEnum;
import com.unicom.swdx.module.system.dal.mysql.calendarwnl.CalendarWnlMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:33
 **/
@Component
@Slf4j
public class SyncCalendarWnl {

    private final String URL = "https://www.mxnzp.com/api/holiday/list/year/{date}?ignoreHoliday=false&app_id=fjshljh1mjcmsujg&app_secret=Zkl2ajdDMmR0YVVoU21tOVlqSlA4QT09";

    @Resource
    private CalendarWnlMapper calendarWnlMapper;

    @Async
    public void sync() {
        int year = LocalDate.now().getYear() + 1;
        String remoteUrl = StrUtil.replace(URL,"{date}", String.valueOf(year));
        // 请求远程接口
        String resStr = HttpUtil.get(remoteUrl);
        JSONObject json = JSONUtil.parseObj(resStr);
        Integer code = json.get("code",Integer.class);
        if (code != 1) {
            log.error("同步万年历接口调用报错，resp：{}",resStr);
            return;
        }
        List<CalendarRemoteDTO> data = json.getBeanList("data", CalendarRemoteDTO.class);
        for (CalendarRemoteDTO month : data) {
            List<CalendarRemoteDTO.CalendarDay> days = month.getDays();
            for (CalendarRemoteDTO.CalendarDay day : days) {
                CalendarWnlDO build = CalendarWnlDO.builder()
                        .gregorianDate(DateUtils.parseLocalDate(day.getDate()))
                        .holidayType(day.getType())
                        .holidayName(day.getTypeDes())
                        .build();
                // 修正调休的日期。当日期为周末，但是工作日，则为调休
                if (day.getWeekDay() == 6 || day.getWeekDay() == 7) {
                    if (day.getType() == 0) {
                        build.setHolidayType(HolidayTypeEnum.LIEU.getCode());
                        build.setHolidayName("调休");
                    }
                }
                calendarWnlMapper.updateByDate(build);
            }
        }
    }

}
