package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@ApiModel("管理后台 - 消息模板新增 Request VO")
@Data
public class MessageAuthorityCreateReqVO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 数据范围ID列表，以逗号分隔
     */
    private String dataScopeIds;

    /**
     * 数据范围名字，以逗号分隔
     */
    @NotNull(message = "数据范围名字")
    private String dataScopeNames;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户手机号
     */
    private String phone;

//    /**
//     * 租户ID
//     */
//    private Long tenantId;
//
//    /**
//     * 创建时间
//     */
//    private LocalDateTime createTime;
//
//    /**
//     * 最后更新时间
//     */
//    private LocalDateTime updateTime;
//
//    /**
//     * 创建者
//     */
//    private String creator;
//
//    /**
//     * 更新者
//     */
//    private String updater;
//
//    /**
//     * 是否删除
//     */
//    private Boolean deleted;




}
