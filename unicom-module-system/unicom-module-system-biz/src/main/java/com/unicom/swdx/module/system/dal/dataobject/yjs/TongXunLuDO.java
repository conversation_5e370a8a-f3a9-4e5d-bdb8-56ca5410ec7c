package com.unicom.swdx.module.system.dal.dataobject.yjs;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组织表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("tongxunlu_user")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TongXunLuDO {


    private String used;
    private String name;
    private String deptname;
    private String mobile;
    private String acount;
    private String adduser;



}
