package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ApiModel("首页 - 日程 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ScheduleRespVO extends ScheduleBaseVO {

    @ApiModelProperty(value = "主键", required = true)
    private Long id;

}
