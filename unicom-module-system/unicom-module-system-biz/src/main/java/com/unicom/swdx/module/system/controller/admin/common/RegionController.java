package com.unicom.swdx.module.system.controller.admin.common;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.common.vo.region.RegionSimpleRespVO;
import com.unicom.swdx.module.system.convert.region.RegionConvert;
import com.unicom.swdx.module.system.dal.dataobject.region.RegionDO;
import com.unicom.swdx.module.system.service.region.RegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 地区")
@RestController
@RequestMapping("/system/region")
@Validated
public class RegionController {

    @Resource
    private RegionService regionService;

    @GetMapping("/list-simple")
    @ApiOperation("获得地区列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "父地区id", example = "0", defaultValue = "10000000",dataTypeClass = Long.class),
            @ApiImplicitParam(name = "level", value = "地区等级", example = "0", defaultValue = "3",dataTypeClass = Integer.class)
    })
    @PermitAll
    public CommonResult<List<RegionSimpleRespVO>> getSimpleRegions(@RequestParam(value = "id",required = false,defaultValue = "10000000") Long id
                                                                  , @RequestParam(value = "level",required = false,defaultValue = "3") Integer level) {
        List<RegionDO> list = regionService.getRegionListByParentId(id,level);
        return success(RegionConvert.INSTANCE.convertList(list));
    }

}
