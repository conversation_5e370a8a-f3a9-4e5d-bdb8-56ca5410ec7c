package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.unicom.swdx.module.system.enums.home.SubSystemEnum;
import com.unicom.swdx.module.system.enums.home.TodoType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/6/15 16:17
 **/
@Data
public class TodoUpdateSimpleReqVO {

    /**
     * {@link SubSystemEnum}
     */
    @ApiModelProperty(value = "发起子系统id（见枚举SubSystemEnum）", required = true)
    @NotNull(message = "发起子系统id不能为空")
    private Integer subsystemId;
    /**
     * {@link TodoType}
     */
    @ApiModelProperty(value = "待办类型（见枚举TodoType）", required = true)
    @NotNull(message = "待办类型不能为空")
    private Integer type;

    @ApiModelProperty("事项")
    @NotNull(message = "待办事项不能为空")
    private Integer item;

    @ApiModelProperty(value = "待办事项id",notes = "针对事项的唯一id")
    @NotNull(message = "待办事项id不能为空")
    private String todoId;

}
