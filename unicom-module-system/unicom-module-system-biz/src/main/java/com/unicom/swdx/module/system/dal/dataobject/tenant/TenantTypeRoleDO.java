package com.unicom.swdx.module.system.dal.dataobject.tenant;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 机构用户类型 DO
 *
 * <AUTHOR>
 */
@TableName("system_tenant_type_role")
@KeySequence("system_tenant_type_role_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantTypeRoleDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 机构用户类型id
     */
    private Long tenantTypeId;
    /**
     * 角色id
     */
    private Long roleId;

}
