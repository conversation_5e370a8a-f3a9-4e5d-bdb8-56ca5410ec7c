package com.unicom.swdx.module.system.service.permission;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.MenuCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.MenuListReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.MenuUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 菜单 Service 接口
 *
 * <AUTHOR>
 */
public interface MenuService extends IService<MenuDO> {

    /**
     * 初始化菜单的本地缓存
     */
    void initLocalCache();


    void deletecache();

    /**
     * 创建菜单
     *
     * @param reqVO 菜单信息
     * @return 创建出来的菜单编号
     */
    Long createMenu(MenuCreateReqVO reqVO);

    /**
     * 更新菜单
     *
     * @param reqVO 菜单信息
     */
    void updateMenu(MenuUpdateReqVO reqVO);

    /**
     * 删除菜单
     *
     * @param id 菜单编号
     */
    void deleteMenu(Long id);

    /**
     * 筛选菜单列表
     *
     * @param reqVO 筛选条件请求 VO
     * @return 菜单列表
     */
    List<MenuDO> getMenus(MenuListReqVO reqVO);

    /**
     * 获得菜单
     *
     * @param id 菜单编号
     * @return 菜单
     */
    MenuDO getMenu(Long id);

    /**
     * 获取角色拥有的所有菜单权限
     * @param roleId 角色id
     * @return 菜单列表
     */
    List<MenuDO> getClientMenusByRoleId(Long roleId);




//    /**
//     * 根据应用id获取菜单列表
//     * @param clientId 应用id
//     * @return 菜单集合
//     */
//    List<MenuDO> getClientMenus(Long clientId);


//    /**
//     * 基于租户，筛选菜单列表
//     * 注意，如果是系统租户，返回的还是全菜单
//     *
//     * @param reqVO 筛选条件请求 VO
//     * @return 菜单列表
//     */
////    List<MenuDO> getTenantMenus(MenuListReqVO reqVO);



    /**
     * 获得所有菜单，从缓存中
     * 任一参数为空时，则返回为空
     *
     * @param menuTypes 菜单类型数组
     * @param menusStatuses 菜单状态数组
     * @return 菜单列表
     */
    List<MenuDO> getMenuListFromCache(Collection<Integer> menuTypes, Collection<Integer> menusStatuses);

    /**
     * 获得指定编号的菜单数组，从缓存中
     *
     * 任一参数为空时，则返回为空
     *
     * @param menuIds 菜单编号数组
     * @param menuTypes 菜单类型数组
     * @param menusStatuses 菜单状态数组
     * @return 菜单数组
     */
    List<MenuDO> getMenuListFromCache(Collection<Long> menuIds, Collection<Integer> menuTypes,
                                      Collection<Integer> menusStatuses);

    /**
     * 获得权限标识对应的菜单数组
     *
     * @param permission 权限标识
     * @return 数组
     */
    List<MenuDO> getMenuListByPermissionFromCache(String permission);

    /**
     * 补全菜单的父级菜单
     * @param menuIds 菜单
     * @return 菜单
     */
    Set<Long> completeMenuIdParents(List<Long> menuIds);


    /**
     * 获取所有的快捷入口
     *
     * @return 菜单
     */
    List<MenuDO> getAllShortcutMenus();

    /**
     * 获取菜单的快捷入口路径
     * @param id 菜单jid
     * @return 快捷入口路径
     */
    String getShortcutPath(Long id);
}
