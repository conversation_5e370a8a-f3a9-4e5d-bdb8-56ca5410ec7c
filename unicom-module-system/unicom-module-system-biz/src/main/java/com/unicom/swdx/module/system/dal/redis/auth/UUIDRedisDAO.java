package com.unicom.swdx.module.system.dal.redis.auth;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.module.system.dal.redis.RedisKeyConstants.UUID_USERID;

/**
 * <AUTHOR>
 * @date 2023/3/7 9:47
 **/
@Repository
public class UUIDRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public  String get(String uuid) {
        String redisKey = formatKey(uuid);
        return stringRedisTemplate.opsForValue().get(redisKey);
    }

    public  void set(String uuid, Long userId) {
        String redisKey = formatKey(uuid);
        stringRedisTemplate.opsForValue().set(redisKey, userId.toString(), 600, TimeUnit.SECONDS);
    }

    private static String formatKey(String mobile) {
        return String.format(UUID_USERID.getKeyTemplate(), mobile);
    }

}
