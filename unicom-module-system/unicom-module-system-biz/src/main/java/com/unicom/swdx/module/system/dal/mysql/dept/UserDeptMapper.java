package com.unicom.swdx.module.system.dal.mysql.dept;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.user.dto.HrDeptDTO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserPostDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Mapper
public interface UserDeptMapper extends BaseMapperX<UserDeptDO> {


    //缓存30分钟用户部门信息
     Cache<Long, List<Long>> resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(128*50)   // 设定最大容量
                    .expireAfterWrite(30L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();



    default List<Long> getDeptList(@Param("userId") Long userId){

        List<Long>  deptlist = null;

        try {
            deptlist = resultCache.get(userId, () -> {

                return getDeptListsql(userId);
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return deptlist;
    }


    @Select("select distinct dept_id from system_user_dept where user_id = #{userId} and deleted !=true")
    List<Long> getDeptListsql(@Param("userId") Long userId);



    List<HrDeptDTO> getDeptDetailList(@Param("userId") Long userId);

    List<Long> getDeptListByUserIds(@Param("ids") List<Long> ids);



    List<UserDeptDO> getDeptMapByUserIds(@Param("ids") List<Long> ids);


    List<Long> selectUserIdListByDeptIds(@Param("deptIds") Set<Long> deptIds);
}
