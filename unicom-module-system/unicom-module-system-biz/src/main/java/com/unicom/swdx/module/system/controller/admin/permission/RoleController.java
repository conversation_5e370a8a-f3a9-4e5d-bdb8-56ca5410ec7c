package com.unicom.swdx.module.system.controller.admin.permission;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.*;
import com.unicom.swdx.module.system.convert.permission.RoleConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.service.permission.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "管理后台 - 角色")
@RestController
@RequestMapping("/system/role")
@Validated
public class RoleController {

    @Resource
    private RoleService roleService;

    @PostMapping("/create")
    @ApiOperation("创建角色")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<Long> createRole(@Valid @RequestBody RoleCreateReqVO reqVO) {
        return success(roleService.createRole(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation("修改角色")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:role:update')")
    public CommonResult<Boolean> updateRole(@Valid @RequestBody RoleUpdateReqVO reqVO) {
        roleService.updateRole(reqVO);
        return success(true);
    }

    @PostMapping("/update-status")
    @ApiOperation("修改角色状态")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:role:update')")
    public CommonResult<Boolean> updateRoleStatus(@Valid @RequestBody RoleUpdateStatusReqVO reqVO) {
        roleService.updateRoleStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除角色")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "角色编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:role:delete')")
    public CommonResult<Boolean> deleteRole(@RequestParam("id") Long id) {
        roleService.deleteRole(id);
        return success(true);
    }

    @GetMapping("/page")
    @ApiOperation("获得角色分页")
    public CommonResult<PageResult<RoleRespVO>> getRolePage(RolePageReqVO reqVO) {
        return success(roleService.getRolePage(reqVO));
    }


    /**
     * 角色信息菜单需要根据用户能看到的菜单来 admin看全部
     *
     * @param id
     * @return
     */
    @GetMapping("/get")
    @ApiOperation("获得角色信息")
    public CommonResult<RoleRespVO> getRole(@RequestParam("id") Long id) {
        return success(roleService.getRole(id));
    }

    @GetMapping("/  get-role-users")
    @ApiOperation("获得角色分配用户信息")
    public CommonResult<RoleUsersRespVO> getRoleUsers(RoleUsersPageReqVO pageReqVO) {
        return success(roleService.getRoleUsers(pageReqVO));
    }

    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获取角色精简信息列表", notes = "只包含被开启的角色，展示用户所属机构的所有角色")
    public CommonResult<List<RoleClientSimpleRespVO>> getSimpleRoles(@RequestParam("id") Long userId) {
        return success(roleService.getSimpleRoleList(userId));
    }

    @GetMapping("/list-client-simple")
    @ApiOperation(value = "获取应用-角色精简信息列表", notes = "只包含被开启的角色，可根据角色类型获取，主要用于前端的下拉选项")
    public CommonResult<List<RoleClientSimpleRespVO>> getClientSimpleRoles(@RequestParam(value = "type",required = false) Integer type) {
        return success(roleService.getClientSimpleRoles(type));
    }

    @GetMapping("/export")
    @ApiOperation("导出角色信息")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('system:role:export')")
    public void export(HttpServletResponse response, @Validated RoleExportReqVO reqVO) throws IOException {
        List<RoleDO> list = roleService.getRoleList(reqVO);
        List<RoleExcelVO> data = RoleConvert.INSTANCE.convertList03(list);
        // 输出
        ExcelUtils.write(response, "角色信息.xls", "角色列表", RoleExcelVO.class, data);
    }

}
