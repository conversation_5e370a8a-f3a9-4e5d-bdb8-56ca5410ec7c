package com.unicom.swdx.module.system.api.region;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.region.dto.RegionDTO;
import com.unicom.swdx.module.system.service.region.RegionService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

/**
 * <AUTHOR>
 * @date 2023/4/12 14:32
 **/
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class RegionApiImpl implements RegionApi{

    @Resource
    private RegionService regionService;

    @Override
    public CommonResult<List<RegionDTO>> getList() {
        return success(regionService.getAllRegion());
    }

}
