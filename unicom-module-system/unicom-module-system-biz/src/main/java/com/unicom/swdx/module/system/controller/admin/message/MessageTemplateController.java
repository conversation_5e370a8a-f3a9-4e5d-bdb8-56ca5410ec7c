package com.unicom.swdx.module.system.controller.admin.message;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.*;
import com.unicom.swdx.module.system.convert.message.MessageConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import com.unicom.swdx.module.system.service.message.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "管理后台 - 消息模板")
@RestController
@RequestMapping("/system/messageTemplate")
public class MessageTemplateController {


    @Resource
    private MessageService messageService;

    @PostMapping("/create")
    @ApiOperation("创建消息模板")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:messageTemplate:create')")
    public CommonResult<Long> createMessage(@Valid @RequestBody MessageCreateReqVO createReqVO) {
        return success(messageService.createMessage(createReqVO));
    }
    @GetMapping("/page")
    @ApiOperation("获得消息模板分页")
    public CommonResult<PageResult<MessagePageRespVO>> getMessagePage(@Valid MessagePageReqVO pageVO) {
        PageResult<MessageDO> pageResult = messageService.getMessagePage(pageVO);
        return success(MessageConvert.INSTANCE.convertPage(pageResult));
    }
    @GetMapping("/get")
    @ApiOperation("获得消息模板")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:messageTemplate:get')")
    public CommonResult<MessageRespGetVO> getMessage(@RequestParam("id") Long id) {
        MessageDO message = messageService.getMessage(id);
        return success(MessageConvert.INSTANCE.convert01(message));
    }
    @PostMapping("/update")
    @ApiOperation("更新消息模板")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:messageTemplate:update')")
    public CommonResult<Boolean> updateMessage(@Valid @RequestBody MessageUpdateReqVO updateReqVO) {
        messageService.updateMessage(updateReqVO);
        return success(true);
    }
    @PostMapping("/delete")
    @ApiOperation("删除消息模板")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:messageTemplate:delete')")
    public CommonResult<Boolean> deleteMessage(@RequestParam("id") Long id) {
        messageService.deleteMessage(id);
        return success(true);
    }

}
