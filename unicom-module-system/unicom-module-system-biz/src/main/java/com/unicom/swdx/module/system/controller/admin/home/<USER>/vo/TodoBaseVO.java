package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
* 待办事项 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TodoBaseVO {

    @ApiModelProperty(value = "类型（见枚举）", required = true)
    @NotNull(message = "类型（见枚举）不能为空")
    private Integer type;

    @ApiModelProperty(value = "缓急性", required = true)
    @NotBlank(message = "缓急性不能为空")
    private String urgencyLevel;

    @ApiModelProperty(value = "标题", required = true)
    @NotNull(message = "标题不能为空")
    private String title;

    @ApiModelProperty(value = "发起人", required = true)
    @NotNull(message = "发起人不能为空")
    private String submitter;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起时间", required = true)
    @NotNull(message = "发起时间不能为空")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "发起子系统id", required = true)
    @NotNull(message = "发起子系统id不能为空")
    private Integer subsystemId;

    @ApiModelProperty(value = "跳转链接", required = true)
    @NotNull(message = "跳转链接不能为空")
    private String linkUrl;

    @ApiModelProperty(value = "待办人", required = true)
    @NotNull(message = "待办人不能为空")
    private String todoUserId;

    @ApiModelProperty(value = "状态（0=待办，1=已办）", required = true)
    @NotNull(message = "状态（0=待办，1=已办）不能为空")
    private Integer status;

}
