package com.unicom.swdx.module.system.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.enums.UserTypeEnum;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthForgetPasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthUpdatePasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdateMobileReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdatePasswordRespVO;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.social.SocialUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.dept.PostService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.social.SocialUserService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.infra.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Api(tags = "管理后台 - 用户个人中心")
@RestController
@RequestMapping("/system/user/profile")
@Validated
@Slf4j
public class UserProfileController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private SocialUserService socialService;

    @GetMapping("/get")
    @ApiOperation("获得登录用户信息")
    public CommonResult<UserProfileRespVO> profile() {
        // 获得用户基本信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        UserProfileRespVO resp = UserConvert.INSTANCE.convert03(user);
        // 获得用户角色
        List<RoleDO> userRoles = roleService.getRolesByIds(permissionService.getUserRoleIdListByUserId(user.getId()));
        resp.setRoles(UserConvert.INSTANCE.convertList(userRoles));
        // 获得部门信息
        if (user.getDeptId() != null) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            resp.setDept(UserConvert.INSTANCE.convert02(dept));
        }
        // 获得岗位信息
        if (CollUtil.isNotEmpty(user.getPostIds())) {
            List<PostDO> posts = postService.getPosts(user.getPostIds());
            resp.setPosts(UserConvert.INSTANCE.convertList02(posts));
        }
        // 获得社交用户信息
        List<SocialUserDO> socialUsers = socialService.getSocialUserList(user.getId(), UserTypeEnum.ADMIN.getValue());
        resp.setSocialUsers(UserConvert.INSTANCE.convertList03(socialUsers));
        return success(resp);
    }

    @PostMapping("/update")
    @ApiOperation("修改用户个人信息")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateUserProfile(@Valid @RequestBody UserProfileUpdateReqVO reqVO) {
        userService.updateUserProfile(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/update-password")
    @ApiOperation("修改用户个人密码")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateUserProfilePassword(@Valid @RequestBody UserProfileUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/update-app-password")
    @ApiOperation("修改用户个人密码app")
    @PermitAll
    public CommonResult<Boolean> updateAppUserProfilePassword(@Valid @RequestBody AuthUpdatePasswordReqVO reqVO) {
        userService.updateAuthUserPassword(reqVO);
        return success(true);
    }

    @GetMapping("/userid")
    @PermitAll
    @ApiOperation("获取用户id")
    public CommonResult<UserUpdatePasswordRespVO> messageVerification(UserProfileVerificationMobileReqVO reqVO) {
        AuthForgetPasswordReqVO authForgetPasswordReqVO = new AuthForgetPasswordReqVO();
        authForgetPasswordReqVO.setUsername(userService.getUser(getLoginUserId()).getUsername());
        authForgetPasswordReqVO.setMobile(reqVO.getMobile());
        authForgetPasswordReqVO.setVerification(reqVO.getVerification());
        return success(userService.getUserIdResp(authForgetPasswordReqVO));
    }

    @PostMapping ("/verify-password")
    @ApiOperation("校验用户密码")
    public CommonResult<Boolean> verifyPassword(@Valid @RequestBody UserProfileVerificationPasswordReqVO reqVO) {
        userService.verifyPassword(reqVO);
        return success(true);
    }

    @PostMapping("/update-app-mobile")
    @ApiOperation("修改用户个人手机号app")
    public CommonResult<String> updateAppUserProfileMobile(@Valid @RequestBody UserProfileUpdateMobileReqVO reqVO) {
        return success(userService.updateAppUserMobile(reqVO));
    }

    @PostMapping("/update-mobile")
    @ApiOperation("修改用户个人手机号")
    public CommonResult<String> updateUserProfileMobile(@Valid @RequestBody UserUpdateMobileReqVO reqVo) {
        return success(userService.updateUserMobile(reqVo));
    }
    @PostMapping("/update-avatar")
    @ApiOperation("上传用户个人头像")
    public CommonResult<String> updateUserAvatar(@RequestParam("avatarFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw ServiceExceptionUtil.exception(FILE_IS_EMPTY);
        }
        String avatar = userService.updateUserAvatar(getLoginUserId(), file.getInputStream());
        return success(avatar);
    }


    @PostMapping("/update-avatar-app")
    @ApiOperation("上传用户个人头像app")
    public CommonResult<String> updateUserAvatarApp(@RequestBody UserProfileReqDTO userProfileReqDTO) {
        String avatar = userService.updateUserAvatarApp(getLoginUserId(), userProfileReqDTO.getImageBase64(), userProfileReqDTO.getFileType());
        return success(avatar);
    }

    @GetMapping("/password-expired-time")
    @ApiOperation("查询用户密码过期时间")
    public CommonResult<UserPasswordUpdateTimeRespVO> getPasswordUpdateTime() {
        return success(userService.getPasswordUpdateTime());
    }

}
