package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 租户更新 Request VO")
@Data
@ToString(callSuper = true)
public class TenantStatusUpdateReqVO {

    @ApiModelProperty(value = "机构id",required = true,example = "205")
    @NotNull(message = "机构id不能为空")
    private Long id;

    @ApiModelProperty(value = "机构状态",required = true,example = "1")
    @NotNull(message = "机构状态不能为空")
    private Integer status;


}
