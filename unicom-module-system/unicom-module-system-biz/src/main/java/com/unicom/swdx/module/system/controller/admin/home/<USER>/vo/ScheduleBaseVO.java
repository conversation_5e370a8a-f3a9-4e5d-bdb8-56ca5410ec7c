package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.annotations.*;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;

/**
* 日程 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ScheduleBaseVO {

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", required = true)
    @NotNull(message = "日程日期不能为空")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate scheduleDate;
    /**
     * 日程内容
     */
    @ApiModelProperty(value = "日程内容", required = true, notes = "输入限制50字符")
    @NotBlank(message = "日程内容不能为空")
    @Length(min = 0, max = 200, message = "日程内容不能超过200字符")
    private String content;

}
