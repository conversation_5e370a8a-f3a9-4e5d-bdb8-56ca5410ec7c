package com.unicom.swdx.module.system.dal.dataobject.dept;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("old_dept")
@KeySequence("old_dept_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OldDeptDO {

    private Long id;

    private String oldDeptId;

    private String oldDeptName;

    private String parentDeptName;

    private Long deptId;

    private String  parentDeptId;

}
