package com.unicom.swdx.module.system.controller.admin.oaNotice.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 通知公告 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class OaNoticeBaseNewVO {

    @ApiModelProperty(value = "公告标题", required = true, example = "小博主")
    @Size(max = 100, message = "公告标题不能超过100个字符")
    private String title;

    @ApiModelProperty(value = "公告类型，1为通知，2为公告", required = true, example = "2")
    @NotNull(message = "公告类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "公告内容", required = true, example = "半生编码")
    private String content;

    @ApiModelProperty(value = "公告状态，1为已发布，2为草稿", required = true, example = "1", notes = "参见 CommonStatusEnum 枚举类")
    @NotNull(message = "公告状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "创建人姓名", example = "aaa")
    private String creator;

    @ApiModelProperty(value = "创建人id", required = true, example = "666")
    private Long creatorId;

    @ApiModelProperty(value = "创建时间", example = "2024-03-05 12:02:33")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "接收人部门id列表，以|号分隔", example = "|100|99|")
    private String recipientId;

    @ApiModelProperty(value = "接收人用户id列表，以|号分隔", example = "|100|99|")
    private String recipientUserId;

    @ApiModelProperty(value = "文件路径", example = "aaa.com")
    private String fileUrl;

    @ApiModelProperty(value = "文件权限,1仅查看，2下载与查看", example = "aaa")
    private Integer fileAuthority;

    @ApiModelProperty(value = "是否删除,0/1", example = "0")
    private Boolean deleted;

    @ApiModelProperty(value = "更新人id", example = "111")
    private String updater;

    @ApiModelProperty(value = "更新人id", example = "222")
    private Long updaterId;

    @ApiModelProperty(value = "更新时间", example = "2024-03-05 12:02:33")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否被下架了,0/1", example = "0")
    private Boolean removed;

    @ApiModelProperty(value = "阅读量", example = "666")
    private Long readNumber;

    @ApiModelProperty(value = "是否已读了,0/1", example = "0")
    private Boolean read;

    @ApiModelProperty(value = "置顶状态，0为不置顶，1为置顶", required = true, example = "0")
    @NotNull(message = "置顶状态不能为空")
    private Integer isTopNotice;

    @ApiModelProperty(value = "置顶时间", example = "2024-03-05 12:02:33")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime topTime;



}
