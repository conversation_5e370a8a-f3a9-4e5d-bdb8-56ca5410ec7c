package com.unicom.swdx.module.system.controller.admin.message;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.DesensitizedUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.*;
import com.unicom.swdx.module.system.service.message.MessageSendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import java.util.ArrayList;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.CREATE;

@Api(tags = "管理后台 - 消息发送")
@RestController
@RequestMapping("/system/messageSend")
public class MessageSendController {


    @Resource
    private MessageSendService messageSendService;

    @Resource
    private DeptApi deptApi;

    @PostMapping("/create")
    @ApiOperation("创建消息发送")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:messageSend:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    public CommonResult<Long> createMessageSend(@Valid @RequestBody MessageSendCreateReqVO createReqVO) {
        return success(messageSendService.createMessageSend(createReqVO));
    }
    @GetMapping("/page")
    @ApiOperation("获得消息发送分页")
    public CommonResult<PageResult<MessageSendPageRespVO>> getMessageSendPage(@Valid MessageSendPageReqVO pageVO) {
        PageResult<MessageSendPageRespVO> pageResult = messageSendService.getMessagePage(pageVO);
        if(pageResult!=null&&pageResult.getList()!=null){
            pageResult.getList().forEach(it->{
                List<String> mobiles =it.getReceivingMobile();

                if(CollectionUtil.isNotEmpty(mobiles)){
                    List<String> temp = new ArrayList<>();
                    for (String mobile : mobiles) {
                        if( Validator.isMobile(mobile)){
                            temp.add(DesensitizedUtil.mobilePhone(mobile));
                        }
                    }

                    it.setReceivingMobile(temp);
                }

            });
        }

        return success(pageResult);
    }
    @GetMapping("/get")
    @ApiOperation("获得消息发送详情")
    @PreAuthorize("@ss.hasPermission('system:MessageSend:get')")
    public CommonResult<List<MessageSendGetVO>> getMessageSend(@Valid MessageSendGetReqVO req) {
        List<MessageSendGetVO> listResult = messageSendService.getMessage(req);
        return success(listResult);
    }


//    @GetMapping("/get")
//    @ApiOperation("获得消息发送详情")
//    @PreAuthorize("@ss.hasPermission('system:MessageSend:get')")
//    public CommonResult<PageResult<MessageSendRespGetVO>> getMessageSend(@Valid MessageSendGetReqVO req) {
//        PageResult<MessageSendRespGetVO> pageResult = messageSendService.getMessage(req);
//        return success(pageResult);
//    }

    @PostMapping("/cancelQue")
    @ApiOperation(value = "取消短信发送")
    public CommonResult<String> cancelQue(@RequestParam("title") List<String> title) {
        return deptApi.sendMessageRefreshList(title);
    }

    @PostMapping("/cancelById")
    @ApiOperation(value = "取消短信发送")
    public CommonResult<String> cancelById(@RequestParam("title") List<Long> ids) {
        return deptApi.sendMessageRefreshListById(ids);
    }

}
