package com.unicom.swdx.module.system.dal.dataobject.payMessage;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "pay_message", autoResultMap = true)
@KeySequence("pay_message_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayMessageDO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String outTradeNo;

    /**
     * 消息内容
     */
    private String wxMessage;

    /**
     * 消息内容
     */
    private String oneCardMessage;

    /**
     * 收到微信回调返回消息成功
     */
    private Boolean receiveWxSuccess;

    /**
     * 收到一卡通回调返回消息成功
     */
    private Boolean receiveOneCardSuccess;

    /**
     * 插入时间
     */
    private LocalDateTime createTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;


    private String notifyurl;

}
