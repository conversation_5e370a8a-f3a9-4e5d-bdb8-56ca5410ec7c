package com.unicom.swdx.module.system.dal.mysql.permission;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

@Mapper
public interface UserRoleGroupMapper extends BaseMapperX<RoleGroupUserDO> {
    Long getUsersByRoleGroupIds(@Param("id") Long id);

    Set<Long> selectRoleGroupId(@Param("userId") Long userId);
}
