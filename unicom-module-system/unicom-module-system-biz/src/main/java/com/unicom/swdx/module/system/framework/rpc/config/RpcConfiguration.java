package com.unicom.swdx.module.system.framework.rpc.config;

import com.unicom.swdx.module.edu.api.teacherinformation.TeacherinformationApi;
import com.unicom.swdx.module.edu.api.signupunit.SignUpUnitApi;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, ReceiveApi.class , PersonnalApi.class, SignUpUnitApi.class,TeacherinformationApi.class})
public class RpcConfiguration {
}
