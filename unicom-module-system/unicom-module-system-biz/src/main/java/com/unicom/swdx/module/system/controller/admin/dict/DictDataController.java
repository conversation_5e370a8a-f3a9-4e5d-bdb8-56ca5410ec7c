package com.unicom.swdx.module.system.controller.admin.dict;

import cn.hutool.core.util.ObjectUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.dict.vo.data.*;
import com.unicom.swdx.module.system.convert.dict.DictDataConvert;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictTypeDO;
import com.unicom.swdx.module.system.service.dict.DictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.DICT_TYPE_UNAUTH;

@Api(tags = "管理后台 - 字典数据")
@RestController
@RequestMapping("/system/dict-data")
@Validated
@RefreshScope
public class DictDataController {

    @Resource
    private DictDataService dictDataService;

    @Value("#{'${unicom.unAuthDictType}'.split(',')}")
    private Set<String> unAuthDictType;

    @PostMapping("/create")
    @ApiOperation("新增字典数据")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:dict:create')")
    public CommonResult<Long> createDictData(@Valid @RequestBody DictDataCreateReqVO reqVO) {
        Long dictDataId = dictDataService.createDictData(reqVO);
        return success(dictDataId);
    }

    @PostMapping("/update")
    @ApiOperation("修改字典数据")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:dict:update')")
    public CommonResult<Boolean> updateDictData(@Valid @RequestBody DictDataUpdateReqVO reqVO) {
        dictDataService.updateDictData(reqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除字典数据")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:dict:delete')")
    public CommonResult<Boolean> deleteDictData(Long id) {
        dictDataService.deleteDictData(id);
        return success(true);
    }

    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获得全部字典数据列表", notes = "一般用于管理后台缓存字典数据在本地")
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictDataSimpleRespVO>> getAllSimpleDictDatas() {
        List<DictDataDO> list = dictDataService.getDictDatas();
        return success(DictDataConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-child-simple")
    @ApiOperation(value = "获得子字典数据列表")
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictDataSimpleRespVO>> getChildSimpleDictDatas(@RequestParam("ids") List<Long> ids) {
        List<DictDataDO> list = dictDataService.getDictDatasByParentId(ids);
        return success(DictDataConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-simple")
    @ApiOperation(value = "根据字典类型获得字典数据列表")
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictDataSimpleRespVO>> getSimpleDictDatas(@Valid DictDataSimpleReqVO reqVO) {
        List<DictDataDO> list = dictDataService.getDictDatas(reqVO);
        return success(DictDataConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-simple-unauth")
    @ApiOperation(value = "根据字典类型获得字典数据列表(开放全部权限)")
    @PermitAll
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictDataSimpleRespVO>> getSimpleDictDatasUnauth(@Valid DictDataSimpleReqVO reqVO) {
        if (!unAuthDictType.contains(reqVO.getDictType())) {
            return error(DICT_TYPE_UNAUTH);
        }
        List<DictDataDO> list = dictDataService.getDictDatas(reqVO);
        return success(DictDataConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-simple-tree")
    @ApiOperation(value = "根据字典类型获得字典数据树状列表")
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictDataSimpleTreeRespVO>> getSimpleTreeDictDatas(@Valid DictDataSimpleReqVO reqVO) {
        List<DictDataDO> list = dictDataService.getDictDatas(reqVO);
        return success(DictDataConvert.INSTANCE.buildDictDataTree(list));
    }

    @GetMapping("/page")
    @ApiOperation("/获得字典类型的分页列表")
//    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<PageResult<DictDataRespVO>> getDictTypePage(@Valid DictDataPageReqVO reqVO) {
        List<DictDataRespVO> result = dictDataService.getDictDataList(reqVO);
        return success(new PageResult<>(result, (long) result.size()));
    }

    @GetMapping(value = "/get")
    @ApiOperation("/查询字典数据详细")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
//    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<DictDataRespVO> getDictData(@RequestParam("id") Long id) {
        return success(DictDataConvert.INSTANCE.convert(dictDataService.getDictData(id)));
    }

    @GetMapping("/export")
    @ApiOperation("导出字典数据")
    @PreAuthorize("@ss.hasPermission('system:dict:export')")
    @OperateLog(type = EXPORT)
    public void export(HttpServletResponse response, @Valid DictDataPageReqVO reqVO) throws IOException {
        List<DictDataExcelVO> data = dictDataService.getDictDataExcel(reqVO);
        // 输出
        ExcelUtils.write(response, "字典数据.xls", "数据列表", DictDataExcelVO.class, data);
    }


    @GetMapping("/needPwd")
    @ApiOperation("/获得是否需要密码登录")
    @PermitAll
//    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<Boolean> needPwd() {
        DictDataDO result = dictDataService.parseDictData("use_pwd" ,"use_pwd_type");
        if(ObjectUtil.isNotEmpty(result)&& "1".equals(result.getValue())){
            return  success(true);
        }
        return success(false);
    }



    @ApiOperation("字典同步接口data")
    @GetMapping("/getallData")
    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<List<DictDataDO>> getallData() {
        List<DictDataDO> list = dictDataService.getDictDatas();
        return success(list);
    }
}
