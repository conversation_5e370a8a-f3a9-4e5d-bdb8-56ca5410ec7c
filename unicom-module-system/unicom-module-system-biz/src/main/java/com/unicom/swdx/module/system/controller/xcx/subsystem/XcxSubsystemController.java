package com.unicom.swdx.module.system.controller.xcx.subsystem;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.controller.admin.subsystem.vo.ClientRespVO;
import com.unicom.swdx.module.system.service.subsystem.SubsystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Api(tags = "业务中台 - 小程序首页")
@RestController
@RequestMapping("/system/xcx/subsystem")
@Validated
public class XcxSubsystemController {

    @Resource
    private SubsystemService subsystemService;

    @GetMapping("/list")
    @ApiOperation("获得子系统列表")
    public CommonResult<List<List<ClientRespVO>>> getSubsystemList() {
        List<Integer> visibility = new ArrayList<>(Arrays.asList(0, 2));
        List<ClientRespVO> subsystemList = subsystemService.getSubsystemList(getLoginUserId(),visibility);
        subsystemList.removeIf(clientRespVO -> Objects.equals(clientRespVO.getCode(), "keyan") ||
                Objects.equals(clientRespVO.getCode(),"jiaowu") ||
                Objects.equals(clientRespVO.getCode(),"xueyuan") ||
                Objects.equals(clientRespVO.getCode(),"dangjian") ||
                Objects.equals(clientRespVO.getCode(),"chaiwu") ||
                Objects.equals(clientRespVO.getCode(),"postgraduate") ||
                Objects.equals(clientRespVO.getCode(),"clientsjzt") ||
                Objects.equals(clientRespVO.getCode(),"cv") ||
                Objects.equals(clientRespVO.getCode(),"wangluoketang") ||
                Objects.equals(clientRespVO.getCode(),"yuntaigou") ||
                Objects.equals(clientRespVO.getCode(),"cnki") ||
                Objects.equals(clientRespVO.getCode(),"databigscreen") ||
                Objects.equals(clientRespVO.getCode(),"other") ||
                Objects.equals(clientRespVO.getCode(),"system") ||
                Objects.equals(clientRespVO.getCode(),"personnel") ||
                Objects.equals(clientRespVO.getCode(),"tougao"));

        //每8个拆成一组
        return success(partitionList(subsystemList,8));
    }

    public static <T> List<List<T>> partitionList(List<T> list, int size) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            int end = Math.min(i + size, list.size());
            partitions.add(new ArrayList<>(list.subList(i, end)));
        }
        return partitions;
    }

}
