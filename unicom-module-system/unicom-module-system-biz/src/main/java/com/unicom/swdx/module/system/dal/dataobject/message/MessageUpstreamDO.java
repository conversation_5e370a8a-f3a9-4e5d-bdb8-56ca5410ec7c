package com.unicom.swdx.module.system.dal.dataobject.message;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 消息发送权限范围 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_message_upstream", autoResultMap = true)
@KeySequence("system_message_upstream_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MessageUpstreamDO implements Serializable {


    /**
     * 编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 内容
     */
    private String MessageContent;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 接收手机号
     */
    private String cpMoblie;


    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Boolean deleted;

//    /**
//     * 创建者，目前使用 SysUser 的 id 编号
//     *
//     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
//     */
//    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
//    private String creator;
//    /**
//     * 更新者，目前使用 SysUser 的 id 编号
//     *
//     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
//     */
//    @TableField(fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.VARCHAR)
//    private String updater;

//    /**
//     * 账号状态，0为启用1为禁用
//     */
//    private Integer status;



//    // Getter和Setter方法
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public String getDataScopeIds() {
//        return dataScopeIds;
//    }
//
//    public void setDataScopeIds(String dataScopeIds) {
//        this.dataScopeIds = dataScopeIds;
//    }
//
//    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }



}
