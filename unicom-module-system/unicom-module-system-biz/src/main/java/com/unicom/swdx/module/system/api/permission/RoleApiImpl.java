package com.unicom.swdx.module.system.api.permission;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class RoleApiImpl implements RoleApi {

    @Resource
    private RoleService roleService;
    @Resource
    private PermissionService permissionService;
    @Override
    public CommonResult<Boolean> validRoles(Collection<Long> ids) {
        roleService.validRoles(ids);
        return success(true);
    }

    @Override
    public CommonResult<Long> getRoleIdByName(String roleName) {
        return success(roleService.getRoleByName(roleName));
    }

    @Override
    public CommonResult<Long> getInnerRoleIdByCode(String roleCode) {
        return success(roleService.getInnerRoleIdByCode(roleCode));
    }

    @Override
    public CommonResult<Long> getCustomRoleIdByCode(String roleCode) {
        return success(roleService.getCustomRoleIdByCode(roleCode));
    }

    @Override
    public CommonResult<Boolean> createRole(Long userId,  Set<Long> roleIds) {
        permissionService.addUserRole(userId, roleIds);
        return success(true);
    }

}
