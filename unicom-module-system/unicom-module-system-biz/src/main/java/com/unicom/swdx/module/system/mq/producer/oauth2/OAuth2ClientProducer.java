package com.unicom.swdx.module.system.mq.producer.oauth2;

import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import com.unicom.swdx.module.system.mq.producer.RefreshMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * OAuth 2.0 客户端相关消息的 Producer
 */
@Component
@Slf4j
public class OAuth2ClientProducer extends AbstractProducer{

    @Async
    public void sendRefreshMessage() {
        log.info("[send][ OAuth2Client 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "ClientRefresh");
    }

}
