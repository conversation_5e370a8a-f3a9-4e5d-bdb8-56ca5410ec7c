package com.unicom.swdx.module.system.dal.redis.auth;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.module.system.enums.sms.SmsMessageEnum;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.module.system.dal.redis.RedisKeyConstants.VERIFICATION_MOBILE;


@Repository
public class VerificationRedisDAO {

    @Resource
    private  StringRedisTemplate stringRedisTemplate;

    // 失败次数过期时间（10分钟）
    public static final int FAIL_COUNT_EXPIRATION_TIME = 10 * 60; // 10分钟转换为秒


    public  String get(SmsMessageEnum verificationTypeEnum, String mobile) {
        String redisKey = formatKey(verificationTypeEnum,mobile);
        return stringRedisTemplate.opsForValue().get(redisKey);
    }

    public  void set(SmsMessageEnum verificationTypeEnum, String mobile, String verification) {
        String redisKey = formatKey(verificationTypeEnum,mobile);
        stringRedisTemplate.opsForValue().set(redisKey, verification, 600, TimeUnit.SECONDS);
    }
    private static String formatKey(SmsMessageEnum verificationTypeEnum,String mobile) {
        return String.format(VERIFICATION_MOBILE.getKeyTemplate(), verificationTypeEnum.getCode(), mobile);
    }

    // 获取失败次数
    public Integer getFailCount(String mobile) {
        String failCountKey = getFailCountKey(mobile);
        String failCountStr = stringRedisTemplate.opsForValue().get(failCountKey);
        return StrUtil.isBlank(failCountStr) ? 0 : Integer.parseInt(failCountStr);
    }

    // 获取失败次数的Redis键
    private String getFailCountKey(String mobile) {
        return String.format("fail_count_%s", mobile); // 根据需要调整键的格式
    }
    // VerificationRedisDAO类中的新方法
    public void incrementFailCount(String mobile) {
        String failCountKey = getFailCountKey(mobile);
        // 使用Redis的INCR命令原子地增加失败次数
        Long newFailCount = stringRedisTemplate.opsForValue().increment(failCountKey, 1);
        // 可选：设置新的过期时间（如果需要）
        stringRedisTemplate.expire(failCountKey, FAIL_COUNT_EXPIRATION_TIME, TimeUnit.SECONDS);
    }
}
