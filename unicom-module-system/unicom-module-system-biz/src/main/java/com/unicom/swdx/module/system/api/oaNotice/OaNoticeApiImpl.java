package com.unicom.swdx.module.system.api.oaNotice;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeCreateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.service.oaNotice.OaNoticeServiceImpl;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class OaNoticeApiImpl implements OaNoticeApi{

    @Resource
    OaNoticeServiceImpl oaNoticeService;

    @Resource
    private AdminUserMapper userMapper;

    @Override
    public CommonResult<Long> sendNotice(String title, String message, List<String> it) {
        OaNoticeCreateReqVO createReqVO = new OaNoticeCreateReqVO();
        StringBuilder sb0 = new StringBuilder();
        sb0.append("|");
        for(String mobile:it){
            AdminUserDO user =userMapper.selectByMobile(mobile);
            if(user==null){
                continue;
            }
            sb0.append(user.getId()).append("|");
        }
        createReqVO.setContent(message);
        createReqVO.setTitle(title);
        createReqVO.setType(4);
        createReqVO.setStatus(1);
        createReqVO.setIsRecruit(0);
        createReqVO.setRecipientUserId(sb0.toString());
        oaNoticeService.createNotice(createReqVO);
        return success(null);
    }
}
