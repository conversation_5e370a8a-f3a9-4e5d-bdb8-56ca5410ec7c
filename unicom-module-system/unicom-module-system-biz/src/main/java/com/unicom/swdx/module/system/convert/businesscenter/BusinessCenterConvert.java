package com.unicom.swdx.module.system.convert.businesscenter;

import com.unicom.swdx.module.system.api.businesscenter.dto.DeptSimpleRespDTO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalRespVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BusinessCenterConvert {

    BusinessCenterConvert INSTANCE = Mappers.getMapper(BusinessCenterConvert.class);

    List<DeptSimpleRespDTO> convertList(List<DeptSimpleRespVO> deptSimpleRespDTOS);

    List<PersonnalRespDTO> convertPersonList(List<BusinessCenterPersonnalRespVO> personRespDTOS);
}
