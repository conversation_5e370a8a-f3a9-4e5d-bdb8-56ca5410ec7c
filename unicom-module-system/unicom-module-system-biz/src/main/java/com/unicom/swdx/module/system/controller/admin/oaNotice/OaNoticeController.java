package com.unicom.swdx.module.system.controller.admin.oaNotice;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.*;
import com.unicom.swdx.module.system.service.oaNotice.OaNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - OA系统通知公告")
@RestController
@RequestMapping("/system/oaNotice")
@Validated
public class OaNoticeController {

    @Resource
    private OaNoticeService noticeService;

    @PostMapping("/create")
    @ApiOperation("创建OA系统通知公告")
    @PreAuthorize("@ss.hasPermission('system:oaNotice:create')")
    public CommonResult<Long> createNotice(@Valid @RequestBody OaNoticeCreateReqVO reqVO) {
        if(reqVO.getIsTopNotice()==0){
            reqVO.setTopTime(null);
        }
        Long noticeId = noticeService.createNotice(reqVO);
        return success(noticeId);
    }

    @PostMapping("/update")
    @ApiOperation("修改OA系统通知公告")
    @PreAuthorize("@ss.hasPermission('system:oaNotice:update')")
    public CommonResult<Boolean> updateNotice(@Valid @RequestBody OaNoticeUpdateReqVO reqVO) {
        if(reqVO.getIsTopNotice()==0){
            reqVO.setTopTime(null);
        }
        noticeService.updateNotice(reqVO);
        return success(true);
    }

    @PostMapping("/updatePublish")
    @ApiOperation("编辑OA系统通知公告并发布")
    @PreAuthorize("@ss.hasPermission('system:oaNotice:update')")
    public CommonResult<Boolean> updateNoticeAndPublish(@Valid @RequestBody OaNoticeUpdateReqVO reqVO) {
        if(reqVO.getIsTopNotice()==0){
            reqVO.setTopTime(null);
        }
        noticeService.updatePublishNotice(reqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除OA系统通知公告")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:oaNotice:delete')")
    public CommonResult<Boolean> deleteNotice(@RequestParam("id") Long id) {
        noticeService.deleteNotice(id);
        return success(true);
    }



    @GetMapping("/publishedPage")
    @ApiOperation("获取已发布通知公告列表")
    public CommonResult<PageResult<OaNoticeRespVO>> pagePublishedNotices(@Validated OaNoticePageReqVO reqVO) {
        return success(noticeService.pagePublishedNotices(reqVO));
    }

    @GetMapping("/draftPage")
    @ApiOperation("获取通知公告草稿箱列表")
    public CommonResult<PageResult<OaNoticeRespVO>> pageDraftNotices(@Validated OaNoticePageReqVO reqVO) {
        return success(noticeService.pageDraftNotices(reqVO));
    }

    @GetMapping("/get")
    @ApiOperation("获得OA系统通知公告详情")
    public CommonResult<OaNoticeRespVO> getNotice(@RequestParam("id") Long id) {
        return success(noticeService.getNotice(id));
    }

    @GetMapping("/read")
    @ApiOperation("阅读通知公告详情")
    public CommonResult<OaNoticeRespVO> readNotice(@RequestParam("id") Long id) {
        return success(noticeService.readNotice(id));
    }

    @GetMapping("/personalNotice")
    @ApiOperation("OA系统首页用户收到的通知公告列表")
    public CommonResult<PageResult<OaNoticeRespVO>> getPersonalNotice(@Validated OaNoticePersonalReqVO reqVO) {
        return success(noticeService.getPersonalNotice(reqVO));
    }

}
