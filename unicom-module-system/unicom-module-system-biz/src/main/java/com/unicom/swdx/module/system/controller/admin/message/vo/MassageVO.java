package com.unicom.swdx.module.system.controller.admin.message.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;

/**
 *
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class MassageVO {

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号", required = true)
    @NotNull(message = "账号不能为空")
    private String cpName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true)
    @NotNull(message = "密码不能为空")
    private String cpPassword;

    /**
     * 发送目标的手机号码
     */
    @ApiModelProperty(value = "目标号码", required = true)
    @NotNull(message = "目标号码不能为空")
    private String desMobile;

    /**
     * 发送内容
     * 签名+内容
     */
    @ApiModelProperty(value = "发送内容", required = false)
    private String content;

    /**
     * 选填
     * 最长16位数字
     */
    @ApiModelProperty(value = "扩展码", required = false)
    private String extCode;

    /**
     * 选填
     * 推送状态报告接口中的key
     * 用于对照状态回执
     * 长度为8-64位字母与数字组合
     */
    @ApiModelProperty(value = "自定义消息id", required = true)
    private String smsId;

    /**
     * 加密标签
     */
    @ApiModelProperty(value = "加密标签", required = true)
    private String SecretSign;

    /**
     * 0表示：多个号码发相同内容，多个号码之间用半角逗号分隔，最多2000个
     * 1表示：多个号码发不同内容，注意请保证手机个数与下发内容的顺序与个数一致
     */
    @ApiModelProperty(value = "发送类型", required = true)
    private String type;


    private String templateCode;

    private Map<String, Object> templateParams;

}
