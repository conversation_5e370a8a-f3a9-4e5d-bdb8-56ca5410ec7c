package com.unicom.swdx.module.system.controller.admin.message;

import com.alibaba.ttl.threadpool.agent.internal.javassist.tools.reflect.CannotCreateException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdatePasswordRespVO;
import com.unicom.swdx.module.system.convert.message.MessageConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;
import com.unicom.swdx.module.system.service.message.MessageAuthorityService;
import com.unicom.swdx.module.system.service.message.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "管理后台 - 消息权限")
@RestController
@RequestMapping("/system/messageAuthority")
public class MessageAuthorityController {


    @Resource
    private MessageAuthorityService messageAuthorityService;

    @PostMapping("/create")
    @ApiOperation("创建消息权限")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:messageAuthority:create')")
    public CommonResult<Long> createMessage(@Valid @RequestBody MessageAuthorityCreateReqVO createReqVO) throws CannotCreateException {
        return success(messageAuthorityService.createMessageAuthority(createReqVO));
    }
    @GetMapping("/page")
    @ApiOperation("获得消息权限分页")
    @PreAuthorize("@ss.hasPermission('system:messageAuthority:page')")
    public CommonResult<PageResult<MessageAuthorityDO>> getMessagePage(@Valid MessageAuthorityPageReqVO pageVO) {
        PageResult<MessageAuthorityDO> pageResult = messageAuthorityService.getMessageAuthorityPage(pageVO);
        return success(pageResult);
    }
    @GetMapping("/get")
    @ApiOperation("获得消息权限")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:messageAuthority:get')")
    public CommonResult<MessageAuthorityDO> getMessage(@RequestParam("id") Long id) {
        MessageAuthorityDO message = messageAuthorityService.getMessageAuthority(id);
        return success(message);
    }
    @PostMapping("/update")
    @ApiOperation("更新消息权限")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:messageAuthority:update')")
    public CommonResult<Boolean> updateMessage(@Valid @RequestBody MessageAuthorityUpdateReqVO updateReqVO) {
        messageAuthorityService.updateMessageAuthority(updateReqVO);
        return success(true);
    }
    @PostMapping("/delete")
    @ApiOperation("删除消息权限")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:messageAuthority:delete')")
    public CommonResult<Boolean> deleteMessage(@RequestParam("id") Long id) {
        messageAuthorityService.deleteMessageAuthority(id);
        return success(true);
    }

}
