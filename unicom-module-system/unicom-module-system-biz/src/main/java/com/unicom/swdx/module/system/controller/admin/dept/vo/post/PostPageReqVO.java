package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 岗位分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PostPageReqVO extends PageParam {

    @ApiModelProperty(value = "岗位编号", example = "1")
    private Long id;

    @ApiModelProperty(value = "所属机构编号", example = "1")
    private Long tenantId;

    @ApiModelProperty(value = "岗位标识", example = "unicom", notes = "模糊匹配")
    private String code;

    @ApiModelProperty(value = "岗位名称", example = "sk", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "岗位排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "所属机构", example = "unicom", notes = "模糊匹配")
    private String tenant;

    @ApiModelProperty(value = "展示状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2022-05-02 07:25:24")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "是否展示", example = "true")
    private Boolean displayState;
}
