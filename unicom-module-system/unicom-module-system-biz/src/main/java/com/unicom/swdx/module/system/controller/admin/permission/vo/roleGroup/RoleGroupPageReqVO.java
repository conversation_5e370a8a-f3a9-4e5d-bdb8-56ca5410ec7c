package com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("管理后台 - 角色组分页 Request VO")
public class RoleGroupPageReqVO extends PageParam {
    @ApiModelProperty(value = "角色组名称", example = "sk", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "所属机构", example = "湖南省体育局", notes = "模糊匹配")
    private String tenantName;

    @ApiModelProperty(value = "展示状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;
}
