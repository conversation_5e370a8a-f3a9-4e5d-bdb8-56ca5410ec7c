package com.unicom.swdx.module.system.controller.admin.permission.vo.menu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("管理后台 - 菜单列表 Request VO")
@Data
public class MenuListReqVO {

    @ApiModelProperty(value = "应用id", example = "1")
    private Long clientId;

    @ApiModelProperty(value = "菜单名称", example = "sk", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "展示状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

}
