package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 岗位 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PostBaseVO {

    @ApiModelProperty(value = "岗位名称", required = true, example = "小博主")
    @NotBlank(message = "岗位名称不能为空")
    @Size(max = 50, message = "岗位名称长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "岗位标识", required = true, example = "unicom")
    @NotBlank(message = "岗位标识不能为空")
    @Size(max = 64, message = "岗位标识长度不能超过64个字符")
    private String code;

    @ApiModelProperty(value = "显示顺序不能为空", required = true, example = "1024")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "备注", example = "快乐的备注")
    private String remark;

//    @ApiModelProperty(value = "所属机构", example = "tyj")
//    private String tenant;

    @ApiModelProperty(value = "是否展示", example = "true")
    private Boolean displayState;

}
