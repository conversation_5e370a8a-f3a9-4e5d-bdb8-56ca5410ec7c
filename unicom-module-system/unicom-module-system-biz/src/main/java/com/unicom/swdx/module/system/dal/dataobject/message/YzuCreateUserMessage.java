package com.unicom.swdx.module.system.dal.dataobject.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.unicom.swdx.framework.common.util.object.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;

@Builder
@AllArgsConstructor
@Data
public class YzuCreateUserMessage {


    @JsonProperty("header")
    private HeaderDTO header;
    @JsonProperty("body")
    private BodyDTO body;

    @NoArgsConstructor
    @Data
    public static class HeaderDTO {
        @JsonProperty("sender")
        private String sender ="002";
        @JsonProperty("eventType")
        private String eventType ="00200005";
        @JsonProperty("tenant_code")
        private String tenantCode ="411000";
    }

    @NoArgsConstructor
    @Data
    @Builder
    @AllArgsConstructor
    public static class BodyDTO {
        @JsonProperty("student")
        private StudentDTO student;
        @JsonProperty("trainClassStudent")
        private TrainClassStudentDTO trainClassStudent;

        @NoArgsConstructor
        @Data
        public static class StudentDTO {
            @JsonProperty("birthday")
            private String birthday;
            @JsonProperty("flag")
            private String flag;
            @JsonProperty("nation")
            private String nation;
            @JsonProperty("idCard")
            private String idCard;
            @JsonProperty("sex")
            private String sex;
            @JsonProperty("currentClassId")
            private String currentClassId;
            @JsonProperty("currentJigouId")
            private String currentJigouId;
            @JsonProperty("post")
            private String post;
            @JsonProperty("currentJigouName")
            private String currentJigouName;
            @JsonProperty("phone")
            private String phone;
            @JsonProperty("culture")
            private String culture;
            @JsonProperty("name")
            private String name;
            @JsonProperty("rank")
            private String rank;
            @JsonProperty("zzmm")
            private String zzmm;
            @JsonProperty("id")
            private String id;
            @JsonProperty("tenant_code")
            private String tenantCode;
        }

        @NoArgsConstructor
        @Data
        public static class TrainClassStudentDTO {
            @JsonProperty("studentId")
            private String studentId;
            @JsonProperty("upTime")
            private String upTime = "";
            @JsonProperty("classId")
            private String classId;
            @JsonProperty("id")
            private String id;
            @JsonProperty("upStatus")
            private String upStatus ="";
            @JsonProperty("status")
            private String status;
        }
    }




}
