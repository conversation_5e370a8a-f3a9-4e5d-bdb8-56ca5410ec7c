package com.unicom.swdx.module.system.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.user.vo.trainee.TraineeUserReqVO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.TraineeUserDO;
import com.unicom.swdx.module.system.mq.message.StuDelMessage;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.service.user.TraineeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.module.system.enums.kafka.member.TraineeEventType.*;

@Component
@Slf4j
public class OldKafkaConsumer {

    @Resource
    private TraineeService traineeService;

    @Resource
    private AdminUserService userService;

    @Resource
    private TenantService tenantService;

//    @KafkaListener(topics = "old-kafka-test",groupId = "groupTest1**")
//    public void onTestMessage(ConsumerRecord<String, String> record) {
//        String value = record.value();
//        log.info("[execute][模拟旧kafka收到消息]------"+value);
//    }

    @KafkaListener(topics = "${sendTopic.newKafka}", groupId = "${group-id.one}")
    public void onMessage(ConsumerRecord<String, String> record) {
        String value = record.value();
        log.info("[execute][收到需要消费的消息]------"+value);

        JSONObject jsonObject = JSONUtil.parseObj(value);
        String headerStr = jsonObject.getStr("header");
        JSONObject header = JSONUtil.parseObj(headerStr);
        String eventType = header.getStr("eventType");
        if(Objects.equals(eventType, addMemberEventType) || Objects.equals(eventType, editMemberEventType)){
            handleTrainee(value);
        }else if (Objects.equals(eventType, delMemberEventType) ){
            //删除学员

            StuDelMessage jsonObjecta = JSONUtil.toBean(value , StuDelMessage.class);

            if(jsonObjecta==null){
                return;
            }



            String traineeId = jsonObjecta.getBody().getId();
            LambdaQueryWrapperX<TraineeUserDO> wrapperselect = new LambdaQueryWrapperX<TraineeUserDO>()
                    .eqIfPresent(TraineeUserDO::getTraineeId, traineeId);
            TraineeUserDO traineeUser = traineeService.getOne(wrapperselect.last("limit 1"));


            if(ObjectUtil.isEmpty(traineeUser)){
                return;
            }


            LambdaQueryWrapperX<TraineeUserDO> wrapper = new LambdaQueryWrapperX<TraineeUserDO>()
                    .eqIfPresent(TraineeUserDO::getMobile, traineeUser.getMobile());

            List<TraineeUserDO> traineeUsers = traineeService.list(wrapper);

            if(CollectionUtil.isNotEmpty(traineeUsers)){

//               if(traineeUsers.size()==1){
//                    userService.deleteUser(traineeUsers.get(0).getUserId());
//               }


               traineeService.getBaseMapper().deleteById(traineeUser.getId());


            }



        } else{

            log.info("未处理eventType{}",eventType);
        }
    }

    public void handleTrainee(String value){
        JSONObject jsonObject = JSONUtil.parseObj(value);
        String bodyStr = jsonObject.getStr("body");
        String headerStr = jsonObject.getStr("header");
        JSONObject body = JSONUtil.parseObj(bodyStr);
        JSONObject header = JSONUtil.parseObj(headerStr);
        String studentStr = body.getStr("student");
        String trainClassStudentStr = body.getStr("trainClassStudent");
        JSONObject student = JSONUtil.parseObj(studentStr);
        JSONObject trainClassStudent = JSONUtil.parseObj(trainClassStudentStr);

        TraineeUserReqVO traineeUser = new TraineeUserReqVO();
        Long userId = null;
        String traineeId = null;
        TraineeUserDO traineeUserDO = new TraineeUserDO();

        switch (header.getStr("eventType")){
            case addMemberEventType:
                //添加学员
                if(Objects.nonNull(header.getStr("tenant_code")) && header.getStr("tenant_code").isEmpty()){
                    Long tenantId = tenantService.getTenantByCode(header.getStr("tenant_code")).getId();
                    if(Objects.isNull(tenantId)){
                        log.info("学员所属租户编码tenant_code:{}不存在，添加失败",header.getStr("tenant_code"));
                        return;
                    }
                    traineeUser.setTenantId(tenantId.toString());
                }else {
                    //默认为中共省委党校机构
                    traineeUser.setTenantId("25");
                }
                traineeUser.setNickname(student.getStr("name"));
                if(Objects.nonNull(student.getStr("sex"))){
                    traineeUser.setSex(Integer.parseInt(student.getStr("sex")));
                }
                traineeUser.setMobile(student.getStr("phone"));
                if (StrUtil.isBlank(traineeUser.getMobile())) {
                    return;
                }
                traineeId = trainClassStudent.getStr("id");
                if (StrUtil.isBlank(traineeId)) {
                    return;
                }else{
                    traineeUser.setTraineeId(traineeId);
                }
                AdminUserDO userExist = userService.getUserByMobile(traineeUser.getMobile());
                if(Objects.nonNull(userExist)){
                    //存在用户的话 这里需要加入学员端的权限
                    userService.createTraineeUser(traineeUser);



                    //该学员已经在业中有账号，可能是教职工，也可能是曾经已经上过班级的学员   1人多个班
//                    TraineeUserDO trainee = traineeService.getTraineeUserByUserId(userExist.getId());
//                    if(Objects.nonNull(trainee)){
//                        //该学员可能之前已经上过班级了
//                        trainee.setTraineeId(traineeId);
//                        traineeService.updateById(trainee);
//                    }else{

                    TraineeUserDO exituser = traineeService.getTraineeUserByTraineeId(traineeId);

                    if(ObjectUtil.isEmpty(exituser)||exituser.getId()==null){
                        traineeUserDO.setUserId(userExist.getId());
                        traineeUserDO.setTraineeId(traineeId);
                        traineeUserDO.setMobile(traineeUser.getMobile());
                        //classId
                        traineeUserDO.setClassstr(trainClassStudent.getStr("classId"));

                        traineeService.createTrainee(traineeUserDO);

                    }

                }else {
                    userId = userService.createTraineeUser(traineeUser);


                    TraineeUserDO exituser = traineeService.getTraineeUserByTraineeId(traineeId);

                    if(ObjectUtil.isEmpty(exituser)||exituser.getId()==null){
                        traineeUserDO.setUserId(userId);
                        traineeUserDO.setTraineeId(traineeId);
                        traineeUserDO.setMobile(traineeUser.getMobile());
                        traineeService.createTrainee(traineeUserDO);
                    }

                }
                break;
            case editMemberEventType:
                //修改学员
//                if(Objects.nonNull(header.getStr("tenantId")) && header.getStr("tenantId").isEmpty()){
//                    traineeUser.setTenantId(header.getStr("tenantId"));
//                }else {
//                    //默认为中共省委党校机构
//                    traineeUser.setTenantId("25");
//                }
                traineeUser.setNickname(student.getStr("name"));
                if(Objects.nonNull(student.getStr("sex"))){
                    traineeUser.setSex(Integer.parseInt(student.getStr("sex")));
                }
                traineeUser.setMobile(student.getStr("phone"));

                traineeId = trainClassStudent.getStr("id");

                //通过学员id获得学员在业中的用户id
                TraineeUserDO oldTraineeUserDO = traineeService.getTraineeUserByTraineeId(traineeId);
                userId= oldTraineeUserDO.getUserId();

                traineeUserDO.setUserId(userId);
                traineeUserDO.setTraineeId(traineeId);
                //更新最主要是保持手机号同步
                traineeUserDO.setMobile(traineeUser.getMobile());
                //更新学员表
                traineeUserDO.setId(oldTraineeUserDO.getId());
                traineeService.updateById(traineeUserDO);

                traineeUser.setUserId(userId);
                //更新用户表
                userService.updateTraineeUserById(traineeUser);
                break;
            default:
                log.info("没有对应的eventType！");
        }
    }

}
