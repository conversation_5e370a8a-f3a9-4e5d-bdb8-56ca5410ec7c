package com.unicom.swdx.module.system.controller.admin.tenant;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.type.*;
import com.unicom.swdx.module.system.convert.tenant.TenantTypeConvert;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeDO;
import com.unicom.swdx.module.system.service.tenant.TenantTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;


@Api(tags = "管理后台 - 机构用户类型")
@RestController
@RequestMapping("/system/tenant-type")
@Validated
public class TenantTypeController {

    @Resource
    private TenantTypeService tenantTypeService;

    @PostMapping("/create")
    @ApiOperation("创建机构用户类型")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:tenant-type:create')")
    public CommonResult<Long> createTenantType(@Valid @RequestBody TenantTypeCreateReqVO createReqVO) {
        return success(tenantTypeService.createTenantType(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新机构用户类型")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant-type:update')")
    public CommonResult<Boolean> updateTenantType(@Valid @RequestBody TenantTypeUpdateReqVO updateReqVO) {
        tenantTypeService.updateTenantType(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-status")
    @ApiOperation("更新机构用户类型状态")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant-type:update')")
    public CommonResult<Boolean> updateTenantTypeStatus(@Valid @RequestBody TenantTypeUpdateStatusReqVO updateReqVO) {
        tenantTypeService.updateTenantTypeStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除机构用户类型")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:tenant-type:delete')")
    public CommonResult<Boolean> deleteTenantType(@RequestParam("id") Long id) {
        tenantTypeService.deleteTenantType(id);
        return success(true);
    }

    @GetMapping("/page")
    @ApiOperation("获得机构用户类型分页")
    public CommonResult<PageResult<TenantTypeRespVO>> getTenantTypePage(@Valid TenantTypePageReqVO pageVO) {
        return success(tenantTypeService.getTenantTypePage(pageVO));
    }

    @GetMapping("/list-simple")
    @ApiOperation("获得机构用户类型精简列表")
    public CommonResult<List<TenantTypeSimpleRespVO>> getTenantTypeList() {
        List<TenantTypeDO> list = tenantTypeService.getTenantTypeList();
        return success(TenantTypeConvert.INSTANCE.convertList01(list));
    }

}
