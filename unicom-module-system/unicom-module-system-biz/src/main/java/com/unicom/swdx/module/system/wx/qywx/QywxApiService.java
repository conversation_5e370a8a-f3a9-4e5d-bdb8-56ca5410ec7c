package com.unicom.swdx.module.system.wx.qywx;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.system.controller.admin.auth.vo.QywxQRCodeRespVO;
import com.unicom.swdx.module.system.wx.qywx.vo.QywxAccessTokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024/1/23 12:40
 **/
@Slf4j
@Service
@EnableConfigurationProperties(QywxProperties.class)
public class QywxApiService {

    private final String REDISKEY_ACCESSTOKEN = "qywx:access_token";

    private final String URL_STABLE_ACCESSTOKEN = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";

    private final String URL_GET_USER_TICKET = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo";
    private final String URL_GET_USER_DETAIL = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail";
    private final String URL_GET_USER_F = "https://qyapi.weixin.qq.com/cgi-bin/user/get";


    @Resource
    private RedisUtil redisUtil;

    @Resource
    private QywxProperties properties;

    /**
     * 获取accessToken
     * @return 获取accessToken
     */
    public String getAccessToken() {
        // 先从缓存中获取
        Object accessTokenObj = redisUtil.get(REDISKEY_ACCESSTOKEN);
        if (Objects.nonNull(accessTokenObj)) {
            return accessTokenObj.toString();
        }
        Map<String,Object> params = new HashMap<>();
        params.put("corpid", properties.getCorpid());
        params.put("corpsecret", properties.getCorpsecret());
        String resp = HttpUtil.get(URL_STABLE_ACCESSTOKEN, params);
        if (StrUtil.isBlank(resp)) {
            throw  exception(ACCESS_TOKEN_ERROR);
        }
        QywxAccessTokenVO respVO = null;
        try {
            respVO = JSONUtil.toBean(resp, QywxAccessTokenVO.class);
        } catch (Exception e) {
            throw exception(ACCESS_TOKEN_ERROR);
        }
        redisUtil.set(REDISKEY_ACCESSTOKEN,respVO.getAccessToken(),respVO.getExpiresIn());
        return respVO.getAccessToken();
    }

    /**
     * 根据code获取用户userid和user_ticket
     * @param code
     * @return
     */
    public String getWxUserTicket(String code) {
        Map<String,Object> params = new HashMap<>();
        params.put("access_token",this.getAccessToken());
        params.put("code",code);
        String respStr = HttpUtil.get(URL_GET_USER_TICKET, params);
        if (StrUtil.isBlank(respStr)) {
            throw exception(USER_TICKET_NOT_EXIST);
        }
        log.info(respStr);
        try {
            JSONObject jsonObject = JSONUtil.parseObj(respStr);
            Object userTicket = jsonObject.get("user_ticket");
            Object userid = jsonObject.get("userid");
            if (Objects.nonNull(userTicket)) {
                return userTicket.toString();
            }
            log.error("获取user_ticket");
            return null;
        } catch (Exception e) {
            log.error("获取user_ticket");
            return null;
        }
    }

    /**
     * 根据user_ticket获取用户手机号
     * @param userTicket user_ticket
     * @return 手机号
     */
    public String getMobileFTicket(String userTicket) {
        Map<String,Object> params = new HashMap<>();
        params.put("user_ticket",userTicket);
        String respStr = HttpUtil.post(URL_GET_USER_DETAIL + "?access_token=" + this.getAccessToken(),JSONUtil.toJsonStr(params));
        if (StrUtil.isBlank(respStr)) {
            throw exception(CODE_TO_PHONE_ERROR);
        }
        log.info(respStr);
        try {
            JSONObject jsonObject = JSONUtil.parseObj(respStr);
            Object mobile = jsonObject.get("mobile");
            if (Objects.nonNull(mobile)) {
                return mobile.toString();
            }
            throw exception(CODE_TO_PHONE_ERROR);
        } catch (Exception e) {
            throw exception(CODE_TO_PHONE_ERROR);
        }
    }

    /**
     * 根据user_ticket获取用户手机号
     * @param userid userid
     * @return 手机号
     */
    public String getMobileFuserid(String userid) {
        Map<String,Object> params = new HashMap<>();
        params.put("userid",userid);
        String respStr = HttpUtil.post(URL_GET_USER_F + "?access_token=" + this.getAccessToken(),JSONUtil.toJsonStr(params));
        if (StrUtil.isBlank(respStr)) {
            throw exception(CODE_TO_PHONE_ERROR);
        }
        log.info(respStr);
        try {
            JSONObject jsonObject = JSONUtil.parseObj(respStr);
            Object mobile = jsonObject.get("mobile");
            if (Objects.nonNull(mobile)) {
                return mobile.toString();
            }
            throw exception(CODE_TO_PHONE_ERROR);
        } catch (Exception e) {
            throw exception(CODE_TO_PHONE_ERROR);
        }
    }

    public QywxQRCodeRespVO getQRCode() {
        String uuid = UUID.fastUUID().toString();
        String authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?";
        authUrl += "appid=" + properties.getCorpid();
        authUrl += "&redirect_uri=" + properties.getRedirect_uri();
        authUrl += "&redirect_uri=" + properties.getRedirect_uri();
        authUrl += "&response_type=code&scope=snsapi_privateinfo";
        authUrl += "&state=" + uuid;
        authUrl += "&agentid=" + properties.getAgentid();
        QywxQRCodeRespVO respVO = new QywxQRCodeRespVO();
        respVO.setQrCodeUrl(authUrl);
        respVO.setUniqueCode(uuid);
        return respVO;
    }

    public String getQywxMobile(String code) {
        Map<String,Object> params = new HashMap<>();
        params.put("access_token",this.getAccessToken());
        params.put("code",code);
        String respStr = HttpUtil.get(URL_GET_USER_TICKET, params);
        if (StrUtil.isBlank(respStr)) {
            throw exception(USER_TICKET_NOT_EXIST);
        }
        log.info(respStr);
        try {
            JSONObject jsonObject = JSONUtil.parseObj(respStr);
            Object userTicket = jsonObject.get("user_ticket");
            Object userid = jsonObject.get("userid");
            if (Objects.nonNull(userTicket)) {
                return getMobileFTicket(userTicket.toString());
            }
            if (Objects.nonNull(userid)) {
                return getMobileFuserid(userid.toString());
            }
            throw exception(CODE_TO_PHONE_ERROR);
        } catch (Exception e) {
            log.error("获取用户手机号失败");
            throw exception(CODE_TO_PHONE_ERROR);
        }
    }
}
