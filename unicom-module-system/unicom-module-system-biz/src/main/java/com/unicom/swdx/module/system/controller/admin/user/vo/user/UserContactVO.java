package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 用户 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class UserContactVO {

    @ApiModelProperty(value = "用户昵称", required = true, example = "芋艿")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickname;

    @ApiModelProperty(value = "id", example = "1024")
    private Long id;

    @ApiModelProperty(value = "所属组织", example = "机构")
    private String deptName;

    @ApiModelProperty(value = "岗位数组", example = "1")
    private List<String> posts;

    @ApiModelProperty(value = "行政职务名称", example = "主任")
    private String administrativePositionName;

    @ApiModelProperty(value = "手机号码", example = "15601691300")
    @Mobile
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "多部门id")
    private List<Long> deptIds;

    @ApiModelProperty(value = "多部门")
    private List<DeptDO> departments;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "头像")
    private String avatar;

}
