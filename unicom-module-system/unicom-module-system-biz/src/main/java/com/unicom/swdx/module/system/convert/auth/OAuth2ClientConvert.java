package com.unicom.swdx.module.system.convert.auth;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.oauth2.dto.OAuth2ClientDTO;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.client.*;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

/**
 * OAuth2 客户端 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OAuth2ClientConvert {

    OAuth2ClientConvert INSTANCE = Mappers.getMapper(OAuth2ClientConvert.class);

    OAuth2ClientDO convert(OAuth2ClientCreateReqVO bean);

    OAuth2ClientDO convert(OAuth2ClientUpdateReqVO bean);

    OAuth2ClientDO convert(OAuth2ClientUpdateStatusReqVO bean);

    OAuth2ClientRespVO convert(OAuth2ClientDO bean);

    List<OAuth2ClientRespVO> convertList(List<OAuth2ClientDO> list);

    PageResult<OAuth2ClientRespVO> convertPage(PageResult<OAuth2ClientDO> page);

    List<OAuth2ClientSimpleRespVO> convertList01(List<OAuth2ClientDO> list);

    List<OAuth2ClientDTO> convertList02(Collection<OAuth2ClientDO> list);

}
