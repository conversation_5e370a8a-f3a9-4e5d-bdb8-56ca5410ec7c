package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveIDCardSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 审批状态获取 Response VO")
@Data
@ToString(callSuper = true)
public class TenantApprovalStatusRespVO {

    @ApiModelProperty(value = "上一条审批类型",notes = "0 注册 1 变更")
    private Integer lastApprovalType;

    @ApiModelProperty(value = "上一条审批状态",notes = "0 审批中 1 通过 2 驳回")
    private Integer lastApprovalStatus;

    @ApiModelProperty(value = "上一条审批备注",notes = "驳回原因")
    private String lastApprovalRemark;

    @ApiModelProperty(value = "机构类型", required = true, example = "1")
    private Integer tenantType;

    @ApiModelProperty(value = "机构级别", required = false, example = "1")
    private Integer tenantLevel;

    @ApiModelProperty(value = "范围", example = "[10000216,10002038]")
    private List<Long> levelRange;

    @ApiModelProperty(value = "业务指导单位")
    private Integer guideTenantId;

    @ApiModelProperty(value = "单位类型", required = true, example = "1")
    private Integer companyType;

    @ApiModelProperty(value = "关联机构id",required = true)
    private Long linkedTenantId;

    @ApiModelProperty(value = "机构全称", required = true)
    private String tenantName;

    @ApiModelProperty(value = "统一社会信用代码",required = true,example = "sk")
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "注册地区划",required = true,example = "sk")
    private List<Long> registerRegion;

    @ApiModelProperty(value = "注册地地址",required = true,example = "sk")
    private String registerAddress;

    @ApiModelProperty(value = "所在地区划",required = true,example = "sk")
    private List<Long> locationRegion;

    @ApiModelProperty(value = "所在地地址",required = true,example = "sk")
    private String locationAddress;

    @ApiModelProperty(value = "法定代表人姓名",required = true,example = "sk")
    private String legalRepresentativeName;

    @ApiModelProperty(value = "法人代表身份证号码",required = true,example = "sk")
    @JsonSerialize(using = SensitiveIDCardSerializer.class)
    private String legalRepresentativeIdCard;

    @ApiModelProperty(value = "机构用户类型", required = true, example = "1")
    private List<Long> instUserType;

    @ApiModelProperty(value = "营业执照", required = true, example = "sk")
    private String businessLicenseUrl;

    @ApiModelProperty(value = "过期时间",required = false)
    private LocalDateTime expireTime;
}
