package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.bind.DefaultValue;

@Data
public class TodoListPageAllReqVO extends PageParam {
    @ApiModelProperty(value = "事项发起人", required = true)
    private Long loginUserId;

    @ApiModelProperty(value = "事项名称", required = true)
    private String task;

    @ApiModelProperty(value = "发起人", required = true)
    private String name;

    @ApiModelProperty(value = "事项来源", required = true)
    private String source;

    @ApiModelProperty(value = "是否为App", required = false)
    private Integer isApp=0;

}
