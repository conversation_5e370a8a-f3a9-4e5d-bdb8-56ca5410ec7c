package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@ApiModel("管理后台 - 用户更新手机号 Request VO")
@Data
public class UserUpdateMobileReqVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "旧手机号", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @Mobile
    private String oldMobile;

    @ApiModelProperty(value = "新手机号码", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @Mobile
    private String newMobile;

    @ApiModelProperty(value = "验证码", required = true, example = "1234", notes = "验证码开启时，需要传递")
    @NotBlank(message = "验证码不能为空", groups = AuthLoginReqVO.CodeEnableGroup.class)
    private String verification;

}
