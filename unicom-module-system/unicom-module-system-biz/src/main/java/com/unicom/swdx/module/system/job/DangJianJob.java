package com.unicom.swdx.module.system.job;

import com.unicom.swdx.module.system.controller.admin.auth.AuthController;
import com.unicom.swdx.module.system.controller.admin.dept.DeptController;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class DangJianJob {

    @Resource
    private AuthController authController;

    @XxlJob("DangJianJob")
    public void execute() {
        log.info("开始党建数据同步");
        authController.dangjian();
        log.info("党建数据同步完成");
    }

}
