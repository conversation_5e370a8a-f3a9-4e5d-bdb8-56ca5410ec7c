package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 用户分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserPageReqVO extends PageParam {

    @ApiModelProperty(value = "用户账号", example = "unicom", notes = "模糊匹配")
    private String username;

    @ApiModelProperty(value = "姓名", example = "张三", notes = "模糊匹配")
    private String nickname;

    @ApiModelProperty(value = "手机号码", example = "unicom", notes = "模糊匹配")
    private String mobile;

    @ApiModelProperty(value = "状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @ApiModelProperty(value = "机构ID", example = "25")
    private Long tenantId;

    @ApiModelProperty(value = "组织编号", example = "1024", notes = "同时筛选子组织")
    private Long deptId;

    @ApiModelProperty(value = "组织名称", example = "机构", notes = "同时筛选子组织")
    private String deptName;

    @ApiModelProperty(value = "是否需要筛选授权")
    private Boolean isAuthority;
    @ApiModelProperty(value = "是否展示全部")
    private Boolean isAll;
    @ApiModelProperty(value = "是否需要过滤")
    private Boolean isScreen;

    @ApiModelProperty(value = "负责人部门编号")
    private List<Long> leaderDeptId;

    @ApiModelProperty(value = "是否外部人员管理")
    private Boolean isExternal;

    @ApiModelProperty(value = "人员状态")
    private Integer personStatus;

    @ApiModelProperty(value = "人员分类")
    private Integer personClassification;

    @ApiModelProperty(value = "小程序")
    private Boolean hasXcx;

}
