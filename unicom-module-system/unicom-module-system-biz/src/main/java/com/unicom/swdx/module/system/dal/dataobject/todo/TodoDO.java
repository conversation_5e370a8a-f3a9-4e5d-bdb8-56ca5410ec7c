package com.unicom.swdx.module.system.dal.dataobject.todo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.module.system.enums.home.TodoItemEnum;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 待办事项 DO
 *
 * <AUTHOR>
 */
@TableName("midoffice_todo")
@KeySequence("midoffice_todo_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TodoDO extends BaseDO{

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 类型（见枚举）
     */
    private Integer type;
    /**
     * 缓急性（见政务字典）
     */
    private String urgencyLevel;
    /**
     * 标题
     */
    private String title;
    /**
     * 发起人
     */
    private String submitter;
    /**
     * 备注
     */
    private String remark;
    /**
     * 发起时间
     */
    private LocalDateTime submitTime;
    /**
     * 发起子系统id
     */
    private Integer subsystemId;
    /**
     * 跳转链接
     */
    private String linkUrl;
    /**
     * 待办人
     */
    private String todoUserId;
    /**
     * 参会人是否需要回执 0:是 1:否
     */
    private Boolean receipt;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 参会地点
     */
    private String meetingAddress;
    /**
     * 状态（0=待办，1=已办）
     */
    private Integer status;

    /**
     * 待办流程id（ps：可能不唯一，因为id为子系统生成）
     */
    private String processId;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 任务节点
     */
    private String taskCode;

    /**
     * 流程状态
     */
    private Integer processStatus;
    /**
     * 具体事项{@link TodoItemEnum}
     */
    private Integer item;
    /**
     * 待办id
     */
    private String todoId;
    /**
     * 收文来源机构id
     */
    private Long receivingSourceTenantId;

    /**
     * 权限标识
     */
    private String permission;

    /**
     * 政民互动来源
     * */
    private String peopleIneractionSource;
}
