package com.unicom.swdx.module.system.job;


import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class JiaowuJob {


    @Resource
    private PersonnalApi personnalApi;

    @XxlJob("JiaowuJob")
    public void execute() {

        personnalApi.sendAllPersonInfo("http://10.32.23.143:8888");

    }


}
