package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 快捷入口用户关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ShortcutUserBaseVO {

    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "快捷入口id", required = true)
    @NotNull(message = "快捷入口id不能为空")
    private Integer shortcutId;

}
