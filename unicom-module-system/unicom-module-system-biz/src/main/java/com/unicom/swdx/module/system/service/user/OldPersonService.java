package com.unicom.swdx.module.system.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.system.dal.dataobject.dept.OldDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.OldPersonDO;

import java.util.List;

public interface OldPersonService extends IService<OldPersonDO> {

    OldPersonDO getOldPersonByUserId(Long userId);


    List<OldPersonDO> getOldPersonsByUserId(Long userId);

    List<OldPersonDO> getOldPersonsByMobile(String mobile);

    OldPersonDO getOldPersonsByMobileOrName(String mobile ,String name);

}
