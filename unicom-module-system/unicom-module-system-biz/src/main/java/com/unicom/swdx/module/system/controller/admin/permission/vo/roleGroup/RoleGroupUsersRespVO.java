package com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RoleGroupUsersRespVO extends RoleGroupBaseVO{

    @ApiModelProperty(value = "已分配用户精简信息")
    private PageResult<UserSimpleRespVO> users;
}
