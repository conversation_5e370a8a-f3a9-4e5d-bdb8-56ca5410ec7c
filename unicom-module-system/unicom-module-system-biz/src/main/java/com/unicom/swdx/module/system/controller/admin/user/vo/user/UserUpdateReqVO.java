package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.validation.Mobile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 用户更新 Request VO")
@Data
//@EqualsAndHashCode(callSuper = true)
public class UserUpdateReqVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    @NotNull(message = "用户编号不能为空")
    private Long id;

    @ApiModelProperty(value = "用户名", required = true, example = "unicom")
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @ApiModelProperty(value = "用户昵称", required = true, example = "芋艿")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickname;

    @ApiModelProperty(value = "组织ID", example = "1")
    @NotNull(message = "组织不能为空")
    private Long deptId;

    @ApiModelProperty(value = "手机号码", example = "15601691300")
    @Mobile
    private String mobile;

    @ApiModelProperty(value = "显示排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    @ApiModelProperty(value = "用户性别", example = "1", notes = "参见 SexEnum 枚举类")
    private Integer sex;

    @ApiModelProperty(value = "岗位编号数组", example = "1")
    private Set<Long> postIds;

    @ApiModelProperty(value = "用户状态", example = "0",notes = "0=开启，1=关闭")
    @NotNull(message = "用户状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "用户头像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @ApiModelProperty(value = "备注", example = "我是一个用户")
    private String remark;

    private List<Long> deptIds;
}
