package com.unicom.swdx.module.system.service.dept;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.collection.SetUtils.asSet;

/**
 * 岗位 Service 接口
 *
 * <AUTHOR>
 */
public interface PostService extends IService<PostDO> {

    /**
     * 创建岗位
     *
     * @param reqVO 岗位信息
     * @return 岗位编号
     */
    Long createPost(PostCreateReqVO reqVO);

    /**
     * 更新岗位
     *
     * @param reqVO 岗位信息
     */
    void updatePost(PostUpdateReqVO reqVO);

    /**
     * 删除岗位信息
     *
     * @param id 岗位编号
     */
    void deletePost(Long id);

    /**
     * 获得岗位列表
     *
     * @param ids 岗位编号数组。如果为空，不进行筛选
     * @return 部门列表
     */
    default List<PostDO> getPosts(@Nullable Collection<Long> ids) {
        return getPosts(ids, asSet(CommonStatusEnum.ENABLE.getStatus(), CommonStatusEnum.DISABLE.getStatus()),null);
    }

    /**
     * 获得符合条件的岗位列表
     *
     * @param ids 岗位编号数组。如果为空，不进行筛选
     * @param statuses 状态数组。如果为空，不进行筛选
     * @return 部门列表
     */
    List<PostDO> getPosts(@Nullable Collection<Long> ids, @Nullable Collection<Integer> statuses,Long deptId);

    /**
     * 获得岗位分页列表
     *
     * @param reqVO 分页条件
     * @return 部门分页列表
     */
    PageResult<PostRespVO> getPostPage(PostPageReqVO reqVO);

    /**
     * 获得岗位列表
     *
     * @param reqVO 查询条件
     * @return 部门列表
     */
    List<PostExcelVO> getPosts(PostExportReqVO reqVO);

    /**
     * 获得岗位信息
     *
     * @param id 岗位编号
     * @return 岗位信息
     */
    PostDO getPost(Long id);

    /**
     * 校验岗位们是否有效。如下情况，视为无效：
     * 1. 岗位编号不存在
     * 2. 岗位被禁用
     *
     * @param ids 岗位编号数组
     */
    void validPosts(Collection<Long> ids);



    /**
     * 获得指定机构下的岗位
     *
     * @param tenantId 机构id
     * @return 岗位列表
     */
    List<PostDO> getPostList(Long tenantId);

    PostImportRespVO importPost(List<PostImportExcelVO> importPosts);

    /**
     * 获取用户对应岗位标识的排序最大的岗位
     * @param userId 用户id
     * @param codes 岗位标识
     * @return 最大岗位
     */
    PostDO getMaxSortPostByUser(Long userId, Collection<String> codes);

    /**
     * 获取用户对应岗位标识的排序最小的岗位
     * @param userId 用户id
     * @param codes 岗位标识
     * @return 最大岗位
     */
    PostDO getMinSortPostByUser(Long userId, Collection<String> codes);

    List<Long> getUserByPost(String code, Long tenantId);


    Boolean judgeHRDirector(Long loginUserId);

    List<PostSimpleUserRespVO> getPosts(String processInstanceId);

    PostDO getPostByCode(String postCode, Long tenantId);

    void saveUserPosts(Long userId, Set<Long> postIds);

    PostUsersRespVO getPostUsers(PostUsersPageReqVO pageReqVO);
}
