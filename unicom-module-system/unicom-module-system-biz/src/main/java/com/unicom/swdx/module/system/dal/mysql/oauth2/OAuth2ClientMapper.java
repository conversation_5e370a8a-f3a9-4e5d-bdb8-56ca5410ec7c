package com.unicom.swdx.module.system.dal.mysql.oauth2;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.client.OAuth2ClientPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OAuth2 客户端 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OAuth2ClientMapper extends BaseMapperX<OAuth2ClientDO> {

    default PageResult<OAuth2ClientDO> selectPage(OAuth2ClientPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OAuth2ClientDO>()
                .likeIfPresent(OAuth2ClientDO::getName, reqVO.getName())
                .likeIfPresent(OAuth2ClientDO::getCode,reqVO.getCode())
                .eqIfPresent(OAuth2ClientDO::getStatus, reqVO.getStatus())
                .orderBy(true, true, OAuth2ClientDO::getSort)
                .orderBy(true, false, OAuth2ClientDO::getCreateTime));
    }

    default OAuth2ClientDO selectByClientId(String clientId) {
        return selectOne(OAuth2ClientDO::getClientId, clientId);
    }

    @Select("SELECT COUNT(*) FROM system_oauth2_client WHERE update_time > #{maxUpdateTime}")
    int selectCountByUpdateTimeGt(LocalDateTime maxUpdateTime);

    default OAuth2ClientDO selectByName(String name) {
        return selectOne(OAuth2ClientDO::getName, name);
    }

    default OAuth2ClientDO selectByCode(String code) {
        return selectOne(OAuth2ClientDO::getCode, code);
    }

    default List<OAuth2ClientDO> selectListEnable() {
        return selectList(new LambdaQueryWrapperX<OAuth2ClientDO>()
                .eq(OAuth2ClientDO::getStatus,CommonStatusEnum.ENABLE.getStatus()));
    }

}
