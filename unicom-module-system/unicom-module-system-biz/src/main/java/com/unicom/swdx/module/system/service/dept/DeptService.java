package com.unicom.swdx.module.system.service.dept;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.module.system.api.dept.dto.DeptDTO;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;

import java.util.*;

/**
 * 部门 Service 接口
 *
 * <AUTHOR>
 */
public interface DeptService extends IService<DeptDO> {

    /**
     * 初始化部门的本地缓存
     */
    void initLocalCache();

    /**
     * 创建部门
     *
     * @param reqVO 部门信息
     * @return 部门编号
     */
    Long createDept(DeptCreateReqVO reqVO);

    List<DeptDTO> getalllistdept(String url);

    /**
     * 更新部门
     *
     * @param reqVO 部门信息
     */
    void updateDept(DeptUpdateReqVO reqVO);

    /**
     * 删除部门
     *
     * @param id 部门编号
     */
    void deleteDept(Long id);

    Integer getChildrenDeptCount(Long id);

    Long getAllDeptUsers(Long id);
    /**
     * 筛选部门列表
     *
     * @param reqVO 筛选条件请求 VO
     * @return 部门列表
     */
    List<DeptDO> getSimpleDepts(DeptListReqVO reqVO);

    /**
     * 获取组织列表
     * @param reqVO
     * @return 组织列表
     */
    List<DeptRespVO> getDeptsList(DeptListReqVO reqVO);

    /**
     *
     * @param parentId
     * @return
     */
    List<DeptRespVO> getChildrenDeptByDeptId(Long parentId);

    /**
     * 获得所有子部门，从缓存中
     *
     * @param parentId 部门编号
     * @param recursive 是否递归获取所有
     * @return 子部门列表
     */
    List<DeptDO> getDeptsByParentIdFromCache(Long parentId, boolean recursive);

    /**
     * 获得部门信息数组
     *
     * @param ids 部门编号数组
     * @return 部门信息数组
     */
    List<DeptDO> getDepts(Collection<Long> ids);

    /**
     * 获得部门信息
     *
     * @param id 部门编号
     * @return 部门信息
     */
    DeptDO getDept(Long id);


    List<DeptDO> getCacheDepts(List<Long> ids);

    DeptDO getDeptIgnoreDeleted(Long id);


    DeptDO getDeptByTenantAndName(Long tenantid ,String deptname );

    /**
     * 校验部门们是否有效。如下情况，视为无效：
     * 1. 部门编号不存在
     * 2. 部门被禁用
     *
     * @param ids 角色编号数组
     */
    void validDepts(Collection<Long> ids);

    /**
     * 获得指定编号的部门列表
     *
     * @param ids 部门编号数组
     * @return 部门列表
     */
    List<DeptDO> getSimpleDepts(Collection<Long> ids);

    /**
     * 获得指定编号的部门 Map
     *
     * @param ids 部门编号数组
     * @return 部门 Map
     */
    @DataPermission(enable = false)
    default Map<Long, DeptDO> getDeptMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<DeptDO> list = getSimpleDepts(ids);
        return CollectionUtils.convertMap(list, DeptDO::getId);
    }

    void clearLeader(Long id);

    List<DeptDO> getAllParentDeptByDeptId(Long deptId);

    /**
     * 获取组织下所有子组织
     * @param deptId
     * @return
     */
    List<DeptDO> getAllChildrenDeptByDeptId(Long deptId);

    /**
     *
     * @param deptIds
     * @return
     */
    List<DeptDO> getAllChildrenDeptByDeletedDeptId(List<Long> deptIds);

    /**
     * 部门路径转换
     * @param deptSimpleRespVOS
     * @return
     */
    List<DeptSimpleRespVO> convertPath(List<DeptSimpleRespVO> deptSimpleRespVOS);

    /**
     * 获取所有组织精简信息（用于获取组织树）
     * @return
     */
    List<DeptTreeVO> getTreeDept();

    /**
     * 获得指定机构下的组织树（用于获取组织树）
     * @return
     */
    List<DeptTreeVO> getTreeDeptInTenant(Set<Long> tenantIds);

    List<Long> getDeptTreeId(List<Long> deptTreeIds,List<DeptTreeVO> treeDept);

    List<DeptDO> selectChildrenDeptByDeptName(String deptName);


    /**
     * 补全这些部门id的所有子部门
     * @param deptIds 部门id集合
     * @return 包含所有子部门的部门id集合
     */
    Set<Long> completeChildDeptIdFromCache(Set<Long> deptIds);

    /**
     * 获取用户有数据权限的组织id列表
     * @return 组织id列表
     */
    Set<Long> getDataPermissionDepts();

    Long getTenantIdByDeptId(Long deptId);

    DeptDO getByLeaderUserId(Long leaderUserId);

    Long getDeptIdByDeptName(String deptName);

    Long getDeptIdByUserId(Long id);

    List<DeptRespDTO> getListByLeader(Long leaderUserId);

    void sendAllDeptInfo(String url);


}
