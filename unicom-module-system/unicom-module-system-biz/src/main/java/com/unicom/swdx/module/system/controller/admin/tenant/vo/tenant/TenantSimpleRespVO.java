package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import java.time.LocalDateTime;
@ApiModel("管理后台 - 机构精简列表 Response VO")
@Data
@ToString(callSuper = true)
public class TenantSimpleRespVO {

    @ApiModelProperty(value = "机构id", example = "1024")
    private Long id;

    @ApiModelProperty(value = "机构名", example = "sk")
    private String name;
    @ApiModelProperty(value = "管理员姓名")
    private String contactNickname;
    @ApiModelProperty(value = "管理员手机号码")
    private String contactMobile;
    @ApiModelProperty(value = "用户名")
    private String contactName;
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "所属区域编码")
    private Integer tenantCode;

}
