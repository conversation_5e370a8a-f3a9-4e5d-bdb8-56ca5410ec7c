package com.unicom.swdx.module.system.convert.message;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessagePageRespVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageRespGetVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MessageConvert {

    MessageConvert INSTANCE = Mappers.getMapper(MessageConvert.class);

    MessageDO convert(MessageCreateReqVO bean);
    MessageDO convert(MessageUpdateReqVO bean);
    PageResult<MessagePageRespVO> convertPage(PageResult<MessageDO> page);
    MessageRespGetVO convert01(MessageDO bean);
}
