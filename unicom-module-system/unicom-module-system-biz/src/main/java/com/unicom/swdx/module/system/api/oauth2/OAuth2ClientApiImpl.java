package com.unicom.swdx.module.system.api.oauth2;

import com.unicom.swdx.module.system.api.oauth2.dto.OAuth2ClientDTO;
import com.unicom.swdx.module.system.convert.auth.OAuth2ClientConvert;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

/**
 * <AUTHOR>
 * @date 2023/9/18 16:27
 **/
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class OAuth2ClientApiImpl implements OAuth2ClientApi{

    @Resource
    @Lazy
    private OAuth2ClientService oAuth2ClientService;

    @Override
    public List<OAuth2ClientDTO> getAll() {
        Collection<OAuth2ClientDO> clientList = oAuth2ClientService.getEnableListFromCache();
        return OAuth2ClientConvert.INSTANCE.convertList02(clientList);
    }
}
