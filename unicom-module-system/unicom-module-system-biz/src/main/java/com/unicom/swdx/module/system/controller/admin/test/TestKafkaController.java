package com.unicom.swdx.module.system.controller.admin.test;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.mq.producer.infor.InforProducer;
import com.unicom.swdx.module.system.mq.producer.user.TeacherProducer;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/system/test")
public class TestKafkaController {

//    @Resource
//    private TeacherProducer teacherProducer;

    @Resource
    private InforProducer inforProducer;

    @Resource
    private SmsSendApi smsSendApi;

//    @GetMapping("kafka")
//    public CommonResult<Boolean> test() {
//        teacherProducer.sendTeacherData();
//        return success(true);
//    }

    @GetMapping("infor")
    public CommonResult<Boolean> testInfor() {
        inforProducer.sendRefreshMessage("ddd");
        return success(true);
    }

    @RequestMapping("sendSms")
    public CommonResult<Long> sendSms() {
        Map<String,Object> map = new HashMap<>();
        map.put("arg1", "【湖南省委党校】发送给lx");
        Long id = smsSendApi.sendSingleSms("18608404235", null, null, "admin-sms-login-new", map);
        return success(id);
    }

}
