package com.unicom.swdx.module.system.service.databoard;

import com.unicom.swdx.module.system.controller.admin.databoard.vo.*;

import java.util.List;

public interface DataboardService {

    /**
     * 获取机构统计信息
     * @return 机构统计信息
     *
     * */
    TenantCountRespVO getTenantCount();


    /**
     * 获取用户统计信息
     * @return 用户统计信息
     *
     * */
    UserCountRespVO getUserCount();


    List<LastSixMonthTenantRespVO> getLastSixMonthTenant();


    List<LastSixMonthUsersRespVO> getLastSixMonthUsers();


    RealNameUsersCountRespVO getRealNameUsersCount();

    List<UsersAttestationTypeCountRespVO> getUserAttestationTypeCount();

}
