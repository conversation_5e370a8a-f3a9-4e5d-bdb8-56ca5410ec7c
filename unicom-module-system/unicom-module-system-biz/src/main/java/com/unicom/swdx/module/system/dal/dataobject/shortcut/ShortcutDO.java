package com.unicom.swdx.module.system.dal.dataobject.shortcut;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 快捷入口 DO
 *
 * <AUTHOR>
 */
@TableName("midoffice_shortcut")
@KeySequence("midoffice_shortcut_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ShortcutDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 所属子系统id
     */
    private Long clientId;
    /**
     * 快捷入口名称
     */
    private String name;
    /**
     * 快捷入口链接
     */
    private String linkUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 图标
     */
    private String svgIcon;
    /**
     * 菜单id
     */
    private Long menuId;
    /**
     * 状态
     */
    private Integer status;

}
