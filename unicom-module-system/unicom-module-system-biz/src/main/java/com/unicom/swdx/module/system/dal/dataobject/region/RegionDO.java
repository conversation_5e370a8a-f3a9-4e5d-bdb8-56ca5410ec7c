package com.unicom.swdx.module.system.dal.dataobject.region;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 地区 DO
 *
 * <AUTHOR>
 */
@TableName("system_region")
@KeySequence("system_region_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegionDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 行政区划代码
     */
    private Integer code;
    /**
     * 名称
     */
    private String name;
    /**
     * 上级id
     */
    private Long parentId;
    /**
     * level
     */
    private Integer level;

}
