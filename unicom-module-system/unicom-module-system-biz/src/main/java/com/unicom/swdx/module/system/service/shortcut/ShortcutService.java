package com.unicom.swdx.module.system.service.shortcut;


import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutClientRespVo;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutUpdateReqVO;
import javax.validation.Valid;
import java.util.List;

/**
 * 快捷入口 Service 接口
 *
 * <AUTHOR>
 */
public interface ShortcutService {

    /**
     * 创建快捷入口
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createShortcut(@Valid ShortcutCreateReqVO createReqVO);

    /**
     * 更新快捷入口
     *
     * @param updateReqVO 更新信息
     */
    void updateShortcut(@Valid ShortcutUpdateReqVO updateReqVO);

    /**
     * 获得快捷入口列表
     *
     * @return 快捷入口列表
     */
    List<ShortcutClientRespVo> getShortcutList();

    void refreshShortcut();

}
