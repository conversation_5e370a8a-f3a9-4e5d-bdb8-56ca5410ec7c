package com.unicom.swdx.module.system.controller.admin.oauth2;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.client.*;
import com.unicom.swdx.module.system.convert.auth.OAuth2ClientConvert;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 应用管理")
@RestController
@RequestMapping("/system/oauth2-client")
@Validated
public class OAuth2ClientController {

    @Resource
    private OAuth2ClientService oAuth2ClientService;

    @GetMapping("/init")
    @ApiOperation("初始化缓存")
    public CommonResult<Boolean> initCache() {
        oAuth2ClientService.initLocalCache();
        return success(true);
    }

    @PostMapping("/create")
    @ApiOperation("创建应用")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:create')")
    public CommonResult<Long> createOAuth2Client(@Valid @RequestBody OAuth2ClientCreateReqVO createReqVO) {
        return success(oAuth2ClientService.createOAuth2Client(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新应用")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:update')")
    public CommonResult<Boolean> updateOAuth2Client(@Valid @RequestBody OAuth2ClientUpdateReqVO updateReqVO) {
        oAuth2ClientService.updateOAuth2Client(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-status")
    @ApiOperation("更新应用状态")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:update')")
    public CommonResult<Boolean> updateOAuth2ClientStatus(@Valid @RequestBody OAuth2ClientUpdateStatusReqVO updateReqVO) {
        oAuth2ClientService.updateOAuth2ClientStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除应用")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:delete')")
    public CommonResult<Boolean> deleteOAuth2Client(@RequestParam("id") Long id) {
        oAuth2ClientService.deleteOAuth2Client(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得应用")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
//    @PreAuthorize("@ss.hasPermission('system:oauth2-client:query')")
    public CommonResult<OAuth2ClientRespVO> getOAuth2Client(@RequestParam("id") Long id) {
        OAuth2ClientDO oAuth2Client = oAuth2ClientService.getOAuth2Client(id);
        return success(OAuth2ClientConvert.INSTANCE.convert(oAuth2Client));
    }

    @GetMapping("/list-simple")
    @ApiOperation("获得应用精简列表")
    public CommonResult<List<OAuth2ClientSimpleRespVO>> getClientSimpleList() {
        List<OAuth2ClientDO> list = oAuth2ClientService.getClientSimpleList();
        return success(OAuth2ClientConvert.INSTANCE.convertList01(list));
    }

    @GetMapping("/list-all-simple")
    @ApiOperation("获得应用精简列表")
    public CommonResult<List<OAuth2ClientSimpleRespVO>> getAllClientSimpleList() {
        List<OAuth2ClientDO> list = oAuth2ClientService.getAllClientSimpleList();
        return success(OAuth2ClientConvert.INSTANCE.convertList01(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得应用分页")
    public CommonResult<PageResult<OAuth2ClientRespVO>> getOAuth2ClientPage(@Valid OAuth2ClientPageReqVO pageVO) {
        PageResult<OAuth2ClientDO> pageResult = oAuth2ClientService.getOAuth2ClientPage(pageVO);
        return success(OAuth2ClientConvert.INSTANCE.convertPage(pageResult));
    }

}
