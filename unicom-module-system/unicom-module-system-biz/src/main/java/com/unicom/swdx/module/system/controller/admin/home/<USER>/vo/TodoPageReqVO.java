package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 待办事项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TodoPageReqVO extends PageParam {
    @ApiModelProperty(value = "排序类型，0=最早发起，1=最近发起")
    private Integer sort;

    @ApiModelProperty(value = "类型（见字典 midoffice_todo_type）")
    private Integer type;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "状态（0=待办，1=已办）")
    private Integer status;

    @ApiModelProperty(value = "参会人是否需要回执 0:是 1:否")
    private Boolean receipt;

}
