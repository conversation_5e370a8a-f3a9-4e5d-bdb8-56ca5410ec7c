package com.unicom.swdx.module.system.service.message;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessagePageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpdateReqVO;
import com.unicom.swdx.module.system.convert.message.MessageConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.mysql.message.MessageAuthorityMapper;
import com.unicom.swdx.module.system.dal.mysql.message.MessageMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.TEMPLATE_NAME_DUPLICATE;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.TEMPLATE_NOT_EXISTS;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

/**
 * 机构 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MessageServiceImpl extends ServiceImpl<MessageMapper, MessageDO> implements MessageService {

    @Resource
    private MessageMapper messageMapper;
    @Resource
    private RoleService roleService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private MessageAuthorityMapper messageAuthorityMapper;

    @Resource
    private RoleMapper roleMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMessage(MessageCreateReqVO createReqVO) {
        // 校验消息模板
        checkCreate(createReqVO);
        //创建消息模板
        MessageDO message = MessageConvert.INSTANCE.convert(createReqVO);
        message.setTenantId(getTenantId());
        messageMapper.insert(message);
        return message.getId();
    }
    @Override
    public PageResult<MessageDO> getMessagePage(MessagePageReqVO pageReqVO) {
//        //判断是否为管理员
//        RoleDO role = roleService.getRoleById(getLoginUserId());
//        Boolean isAdmin = roleService.hasAnySuperAdmin(role.getDataScopeDeptIds());
 //       判断是否为超级管理员
        List<Long> rloes = roleMapper.selectAllRoleByUserId(SecurityFrameworkUtils.getLoginUserId());
        Boolean isAdmin = permissionService.isSuperAdmin(SecurityFrameworkUtils.getLoginUserId())|| rloes.contains(6L);
        Boolean hasSuperAdmin = permissionService.isSuperAdmin(SecurityFrameworkUtils.getLoginUserId());
        return messageMapper.selectPage(pageReqVO,isAdmin,hasSuperAdmin);
    }
    private void checkCreate(MessageCreateReqVO createReqVO) {
        if(createReqVO.getSystemId()==null){
            return;
        }
        List<String> messa = messageMapper.selectBySystemId(createReqVO.getSystemId());
        if (messa == null) {
            return;
        }

        if(messa.stream().anyMatch(u-> Objects.equals(u, createReqVO.getName()))){
                throw ServiceExceptionUtil.exception(TEMPLATE_NAME_DUPLICATE);
        }
    }

    @Override
    public MessageDO getMessage(Long id) {
        return this.getById(id);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessage(MessageUpdateReqVO updateReqVO) {
        // 校验信息模板
        checkUpdate(updateReqVO);
        // 更新信息模板
        MessageDO message = MessageConvert.INSTANCE.convert(updateReqVO);
        messageMapper.updateById(message);
    }

    private void checkUpdate(MessageUpdateReqVO updateReqVO) {
        if(updateReqVO.getSystemId()==null){
            return;
        }
        List<String> messas = messageMapper.selectBySystemId(updateReqVO.getSystemId());
        if (messas == null) {
            return;
        }
        int count = 0;
       for(String messa:messas){
           if(Objects.equals(messa, updateReqVO.getName())){
               count++;
           }
       }
       if(count>1){
           throw ServiceExceptionUtil.exception(TEMPLATE_NAME_DUPLICATE);
       }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMessage(Long id) {
        // 校验机构id是否存在
        MessageDO messageDO = this.getById(id);
        if (messageDO == null) {
            throw exception(TEMPLATE_NOT_EXISTS);
        }
        // 删除信息模板
        this.removeById(id);

    }

}
