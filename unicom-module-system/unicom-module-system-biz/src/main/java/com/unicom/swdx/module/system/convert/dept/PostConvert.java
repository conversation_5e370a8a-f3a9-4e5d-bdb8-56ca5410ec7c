package com.unicom.swdx.module.system.convert.dept;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PostConvert {

    PostConvert INSTANCE = Mappers.getMapper(PostConvert.class);

    List<PostSimpleRespVO> convertList02(List<PostDO> list);
    List<PostSimpleUserRespVO> convertList04(List<PostDO> list);

    PageResult<PostRespVO> convertPage(PageResult<PostDO> page);

    PostRespVO convert(PostDO id);

    PostDO convert(PostCreateReqVO bean);
    PostDO convert01(PostImportExcelVO bean);
    PostDO convert(PostUpdateReqVO reqVO);

    List<PostExcelVO> convertList03(List<PostRespVO> list);

    List<PostDO> convertList(List<PostImportExcelVO> bean);

    PostRespDTO convertDTO(PostDO bean);

    PostUsersRespVO convert1(PostDO post);
}
