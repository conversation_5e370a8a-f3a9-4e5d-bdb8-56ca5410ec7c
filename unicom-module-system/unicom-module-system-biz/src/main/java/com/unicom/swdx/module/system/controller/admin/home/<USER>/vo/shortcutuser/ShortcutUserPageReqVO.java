package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("业务中台 - 快捷入口用户关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShortcutUserPageReqVO extends PageParam {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "快捷入口id")
    private Integer shortcutId;

}
