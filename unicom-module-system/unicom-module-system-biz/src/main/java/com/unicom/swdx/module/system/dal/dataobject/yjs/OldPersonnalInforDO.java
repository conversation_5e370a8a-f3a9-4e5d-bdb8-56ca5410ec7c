package com.unicom.swdx.module.system.dal.dataobject.yjs;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("old_personnalinfor_base")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OldPersonnalInforDO {


    @JsonProperty("name")
    private String name;
    @JsonProperty("sex")
    private String sex;
    @JsonProperty("nation")
    private String nation;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("birthday")
    private String birthday;
    @JsonProperty("id_card")
    private String idCard;
    @JsonProperty("jiguan")
    private String jiguan;
    @JsonProperty("_rank")
    private String rank;
    @JsonProperty("culture")
    private String culture;
    @JsonProperty("zzmm")
    private String zzmm;
    @JsonProperty("post")
    private String post;
    @JsonProperty("work_time")
    private String workTime;
    @JsonProperty("id")
    private String id;

    private Long userId;

}
