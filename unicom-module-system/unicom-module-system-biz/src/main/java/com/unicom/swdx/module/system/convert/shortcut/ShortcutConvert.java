package com.unicom.swdx.module.system.convert.shortcut;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutExcelVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutRespVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.shortcut.ShortcutDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 快捷入口 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ShortcutConvert {

    ShortcutConvert INSTANCE = Mappers.getMapper(ShortcutConvert.class);

    ShortcutDO convert(ShortcutCreateReqVO bean);

    ShortcutDO convert(ShortcutUpdateReqVO bean);

    ShortcutRespVO convert(ShortcutDO bean);

    List<ShortcutRespVO> convertList(List<ShortcutDO> list);

    PageResult<ShortcutRespVO> convertPage(PageResult<ShortcutDO> page);

    List<ShortcutExcelVO> convertList02(List<ShortcutDO> list);

}
