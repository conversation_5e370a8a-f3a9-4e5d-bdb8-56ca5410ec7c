package com.unicom.swdx.module.system.dal.mysql.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantApprovalPageReqVO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantApprovalStatusRespVO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantChangePageRespVO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantApprovalDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 机构认证审批 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantApprovalMapper extends BaseMapperX<TenantApprovalDO> {


    List<TenantChangePageRespVO> selectChangePage(IPage page, @Param("param") Long loginUserId);

    TenantApprovalStatusRespVO selectApprovalStatus(@Param("param") Long linkedTenantId);

    TenantApprovalStatusRespVO selectTenantInfo(@Param("param") Long linkedTenantId);

    List<TenantChangePageRespVO> selectApprovalPage(IPage page, @Param("param") TenantApprovalPageReqVO tenantApprovalPageReqVO);

    @Select("select * from system_tenant_approval")
    List<TenantApprovalDO> selectAllList();

    @Update("update system_tenant_approval set LEGAL_REPRESENTATIVE_ID_CARD = #{param.legalRepresentativeIdCard} where id = #{param.id}")
    void updateAllById(@Param("param") TenantApprovalDO newT);
}
