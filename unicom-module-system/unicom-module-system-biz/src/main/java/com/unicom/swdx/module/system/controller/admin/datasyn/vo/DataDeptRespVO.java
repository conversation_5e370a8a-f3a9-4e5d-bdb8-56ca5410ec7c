package com.unicom.swdx.module.system.controller.admin.datasyn.vo;

import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("管理后台 - 部门信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DataDeptRespVO extends DeptBaseVO {

    @ApiModelProperty(value = "部门编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "创建时间", required = true, example = "时间戳格式")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "根组织标志（1/根组织，0/子组织）", required = true, example = "1")
    private Integer isRoot;

    @ApiModelProperty(value = "部门人数", required = true, example = "部门人数")
    private Long deptUserCounts;

    @ApiModelProperty("是否可编辑")
    private Boolean editable;

    @ApiModelProperty("是否可新增")
    private Boolean addable;

    @ApiModelProperty("机构id")
    private Long tenantId;


    @ApiModelProperty(value = "老业中父菜单id", example = "1024")
    private String oldparentId;

    @ApiModelProperty(value = "老业中菜单id", example = "1024")
    private String oldid;

}
