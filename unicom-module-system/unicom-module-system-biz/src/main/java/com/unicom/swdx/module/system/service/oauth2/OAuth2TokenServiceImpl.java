package com.unicom.swdx.module.system.service.oauth2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.unicom.swdx.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.oauth2.OAuth2AccessTokenMapper;
import com.unicom.swdx.module.system.dal.mysql.oauth2.OAuth2RefreshTokenMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.redis.oauth2.OAuth2AccessTokenRedisDAO;
import com.unicom.swdx.module.system.enums.oauth2.OAuth2ClientConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertSet;

/**
 * OAuth2.0 Token Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class OAuth2TokenServiceImpl implements OAuth2TokenService {

    @Resource
    private OAuth2AccessTokenMapper oauth2AccessTokenMapper;
    @Resource
    private OAuth2RefreshTokenMapper oauth2RefreshTokenMapper;

    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @Resource
    private OAuth2ClientService oauth2ClientService;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private TenantMapper tenantMapper;

    @Value("${unicom.access_token.expired_days:1}")
    private Long expiredDays;
//    @Value("${unicom.access_token.expired_Minutes:30}")
//    private Long expiredMinutes;

    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(Long userId, Integer userType, String clientId, List<String> scopes) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userId, userType, clientDO, scopes);
        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(Long userId, Integer userType, String clientId, List<String> scopes, String oldToken) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userId, userType, clientDO, scopes);
        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO, oldToken);
    }

    @Override
    public OAuth2AccessTokenDO createAppAccessToken(Long userId, Integer userType) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userId, userType, clientDO, null);
        // 创建访问令牌
        return createAppOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO refreshAccessToken(String refreshToken, String clientId) {
        // 查询访问令牌
        OAuth2RefreshTokenDO refreshTokenDO = oauth2RefreshTokenMapper.selectByRefreshToken(refreshToken);
        if (refreshTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }

        // 校验 Client 匹配
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        if (ObjectUtil.notEqual(clientId, refreshTokenDO.getClientId())) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "刷新令牌的客户端编号不正确");
        }

        // 移除相关的访问令牌
        List<OAuth2AccessTokenDO> accessTokenDOs = oauth2AccessTokenMapper.selectListByRefreshToken(refreshToken);
        if (CollUtil.isNotEmpty(accessTokenDOs)) {
            oauth2AccessTokenMapper.deleteBatchIds(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getId));
            oauth2AccessTokenRedisDAO.deleteList(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getAccessToken));
        }

        // 已过期的情况下，删除刷新令牌
        if (DateUtils.isExpired(refreshTokenDO.getExpiresTime())) {
            oauth2RefreshTokenMapper.deleteById(refreshTokenDO.getId());
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "刷新令牌已过期");
        }

        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO getAccessToken(String accessToken) {
        // 优先从 Redis 中获取
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenRedisDAO.get(accessToken);
        if (accessTokenDO != null) {
            return accessTokenDO;
        }

//        // 获取不到，从 MySQL 中获取
//        accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
//        // 如果在 MySQL 存在，则往 Redis 中写入
//        if (accessTokenDO != null && !DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
//            oauth2AccessTokenRedisDAO.set(accessTokenDO);
//        }
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO checkAccessToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO removeAccessToken(String accessToken) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (accessTokenDO == null) {
            return null;
        }
        oauth2AccessTokenMapper.deleteById(accessTokenDO.getId());
        oauth2AccessTokenRedisDAO.delete(accessToken);
        // 删除刷新令牌
        oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
        return accessTokenDO;
    }

    @Override
    public PageResult<OAuth2AccessTokenDO> getAccessTokenPage(OAuth2AccessTokenPageReqVO reqVO) {
        return oauth2AccessTokenMapper.selectPage(reqVO);
    }

    @Override
    public void removeAccessToken(Long tenantId) {
        List<OAuth2AccessTokenDO> list =  oauth2AccessTokenMapper.selectByTenantId(tenantId);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (OAuth2AccessTokenDO oAuth2AccessTokenDO : list) {
            removeAccessToken(oAuth2AccessTokenDO.getAccessToken());
        }
    }

    /**
     * 获取用户上次登录时间
     * @param userId 用户id
     * @return 上次登录时间
     */
    @Override
    public LocalDateTime getLoginLastTime(Long userId) {
        List<OAuth2AccessTokenDO> list = oauth2AccessTokenMapper.selectByUserIdLimit2(userId);
        if (CollUtil.isEmpty(list) || list.size() < 2) {
            return null;
        }
        return list.get(1).getCreateTime();
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO) {
        // 根据机构过期时间进行修正
        AdminUserDO userDO = adminUserMapper.selectById(refreshTokenDO.getUserId());
        clientDO.getAccessTokenValiditySeconds();
        // 转换为分钟
        int accessTokenValidityMinutes = clientDO.getAccessTokenValiditySeconds()/60;
        LocalDateTime expiresTime = LocalDateTime.now().plusMinutes(accessTokenValidityMinutes);
        // 统一到第N天凌晨2点下线
//        LocalDateTime expiresTime = LocalDate.now().plusDays(expiredDays).atTime(2,0,0);
        TenantDO tenantDO = tenantMapper.selectById(userDO.getTenantId());
//        if (expiresTime.isAfter(tenantDO.getExpireTime())) {
//            expiresTime = tenantDO.getExpireTime().toLocalDate().atTime(23,59,59);
//        }
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUserId(refreshTokenDO.getUserId()).setUserType(refreshTokenDO.getUserType())
                .setClientId(clientDO.getClientId()).setScopes(refreshTokenDO.getScopes())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setExpiresTime(expiresTime);
        accessTokenDO.setTenantId(userDO.getTenantId());
        oauth2AccessTokenMapper.insert(accessTokenDO);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(accessTokenDO);
        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO, String oldToken) {
        // 根据机构过期时间进行修正
        AdminUserDO userDO = adminUserMapper.selectById(refreshTokenDO.getUserId());
        clientDO.getAccessTokenValiditySeconds();
        // 转换为分钟
        int accessTokenValidityMinutes = clientDO.getAccessTokenValiditySeconds()/60;
        LocalDateTime expiresTime = LocalDateTime.now().plusMinutes(accessTokenValidityMinutes);
        // 统一到第N天凌晨2点下线
//        LocalDateTime expiresTime = LocalDate.now().plusDays(expiredDays).atTime(2,0,0);
        TenantDO tenantDO = tenantMapper.selectById(userDO.getTenantId());
//        if (expiresTime.isAfter(tenantDO.getExpireTime())) {
//            expiresTime = tenantDO.getExpireTime().toLocalDate().atTime(23,59,59);
//        }
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUserId(refreshTokenDO.getUserId()).setUserType(refreshTokenDO.getUserType())
                .setClientId(clientDO.getClientId()).setScopes(refreshTokenDO.getScopes())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setOldToken(oldToken)
                .setExpiresTime(expiresTime);
        accessTokenDO.setTenantId(userDO.getTenantId());
        oauth2AccessTokenMapper.insert(accessTokenDO);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(accessTokenDO);

        return accessTokenDO;
    }

    private OAuth2AccessTokenDO createAppOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO) {
        // 设置7天过期时间，
        // TODO 改为可配置
        LocalDateTime expiresTime = LocalDateTime.now().plusDays(7);
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                .setUserId(refreshTokenDO.getUserId()).setUserType(refreshTokenDO.getUserType())
                .setClientId(clientDO.getClientId()).setScopes(refreshTokenDO.getScopes())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setExpiresTime(expiresTime);
        accessTokenDO.setTenantId(0L);
        oauth2AccessTokenMapper.insert(accessTokenDO);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(accessTokenDO);
        return accessTokenDO;
    }

    private OAuth2RefreshTokenDO createOAuth2RefreshToken(Long userId, Integer userType, OAuth2ClientDO clientDO, List<String> scopes) {
        OAuth2RefreshTokenDO refreshToken = new OAuth2RefreshTokenDO().setRefreshToken(generateRefreshToken())
                .setUserId(userId).setUserType(userType)
                .setClientId(clientDO.getClientId()).setScopes(scopes)
                .setExpiresTime(LocalDateTime.now().plusSeconds(clientDO.getRefreshTokenValiditySeconds()));
        oauth2RefreshTokenMapper.insert(refreshToken);
        return refreshToken;
    }

    private static String generateAccessToken() {
        return IdUtil.fastSimpleUUID();
    }

    private static String generateRefreshToken() {
        return IdUtil.fastSimpleUUID();
    }

}
