package com.unicom.swdx.module.system.dal.dataobject.dept;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位表
 *
 * <AUTHOR>
 */
@TableName("system_post")
@KeySequence("system_post_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
//@Builder
@EqualsAndHashCode(callSuper = true)
public class PostDO extends BaseDO {

    /**
     * 岗位序号
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenant_id;
    /**
     * 岗位名称
     */
    @TableField(value = "post_name")
    private String name;
    /**
     * 岗位编码
     */
    private String code;
    /**
     * 岗位排序
     */
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

    /**
     * 是否展示
     */
    private Boolean displayState;

}
