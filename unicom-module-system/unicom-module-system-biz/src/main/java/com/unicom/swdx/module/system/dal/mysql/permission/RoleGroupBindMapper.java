package com.unicom.swdx.module.system.dal.mysql.permission;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupBindDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

@Mapper
public interface RoleGroupBindMapper extends BaseMapperX<RoleGroupBindDO> {
    Set<Long> selectRoleId(@Param("id") Long id);
}
