package com.unicom.swdx.module.system.service.businesscenter;


import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.businesscenter.dto.OAuth2UserInfosRespDTO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalRespVO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterUserSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserRespVO;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

/**
 * 业中远程调用 Service 接口
 */
public interface BusinessCenterService {

    /**
     * 获取业中部门列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    List<DeptSimpleRespVO> getDept(Long tenantId, Integer type, String token) throws URISyntaxException;

    /**
     * 获取某个用户在业中的部门权限列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    List<Long> getUserDeptPermissionByToken(Long tenantId, Integer type, String token) throws URISyntaxException;

    /** 获取某个用户在业中的部门权限列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param userId   用户ID
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    List<Long> getUserDeptPermissionByUserId(Long tenantId, Integer type, Long userId, String token) throws URISyntaxException;

    /**
     * 获取业中某个部门所有子子部门列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param deptId   部门编号
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    List<DeptSimpleRespVO> getDeptAllChildren(Long tenantId, Integer type, Long deptId, String token) throws URISyntaxException;

    /**
     * 获取业中用户详情
     *
     * @param id    用户id
     * @return 业中用户详情
     */
    UserRespVO getUserInfoByUserId(Long id) throws URISyntaxException;

    /**
     * 获取业中用户详情
     *
     * @param token 当前登录用户的token
     * @return 业中用户详情
     */
    OAuth2UserInfosRespDTO getUserInfoByToken(String token) throws URISyntaxException;

    /**
     * 获取业中用户列表
     *
     * @param param 参数
     * @param token 当前业中登录用户的token
     * @return 用户列表
     */
    List<BusinessCenterUserSimpleRespVO> getUser(Map<String, String> param, String token) throws URISyntaxException;

    String getOldToken(String tokenOfEdu);

    PageResult<BusinessCenterPersonnalSimpleRespVO> getPersonnalSimple(Long tenantId, Integer deptId, String username, String token, Integer pageNo, Integer pageSize) throws URISyntaxException;

    List<BusinessCenterPersonnalRespVO> getPersonnalList(Long tenantId, String token) throws URISyntaxException;

    BusinessCenterPersonnalRespVO getPersonnalById(Long id, String token) throws URISyntaxException;

    List<Long> getAdminInfo(Long roleId,String token) throws URISyntaxException;
}
