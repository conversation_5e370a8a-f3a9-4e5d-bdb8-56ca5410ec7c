package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppTodoListPageVO {
    @ApiModelProperty(value = "事项发起人", required = true)
    private String name;

    @ApiModelProperty(value = "待办理事项", required = true)
    private String task;

    @ApiModelProperty(value = "发起时间", required = true)
    private String initiationTime;

    @ApiModelProperty(value = "审批状态", required = true)
    private String status;

    @ApiModelProperty(value = "流程实例的编号", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类别", required = true)
    private String category;
}
