package com.unicom.swdx.module.system.controller.admin.message.vo.send;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@ApiModel("管理后台 - 消息发送 Response VO")
@Data
@ToString(callSuper = true)
public class MessageSendPageRespVO {

    @ApiModelProperty(value = "发送id", required = true, example = "1")
    private Long id;
    @ApiModelProperty(value = "发送时间")
    private LocalDateTime timedSendTime;

    @ApiModelProperty(value = "是否定时发送")
    private Boolean ifTimedSend;
    @ApiModelProperty(value = "系统id", required = true, example = "1024")
    private Long systemId;

    @ApiModelProperty(value = "模板名称", example = "测试体育俱乐部")
    private String templateName;

    @ApiModelProperty(value = "消息内容",example = "张三")
    private String messageContent;
    @ApiModelProperty(value = "模板内容",example = "张三")
    private String templateContent;

//    @TableField(typeHandler = JsonLongSetTypeHandler.class)
    @ApiModelProperty(value = "接收人员id")
    private Set<Long> receivingPersonIds;

//    @TableField(typeHandler = StringListTypeHandler.class)
    @ApiModelProperty(value = "接收人员姓名")
    private List<String> receivingPersonNames;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "通知公告发送成功条数")
    private Integer noticeSuccess;
    @ApiModelProperty(value = "短信发送成功条数")
    private Integer messageSuccess;

    @ApiModelProperty(value = "通知公告发送失败条数")
    private Integer noticeFail;
    @ApiModelProperty(value = "短信发送失败条数")
    private Integer messageFail;
    @ApiModelProperty(value = "通知公告待发送条数")
    private Integer noticeReady;
    @ApiModelProperty(value = "短信发送待发送条数")
    private Integer messageReady;

    @ApiModelProperty(value = "通告定时发送成功条数")
    private Integer successNotice;

    @ApiModelProperty(value = "通告定时发送失败条数")
    private Integer failNotice;
    @ApiModelProperty(value = "短信定时发送成功条数")
    private Integer successMessage;

    @ApiModelProperty(value = "短信定时发送失败条数")
    private Integer failMessage;


    @ApiModelProperty(value = "接收人员手机")
    private List<String> receivingMobile;

    private Integer sendMode;

    @ApiModelProperty(value = "发送人员姓名")
    private String nickname;




}
