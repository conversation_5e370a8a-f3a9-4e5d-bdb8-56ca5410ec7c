package com.unicom.swdx.module.system.mq.producer.sensitiveword;

import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import com.unicom.swdx.module.system.mq.producer.RefreshMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 敏感词相关的 Producer
 */
@Component
@Slf4j
public class SensitiveWordProducer extends AbstractProducer{

    @Async
    public void sendRefreshMessage() {
        log.info("[send][ SensitiveWord 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "SensitiveWordRefresh");
    }

}
