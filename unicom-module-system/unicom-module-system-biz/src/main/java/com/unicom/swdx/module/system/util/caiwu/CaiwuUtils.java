package com.unicom.swdx.module.system.util.caiwu;

import lombok.SneakyThrows;

import java.net.URLEncoder;

public class CaiwuUtils {

    static String publicKey="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNGxpC+uYkazbTORvZyxPgHsYgsIoIkuXzOPrwyHKFfF//3tfi8xhNMF9SFWGRrBWHNFQiaEf3RWGI4gZvEhjAgSnPu/u/PNNIVI+fTPHNIWSxvLZpgelMhvmGVFia9OZ4ujcupdIdLkPzYdHip1zEaCjlas1FzwZGyzYiqbE02wIDAQAB";
    static String MD5Key="R9i_AppServer_CB";
    public static void main(String[] args) throws Exception {
//        String md5 = MD5Utils.MD5Encoder(MD5Key + "", "UTF-8");
//        byte[] data = ("" + md5).getBytes();
//        //公钥加密
//        byte[] encodedData = RSAUtils.encryptByPublicKey(data, publicKey);
//        String yonghuKey = URLEncoder.encode(Base64Utils.encode(encodedData), "UTF-8");
////        System.out.println("http://************:8282/singleLogin?yonghuKey="+yonghuKey);
//        System.out.println("http://http://************:8386/singleLogin?yonghuKey="+yonghuKey);

        System.out.println(CaiwuUtils.getMobilePath("13974818666"));
    }



    @SneakyThrows
    public static String getPath(String mobile) {
        String md5 = MD5Utils.MD5Encoder(MD5Key + mobile, "UTF-8");
        byte[] data = (mobile + md5).getBytes();
        //公钥加密
        byte[] encodedData = RSAUtils.encryptByPublicKey(data, publicKey);
        String yonghuKey = URLEncoder.encode(Base64Utils.encode(encodedData), "UTF-8");
        return "http://************:8282/singleLogin?yonghuKey=" + yonghuKey;
    }

    @SneakyThrows
    public static String getMobilePath(String mobile) {
        String md5 = MD5Utils.MD5Encoder(MD5Key + mobile, "UTF-8");
        byte[] data = (mobile + md5).getBytes();
        //公钥加密
        byte[] encodedData = RSAUtils.encryptByPublicKey(data, publicKey);
        String yonghuKey = URLEncoder.encode(Base64Utils.encode(encodedData), "UTF-8");
        return "https://cwapp.hnswdx.gov.cn/singleLogin?yonghuKey=" + yonghuKey;
    }



}
