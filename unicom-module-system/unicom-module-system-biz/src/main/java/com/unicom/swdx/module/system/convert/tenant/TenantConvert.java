package com.unicom.swdx.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.tenant.dto.TenantInfoRespDTO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.*;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.enums.tenant.CompanyTypeEnum;
import com.unicom.swdx.module.system.service.region.RegionService;
import com.unicom.swdx.module.system.service.tenant.TenantTypeService;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 租户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConvert {

    TenantConvert INSTANCE = Mappers.getMapper(TenantConvert.class);

    RegionService regionService = SpringUtil.getBean(RegionService.class);

    TenantDO convert(TenantCreateReqVO bean);

    TenantDO convert(TenantUpdateReqVO bean);

    TenantDO convert(TenantRegisterReqVO bean);

    TenantRespVO convert(TenantDO bean);
    TenantRespGetVO convert01(TenantDO bean);

    List<TenantRespVO> convertList(List<TenantDO> list);

    PageResult<TenantRespVO> convertPage(PageResult<TenantDO> page);

    List<TenantSimpleRespVO> convertList01(List<TenantDO> simpleTenant);

    List<TenantExcelVO> convertList02(List<TenantDO> list);

    TenantDO convert(AdminAddTenantReqVO adminAddTenantReqVO);

    TenantDO convert(AdminSetTenantReqVO adminSetTenantReqVO);

    // 导出excel的封装
    default TenantExcelVO tenantDOToTenantExcelVO(TenantDO tenantDO) {
        if ( tenantDO == null ) {
            return null;
        }
        TenantExcelVO tenantExcelVO = new TenantExcelVO();
        tenantExcelVO.setId( tenantDO.getId() );
        tenantExcelVO.setName( tenantDO.getName() );
        tenantExcelVO.setContactNickname( tenantDO.getContactNickname() );
        tenantExcelVO.setContactMobile( tenantDO.getContactMobile() );
        tenantExcelVO.setContactName( tenantDO.getContactName() );
        tenantExcelVO.setCreateTime( tenantDO.getCreateTime() );
        tenantExcelVO.setStatus( tenantDO.getStatus() );
        return tenantExcelVO;
    }

    List<TenantInfoRespDTO> convertList03(List<TenantDO> list);

    List<TenantInfoRespDTO> convertList04(List<TenantDO> tenantDOS);

//    List<TenantInfoVO> convertList04(List<TenantDO> tenantListByIds);

}
