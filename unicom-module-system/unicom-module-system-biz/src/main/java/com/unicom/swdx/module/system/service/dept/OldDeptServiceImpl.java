package com.unicom.swdx.module.system.service.dept;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.dept.dto.OldDeptDTO;
import com.unicom.swdx.module.system.dal.dataobject.dept.OldDeptDO;
import com.unicom.swdx.module.system.dal.mysql.dept.OldDeptMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class OldDeptServiceImpl extends ServiceImpl<OldDeptMapper, OldDeptDO> implements OldDeptService{

    @Override
    public OldDeptDO getOldDeptByDeptId(Long deptId) {
        LambdaQueryWrapperX<OldDeptDO> wrapper = new LambdaQueryWrapperX<OldDeptDO>()
                .eq(OldDeptDO::getDeptId, deptId);
        OldDeptDO oldDept = getOne(wrapper);
        return oldDept;
    }

}
