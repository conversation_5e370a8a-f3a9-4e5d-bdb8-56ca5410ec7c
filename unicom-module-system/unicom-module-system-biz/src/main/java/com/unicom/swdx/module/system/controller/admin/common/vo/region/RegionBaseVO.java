package com.unicom.swdx.module.system.controller.admin.common.vo.region;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 地区 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class RegionBaseVO {

    @ApiModelProperty(value = "行政区划代码")
    private Integer code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "上级id")
    private Long parentId;

    @ApiModelProperty(value = "level")
    private Integer level;

}
