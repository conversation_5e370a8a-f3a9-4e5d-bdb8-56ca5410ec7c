package com.unicom.swdx.module.system.controller.admin.auth.vo;

import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@ApiModel(value = "管理后台 - 登录用户的权限信息 Response VO", description = "额外包括用户信息和角色列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthPermissionInfoRespVO {

    @ApiModelProperty(value = "用户信息", required = true)
    private UserVO user;

    @ApiModelProperty(value = "角色标识数组", required = true)
    private Set<String> roles;

    @ApiModelProperty(value = "操作权限标识数组", required = true)
    private Set<String> permissions;


    @ApiModel("用户信息 VO")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserVO {


        @ApiModelProperty(value = "判断是否为学生，教职工(0学生1教职工2学生和教职工3普通用户)")
        private Integer type;


        @ApiModelProperty(value = "用户编号", required = true, example = "1024")
        private Long id;

        @ApiModelProperty(value = "用户名", required = true, example = "unicom")
        private String username;

        @ApiModelProperty(value = "部门ID", required = false, example = "1024")
        private Long deptId;

        @ApiModelProperty(value = "部门名称", required = false, example = "1024")
        private String deptName;

        @ApiModelProperty(value = "用户昵称", required = true, example = "skdm")
        private String nickname;

        @ApiModelProperty(value = "用户头像", required = true, example = "http://www.iocoder.cn/xx.jpg")
        private String avatar;

        @ApiModelProperty(value = "用户性别", required = true, example = "1")
        private Integer sex;

        @ApiModelProperty(value = "根部门ID", required = false, example = "1024")
        private Long rootDeptId;

        @ApiModelProperty(value = "根部门名称", required = false, example = "1024")
        private String rootDeptName;

        @ApiModelProperty(value = "上次登录时间")
        private String loginLastTime;

        @ApiModelProperty(value = "机构id")
        private Long tenantId;

        @ApiModelProperty(value = "用户类型", notes = "0=超级管理员，1=机构管理员，2=普通用户")
        private Integer userRoleType;

        @ApiModelProperty(value = "手机号")
        private String mobile;

        private Set<Long> postIds;

        @ApiModelProperty(value = "多部门")
        private List<DeptDO> departments;


        private String othersystemid;

    }

}
