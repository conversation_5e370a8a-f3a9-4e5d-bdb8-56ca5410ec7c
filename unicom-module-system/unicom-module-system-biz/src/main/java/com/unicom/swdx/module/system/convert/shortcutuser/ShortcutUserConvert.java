package com.unicom.swdx.module.system.convert.shortcutuser;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserExcelVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserRespVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcutuser.ShortcutUserUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.shortcut.ShortcutUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 快捷入口用户关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ShortcutUserConvert {

    ShortcutUserConvert INSTANCE = Mappers.getMapper(ShortcutUserConvert.class);

    ShortcutUserDO convert(ShortcutUserCreateReqVO bean);

    ShortcutUserDO convert(ShortcutUserUpdateReqVO bean);

    ShortcutUserRespVO convert(ShortcutUserDO bean);

    List<ShortcutUserRespVO> convertList(List<ShortcutUserDO> list);

    PageResult<ShortcutUserRespVO> convertPage(PageResult<ShortcutUserDO> page);

    List<ShortcutUserExcelVO> convertList02(List<ShortcutUserDO> list);

}
