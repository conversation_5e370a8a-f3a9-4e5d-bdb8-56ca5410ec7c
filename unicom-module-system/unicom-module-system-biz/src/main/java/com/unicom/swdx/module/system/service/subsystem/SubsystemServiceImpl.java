package com.unicom.swdx.module.system.service.subsystem;

import cn.hutool.core.text.CharSequenceUtil;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.enums.VisibilityEnum;
import com.unicom.swdx.module.system.controller.admin.subsystem.vo.ClientRespVO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.util.caiwu.CaiwuUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 子系统 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SubsystemServiceImpl implements SubsystemService {

    @Resource
    private RoleService roleService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private OAuth2ClientService oAuth2ClientService;

    @Resource
    private AdminUserService userService;

    @Override
    public List<ClientRespVO> getSubsystemList(Long userId,List<Integer> visibility) {
        List<ClientRespVO> result = new ArrayList<>();
        if (permissionService.isSuperAdmin(userId)) {
            Collection<OAuth2ClientDO> all = oAuth2ClientService.getEnableListFromCache();

            all.forEach(c -> {
                if(visibility.contains(c.getVisibility())){
                result.add(ClientRespVO.builder()
                        .id(c.getId())
                        .code(c.getCode())
                        .logo(c.getLogo())
                        .path(c.getPath())
                        .name(c.getName())
                        .sort(c.getSort())
                        .build());
            }
            });
            dealResult(result, userId);
            return result;
        }
        Set<Long> roleIds = permissionService.getAllEnableRoleIdListFromCache(userId);
        List<RoleDO> roles = roleService.getRolesByIds(roleIds);
        Set<Long> clientIds = roles.stream().filter(Objects::nonNull).filter(roleDO -> Objects.equals(CommonStatusEnum.ENABLE.getStatus(), roleDO.getStatus()))
                .map(RoleDO::getClientId).collect(Collectors.toSet());


        clientIds.forEach(id -> {
            OAuth2ClientDO clientDO = oAuth2ClientService.getFromCache(id);
            if (Objects.equals(CommonStatusEnum.ENABLE.getStatus(), clientDO.getStatus())
            && visibility.contains(clientDO.getVisibility())) {
                result.add(ClientRespVO.builder()
                        .id(id)
                        .code(clientDO.getCode())
                        .logo(clientDO.getLogo())
                        .path(clientDO.getPath())
                        .name(clientDO.getName())
                        .sort(clientDO.getSort())
                        .build());
            }
        });
//
        dealResult(result, userId);
        return result;
    }

    private void dealResult(List<ClientRespVO> result, Long userId) {
        result.forEach(c -> {
            // 特殊处理财务系统
            if (CharSequenceUtil.equals("chaiwu", c.getCode())) {
                c.setPath(CaiwuUtils.getPath(userService.getUser(userId).getMobile()));
            }

            if (CharSequenceUtil.equals("caiwumobile", c.getCode())) {
                c.setPath(CaiwuUtils.getMobilePath(userService.getUser(userId).getMobile()));
            }
        });
        result.sort(Comparator.comparing(ClientRespVO::getSort));
    }
}
