package com.unicom.swdx.module.system.mq.producer.tenant;

import com.alibaba.fastjson.JSON;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaDeptDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaHeaderDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaTenantDTO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.OldDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.enums.kafka.dept.DeptEventType;
import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import com.unicom.swdx.module.system.service.dept.DeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

import static com.unicom.swdx.module.system.enums.kafka.dept.DeptEventType.*;
import static com.unicom.swdx.module.system.enums.kafka.dept.DeptEventType.delTenantEventType;

/**
 * 机构相关消息的 Producer
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@Slf4j
@Component
public class TenantProducer extends AbstractProducer {

    @Value("${sendTopic.oldKafka}")
    private String oldKafkaTopic;

    @Value("${sendTopic.newKafka}")
    private String newKafkaTopic;

    @Async
    public void sendTenantRefreshMessage() {
        log.info("[send][ Tenant 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "TenantRefresh");
    }

    @Async
    public void sendTenantData(String eventType, TenantDO tenant, Long deptId) {
        log.info("[send][ 更改机构信息成功，向kafka发送机构更新数据 ]");

        OldKafkaHeaderDTO header = new OldKafkaHeaderDTO();
        OldKafkaTenantDTO body = new OldKafkaTenantDTO();
        OldKafkaMessageDTO message = new OldKafkaMessageDTO();

        header.setSender("004");
        header.setEventId(UUID.randomUUID().toString());
        header.setEventType(eventType);
        header.setTimestamp(System.currentTimeMillis());
        header.setTenant_code(tenant.getTenantCode());

        if(Objects.equals(eventType, addTenantEventType)){
            //新增机构
            body.setId(tenant.getId());
            body.setCompanyName(tenant.getName());
            body.setContactPhone(tenant.getContactMobile());
            body.setAddress(tenant.getLocationAddress());
            body.setAdmin_id(tenant.getContactUserId());
            body.setContactUserName(tenant.getContactNickname());
            body.setPassword(tenant.getContactPassword());
            body.setUsername(tenant.getContactName());
            body.setDept_num("0");
            body.setNew_parent_id(0L);
            body.setNew_dept_id(deptId);
            body.setDept_name(tenant.getName());
            body.setStatus(tenant.getStatus());

        } else if (Objects.equals(eventType, delTenantEventType)) {
            //删除机构
            body.setId(tenant.getId());

        } else if (Objects.equals(eventType, editTenantEventType)) {
            //修改机构
            body.setId(tenant.getId());
            body.setCompanyName(tenant.getName());
            body.setContactPhone(tenant.getContactMobile());
            body.setAddress(tenant.getLocationAddress());
            body.setContactUserName(tenant.getContactNickname());
//            body.setAdmin_id(tenant.getContactUserId());
//            body.setPassword(tenant.getContactPassword());
//            body.setUsername(tenant.getContactName());
            body.setDept_num("0");
//            body.setNew_parent_id(0L);
            body.setNew_dept_id(deptId);
            body.setDept_name(tenant.getName());
            body.setStatus(tenant.getStatus());
        }

        message.setHeader(header);
        message.setBody(body);

        log.info("打印----------"+ JSON.toJSONString(message)+"---------------");

        kafkaTemplate.send(newKafkaTopic, JSON.toJSONString(message));
    }

}
