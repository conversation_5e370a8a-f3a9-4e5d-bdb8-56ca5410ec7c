package com.unicom.swdx.module.system.controller.admin.dept.vo.dept;

import lombok.Data;

import java.util.List;

/**
 * 部门 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DeptTreeVO {

    private Long id;

    private String name;

    private Long parentId;

    private Integer sort;

    /**
     * 节点是否可选，默认true
     */
    private Boolean isSelectable = true;

    private List<DeptTreeVO> children;

    /**
     * 是否叶子节点 true 是 false 否
     */
    private Boolean user;
}
