package com.unicom.swdx.module.system.controller.admin.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "教务系统 - 单点登录 Request VO", description = "")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingleSignOnReqVO {

    @ApiModelProperty(value = "用户id", required = false, example = "26")
    private String id;

    @ApiModelProperty(value = "employeeId", required = false, example = "12345678910")
    private String employeeId;

    @ApiModelProperty(value = "业中token", required = false, example = "a4b1f40b2517462d9a8a8aac1398fec5")
    private String token;
}
