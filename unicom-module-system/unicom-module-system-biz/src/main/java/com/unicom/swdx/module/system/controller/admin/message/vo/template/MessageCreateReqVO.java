package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@ApiModel("管理后台 - 消息模板新增 Request VO")
@Data
public class MessageCreateReqVO {
    @ApiModelProperty(value = "系统id",required = true,example = "1")
    @NotNull(message = "系统id不能为空")
    private Long systemId;

    @ApiModelProperty(value = "模板名称", required = true,example = "skyline")
    @NotNull(message = "模板名称不能为空")
    @Size(min = 0, max = 30, message = "模板名称限制30字")
    private String name;

    @ApiModelProperty(value = "模板内容", required = true,example = "skyline")
    @NotNull(message = "模板内容不能为空")
    @Size(min = 0, max = 1000, message = "模板内容限制1000字")
    private String content;

    @ApiModelProperty(value = "备注",example = "skyline")

    @Size(min = 0, max = 1000, message = "备注限制1000字")
    private String remark;

}
