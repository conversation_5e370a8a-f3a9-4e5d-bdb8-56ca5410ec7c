package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;


import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 获取申请记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantApprovalPageReqVO extends PageParam {

    @ApiModelProperty(value = "机构全称")
    private String tenantName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String unifiedSocialCreditCode;

    @ApiModelProperty(value = "法定代表人/户主姓名")
    private String legalRepresentativeName;

    @ApiModelProperty(value = "单位类型")
    private Integer companyType;

    @ApiModelProperty(value = "认证类型")
    private Integer approvalType;

    @ApiModelProperty(value = "审批状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "机构类型", example = "1")
    private Integer tenantType;

}
