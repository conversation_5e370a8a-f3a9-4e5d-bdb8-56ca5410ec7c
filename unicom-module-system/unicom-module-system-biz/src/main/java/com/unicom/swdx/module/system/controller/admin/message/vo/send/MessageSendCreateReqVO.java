package com.unicom.swdx.module.system.controller.admin.message.vo.send;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 消息发送新增 Request VO")
@Data
public class MessageSendCreateReqVO {
    @ApiModelProperty(value = "设置定时发送",required = true)
    @NotNull(message = "设置定时发送不能为空")
    private Boolean ifTimedSend;

    @ApiModelProperty(value = "定时发送时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime timedSendTime;

    @ApiModelProperty(value = "发送方式",required = true)
    @NotNull(message = "发送方式不能为空")
    private Integer sendMode;

    @ApiModelProperty(value = "接收人员id")
    private Set<Long> receivingPersonIds;


    @ApiModelProperty(value = "接收电话号码")
    private List<String> receivingMobile;

    @ApiModelProperty(value = "发送内容方式")
    @NotNull(message = "发送内容方式不能为空")
    private Integer sendContentMode;

    @ApiModelProperty(value = "模板名称id")
    private Integer templateId;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板内容")
    private String templateContent;

    @ApiModelProperty(value = "消息内容")
    @Size(min = 0, max = 1000, message = "模板内容限制1000字")
    private String messageContent;

    @ApiModelProperty(value = "短信发送渠道")
    private Boolean messageChannel;

    @ApiModelProperty(value = "通知公告发送渠道")
    private Boolean noticeChannel;

    @ApiModelProperty(value = "系统id")
    private Long systemId;
}
