package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/25 11:21
 **/
@Data
public class TenantInfoVO {

    @ApiModelProperty(value = "租户编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "机构类型", required = true, example = "1")
    private Integer tenantType;

    @ApiModelProperty(value = "机构级别", required = false, example = "1")
    private Integer tenantLevel;

    @ApiModelProperty(value = "范围", example = "[10000216,10002038]")
    private List<Integer> levelRange;

    @ApiModelProperty(value = "业务指导单位")
    private Integer guideTenantId;

    @ApiModelProperty(value = "单位类型", example = "1")
    private Integer companyType;

    @ApiModelProperty(value = "机构全称", example = "测试体育俱乐部")
    private String name;

    @ApiModelProperty(value = "统一社会信用代码",example = "sport4541210144")
    private String unifiedSocialCreditCode;

//    @ApiModelProperty(value = "注册地区划")
//    private List<Long> registerRegion;

    @ApiModelProperty(value = "注册地区划 行政区划代码")
    private List<Integer> registerRegionCode;

    @ApiModelProperty(value = "注册地地址",example = "湖南长沙")
    private String registerAddress;

//    @ApiModelProperty(value = "所在地区划")
//    private List<Long> locationRegion;

    @ApiModelProperty(value = "所在地区划 行政区划代码")
    private List<Integer> locationRegionCode;

    @ApiModelProperty(value = "所在地地址",example = "湖南长沙")
    private String locationAddress;

    @ApiModelProperty(value = "法定代表人姓名",example = "张三")
    private String legalRepresentativeName;

    @ApiModelProperty(value = "法人代表身份证号码",example = "****************")
    private String legalRepresentativeIdCard;

    @ApiModelProperty(value = "机构用户类型", example = "1")
    private List<Long> instUserType;

    @ApiModelProperty(value = "营业执照", example = "soccer.club/index")
    private String businessLicenseUrl;

    @ApiModelProperty(value = "机构管理人姓名",example = "张三")
    private String contactNickname;

    @ApiModelProperty(value = "机构管理员手机号码（联系手机）",example = "***********")
    private String contactMobile;

    @ApiModelProperty(value = "用户名（原联系人）",example = "skyline")
    private String contactName;

    @ApiModelProperty(value = "机构状态",example = "1")
    private Integer status;

    @ApiModelProperty(value = "过期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

}
