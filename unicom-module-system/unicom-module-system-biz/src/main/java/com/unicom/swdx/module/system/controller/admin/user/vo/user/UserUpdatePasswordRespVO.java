package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/7 9:22
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserUpdatePasswordRespVO {

    @ApiModelProperty("用户id")
    private Long id;

    @ApiModelProperty(value = "uuid", notes = "用作修改密码接口携带的唯一标识")
    private String uuid;

}
