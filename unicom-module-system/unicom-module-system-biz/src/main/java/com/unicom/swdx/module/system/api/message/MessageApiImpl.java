package com.unicom.swdx.module.system.api.message;

import cn.hutool.core.bean.BeanUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.module.system.api.message.dto.MessageAuthorityUpdateReqDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserReqDTO;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import com.unicom.swdx.module.system.api.user.dto.OldPersonDTO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityUpdateReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdateReqVO;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.OldPersonDO;
import com.unicom.swdx.module.system.service.message.MessageAuthorityService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.service.user.OldPersonService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class MessageApiImpl implements MessageApi {

    @Resource
    private AdminUserService userService;
    @Resource
    private MessageAuthorityService messageAuthorityService;

    @Resource
    private OldPersonService oldPersonService;

    @DataPermission(enable = false)
    @Override
    public Long getMessage(Long userId) {
        Long Mid = messageAuthorityService.getMessageAuthorityId(userId);
        return Mid;
    }
    @DataPermission(enable = false)
    @Override
    public CommonResult<Boolean> updateMessage(MessageAuthorityUpdateReqDTO updateReqDTO) {
        MessageAuthorityUpdateReqVO updateReqVO = new MessageAuthorityUpdateReqVO();
        updateReqVO.setId(updateReqDTO.getId());
        updateReqVO.setStatus(updateReqDTO.getStatus());
        messageAuthorityService.updateMessageAuthority(updateReqVO);
        return success(true);
    }
}
