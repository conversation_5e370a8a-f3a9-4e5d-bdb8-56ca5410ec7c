package com.unicom.swdx.module.system.dal.mysql.dept;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserPostDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UserPostMapper extends BaseMapperX<UserPostDO> {

    default List<UserPostDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<UserPostDO>()
                .eq(UserPostDO::getUserId, userId));
    }

    /**
     * 通过岗位标识查找用户
     * @param postCodes
     * @param tenantId
     * @return
     */
    List<Long> selectUsersByPostCode(@Param("postCodes") List<String> postCodes,@Param("tenantId") Long tenantId);

    /**
     * 通过岗位标识、部门id查找用户
     * @param postCodes
     * @param tenantId
     * @return
     */
    List<Long> selectUsersByPostDeptCode(@Param("postCodes") List<String> postCodes,@Param("tenantId") Long tenantId
            ,@Param("deptId") Long deptId);


    default void deleteByUserIdAndPostId(Long userId, Collection<Long> postIds) {
        delete(new LambdaQueryWrapperX<UserPostDO>()
                .eq(UserPostDO::getUserId, userId)
                .in(UserPostDO::getPostId, postIds));
    }

    default List<UserPostDO> selectListByPostIds(Collection<Long> postIds) {
        return selectList(new LambdaQueryWrapperX<UserPostDO>()
                .in(UserPostDO::getPostId, postIds));
    }

    default void deleteByUserId(Long userId){
        delete(Wrappers.lambdaUpdate(UserPostDO.class).eq(UserPostDO::getUserId, userId));
    }
}
