package com.unicom.swdx.module.system.job.calendarwnl;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:42
 **/
@Data
public class CalendarRemoteDTO {

    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 日
     */
    private List<CalendarDay> days;

    @Data
    public class CalendarDay {
        /**
         * 日期
         */
        private String date;

        /**
         * 当前周第几天 1-周一 2-周二 ... 7-周日
         */
        private Integer weekDay;

        /**
         * 天干地支纪年法描述 例如：戊戌
         */
        private String yearTips;

        /**
         * 类型 0 工作日 1 假日 2 节假日
         */
        private Integer type;

        /**
         * 类型描述 比如 国庆,休息日,工作日
         */
        private String typeDes;

        /**
         * 属相 例如：狗
         */
        private String chineseZodiac;

        /**
         * 节气描述 例如：小雪
         */
        private String solarTerms;

        /**
         * 农历日期
         */
        private String lunarCalendar;

        /**
         * 宜事项
         */
        private String suit;

        /**
         * 	这一年的第几天
         */
        private Integer dayOfYear;

        /**
         * 这一年的第几周
         */
        private Integer weekOfYear;

        /**
         * 	星座
         */
        private String constellation;

        /**
         * 如果当前是工作日 则返回是当前月的第几个工作日，否则返回0
         */
        private Integer indexWorkDayOfMonth;

    }


}
