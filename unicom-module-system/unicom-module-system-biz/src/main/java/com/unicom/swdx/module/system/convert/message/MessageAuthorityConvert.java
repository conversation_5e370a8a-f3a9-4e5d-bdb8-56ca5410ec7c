package com.unicom.swdx.module.system.convert.message;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.*;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MessageAuthorityConvert {

    MessageAuthorityConvert INSTANCE = Mappers.getMapper(MessageAuthorityConvert.class);

    MessageAuthorityDO convert(MessageAuthorityCreateReqVO bean);
    MessageAuthorityDO convert(MessageAuthorityUpdateReqVO bean);
//    PageResult<MessagePageRespVO> convertPage(PageResult<MessageDO> page);
//    MessageRespGetVO convert01(MessageDO bean);
}
