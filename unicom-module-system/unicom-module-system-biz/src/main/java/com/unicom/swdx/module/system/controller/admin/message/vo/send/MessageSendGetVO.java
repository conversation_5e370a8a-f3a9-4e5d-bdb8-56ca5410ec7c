package com.unicom.swdx.module.system.controller.admin.message.vo.send;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 消息发送详情 Request VO")
@Data
@ToString(callSuper = true)
public class MessageSendGetVO  {
    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "总数")
    private Integer total;

    @ApiModelProperty(value = "发送id", example = "")
    private List<String> successReceiver;

//    @ApiModelProperty(value = "发送id", example = "")
//    private List<String> successPhone;

    @ApiModelProperty(value = "成功总数")
    private Integer successTotal;

    @ApiModelProperty(value = "发送id", example = "")
    private List<String> failReceiver;

//    @ApiModelProperty(value = "失败发送手机号", example = "")
//    private List<String> failPhone;

    @ApiModelProperty(value = "失败总数")
    private Integer failTotal;

    @ApiModelProperty(value = "发送id", example = "")
    private List<String> unsentReceiver;

//    @ApiModelProperty(value = "失败发送手机号", example = "")
//    private List<String> failPhone;

    @ApiModelProperty(value = "失败总数")
    private Integer unsentTotal;

}
