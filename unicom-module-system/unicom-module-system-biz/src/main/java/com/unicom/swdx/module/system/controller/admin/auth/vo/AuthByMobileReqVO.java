package com.unicom.swdx.module.system.controller.admin.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@ApiModel(value = "教务系统 - 单点登录 Request VO", description = "")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthByMobileReqVO {

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    @NotNull(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳", required = true)
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;
}
