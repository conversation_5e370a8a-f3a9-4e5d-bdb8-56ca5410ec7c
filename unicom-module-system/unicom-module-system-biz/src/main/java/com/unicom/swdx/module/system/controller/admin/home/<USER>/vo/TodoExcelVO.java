package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 待办事项 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TodoExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("类型（见枚举）")
    private Integer type;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("发起人")
    private String submitter;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("发起时间")
    private Date submitTime;

    @ExcelProperty("发起子系统id")
    private Integer subsystemId;

    @ExcelProperty("跳转链接")
    private String linkUrl;

    @ExcelProperty("待办人")
    private String todoUserId;

    @ExcelProperty("状态（0=待办，1=已办）")
    private Integer status;

    @ExcelProperty("创建时间")
    private Date createTime;

}
