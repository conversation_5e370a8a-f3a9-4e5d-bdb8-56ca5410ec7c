package com.unicom.swdx.module.system.mq.producer.tenant;

import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 机构用户类型相关消息的 Producer
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@Slf4j
@Component
public class TenantTypeProducer extends AbstractProducer{

    @Async
    public void sendTenantTypeRefreshMessage() {
        log.info("[send][ TenantType 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "TenantTypeRefresh");
    }

}
