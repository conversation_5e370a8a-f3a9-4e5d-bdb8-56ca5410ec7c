package com.unicom.swdx.module.system.controller.admin.auth;

import com.unicom.swdx.module.system.controller.admin.auth.vo.SsoAccessTokenRespVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.SsoUserAttributes;
import com.unicom.swdx.module.system.controller.admin.auth.vo.SsoUserProfileRespVO;
import com.unicom.swdx.module.system.controller.admin.oauth2.OAuth2OpenController;
import com.unicom.swdx.module.system.controller.admin.oauth2.vo.open.OAuth2OpenAccessTokenRespVO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.service.oauth2.OAuth2TokenService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@RestController
@RequestMapping("/sso/oauth2.0")
@Slf4j
public class SsoController {

    @Resource
    private OAuth2OpenController openController;

    @Resource
    private OAuth2TokenService oauth2TokenService;

    @Resource
    private AdminUserService userService;

    @RequestMapping(value = "/accessToken",method = {RequestMethod.GET,RequestMethod.POST})
    @PermitAll
    public SsoAccessTokenRespVO accessToken(HttpServletRequest request,
                                            @RequestParam("grant_type") String grantType,
                                            @RequestParam(value = "code", required = false) String code, // 授权码模式
                                            @RequestParam(value = "redirect_uri", required = false) String redirectUri, // 授权码模式
                                            @RequestParam(value = "state", required = false) String state, // 授权码模式
                                            @RequestParam(value = "username", required = false) String username, // 密码模式
                                            @RequestParam(value = "password", required = false) String password, // 密码模式
                                            @RequestParam(value = "scope", required = false) String scope, // 密码模式
                                            @RequestParam(value = "refresh_token", required = false) String refreshToken) {
        OAuth2OpenAccessTokenRespVO accessTokenRespVO = openController.postAccessToken(request, grantType, code, redirectUri, state, username, password, scope, refreshToken).getCheckedData();
        SsoAccessTokenRespVO result = new SsoAccessTokenRespVO();
        if (Objects.nonNull(accessTokenRespVO)) {
            result.setCode("0");
            result.setAccess_token(accessTokenRespVO.getAccessToken());
            result.setMsg("SUCCESS");
            result.setExpires(accessTokenRespVO.getExpiresIn());
            result.setStatus(200);
        }
        log.info("sso认证：code = {}",code);
        return result;
    }

    @RequestMapping(value = "/profile",method = {RequestMethod.GET,RequestMethod.POST})
    @PermitAll
    public SsoUserProfileRespVO getUserProfile(@RequestParam("access_token") String access_token) {
        log.info("sso token校验：{}",access_token);
        // 校验令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.checkAccessToken(access_token);
        AdminUserDO user = userService.getUser(accessTokenDO.getUserId());
        SsoUserProfileRespVO result = new SsoUserProfileRespVO();
        result.setCode("0");
        result.setStatus(200);
        result.setMsg("SUCCESS");
        result.setId(user.getMobile());
        SsoUserAttributes attributes = new SsoUserAttributes();
        attributes.setId(user.getId());
        attributes.setMobile(user.getMobile());
        attributes.setLoginid(user.getMobile());
        attributes.setWorkcode(user.getOthersystemid());
        attributes.setWxxcxOpenid(user.getWxxcxOpenid());
        result.setAttributes(attributes);
        return result;
    }

}
