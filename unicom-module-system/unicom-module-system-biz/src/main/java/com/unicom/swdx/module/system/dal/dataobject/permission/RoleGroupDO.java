package com.unicom.swdx.module.system.dal.dataobject.permission;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.type.JsonLongSetTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.system.enums.permission.DataScopeEnum;
import com.unicom.swdx.module.system.enums.permission.RoleTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

@TableName(value = "system_role_group", autoResultMap = true)
@KeySequence("system_role_group_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleGroupDO extends TenantBaseDO {

    /**
     * 角色ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 角色组名称
     */
    private String name;

    /**
     * 角色组排序
     */
    private Integer sort;

    /**
     * 角色组状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

}
