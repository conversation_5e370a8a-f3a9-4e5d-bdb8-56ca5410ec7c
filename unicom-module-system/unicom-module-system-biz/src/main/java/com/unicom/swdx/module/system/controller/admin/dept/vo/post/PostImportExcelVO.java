package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class PostImportExcelVO {

    @ExcelProperty(value = "岗位名称\n（必填）")
    @NotNull(message = "岗位名称不能为空")
    @Size(max = 30, message = "岗位名称长度不能超过30个字符")
    private String name;

    @ExcelProperty(value = "岗位标识\n（必填）")
    @NotNull(message = "岗位标识不能为空")
    @Size(max = 30, message = "岗位标识长度不能超过30个字符")
    private String code;

    @ExcelProperty(value = "备注")
    @Size(max = 50, message = "岗位标识长度不能超过50个字符")
    private String remark;

}
