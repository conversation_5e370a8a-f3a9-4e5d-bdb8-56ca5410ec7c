package com.unicom.swdx.module.system.controller.xcx.auth;

import cn.hutool.http.HttpUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginReqVO;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginRespVO;
import com.unicom.swdx.module.system.service.auth.AdminAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.OPENID_NOT_BIND;

/**
 * <AUTHOR>
 * @date 2024/1/23 22:11
 **/
@Api(tags = "WXXCX - 认证")
@RestController
@RequestMapping("/system/xcx/auth")
@Validated
@Slf4j
public class WxXcxAuthController {

    @Resource
    private AdminAuthService authService;

    @PostMapping("/openid-login")
    @PermitAll
    @ApiOperation("小程序用户openid登录")
    public CommonResult<WxXcxAuthLoginRespVO> openidLogin(@RequestBody @Valid WxXcxAuthLoginReqVO reqVO) {
        log.info(reqVO.toString());
        WxXcxAuthLoginRespVO respVO = authService.openidLogin(reqVO);
        if (Objects.isNull(respVO)) {
            return error(OPENID_NOT_BIND);
        }
        return success(respVO);
    }

    @PostMapping("/mobile-login")
    @PermitAll
    @ApiOperation("小程序用户手机号登录")
    public CommonResult<WxXcxAuthLoginRespVO> mobileLogin(@RequestBody @Valid WxXcxAuthLoginReqVO reqVO) {
        log.info(reqVO.toString());
        return success(authService.mobileLogin(reqVO));
    }

}
