package com.unicom.swdx.module.system.dal.dataobject.user;


import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class UserIdentityVO {

    private Long userId;

    private String name ;

    private String code ;

    private Integer use ;

    public static void main(String[] args) {
        UserIdentityVO userIdentityVO = new UserIdentityVO();

        userIdentityVO.setUserId(1l);

        UserIdentityVO userIdentityVO1 = new UserIdentityVO();

        userIdentityVO1.setUserId(1l);

        Set<UserIdentityVO> list = new HashSet<>();

        list.add(userIdentityVO);
        list.add(userIdentityVO1);

        System.out.println(list);


    }
}
