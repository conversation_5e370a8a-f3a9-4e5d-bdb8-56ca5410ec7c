package com.unicom.swdx.module.system.service.oaNotice;

import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.*;
import com.unicom.swdx.module.system.convert.oaNotice.OaNoticeConvert;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.NoticeReadRecordMapper;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.OaNoticeMapper;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertSet;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.NOTICE_NOT_FOUND;

/**
 * OA系统通知公告 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class OaNoticeServiceImpl implements OaNoticeService {

    @Resource
    private OaNoticeMapper oaNoticeMapper;

    @Resource
    private NoticeReadRecordMapper noticeReadRecordMapper;

    @Resource
    private AdminUserService userService;
    @Override
    public Long createNotice(OaNoticeCreateReqVO reqVO) {
        //直接用登陆人的用户id而不是前端传的
        AdminUserDO user = userService.getUser(getLoginUserId());
        //是否发布到招聘网站
        if (reqVO.getIsRecruit() != null && reqVO.getIsRecruit() == 1) {
            reqVO.setType(3);
        }
        reqVO.setCreatorId(user.getId());

        if(reqVO.getRecipientId() != null && reqVO.getRecipientId().equals("|100|")){
            reqVO.setType(2);
        }

        OaNoticeDO notice = OaNoticeConvert.INSTANCE.convert(reqVO);
        notice.setTenantId(SecurityFrameworkUtils.getTenantId());
        if (notice.getId() != null) {
            oaNoticeMapper.updateById(notice);
        } else {
            oaNoticeMapper.insert(notice);
        }
        return notice.getId();
    }

    @Override
    public void updateNotice(OaNoticeUpdateReqVO reqVO) {
        // 校验是否存在
        this.checkNoticeExists(reqVO.getId());

        //是否发布到招聘网站
        if (reqVO.getIsRecruit() != null && reqVO.getIsRecruit() == 1) {
            reqVO.setType(3);
        }

        // 更新通知公告
        OaNoticeDO updateObj = OaNoticeConvert.INSTANCE.convert(reqVO);
        oaNoticeMapper.updateById(updateObj);
    }

    @Override
    public void updatePublishNotice(OaNoticeUpdateReqVO reqVO) {
        // 校验是否存在
        this.checkNoticeExists(reqVO.getId());

        //更新创建时间
        reqVO.setCreateTime(LocalDateTime.now());

        //是否发布到招聘网站
        if (reqVO.getIsRecruit() != null && reqVO.getIsRecruit() == 1) {
            reqVO.setType(3);
        }

        // 更新通知公告
        OaNoticeDO updateObj = OaNoticeConvert.INSTANCE.convert(reqVO);
        //阅读数记为0
        updateObj.setReadNumber(0L);
        oaNoticeMapper.updateById(updateObj);

        //清空之前的阅读记录
        noticeReadRecordMapper.emptyReadRecord(reqVO.getId());

    }

    @Override
    public void deleteNotice(Long id) {
        // 校验是否存在
        this.checkNoticeExists(id);
        // 删除通知公告
        oaNoticeMapper.deleteById(id);
    }

    @Override
    public PageResult<OaNoticeDO> pageNotices(OaNoticePageReqVO reqVO) {
        // 处理基于用户昵称的查询
        Collection<Long> userIds = null;
        if (CharSequenceUtil.isNotBlank(reqVO.getTitle())) {
            userIds = convertSet(userService.getUsersByNickname(reqVO.getTitle()), AdminUserDO::getId);
        }
        // 查询分页
        return oaNoticeMapper.selectPage(reqVO, userIds);

    }

    @Override
    public PageResult<OaNoticeRespVO> pagePublishedNotices(OaNoticePageReqVO reqVO) {
        Long userId;
        //只能看到自己发的
        if (reqVO.getType()!= null && reqVO.getType() != 3) {
            userId = getLoginUserId();
        } else {
            userId = null;
        }

        // 处理基于用户昵称的查询
        Collection<Long> userIds = null;
        if(reqVO.getTitle()!=null) {
            if (CharSequenceUtil.isNotBlank(reqVO.getTitle())) {
                userIds = convertSet(userService.getUsersByNickname(reqVO.getTitle()), AdminUserDO::getId);
            }
        }

        PageResult<OaNoticeRespVO> result=new PageResult<>();
        List<OaNoticeRespVO> respVOList;
        if(userIds!=null) {
            respVOList = oaNoticeMapper.selectPageByStatusList(reqVO, userIds, 1, userId, SecurityFrameworkUtils.getTenantId());
            result.setTotal(oaNoticeMapper.selectPageByStatusNum(reqVO, userIds,1,userId,SecurityFrameworkUtils.getTenantId()));
        }
        else {
            respVOList = oaNoticeMapper.selectPageByStatusUserIdList(reqVO, 1, userId,SecurityFrameworkUtils.getTenantId());
            result.setTotal(oaNoticeMapper.selectPageByStatusUserIdNum(reqVO,1,userId,SecurityFrameworkUtils.getTenantId()));
        }
        for(OaNoticeRespVO a:respVOList){
            //富文本太多了
            a.setContent(null);
            //文件路径要隐藏
            a.setFileUrl(null);
        }
        result.setList(respVOList);
        // 查询分页
        return result;

    }

    @Override
    public PageResult<OaNoticeRespVO> pageDraftNotices(OaNoticePageReqVO reqVO) {
        //只能看到自己发的
        Long userId = getLoginUserId();
        // 处理基于用户昵称的查询
        Collection<Long> userIds = null;
        if (CharSequenceUtil.isNotBlank(reqVO.getTitle())) {
            userIds = convertSet(userService.getUsersByNickname(reqVO.getTitle()), AdminUserDO::getId);
        }

        PageResult<OaNoticeRespVO> result=new PageResult<>();
        List<OaNoticeRespVO> respVOList;
        if(userIds!=null) {
            respVOList = oaNoticeMapper.selectPageByStatusList(reqVO, userIds, 2, userId,SecurityFrameworkUtils.getTenantId());
            result.setTotal(oaNoticeMapper.selectPageByStatusNum(reqVO, userIds,2,userId,SecurityFrameworkUtils.getTenantId()));
        }
        else {
            respVOList = oaNoticeMapper.selectPageByStatusUserIdList(reqVO, 2, userId,SecurityFrameworkUtils.getTenantId());
            result.setTotal(oaNoticeMapper.selectPageByStatusUserIdNum(reqVO,2,userId,SecurityFrameworkUtils.getTenantId()));
        }
        for(OaNoticeRespVO a:respVOList){
            //富文本太多了
            a.setContent(null);
            //文件路径要隐藏
            a.setFileUrl(null);
        }
        result.setList(respVOList);
        // 查询分页
        return result;

    }

    @Override
    public OaNoticeRespVO getNotice(Long id) {

        //阅读量不用加一

        //如果文件权限没有下载，隐藏掉文件路径

        return oaNoticeMapper.selectRespById(id);
    }

    @Override
    public OaNoticeRespVO readNotice(Long id) {

        //阅读量加一
        Long userId = getLoginUserId();
        //先看这个用户有没有对应阅读记录，分步操作比大量数据的表用WHERE NOT EXISTS+INSERT INTO....ON DUPLICATE KEY UPDATE或许更好些
        Integer hadRecord = noticeReadRecordMapper.hadRecord(userId,id);
        if(hadRecord == 0){

            //先计数+1，这里放在通知公告表本身，查询效率更高，不过得注意维护
            oaNoticeMapper.updateReadNum(id);


            noticeReadRecordMapper.insertOne(userId,id);
        }


        /*//如果文件权限没有下载，隐藏掉文件路径

         */
        return oaNoticeMapper.selectRespById(id);

    }

    @VisibleForTesting
    public void checkNoticeExists(Long id) {
        if (id == null) {
            return;
        }
        OaNoticeDO notice = oaNoticeMapper.selectById(id);
        if (notice == null) {
            throw ServiceExceptionUtil.exception(NOTICE_NOT_FOUND);
        }
    }



    public static Cache<OaNoticePersonalReqVO, PageResult<OaNoticeRespVO>> resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(128*8)   // 设定最大容量
                    .expireAfterWrite(5, TimeUnit.SECONDS) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();



    @Override
    public PageResult<OaNoticeRespVO> getPersonalNotice(OaNoticePersonalReqVO reqVO) {


        PageResult<OaNoticeRespVO> resultx = null;
        try {
            resultx = resultCache.get(reqVO, () -> {

                // 获得用户基本信息
                AdminUserDO user = userService.getUser(getLoginUserId());
                if (user == null) {
                    return null;
                }

                if(!user.getDeptId().equals(reqVO.getDeptId())){
                    reqVO.setDeptId(user.getDeptId());
                }

                if(!user.getId().equals(reqVO.getUserId())){
                    reqVO.setUserId(user.getId());
                }

                PageResult<OaNoticeRespVO> result=new PageResult<>();
                if(reqVO.getRead()!=null) {

                    //全部的
                    if(reqVO.getRead()==2) {
                        result.setList(oaNoticeMapper.selectPersonalNotice(reqVO,SecurityFrameworkUtils.getTenantId()));
                        result.setTotal(oaNoticeMapper.selectPersonalNoticeNum(reqVO,SecurityFrameworkUtils.getTenantId()));
                    }

                    //已读的
                    if(reqVO.getRead()==1) {
                        result.setList(oaNoticeMapper.selectPersonalNoticeRead(reqVO,SecurityFrameworkUtils.getTenantId()));
                        result.setTotal(oaNoticeMapper.selectPersonalNoticeNumRead(reqVO,SecurityFrameworkUtils.getTenantId()));
                    }

                    //未读的
                    if(reqVO.getRead()==0) {
                        result.setList(oaNoticeMapper.selectPersonalNoticeNotRead(reqVO,SecurityFrameworkUtils.getTenantId()));
                        result.setTotal(oaNoticeMapper.selectPersonalNoticeNumNotRead(reqVO,SecurityFrameworkUtils.getTenantId()));
                    }
                }
                else {
                    result.setList(oaNoticeMapper.selectPersonalNotice(reqVO,SecurityFrameworkUtils.getTenantId()));
                    result.setTotal(oaNoticeMapper.selectPersonalNoticeNum(reqVO,SecurityFrameworkUtils.getTenantId()));
                }

                result.getList().forEach(e->{
                    e.setContent(null);
                });
                // 查询分页
                return result;


                    });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }


        return resultx;

    }

}
