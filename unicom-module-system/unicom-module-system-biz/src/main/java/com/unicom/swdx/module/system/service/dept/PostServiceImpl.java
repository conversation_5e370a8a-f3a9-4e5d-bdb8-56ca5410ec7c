package com.unicom.swdx.module.system.service.dept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.*;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupUsersRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import com.unicom.swdx.module.system.convert.dept.PostConvert;
import com.unicom.swdx.module.system.convert.permission.RoleGroupConvert;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserPostDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.PostMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserPostMapper;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertList;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertMap;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.*;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * 岗位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PostServiceImpl extends ServiceImpl<PostMapper, PostDO> implements PostService {

    @Resource
    private PostMapper postMapper;
    @Resource
    @Lazy
    private PermissionService permissionService;
    @Resource
    private DeptMapper deptMapper;

    @Resource
    @Lazy
    private AdminUserService userService;

    @Resource
    private UserPostMapper userPostMapper;

    @Resource
    private ReceiveApi receiveApi;

    @Override
    public Long createPost(PostCreateReqVO reqVO) {
        // 校验正确性
        this.checkCreateOrUpdate(null, reqVO.getName(), reqVO.getCode(), getTenantId());
        // 插入岗位
        PostDO post = PostConvert.INSTANCE.convert(reqVO);
        post.setTenant_id(getLoginUser().getTenantId());
        postMapper.insert(post);
        return post.getId();
    }

    @Override
    public void updatePost(PostUpdateReqVO reqVO) {
        // 校验正确性
        this.checkCreateOrUpdate(reqVO.getId(), reqVO.getName(), reqVO.getCode(), getTenantId());
        // 更新岗位
        PostDO updateObj = PostConvert.INSTANCE.convert(reqVO);
        postMapper.updateById(updateObj);
    }

    @Override
    public void deletePost(Long id) {
        // 校验是否存在
        this.checkPostExists(id);
        // 删除部门
        postMapper.deleteById(id);
    }

    @Override
    public List<PostDO> getPosts(Collection<Long> ids, Collection<Integer> statuses, Long deptId) {
        Long tenantId = deptMapper.selectTenantIdByDeptId(deptId);
        Boolean hasAdminRole =  permissionService.isSuperAdmin(SecurityFrameworkUtils.getLoginUserId());
        return postMapper.selectList(ids, statuses, tenantId,hasAdminRole);
    }

    @Override
    public PageResult<PostRespVO> getPostPage(PostPageReqVO reqVO) {
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(null);
        } else {
            reqVO.setTenantId(getTenantId());
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getName())) {
            //修复模糊查询逃逸
            reqVO.setName(reqVO.getName().replace("%","\\%").replace("_","\\_"));
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getCode())) {
            //修复模糊查询逃逸
            reqVO.setCode(reqVO.getCode().replace("%","\\%").replace("_","\\_"));
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getTenant())) {
            //修复模糊查询逃逸
            reqVO.setTenant(reqVO.getTenant().replace("%","\\%").replace("_","\\_"));
        }
        IPage page = MyBatisUtils.buildPage(reqVO);
        List<PostRespVO> data = postMapper.selectPostPage(page, reqVO);
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public List<PostExcelVO> getPosts(PostExportReqVO reqVO) {
        Long tenantId = null;
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            tenantId = getTenantId();
        }
        return postMapper.selectPostList(reqVO, tenantId);
    }

    @Override
    public PostDO getPost(Long id) {
        return postMapper.selectById(id);
    }

    private void checkCreateOrUpdate(Long id, String name, String code, Long tenantId) {
        // 校验自己存在
        checkPostExists(id);
        // 校验岗位名的唯一性
        checkPostNameUnique(id, name, tenantId);
        // 校验岗位编码的唯一性
        checkPostCodeUnique(id, code, tenantId);
    }

    private void checkPostNameUnique(Long id, String name, Long tenantId) {
        PostDO post = postMapper.selectByName(name, tenantId);
        if (post == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的岗位
        if (id == null) {
            throw ServiceExceptionUtil.exception(POST_NAME_DUPLICATE);
        }
        if (!post.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(POST_NAME_DUPLICATE);
        }
    }

    private void checkPostCodeUnique(Long id, String code, Long tenantId) {
        PostDO post = postMapper.selectByCode(code, tenantId);
        if (post == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的岗位
        if (id == null) {
            throw ServiceExceptionUtil.exception(POST_CODE_DUPLICATE);
        }
        if (!post.getId().equals(id)) {
            throw ServiceExceptionUtil.exception(POST_CODE_DUPLICATE);
        }
    }

    private void checkPostExists(Long id) {
        if (id == null) {
            return;
        }
        PostDO post = postMapper.selectById(id);
        if (post == null) {
            throw ServiceExceptionUtil.exception(POST_NOT_FOUND);
        }
    }

    @Override
    public void validPosts(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得岗位信息
        List<PostDO> posts = postMapper.selectBatchIds(ids);
        Map<Long, PostDO> postMap = convertMap(posts, PostDO::getId);
        // 校验
        ids.forEach(id -> {
            PostDO post = postMap.get(id);
            if (post == null) {
                throw exception(POST_NOT_FOUND);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(post.getStatus())) {
                throw exception(POST_NOT_ENABLE, post.getName());
            }
        });
    }

    @Override
    public List<PostDO> getPostList(Long tenantId) {
        return postMapper.selectList(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public PostImportRespVO importPost(List<PostImportExcelVO> importPosts) {
        if (CollUtil.isEmpty(importPosts)) {
            throw exception(POST_IMPORT_LIST_IS_EMPTY);
        }
        PostImportRespVO respVO = PostImportRespVO.builder().createPostnames(new ArrayList<>())
                .build();

        List<PostDO> postDOS = PostConvert.INSTANCE.convertList(importPosts);
        postDOS.forEach(postDO -> {
            this.checkCreateOrUpdate(null, postDO.getName(), postDO.getCode(), getTenantId());
            postDO.setSort(0);
            postDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
            postDO.setDisplayState(true);
            postDO.setTenant_id(SecurityFrameworkUtils.getTenantId());
        });
        postMapper.insertBatch(postDOS);
        respVO.getCreatePostnames().add("导入成功");
//        respVO.setFlag(true);
//        importPosts.forEach(importPost -> {
//            PostDO convert = PostConvert.INSTANCE.convert01(importPost);
//
//            postMapper.insert(convert);
//            respVO.getCreatePostnames().add(importPost.getName());
//        });
//        self.pushCreateToDataTop(convert);
        return respVO;
    }

    /**
     * 获取用户对应岗位标识的排序最大的岗位
     * @param userId 用户id
     * @param codes 岗位标识
     * @return 最大岗位
     */
    @Override
    public PostDO getMaxSortPostByUser(Long userId, Collection<String> codes) {
        AdminUserDO user = userService.getUser(userId);
        if (Objects.isNull(user)) return null;
        if(CollUtil.isEmpty(user.getPostIds())){
            return null;
        }
        // 增加一个不存在的id，避免查询错误或过滤条件
        user.getPostIds().add(-1L);
        List<PostDO> posts = this.getPosts(user.getPostIds());
        return posts.stream().filter(p -> Objects.equals(CommonStatusEnum.ENABLE.getStatus(),p.getStatus()))
                .filter(p -> codes.contains(p.getCode()))
                .max(Comparator.comparing(PostDO::getSort)).orElse(null);
    }

    /**
     * 获取用户对应岗位标识的排序最小的岗位
     * @param userId 用户id
     * @param codes 岗位标识
     * @return 最大岗位
     */
    @Override
    public PostDO getMinSortPostByUser(Long userId, Collection<String> codes) {
        AdminUserDO user = userService.getUser(userId);
        if (Objects.isNull(user)) return null;
        // 增加一个不存在的id，避免查询错误或过滤条件
        if( user.getPostIds()==null){
            user.setPostIds(new HashSet<>());
        }
        user.getPostIds().add(-1L);
        List<PostDO> posts = this.getPosts(user.getPostIds());
        return posts.stream().filter(p -> Objects.equals(CommonStatusEnum.ENABLE.getStatus(),p.getStatus()))
                .filter(p -> codes.contains(p.getCode()))
                .min(Comparator.comparing(PostDO::getSort)).orElse(null);
    }

    @Override
    public List<Long> getUserByPost(String code, Long tenantId) {
        List<Long> userIds = userPostMapper.selectUsersByPostCode(CollUtil.newArrayList(code), tenantId);
        if (CollUtil.isEmpty(userIds)) return null;
        return userIds;
    }

    @Override
    public Boolean judgeHRDirector(Long loginUserId) {
        List<Long> hrUserIds = getUserByPost("oa-hr-charge-leave-data",userService.getUser(loginUserId).getTenantId());
        if(CollUtil.isEmpty(hrUserIds)){
            return false;
        }else {
            return hrUserIds.contains(loginUserId);
        }
    }

    private List<UserSimpleRespVO> getUsersByPostId(Long id,List<Long> received) {
        List<UserPostDO> userPostDOS = userPostMapper.selectListByPostIds(CollUtil.newArrayList(id));
        List<Long> userIds = userPostDOS.stream()
                .filter(u -> CollUtil.isEmpty(received) || !received.contains(u.getUserId()))
                .map(UserPostDO::getUserId)
                .collect(Collectors.toList());
        return UserConvert.INSTANCE.covertList8(userService.getUsers(userIds).stream().sorted(Comparator.comparing(AdminUserDO::getSort)).collect(Collectors.toList())).stream().filter(it-> Objects.equals(it.getTenantId(), getTenantId())).collect(Collectors.toList());
    }

    @Override
    public List<PostSimpleUserRespVO> getPosts(String processInstanceId) {
        List<PostDO> list = baseMapper.selectList(new LambdaQueryWrapperX<PostDO>()
                .eq(PostDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .eq(PostDO::getTenant_id, getTenantId())
                .eq(PostDO::getDisplayState, true)
        );
        // 排序后，返回给前端
        list.sort((Comparator.comparing(PostDO::getSort)).thenComparing(PostDO::getId));
        List<Long> received = null;
        if (StrUtil.isNotBlank(processInstanceId)) {
            received = receiveApi.getReceivedUserIds(processInstanceId).getCheckedData();
        }
        List<PostSimpleUserRespVO> result = PostConvert.INSTANCE.convertList04(list);
        for (PostSimpleUserRespVO post : result) {
            post.setUsers(this.getUsersByPostId(post.getId(),received));
        }
        result.removeIf(p -> CollUtil.isEmpty(p.getUsers()));
        return result;
    }

    @Override
    public PostDO getPostByCode(String postCode, Long tenantId) {
        return baseMapper.selectByCode(postCode, tenantId);
    }

    @Override
    public void saveUserPosts(Long userId, Set<Long> postIds) {
        userPostMapper.insertBatch(convertList(postIds,
                postId -> new UserPostDO().setUserId(userId).setPostId(postId)));
    }

    @Override
    public PostUsersRespVO getPostUsers(PostUsersPageReqVO pageReqVO) {
        //角色组基本信息
        PostDO post = postMapper.selectById(pageReqVO.getPostId());
        PostUsersRespVO result = PostConvert.INSTANCE.convert1(post);
        //用户精简信息
        PageResult<UserSimpleRespVO> users = userService.getUsersByPostUsers(pageReqVO);
        result.setUsers(users);
        return result;
    }
}
