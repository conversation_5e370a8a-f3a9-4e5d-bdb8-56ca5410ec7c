package com.unicom.swdx.module.system.convert.message;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageRespVO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageSendDO;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface MessageSendConvert {

    MessageSendConvert INSTANCE = Mappers.getMapper(MessageSendConvert.class);

    MessageSendDO convert(MessageSendCreateReqVO bean);
//    MessageDO convert(MessageUpdateReqVO bean);
    PageResult<MessageSendPageRespVO> convertPage(PageResult<MessageSendDO> page);
//    MessageRespGetVO convert01(MessageDO bean);
    TimedTaskDO convertT(SmsSendReq bean);
}
