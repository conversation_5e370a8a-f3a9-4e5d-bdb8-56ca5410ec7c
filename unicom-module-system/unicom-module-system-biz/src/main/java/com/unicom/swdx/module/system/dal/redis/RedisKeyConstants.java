package com.unicom.swdx.module.system.dal.redis;

import com.unicom.swdx.framework.redis.core.RedisKeyDefine;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;

import java.time.Duration;

import static com.unicom.swdx.framework.redis.core.RedisKeyDefine.KeyTypeEnum.STRING;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    RedisKeyDefine BUSINESS_CENTER_DATA = new RedisKeyDefine("访问令牌的缓存",
            "business_center_data:%s", // 参数为 token
            STRING, String.class, RedisKeyDefine.TimeoutTypeEnum.DYNAMIC);

    RedisKeyDefine OAUTH2_ACCESS_TOKEN = new RedisKeyDefine("访问令牌的缓存",
            "oauth2_access_token:%s", // 参数为访问令牌 token
            STRING, OAuth2AccessTokenDO.class, RedisKeyDefine.TimeoutTypeEnum.DYNAMIC);

    RedisKeyDefine SOCIAL_AUTH_STATE = new RedisKeyDefine("社交登陆的 state", // 注意，它是被 JustAuth 的 justauth.type.prefix 使用到
            "social_auth_state:%s", // 参数为 state
            STRING, String.class, Duration.ofHours(24)); // 值为 state

    RedisKeyDefine VERIFICATION_MOBILE = new RedisKeyDefine("验证码缓存",
            "verification_mobile:%s:%s", // 参数为 state
            STRING, String.class, Duration.ofMinutes(1)); // 值为 state

    RedisKeyDefine UUID_USERID = new RedisKeyDefine("忘记密码uuid",
            "forget_password_uuid:%s", // 参数为 state
            STRING, String.class, Duration.ofMinutes(1)); // 值为 state

    RedisKeyDefine MOBILE_MESSAGE_TIMES_DAY = new RedisKeyDefine("日短信发送次数",
            "mobile_message_times_day:%s", // 参数为 state
            STRING, String.class, Duration.ofMinutes(1)); // 值为 state

    RedisKeyDefine MOBILE_MESSAGE_TIMES_MINUTE = new RedisKeyDefine("分钟短信发送次数",
            "mobile_message_times_minute:%s", // 参数为 state
            STRING, String.class, Duration.ofMinutes(1)); // 值为 state

    RedisKeyDefine LOGIN_BAD_PASSWORD_TIMES = new RedisKeyDefine("用户输错密码次数",
            "login_bad_password_times:%s", // 参数为 state
            STRING, String.class, Duration.ofMinutes(30)); // 值为 state
}
