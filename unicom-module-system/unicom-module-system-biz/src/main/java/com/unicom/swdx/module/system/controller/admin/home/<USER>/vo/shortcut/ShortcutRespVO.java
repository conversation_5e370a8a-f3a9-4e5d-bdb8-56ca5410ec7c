package com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("业务中台 - 快捷入口 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShortcutRespVO extends ShortcutBaseVO {

    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    @JsonIgnore
    private Long shortcutUserId;

}
