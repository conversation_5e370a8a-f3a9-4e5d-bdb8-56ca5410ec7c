package com.unicom.swdx.module.system.service.user;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.module.system.api.user.dto.CreateAdminForUnitBatchDTO;
import com.unicom.swdx.module.system.api.user.dto.CreateAdminForUnitBatchResultDTO;
import com.unicom.swdx.module.system.api.user.dto.UserInputDTO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthForgetPasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthUpdatePasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptCountVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.trainee.TraineeUserReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;

import javax.validation.Valid;
import java.io.InputStream;
import java.util.*;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface AdminUserService {

    /**
     * 创建用户
     *
     * @param reqVO 用户信息
     * @return 用户编号
     */
    Long createUser(@Valid UserCreateReqVO reqVO,Long tenantId,Integer userCategory);

    /**
     * 教务系统创建用户
     * @param mobile
     * @return
     */
    Long createUserJW(String mobile, String nickname , Long tennantid);

     /**
     * 调训系统创建单位管理员用户
     *
     * @param username 用户名
     * @param mobile 手机号
     * @param nickname 用户昵称
     * @return 用户id
     */
    Long createAdminForUnit(String username, String mobile, String nickname);


    /**
     * 批量创建调训单位管理员用户
     * @param dtos 调训单位管理员用户信息
     * @return 调训单位管理员用户信息 （包含插入id）
     */
    List<CreateAdminForUnitBatchResultDTO> batchCreateAdminForUnit(CreateAdminForUnitBatchDTO dtos);

    /**
     * 更新单位管理员手机号
     * @param userId 用户id
     * @param mobile 新手机号
     */
    Long updateAdminMobileForUnit(Long userId, String mobile);

    /**
     * 删除用户
     * @param userIds 用户id集合
     */
    void deleteByIds(List<Long> userIds);

    Long createUserJWall(String mobile, String nickname,String systemid);

    Map<String, Long> createUserBatchByTenant(List<UserInputDTO> userInputList, Long tenantId);

    Long createUserHr(@Valid UserCreateReqVO reqVO,Long tenantId);

    /**
     * 创建应聘用户
     *
     * @param reqVO 用户信息
     * @return 用户编号
     */
    Long createApplyUser(@Valid ApplyUserCreateReqVO reqVO,Long tenantId);

    /**
     * 创建学员用户
     *
     * @param reqVO 用户信息
     * @return 用户编号
     */
    Long createTraineeUser(TraineeUserReqVO reqVO);


    Long createYJSUser(TraineeUserReqVO reqVO);

    /**
     * 修改用户
     *
     * @param reqVO 用户信息
     */
    void updateUser(@Valid UserUpdateReqVO reqVO);

    /**
     * 修改密码
     *
     * @param id       用户编号
     * @param password 密码
     */
    void updateUserPassword(Long id, String password);

    /**
     * 更新用户的最后登陆信息
     *
     * @param id 用户编号
     * @param loginIp 登陆 IP
     */
    void updateUserLogin(Long id, String loginIp);

    AuthLoginRespVO updateUserDefault(Long id);

    /**
     * 修改用户个人信息
     *
     * @param id 用户编号
     * @param reqVO 用户个人信息
     */
    void updateUserProfile(Long id, @Valid UserProfileUpdateReqVO reqVO);

    /**
     * 修改用户个人密码
     *
     * @param id 用户编号
     * @param reqVO 更新用户个人密码
     */
    void updateUserPassword(Long id, @Valid UserProfileUpdatePasswordReqVO reqVO);

    /**
     * 根据用户id更新用户信息
     * @param user 用户信息
     */
    void updateUserById(AdminUserDO user);
    /**
     * 修改用户个人手机号
     *
     * @param reqVO 更新用户个人手机号
     */
    String updateUserMobile(UserUpdateMobileReqVO reqVO);

    /**
     * 修改App用户个人手机号
     *
     * @param reqVO 更新App用户个人手机号
     */
    String updateAppUserMobile(UserProfileUpdateMobileReqVO reqVO);

    /**
     * 更新用户头像
     *
     * @param id         用户 id
     * @param avatarFile 头像文件
     */
    String updateUserAvatar(Long id, InputStream avatarFile) throws Exception;

    /**
     * 更新用户头像
     *
     * @param id         用户 id
     * @param baseImage 头像文件
     */
    String updateUserAvatarApp(Long id, String baseImage, String fileType);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 修改app设备ID
     *
     * @param id     用户编号
     * @param appCid app设备ID
     */
    void updateUserAppCid(Long id, String appCid);

    /**
     * 删除普通用户
     *
     * @param id 用户编号
     */
    void deleteUser(Long id);

    /**
     * 删除机构管理员用户
     * @param id 机构管理员id
     */
    void deleteAdminUser(Long id);


    /**
     * 获得用户分页列表
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<AdminUserDO> getUserPage(UserPageReqVO reqVO);

    /**
     * 获得用户分页列表(用户管理)
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<AdminUserDO> getUserPageManage(UserPageReqVO reqVO);

    /**
     * 获得用户分页列表（消息中心授权过滤后）
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<AdminUserDO> getUserPageManageAuthority(UserPageReqVO reqVO);

    /**
     * 获得导出用户列表
     *
     * @param reqVO 导出列表请求
     * @return 用户列表
     */
    List<AdminUserDO> getUserExport(UserExportReqVO reqVO);

    /**
     * 获得用户通讯录分页列表
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<UserContactVO> getUserContactPage(UserPageReqVO reqVO);

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AdminUserDO getUser(Long id);

    /**
     * 通过用户 ID 查询用户,无论用户是否删除
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AdminUserDO getHistoryUser(Long id);

    /**
     * 根据用户名获取用户信息
     * @param username 用户名
     * @return 用户信息
     */
    AdminUserDO getUserByUsername(String username);

    /**
     * 根据用户名从单位表获取用户信息
     * @param username 用户名
     * @return 用户信息
     */
    AdminUserDO getUserByUnitUsername(String username);

    /**
     * 根据用户名获取用户信息 (不作手机号为空校验)
     * @param username 用户名
     * @return 用户信息
     */
    AdminUserDO getUserByUsernameForUnit(String username);

    List<AdminUserDO> getUsersByUsername(String username);
    /**
     * 根据用户名获取用户信息
     * @param username 用户名
     * @return 用户信息
     */
    AdminUserDO getByUsername(String username);

    /**
     * 获得指定部门的用户数组
     *
     * @param deptIds 部门数组
     * @return 用户数组
     */
    List<AdminUserDO> getUsersByDeptIds(Collection<Long> deptIds);

    /**
     * 获得指定岗位的用户数组
     *
     * @param postIds 岗位数组
     * @return 用户数组
     */
    List<AdminUserDO> getUsersByPostIds(Collection<Long> postIds);

    /**
     * 获得用户列表
     *
     * @param ids 用户编号数组
     * @return 用户列表
     */
    List<AdminUserDO> getUsers(Collection<Long> ids);

    /**
     * 校验用户们是否有效。如下情况，视为无效：
     * 1. 用户编号不存在
     * 2. 用户被禁用
     *
     * @param ids 用户编号数组
     */
    void validUsers(Set<Long> ids);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    @DataPermission(enable = false)
    default Map<Long, AdminUserDO> getUserMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        return CollectionUtils.convertMap(getUsers(ids), AdminUserDO::getId);
    }

    /**
     * 获得用户列表，基于昵称模糊匹配
     *
     * @param nickname 昵称
     * @return 用户列表
     */
    List<AdminUserDO> getUsersByNickname(String nickname);

    /**
     * 批量导入用户
     *
     * @param importUsers     导入用户列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    UserImportRespVO importUsers(List<UserImportExcelVO> importUsers, boolean isUpdateSupport);

    /**
     * 获得指定状态的用户们
     *
     * @param status 状态
     * @return 用户们
     */
    List<AdminUserDO> getUsersByStatus(Integer status);

    /**
     * 获取组织下开启的用户
     * @param deptId
     * @return 用户
     */
    List<AdminUserDO> getUsersByStatusAndDeptId(Long deptId);

    /**
     * 获取机构下所有用户精简信息
     * @return 用户精简信息
     */
    List<UserSimpleRespVO> getUsersByStatusAndTenantId(String nickname);

    /**
     * 根据用户ids获取用户精简信息分页
     * @param pageReqVO 用户角色信息
     * @return 用户精简信息
     */
    PageResult<UserSimpleRespVO> getUsersByUserIds(RoleUsersPageReqVO pageReqVO);

    /**
     * 获取多组织下的用户
     * @param deptIds
     * @param name
     * @return 用户
     */
    List<AdminUserDO> getUsersDeptId(List<Long> deptIds, String name);

    /**
     * 判断密码是否匹配
     *
     * @param rawPassword 未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    /**
     * 判断验证码登录是否匹配
     * @param verification 验证码
     * @param mobile 手机号
     * @return 是否匹配
     */
    boolean isVerificationMatch(String verification,String mobile);

    /**
     * 判断验证码注册是否匹配
     * @param verification 验证码
     * @param mobile 手机号
     * @return 是否匹配
     */
    boolean isRegisterVerificationMatch(String verification,String mobile);

    /**
     * 判断验证码登录是否匹配
     * @param verification 验证码
     * @param mobile 手机号
     * @param username 用户名
     * @return 是否匹配
     */
    boolean isVerificationMatch(String verification,String mobile,String username);


    /**
     * 判断验证码重置密码是否匹配
     * @param verification 验证码
     * @param mobile 手机号
     * @return 是否匹配
     */
    boolean isResetPasswordVerificationMatch(String verification, String mobile);

    /**
     * 根据角色id和组织id获取用户
     * @param deptId
     * @param roleId
     * @return
     */
    List<AdminUserDO> getUsersByDeptRoleIds(Long deptId, Long roleId);

    UserUpdatePasswordRespVO getUserIdResp(AuthForgetPasswordReqVO reqVO);

    void updateAuthUserPassword(AuthUpdatePasswordReqVO reqVO);

    List<Long> selectIdListByTenantId(Long tenantId);

    /**
     * 根据机构id查询机构下的所有用户信息
     * @param tenantId 机构id
     * @return 用户信息
     */
    List<AdminUserDO> getListByTenantId(Long tenantId);

    /**
     * 根据用户昵称模糊搜索用户信息
     * @param nickname 用户昵称
     * @return 用户信息
     */
    List<UserTenantSimpleRespVO> getUserTenantByNameLike(String nickname);

    /**
     * 获取用户的密码更新时间信息
     * @return
     */
    UserPasswordUpdateTimeRespVO getPasswordUpdateTime();

    /**
     * 校验用户密码是否已经过期
     * @param user
     */
    void validPasswordExpired(AdminUserDO user);

    /**
     * 校验用户输入密码
     * @param reqVO
     */
    void verifyPassword(UserProfileVerificationPasswordReqVO reqVO);

    /**
     * 根据手机号获取用户
     * @param mobile 手机号
     * @return 用户
     */
    AdminUserDO getUserByMobile(String mobile);
    List<AdminUserDO> getUserByMobiles(List<String> mobiles);

    AdminUserDO getUserByMobileTenant(String mobile ,Long tenantId);

    List<AdminUserDO> getUsersByMobile(String mobile);

    /**
     * 根据unionid获取用户
     * @param unionid unionid
     * @return 用户
     */
    AdminUserDO getByUnionid(String unionid);

    /**
     * 根据openid获取用户
     * @param openid openid
     * @return 用户
     */
    AdminUserDO getByOpenid(String openid);

    /**
     * 更新用户的openid和unionid
     * @param user
     */
    void updateOpenidAndUnionid(AdminUserDO user);

    List<AdminUserDO> getUsersByPostCode(List<String> postCodes, Long tenantId);

    List<AdminUserDO> getUsersByPostDeptCode(List<String> postCodes, Long tenantId,Long deptId);

    List<AdminUserDO> getUsersByDeptName(String deptName, Long tenantId);

    List<AdminUserDO> getUsersByTenantId(Long tenantId);

    void updateTraineeUserById(TraineeUserReqVO traineeUser);

    List<DeptDO> getAllDept(Long userId);

    Boolean lastLogin();

    PageResult<UserSimpleRespVO> getUsersByGroupIds(RoleGroupUsersPageReqVO pageReqVO);

    PageResult<UserSimpleRespVO> getUsersByPostUsers(PostUsersPageReqVO pageReqVO);

    List<Long> getUserIdsByGroupId(Long id);

    Boolean deleteOpenId(String userId);

    Float getMoney(String othersystemid);

    List<AdminUserDO> countByDeptIdAdminUserDosCache(List<Long> deptIds,Long tenantId,Integer personStatus);
    List<DeptCountVO> countByDeptsCache(Long tenantId,Integer personStatus);

    Long editUserJW(String phone, Long userId, String nickname);
    Boolean deleteUserJW(List<Long> ids);

    List<Long> createUsersJW(String jsonStr);

    void resetUserPassword(Long userId);

    void codeUpdateUserPassword(Long id, String newPassword);
}
