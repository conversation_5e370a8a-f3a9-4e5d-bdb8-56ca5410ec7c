package com.unicom.swdx.module.system.api.dept;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.convert.dept.PostConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserPostDO;
import com.unicom.swdx.module.system.service.dept.PostService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertList;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class PostApiImpl implements PostApi {

    @Resource
    private PostService postService;

    @Override
    public CommonResult<Boolean> validPosts(Collection<Long> ids) {
        postService.validPosts(ids);
        return success(true);
    }

    @Override
    public PostRespDTO getMaxSortPostByUser(Long userId, Collection<String> codes) {
        return PostConvert.INSTANCE.convertDTO(postService.getMaxSortPostByUser(userId,codes));
    }

    @Override
    public PostRespDTO getMinSortPostByUser(Long userId, Collection<String> codes) {
        return PostConvert.INSTANCE.convertDTO(postService.getMinSortPostByUser(userId,codes));
    }

    @Override
    public List<Long> getUserByPost(String code, Long tenantId) {
        return postService.getUserByPost(code, tenantId);
    }

    @Override
    public CommonResult<Boolean> isHRDirector(Long loginUserId) {
        return success(postService.judgeHRDirector(loginUserId));
    }

    @Override
    public CommonResult<PostRespDTO> getPostByCode(String postCode, Long tenantId) {
        return success(PostConvert.INSTANCE.convertDTO(postService.getPostByCode(postCode, tenantId)));
    }

    @Override
    public CommonResult<PostRespDTO> getPostById(Long postId) {

        return success(PostConvert.INSTANCE.convertDTO(postService.getPost(postId)));
    }

    @Override
    public void saveUserPost(Long userId, Set<Long> postIds) {
        postService.saveUserPosts(userId, postIds);
    }
}
