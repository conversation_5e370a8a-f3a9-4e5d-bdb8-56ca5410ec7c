package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 上行短信分页 Request VO")
@Data
@ToString(callSuper = true)
public class MessageUpstreamPageVO {

    private Long id;

    private String phone;

    @ApiModelProperty(value = "姓名", example = "sk")
    private String name;

    @ApiModelProperty(value = "消息内容", example = "sk")
    private String messageContent;

    @ApiModelProperty(value = "创建时间", example = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}
