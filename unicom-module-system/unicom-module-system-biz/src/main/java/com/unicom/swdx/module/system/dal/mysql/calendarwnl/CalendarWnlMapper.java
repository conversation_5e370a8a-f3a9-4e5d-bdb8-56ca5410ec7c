package com.unicom.swdx.module.system.dal.mysql.calendarwnl;


import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.dal.dataobject.calendarwnl.CalendarWnlDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 万年历 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CalendarWnlMapper extends BaseMapperX<CalendarWnlDO> {

    /**
     * 根据日期更新万年历
     * @param calendarWnlDO do
     */
    default void updateByDate(CalendarWnlDO calendarWnlDO) {
        update(calendarWnlDO,new LambdaQueryWrapperX<CalendarWnlDO>()
                .eq(CalendarWnlDO::getGregorianDate,calendarWnlDO.getGregorianDate()));
    }

}
