package com.unicom.swdx.module.system.dal.mysql.tenant;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.type.TenantTypePageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 机构用户类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantTypeMapper extends BaseMapperX<TenantTypeDO> {

    default PageResult<TenantTypeDO> selectPage(TenantTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantTypeDO>()
                .likeIfPresent(TenantTypeDO::getName, reqVO.getName())
                .eqIfPresent(TenantTypeDO::getStatus, reqVO.getStatus())
                .orderByDesc(TenantTypeDO::getId));
    }

    default List<TenantTypeDO> selectEnableList() {
        return selectList(new LambdaQueryWrapperX<TenantTypeDO>()
                .eq(TenantTypeDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
        );
    }

    default TenantTypeDO selectByName(String name) {
        return selectOne(new LambdaQueryWrapperX<TenantTypeDO>()
                .eq(TenantTypeDO::getName,name)
                .last("limit 1")
        );
    }

    default List<TenantTypeDO> selectListEnable(Collection<Long> ids) {
        return selectList(new LambdaQueryWrapperX<TenantTypeDO>()
                .inIfPresent(TenantTypeDO::getId,ids)
                .eq(TenantTypeDO::getStatus,CommonStatusEnum.ENABLE.getStatus())
        );
    }

}
