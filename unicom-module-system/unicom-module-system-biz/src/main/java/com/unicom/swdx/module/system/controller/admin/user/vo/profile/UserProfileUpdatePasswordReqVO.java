package com.unicom.swdx.module.system.controller.admin.user.vo.profile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

@ApiModel("管理后台 - 用户个人中心更新密码 Request VO")
@Data
public class UserProfileUpdatePasswordReqVO {

    @ApiModelProperty(value = "旧密码", required = true, example = "123456")
    @NotEmpty(message = "旧密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String oldPassword;

    @ApiModelProperty(value = "新密码", required = true, example = "654321")
    @NotEmpty(message = "新密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String newPassword;

}
