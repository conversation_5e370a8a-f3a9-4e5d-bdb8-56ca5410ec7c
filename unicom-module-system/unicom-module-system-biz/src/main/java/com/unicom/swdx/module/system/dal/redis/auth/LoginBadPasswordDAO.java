package com.unicom.swdx.module.system.dal.redis.auth;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.module.system.dal.redis.RedisKeyConstants.LOGIN_BAD_PASSWORD_TIMES;

/**
 * 用户登录输错密码DAO
 *
 * <AUTHOR>
 * @date 2023/6/30 15:27
 **/
@Repository
public class LoginBadPasswordDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public Long get(String username) {
        String redisKey = formatKey(username);
        String times = stringRedisTemplate.opsForValue().get(redisKey);
        return Objects.isNull(times)?0:Long.parseLong(times);
    }

    public void set(String username) {
        String redisKey = formatKey(username);
        stringRedisTemplate.opsForValue().increment(redisKey);
        stringRedisTemplate.expire(redisKey,30, TimeUnit.MINUTES);
    }

    private String formatKey(String username) {
        return String.format(LOGIN_BAD_PASSWORD_TIMES.getKeyTemplate(), username);
    }

}
