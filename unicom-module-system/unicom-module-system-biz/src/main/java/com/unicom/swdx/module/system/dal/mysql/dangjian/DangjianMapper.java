package com.unicom.swdx.module.system.dal.mysql.dangjian;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.api.user.dto.DangJianDTO;
import com.unicom.swdx.module.system.dal.dataobject.yjs.YjsStuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
@DS("dangjian")
public interface DangjianMapper  {


    @Select("select user_mobile  from t_user tu left join t_user_party_member tupm on tu.user_id = tupm.user_id \n" +
            "where tupm.party_member_status = 1 and party_status in (1,2)")
    List<String> selectUserMobile();


    DangJianDTO selectUserInfo(@Param("mobile")String mobile );

}
