package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveIDCardSerializer;
import com.unicom.swdx.framework.jackson.core.databind.SensitiveMobileSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.checkerframework.checker.units.qual.A;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 机构 Response VO")
@Data
@ToString(callSuper = true)
public class TenantRespVO {

    @ApiModelProperty(value = "机构id", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "机构全称", example = "测试体育俱乐部")
    private String name;

    @ApiModelProperty(value = "所在地区划")
    private List<Long> locationRegion;

    @ApiModelProperty(value = "所在地地址",example = "湖南长沙")
    private String locationAddress;

    @ApiModelProperty(value = "机构用户类型", example = "1")
    private List<Long> instUserType;

    @ApiModelProperty(value = "机构管理人姓名",example = "张三")
    private String contactNickname;

    @ApiModelProperty(value = "机构管理员手机号码（联系手机）",example = "13112345678")
    @JsonSerialize(using = SensitiveMobileSerializer.class)
    private String contactMobile;

    @ApiModelProperty(value = "用户名（原联系人）",example = "skyline")
    private String contactName;

    @ApiModelProperty(value = "机构状态",example = "1")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

}
