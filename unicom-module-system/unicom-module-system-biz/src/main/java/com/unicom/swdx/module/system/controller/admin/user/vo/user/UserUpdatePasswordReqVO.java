package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

@ApiModel("管理后台 - 用户更新密码 Request VO")
@Data
public class UserUpdatePasswordReqVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    @NotNull(message = "用户编号不能为空")
    private Long id;

    @ApiModelProperty(value = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String password;

}
