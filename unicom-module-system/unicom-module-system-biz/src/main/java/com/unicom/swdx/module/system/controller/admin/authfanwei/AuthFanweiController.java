package com.unicom.swdx.module.system.controller.admin.authfanwei;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.service.auth.AdminAuthService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

@Controller
@RequestMapping("/fanwei")
@Slf4j
public class AuthFanweiController {

    public final String sso_url = "https://ehall.hnswdx.gov.cn/sso";
    public final String client_id = "7dd6840a-f6b0-45c3-9795-5acdd7b5f99f";
    public final String client_secret = "4ySnotrAGBhvRt1eNLSYjSbU";

    public final String app_url = "https://dxoa.hnswdx.gov.cn/fanwei/login";

    public final String portal_url = "https://dxoa.hnswdx.gov.cn/ssoLogin";

    @Resource
    private AdminAuthService authService;


    @GetMapping("/login")
    @SneakyThrows
    @PermitAll
    public void login(HttpServletRequest request,HttpServletResponse response) {
//        response.setCharacterEncoding("UTF-8");
//        response.setContentType("text/html;charset=UTF-8");

        String ticket = request.getParameter("ticket");
        if (ticket != null) {
//            response.getWriter().write("ticket:"+ticket);

            //获取access_token
            String interface_url = sso_url + "/oauth2.0/accessToken";
            interface_url += "?client_id=" + client_id;
            interface_url += "&client_secret=" + client_secret;
            interface_url += "&grant_type=authorization_code";
            interface_url += "&code="+ticket;
            interface_url += "&redirect_uri=" + URLEncoder.encode(app_url, "UTF-8");
            try {
                HttpResponse accessTokenResp = HttpUtil.createGet(interface_url)
                        .header("cache-control", "no-cache")
                        .header("Content-Type", "text/html;charset:utf-8;")
                        .execute();
                String body = accessTokenResp.body();
//                response.getWriter().write("\r\n<h3>get access_token result:</h3>\r\n"+body);
                JSONObject object = JSON.parseObject(body);
                if (object != null && object.containsKey("access_token")) {
                    String access_token = object.getString("access_token");
                    interface_url = sso_url + "/oauth2.0/profile?access_token=" + access_token;
                    HttpResponse profileResp = HttpUtil.createGet(interface_url)
                            .header("cache-control", "no-cache")
                            .execute();
                    String body1 = profileResp.body();
//                    response.getWriter().write("\r\n<h3>get userinfo result:</h3>\r\n" + body1);
                    JSONObject object1 = JSON.parseObject(body1);
                    if (object1.containsKey("id")) {
                        String mobile = object1.getString("id");
                        // 重定向到门户页面
                        AuthLoginRespVO authLoginRespVO = authService.mobileLogin(mobile);
                        String authUrl = portal_url + "?accessToken=" + authLoginRespVO.getAccessToken() + "&refreshToken=" + authLoginRespVO.getRefreshToken() + "&userId=" + authLoginRespVO.getUserId();
                        response.sendRedirect(authUrl);
//                        response.getWriter().write("\r\n<h3>Congratulation : "+object1.getString("id")+"</h3><h3>Welcome to Use OAuth2 , \t THIS is Success!</h3>");
                    }
                } else {
                    String authUrl = sso_url + "/oauth2.0/authorize?client_id=" + client_id + "&redirect_uri=" + URLEncoder.encode(app_url, "UTF-8") + "&response_type=code";
                    response.sendRedirect(authUrl);
                    return;
                }

            } catch (Exception e) {
                log.error(e.toString());
            }

        } else {
            String authUrl = sso_url + "/oauth2.0/authorize?client_id=" + client_id + "&redirect_uri=" + URLEncoder.encode(app_url, "UTF-8") + "&response_type=code";
            response.sendRedirect(authUrl);
            return;
        }
    }

}
