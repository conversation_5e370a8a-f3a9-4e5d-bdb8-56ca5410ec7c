package com.unicom.swdx.module.system.controller.admin.permission.vo.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("管理后台 - 角色精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoleClientSimpleRespVO {

    @ApiModelProperty(value = "应用id")
    private String id;

    @ApiModelProperty(value = "应用名称")
    private String name;

    private List<RoleSimpleRespVO> children;

}
