package com.unicom.swdx.module.system.service.message;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessagePageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;

import javax.validation.Valid;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageService extends IService<MessageDO> {

    /**
     * 创建消息模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMessage(@Valid MessageCreateReqVO createReqVO);
    PageResult<MessageDO> getMessagePage(MessagePageReqVO pageReqVO);
    /**
     * 获得消息模板
     *
     * @param id 编号
     * @return 消息模板
     */
    MessageDO getMessage(Long id);
    /**
     * 更新消息模板
     *
     */
    void updateMessage(@Valid MessageUpdateReqVO updateReqVO);
    /**
     * 删除消息模板
     *
     * @param id 编号
     */
    void deleteMessage(Long id);



}
