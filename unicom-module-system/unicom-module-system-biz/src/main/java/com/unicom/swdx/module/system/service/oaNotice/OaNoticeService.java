package com.unicom.swdx.module.system.service.oaNotice;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.oaNotice.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;

/**
 * OA系统通知公告 Service 接口
 */
public interface OaNoticeService {

    /**
     * 创建通知公告
     *
     * @param reqVO 岗位公告公告信息
     * @return 岗位公告公告编号
     */
    Long createNotice(OaNoticeCreateReqVO reqVO);

    /**
     * 更新通知公告
     *
     * @param reqVO 岗位公告公告信息
     */
    void updateNotice(OaNoticeUpdateReqVO reqVO);

    /**
     * 更新通知公告
     *
     * @param reqVO 岗位公告公告信息
     */
    void updatePublishNotice(OaNoticeUpdateReqVO reqVO);

    /**
     * 删除通知公告
     *
     * @param id 岗位公告公告编号
     */
    void deleteNotice(Long id);

    /**
     * 获得通知公告分页列表
     *
     * @param reqVO 分页条件
     * @return 部门分页列表
     */
    PageResult<OaNoticeDO> pageNotices(OaNoticePageReqVO reqVO);

    /**
     * 获得已发布通知公告列表
     *
     * @param reqVO 分页条件
     * @return 部门分页列表
     */
    PageResult<OaNoticeRespVO> pagePublishedNotices(OaNoticePageReqVO reqVO);

    /**
     * 获得草稿箱通知公告分页列表
     *
     * @param reqVO 分页条件
     * @return 部门分页列表
     */
    PageResult<OaNoticeRespVO> pageDraftNotices(OaNoticePageReqVO reqVO);

    /**
     * 获得通知公告详情
     *
     * @param id 岗位公告公告编号
     * @return 岗位公告公告信息
     */
    OaNoticeRespVO getNotice(Long id);

    /**
     * 获得通知公告详情
     *
     * @param id 岗位公告公告编号
     * @return 岗位公告公告信息
     */
    OaNoticeRespVO readNotice(Long id);

    /**
     * 获得具体用户收到的通知公告
     *
     * @param reqVO 分页条件
     * @return 部门分页列表
     */
    PageResult<OaNoticeRespVO> getPersonalNotice(OaNoticePersonalReqVO reqVO);

}
