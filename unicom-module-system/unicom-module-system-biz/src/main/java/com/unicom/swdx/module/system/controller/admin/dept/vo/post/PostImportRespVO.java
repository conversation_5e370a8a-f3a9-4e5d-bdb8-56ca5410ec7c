package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ApiModel("管理后台 - 用户导入 Response VO")
@Data
@Builder
public class PostImportRespVO {
//
    @ApiModelProperty(value = "创建成功的岗位数组", required = true)
    private List<String> createPostnames;

//    @ApiModelProperty(value = "更新成功的岗位数组", required = true)
//    private List<String> updatePostnames;

//    @ApiModelProperty(value = "导入失败的岗位集合", required = true, notes = "key 为用户名，value 为失败原因")
//    private Map<String, String> failurePostnames;


}
