package com.unicom.swdx.module.system.controller.admin.permission.vo.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("管理后台 - 角色分页 Request VO")
@Data
public class RoleExportReqVO {

    @ApiModelProperty(value = "角色名称", example = "sk", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "角色标识", example = "unicom", notes = "模糊匹配")
    private String code;

    @ApiModelProperty(value = "所属机构", example = "湖南省体育局", notes = "模糊匹配")
    private String tenantName;

    @ApiModelProperty(value = "应用id", example = "1")
    private Integer clientId;

    @ApiModelProperty(value = "角色类型",notes = "枚举 {RoleTypeEnum}",required = true,example = "1")
    private Integer type;

    @ApiModelProperty(value = "展示状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

}
