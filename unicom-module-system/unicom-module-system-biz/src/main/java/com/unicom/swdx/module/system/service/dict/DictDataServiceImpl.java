package com.unicom.swdx.module.system.service.dict;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.annotations.VisibleForTesting;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.dict.vo.data.*;
import com.unicom.swdx.module.system.convert.dict.DictDataConvert;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictTypeDO;
import com.unicom.swdx.module.system.dal.mysql.dict.DictDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * 字典数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DictDataServiceImpl implements DictDataService {

    /**
     * 排序 dictType > sort
     */
    private static final Comparator<DictDataDO> COMPARATOR_TYPE_AND_SORT = Comparator
            .comparing(DictDataDO::getDictType)
            .thenComparingInt(DictDataDO::getSort);

    /**
     * 排序 parentId > sort
     */
    private static final Comparator<DictDataDO> COMPARATOR_PARENT_AND_SORT = Comparator
            .comparing(DictDataDO::getParentId)
            .thenComparingInt(DictDataDO::getSort);

    @Resource
    private DictTypeService dictTypeService;

    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 查询所有字典
     * @return 返回状态为正常、且为根目录的列表数据
     */
    @Override
    public List<DictDataDO> getDictDatas() {
        List<DictDataDO> list = dictDataMapper.selectListOnbycache();
        list.sort(COMPARATOR_TYPE_AND_SORT);
        return list;
    }

    /**
     * 获取所有未删除的字典数据
     * @return 所有未删除的字典数据列表
     */
    @Override
    public List<DictDataDO> getAllDictDatas() {
        List<DictDataDO> list = dictDataMapper.selectList();
        list.sort(COMPARATOR_TYPE_AND_SORT);
        return list;
    }

    /**
     * 分页查询字典数据
     * @param reqVO 分页请求
     * @return 分页结果
     */
    @Override
    public List<DictDataRespVO> getDictDataList(DictDataPageReqVO reqVO) {
        List<DictDataDO> result = dictDataMapper.selectList(reqVO);
        if (result.isEmpty()) {
            return Collections.emptyList();
        }
        return getDictDataListWithChild(result,reqVO);
    }

    @Override
    public List<DictDataExcelVO> getDictDataExcel(DictDataPageReqVO reqVO) {
        return getDictDataExcelList(getDictDataList(reqVO));
    }

    /**
     * 处理list中所有的子字典数据
     * @param list 父字典数据集
     * @return 包含子字典数据的结果集
     */
    private List<DictDataRespVO> getDictDataListWithChild(List<DictDataDO> list,DictDataPageReqVO reqVO) {
        List<Long> idList = list.stream().map(DictDataDO::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        List<DictDataDO> childList = dictDataMapper.selectAllChildByStatus(idList,reqVO.getStatus());
        List<Long> childIdList = childList.stream().map(DictDataDO::getId).collect(Collectors.toList());
        // 筛选排除掉子字典数据
        list = list.stream().filter(l -> !childIdList.contains(l.getId()))
//                .filter(l -> Objects.isNull(reqVO.getStatus()) || Objects.equals(reqVO.getStatus(),l.getStatus()))
                .collect(Collectors.toList());
        // 先处理根字典
        List<DictDataDO> rootDictData = list.stream().filter(l -> l.getParentId() == 0).collect(Collectors.toList());
        List<DictDataDO> otherDictData = list.stream().filter(l -> l.getParentId() != 0).collect(Collectors.toList());
        // 封装结果
        List<DictDataRespVO> result = new ArrayList<>();
        for (DictDataDO dictData : rootDictData) {
            DictDataRespVO convert = DictDataConvert.INSTANCE.convert(dictData);
            dealChild(convert,childList,reqVO.getStatus());
            result.add(convert);
        }
        for (DictDataDO dictData : otherDictData) {
            DictDataRespVO convert = DictDataConvert.INSTANCE.convert(dictData);
            dealChild(convert,childList,reqVO.getStatus());
            result.add(convert);
        }
        return result;
    }

    /**
     * 递归处理每一层字典的子字典数据
     * @param respVO 当前处理的字典数据
     * @param children 所有的子字典数据集
     */
    private void dealChild(DictDataRespVO respVO, List<DictDataDO> children,Integer status) {
        if (CollUtil.isEmpty(children)) {
            return;
        }
        List<DictDataDO> child = children.stream()
                .filter(c -> NumberUtil.equals(c.getParentId(), respVO.getId()))
                .filter(c -> {
                    if (Objects.isNull(status)) {
                        return true;
                    } else {
                        return Objects.equals(status,c.getStatus());
                    }
                })
                .sorted(COMPARATOR_PARENT_AND_SORT)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(child)) {
            return;
        }
        List<DictDataRespVO> list = new ArrayList<>( child.size() );
        for (DictDataDO dataDO : child) {
            DictDataRespVO convert = DictDataConvert.INSTANCE.convert(dataDO);
            dealChild(convert,children,status);
            list.add(convert);
        }
        respVO.setChild(list);
    }

    /**
     * 封装字典数据导出字段
     * @param list 包含父子关系的字典数据结果集
     * @return 导出字典数据结果集
     */
    private List<DictDataExcelVO> getDictDataExcelList(List<DictDataRespVO> list) {
        List<DictDataExcelVO> result = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        list.forEach(vo -> dealChildToList(vo,result));
        return result;
    }

    /**
     * 递归取出字典数据导出字段的子字典
     * @param vo 单个包含父子关系的字典数据
     * @param result 导出字典数据结果集
     */
    private void dealChildToList(DictDataRespVO vo, List<DictDataExcelVO> result) {
        DictDataExcelVO excelVO = DictDataConvert.INSTANCE.convert(vo);
        result.add(excelVO);
        if (CollUtil.isNotEmpty(vo.getChild())) {
            vo.getChild().forEach(c -> dealChildToList(c,result));
        }
    }

    @Override
    public DictDataDO getDictData(Long id) {
        return dictDataMapper.selectById(id);
    }

    @Override
    public Long createDictData(DictDataCreateReqVO reqVO) {
        // 校验正确性
        checkCreateOrUpdate(null, reqVO.getValue(), reqVO.getDictType());

        // 插入字典类型
        DictDataDO dictData = DictDataConvert.INSTANCE.convert(reqVO);
        // 父字典设置默认值
        if (Objects.isNull(dictData.getParentId())) {
            dictData.setParentId(0L);
        }
        dictDataMapper.insert(dictData);
        return dictData.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDictData(DictDataUpdateReqVO reqVO) {
        // 校验正确性
        checkCreateOrUpdate(reqVO.getId(), reqVO.getValue(), reqVO.getDictType());
        if (Objects.isNull(reqVO.getParentId())) {
            reqVO.setParentId(0L);
        }
        // 校验父级字典
        checkParent(reqVO.getId(),reqVO.getParentId());
        // 更新字典类型
        DictDataDO updateObj = DictDataConvert.INSTANCE.convert(reqVO);
        dictDataMapper.updateById(updateObj);
        // 更新子字典状态
        List<DictDataDO> childList = dictDataMapper.selectAllChild(Collections.singletonList(reqVO.getId()));
        if (CollUtil.isNotEmpty(childList)) {
            dictDataMapper.update(new DictDataDO().setStatus(reqVO.getStatus()),
                    new LambdaQueryWrapperX<DictDataDO>().in(DictDataDO::getId,childList.stream().map(DictDataDO::getId).collect(Collectors.toList())));
        }

    }



    @Override
    public void deleteDictData(Long id) {
        // 校验是否存在
        checkDictDataExists(id);
        // 校验字典是否有子字典数据
        validateHasChildren(id);
        // 删除字典数据
        dictDataMapper.deleteById(id);
    }

    /**
     * 校验字典是否有子字典数据
     * @param id 字典数据id
     */
    private void validateHasChildren(Long id) {
        List<DictDataDO> list = dictDataMapper.selectAllChild(Collections.singletonList(id));
        if (CollUtil.isNotEmpty(list)) {
            throw exception(DICT_DATA_HAS_CHILDREN);
        }
    }

    @Override
    public long countByDictType(String dictType) {
        return dictDataMapper.selectCountByDictType(dictType);
    }


    private void checkCreateOrUpdate(Long id, String value, String dictType) {
        // 校验自己存在
        checkDictDataExists(id);
        // 校验字典类型有效
        checkDictTypeValid(dictType);
        // 校验字典数据的值的唯一性
        checkDictDataValueUnique(id, dictType, value);
    }

    @VisibleForTesting
    public void checkDictDataValueUnique(Long id, String dictType, String value) {
        DictDataDO dictData = dictDataMapper.selectByDictTypeAndValue(dictType, value);
        if (dictData == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典数据
        if (id == null) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
        if (!dictData.getId().equals(id)) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
    }

    @VisibleForTesting
    public void checkDictDataExists(Long id) {
        if (id == null) {
            return;
        }
        DictDataDO dictData = dictDataMapper.selectById(id);
        if (dictData == null) {
            throw exception(DICT_DATA_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    public void checkDictTypeValid(String type) {
        DictTypeDO dictType = dictTypeService.getDictType(type);
        if (dictType == null) {
            throw exception(DICT_TYPE_NOT_EXISTS);
        }
        if (!CommonStatusEnum.ENABLE.getStatus().equals(dictType.getStatus())) {
            throw exception(DICT_TYPE_NOT_ENABLE);
        }
    }

    @Override
    public void validDictDatas(String dictType, Collection<String> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        Map<String, DictDataDO> dictDataMap = CollectionUtils.convertMap(
                dictDataMapper.selectByDictTypeAndValues(dictType, values), DictDataDO::getValue);
        // 校验
        values.forEach(value -> {
            DictDataDO dictData = dictDataMap.get(value);
            if (dictData == null) {
                throw exception(DICT_DATA_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(dictData.getStatus())) {
                throw exception(DICT_DATA_NOT_ENABLE, dictData.getLabel());
            }
        });
    }

    private void checkParent(Long id,Long parentId) {
        if (Objects.equals(id,parentId)) {
            throw exception(DICT_DATA_PARENT_IS_SELF);
        }
    }

    @Override
    public DictDataDO getDictData(String dictType, String value) {
        return dictDataMapper.selectByDictTypeAndValue(dictType, value);
    }

    @Override
    public DictDataDO parseDictData(String dictType, String label) {
        return dictDataMapper.selectByDictTypeAndLabel(dictType, label);
    }

    @Override
    public List<DictDataDO> getDictDatasByParentId(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<DictDataDO> list = dictDataMapper.selectChildListOnByParentId(ids);
        list.sort(COMPARATOR_PARENT_AND_SORT);
        return list;
    }

    @Override
    public List<DictDataDO> getDictDatas(DictDataSimpleReqVO reqVO) {
        List<DictDataDO> list = dictDataMapper.selectListOn(reqVO);
        list.sort(COMPARATOR_PARENT_AND_SORT);
        return list;
    }

    @Override
    public List<DictDataDO> getDictDatasByTypes(List<String> dictTypes) {
        LambdaQueryWrapperX<DictDataDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.in(DictDataDO::getDictType, dictTypes);
        lambdaQueryWrapperX.eq(DictDataDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        lambdaQueryWrapperX.orderByAsc(DictDataDO::getSort);
        return dictDataMapper.selectList(lambdaQueryWrapperX);
    }

    @Override
    public void createDictTypeBatch(List<DictDataDO> toInsert) {
        dictDataMapper.insertBatch(toInsert);
    }

    @Override
    public void deleteDictTypeBatch(List<Long> idsToDelete) {
        idsToDelete.forEach(it-> dictDataMapper.deletedictById(it));
    }

    @Override
    public void updateDictTypeBatch(List<DictDataDO> toUpdate) {
        for (DictDataDO dictDataDO : toUpdate) {
            dictDataMapper.updateById(dictDataDO);
        }

    }
}
