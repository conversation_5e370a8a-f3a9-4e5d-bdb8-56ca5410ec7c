package com.unicom.swdx.module.system.dal.dataobject.yjs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class OldUsersResponse {


    @JsonProperty("ResponseParam")
    private ResponseParamDTO responseParam;
    @JsonProperty("MessageStatus")
    private String messageStatus;
    @JsonProperty("MessageSequence")
    private String messageSequence;
    @JsonProperty("Remark")
    private String remark;

    @NoArgsConstructor
    @Data
    public static class ResponseParamDTO {
        @JsonProperty("ResourceInfos")
        private List<ResourceInfosDTO> resourceInfos;

        @NoArgsConstructor
        @Data
        public static class ResourceInfosDTO {
            @JsonProperty("DataInfo")
            private List<List<String>> dataInfo;
        }
    }
}


