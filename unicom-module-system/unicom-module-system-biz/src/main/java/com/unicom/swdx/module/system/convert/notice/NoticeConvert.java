package com.unicom.swdx.module.system.convert.notice;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticeCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticeRespVO;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticeUpdateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.notice.NoticeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface NoticeConvert {

    NoticeConvert INSTANCE = Mappers.getMapper(NoticeConvert.class);

    PageResult<NoticeRespVO> convertPage(PageResult<NoticeDO> page);
    List<NoticeRespVO> convertList(List<NoticeDO> list);
    NoticeRespVO convert(NoticeDO bean);

    NoticeDO convert(NoticeUpdateReqVO bean);

    NoticeDO convert(NoticeCreateReqVO bean);

}
