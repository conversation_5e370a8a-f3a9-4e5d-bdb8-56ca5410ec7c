package com.unicom.swdx.module.system.api.permission;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Set;

import static com.unicom.swdx.framework.common.pojo.CommonResult.*;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class PermissionApiImpl implements PermissionApi {

    @Resource
    private PermissionService permissionService;

    @Override
    public CommonResult<Set<Long>> getUserRoleIdListByRoleIds(Collection<Long> roleIds) {
        return success(permissionService.getUserRoleIdListByRoleIds(roleIds));
    }

    @Override
    public CommonResult<Boolean> hasAnyPermissions(Long userId, String... permissions) {
        return success(permissionService.hasAnyPermissions(userId, permissions));
    }

    @Override
    public CommonResult<Boolean> hasAnyRoles(Long userId, String... roles) {
        return success(permissionService.hasAnyRoles(userId, roles));
    }

    @Override
    public CommonResult<DeptDataPermissionRespDTO> getDeptDataPermission(Long userId) {
        return success(permissionService.getDeptDataPermission(userId));
    }

    @Override
    public CommonResult<DeptDataPermissionRespDTO> getDeptDataPermissionWithClientCode(Long userId, String code) {
        return success(permissionService.getDeptDataPermission(userId,code));
    }

    @Override
    public Set<Long> getMenuIdSetAuthorized(Long userId) {
        return permissionService.getMenuIdSetAuthorizedByUserId(userId);
    }

    @Override
    public Set<String> getPermission(Long userId) {
        return permissionService.getPermission(userId);
    }

    @Override
    public void assignUserRoleGroup(Long userId, String name) {
        permissionService.hrAssignUserRole(userId,name);
    }

}
