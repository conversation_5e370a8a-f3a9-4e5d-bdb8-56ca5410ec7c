package com.unicom.swdx.module.system.service.shortcut;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.api.oauth2.OAuth2ClientApi;
import com.unicom.swdx.module.system.api.oauth2.dto.OAuth2ClientDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutClientRespVo;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutUpdateReqVO;
import com.unicom.swdx.module.system.convert.shortcut.ShortcutConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;
import com.unicom.swdx.module.system.dal.dataobject.shortcut.ShortcutDO;
import com.unicom.swdx.module.system.dal.mysql.shortcut.ShortcutMapper;
import com.unicom.swdx.module.system.dal.mysql.shortcut.ShortcutUserMapper;
import com.unicom.swdx.module.system.enums.permission.MenuTypeEnum;
import com.unicom.swdx.module.system.service.permission.MenuService;
import com.unicom.swdx.module.system.service.shortcut.dto.MenuShortcutDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.SHORTCUT_NOT_EXISTS;


/**
 * 快捷入口 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ShortcutServiceImpl implements ShortcutService {

    @Resource
    private ShortcutMapper shortcutMapper;

    @Resource
    private ShortcutUserMapper shortcutUserMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private MenuService menuService;

    @Resource
    private OAuth2ClientApi oAuth2ClientApi;

    @Override
    public Long createShortcut(ShortcutCreateReqVO createReqVO) {
        // 插入
        ShortcutDO shortcut = ShortcutConvert.INSTANCE.convert(createReqVO);
        shortcutMapper.insert(shortcut);
        // 返回
        return shortcut.getId();
    }

    @Override
    public void updateShortcut(ShortcutUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateShortcutExists(updateReqVO.getId());
        // 更新
        ShortcutDO updateObj = ShortcutConvert.INSTANCE.convert(updateReqVO);
        shortcutMapper.updateById(updateObj);
    }

    private void validateShortcutExists(Long id) {
        if (shortcutMapper.selectById(id) == null) {
            throw exception(SHORTCUT_NOT_EXISTS);
        }
    }

    @Override
    public List<ShortcutClientRespVo> getShortcutList() {
        List<ShortcutDO> shortcutDOS = shortcutMapper.selectList();
        // 过滤用户有权限的状态开启的快捷入口
        Set<Long> menuIds = permissionApi.getMenuIdSetAuthorized(getLoginUserId());
   //     menuIds.add(3211l);
        shortcutDOS = shortcutDOS.stream()
                .filter(m -> menuIds.contains(m.getMenuId()))
                .filter(m -> Objects.equals(CommonStatusEnum.ENABLE.getStatus(),m.getStatus()))
                .collect(Collectors.toList());

        Set<Long> clientIds = shortcutDOS.stream().map(ShortcutDO::getClientId).collect(Collectors.toSet());
        List<OAuth2ClientDTO> clientList = oAuth2ClientApi.getAll();
        List<Long> clientIdList = clientList.stream().filter(c -> clientIds.contains(c.getId())).sorted(Comparator.comparing(OAuth2ClientDTO::getSort,Comparator.nullsLast(Integer::compareTo))).map(OAuth2ClientDTO::getId).collect(Collectors.toList());
        List<ShortcutClientRespVo> shortcutClientRespVos = new ArrayList<>();
        for (Long id : clientIdList) {
            ShortcutClientRespVo shortcutClientRespVo = new ShortcutClientRespVo();
            shortcutClientRespVo.setId(id);
            shortcutClientRespVo.setShortcutRespVOList(ShortcutConvert.INSTANCE.convertList(shortcutDOS.stream()
                    .filter(s -> Objects.equals(s.getClientId(),id))
                    .sorted(Comparator.comparing(ShortcutDO::getSort , Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList())));
            if (CollUtil.isNotEmpty(shortcutClientRespVo.getShortcutRespVOList())) {
                shortcutClientRespVos.add(shortcutClientRespVo);
            }
        }
        return shortcutClientRespVos;
    }

    /**
     *
     */
    @SneakyThrows
    @Override
    @Async
    public void refreshShortcut() {
        log.info("[refresh] 刷新快捷入口");
        // 休眠2秒，等菜单缓存刷新完成再发送刷新快捷入口消息
        Thread.sleep(2000L);
        List<MenuShortcutDTO> reqDTO = this.getMenuShortcutList();
        List<ShortcutDO> oldList = shortcutMapper.selectList();
        Map<Long, ShortcutDO> oldMap = CollectionUtils.convertMap(oldList, ShortcutDO::getMenuId);
        Set<Long> oldMenuIds = oldMap.keySet();
        Map<Long, MenuShortcutDTO> newMap = CollectionUtils.convertMap(reqDTO, MenuShortcutDTO::getId);
        Set<Long> newMenuIds = newMap.keySet();
        // 新增的快捷入口
        Collection<Long> createIds = CollUtil.subtract(newMenuIds, oldMenuIds);
        if (CollUtil.isNotEmpty(createIds)) {
            for (Long id : createIds) {
                MenuShortcutDTO newShortcut = newMap.get(id);
                ShortcutDO shortcutDO = ShortcutDO.builder()
                        .clientId(newShortcut.getClientId())
                        .name(newShortcut.getName())
                        .linkUrl(newShortcut.getPath())
                        .svgIcon(newShortcut.getShortcutIcon())
                        .sort(newShortcut.getSort())
                        .menuId(id)
                        .status(newShortcut.getStatus())
                        .build();
                shortcutMapper.insert(shortcutDO);
            }
        }
        // 删除的快捷入口
        Collection<Long> deleteIds = CollUtil.subtract(oldMenuIds,newMenuIds);
        if (CollUtil.isNotEmpty(deleteIds)) {
            Set<Long> oldDeletedIds = new HashSet<>();
            for (Long deleteId : deleteIds) {
                oldDeletedIds.add(oldMap.get(deleteId).getId());
            }
            shortcutMapper.deleteBatchIds(oldDeletedIds);
            // 同时删除用户关联的
            shortcutUserMapper.deleteByShortcutIds(oldDeletedIds);
        }
        // 修改的快捷入口
        Collection<Long> updateIds = CollUtil.subtract(newMenuIds, createIds);
        if (CollUtil.isNotEmpty(updateIds)) {
            for (Long id : updateIds) {
                ShortcutDO o = oldMap.get(id);
                MenuShortcutDTO n = newMap.get(id);
                if (equalsShortcut(o,n)) {
                    continue;
                }
                o.setName(n.getName());
                o.setSort(n.getSort());
                o.setLinkUrl(n.getPath());
                o.setClientId(n.getClientId());
                o.setSvgIcon(n.getShortcutIcon());
                o.setStatus(n.getStatus());
                shortcutMapper.updateById(o);
            }
        }
    }

    /**
     * 获取菜单的快捷入口
     * @return 快捷入口
     */
    private List<MenuShortcutDTO> getMenuShortcutList() {
        List<MenuDO> shortcutMenus = menuService.getAllShortcutMenus();
        if (CollUtil.isEmpty(shortcutMenus)) {
            return Collections.emptyList();
        }
        List<MenuShortcutDTO> result = new ArrayList<>();
        for (MenuDO menu : shortcutMenus) {
            MenuShortcutDTO dto = new MenuShortcutDTO();
            dto.setId(menu.getId());
            dto.setName(menu.getName());
            dto.setClientId(menu.getClientId());
            dto.setShortcutIcon(menu.getShortcutIcon());
            dto.setStatus(menu.getStatus());
            dto.setSort(menu.getSort());
            if (MenuTypeEnum.MENU.equals(menu.getType())) {
                // 菜单需要拼接父级path
                dto.setPath(menuService.getShortcutPath(menu.getId()));
            } else if (MenuTypeEnum.BUTTON.equals(menu.getType())) {
                dto.setPath(menu.getPath());
            }
            result.add(dto);
        }
        return result;
    }

    /**
     * 判断两个快捷入口是否相等
     * @param o 原来的
     * @param n 新来的
     * @return 是否相等
     */
    private boolean equalsShortcut(ShortcutDO o, MenuShortcutDTO n) {
        return Objects.equals(n.getClientId(),o.getClientId())
                && Objects.equals(n.getName(),o.getName())
                && Objects.equals(n.getPath(),o.getLinkUrl())
                && Objects.equals(n.getStatus(),o.getStatus())
                && Objects.equals(n.getSort(),o.getSort())
                && Objects.equals(n.getShortcutIcon(),o.getSvgIcon());
    }

}
