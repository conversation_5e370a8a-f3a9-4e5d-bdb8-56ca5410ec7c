package com.unicom.swdx.module.system.controller.admin.permission;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.permission.vo.menu.*;
import com.unicom.swdx.module.system.convert.permission.MenuConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;
import com.unicom.swdx.module.system.service.permission.MenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "管理后台 - 菜单")
@RestController
@RequestMapping("/system/menu")
@Validated
public class MenuController {

    @Resource
    private MenuService menuService;

    @PostMapping("/create")
    @ApiOperation("创建菜单")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:menu:create')")
    public CommonResult<Long> createMenu(@Valid @RequestBody MenuCreateReqVO reqVO) {
        Long menuId = menuService.createMenu(reqVO);
        return success(menuId);
    }

    @PostMapping("/update")
    @ApiOperation("修改菜单")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:menu:update')")
    public CommonResult<Boolean> updateMenu(@Valid @RequestBody MenuUpdateReqVO reqVO) {
        menuService.updateMenu(reqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除菜单")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "角色编号", required= true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:menu:delete')")
    public CommonResult<Boolean> deleteMenu(@RequestParam("id") Long id) {
        menuService.deleteMenu(id);
        return success(true);
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取菜单列表", notes = "用于【菜单管理】界面")
    public CommonResult<List<MenuRespVO>> getMenus(MenuListReqVO reqVO) {
        List<MenuDO> list = menuService.getMenus(reqVO);
        list.sort(Comparator.comparing(MenuDO::getSort));
        return success(MenuConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/get")
    @ApiOperation("获取菜单信息")
    public CommonResult<MenuRespVO> getMenu(Long id) {
        MenuDO menu = menuService.getMenu(id);
        return success(MenuConvert.INSTANCE.convert(menu));
    }

    @GetMapping("/list-role-simple")
    @ApiOperation(value = "根据角色id获取角色拥有的菜单精简信息列表")
    public CommonResult<List<MenuSimpleRespVO>> getSimpleClientMenusRole(@RequestParam("id") Long roleId) {
        List<MenuDO> list = menuService.getClientMenusByRoleId(roleId);
        // 排序后，返回给前端
        list.sort(Comparator.comparing(MenuDO::getSort));
        return success(MenuConvert.INSTANCE.convertList02(list));
    }

//    @GetMapping("/list-client-simple")
//    @ApiOperation(value = "根据应用id获取菜单精简信息列表", notes = "只查询出当前机构有权限的菜单")
//    public CommonResult<List<MenuSimpleRespVO>> getSimpleClientMenus(@RequestParam("clientId") Long clientId) {
//        List<MenuDO> list = menuService.getClientMenus(clientId);
//        // 排序后，返回给前端
//        list.sort(Comparator.comparing(MenuDO::getSort));
//        return success(MenuConvert.INSTANCE.convertList02(list));
//    }

//    @GetMapping("/list-all-simple")
//    @ApiOperation(value = "获取菜单精简信息列表", notes = "只包含被开启的菜单，用于【角色分配菜单】功能的选项。" +
//            "在多租户的场景下，会只返回租户所在套餐有的菜单")
//    public CommonResult<List<MenuSimpleRespVO>> getSimpleMenus() {
//        // 获得菜单列表，只要开启状态的
//        MenuListReqVO reqVO = new MenuListReqVO();
//        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
//        List<MenuDO> list = menuService.getTenantMenus(reqVO);
//        // 排序后，返回给前端
//        list.sort(Comparator.comparing(MenuDO::getSort));
//        return success(MenuConvert.INSTANCE.convertList02(list));
//    }

}
