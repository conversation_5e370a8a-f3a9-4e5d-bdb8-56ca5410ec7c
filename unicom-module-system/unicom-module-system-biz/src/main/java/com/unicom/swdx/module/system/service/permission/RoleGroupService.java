package com.unicom.swdx.module.system.service.permission;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleClientSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.*;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupDO;

import java.util.List;
import java.util.Set;

public interface RoleGroupService extends IService<RoleGroupDO>{
    Long createRoleGroup(RoleGroupCreateReqVO reqVO);

    void updateRoleGroup(RoleGroupUpdateReqVO reqVO);

    void deleteRoleGroup(Long id);

    PageResult<RoleGroupRespVO> getRoleGroupPage(RoleGroupPageReqVO reqVO);

    RoleGroupRespVO getRoleGroup(Long id);

    RoleGroupUsersRespVO getRoleGroupUsersPage(RoleGroupUsersPageReqVO pageReqVO);

    void updateRoleGroupStatus(Long id, Integer status);


    List<RoleClientSimpleRespVO> getSimpleRoleList(Long groupId);

    List<Long> getRoleGroupUsers(Long id);
    Long getRoleGroupByName(String name);
}
