package com.unicom.swdx.module.system.controller.xcx.auth.vo;

import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/23 22:14
 **/
@Data
@ApiModel("管理后台 - 小程序登录 Response VO")
public class WxXcxAuthLoginRespVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    private Long userId;

    @ApiModelProperty(value = "访问令牌", required = true, example = "happy")
    private String accessToken;

    @ApiModelProperty(value = "刷新令牌", required = true, example = "nice")
    private String refreshToken;

    @ApiModelProperty(value = "过期时间", required = true)
    private LocalDateTime expiresTime;

    List<AuthLoginRespVO> authLoginRespVOList;

}
