package com.unicom.swdx.module.system.service.notice;

import cn.hutool.core.util.StrUtil;
import com.google.common.annotations.VisibleForTesting;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticeCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticeUpdateReqVO;
import com.unicom.swdx.module.system.convert.notice.NoticeConvert;
import com.unicom.swdx.module.system.dal.dataobject.notice.NoticeDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.notice.NoticeMapper;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Collection;

import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertSet;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.NOTICE_NOT_FOUND;

/**
 * 通知公告 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private NoticeMapper noticeMapper;

    @Resource
    private AdminUserService userService;
    @Override
    public Long createNotice(NoticeCreateReqVO reqVO) {
        NoticeDO notice = NoticeConvert.INSTANCE.convert(reqVO);
        noticeMapper.insert(notice);
        return notice.getId();
    }

    @Override
    public void updateNotice(NoticeUpdateReqVO reqVO) {
        // 校验是否存在
        this.checkNoticeExists(reqVO.getId());
        // 更新通知公告
        NoticeDO updateObj = NoticeConvert.INSTANCE.convert(reqVO);
        noticeMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotice(Long id) {
        // 校验是否存在
        this.checkNoticeExists(id);
        // 删除通知公告
        noticeMapper.deleteById(id);
    }

    @Override
    public PageResult<NoticeDO> pageNotices(NoticePageReqVO reqVO) {
        // 处理基于用户昵称的查询
        Collection<Long> userIds = null;
        if (StrUtil.isNotBlank(reqVO.getTitle())) {
            userIds = convertSet(userService.getUsersByNickname(reqVO.getTitle()), AdminUserDO::getId);
        }
        // 查询分页
        return noticeMapper.selectPage(reqVO, userIds);

    }

    @Override
    public NoticeDO getNotice(Long id) {
        return noticeMapper.selectById(id);
    }

    @VisibleForTesting
    public void checkNoticeExists(Long id) {
        if (id == null) {
            return;
        }
        NoticeDO notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw ServiceExceptionUtil.exception(NOTICE_NOT_FOUND);
        }
    }

}
