package com.unicom.swdx.module.system.controller.xcx.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/23 22:18
 **/
@Data
@ApiModel("管理后台 - 小程序登录 Request VO")
public class WxXcxAuthLoginReqVO {

    @ApiModelProperty("二维码链接携带的唯一码")
    private String uniqueCode;

    @ApiModelProperty("获取手机号的code")
    private String mobileCode;

    @ApiModelProperty("获取openid的code")
    private String loginCode;

}
