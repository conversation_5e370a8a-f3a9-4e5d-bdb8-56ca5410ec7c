package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("首页 - 日程创建 Request VO")
@Data
public class ScheduleCreateReqVO extends ScheduleBaseVO{

    /**
     * 流程审批结束创建日程
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date endTime;

    @ApiModelProperty(value = "类型(1为外出报告)")
    private Integer type;

    @ApiModelProperty(value = "流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "抄送人和参与人")
    private List<Long> userIds;

    @ApiModelProperty(value = "汇总年份")
    private Integer year;

    @ApiModelProperty(value = "汇总周")
    private Integer week;

    @ApiModelProperty(value = "中心工作id")
    private Integer inforId;

    //@NotEmpty(message = "任务名称不能为空")
    @ApiModelProperty(value = "任务名称")
    private String taskname;

    @ApiModelProperty(value = "是否是年度的")
    private Integer yeartag;
}
