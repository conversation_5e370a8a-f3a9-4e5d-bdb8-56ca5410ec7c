package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

@ApiModel("管理后台 - 消息模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MessagePageReqVO extends PageParam {
    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "模板名称", example = "sk")
    @Length(min = 0,max = 30,message = "模板名称字数限制")
    private String name;

    @ApiModelProperty(value = "模板内容")
    @Length(min = 0,max = 1000,message = "模板内容字数限制")
    private String content;

}
