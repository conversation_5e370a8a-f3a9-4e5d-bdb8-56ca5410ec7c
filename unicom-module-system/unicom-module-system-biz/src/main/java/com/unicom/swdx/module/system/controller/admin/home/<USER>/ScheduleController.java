package com.unicom.swdx.module.system.controller.admin.home.schedule;

import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.home.schedule.vo.*;
import com.unicom.swdx.module.system.convert.schedule.ScheduleConvert;
import com.unicom.swdx.module.system.dal.dataobject.schedule.ScheduleDO;
import com.unicom.swdx.module.system.service.schedule.ScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

@Api(tags = "首页 - 日程")
@RestController
@RequestMapping("/system/home/<USER>")
@Validated
public class ScheduleController {

    @Resource
    private ScheduleService scheduleService;

    @PostMapping("/create")
    @ApiOperation("创建日程")
    @OperateLog(type = CREATE)
    public CommonResult<Boolean> createSchedule(@Valid @RequestBody ScheduleCreateReqVO createReqVO) {
        scheduleService.createSchedule(createReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation("更新日程")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateSchedule(@Valid @RequestBody ScheduleUpdateReqVO updateReqVO) {
        scheduleService.updateSchedule(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除日程")
    @OperateLog(type = DELETE)
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> deleteSchedule(@RequestParam("id") Long id) {
        scheduleService.deleteSchedule(id);
        return success(true);
    }

    @GetMapping("/listByDay")
    @ApiOperation("按日期查询日程详情列表")
    public CommonResult<List<ScheduleWholeRespVO>> list(@Valid ScheduleQueryReqVO reqVO) {
        return success(ScheduleConvert.INSTANCE.convertWholeList(scheduleService.getList(reqVO)));
    }

    @GetMapping("/listByMonth")
    @ApiOperation("按月获取日程简介")
    public CommonResult<List<CalendarWnlScheduleRespVO>> listByMonth(@Valid ScheduleQueryReqVO reqVO) {
        return success(scheduleService.getListByMonth(reqVO));
    }

    @PostMapping("/create-user")
    @ApiOperation("创建日程（指定人）")
    @OperateLog(type = CREATE)
    public CommonResult<Boolean> createScheduleByUser(@Valid @RequestBody ScheduleCreateReqVO createReqVO) {
        if(createReqVO.getUserId() == null){
            throw exception(new ErrorCode(403, "用户id不能为空"));
        }
        scheduleService.createScheduleByUser(createReqVO);
        return success(true);
    }
}
