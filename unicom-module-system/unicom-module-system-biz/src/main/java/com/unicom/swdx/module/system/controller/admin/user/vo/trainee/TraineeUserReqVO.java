package com.unicom.swdx.module.system.controller.admin.user.vo.trainee;

import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

@ApiModel("学员用户创建 Request VO")
@Data
public class TraineeUserReqVO {

    private String tenantId;

    private Long userId;

    private String mobile;

    private String nickname;

    private Integer sex;

    private String traineeId;

}
