package com.unicom.swdx.module.system.controller.admin.dept.vo.post;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value = "管理后台 - 岗位导出 Request VO", description = "参数和 PostExcelVO 是一致的")
@Data
public class PostExportReqVO {

    @ApiModelProperty(value = "岗位编号", example = "1")
    private long id;

    @ApiModelProperty(value = "岗位标识", example = "unicom", notes = "模糊匹配")
    private String code;

    @ApiModelProperty(value = "岗位名称", example = "sk", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "岗位排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "所属机构", example = "unicom", notes = "模糊匹配")
    private String tenant;

    @ApiModelProperty(value = "展示状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "是否展示", example = "true")
    private Boolean displayState;

    @ApiModelProperty(value = "创建时间", example = "2022-05-02 07:25:24")
    private LocalDateTime createTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
