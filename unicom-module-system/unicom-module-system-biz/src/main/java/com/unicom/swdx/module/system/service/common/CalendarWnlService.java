package com.unicom.swdx.module.system.service.common;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.system.dal.dataobject.calendarwnl.CalendarWnlDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 万年历 Service 接口
 *
 * 提供数据库中保存的万年历、节假日信息的接口
 *
 */
public interface CalendarWnlService extends IService<CalendarWnlDO> {

//    /**
//     * 获得年度日历信息
//     * @param date 日期
//     * @param timeScale 时间尺度 year month day
//     * @return 精确到时间尺度的日历信息
//     */
//    CommonResult<List<CalendarWnlRespDTO>> getAnnualCalendar(LocalDate date, String timeScale);

    /**
     * 获得指定日期范围的日历信息
     * @param startDate 起始日期
     * @param endDate 结束日期
     */
    List<CalendarWnlDO> getCalendarInRange(LocalDate startDate, LocalDate endDate);

}
