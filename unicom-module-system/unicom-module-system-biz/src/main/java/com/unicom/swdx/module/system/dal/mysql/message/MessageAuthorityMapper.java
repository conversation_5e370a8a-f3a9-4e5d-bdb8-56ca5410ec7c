package com.unicom.swdx.module.system.dal.mysql.message;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityPageReqVO;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessagePageReqVO;
import com.unicom.swdx.module.system.convert.message.MessageAuthorityConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;

@Mapper
public interface MessageAuthorityMapper extends BaseMapperX<MessageAuthorityDO> {

    @Select("select count(*) from system_message_authority where user_id = #{id} and status = 0 and deleted != true")
    Integer getById(@Param("id") Long id);

    @Select("select id from system_message_authority where user_id = #{id} and status = 0 and deleted != true Limit 1")
    Long getIdByUserId(@Param("id") Long id);

    @Select("select data_scope_ids from system_message_authority where user_id = #{id} and status = 0 and deleted != true")
    String getIdsById(@Param("id") Long id);

    //删除多个

    //查看
    default PageResult<MessageAuthorityDO> selectPage(MessageAuthorityPageReqVO reqVO,Boolean hasSuperAdmin) {
        LambdaQueryWrapperX<MessageAuthorityDO> query = new LambdaQueryWrapperX<MessageAuthorityDO>()
                .likeIfPresent(MessageAuthorityDO::getName,reqVO.getName())
                //状态为启用
//                .eq(MessageAuthorityDO::getStatus,0)
                .orderByDesc(MessageAuthorityDO::getCreateTime);
        if(!hasSuperAdmin){
            query.eq(MessageAuthorityDO::getTenantId,getTenantId());
        }
        return selectPage(reqVO, query);
    }
    //更改


}
