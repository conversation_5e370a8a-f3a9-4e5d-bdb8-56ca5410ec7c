package com.unicom.swdx.module.system.service.sms;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.sms.vo.log.SmsLogExportReqVO;
import com.unicom.swdx.module.system.controller.admin.sms.vo.log.SmsLogPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.sms.SmsLogDO;
import com.unicom.swdx.module.system.dal.mysql.sms.SmsLogMapper;
import com.unicom.swdx.module.system.enums.sms.SmsReceiveStatusEnum;
import com.unicom.swdx.module.system.enums.sms.SmsSendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 短信日志 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsLogServiceImpl implements SmsLogService {

    @Resource
    private SmsLogMapper smsLogMapper;

    @Override
    public Long createSmsLog(String mobile, Long userId,
                             String templateId, Map<String, Object> templateParams) {
        SmsLogDO.SmsLogDOBuilder logBuilder = SmsLogDO.builder();
        // 根据是否要发送，设置状态
        logBuilder.sendStatus(SmsSendStatusEnum.INIT.getStatus());
        // 设置手机相关字段
        logBuilder.mobile(mobile).userId(userId);
        // 设置模板相关字段
        logBuilder.templateCode(templateId);
        logBuilder.templateParams(templateParams);
        logBuilder.sendTime(LocalDateTime.now());
        // 设置接收相关字段
        //logBuilder.apiReceiveMsg(receiveMsg);
        // 插入数据库
        SmsLogDO logDO = logBuilder.build();
        if (Objects.isNull(userId)) {
            // 设置默认创建人为超管
            logDO.setCreator("1");
        } else {
            logDO.setCreator(userId.toString());
        }

        smsLogMapper.insert(logDO);
        return logDO.getId();
    }

    @Override
    public void updateSmsSendResult(Long id, Integer sendCode, String sendMsg,
                                    String apiSendCode, String apiSendMsg,
                                    String apiRequestId, String apiSerialNo) {
        SmsSendStatusEnum sendStatus = CommonResult.isSuccess(sendCode) ?
                SmsSendStatusEnum.SUCCESS : SmsSendStatusEnum.FAILURE;
        smsLogMapper.updateById(SmsLogDO.builder().id(id).sendStatus(sendStatus.getStatus())
                .sendTime(LocalDateTime.now()).sendCode(sendCode).sendMsg(sendMsg)
                .apiSendCode(apiSendCode).apiSendMsg(apiSendMsg)
                .apiRequestId(apiRequestId).apiSerialNo(apiSerialNo).build());
    }

    @Override
    public void updateSmsReceiveResult(Long id, Boolean success, LocalDateTime receiveTime,
                                       String apiReceiveCode, String apiReceiveMsg) {
        SmsReceiveStatusEnum receiveStatus = Objects.equals(success, true) ?
                SmsReceiveStatusEnum.SUCCESS : SmsReceiveStatusEnum.FAILURE;
        smsLogMapper.updateById(SmsLogDO.builder().id(id).receiveStatus(receiveStatus.getStatus())
                .receiveTime(receiveTime).apiReceiveCode(apiReceiveCode).apiReceiveMsg(apiReceiveMsg).build());
    }

    @Override
    public PageResult<SmsLogDO> getSmsLogPage(SmsLogPageReqVO pageReqVO) {
        return smsLogMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SmsLogDO> getSmsLogList(SmsLogExportReqVO exportReqVO) {
        return smsLogMapper.selectList(exportReqVO);
    }

    @Override
    public void updateSmsLog(Long smsLogId, boolean sendStatus, String body) {
        smsLogMapper.updateById(SmsLogDO.builder()
                        .id(smsLogId)
                        .sendStatus(sendStatus ? SmsSendStatusEnum.SUCCESS.getStatus() : SmsSendStatusEnum.FAILURE.getStatus())
                        .receiveTime(LocalDateTime.now())
                        .apiReceiveMsg(body)
                .build());
    }

}
