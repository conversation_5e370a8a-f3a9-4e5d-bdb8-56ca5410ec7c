package com.unicom.swdx.module.system.service.message;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ValidationUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.controller.admin.message.vo.send.*;
import com.unicom.swdx.module.system.convert.message.MessageSendConvert;
import com.unicom.swdx.module.system.convert.message.TimedTaskConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageSendDO;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.PostMapper;
import com.unicom.swdx.module.system.dal.mysql.message.MessageSendMapper;
import com.unicom.swdx.module.system.dal.mysql.message.TimedTaskMapper;
import com.unicom.swdx.module.system.dal.mysql.oaNotice.OaNoticeMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.job.smsjob.QueueTask;
import com.unicom.swdx.module.system.service.messagebase.MessageBaseService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.SEND_TIME_BEFOR;

/**
 * 机构 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MessageSendServiceImpl extends ServiceImpl<MessageSendMapper, MessageSendDO> implements MessageSendService {

    @Resource
    private MessageSendMapper messageSendMapper;
    @Resource
    private AdminUserMapper userMapper;
    @Resource
    private PostMapper postMapper;
    @Resource
    private MessageBaseService messageBaseService;
    @Resource
    private TimedTaskMapper timedTaskMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private AdminUserService userService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private RoleService roleService;

    @Resource
    private OaNoticeMapper oaNoticeMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMessageSend(MessageSendCreateReqVO createReqVO) {

        MessageSendDO message = MessageSendConvert.INSTANCE.convert(createReqVO);
        List<String> mobiles = new ArrayList<>();
        String mss = "";
        if(1==message.getSendMode()){
            Set<Long> userIds = message.getReceivingPersonIds();
            for (Long userId : userIds) {
                AdminUserDO user = userMapper.selectById(userId);
                if(user!=null){
                    mobiles.add(user.getMobile());
                }
            }
        }
        if(2==message.getSendMode()) {
            mobiles = createReqVO.getReceivingMobile();
        }
        if(1==message.getSendContentMode()){
            mss = createReqVO.getTemplateContent();
        }
        if(2==message.getSendContentMode()){
            mss = createReqVO.getMessageContent();
        }
        List<String> names = new ArrayList<>();
        Set<Long> ids = new HashSet<>();

        for(String mobile:mobiles){
            AdminUserDO user =userMapper.selectByMobile(mobile);
            if(user!=null){
                names.add(user.getNickname());
                ids.add(user.getId());
            }
        }
        message.setReceivingMobile(mobiles);
        message.setReceivingPersonNames(names);
        message.setReceivingPersonIds(ids);
        message.setTenantId(getTenantId());
        if(!message.getIfTimedSend()){
            if(createReqVO.getMessageChannel()){

                messageSendMapper.insert(message);

                int countSuccess = 0;
                for(String mobile:mobiles){

                    TimedTaskDO timedTaskDO = new TimedTaskDO();
                    timedTaskDO.setSendId(message.getId());
                    timedTaskDO.setMessage(mss);
                    timedTaskDO.setSendTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
                    timedTaskDO.setPhone(ListUtil.of(mobile));
                    timedTaskDO.setTitle(createReqVO.getTemplateName());
                    if(ValidationUtils.isMobile(mobile)){
                        messageBaseService.sendSingleMessage(mobile, "【湖南省委党校】"+mss);
                        countSuccess++;
                        timedTaskDO.setSuccessMessage(true);
                    }else{
                        timedTaskDO.setFailMessage(true);
                    }
                    AdminUserDO user =userMapper.selectByMobile(mobile);
                    if(user!=null){
                        timedTaskDO.setReceiver(user.getNickname());
                    }
                    timedTaskDO.setStatus(true);
                    timedTaskMapper.insert(timedTaskDO);
                }
                message.setMessageSuccess(countSuccess);
                message.setMessageFail(mobiles.size()-countSuccess);
                messageSendMapper.updateById(message);
            }
            if(createReqVO.getNoticeChannel()){
//                调用通知公告接口(3.14注释过)
//                OaNoticeDO notice = new OaNoticeDO();
//                notice.setContent(mss);
//                notice.setTitle(createReqVO.getTemplateName());
//
//                int countFail = 0;
//                StringBuilder sb0 = new StringBuilder();
//                sb0.append("|");
//                for(String mobile:mobiles){
//                    AdminUserDO user =userMapper.selectByMobile(mobile);
//                    if(user==null){
//                        countFail++;
//                        continue;
//                    }
//                    sb0.append(user.getId()).append("|");
//                }
//                notice.setRecipientUserId(sb0.toString());
//                notice.setType(1);
//                notice.setStatus(1);
//                oaNoticeMapper.insert(notice);
//                message.setMessageSuccess(mobiles.size() - countFail);
//                message.setMessageFail(countFail);
            }
//            messageSendMapper.insert(message);

        }else{
            if(createReqVO.getTimedSendTime().isBefore(LocalDateTime.now())){
                throw ServiceExceptionUtil.exception(SEND_TIME_BEFOR);
            }
//            SmsSendReq req = new SmsSendReq();
            if(createReqVO.getMessageChannel()){
                message.setMessageReady(mobiles.size() );

            }
            if(createReqVO.getNoticeChannel()){
                message.setNoticeReady(mobiles.size() );
            }
            messageSendMapper.insert(message);
            if(createReqVO.getMessageChannel()){

                for(String mobile:mobiles){
                    SmsSendReq req = new SmsSendReq();
                    req.setMessage(mss);
                    req.setSendId(message.getId());
                    req.setType(1);
                    req.setPhone(ListUtil.of(mobile));
                    req.setSendTime(Date.from(createReqVO.getTimedSendTime().atZone(ZoneId.systemDefault()).toInstant()));
                    TimedTaskDO timedTaskDO = TimedTaskConvert.INSTANCE.convert(req);
                    AdminUserDO user =userMapper.selectByMobile(mobile);
                    if(user!=null){
                        timedTaskDO.setReceiver(user.getNickname());
                    }
                    timedTaskDO.setTitle(createReqVO.getTemplateName());
                    timedTaskMapper.insert(timedTaskDO);
                    req.setTaskId(timedTaskDO.getId());
                    QueueTask.putTask(req);
                }

            }
            if(createReqVO.getNoticeChannel()){
//                调用通知公告接口
                SmsSendReq req = new SmsSendReq();
                req.setSendId(message.getId());
                req.setType(2);
                req.setPhone(mobiles);
                req.setMessage(mss);
                req.setTitle(createReqVO.getTemplateName());
                req.setSendTime(Date.from(createReqVO.getTimedSendTime().atZone(ZoneId.systemDefault()).toInstant()));

                TimedTaskDO timedTaskDO = TimedTaskConvert.INSTANCE.convert(req);
                timedTaskMapper.insert(timedTaskDO);
                req.setTaskId(timedTaskDO.getId());
                QueueTask.putTask(req);
            }
        }
        return message.getId();
    }
    @Override
    public PageResult<MessageSendPageRespVO> getMessagePage(MessageSendPageReqVO pageReqVO) {
        IPage page = MyBatisUtils.buildPage(pageReqVO);
        //        //判断是否为管理员
        RoleDO role = roleService.getRoleById(getLoginUserId());
        Boolean hasSuperAdmin = permissionService.isSuperAdmin(getLoginUserId());;
        //       判断是否为超级管理员
        List<Long> rloes = roleMapper.selectAllRoleByUserId(SecurityFrameworkUtils.getLoginUserId());

        Boolean isAdmin = permissionService.isSuperAdmin(SecurityFrameworkUtils.getLoginUserId())|| rloes.contains(6L);
        List<MessageSendPageRespVO> data = messageSendMapper.selectMyPage(page, pageReqVO,isAdmin,SecurityFrameworkUtils.getLoginUserId(),hasSuperAdmin,getTenantId(),pageReqVO.getNickname());
        return new PageResult<>(data, page.getTotal());
    }

    @Override
    public List<MessageSendGetVO> getMessage(MessageSendGetReqVO req) {
//        IPage page = MyBatisUtils.buildPage(req);
        List<MessageSendRespGetVO> result =messageSendMapper.selectGet(req);
        MessageSendGetVO messageSendGetVO = new MessageSendGetVO();
        if (!result.isEmpty()){
        String content = result.get(0).getMessage();
        Integer total = result.size();
        List<MessageSendRespGetVO> data = result.stream()
                    .peek(vo -> {
                        if (vo.getReceiver() == null) {
                            vo.setReceiver(vo.getPhone());
                        }
                    })
                    .collect(Collectors.toList());

            //receiver
        //phone
        //成功
        List<MessageSendRespGetVO> successList = data.stream().
                filter(MessageSendRespGetVO::isSuccessMessage).collect(Collectors.toList());
        List<String> successReceiver = successList.stream().
                map(MessageSendRespGetVO::getReceiver).collect(Collectors.toList());
        Integer successTotal = successList.size();
        //失败
        List<MessageSendRespGetVO> failList = data.stream().
                filter(MessageSendRespGetVO::isFailMessage).collect(Collectors.toList());
        List<String> failReceiver = failList.stream().
                map(MessageSendRespGetVO::getReceiver).collect(Collectors.toList());
        Integer failTotal = failList.size();
        //未发送
            List<MessageSendRespGetVO> unsentList = data.stream()
                    .filter(message -> !message.isStatus())
                    .collect(Collectors.toList());
            List<String> unsentReceiver = unsentList.stream().
            map(MessageSendRespGetVO::getReceiver).collect(Collectors.toList());
        Integer unsentTotal = unsentList.size();

        //赋值
        messageSendGetVO.setContent(content);
        messageSendGetVO.setTotal(total);
        messageSendGetVO.setSuccessReceiver(successReceiver);
        messageSendGetVO.setSuccessTotal(successTotal);
        messageSendGetVO.setFailReceiver(failReceiver);
        messageSendGetVO.setFailTotal(failTotal);
        messageSendGetVO.setUnsentReceiver(unsentReceiver);
        messageSendGetVO.setUnsentTotal(unsentTotal);
        }
        List<MessageSendGetVO> messageSendGetVOList = new ArrayList<>();
        messageSendGetVOList.add(messageSendGetVO);
        return messageSendGetVOList;
    }


//    @Override
//    public PageResult<MessageSendRespGetVO> getMessage(MessageSendGetReqVO req) {
//        IPage page = MyBatisUtils.buildPage(req);
//        List<MessageSendRespGetVO> data =messageSendMapper.selectGet(page, req);
//        return new PageResult<>(data, page.getTotal());
//    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateMessage(MessageUpdateReqVO updateReqVO) {
//        // 校验信息模板
//        checkUpdate(updateReqVO);
//        // 更新信息模板
//        MessageDO message = MessageConvert.INSTANCE.convert(updateReqVO);
//        messageMapper.updateById(message);
//    }
//
//    private void checkUpdate(MessageUpdateReqVO updateReqVO) {
//        if(updateReqVO.getSystemId()==null){
//            return;
//        }
//        List<String> messa = messageMapper.selectBySystemId(updateReqVO.getSystemId());
//        if (messa == null) {
//            return;
//        }
//        if(messa.stream().anyMatch(u-> Objects.equals(u, updateReqVO.getName()))){
//            throw ServiceExceptionUtil.exception(TEMPLATE_NAME_DUPLICATE);
//        }
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void deleteMessage(Long id) {
//        // 校验机构id是否存在
//        MessageDO messageDO = this.getById(id);
//        if (messageDO == null) {
//            throw exception(TEMPLATE_NOT_EXISTS);
//        }
//        // 删除信息模板
//        this.removeById(id);
//
//    }

}
