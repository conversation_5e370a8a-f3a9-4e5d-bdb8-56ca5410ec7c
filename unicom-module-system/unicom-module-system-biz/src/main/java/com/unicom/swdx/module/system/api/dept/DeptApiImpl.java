package com.unicom.swdx.module.system.api.dept;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dept.dto.OldDeptDTO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptListReqVO;
import com.unicom.swdx.module.system.convert.dept.DeptConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import com.unicom.swdx.module.system.dal.mysql.message.TimedTaskMapper;
import com.unicom.swdx.module.system.job.smsjob.QueueTask;
import com.unicom.swdx.module.system.mq.producer.infor.InforProducer;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.dept.OldDeptService;
import com.unicom.swdx.module.system.service.sms.SmsLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
@Slf4j
public class DeptApiImpl implements DeptApi {

    @Resource
    private DeptService deptService;

    @Resource
    private OldDeptService oldDeptService;

    @Resource
    private SmsLogService smsLogService;


    @Resource
    private InforProducer inforProducer;

    @DataPermission(enable = false)
    @Override
    public CommonResult<DeptRespDTO> getDept(Long id) {
        DeptDO dept = deptService.getDept(id);
        return success(DeptConvert.INSTANCE.convert03(dept));
    }

    @Override
    public CommonResult<DeptRespDTO> getDeptByTenantAndName(Long tenantid ,String deptname) {
        DeptDO dept = deptService.getDeptByTenantAndName(tenantid , deptname);
        return success(DeptConvert.INSTANCE.convert03(dept));
    }

    @DataPermission(enable = false)
    @Override
    public CommonResult<List<DeptRespDTO>> getDepts(Collection<Long> ids) {
        List<DeptDO> depts = deptService.getDepts(ids);
        return success(DeptConvert.INSTANCE.convertList03(depts));
    }

    @Override
    public CommonResult<Boolean> validDepts(Collection<Long> ids) {
        deptService.validDepts(ids);
        return success(true);
    }

    @Override
    public CommonResult<List<DeptRespDTO>> getAllChildrenDept(Long id) {
        List<DeptDO> depts = deptService.getAllChildrenDeptByDeptId(id);
        return success(DeptConvert.INSTANCE.convertList03(depts));
    }

    @Override
    public CommonResult<List<DeptRespDTO>> getAllChildrenDeletedDept(List<Long> deptIds) {
        List<DeptDO> depts = deptService.getAllChildrenDeptByDeletedDeptId(deptIds);
        return success(DeptConvert.INSTANCE.convertList03(depts));
    }

    @Override
    public CommonResult<List<DeptRespDTO>> getAllDepts(Long tenantId) {
        DeptListReqVO  reqVO = new DeptListReqVO();
        reqVO.setTenantId(tenantId);
        List<DeptDO> deptsList = deptService.getSimpleDepts(reqVO);
        return success(DeptConvert.INSTANCE.convertList03(deptsList));
    }

    @Override
    public CommonResult<Boolean> isLeaderUser(Long leaderUserId) {
        if(Objects.isNull(deptService.getByLeaderUserId(leaderUserId))){
            return success(false);
        }else {
            return success(true);
        }
    }

    @Override
    public CommonResult<List<DeptRespDTO>> getByLeaderUser(Long leaderUserId) {
        return success(deptService.getListByLeader(leaderUserId));
    }

    @Override
    public CommonResult<OldDeptDTO> getOldDeptByDeptId(Long deptId) {
        OldDeptDTO oldDeptDTO = new OldDeptDTO();
        BeanUtil.copyProperties(oldDeptService.getOldDeptByDeptId(deptId), oldDeptDTO);
        return success(oldDeptDTO);
    }


    @Resource
    private TimedTaskMapper timedTaskMapper;


    @Override
    public CommonResult<String> sendMessageRefresh(String code) {
        QueueTask.remove(code ,smsLogService);     //删除队列的延迟消息数据
        inforProducer.sendRefreshMessage(code);   //删除其他服务器备份的 延迟消息数据

        LambdaQueryWrapper<TimedTaskDO> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.eq(TimedTaskDO::getTitle , code);
        timedTaskMapper.delete(lambdaQueryWrapper);   //删除system_timed_task数据库备份的 延迟消息数据
        return success("0");
    }

    @Override
    @Transactional
    public CommonResult<String> sendMessageRefreshList(List<String> codes) {
        for (String code : codes) {
            QueueTask.remove(code, smsLogService);     //删除队列的延迟消息数据
            inforProducer.sendRefreshMessage(code);   //删除其他服务器备份的 延迟消息数据

            LambdaQueryWrapper<TimedTaskDO> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
            lambdaQueryWrapper.eq(TimedTaskDO::getTitle, code);
            timedTaskMapper.delete(lambdaQueryWrapper);   //删除system_timed_task数据库备份的 延迟消息数据
        }
        return success("0");
    }

    @Override
    public CommonResult<String> sendMessageRefreshListById(List<Long> ids) {
        for (Long id : ids) {
            QueueTask.remove(id, smsLogService);     //删除队列的延迟消息数据
            inforProducer.sendRefreshMessage(id);   //删除其他服务器备份的 延迟消息数据

            LambdaQueryWrapper<TimedTaskDO> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
            lambdaQueryWrapper.eq(TimedTaskDO::getSendId, id);
            timedTaskMapper.delete(lambdaQueryWrapper);   //删除system_timed_task数据库备份的 延迟消息数据
        }
        return success("0");
    }

    @Override
    public void asynTest(String code) {
        log.info("异步测试DubboService void  begin");
        try {
            Thread.sleep(5000);
            log.info("异步测试DubboService void  end");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CommonResult<String> syncTest(String code) {
        log.info("同步测试DubboService void  begin");
        log.info("同步测试DubboService void  end");
        return success("0");
    }

    @Override
    public void timeTest(LocalDateTime time) {

        ZoneId zoneId = ZoneId.systemDefault();

        log.info("时间测试DubboService  end {}  " , DateUtil.formatDateTime(new Date()));
    }
}
