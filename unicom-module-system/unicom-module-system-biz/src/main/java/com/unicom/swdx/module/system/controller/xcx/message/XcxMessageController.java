package com.unicom.swdx.module.system.controller.xcx.message;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.wx.xcx.WxXcxApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/system/xcxMsg")
@Slf4j
public class XcxMessageController {

    @Resource
    private WxXcxApiService wxXcxApiService;

    @GetMapping("/sendPermanent")
    public CommonResult<Boolean> sendPermanent() {
        wxXcxApiService.sendXcxMessage();
        return success(true);
    }

}
