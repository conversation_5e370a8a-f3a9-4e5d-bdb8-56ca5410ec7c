package com.unicom.swdx.module.system.service.databoard;


import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.databoard.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.databoard.DataboardMapper;
import com.unicom.swdx.module.system.dal.mysql.dict.DictDataMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class DataboardServiceImpl implements DataboardService {



    @Resource
    private DataboardMapper databoardMapper;

    @Resource TenantMapper tenantMapper;

    @Resource DictDataMapper dictDataMapper;

    @Resource AdminUserMapper adminUserMapper;

    /**
     * 获取机构统计信息
     * @return 机构统计信息
     *
     * */
    @Override
    public TenantCountRespVO getTenantCount()
    {
        TenantCountRespVO tenantCountRespVO = new TenantCountRespVO();
        tenantCountRespVO.setAllTenant(databoardMapper.selectAllTenantCount());
        tenantCountRespVO.setNewTenant(databoardMapper.selectNewTenantCount(LocalDate.now()));
        tenantCountRespVO.setRegisteringTenant(databoardMapper.selectRegisteringTenantCount());
        return tenantCountRespVO;
    }

    /**
     * 获取用户统计信息
     * @return 用户统计信息
     *
     * */
    @Override
    public UserCountRespVO getUserCount()
    {
        UserCountRespVO userCountRespVO = new UserCountRespVO();
        userCountRespVO.setAllUsers(databoardMapper.selectAllUsersCount());
        userCountRespVO.setNewUsers(databoardMapper.selectNewUsersCount(LocalDate.now()));
        userCountRespVO.setActiveUsers(databoardMapper.selectActiveUsersCount(LocalDate.now()));
        return userCountRespVO;
    }

    /**
     * 获取近六个月的机构新增趋势
     *
     * @return 六个月的机构新增趋势
     * */
    @Override
    public List<LastSixMonthTenantRespVO> getLastSixMonthTenant()
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH,1);
        calendar.set(Calendar.DAY_OF_MONTH,1);

        List<LastSixMonthTenantRespVO> lastSixMonthTenantRespVOList = new ArrayList<>();

        for (int i = 0;i  < 6; i++)
        {
            lastSixMonthTenantRespVOList.add(0,databoardMapper.selectAMonthTenantRespVO(calendar.getTime()));
            calendar.add(Calendar.MONTH,-1);
        }
        return lastSixMonthTenantRespVOList;
    }


    @Override
    public List<LastSixMonthUsersRespVO> getLastSixMonthUsers()
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH,1);
        calendar.set(Calendar.DAY_OF_MONTH,1);

        List<LastSixMonthUsersRespVO> lastSixMonthUsersRespVOList = new ArrayList<>();

        for(int i = 0;i < 6;i++)
        {
            lastSixMonthUsersRespVOList.add(0,databoardMapper.selectAMonthUsersRespVO(calendar.getTime()));
            calendar.add(Calendar.MONTH,-1);
        }



        return lastSixMonthUsersRespVOList;
    }


    @Override
    public RealNameUsersCountRespVO getRealNameUsersCount()
    {
        RealNameUsersCountRespVO realNameUsersCountRespVO = new RealNameUsersCountRespVO();
        realNameUsersCountRespVO.setRealNamePercent((float)realNameUsersCountRespVO.getRealNameNum()/(float)(realNameUsersCountRespVO.getRealNameNum()+realNameUsersCountRespVO.getNormalNum()));
        realNameUsersCountRespVO.setNormalPercent((float)realNameUsersCountRespVO.getNormalNum()/(float)(realNameUsersCountRespVO.getRealNameNum()+realNameUsersCountRespVO.getNormalNum()));

        return realNameUsersCountRespVO;
    }

    @Override
    public List<UsersAttestationTypeCountRespVO> getUserAttestationTypeCount()
    {
        List<String> typeList = dictDataMapper.selectList("dict_type","system_users_public_authenticate_identity")
                .stream().map(DictDataDO::getValue).collect(Collectors.toList());
        List<UsersAttestationTypeCountRespVO> usersAttestationTypeCountRespVOList = new ArrayList<>();
        List<List<Long>> idListList = new ArrayList<>();
        long num;
        for (int i =0;i < typeList.size();i++)
        {
            num = 0;
            for (List<Long> ids :idListList)
            {

                if (ids != null && ids.contains(Long.parseLong(typeList.get(i))))
                {
                    num++;
                }
            }
            usersAttestationTypeCountRespVOList.add(new UsersAttestationTypeCountRespVO());
            usersAttestationTypeCountRespVOList.get(i).setNum(num);
        }

        long allNum =0;
        for (UsersAttestationTypeCountRespVO usersAttestationTypeCountRespVO : usersAttestationTypeCountRespVOList)
        {
            allNum+=usersAttestationTypeCountRespVO.getNum();
        }

        for (UsersAttestationTypeCountRespVO usersAttestationTypeCountRespVO : usersAttestationTypeCountRespVOList)
        {
            usersAttestationTypeCountRespVO.setPercent((float)usersAttestationTypeCountRespVO.getNum()/(float)allNum);
        }

        for (int i = 0;i < typeList.size();i++)
        {
            usersAttestationTypeCountRespVOList.get(i).setTypeName(dictDataMapper.selectByDictTypeAndValue("system_users_public_authenticate_identity",typeList.get(i)).getLabel());
        }


        return usersAttestationTypeCountRespVOList;
    }

}
