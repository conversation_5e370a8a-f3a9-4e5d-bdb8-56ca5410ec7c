package com.unicom.swdx.module.system.dal.mysql.message;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TimedTaskMapper extends BaseMapperX<TimedTaskDO> {

    void updateStatus(@Param("id") Long id);

    void updateSuccess(@Param("id") Long id);
    Integer count(@Param("type") Integer type);
    List<TimedTaskDO>  selectByStatus();
//    default PageResult<MessageSendDO> selectPage(MessageSendPageReqVO reqVO) {
//        LambdaQueryWrapperX<MessageSendDO> query = new LambdaQueryWrapperX<MessageSendDO>()
//                .eqIfPresent(MessageSendDO::getTemplateId,reqVO.getTemplateId())
//                .likeIfPresent(MessageSendDO::getMessageContent,reqVO.getMessageContent())
//                .betweenIfPresent(MessageSendDO::getCreateTime,reqVO.getCreateTime())
//                .orderByDesc(MessageSendDO::getId);
//        return selectPage(reqVO, query);
//    }
}
