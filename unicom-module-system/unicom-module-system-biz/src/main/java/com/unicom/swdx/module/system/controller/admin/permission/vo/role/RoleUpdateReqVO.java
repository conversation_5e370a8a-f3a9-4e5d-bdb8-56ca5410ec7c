package com.unicom.swdx.module.system.controller.admin.permission.vo.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Set;

@ApiModel("管理后台 - 角色更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleUpdateReqVO extends RoleBaseVO {

    @ApiModelProperty(value = "角色编号", required = true, example = "1024")
    @NotNull(message = "角色编号不能为空")
    private Long id;

    @ApiModelProperty(value = "角色分配的菜单编号集合", example = "1,3,5")
    private Set<Long> roleMenuIds = Collections.emptySet();

    @ApiModelProperty(value = "数据范围", required = true, example = "1", notes = "参见 DataScopeEnum 枚举类")
    @NotNull(message = "数据范围不能为空")
//    TODO 这里要多一个枚举校验
    private Integer dataScope;

    @ApiModelProperty(value = "自定义分配的部门编号集合", example = "1,3,5", notes = "只有范围类型为 DEPT_CUSTOM 时，该字段才需要")
    private Set<Long> dataScopeDeptIds = Collections.emptySet();

}
