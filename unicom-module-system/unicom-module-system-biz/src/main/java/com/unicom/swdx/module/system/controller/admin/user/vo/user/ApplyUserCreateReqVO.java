package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.unicom.swdx.framework.common.validation.Mobile;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

@ApiModel("招聘系统 - 用户创建 Request VO")
@Data
public class ApplyUserCreateReqVO {
    @ApiModelProperty(value = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    @Length(min = 8, max = 20, message = "密码长度为 8-20 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    private String password;

    @ApiModelProperty(value = "验证码", required = true, example = "1234", notes = "验证码开启时，需要传递")
    @NotBlank(message = "验证码不能为空", groups = AuthLoginReqVO.CodeEnableGroup.class)
    private String verification;

    @ApiModelProperty(value = "用户名", required = true, example = "unicom")
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @ApiModelProperty(value = "手机号码", example = "15601691300")
    @Mobile
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "组织ID", example = "我是一个用户")
    private Long deptId;

    @ApiModelProperty(value = "用户昵称", required = true, example = "芋艿")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickname;
}
