package com.unicom.swdx.module.system.api.common;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.common.dto.CalendarWnlRespDTO;
import com.unicom.swdx.module.system.dal.dataobject.calendarwnl.CalendarWnlDO;
import com.unicom.swdx.module.system.enums.common.HolidayTypeEnum;
import com.unicom.swdx.module.system.service.common.CalendarWnlService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class CalendarWnlApiImpl implements CalendarWnlApi{

    @Resource
    private CalendarWnlService calendarWnlService;

    @Override
    public CommonResult<List<CalendarWnlRespDTO>> getCalendarInRange(LocalDate startDate, LocalDate endDate) {

        List<CalendarWnlDO> range = calendarWnlService.getCalendarInRange(startDate, endDate);
        List<CalendarWnlRespDTO> dtoList = new ArrayList<>();
        for (CalendarWnlDO calendarWnlDO : range) {
            CalendarWnlRespDTO calendarWnlRespDTO = new CalendarWnlRespDTO();
            BeanUtils.copyProperties(calendarWnlDO,calendarWnlRespDTO);
            dtoList.add(calendarWnlRespDTO);
        }
        return success(dtoList);

    }

    @Override
    public CommonResult<Integer> calculateWorkDay(LocalDate startDate, LocalDate endDate) {

        List<CalendarWnlRespDTO> calendarRespList = this.getCalendarInRange(startDate, endDate).getCheckedData();
        //正常工作日天数
        List<CalendarWnlRespDTO> workdayList = calendarRespList.stream().filter(workday -> (HolidayTypeEnum.WORK_DAY.getType().equals(workday.getHolidayType()))).collect(Collectors.toList());
        //调休工作日
        List<CalendarWnlRespDTO> exchangeDayList = calendarRespList.stream().filter(workday -> (HolidayTypeEnum.EXCHANGE_DAY.getType().equals(workday.getHolidayType()))).collect(Collectors.toList());

        return success(workdayList.size()+exchangeDayList.size());

    }
}
