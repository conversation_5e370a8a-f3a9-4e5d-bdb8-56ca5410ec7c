package com.unicom.swdx.module.system.controller.admin.businesscenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("用户精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSimpleRespVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "uuid")
    private String uid;

    @ApiModelProperty(value = "用户名", required = true, example = "sk")
    private String username;

    @ApiModelProperty(value = "用户昵称", required = true, example = "sk")
    private String nickname;

    @ApiModelProperty(value = "所属机构Id")
    @Ignore
    private Long tenantId;

    @ApiModelProperty(value = "所属机构")
    private String tenantName;

    @ApiModelProperty(value = "所属组织")
    private String deptName;

}
