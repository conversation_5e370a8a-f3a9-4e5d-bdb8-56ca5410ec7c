package com.unicom.swdx.module.system.controller.admin.common;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.job.calendarwnl.SyncCalendarWnl;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2023/4/10 16:28
 **/
@RestController
@RequestMapping("/system/calendarWnl")
public class CalendarWnlController {

    @Resource
    private SyncCalendarWnl calendarWnl;

    /**
     * todo 测试接口，待删除
     * @return
     */
    @GetMapping("/sync")
    public CommonResult<Boolean> sync() {
        calendarWnl.sync();
        return success(true);
    }

}
