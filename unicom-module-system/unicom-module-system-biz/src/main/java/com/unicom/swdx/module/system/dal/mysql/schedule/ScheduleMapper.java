package com.unicom.swdx.module.system.dal.mysql.schedule;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.controller.admin.home.schedule.vo.ScheduleCreateReqVO;
import com.unicom.swdx.module.system.dal.dataobject.schedule.ScheduleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 日程 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ScheduleMapper extends BaseMapperX<ScheduleDO> {

    List<ScheduleDO> getScheduleByUserIdPId(@Param("param")ScheduleCreateReqVO scheduleCreateReqVO);



}
