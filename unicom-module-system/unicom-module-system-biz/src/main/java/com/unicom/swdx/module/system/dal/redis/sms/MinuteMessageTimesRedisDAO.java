package com.unicom.swdx.module.system.dal.redis.sms;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.module.system.dal.redis.RedisKeyConstants.MOBILE_MESSAGE_TIMES_MINUTE;

/**
 * 分钟级短信发送限制
 */
@Repository
@Slf4j
public class MinuteMessageTimesRedisDAO {

    @Resource
    private  StringRedisTemplate stringRedisTemplate;

    public Long get(String mobile) {
        String redisKey = formatKey(mobile);
        String times = stringRedisTemplate.opsForValue().get(redisKey);
        return StrUtil.isBlank(times)?0:Long.parseLong(times);
    }

    public Boolean set(String mobile) {
        String redisKey = formatKey(mobile);
        return stringRedisTemplate.opsForValue().setIfAbsent(redisKey,mobile,1, TimeUnit.MINUTES);
    }
    private static String formatKey(String mobile) {
        return String.format(MOBILE_MESSAGE_TIMES_MINUTE.getKeyTemplate(), mobile);
    }

}
