package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import cn.hutool.core.date.DatePattern;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/1/28 13:37
 **/
@ApiModel("首页 - 日程列表 Request VO")
@Data
public class ScheduleQueryReqVO {

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", required = true)
    @NotNull(message = "日程日期不能为空")
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate date;

}
