package com.unicom.swdx.module.system.controller.admin.message;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.sms.core.client.SmsCommonResult;
import com.unicom.swdx.framework.sms.core.client.dto.SmsSendRespDTO;
import com.unicom.swdx.module.system.controller.admin.message.vo.MassageVO;
import com.unicom.swdx.module.system.service.messagebase.MessageBaseService;
import com.unicom.swdx.module.system.service.sms.SmsSendService;
import io.swagger.annotations.Api;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2024/2/28 10:53
 */

@Api(tags = "管理后台 - 短信通知")
@RestController
@RequestMapping("/system/massage")
@Validated
public class MessageController {

    @Resource
    public SmsSendService smsSendService;


    @Resource
    private MessageBaseService messageBaseService;


    @PostMapping("/send")
    public ResponseEntity<Map<String, Object>> sendSms(@RequestBody MassageVO massageVO) {
        Map<String, Object> result = messageBaseService.sendSms(massageVO);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/uplinkMessage")
    @PermitAll
    public CommonResult<Boolean> uplinkMessage(@RequestParam String userMoblie,@RequestParam String content,
                                               @RequestParam(required = false) String cpMoblie
                                                ) {
        messageBaseService.uplinkMessage(userMoblie,content,cpMoblie);
        return CommonResult.success(true);
    }

    @PostMapping("/s")
    public CommonResult<Boolean> send(@RequestParam String mobile,@RequestParam String msg) {
        messageBaseService.sendSingleMessage(mobile,msg);
        return CommonResult.success(true);
    }


    @PostMapping("/sendBytemplate")
    public CommonResult<SmsSendRespDTO> sendBytemplate(@RequestBody MassageVO massageVO) {

        SmsCommonResult<SmsSendRespDTO> messageid  = smsSendService.sendSingleSmss(massageVO.getDesMobile(), null,null ,massageVO.getTemplateCode(),massageVO.getTemplateParams());
        CommonResult commonResult = new CommonResult();
        commonResult.setCode(messageid.getCode());
        commonResult.setMsg(messageid.getMsg());
        commonResult.setData(messageid.getData());
        return commonResult;
    }
}
