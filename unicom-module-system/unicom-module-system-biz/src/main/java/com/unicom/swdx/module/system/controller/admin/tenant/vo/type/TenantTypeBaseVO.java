package com.unicom.swdx.module.system.controller.admin.tenant.vo.type;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 机构用户类型 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TenantTypeBaseVO {

    @ApiModelProperty(value = "机构用户类型名称", required = true)
    @NotNull(message = "机构用户类型名称不能为空")
    @Length(min = 0,max = 30,message = "机构名称不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "状态", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "备注")
    @Length(max = 50, message = "备注最多支持50个字符")
    private String remark;

    @ApiModelProperty(value = "角色id")
    @NotEmpty(message = "角色权限不能为空")
    private Set<Long> roleIds;

}
