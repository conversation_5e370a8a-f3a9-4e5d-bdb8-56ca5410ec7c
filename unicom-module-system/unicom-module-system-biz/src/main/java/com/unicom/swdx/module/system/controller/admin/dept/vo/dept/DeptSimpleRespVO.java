package com.unicom.swdx.module.system.controller.admin.dept.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("管理后台 - 部门精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeptSimpleRespVO {

    @ApiModelProperty(value = "部门编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "部门名称", required = true, example = "sk")
    private String name;

    @ApiModelProperty(value = "父部门 ID", required = true, example = "1024")
    private Long parentId;

    @ApiModelProperty(value = "部门父子路径", required = false, example = "根路径/子路径")
    private String parentDeptName;

    @ApiModelProperty(value = "是否可选")
    private boolean optional;

    @ApiModelProperty(value = "该部门人数")
    private Long count;

    @ApiModelProperty(value = "该状态总人数")
    private Long sum;

}
