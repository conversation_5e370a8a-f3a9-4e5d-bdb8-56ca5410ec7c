package com.unicom.swdx.module.system.job.smsjob;

import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * 延时执行任务
 */
@Getter
@ToString
public class DelayTask implements Delayed {

    private final SmsSendReq data;

    //任务延时时间（ms）从加入到延迟队列时开始计时，多少毫秒后执行该任务
    private final long expireTime;
    private boolean executed = false;
    /**
     * 构造延时任务
     *
     * @param data       业务数据
     * @param expireTime 任务延时时间（ms）
     */
    public DelayTask(SmsSendReq data, long expireTime) {
        super();
        this.data = data;
        this.expireTime = expireTime + System.currentTimeMillis();
    }
    // 获取延时时间
    @Override
    public long getDelay(TimeUnit unit) {
        long time = this.expireTime - System.currentTimeMillis();
        return unit.convert(time, unit);
    }

    // 对延时队列中的元素进行排序
    @Override
    public int compareTo(Delayed o) {
        DelayTask t = (DelayTask) o;
        if (this.expireTime - t.expireTime <= 0) {
            return -1;
        } else {
            return 1;
        }
    }

}
