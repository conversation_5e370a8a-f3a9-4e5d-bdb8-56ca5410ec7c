package com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant;

import cn.hutool.core.date.DatePattern;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 机构分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPageReqVO extends PageParam {

    @ApiModelProperty(value = "机构名", example = "sk")
    @Length(min = 0,max = 30,message = "机构名称不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "管理员姓名")
    @Length(min = 0,max = 10,message = "管理员性能10字以内")
    private String contactNickname;

    @ApiModelProperty(value = "管理员手机号码")
    @Length(min = 0,max = 11,message = "管理员手机号码11位以内")
    private String contactMobile;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

}
