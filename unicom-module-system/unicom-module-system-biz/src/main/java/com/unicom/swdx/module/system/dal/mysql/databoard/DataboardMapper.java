package com.unicom.swdx.module.system.dal.mysql.databoard;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.system.controller.admin.databoard.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Mapper
public interface DataboardMapper extends BaseMapperX<TenantDO> {

    Long selectAllTenantCount();

    Long selectNewTenantCount(@Param("localdate") LocalDate localdate);

    Long selectRegisteringTenantCount();

    Long selectAllUsersCount();

    Long selectNewUsersCount(@Param("localdate") LocalDate localdate);

    Long selectActiveUsersCount(@Param("localdate") LocalDate localdate);

    LastSixMonthTenantRespVO selectAMonthTenantRespVO(@Param("yearMonth") Date yearMonth);

    LastSixMonthUsersRespVO selectAMonthUsersRespVO(@Param("yearMonth") Date yearMonth);

    List<Long> selectTenantTypeList();

    String selectTenantTypeName(@Param("id") Long id);



}
