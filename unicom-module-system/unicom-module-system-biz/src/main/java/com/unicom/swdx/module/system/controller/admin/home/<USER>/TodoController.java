package com.unicom.swdx.module.system.controller.admin.home.todo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.home.todo.vo.*;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemUpdateReqDTO;
import com.unicom.swdx.module.system.convert.todo.TodoConvert;
import com.unicom.swdx.module.system.dal.dataobject.todo.TodoDO;
import com.unicom.swdx.module.system.service.todo.TodoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


@Api(tags = "业务中台 - 待办事项")
@RestController
@RequestMapping("/system/todo")
@Validated
public class TodoController {

    @Resource
    private TodoService todoService;

//    @Autowired
//    private BpmOATaskService oaTaskService;
//
//    @Autowired
//    private BpmTaskService taskService;
    /**
     * 创建待办事项
     *
     * @param reqVO 创建信息
     */
    @PostMapping("/create")
    //@ApiOperation("创建待办事项")
    @PermitAll
    public CommonResult<Boolean> createTodoItem(@Valid @RequestBody TodoCreateSimpleReqVO reqVO) {
        todoService.createSimpleTodo(reqVO);
        return success(true);
    }

    /**
     * 已办事项
     *
     * @param reqVO 待办信息
     */
    @PostMapping("/update")
    //@ApiOperation("已办事项")
    @PermitAll
    public CommonResult<Boolean> updateTodoItem(@RequestBody TodoUpdateSimpleReqVO reqVO) {
        todoService.updateSimpleTodo(reqVO);
        return success(true);
    }

    /**
     * 更新待办事项备注
     *
     * @param reqDTO 待办信息
     * @return 待办的编号
     */
    @PostMapping("/update-remark")
    //@ApiOperation("更新待办事项备注")
    public CommonResult<Boolean> updateTodoItemRemark(@RequestBody TodoItemUpdateReqDTO reqDTO) {
        todoService.updateTodoRemark(reqDTO);
        return success(true);
    }

    /**
     * 删除待办事项
     *
     * @param reqVO 待办信息
     */
    @PermitAll
    @PostMapping("/delete")
    //@ApiOperation("删除待办事项")
    public CommonResult<Boolean> deleteTodoItem(@RequestBody TodoUpdateSimpleReqVO reqVO) {
        todoService.deleteSimpleTodo(reqVO);
        return success(true);
    }

    @GetMapping("/page")
    @ApiOperation("获得待办事项分页")
    public CommonResult<PageResult<TodoRespVO>> getTodoPage(@Valid TodoPageReqVO pageVO) {
        PageResult<TodoDO> pageResult = todoService.getTodoPage(pageVO,getLoginUserId());
        return success(TodoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/num")
    @ApiOperation("获得待办事项数量")
//    @PreAuthorize("@ss.hasPermission('midoffice:officialdoc:query')")
    public CommonResult<Integer> getOfficialdocNum() {
        Integer count = todoService.getTodoNum(getLoginUserId());
        return success(count);
    }

    @GetMapping("/list-page")
    @ApiOperation("获得待办事项分页(新)")
    public CommonResult<PageResult<TodoListPageVO>> getTodoListPage(TodoListPageReqVO todoListPageReqVO) {
        PageResult<TodoListPageVO> todoListPage = todoService.getTodoListPage(todoListPageReqVO,getLoginUserId());
        return success(todoListPage);
    }

    @GetMapping("/done-list-page")
    @ApiOperation("获得已办事项分页(新)")
    public CommonResult<PageResult<TodoListPageVO>> getDoneListPage(TodoListPageReqVO todoListPageReqVO) {
        PageResult<TodoListPageVO> todoListPage = todoService.getDoneListPage(todoListPageReqVO,getLoginUserId());
        return success(todoListPage);
    }

//    @GetMapping("/list-pageNew")
//    @ApiOperation("获得待办事项分页(新)")
//    public CommonResult<PageResult<TodoListPageVO>> getTodoListPageNew(TodoListPageReqVO todoListPageReqVO) {
////        PageResult<TodoListPageVO> todoListPage = todoService.getTodoListPage(todoListPageReqVO,todoListPageReqVO.getLoginUserId());
//        PageResult<TodoListPageVO> todoListPage=new PageResult<>();
//        BpmTaskTodoPageReqVO taskTodoPageReqVO=new BpmTaskTodoPageReqVO();
//        taskTodoPageReqVO.setPageNo(taskTodoPageReqVO.getPageNo());
//        taskTodoPageReqVO.setPageSize(taskTodoPageReqVO.getPageSize());
//
//        BpmOATaskTodoPageReqVO oaTaskTodoPageReqVOageVO=new BpmOATaskTodoPageReqVO();
//        oaTaskTodoPageReqVOageVO.setPageNo(todoListPageReqVO.getPageNo());
//        oaTaskTodoPageReqVOageVO.setPageSize(todoListPageReqVO.getPageSize());
//
//        PageResult<BpmTaskTodoPageItemRespVO> todoTaskPage = taskService.getTodoTaskPage(WebFrameworkUtils.getLoginUserId(), taskTodoPageReqVO);
//        //OA
//        PageResult<BpmOATaskTodoPageItemRespVO> oaTodoTaskPage = oaTaskService.getTodoTaskPage(WebFrameworkUtils.getLoginUserId(), oaTaskTodoPageReqVOageVO);
//        System.out.println(todoTaskPage);
//        System.out.println(oaTodoTaskPage);
//        return success(todoListPage);
//    }

//    @GetMapping("/app-list-page")
//    @ApiOperation("获得待办事项分页(新)")
//    public CommonResult<PageResult<AppTodoListPageVO>> getAPPTodoListPage(TodoListPageReqVO todoListPageReqVO) {
//        PageResult<AppTodoListPageVO> todoListPage = todoService.getAppTodoListPage(todoListPageReqVO,getLoginUserId());
//        return success(todoListPage);
//    }

    @GetMapping("/list-pageAll")
    @ApiOperation("获得全部待办事项分页(新)")
    public CommonResult<PageResult<TodoListPageAllVO>> getTodoListPageALL(TodoListPageAllReqVO todoListPageAllReqVO) {
        PageResult<TodoListPageAllVO> todoListPageAll = todoService.getTodoListPageALL(todoListPageAllReqVO, getLoginUserId());
        return success(todoListPageAll);
    }

    @GetMapping("/list-pageDoneAll")
    @ApiOperation("获得已办事项分页")
    public CommonResult<PageResult<DoneListPageAllVO>> getDoneListPageALL(DoneListPageAllReqVO doneListPageAllReqVO) {
        PageResult<DoneListPageAllVO> todoListPageAll = todoService.getDoneListPageALL(doneListPageAllReqVO, getLoginUserId());
        return  success(todoListPageAll);
    }

    @GetMapping("/getGeoName")
    @ApiOperation("调用第三方接口")
    public String getGeoName() throws JsonProcessingException {
        String s = todoService.savaDataToServerByHutool();
        return s;
    }

}
