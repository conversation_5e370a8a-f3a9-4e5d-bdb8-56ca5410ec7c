package com.unicom.swdx.module.system.service.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.google.common.annotations.VisibleForTesting;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import com.unicom.swdx.module.system.convert.permission.RoleConvert;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.dal.mysql.tenant.TenantMapper;
import com.unicom.swdx.module.system.enums.permission.DataScopeEnum;
import com.unicom.swdx.module.system.enums.permission.RoleCodeEnum;
import com.unicom.swdx.module.system.enums.permission.RoleTypeEnum;
import com.unicom.swdx.module.system.mq.producer.permission.RoleProducer;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * 角色 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

    /**
     * 定时执行 {@link #schedulePeriodicRefresh()} 的周期
     * 因为已经通过 Redis Pub/Sub 机制，所以频率不需要高
     */
    private static final long SCHEDULER_PERIOD = 5 * 60 * 1000L;

    /**
     * 角色缓存
     * key：角色编号 {@link RoleDO#getId()}
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    @Getter
    private volatile Map<Long, RoleDO> roleCache;

    @Resource
    @Lazy
    private PermissionService permissionService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private RoleProducer roleProducer;

    @Resource
    @Lazy
    private OAuth2ClientService oAuth2ClientService;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    @Lazy // 注入自己，所以延迟加载
    private RoleService self;

    @Resource
    private AdminUserService userService;

    @Resource
    @Lazy
    private TenantService tenantService;


    // 超级管理员角色id
    private final Long SUPER_ADMIN_ROLE_ID = 1L;

    // 机构注册的临时角色
    private final Long TEMP_TENANT_ROLE_ID = 2L;

    /**
     * 初始化 {@link #roleCache} 缓存
     */
    @Override
    @PostConstruct
    @TenantIgnore // 忽略自动多租户，全局初始化缓存
    public void initLocalCache() {
        // 获取角色列表，如果有更新
        List<RoleDO> roleList = roleMapper.selectList();
        if (CollUtil.isEmpty(roleList)) {
            return;
        }
        // 写入缓存
        roleCache = CollectionUtils.convertMap(roleList, RoleDO::getId);
        log.info("[initLocalCache][初始化 Role 数量为 {}]", roleList.size());
    }

    @Scheduled(fixedDelay = SCHEDULER_PERIOD, initialDelay = SCHEDULER_PERIOD)
    public void schedulePeriodicRefresh() {
       self.initLocalCache();
    }

    @Override
    @Transactional
    public Long createRole(RoleCreateReqVO reqVO) {
        // 校验角色
        checkDuplicateRole(reqVO,null);
        // 插入到数据库
        RoleDO role = RoleConvert.INSTANCE.convert(reqVO);
        //role.setType(ObjectUtil.defaultIfNull(type, RoleTypeEnum.CUSTOM.getType()));
        role.setStatus(CommonStatusEnum.ENABLE.getStatus());
        role.setDataScope(DataScopeEnum.ALL.getScope()); // 默认可查看所有数据。原因是，可能一些项目不需要项目权限
        // 内置的超级租户不要设置机构

         Long tenantId = getTenantId();
        role.setTenantId(getTenantId());
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            role.setTenantId(0L);
        }
        roleMapper.insert(role);
        // 发送刷新消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                roleProducer.sendRoleRefreshMessage();
            }
        });
        // 返回
        return role.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(RoleUpdateReqVO reqVO) {
        // 校验是否可以更新
        checkUpdateRole(reqVO.getId());
        // 校验角色的唯一字段是否重复
        checkDuplicateRole(reqVO,reqVO.getId());
        // 更新到数据库
        RoleDO updateObject = RoleConvert.INSTANCE.convert(reqVO);
        // 不能更新应用字段
        updateObject.setClientId(null);
        // 更新数据权限
        updateObject.setDataScope(reqVO.getDataScope());
        updateObject.setDataScopeDeptIds(reqVO.getDataScopeDeptIds());
        roleMapper.updateById(updateObject);
        // 更新菜单权限
        permissionService.assignRoleMenu(reqVO.getId(),reqVO.getRoleMenuIds());
        // 发送刷新消息
        roleProducer.sendRoleRefreshMessage();
    }

    @Override
    public void updateRoleStatus(Long id, Integer status) {
        // 校验是否可以更新
        checkUpdateRole(id);
        // 更新状态
        RoleDO updateObject = new RoleDO();
        updateObject.setId(id);
        updateObject.setStatus(status);
        roleMapper.updateById(updateObject);
        // 发送刷新消息
        roleProducer.sendRoleRefreshMessage();
    }

    @Override
    public void updateRoleDataScope(Long id, Integer dataScope, Set<Long> dataScopeDeptIds) {
        // 校验是否可以更新
        checkUpdateRole(id);
        // 更新数据范围
        RoleDO updateObject = new RoleDO();
        updateObject.setId(id);
        updateObject.setDataScope(dataScope);
        updateObject.setDataScopeDeptIds(dataScopeDeptIds);
        roleMapper.updateById(updateObject);
        // 发送刷新消息
        roleProducer.sendRoleRefreshMessage();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        // 校验是否可以更新
        this.checkUpdateRole(id);
        // 标记删除
        roleMapper.deleteById(id);
        // 删除相关数据
        permissionService.processRoleDeleted(id);
        // 发送刷新消息. 注意，需要事务提交后，在进行发送刷新消息。不然 db 还未提交，结果缓存先刷新了
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                roleProducer.sendRoleRefreshMessage();
            }

        });
    }

    /**
     * 获得角色，从缓存中
     *
     * @param id 角色编号
     * @return 角色
     */
    @Override
    public RoleDO getRoleFromCache(Long id) {
        return roleCache.get(id);
    }

    /**
     * 获得角色，优先从缓存中获取
     *
     * @param id 角色编号
     * @return 角色
     */
    @Override
    public RoleDO getRoleById(Long id) {
        RoleDO roleDO = roleCache.get(id);
        if (Objects.isNull(roleDO)) {
            roleDO = roleMapper.selectById(id);
        }
        return roleDO;
    }

    /**
     * 获得角色集合，优先从缓存中获取
     *
     * @param ids 角色编号集合
     * @return 角色集合
     */
    @Override
    public List<RoleDO> getRolesByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return ids.stream().map(this::getRoleById).collect(Collectors.toList());
    }

    /**
     * 根据用户id获得角色列表下拉框
     *
     * @param userId 用户id
     * @return 角色列表
     */
    @Override
    public List<RoleClientSimpleRespVO> getSimpleRoleList(Long userId) {
        // 1、获取用户
        AdminUserDO user = userService.getUser(userId);
        // 用户所属的机构id
        Long tenantId = user.getTenantId();
        TenantDO tenant = tenantService.getTenantFromCache(tenantId);
        Set<Long> roleIdSet;

        if (Objects.equals(1L,tenantId)) {
            // 超管的机构，查出tenant = 0 的角色
            roleIdSet = roleCache.values().stream().filter(r -> Arrays.asList(0L,1L).contains(r.getId()) // 属于该机构的
                            && Objects.equals(r.getStatus(), CommonStatusEnum.ENABLE.getStatus()) // 启用状态的
                    ).map(RoleDO::getId).collect(Collectors.toSet());
        } else {
            // 用户所在机构的机构管理员拥有的所有的角色
            roleIdSet = permissionService.getAllRoleIdsFromCache(tenant.getContactUserId(), CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus()));
        }

        // 增加用户所属机构的自定义的所有已启用的角色
        Set<Long> customRoleIdSet = roleCache.values().stream()
                .filter(r -> Objects.equals(r.getTenantId(), tenantId) // 属于该机构的
                        && Objects.equals(r.getType(), RoleTypeEnum.CUSTOM.getType()) // 类型为自定义的
                        && Objects.equals(r.getStatus(), CommonStatusEnum.ENABLE.getStatus()) // 启用状态的
                ).map(RoleDO::getId).collect(Collectors.toSet());
//        if(CollUtil.isEmpty(roleIdSet)){
//            roleIdSet = new HashSet<Long>();
//        }
        Set<Long> roleIdSet1 = new HashSet<>(roleIdSet);
        if (CollUtil.isNotEmpty(customRoleIdSet)) {
            roleIdSet1.addAll(customRoleIdSet);
        }
        // 封装最后的结果数据
        return dealRoleDoToClients(getRolesByIds(roleIdSet1));
    }

    /**
     * 封装结果数据
     * @param list 角色
     * @return 封装结果
     */
    private List<RoleClientSimpleRespVO> dealRoleDoToClients(List<RoleDO> list) {
        // 过滤掉机构临时管理员
        list.removeIf(r -> Objects.equals(TEMP_TENANT_ROLE_ID, r.getId()));
        // 找出应用id
        Set<Long> clientSet = list.stream().map(RoleDO::getClientId).collect(Collectors.toSet());
        List<RoleClientSimpleRespVO> result = new ArrayList<>();
        // 根据应用id封装结果
        for (Long id : clientSet) {
            result.add(RoleClientSimpleRespVO.builder()
                    .id(UUID.fastUUID().toString())
                    .name(oAuth2ClientService.getNameFromCache(id))
                    .children(list.stream().filter(roleDO -> roleDO.getClientId().equals(id))
                            .map(roleDO -> RoleSimpleRespVO.builder()
                                    .id(roleDO.getId())
//                                    .editable(!isLoginUser)
                                    .name(roleDO.getName()).build())
                            .collect(Collectors.toList()))
                    .build());
        }
        if (CollUtil.isEmpty(result)) {
            throw exception(ROLE_IS_EMPTY);
        }
        return result;
    }

    /**
     * 根据角色类型查询角色列表下拉框
     * @param type 角色类型
     * @return 角色列表
     */
    @Override
    public List<RoleClientSimpleRespVO> getClientSimpleRoles(Integer type) {
        List<RoleDO> list = roleMapper.selectListEnabled(type);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 去除超级管理员角色和机构临时管理员角色
        list.removeIf(r -> Arrays.asList(SUPER_ADMIN_ROLE_ID,TEMP_TENANT_ROLE_ID).contains(r.getId()));
        // 过滤当前用户没有权限的角色(非超管才需要过滤)
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            Set<Long> roleIds = permissionService.getAllEnableRoleIdListFromCache(getLoginUserId());
            list.removeIf(r -> !CollUtil.contains(roleIds,r.getId()));
        }
        Map<Long, List<RoleDO>> clientMap = list.stream().collect(Collectors.groupingBy(RoleDO::getClientId));
        if (CollUtil.isEmpty(clientMap)) {
            return Collections.emptyList();
        }

        // 封装结果
        List<RoleClientSimpleRespVO> result = new ArrayList<>(clientMap.size());
        clientMap.forEach((clientId, roles) -> result.add(RoleClientSimpleRespVO.builder()
                .id(UUID.fastUUID().toString())
                .name(oAuth2ClientService.getNameFromCache(clientId))
                .children(roles.stream().map(roleDO -> RoleSimpleRespVO.builder()
                        .id(roleDO.getId())
                        .name(roleDO.getName()).build()).collect(Collectors.toList()))
                .build()));

        return result;
    }

    @Override
    public boolean hasAnySuperAdmin(Collection<RoleDO> roleList) {
        if (CollectionUtil.isEmpty(roleList)) {
            return false;
        }
        return roleList.stream()
                .filter(Objects::nonNull).anyMatch(role -> RoleCodeEnum.isSuperAdmin(role.getCode()));
    }

    @Override
    public RoleRespVO getRole(Long id) {
        RoleDO role = roleMapper.selectById(id);
        RoleRespVO respVO = RoleConvert.INSTANCE.convert(role);

        // 当前用户所拥有的角色
        Set<Long> roleIds = permissionService.getAllEnableRoleIdListFromCache(getLoginUserId());
        respVO.setEditable(!roleIds.contains(respVO.getId()));


//        // 当前用户所拥有的角色的菜单
//        Set<Long> menuids = new HashSet<>();
//        roleIds.forEach(it->{
//            menuids.addAll(permissionService.getRoleMenuIds(it));
//        });
//
//
//        // 查询角色所拥有的角色的菜单
//        Set<Long> rolemenuids = permissionService.getRoleMenuIds(id);
//
//        // admin给所有
//        if(roleIds.contains(1L)){
//            respVO.setRoleMenuIds(rolemenuids);
//        }else  if(role.getType()!=null && role.getType().intValue()==1){
//            respVO.setRoleMenuIds(rolemenuids);
//        }else{
//
//            Collection<Long> interset =  CollUtil.intersection(menuids , rolemenuids);
//            respVO.setRoleMenuIds( new HashSet<>(interset));
//        }

        return respVO;
    }

    @Override
    public RoleUsersRespVO getRoleUsers(RoleUsersPageReqVO pageReqVO) {
        //角色基本信息
        RoleUsersRespVO roleUsersRespVO = roleMapper.selectBaseRole(pageReqVO.getRoleId());
        //用户精简信息
        PageResult<UserSimpleRespVO> users = userService.getUsersByUserIds(pageReqVO);
        roleUsersRespVO.setUsers(users);
        return roleUsersRespVO;
    }

    @Override
    public PageResult<RoleRespVO> getRolePage(RolePageReqVO reqVO) {
        Long tenantId = getTenantId();
        // 模糊查询机构名称转换成机构id
        List<Long> tenantIds = tenantMapper.selectIdListByNameLike(reqVO.getTenantName());
        // 超管查询所有的角色
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            tenantId = null;
        }
        // 机构绑定的角色
        Set<Long> tenantRoles = permissionService.getRoleIdListByTenantId(getTenantId());
        // 分页查询
        PageResult<RoleDO> list = roleMapper.selectPage(reqVO,tenantIds,tenantId,tenantRoles);

        // 移除刚注册的临时管理员
        list.getList().removeIf(r -> Objects.equals(TEMP_TENANT_ROLE_ID,r.getId()));
        PageResult<RoleRespVO> result = RoleConvert.INSTANCE.convertPage(list);

        // 超管不可修改
        tenantRoles.add(SUPER_ADMIN_ROLE_ID);
        for (RoleRespVO respVO : result.getList()) {
            // 不可修改的标识
            respVO.setEditable(!tenantRoles.contains(respVO.getId()));
        }
        return result;
    }

    @Override
    public List<RoleDO> getRoleList(RoleExportReqVO reqVO) {
        Long tenantId = getTenantId();
        // 模糊查询机构名称转换成机构id
        List<Long> tenantIds = tenantMapper.selectIdListByNameLike(reqVO.getTenantName());
        // 超管查询所有的角色
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            tenantId = null;
        }
        // 机构绑定的角色
        Set<Long> tenantRoles = permissionService.getRoleIdListByTenantId(getTenantId());
        List<RoleDO> result = roleMapper.selectList(reqVO,tenantIds,tenantId,tenantRoles);
        // 移除刚注册的临时管理员
        result.removeIf(r -> Objects.equals(TEMP_TENANT_ROLE_ID,r.getId()));
        return result;
    }

    /**
     * 校验角色的唯一字段是否重复
     *
     * 1. 是否存在相同名字的角色
     * 2. 是否存在相同编码的角色
     *
     * @param roleVO 角色
     * @param id 角色id
     */
    @VisibleForTesting
    public void checkDuplicateRole(RoleBaseVO roleVO, Long id) {
        // 0. 超级管理员，不允许创建
        if (RoleCodeEnum.isSuperAdmin(roleVO.getCode())) {
            throw exception(ROLE_ADMIN_CODE_ERROR, roleVO.getCode());
        }
        RoleDO role;
        // 1. 该 name 名字被该应用下的其它角色所使用
        if (Objects.equals(roleVO.getType(),RoleTypeEnum.SYSTEM.getType())) {
            role = roleMapper.selectByNameAndApplication(roleVO.getName(),roleVO.getClientId());
        } else {
            role = roleMapper.selectByNameAndApplicationAndTenantId(roleVO.getName(),roleVO.getClientId(),getTenantId());
        }
        if (Objects.isNull(id)) {
            if (Objects.nonNull(role)) {
                throw exception(ROLE_NAME_DUPLICATE, roleVO.getName());
            }
        } else {
            if (Objects.nonNull(role) && !Objects.equals(role.getId(),id)) {
                throw exception(ROLE_NAME_DUPLICATE, roleVO.getName());
            }
        }
        // 2. 是否存在相同编码的角色
        if (!StringUtils.hasText(roleVO.getCode())) {
            return;
        }
        // 内置角色角色标识全局唯一，自定义角色，分机构和应用
        // 该 code 编码被其它角色所使用
        if (Objects.equals(roleVO.getType(),RoleTypeEnum.SYSTEM.getType())) {
            role = roleMapper.selectByCode(roleVO.getCode());
        } else {
            role = roleMapper.selectByCodeAndApplicationAndTenant(roleVO.getCode(),roleVO.getClientId(),getTenantId());
        }
        if (Objects.isNull(id)) {
            if (Objects.nonNull(role)) {
                throw exception(ROLE_CODE_DUPLICATE, roleVO.getCode());
            }
        } else {
            if (Objects.nonNull(role) && !Objects.equals(role.getId(),id)) {
                throw exception(ROLE_CODE_DUPLICATE, roleVO.getCode());
            }
        }
    }

    /**
     * 校验角色是否可以被更新
     *
     * @param id 角色编号
     */
    @VisibleForTesting
    public void checkUpdateRole(Long id) {
        RoleDO roleDO = roleMapper.selectById(id);
        if (roleDO == null) {
            throw exception(ROLE_NOT_EXISTS);
        }
        // 内置角色，不允许删除
//        if (RoleTypeEnum.SYSTEM.getType().equals(roleDO.getType())) {
//            throw exception(ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE);
//        }
    }

    @Override
    public void validRoles(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得角色信息
        List<RoleDO> roles = roleMapper.selectBatchIds(ids);
        Map<Long, RoleDO> roleMap = CollectionUtils.convertMap(roles, RoleDO::getId);
        // 校验
        ids.forEach(id -> {
            RoleDO role = roleMap.get(id);
            if (role == null) {
                throw exception(ROLE_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())) {
                throw exception(ROLE_IS_DISABLE, role.getName());
            }
        });
    }

    @Override
    public Long getRoleByName(String name) {
        LambdaQueryWrapperX<RoleDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(RoleDO::getName, name);
        lambdaQueryWrapperX.last(" limit 1 ");
        RoleDO roleDO = roleMapper.selectOne(lambdaQueryWrapperX);
        return  roleDO == null ? null : roleDO.getId();
    }

    @Override
    public Long getInnerRoleIdByCode(String code) {
        LambdaQueryWrapperX<RoleDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(RoleDO::getCode, code);
        lambdaQueryWrapperX.eq(RoleDO::getType, RoleTypeEnum.SYSTEM.getType());
        lambdaQueryWrapperX.last(" limit 1 ");
        RoleDO roleDO = roleMapper.selectOne(lambdaQueryWrapperX);
        return  roleDO == null ? null : roleDO.getId();
    }

    @Override
    public Long getCustomRoleIdByCode(String code) {
        LambdaQueryWrapperX<RoleDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(RoleDO::getCode, code);
        lambdaQueryWrapperX.eq(RoleDO::getType, RoleTypeEnum.CUSTOM.getType());
        lambdaQueryWrapperX.last(" limit 1 ");
        RoleDO roleDO = roleMapper.selectOne(lambdaQueryWrapperX);
        return  roleDO == null ? null : roleDO.getId();
    }

    @Override
    public List<RoleDO> getRolesByClientId(Long clientId) {
        return roleCache.values().stream().filter(r -> Objects.equals(r.getClientId(),clientId)).collect(Collectors.toList());
    }

}
