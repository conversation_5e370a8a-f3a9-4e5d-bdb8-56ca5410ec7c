package com.unicom.swdx.module.system.dal.dataobject.oaNotice;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.module.system.enums.notice.NoticeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 通知公告表
 *
 * <AUTHOR>
 */
@TableName("oa_notice")
@KeySequence("oa_notice_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class OaNoticeDO {

    /**
     * 公告ID
     */
    @TableId(type= IdType.AUTO)
    private Long id;
    /**
     * 公告标题
     */
    private String title;
    /**
     * 公告类型
     *
     * 枚举 {1为通知，2为公告}
     */
    @TableField(value = "notice_type")
    private Integer type;
    /**
     * 公告内容
     */
    private String content;
    /**
     * 公告状态，1为已发布，2为草稿
     *
     * 枚举 {1为已发布，2为草稿}
     */
    @TableField(value = "notice_status")
    private Integer status;
    /**
     * 创建人id
     */
    private Long creatorId;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;
    /**
     * 接收人部门id列表，以|号分隔
     */
    @TableField(value = "receive_dept_id")
    private String recipientId;
    /**
     * 接收人部门id列表，以|号分隔
     */
    private String recipientUserId;
    /**
     * 文件路径
     */
    @TableField(value = "file")
    private String fileUrl;
    /**
     * 文件权限,1仅查看，2下载与查看
     */
    private Integer fileAuthority;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 更新人id
     */
    private String updater;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
    /**
     * 是否下架
     */
    private Boolean removed;
    /**
     * 阅读量
     */
    private Long readNumber;
    /**
     * 置顶状态，0为不置顶，1为置顶
     */
    private Integer isTopNotice;
    /**
     * 置顶时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime topTime;

    /**
     * 是否是招聘公告，0不是，1是
     */
    @ApiModelProperty(value = "是否是招聘公告，0不是，1是", required = true, example = "0")
    private Integer isRecruit;


    private Long tenantId;
}
