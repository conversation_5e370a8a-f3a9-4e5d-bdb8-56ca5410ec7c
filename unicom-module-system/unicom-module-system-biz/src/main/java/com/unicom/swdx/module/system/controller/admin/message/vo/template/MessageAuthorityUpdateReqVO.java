package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@ApiModel("管理后台 - 消息模板新增 Request VO")
@Data
public class MessageAuthorityUpdateReqVO {
    /**
     * 数据范围ID列表，以逗号分隔
     */
 //   @NotNull(message = "数据范围ID")
    private String dataScopeIds;

    /**
     * 数据范围名字，以逗号分隔
     */
 //   @NotNull(message = "数据范围名字")
    private String dataScopeNames;
    /**
     * 用户ID
     */
//    @NotNull(message = "用户id不能为空")
    private Long userId;
    /**
     * 用户ID
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 名字
     */
//    @NotNull(message = "名字不能为空")
    private String name;

    @ApiModelProperty(value = "账号状态")
    private Integer status;

}
