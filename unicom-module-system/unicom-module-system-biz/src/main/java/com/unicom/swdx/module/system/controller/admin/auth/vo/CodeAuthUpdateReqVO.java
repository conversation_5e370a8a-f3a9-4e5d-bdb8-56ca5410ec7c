package com.unicom.swdx.module.system.controller.admin.auth.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unicom.swdx.framework.jackson.core.databind.SMDecryptDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import static com.unicom.swdx.module.system.enums.common.CommonConstants.PASSWORD_REGEX;

@ApiModel(value = "管理后台 - 手机号验证码修改密码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CodeAuthUpdateReqVO {

    @ApiModelProperty(value = "手机号", required = true, example = "12345678910")
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "验证码", required = true, example = "1234", notes = "验证码开启时，需要传递")
    @NotBlank(message = "验证码不能为空", groups = AuthLoginReqVO.CodeEnableGroup.class)
    private String verification;

    @ApiModelProperty(value = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    @Pattern(regexp = PASSWORD_REGEX, message = "密码需同时包含大小写字母、数字、特殊字符")
    @JsonDeserialize(using = SMDecryptDeserializer.class)
    private String newPassword;

}
