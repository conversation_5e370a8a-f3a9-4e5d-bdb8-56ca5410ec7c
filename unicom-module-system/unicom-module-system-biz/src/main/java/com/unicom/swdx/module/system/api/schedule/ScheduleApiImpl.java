package com.unicom.swdx.module.system.api.schedule;

import cn.hutool.core.bean.BeanUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.controller.admin.home.schedule.vo.ScheduleCreateReqVO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import com.unicom.swdx.module.system.service.schedule.ScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDate;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@DubboService(version = ApiConstants.VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Service
@Slf4j
@RestController // 提供 RESTful API 接口，给 Feign 调用
public class ScheduleApiImpl implements ScheduleServiceApi {

    @Resource
    private ScheduleService scheduleService;

    @Override
    public CommonResult<Long> createScheduleOther(ScheduleDto scheduleDto) {
        if (scheduleDto.getUserId() == null) {
            scheduleDto.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }
        ScheduleCreateReqVO scheduleCreateReqVO = new ScheduleCreateReqVO();
        BeanUtil.copyProperties(scheduleDto, scheduleCreateReqVO);
        scheduleCreateReqVO.setContent("日程内容");
        scheduleCreateReqVO.setScheduleDate(LocalDate.now());
        return success(scheduleService.createScheduleOther(scheduleCreateReqVO).longValue());
    }

    @Override
    public CommonResult<Long> createScheduleInfor(ScheduleDto scheduleDto) {
        if (scheduleDto.getUserId() == null) {
            scheduleDto.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }
        ScheduleCreateReqVO scheduleCreateReqVO = new ScheduleCreateReqVO();
        BeanUtil.copyProperties(scheduleDto, scheduleCreateReqVO);
        scheduleCreateReqVO.setContent("日程内容");
        scheduleCreateReqVO.setScheduleDate(LocalDate.now());
        //任务名称
        scheduleCreateReqVO.setTaskname(scheduleDto.getTaskname());
        //添加中心工作id
        scheduleCreateReqVO.setInforId(scheduleDto.getInforId());
        //中心工作类型
        scheduleCreateReqVO.setYeartag(scheduleDto.getYeartag());
        return success(scheduleService.createScheduleInfor(scheduleCreateReqVO).longValue());
    }

    @Override
    public CommonResult<Long> createSchedulesOther(ScheduleDto scheduleDto) {
        if (scheduleDto.getUserId() == null) {
            scheduleDto.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }
        ScheduleCreateReqVO scheduleCreateReqVO = new ScheduleCreateReqVO();
        BeanUtil.copyProperties(scheduleDto, scheduleCreateReqVO);
        //设置抄送人和参与人
        scheduleCreateReqVO.setUserIds(scheduleDto.getUserIds());
        scheduleCreateReqVO.setWeek(scheduleDto.getWeek());
        scheduleCreateReqVO.setYear(scheduleDto.getYear());
        scheduleCreateReqVO.setContent("日程内容");
        scheduleCreateReqVO.setScheduleDate(LocalDate.now());
        return success(scheduleService.createSchedulesOther(scheduleCreateReqVO).longValue());
    }

    @Override
    public CommonResult<Long> deleteScheduleOther(ScheduleDto scheduleDto) {
        if (scheduleDto.getUserId() == null) {
            scheduleDto.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }
        ScheduleCreateReqVO scheduleCreateReqVO = new ScheduleCreateReqVO();
        BeanUtil.copyProperties(scheduleDto, scheduleCreateReqVO);
        scheduleCreateReqVO.setContent("日程内容");
        scheduleCreateReqVO.setScheduleDate(LocalDate.now());
        return success(scheduleService.deleteScheduleOther(scheduleCreateReqVO).longValue());
    }

    @Override
    public CommonResult<Long> deleteScheduleInfor(ScheduleDto scheduleDto) {
        if (scheduleDto.getUserId() == null) {
            scheduleDto.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }

        ScheduleCreateReqVO scheduleCreateReqVO = new ScheduleCreateReqVO();
        BeanUtil.copyProperties(scheduleDto, scheduleCreateReqVO);
        scheduleCreateReqVO.setContent("日程内容");
        scheduleCreateReqVO.setScheduleDate(LocalDate.now());
        //删除中心工作日程
        scheduleCreateReqVO.setInforId(scheduleDto.getInforId());
        return success(scheduleService.deleteScheduleInfor(scheduleCreateReqVO).longValue());
    }
//
//    @Override
//    public CommonResult<Boolean> deleteSchedule(String processInstanceId) {
//        scheduleService.deleteMeetingSchedule(processInstanceId);
//        return success(true);
//    }
//
//    @Override
//    public CommonResult<Integer> cancelMeetingSchedule(String processInstanceId) {
//        int cancelCount = scheduleService.cancelMeetingSchedule(processInstanceId);
//        return success(cancelCount);
//    }
//
//    @Override
//    public CommonResult<Integer> updateScheduleMeetingStatus(String processInstanceId, String meetingStatus) {
//        int updateCount = scheduleService.updateScheduleMeetingStatus(processInstanceId, meetingStatus);
//        return success(updateCount);
//    }
//
//    @Override
//    public CommonResult<Integer> updateMeetingSchedule(String processInstanceId, Long attendeeId, String isAttend) {
//        int updateCount = scheduleService.updateMeetingSchedule(processInstanceId, attendeeId, isAttend);
//        return success(updateCount);
//    }
}
