package com.unicom.swdx.module.system.controller.admin.yezhong;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.dal.dataobject.message.YzuCreateUserMessage;
import com.unicom.swdx.module.system.service.yezhong.YezhongService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 业中同步服务")
@RestController
@RequestMapping("/system/yezhong")
@Validated
@Slf4j
public class YeZhongController {

    @Resource
    YezhongService yezhongService;

    @GetMapping("/getdict")
    @Operation(summary = "同步字典")
    public CommonResult<Boolean> getDict() {
        yezhongService.getDictType();
        yezhongService.getDictData();
        return success(true);
    }


    @GetMapping("/createStudentUser")
    @Operation(summary = "创建业中学员")
    public CommonResult<Boolean> createStudentUser(YzuCreateUserMessage.BodyDTO.StudentDTO studentInfor) {
        yezhongService.createStudentUser(studentInfor);
        return success(true);
    }
}
