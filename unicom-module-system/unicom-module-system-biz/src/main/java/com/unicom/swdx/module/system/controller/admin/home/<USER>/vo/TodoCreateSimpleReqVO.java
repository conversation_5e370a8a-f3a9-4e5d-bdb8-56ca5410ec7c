package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import com.unicom.swdx.module.system.enums.home.SubSystemEnum;
import com.unicom.swdx.module.system.enums.home.TodoType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("管理后台 - 待办事项创建 Request VO")
@Data
public class TodoCreateSimpleReqVO {

    /**
     * {@link SubSystemEnum}
     */
    @ApiModelProperty(value = "发起子系统id（见枚举SubSystemEnum）", required = true)
    @NotNull(message = "发起子系统id不能为空")
    private Integer subsystemId;
    /**
     * {@link TodoType}
     */
    @ApiModelProperty(value = "待办类型（见枚举TodoType）", required = true)
    @NotNull(message = "待办类型不能为空")
    private Integer type;

    @ApiModelProperty("事项")
    @NotNull(message = "待办事项不能为空")
    private Integer item;

    @ApiModelProperty(value = "待办事项id",notes = "针对事项的唯一id")
    @NotNull(message = "待办事项id不能为空")
    private String todoId;

    @ApiModelProperty(value = "标题", required = true)
    @NotBlank(message = "标题不能为空")
    private String title;

    @ApiModelProperty(value = "跳转路径")
    private String linkUrl;

    @ApiModelProperty(value = "发起人userId", required = true)
    @NotNull(message = "发起人不能为空")
    private Long submitter;

    @ApiModelProperty(value = "发起时间", required = true)
    @NotNull(message = "发起时间不能为空")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "菜单权限标识", required = true)
    @NotBlank(message = "菜单权限标识不能为空")
    private String permission;

    @ApiModelProperty(value = "备注")
    private String remark;

}
