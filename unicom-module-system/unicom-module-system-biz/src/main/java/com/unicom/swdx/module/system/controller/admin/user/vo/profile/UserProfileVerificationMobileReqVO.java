package com.unicom.swdx.module.system.controller.admin.user.vo.profile;

import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "管理后台 - 用户个人信息验证手机号码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserProfileVerificationMobileReqVO {

    @ApiModelProperty(value = "手机号", required = true, example = "12345678910")
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "验证码", required = true, example = "1234", notes = "验证码开启时，需要传递")
    @NotBlank(message = "验证码不能为空", groups = AuthLoginReqVO.CodeEnableGroup.class)
    private String verification;

}
