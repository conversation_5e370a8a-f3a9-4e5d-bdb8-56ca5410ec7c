package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.unicom.swdx.module.system.enums.DictTypeConstants.COMMON_STATUS;

/**
 * 用户 Excel 导出 VO
 */
@Data
public class UserExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("用户名")
    private String username;

    @ExcelProperty("姓名")
    private String nickname;

    @ExcelProperty("所属组织")
    private String deptName;

    @ExcelProperty("手机号码")
    private String mobile;

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @ExcelProperty("岗位")
    private String post;

    @ExcelProperty(value = "用户状态", converter = DictConvert.class)
    @DictFormat(COMMON_STATUS)
    private String status;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @ExcelProperty("最后登录时间")
    private LocalDateTime loginDate;

    @ExcelProperty("显示排序\n（必填）")
    @NotNull(message = "显示排序不能为空")
    private Integer sort;
}
