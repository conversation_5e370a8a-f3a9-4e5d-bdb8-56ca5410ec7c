package com.unicom.swdx.module.system.controller.admin.home.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("首页 - 日程完整信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ScheduleWholeRespVO extends ScheduleBaseVO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("日程类型（0=默认）")
    private Integer type;

    @ApiModelProperty("日程拓展内容")
    private String extContent;

    @ApiModelProperty("流程id")
    private String processInstanceId;

    @ApiModelProperty("流程id")
    /**
     * 日程拓展内容（json存储）
     */
    private Integer inforId;
}
