package com.unicom.swdx.module.system.controller.admin.message.vo.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("管理后台 - 机构 Response VO")
@Data
@ToString(callSuper = true)
public class MessageRespGetVO {

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "备注")
    private String remark;

}
