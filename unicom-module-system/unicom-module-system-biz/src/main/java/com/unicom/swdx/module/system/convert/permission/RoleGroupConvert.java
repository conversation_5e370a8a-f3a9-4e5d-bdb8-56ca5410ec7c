package com.unicom.swdx.module.system.convert.permission;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupRespVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupUpdateReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupUsersRespVO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RoleGroupConvert {
    RoleGroupConvert INSTANCE = Mappers.getMapper(RoleGroupConvert.class);

    RoleGroupDO convert(RoleGroupCreateReqVO bean);

    RoleGroupDO convert(RoleGroupUpdateReq<PERSON> bean);

    PageResult<RoleGroupRespVO> convertPage(PageResult<RoleGroupDO> list);

    RoleGroupRespVO convert(RoleGroupDO roleGroup);


    RoleGroupUsersRespVO convert1(RoleGroupDO roleGroup);
}
