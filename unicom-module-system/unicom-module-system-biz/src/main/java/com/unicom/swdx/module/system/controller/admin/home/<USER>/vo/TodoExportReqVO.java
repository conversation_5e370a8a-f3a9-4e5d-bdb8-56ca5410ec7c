package com.unicom.swdx.module.system.controller.admin.home.todo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel(value = "管理后台 - 待办事项 Excel 导出 Request VO", description = "参数和 TodoPageReqVO 是一致的")
@Data
public class TodoExportReqVO {

    @ApiModelProperty(value = "类型（见枚举）")
    private Integer type;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "发起人")
    private String submitter;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] submitTime;

    @ApiModelProperty(value = "发起子系统id")
    private Integer subsystemId;

    @ApiModelProperty(value = "跳转链接")
    private String linkUrl;

    @ApiModelProperty(value = "待办人")
    private String todoUserId;

    @ApiModelProperty(value = "状态（0=待办，1=已办）")
    private Boolean status;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
