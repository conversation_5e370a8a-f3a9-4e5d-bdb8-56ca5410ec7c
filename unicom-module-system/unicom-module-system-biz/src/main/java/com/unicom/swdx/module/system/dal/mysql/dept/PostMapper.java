package com.unicom.swdx.module.system.dal.mysql.dept;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.query.QueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostExcelVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostExportReqVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostPageReqVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostRespVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface PostMapper extends BaseMapperX<PostDO> {

    List<PostRespVO> selectPostPage(IPage page, @Param("param") PostPageReqVO reqVO);
    List<PostExcelVO> selectPostList(@Param("param") PostExportReqVO reqVO,@Param("tenantId") Long tenantId);
    default List<PostDO> selectList(Collection<Long> ids, Collection<Integer> statuses, Long tenantId,Boolean hasAdminRole) {
        LambdaQueryWrapperX<PostDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<PostDO>()
                .inIfPresent(PostDO::getId,ids)
                .eq(PostDO::getDisplayState, true)
                .inIfPresent(PostDO::getStatus, statuses);
        if(!hasAdminRole){
            lambdaQueryWrapperX.eqIfPresent(PostDO::getTenant_id,tenantId);
        }
        return selectList(lambdaQueryWrapperX);
    }

    default PageResult<PostDO> selectPage(PostPageReqVO reqVO) {
        return selectPage(reqVO, new QueryWrapperX<PostDO>()
                .eqIfPresent("status", reqVO.getStatus())
                .likeIfPresent("name",reqVO.getName())
                .and(StrUtil.isNotBlank(reqVO.getCode()), l -> l.like("code",
                        reqVO.getCode()).or().like("name",reqVO.getCode()))
                .orderByDesc("id"));
    }

    default List<PostDO> selectList(PostExportReqVO reqVO) {
        return selectList(new QueryWrapperX<PostDO>()
                .likeIfPresent("code", reqVO.getCode())
                .likeIfPresent("name", reqVO.getName())
                .eqIfPresent("status", reqVO.getStatus()));
    }

    default PostDO selectByName(String name,Long tenantId) {
        return selectOne(new QueryWrapper<PostDO>()
                .eq("name", name)
                .eq("tenant_id", tenantId));
    }

    default PostDO selectByCode(String code,Long tenantId) {
        return selectOne(new QueryWrapperX<PostDO>()
                .eq("code", code)
                .eqIfPresent("tenant_id", tenantId));
    }

    default PostDO selectByCode(String code) {
        return selectOne(new QueryWrapper<PostDO>()
                .eq("code", code));
    }

    default List<PostDO> selectList(Long tenantId) {
        return selectList(new QueryWrapperX<PostDO>()
                .eqIfPresent("status", 0)
                .eqIfPresent("deleted",0)
                .eqIfPresent("tenant_id", tenantId));
    }

}
