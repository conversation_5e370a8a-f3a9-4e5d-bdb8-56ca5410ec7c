package com.unicom.swdx.module.system.controller.admin.dept.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 多部门人数统计")
@Data
@ToString(callSuper = true)
public class DeptCountVO {


    @ApiModelProperty(value = "多部门对应的人数", required = true, example = "1024")
    private Long count;

    @ApiModelProperty(value = "部门编号", example = "2048")
    private Long deptId;
}
