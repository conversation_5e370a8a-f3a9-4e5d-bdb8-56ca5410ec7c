package com.unicom.swdx.module.system.controller.admin.sms;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.controller.admin.sms.vo.send.SmsVerificationCodeReqVO;
import com.unicom.swdx.module.system.service.sms.SmsSendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2023/2/24 17:29
 **/
@Api(tags = "管理后台 - 发送短信")
@RestController
@RequestMapping("/system/sms-send")
public class SmsSendController {

    @Resource
    public SmsSendService smsSendService;

    @PostMapping("/verification-code")
    @ApiOperation("发送验证码短信")
    @PermitAll
    public CommonResult<Boolean> sendSms(@RequestBody @Valid SmsVerificationCodeReqVO reqVO) {
        Map<String, Object> content = new HashMap<>();
        content.put("arg1",reqVO.getCode());
        content.put("arg2",reqVO.getItem());

        //smsSendService.sendSingleSmsToAdminDX(reqVO.getMobile(), null,);
        return success(true);
    }

}
