package com.unicom.swdx.module.system.mq.producer.permission;

import com.unicom.swdx.module.system.mq.message.RefreshMessage;
import com.unicom.swdx.module.system.mq.producer.AbstractProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Role 角色相关消息的 Producer
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RoleProducer extends AbstractProducer{

    @Async
    public void sendRoleRefreshMessage() {
        log.info("[send][ Role 发送刷新消息]");
        kafkaTemplate.send("refresh-service", "RoleRefresh");
    }

}
