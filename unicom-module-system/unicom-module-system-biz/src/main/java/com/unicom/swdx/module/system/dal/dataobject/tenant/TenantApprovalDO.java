package com.unicom.swdx.module.system.dal.dataobject.tenant;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler;
import com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构认证审批 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_approval", autoResultMap = true)
@KeySequence("system_tenant_approval_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantApprovalDO extends BaseDO {

    /**
     * 审批申请id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long linkedTenantId;

    /**
     * 机构类型 TENANT_TYPE
     */
    private Integer tenantType;
    /**
     * 机构级别 TENANT_LEVEL
     */
    private Integer tenantLevel;
    /**
     * 范围 LEVEL_RANGE
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> levelRange;
    /**
     * 业务指导单位 GUIDE_TENANT_ID
     */
    private Integer guideTenantId;

    /**
     * 单位类型
     */
    private Integer companyType;
    /**
     * 机构全称（原租户名）
     */
    private String name;
    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;
    /**
     * 注册地区划
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> registerRegion;
    /**
     * 注册地地址
     */
    private String registerAddress;
    /**
     * 所在地区划
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> locationRegion;
    /**
     * 所在地地址
     */
    private String locationAddress;
    /**
     * 法定代表人姓名
     */
    private String legalRepresentativeName;
    /**
     * 法人代表身份证号码
     */
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    private String legalRepresentativeIdCard;
    /**
     * 机构用户类型
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> instUserType;
    /**
     * 营业执照
     */
    private String businessLicenseUrl;
    /**
     * 联系人的用户编号
     */
    private Long contactUserId;
    /**
     * 机构管理员姓名
     */
    private String contactNickname;
    /**
     * 用户名（原联系人）
     */
    private String contactName;
    /**
     * 机构管理员手机号码（联系手机）
     */
    private String contactMobile;
    /**
     * 租户状态（0正常 1停用）
     */
    private Integer tenantStatus;
    /**
     * 绑定域名
     */
    @TableField("\"domains\"")
    private String domain;
    /**
     * 租户套餐编号
     */
    private Long packageId;
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    /**
     * 账号数量
     */
    private Integer accountCount;
    /**
     * 认证类型（0注册，1变更）
     */
    private Integer approvalType;
    /**
     * 变更内容
     */
    private String changeContent;
    /**
     * 申请人用户编号
     */
    private Long applicantId;
    /**
     * 申请时间
     */
    private LocalDateTime approvalTime;
    /**
     * 审批人用户编号
     */
    private Long approverId;
    /**
     * 审批状态（0待审批，1审批通过，2审批未通过）
     */
    private Integer approvalStatus;

    /**
     * 审批时间
     */
    private LocalDateTime examineTime;

    /**
     * 审核备注
     */
    private String remark;

}
