package com.unicom.swdx.module.system.controller.admin.databoard.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel("管理后台 - 近六个月机构入驻趋势 Response VO")
public class LastSixMonthTenantRespVO {

    @ApiModelProperty(value = "年月")
    private LocalDate yearMonth;

    @ApiModelProperty(value = "本月新增")
    private Long newTenant;

    @ApiModelProperty(value = "上月机构总数")
    private Long lastMonthTenant;

    @ApiModelProperty(value = "机构总数")
    private Long allTenant;

}
