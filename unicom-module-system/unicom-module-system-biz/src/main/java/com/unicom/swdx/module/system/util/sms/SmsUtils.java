package com.unicom.swdx.module.system.util.sms;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.unicom.swdx.framework.common.util.Aes.AesEncryptUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/24 15:48
 **/
@Slf4j
public class SmsUtils {

    private static final String url = "https://szxc.5gweilaixilie.cn/weixin/sign/sendAsyncMsg";

    public static boolean sendSms(String tel, String templateId,  Map<String, Object> content) {
        Map<String, Object> body = getSendBody(tel, templateId, content);
        HttpResponse response = null;
        try {
            response = HttpUtil.createPost("https://szxc.5gweilaixilie.cn/weixin/sign/sendAsyncMsg")
                    .body(JSON.toJSONString(body), "application/json").execute();
        } catch (Exception e) {
            log.error("短信发送失败，{}",body);
            return false;
        }
        JSONObject resp = JSONObject.parseObject(response.body());
        if (!resp.get("respcode").equals(3000)) {
            return false;
        }
        return true;
    }



    public static Map<String, Object> getSendBody(String tel, String templateid,  Map<String, Object> content){
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "12345");
        Map<String, Object> contentBody = new HashMap<>();
        contentBody.put("pipelinetype", "2");
        contentBody.put("templateid", templateid);
        contentBody.put("msgbatchid", "01");
        JSONArray array = new JSONArray();
        array.add(tel);
        contentBody.put("userArray", array.toJSONString());
        params.put("content", AesEncryptUtil.aesEncrypt(JSONObject.toJSONString(contentBody)));
        params.put("contentbody",  AesEncryptUtil.aesEncrypt(JSONObject.toJSONString(content)));
        return params;
    }

    public static void main(String[] args) {
        String tel = "18608404183";
        //String pipelinetype = "2";
        Map<String, Object> content = new HashMap<>();
        content.put("arg0","您有一条收文待处理，请尽快登录系统在待办进行处理。");
        String templateid = "gywlw3";
//        Map<String, Object> sendBody = getSendBody(tel,templateid, content);
//        System.out.println(JSON.toJSONString(sendBody));
//        HttpResponse response = HttpUtil.createPost("https://szxc.5gweilaixilie.cn/weixin/sign/sendAsyncMsg")
//                .body(JSON.toJSONString(sendBody), "application/json").execute();
//        System.out.println(response.body());

        //{"respdesc":"发送成功","data":"[{\"msgid\":\"1629031157358018562\",\"userid\":\"18608404145\",\"status\":\"0\"}]","respcode":3000}

        boolean b = sendSms(tel, templateid, content);
        System.out.println("b = " + b);
    }

}
