package com.unicom.swdx.module.system.controller.admin.dept.vo.dept;

import com.unicom.swdx.framework.common.validation.Mobile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 部门 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DeptBaseVO {

    @ApiModelProperty(value = "菜单名称", required = true, example = "sk")
    @NotBlank(message = "组织名称不能为空")
    @Size(max = 30, message = "组织名称长度不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "父菜单 ID", example = "1024")
    @NotNull(message = "上级组织不能为空")
    private Long parentId;

    @ApiModelProperty(value = "显示顺序不能为空", required = true, example = "1024")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @ApiModelProperty(value = "负责人的用户编号", example = "2048")
    private Long leaderUserId;

    @ApiModelProperty(value = "联系电话", example = "15601691000")
    @Size(max = 11, message = "联系电话长度不能超过11个字符")
    @Mobile(message = "手机格式不正确")
    private String phone;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "见 CommonStatusEnum 枚举")
    @NotNull(message = "状态不能为空")
//    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

    private String code;

    private Integer type;


}
