package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 用户信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserRespVO extends UserBaseVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1")
    private Long id;

    @ApiModelProperty(value = "状态", required = true, example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "最后登录 IP", required = true, example = "***********")
    private String loginIp;

    @ApiModelProperty(value = "最后登录时间", required = true, example = "时间戳格式")
    private LocalDateTime loginDate;

    @ApiModelProperty(value = "创建时间", required = true, example = "时间戳格式")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "信息更新时间", required = true, example = "时间戳格式")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "父级部门名称")
    private String parentDeptName;

    @ApiModelProperty(value = "机构id")
    private Long tenantId;

    @ApiModelProperty(value = "人员状态",example = "1")
    private Integer personnalStatus;

    @ApiModelProperty(value = "人员分类",example = "1")
    private Integer personnalClassification;
}
