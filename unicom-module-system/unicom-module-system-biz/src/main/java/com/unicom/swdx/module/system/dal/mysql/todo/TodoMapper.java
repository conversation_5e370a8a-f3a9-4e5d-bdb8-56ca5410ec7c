package com.unicom.swdx.module.system.dal.mysql.todo;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.todo.dto.TodoItemUpdateReqDTO;
import com.unicom.swdx.module.system.controller.admin.home.todo.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.todo.TodoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 待办事项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TodoMapper extends BaseMapperX<TodoDO> {

    List<TodoDO> selectTodoPage(IPage page, @Param("userId") Long userId, @Param("param") TodoPageReqVO pageReqVO, @Param("permissions")Collection<String> permissions);


    default PageResult<TodoDO> selectPage(Long userId, TodoPageReqVO reqVO) {
        return selectPage(reqVO,new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getTodoUserId,userId)
                .eqIfPresent(TodoDO::getStatus,reqVO.getStatus())
                .eqIfPresent(TodoDO::getType,reqVO.getType())
                //.eqIfPresent(TodoDO::getReceipt,reqVO.getReceipt())
                .and(StrUtil.isNotBlank(reqVO.getTitle()), l ->
                        l.like(TodoDO::getTitle,reqVO.getTitle())
                                .or().like(TodoDO::getSubmitter,reqVO.getTitle()))
                //.gtIfPresent(TodoDO::getStartTime,new Date())
                .orderByAsc(Objects.nonNull(reqVO.getSort()) && reqVO.getSort() == 1,TodoDO::getSubmitTime)
                .orderByDesc(Objects.isNull(reqVO.getSort()) || reqVO.getSort() == 0,TodoDO::getSubmitTime)
        );
    }
    default List<TodoDO> selectList(TodoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TodoDO>()
                .eqIfPresent(TodoDO::getType, reqVO.getType())
                .eqIfPresent(TodoDO::getTitle, reqVO.getTitle())
                .eqIfPresent(TodoDO::getSubmitter, reqVO.getSubmitter())
                .eqIfPresent(TodoDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(TodoDO::getSubmitTime, reqVO.getSubmitTime())
                .eqIfPresent(TodoDO::getSubsystemId, reqVO.getSubsystemId())
                .eqIfPresent(TodoDO::getLinkUrl, reqVO.getLinkUrl())
                .likeIfPresent(TodoDO::getTodoUserId, reqVO.getTodoUserId())
                .eqIfPresent(TodoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TodoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TodoDO::getId));
    }

    @Select("select count(1) from midoffice_todo where (START_TIME IS  NULL or START_TIME > CURRENT_TIMESTAMP) " +
            "and deleted = 0 and status = 0 and todo_user_id = #{userId}")
    Integer selectCountByUserId(Long userId);

    default long selectCountByUpdateReq(TodoItemUpdateReqDTO updateReqVO) {
        return selectCount(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getSubsystemId,updateReqVO.getSubsystemId())
                .eqIfPresent(TodoDO::getLinkUrl,updateReqVO.getPath())
                .eq(TodoDO::getProcessId,updateReqVO.getProcessId())
                .eqIfPresent(TodoDO::getProcessType,updateReqVO.getProcessType())
                .eqIfPresent(TodoDO::getTaskCode,updateReqVO.getTaskCode())
                .eqIfPresent(TodoDO::getProcessStatus,updateReqVO.getProcessStatus())
                .in(TodoDO::getTodoUserId,updateReqVO.getTodoUsers())
        );
    }

    long updateTodo(TodoItemUpdateReqDTO updateReqVO);

    default void deleteByReq(TodoItemUpdateReqDTO reqDTO) {
        delete(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getSubsystemId,reqDTO.getSubsystemId())
                .eqIfPresent(TodoDO::getLinkUrl,reqDTO.getPath())
                .eq(TodoDO::getProcessId,reqDTO.getProcessId())
                .eqIfPresent(TodoDO::getProcessType,reqDTO.getProcessType())
                .eqIfPresent(TodoDO::getProcessStatus,reqDTO.getProcessStatus())
                .in(TodoDO::getTodoUserId,reqDTO.getTodoUsers())
        );
    }

    default void deleteByReqAboutMeeting(TodoItemUpdateReqDTO reqDTO) {
        delete(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getSubsystemId, reqDTO.getSubsystemId())
                .eqIfPresent(TodoDO::getLinkUrl, reqDTO.getPath())
                .eq(TodoDO::getProcessId, reqDTO.getProcessId())
                .eqIfPresent(TodoDO::getProcessType, reqDTO.getProcessType())
                .eqIfPresent(TodoDO::getProcessStatus, reqDTO.getProcessStatus())
                .in(TodoDO::getTodoUserId, reqDTO.getTodoUsers())
                .eq(TodoDO::getStatus, 0)
        );
    }

    default void deleteByReqWithoutTodoUser(TodoItemUpdateReqDTO reqDTO) {
        delete(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getSubsystemId,reqDTO.getSubsystemId())
                .eqIfPresent(TodoDO::getLinkUrl,reqDTO.getPath())
                .eq(TodoDO::getProcessId,reqDTO.getProcessId())
                //.eqIfPresent(TodoDO::getProcessStatus, reqDTO.getProcessStatus())
                .eqIfPresent(TodoDO::getProcessType,reqDTO.getProcessType())
                .eqIfPresent(TodoDO::getProcessStatus,reqDTO.getProcessStatus())
                .eq(TodoDO::getStatus, 0)
        );
    }

    long updateTodoRemark(TodoItemUpdateReqDTO reqDTO);

    default TodoDO selectExistByUpdateSimpleReq(TodoUpdateSimpleReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getItem,reqVO.getItem())
                .eq(TodoDO::getTodoId,reqVO.getTodoId())
                .last(" limit 1 ")
        );
    }

    default void deleteByUpdateSimpleReq(TodoUpdateSimpleReqVO reqVO) {
        delete(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getItem,reqVO.getItem())
                .eq(TodoDO::getTodoId,reqVO.getTodoId())
        );
    }

    default boolean existsByCreateSimpleReq(TodoCreateSimpleReqVO reqVO) {
        return exists(new LambdaQueryWrapperX<TodoDO>()
                .eq(TodoDO::getItem,reqVO.getItem())
                .eq(TodoDO::getTodoId,reqVO.getTodoId())
                .eq(TodoDO::getPermission,reqVO.getPermission())
        );
    }

    //待办首页
    List<TodoListPageVO> getTodoListPage(IPage page,@Param("loginUserId") Long loginUserId,@Param("hhandle") Integer hhandle,@Param("isApp") Integer isApp,@Param("tenantId") Long tenantId);

    List<TodoListPageVO> getTodoAppListPage(IPage page,@Param("loginUserId") Long loginUserId,@Param("hhandle") Integer hhandle);

    List<TodoListPageVO> getDoneListPage(IPage page,@Param("loginUserId") Long loginUserId);

    List<AppTodoListPageVO> getAppTodoListPage(IPage page,@Param("loginUserId") Long loginUserId);

    //待办全部
    List<TodoListPageAllVO> getTodoListPageALL(IPage page,@Param("loginUserId") Long loginUserId,@Param("name1") String name1 ,@Param("task1") String task1,@Param("hhandle") Integer hhandle,@Param("isApp") Integer isApp,@Param("tenantId") Long tenantId);

    List<TodoListPageAllVO> getTodoAppListPageALL(IPage page,@Param("loginUserId") Long loginUserId,@Param("name1") String name1 ,@Param("task1") String task1,@Param("hhandle") Integer hhandle);

    List<DoneListPageAllVO> getDoneListPageALL(IPage page,@Param("loginUserId") Long loginUserId,@Param("name1") String name1 ,@Param("task1") String task1);

    List<Long> selectByTenantIdBpm(@Param("tenantId") Long tenantId);

    List<Long> selectReportOfficerByTenantIdBpm(@Param("tenantId") Long tenantId);

    List<Long> selectPrincipalByTenantIdBpm(@Param("tenantId") Long tenantId);

    List<UserinforToDoVO> selectUserinforByInforId(@Param("inforId") Long inforId);
}
