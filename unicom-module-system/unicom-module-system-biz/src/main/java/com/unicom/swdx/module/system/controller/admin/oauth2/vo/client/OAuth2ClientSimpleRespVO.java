package com.unicom.swdx.module.system.controller.admin.oauth2.vo.client;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("管理后台 - OAuth2 客户端精简列表 Response VO")
@Data
@ToString(callSuper = true)
public class OAuth2ClientSimpleRespVO {

    @ApiModelProperty(value = "编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "应用名称", required = true, example = "政务服务管理子系统")
    private String name;


}
