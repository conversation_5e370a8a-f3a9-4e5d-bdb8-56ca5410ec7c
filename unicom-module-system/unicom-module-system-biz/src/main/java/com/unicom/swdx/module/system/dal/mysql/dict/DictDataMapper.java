package com.unicom.swdx.module.system.dal.mysql.dict;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.dict.vo.data.DictDataExportReqVO;
import com.unicom.swdx.module.system.controller.admin.dict.vo.data.DictDataPageReqVO;
import com.unicom.swdx.module.system.controller.admin.dict.vo.data.DictDataSimpleReqVO;
import com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Mapper
public interface DictDataMapper extends BaseMapperX<DictDataDO> {

    default DictDataDO selectByDictTypeAndValue(String dictType, String value) {
        return selectOne(new LambdaQueryWrapper<DictDataDO>().eq(DictDataDO::getDictType, dictType)
                .eq(DictDataDO::getValue, value));
    }

    default DictDataDO selectByDictTypeAndLabel(String dictType, String label) {
        return selectOne(new LambdaQueryWrapper<DictDataDO>().eq(DictDataDO::getDictType, dictType)
                .eq(DictDataDO::getLabel, label));
    }

    default List<DictDataDO> selectByDictTypeAndValues(String dictType, Collection<String> values) {
        return selectList(new LambdaQueryWrapper<DictDataDO>().eq(DictDataDO::getDictType, dictType)
                .in(DictDataDO::getValue, values));
    }

    default long selectCountByDictType(String dictType) {
        return selectCount(DictDataDO::getDictType, dictType);
    }

    default List<DictDataDO> selectList(DictDataPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
                .eqStrIfPresent(DictDataDO::getDictType, reqVO.getDictType())
                .eqIfPresent(DictDataDO::getStatus, reqVO.getStatus())
                .orderByAsc(Arrays.asList(DictDataDO::getDictType, DictDataDO::getSort)));
    }

    default List<DictDataDO> selectListRoot(DictDataPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
                .likeIfPresent(DictDataDO::getDictType, reqVO.getDictType())
                // 查出第一级的字典值
                .eq(DictDataDO::getParentId, 0)
                .orderByAsc(Arrays.asList(DictDataDO::getDictType, DictDataDO::getSort)));
    }

    default List<DictDataDO> selectExcelList(DictDataExportReqVO reqVO, boolean isRoot) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
                .likeIfPresent(DictDataDO::getDictType, reqVO.getDictType())
                .eqIfPresent(DictDataDO::getStatus, reqVO.getStatus())
                // 默认分页查出第一级的字典值
                .eq(isRoot, DictDataDO::getParentId, 0)
                .orderByAsc(Arrays.asList(DictDataDO::getDictType, DictDataDO::getSort)));
    }

    /**
     * 查询所有字典值
     *
     * @return 返回状态为正常、且为根目录的列表数据
     */
//    default List<DictDataDO> selectListOn() {
//        return selectList(new LambdaQueryWrapperX<DictDataDO>()
//                .eq(DictDataDO::getStatus, 0)
//                .eq(DictDataDO::getParentId, 0)
//        );
//    }

    List<DictDataDO> selectAllChild(@Param("idList") List<Long> idList);

    List<DictDataDO> selectAllChildByStatus(@Param("idList") List<Long> idList, @Param("status") Integer status);


    boolean deletedictById(@Param("id") Long id);


    default List<DictDataDO> selectChildListOnByParentId(List<Long> ids) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .eq(DictDataDO::getStatus, 0)
                .in(DictDataDO::getParentId, ids)
        );
    }



    default List<DictDataDO> selectListOn(DictDataSimpleReqVO reqVO) {


        try {
            return  selectListAllbycache().stream()
                    .filter(data -> data.getDictType().contains(reqVO.getDictType()))
                    .collect(Collectors.toList());
        }catch (Exception e){
            e.printStackTrace();
        }


        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .likeRight(DictDataDO::getDictType,reqVO.getDictType())
                .eq(DictDataDO::getStatus, 0)
        );
    }

    default List<DictDataDO> selectListNotRoot(DictDataPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
                .likeIfPresent(DictDataDO::getDictType, reqVO.getDictType())
                // 查出非第一级的字典值
                .ne(DictDataDO::getParentId, 0)
                .orderByAsc(Arrays.asList(DictDataDO::getDictType, DictDataDO::getSort)));
    }


    //缓存一份字典 访问太高
    public static Cache<String, List<DictDataDO> > resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(1024) // 初始容量
                    .maximumSize(4096)   // 设定最大容量
                    .expireAfterWrite(5L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    default List<DictDataDO> selectListAllbycache(){

        List<DictDataDO>  list =null;
        try {
            list = resultCache.get("selectListByUserId" , () -> {
                return selectList(new LambdaQueryWrapperX<DictDataDO>()
                        .eq(DictDataDO::getStatus, 0)
                );
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return list;
    }


    default List<DictDataDO> selectListOnbycache() {


        try {
            return  selectListAllbycache().stream()
                    .filter(data -> data.getParentId() == 0)
                    .collect(Collectors.toList());
        }catch (Exception e){
            e.printStackTrace();
        }

        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .eq(DictDataDO::getStatus, 0)
                .eq(DictDataDO::getParentId,0)
        );
    }
}
