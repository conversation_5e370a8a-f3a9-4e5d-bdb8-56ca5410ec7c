package com.unicom.swdx.module.system.service.applet;



import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptCountVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.applet.*;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserDeptMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.mysql.user.TraineeUserMapper;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.net.ssl.*;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;


@Service("UserAppletService")
@Slf4j
@RefreshScope
public class UserAppletServiceImpl implements UserAppletService {

    @Resource
    private AdminUserService userService;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private TraineeUserMapper traineeUserMapper;
    @Resource
    private UserDeptMapper userDeptMapper;
    @Resource
    private AdminUserMapper userMapper;

    @Override
    public UserAppletTopVO getDashboard(UserAppletBaseVO userAppletBaseVO) {
        String result;
        UserAppletTopVO vos = null;
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };
        // 初始化SSLContext并设置TrustManager
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            SSLSocketFactory sslSocketFactory = sc.getSocketFactory();

            Object jsonObject = JSONObject.toJSON(userAppletBaseVO);
            String jsonObjectStr = jsonObject.toString();
            System.out.println(jsonObjectStr);
            // 使用Hutool发起POST请求
            result = HttpUtil.createPost("http://10.32.23.142:3290/top-data/data/datacloud/api/yz_edu_stat?version=2&lesseeId=1")
// .setSSLSocketFactory(sslSocketFactory)
                    //请求体
                    .body(jsonObjectStr)
                    .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmbGFnIjowLCJhcHBJZCI6IjVHeWVqYkpyMjd3NWg3UGYifQ.4DJft3hQReEMUQEDgICzkZJ9R_HW_lBrdiehqABSBF4")
                    .header("Content-Type", "application/json")
                    .execute()
                    .body();
            //JSONObject jsonObject = JSONUtil.parseObj(result);
            vos = JSON.parseObject(result, UserAppletTopVO.class);
            if(vos == null || vos.getCode()!=0){
                throw exception(new ErrorCode(1, "请求数据失败"));
            }
            //有其他
//            if(!userAppletTeachingMethodsVO.isEmpty()){
//
//            }
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
        return vos;
    }

    @Override
    public UserAppletTeacherVO getTeachingMethods(UserAppletBaseVO userAppletBaseVO) {
        System.out.println(userAppletBaseVO);
        String result = null;
        UserAppletTeacherVO vos = null;
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };
        // 初始化SSLContext并设置TrustManager
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            SSLSocketFactory sslSocketFactory = sc.getSocketFactory();

            Object jsonObject = JSONObject.toJSON(userAppletBaseVO);
            String jsonObjectStr = jsonObject.toString();
            // 使用Hutool发起POST请求
            result = HttpUtil.createPost("http://10.32.23.142:3290/top-data/data/datacloud/api/yz_work_hour_prop?version=2&lesseeId=1")
// .setSSLSocketFactory(sslSocketFactory)
                    //请求体
                    .body(jsonObjectStr)
                    .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmbGFnIjowLCJhcHBJZCI6IjVHeWVqYkpyMjd3NWg3UGYifQ.4DJft3hQReEMUQEDgICzkZJ9R_HW_lBrdiehqABSBF4")
                    .header("Content-Type", "application/json")
                    .execute()
                    .body();
            //JSONObject jsonObject = JSONUtil.parseObj(result);
            vos = JSON.parseObject(result, UserAppletTeacherVO.class);
            if(vos == null || vos.getCode()!=0){
                throw exception(new ErrorCode(1, "请求数据失败"));
            }
            List<UserAppletTeachingMethodsVO> userAppletTeachingMethodsVO=vos.getData().stream().filter(u->u.getEDUCATE_FORM_NAME() == null).collect(Collectors.toList());
            List<UserAppletTeachingMethodsVO> qt=vos.getData().stream().filter(u->("其他").equals(u.getEDUCATE_FORM_NAME())).collect(Collectors.toList());
            //有null
            if(!userAppletTeachingMethodsVO.isEmpty()){
                if(qt.isEmpty()){
                    //没有其他有null
                    for (UserAppletTeachingMethodsVO u : vos.getData()){
                        if(u.getEDUCATE_FORM_NAME()== null){
                            u.setEDUCATE_FORM_NAME("其他");
                        }
                    }
                }else {
                    //有其他有null
                    for (UserAppletTeachingMethodsVO u : vos.getData()){
                        if("其他".equals(u.getEDUCATE_FORM_NAME())){
                            u.setWork_hour(u.getWork_hour()+userAppletTeachingMethodsVO.get(0).getWork_hour());
                            u.setProp(u.getProp().add(userAppletTeachingMethodsVO.get(0).getProp()));
                        }
                    }
                }
            }
            vos.getData().removeIf(item ->item.getEDUCATE_FORM_NAME() == null);

        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
        return vos;
    }

    @Override
    public UserAppletReultVO getCourseSituation(UserAppletBaseVO userAppletBaseVO) {
        System.out.println(userAppletBaseVO);
        String result;
        UserAppletReultVO vos = null;
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };
        // 初始化SSLContext并设置TrustManager
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            SSLSocketFactory sslSocketFactory = sc.getSocketFactory();

            Object jsonObject = JSONObject.toJSON(userAppletBaseVO);
            String jsonObjectStr = jsonObject.toString();
            System.out.println(jsonObjectStr);
            // 使用Hutool发起POST请求
            result = HttpUtil.createPost("http://10.32.23.142:3290/top-data/data/datacloud/api/eval_teacher?version=4&lesseeId=1")
 .setSSLSocketFactory(sslSocketFactory)
                    //请求体
                    .body(jsonObjectStr)
                    .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmbGFnIjowLCJhcHBJZCI6IjVHeWVqYkpyMjd3NWg3UGYifQ.4DJft3hQReEMUQEDgICzkZJ9R_HW_lBrdiehqABSBF4")
                    .header("Content-Type", "application/json")
                    .execute()
                    .body();
            //JSONObject jsonObject = JSONUtil.parseObj(result);
            vos = JSON.parseObject(result, UserAppletReultVO.class);
            if(vos == null || vos.getCode()!=0){
                throw exception(new ErrorCode(1, "请求数据失败"));
            }
            vos.setTotal(getCourseSituationTotal(userAppletBaseVO));
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
        return vos;
    }

    @Override
    public Integer getCourseSituationTotal(UserAppletBaseVO userAppletBaseVO) {
        String result;
        Integer vos = null;
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };
        // 初始化SSLContext并设置TrustManager
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            SSLSocketFactory sslSocketFactory = sc.getSocketFactory();

            Object jsonObject = JSONObject.toJSON(userAppletBaseVO);
            String jsonObjectStr = jsonObject.toString();
            // 使用Hutool发起POST请求
            result = HttpUtil.createPost("http://10.32.23.142:3290/top-data/data/datacloud/api/get_eval_len?version=4&lesseeId=1")
// .setSSLSocketFactory(sslSocketFactory)
                    //请求体
                    .body(jsonObjectStr)
                    .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmbGFnIjowLCJhcHBJZCI6IjVHeWVqYkpyMjd3NWg3UGYifQ.4DJft3hQReEMUQEDgICzkZJ9R_HW_lBrdiehqABSBF4")
                    .header("Content-Type", "application/json")
                    .execute()
                    .body();
            vos  = (Integer) JSONUtil.getByPath(JSONUtil.parseObj(result), "$.data[0].cnt");
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
        return vos;
    }

    @Override
    public Integer getUserAppletType() {
        AdminUserDO user = userService.getUser(getLoginUserId());
        //小程序判断用户身份(0教职工、1学员、2研究生、3其他人员，4，处长，5校领导)
        LambdaQueryWrapper<DeptDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeptDO::getName,"研究生")
                .eq(DeptDO::getTenantId,user.getTenantId());
        DeptDO yjsDeptDO = deptMapper.selectOne(lambdaQueryWrapper);
        LambdaQueryWrapper<DeptDO> studentQueryWrapper = new LambdaQueryWrapper<>();
        studentQueryWrapper.eq(DeptDO::getName,"学员")
                .eq(DeptDO::getTenantId,user.getTenantId());
        DeptDO studentDeptDO = deptMapper.selectOne(studentQueryWrapper);
        Integer userAppletType = 3;
        if(traineeUserMapper.getHr(user.getId())>0){
            userAppletType = 0;
            //判断处长
            LambdaQueryWrapper<DeptDO> directorQueryWrapper = new LambdaQueryWrapper<>();
            directorQueryWrapper.eq(DeptDO::getTenantId,user.getTenantId());
            directorQueryWrapper.select(DeptDO::getLeaderUserId);
            List<Object> ids = deptMapper.selectObjs(directorQueryWrapper);
            if(ids.contains(user.getId())){
                userAppletType = 4;
            }
            //判断校领导（可以覆盖处长）
            LambdaQueryWrapper<DeptDO> leaderQueryWrapper = new LambdaQueryWrapper<>();
            leaderQueryWrapper.eq(DeptDO::getName,"校（院）领导")
                    .eq(DeptDO::getTenantId,user.getTenantId())
                    .eq(DeptDO::getStatus,0);
            DeptDO leaderDeptDO = deptMapper.selectOne(leaderQueryWrapper);
            //判断多部门
            if(leaderDeptDO!=null) {
                LambdaQueryWrapper<UserDeptDO> userDeptDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                userDeptDOLambdaQueryWrapper.eq(UserDeptDO::getUserId, user.getId())
                        .eq(UserDeptDO::getDeptId,leaderDeptDO.getId());
                UserDeptDO userDeptDO = userDeptMapper.selectOne(userDeptDOLambdaQueryWrapper);
                if(userDeptDO!=null){
                    userAppletType = 5;
                }
            }
            if (leaderDeptDO!=null && user.getDeptId().equals(leaderDeptDO.getId())){
                userAppletType = 5;
            }
        }else if (yjsDeptDO != null && user.getDeptId().equals(yjsDeptDO.getId())){
            userAppletType = 2;
        }else if (studentDeptDO != null && user.getDeptId().equals(studentDeptDO.getId())){
            userAppletType = 1;
        }
        return userAppletType;
    }

    @Override
    public List<DeptDO> getDepts() {
        AdminUserDO user = userService.getUser(getLoginUserId());
        LambdaQueryWrapper<DeptDO> directorQueryWrapper = new LambdaQueryWrapper<>();
        directorQueryWrapper.eq(DeptDO::getTenantId,user.getTenantId())
                .eq(DeptDO::getStatus,0).isNotNull(DeptDO::getLeaderUserId);
        List<DeptDO> list= deptMapper.selectList(directorQueryWrapper);
        List<DeptDO> result = list.stream().filter(d-> d.getLeaderUserId().equals(user.getId())).collect(Collectors.toList());
        List<Long> deptIds= new ArrayList<>();
        if(!result.isEmpty()) {
            for (DeptDO d :result){
                deptIds.add(d.getId());
            }
            List<AdminUserDO> countByDeptIdAdminUserDos = userMapper.countByDeptIdAdminUserDos(deptIds, getTenantId(), 1);
            //统计多部门补充人数
            List<DeptCountVO> deptCountVOS = userMapper.countByDepts(getTenantId(), 1);
            HashMap<Long, Long> deptsCount = new HashMap<>();
            HashMap<Long, Long> countByDeptId = new HashMap<>();
            for (DeptCountVO deptCountVO : deptCountVOS) {
                deptsCount.put(deptCountVO.getDeptId(), deptCountVO.getCount());
            }
            for (AdminUserDO adminUserDO : countByDeptIdAdminUserDos) {
                countByDeptId.put(adminUserDO.getDeptId(),adminUserDO.getId());
            }
            for (DeptDO d :result){
                Long deptId = d.getId();
                Long count1 = deptsCount.get(deptId);
                count1 = count1 != null ? count1 : 0;
                Long count2 = countByDeptId.get(deptId);
                count2 = count2 != null ? count2 : 0;
                d.setCode(String.valueOf((count1+count2)));
            }

        }
        return result;
    }

    public static void main(String[] args) {
        String result = null;
        UserAppletTeacherVO vos = null;
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };
        // 初始化SSLContext并设置TrustManager
        //            SSLContext sc = SSLContext.getInstance("SSL");
//            sc.init(null, trustAllCerts, new SecureRandom());
//            SSLSocketFactory sslSocketFactory = sc.getSocketFactory();
//
//            Object jsonObject = JSONObject.toJSON(userAppletBaseVO);
//            String jsonObjectStr = jsonObject.toString();
//            // 使用Hutool发起POST请求
//            result = HttpUtil.createPost("http://10.32.23.142:3290/top-data/data/datacloud/api/yz_work_hour_prop?version=2&lesseeId=1")
//// .setSSLSocketFactory(sslSocketFactory)
//                    //请求体
//                    .body(jsonObjectStr)
//                    .header("Authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmbGFnIjowLCJhcHBJZCI6IjVHeWVqYkpyMjd3NWg3UGYifQ.4DJft3hQReEMUQEDgICzkZJ9R_HW_lBrdiehqABSBF4")
//                    .header("Content-Type", "application/json")
//                    .execute()
//                    .body();
        //JSONObject jsonObject = JSONUtil.parseObj(result);
        result = "{\n" +
                "    \"code\": 0,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"EDUCATE_FORM_NAME\": \"讲授式\",\n" +
                "            \"prop\": 27.27,\n" +
                "            \"work_hour\": 24\n" +
                "        },\n" +
                "        {\n" +
                "            \"EDUCATE_FORM_NAME\": \"案例式教学\",\n" +
                "            \"prop\": 45.45,\n" +
                "            \"work_hour\": 40\n" +
                "        },\n" +
                "        {\n" +
                "            \"EDUCATE_FORM_NAME\": \"结构化研讨\",\n" +
                "            \"prop\": 4.55,\n" +
                "            \"work_hour\": 4\n" +
                "        },\n" +
                "        {\n" +
                "            \"EDUCATE_FORM_NAME\": \"其他\",\n" +
                "            \"prop\": 4.55,\n" +
                "            \"work_hour\": 4\n" +
                "        },\n" +
                "        {\n" +
                "            \"EDUCATE_FORM_NAME\": null,\n" +
                "            \"prop\": 18.18,\n" +
                "            \"work_hour\": 16\n" +
                "        }\n" +
                "    ],\n" +
                "    \"failed\": false,\n" +
                "    \"msg\": \"成功\",\n" +
                "    \"success\": true\n" +
                "}";
        vos = JSON.parseObject(result, UserAppletTeacherVO.class);
        if(vos == null || vos.getCode()!=0){
            throw exception(new ErrorCode(1, "请求数据失败"));
        }
        List<UserAppletTeachingMethodsVO> userAppletTeachingMethodsVO=vos.getData().stream().filter(u->u.getEDUCATE_FORM_NAME() == null).collect(Collectors.toList());
        List<UserAppletTeachingMethodsVO> qt=vos.getData().stream().filter(u->("其他").equals(u.getEDUCATE_FORM_NAME())).collect(Collectors.toList());
        //有null
        if(!userAppletTeachingMethodsVO.isEmpty()){
            if(qt.isEmpty()){
                //没有其他有null
                for (UserAppletTeachingMethodsVO u : vos.getData()){
                    if(u.getEDUCATE_FORM_NAME()== null){
                        u.setEDUCATE_FORM_NAME("其他");
                    }
                }
            }else {
                //有其他有null
                for (UserAppletTeachingMethodsVO u : vos.getData()){
                    if("其他".equals(u.getEDUCATE_FORM_NAME())){
                        u.setWork_hour(u.getWork_hour()+userAppletTeachingMethodsVO.get(0).getWork_hour());
                        u.setProp(u.getProp().add(userAppletTeachingMethodsVO.get(0).getProp()));
                    }
                }
            }
        }
        vos.getData().removeIf(item ->item.getEDUCATE_FORM_NAME() == null);
        System.out.println(vos);
    }
}
