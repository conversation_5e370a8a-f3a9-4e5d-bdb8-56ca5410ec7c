package com.unicom.swdx.module.system.controller.admin.user.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("用户机构精简信息 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserTenantSimpleRespVO {

    @ApiModelProperty(value = "用户编号", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "用户昵称", required = true, example = "sk")
    private String nickname;

    @ApiModelProperty("机构名称")
    private String tenantName;

}
