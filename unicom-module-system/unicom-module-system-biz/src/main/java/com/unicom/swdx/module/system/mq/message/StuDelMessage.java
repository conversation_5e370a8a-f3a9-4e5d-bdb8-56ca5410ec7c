package com.unicom.swdx.module.system.mq.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class StuDelMessage {


    @JsonProperty("header")
    private HeaderDTO header;
    @JsonProperty("body")
    private BodyDTO body;

    @NoArgsConstructor
    @Data
    public static class HeaderDTO {
        @JsonProperty("sender")
        private String sender;
        @JsonProperty("timestamp")
        private String timestamp;
        @JsonProperty("eventType")
        private String eventType;
        @JsonProperty("eventId")
        private String eventId;
    }

    @NoArgsConstructor
    @Data
    public static class BodyDTO {
        @JsonProperty("classId")
        private String classId;
        @JsonProperty("id")
        private String id;
    }
}
