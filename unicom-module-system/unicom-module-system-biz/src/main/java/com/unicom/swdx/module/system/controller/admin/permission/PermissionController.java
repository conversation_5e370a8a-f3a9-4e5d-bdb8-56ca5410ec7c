package com.unicom.swdx.module.system.controller.admin.permission;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.system.controller.admin.permission.vo.permission.PermissionAssignRoleDataScopeReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.permission.PermissionAssignRoleMenuReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.permission.PermissionAssignUserRoleGroupReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.permission.PermissionAssignUserRoleReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupRespVO;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.OTHER;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static java.util.Collections.emptySet;

/**
 * 权限 Controller，提供赋予用户、角色的权限的 API 接口
 *
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 权限")
@RestController
@RequestMapping("/system/permission")
public class PermissionController {

    @Resource
    private PermissionService permissionService;

    @Resource
    private RoleGroupService roleGroupService;

    @ApiOperation("获得角色拥有的菜单编号")
    @ApiImplicitParam(name = "roleId", value = "角色编号", required = true, dataTypeClass = Long.class)
    @GetMapping("/list-role-resources")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-menu')")
    public CommonResult<Set<Long>> listRoleMenus(Long roleId) {
        return success(permissionService.getRoleMenuIds(roleId));
    }

    @PostMapping("/assign-role-menu")
    @ApiOperation("赋予角色菜单")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-menu')")
    public CommonResult<Boolean> assignRoleMenu(@Validated @RequestBody PermissionAssignRoleMenuReqVO reqVO) {
        // 执行菜单的分配
        permissionService.assignRoleMenu(reqVO.getRoleId(), reqVO.getMenuIds());
        return success(true);
    }

    @PostMapping("/assign-role-data-scope")
    @ApiOperation("赋予角色数据权限")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-data-scope')")
    public CommonResult<Boolean> assignRoleDataScope(@Valid @RequestBody PermissionAssignRoleDataScopeReqVO reqVO) {
        permissionService.assignRoleDataScope(reqVO.getRoleId(), reqVO.getDataScope(), reqVO.getDataScopeDeptIds());
        return success(true);
    }

    @ApiOperation("获得用户拥有的角色编号列表")
    @ApiImplicitParam(name = "userId", value = "用户编号", required = true, dataTypeClass = Long.class)
    @GetMapping("/list-user-roles")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-user-role')")
    public CommonResult<Set<Long>> listAdminRoles(@RequestParam("userId") Long userId) {
        return success(permissionService.getUserRoleIdListByUserId(userId));
    }

    @ApiOperation("获得用户拥有的角色编号列表")
    @ApiImplicitParam(name = "userId", value = "用户编号", required = true, dataTypeClass = Long.class)
    @GetMapping("/list-user-roleGroups")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-user-role')")
    public CommonResult<Set<Long>> listAdminRoleGroups(@RequestParam("userId") Long userId) {
        return success(permissionService.getUserRoleGroupIdListByUserId(userId));
    }

    @ApiOperation("赋予用户角色")
    @PostMapping("/assign-user-role")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:permission:assign-user-role')")
    public CommonResult<Boolean> assignUserRole(@Validated @RequestBody PermissionAssignUserRoleReqVO reqVO) {
        permissionService.assignUserRole(reqVO.getUserId(), reqVO.getRoleIds());
        try {
            TimeUnit.SECONDS.sleep(6);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return success(true);
    }

    @ApiOperation("赋予用户角色组")
    @PostMapping("/assign-user-role-group")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:permission:assign-user-role')")
    public CommonResult<Boolean> assignUserRoleGroup(@Validated @RequestBody PermissionAssignUserRoleGroupReqVO reqVO) {
        Set<Long> roleGroupIds = reqVO.getRoleGroupIds();
        Set<Long> roleIds = new HashSet<>(emptySet());
        for(Long roleGroupId : roleGroupIds) {
            RoleGroupRespVO roleGroup = roleGroupService.getRoleGroup(roleGroupId);
            Set<Long> ids = roleGroup.getRoleIds();
            roleIds.addAll(ids);
        }
        //分配角色
        permissionService.assignUserRole(reqVO.getUserId(), roleIds);
        //分配角色组
        permissionService.assignUserRoleGroup(reqVO.getUserId(), reqVO.getRoleGroupIds());
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return success(true);
    }

    @ApiOperation(value = "获取用户类型",notes = "0=超级管理员，1=机构管理员，2=普通用户")
    @GetMapping("/user-type")
    public CommonResult<Integer> getUserType() {
        return success(permissionService.getUserType(getLoginUserId()));
    }

}
