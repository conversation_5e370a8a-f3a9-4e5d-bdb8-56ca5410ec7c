package com.unicom.swdx.module.system.api.sms;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
import com.unicom.swdx.module.system.convert.message.TimedTaskConvert;
import com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO;
import com.unicom.swdx.module.system.dal.mysql.message.TimedTaskMapper;
import com.unicom.swdx.module.system.job.smsjob.QueueTask;
import com.unicom.swdx.module.system.service.sms.SmsSendService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class SmsQueApiImpl implements SmsQueApi {

    @Resource
    private TimedTaskMapper timedTaskMapper;

    @Override
    public CommonResult<Long> sendque(SmsSendReq req) {
        if(req.getTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                req.setSendTime(sdf.parse(req.getTime()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        TimedTaskDO timedTaskDO = TimedTaskConvert.INSTANCE.convert(req);
        timedTaskMapper.insert(timedTaskDO);
        req.setTaskId(timedTaskDO.getId());
        QueueTask.putTask(req);
        return success(null);
    }

    @Override
    public CommonResult<Long> cancelQue(String title) {
        QueueTask.remove(title);
        return success(null);
    }

}
