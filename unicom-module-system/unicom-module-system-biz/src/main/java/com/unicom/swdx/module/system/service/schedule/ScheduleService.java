package com.unicom.swdx.module.system.service.schedule;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.system.controller.admin.home.schedule.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.schedule.ScheduleDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 日程 Service 接口
 *
 * <AUTHOR>
 */
public interface ScheduleService extends IService<ScheduleDO> {

    /**
     * 创建日程
     *
     * @param createReqVO 创建信息
     */
    void createSchedule(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 创建日程
     *
     * @param createReqVO 创建信息
     */
    void createScheduleByUser(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 创建日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    Integer createScheduleOther(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 创建日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    Integer createScheduleInfor(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 创建多人日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    Integer createSchedulesOther(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 删除
     *
     * @param createReqVO 创建信息
     * @return
     */
    Integer deleteScheduleOther(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 删除中心工作日程
     *
     * @param createReqVO 创建信息
     * @return
     */
    Integer deleteScheduleInfor(@Valid ScheduleCreateReqVO createReqVO);

    /**
     * 更新日程
     *
     * @param updateReqVO 更新信息
     */
    void updateSchedule(@Valid ScheduleUpdateReqVO updateReqVO);

    /**
     * 删除日程
     *
     * @param id 编号
     */
    void deleteSchedule(Long id);

    /**
     * 根据查询条件获取日程列表
     * @param reqVO 查询条件
     * @return 日程列表
     */
    List<ScheduleDO> getList(ScheduleQueryReqVO reqVO);

    /**
     * 获取一个月的日程
     * @param reqVO 查询条件
     * @return 一个月日程
     */
    List<CalendarWnlScheduleRespVO> getListByMonth(ScheduleQueryReqVO reqVO);

}
