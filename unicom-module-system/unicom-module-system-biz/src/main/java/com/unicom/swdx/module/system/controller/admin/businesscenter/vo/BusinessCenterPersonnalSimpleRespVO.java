package com.unicom.swdx.module.system.controller.admin.businesscenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.common.enums.GenderEnum;
import com.unicom.swdx.framework.common.enums.NationEnum;
import com.unicom.swdx.framework.common.enums.PoliticalOutlookEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class BusinessCenterPersonnalSimpleRespVO {

    /**
     * 人事编号
     */
    @ApiModelProperty(value = "人事编号", required = true, example = "1")
    @NotNull(message = "人事编号不能为空")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "业中用户id")
    private Long userId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 行政职务名称
     */
    @ApiModelProperty(value = "行政职务名称",example = "主任")
    private String administrativePositionName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "人员状态")
    private Integer personStatus;
}
