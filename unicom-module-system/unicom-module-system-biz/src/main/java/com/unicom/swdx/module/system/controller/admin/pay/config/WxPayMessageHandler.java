package com.unicom.swdx.module.system.controller.admin.pay.config;

import com.egzosn.pay.common.api.PayMessageHandler;
import com.egzosn.pay.common.api.PayService;
import com.egzosn.pay.common.bean.PayOutMessage;
import com.egzosn.pay.common.exception.PayErrorException;
import com.egzosn.pay.wx.bean.WxPayMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.Map;


@Slf4j
/**
 * 微信支付回调处理器
 * Created by ZaoSheng on 2016/6/1.
 */
@Configuration
public class WxPayMessageHandler implements PayMessageHandler<WxPayMessage, PayService> {

    @Override
    public PayOutMessage handle(WxPayMessage payMessage, Map<String, Object> context, PayService payService) throws PayErrorException {

//        log.info("------------交易号:{}",payMessage.getOutTradeNo());
//        log.info("------------订单号:{}",payMessage.getTransactionId());
//        log.info("------------完整的微信支付回调信息:{}",payMessage.getPayMessage().toString());

        if("SUCCESS".equals(payMessage.getResultCode())){
            log.info("------------微信支付回调处理成功------------");
            return payService.getPayOutMessage("SUCCESS", payMessage.getPayMessage().toString());
        }
        return payService.getPayOutMessage("FAIL", "失败");
    }
}
