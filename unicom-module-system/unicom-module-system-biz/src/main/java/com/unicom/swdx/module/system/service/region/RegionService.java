package com.unicom.swdx.module.system.service.region;


import com.unicom.swdx.module.system.api.region.dto.RegionDTO;
import com.unicom.swdx.module.system.dal.dataobject.region.RegionDO;

import java.util.List;

/**
 * 地区 Service 接口
 *
 * <AUTHOR>
 */
public interface RegionService {

    /**
     * 根据父地区id，获取其下所有的地区
     * @param id 父地区id
     * @param level 等级
     * @return 地区
     */
    List<RegionDO> getRegionListByParentId(Long id, Integer level);

    /**
     * 根据id获取地区名
     * @param id id
     * @return 地区名
     */
    String getRegionName(Long id);

    /**
     * 获取所有的地区（供rpc接口使用）
     * @return 地区
     */
    List<RegionDTO> getAllRegion();

    /**
     * 根据id获取行政区划代码
     * @param id id
     * @return 行政区划代码
     */
    Integer getRegionCode(Long id);

}
