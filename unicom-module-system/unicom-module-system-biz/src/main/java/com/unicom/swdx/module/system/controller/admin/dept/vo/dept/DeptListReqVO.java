package com.unicom.swdx.module.system.controller.admin.dept.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@ApiModel("管理后台 - 组织列表 Request VO")
@Data
public class DeptListReqVO {

    @ApiModelProperty(value = "组织编号", example = "101")
    private Long id;

    @ApiModelProperty(value = "组织名称", example = "sk", notes = "模糊匹配")
    private String name;

    @ApiModelProperty(value = "展示状态", example = "1", notes = "参见 CommonStatusEnum 枚举类")
    private Integer status;

    @ApiModelProperty(value = "所属机构编号", example = "1")
    private Long tenantId;

    @ApiModelProperty(value = "所属机构编号集合", example = "1")
    private Set<Long> tenantIds;
}
