package com.unicom.swdx.module.system.controller.admin.databoard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel("管理后台 - 近六个月用户变化趋势 Response VO")
public class LastSixMonthUsersRespVO {

    @ApiModelProperty(value = "年月")
    private LocalDate yearMonth;

    @ApiModelProperty(value = "用户总数")
    private Long allUsers;

    @ApiModelProperty(value = "公众用户数")
    private Long publicUsers;

    @ApiModelProperty(value = "机构用户数")
    private Long tenantUsers;
}
