package com.unicom.swdx.module.system.convert.schedule;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.home.schedule.vo.*;
import com.unicom.swdx.module.system.dal.dataobject.schedule.ScheduleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 日程 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ScheduleConvert {

    ScheduleConvert INSTANCE = Mappers.getMapper(ScheduleConvert.class);

    ScheduleDO convert(ScheduleCreateReqVO bean);

    ScheduleDO convert(ScheduleUpdateReqVO bean);

    List<ScheduleRespVO> convertList(List<ScheduleDO> list);

    List<ScheduleWholeRespVO> convertWholeList(List<ScheduleDO> list);

}
