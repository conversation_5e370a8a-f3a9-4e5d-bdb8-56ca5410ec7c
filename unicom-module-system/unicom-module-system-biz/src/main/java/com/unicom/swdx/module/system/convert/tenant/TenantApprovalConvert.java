package com.unicom.swdx.module.system.convert.tenant;


import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.AdminAddTenantReqVO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantRegisterInfoChangeVO;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantRegisterReqVO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantApprovalDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 机构认证审批 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantApprovalConvert {

    TenantApprovalConvert INSTANCE = Mappers.getMapper(TenantApprovalConvert.class);

    TenantApprovalDO convert(TenantRegisterReqVO bean);

    TenantApprovalDO convert(TenantRegisterInfoChangeVO bean);

    TenantApprovalDO convert(AdminAddTenantReqVO adminAddTenantReqVO);
}
