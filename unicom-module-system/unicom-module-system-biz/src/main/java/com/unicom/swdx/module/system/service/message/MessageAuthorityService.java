package com.unicom.swdx.module.system.service.message;

import com.alibaba.ttl.threadpool.agent.internal.javassist.tools.reflect.CannotCreateException;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.*;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;
import com.unicom.swdx.module.system.dal.dataobject.message.MessageAuthorityDO;

import javax.validation.Valid;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageAuthorityService extends IService<MessageAuthorityDO> {

    /**
     * 创建消息权限
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMessageAuthority(@Valid MessageAuthorityCreateReqVO createReqVO) ;

    PageResult<MessageAuthorityDO> getMessageAuthorityPage(MessageAuthorityPageReqVO pageReqVO);
    /**
     * 获得消息权限
     *
     * @param id 编号
     * @return 消息权限
     */
    MessageAuthorityDO getMessageAuthority(Long id);

    /**
     * 获得消息权限编号
     *
     * @param userId 编号
     * @return 消息权限
     */
    Long getMessageAuthorityId(Long userId);

    /**
     * 更新消息权限
     *
     */
    void updateMessageAuthority(@Valid MessageAuthorityUpdateReqVO updateReqVO);
    /**
     * 删除消息权限
     *
     * @param id 编号
     */
    void deleteMessageAuthority(Long id);



}
