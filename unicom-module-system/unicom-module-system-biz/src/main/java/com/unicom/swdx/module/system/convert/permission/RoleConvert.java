package com.unicom.swdx.module.system.convert.permission;

import cn.hutool.extra.spring.SpringUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.*;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import com.unicom.swdx.module.system.service.permission.bo.RoleCreateReqBO;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Mapper
public interface RoleConvert {

    RoleConvert INSTANCE = Mappers.getMapper(RoleConvert.class);

    @Resource
    OAuth2ClientService clientService = SpringUtil.getBean(OAuth2ClientService.class);

    TenantService tenantService = SpringUtil.getBean(TenantService.class);

    RoleDO convert(RoleUpdateReqVO bean);

    RoleRespVO convert(RoleDO bean);

    RoleDO convert(RoleCreateReqVO bean);

    List<RoleSimpleRespVO> convertList02(List<RoleDO> list);

    List<RoleExcelVO> convertList03(List<RoleDO> list);

    RoleDO convert(RoleCreateReqBO bean);

    PageResult<RoleRespVO> convertPage(PageResult<RoleDO> pageResult);

    RoleExportReqVO covertReqVO(RolePageReqVO reqVO);

    default RoleExcelVO roleDOToRoleExcelVO(RoleDO roleDO) {
        if ( roleDO == null ) {
            return null;
        }

        RoleExcelVO roleExcelVO = new RoleExcelVO();

//        roleExcelVO.setId( roleDO.getId() );
        roleExcelVO.setName( roleDO.getName() );
        roleExcelVO.setCode( roleDO.getCode() );
//        roleExcelVO.setSort( roleDO.getSort() );
        roleExcelVO.setType(roleDO.getType());
        if ( roleDO.getStatus() != null ) {
            roleExcelVO.setStatus( String.valueOf( roleDO.getStatus() ) );
        }
        roleExcelVO.setCreateTime(roleDO.getCreateTime());
        if (Objects.nonNull(roleDO.getClientId())) {
            roleExcelVO.setClient(clientService.getNameFromCache(roleDO.getClientId()));
        }
        if (Objects.nonNull(roleDO.getTenantId()) && !Objects.equals(0L, roleDO.getTenantId())) {
            roleExcelVO.setTenant(tenantService.getTenantFromCache(roleDO.getTenantId()).getName());
        }

        return roleExcelVO;
    }

}
