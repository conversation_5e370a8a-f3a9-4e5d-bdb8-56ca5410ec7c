package com.unicom.swdx.module.system.dal.mysql.logger;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.system.controller.admin.logger.vo.loginlog.LoginLogExportReqVO;
import com.unicom.swdx.module.system.controller.admin.logger.vo.loginlog.LoginLogPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.logger.LoginLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LoginLogMapper extends BaseMapperX<LoginLogDO> {

    List<LoginLogDO> selectLoginLogPage(IPage page, @Param("param") LoginLogPageReqVO reqVO ,  @Param("tenantId") Long tenantId);

    default PageResult<LoginLogDO> selectPage(LoginLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LoginLogDO>()
                .eqIfPresent(LoginLogDO::getResult, reqVO.getStatus())
                .betweenIfPresent(LoginLogDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getUserIp()),l ->
                        l.like(LoginLogDO::getUsername,reqVO.getUserIp())
                        .or().like(LoginLogDO::getUserIp,reqVO.getUserIp()))
                .eq(LoginLogDO::getTenantId , SecurityFrameworkUtils.getTenantId())
                .orderByDesc(LoginLogDO::getId)
        );
    }

    default List<LoginLogDO> selectList(LoginLogExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LoginLogDO>()
                .eqIfPresent(LoginLogDO::getResult, reqVO.getStatus())
                .betweenIfPresent(LoginLogDO::getCreateTime, reqVO.getCreateTime())
                .and(StrUtil.isNotBlank(reqVO.getUserIp()),l ->
                        l.like(LoginLogDO::getUsername,reqVO.getUserIp())
                                .or().like(LoginLogDO::getUserIp,reqVO.getUserIp()))
                .eq(LoginLogDO::getTenantId , SecurityFrameworkUtils.getTenantId())
                .orderByDesc(LoginLogDO::getId)
                .last("LIMIT 10000")
        );
    }

}
