package com.unicom.swdx.module.system.dal.mysql.notice;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.notice.NoticeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface NoticeMapper extends BaseMapperX<NoticeDO> {

    List<NoticeDO> selectNoticePage(@Param("param") NoticePageReqVO pageReqVO);

    default PageResult<NoticeDO> selectPage(NoticePageReqVO reqVO,Collection<Long>userIds) {
        LambdaQueryWrapperX<NoticeDO> query = new LambdaQueryWrapperX<NoticeDO>()
                .eqIfPresent(NoticeDO::getType,reqVO.getType())
                .eqIfPresent(NoticeDO::getStatus, reqVO.getStatus());
//        if (Boolean.TRUE.equals(reqVO.getSuccess())) {
//            query.eq(NoticeDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
//        } else if (Boolean.FALSE.equals(reqVO.getSuccess())) {
//            query.gt(NoticeDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
//        }
        if (StrUtil.isNotBlank(reqVO.getTitle())) {
            query.inIfPresent(NoticeDO::getCreator, userIds).or(qw -> qw.like(NoticeDO::getTitle, reqVO.getTitle()));
        }
        query.orderByDesc(NoticeDO::getId); // 降序
        return selectPage(reqVO, query);
    }

}

