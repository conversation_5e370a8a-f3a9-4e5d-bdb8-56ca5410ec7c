<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.dangjian.DangjianMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectUserInfo" resultType="com.unicom.swdx.module.system.api.user.dto.DangJianDTO">
        select
            tupm.user_name,
            tupm.join_party_date,
            tupm.official_date,
            CONCAT(tupm.introducer, ', ', tupm.introducer_two) AS combined_introducers,
            tpo.party_org_name,
            tp.party_org_name as parent_party_org_name
        from
            t_user tu
                right join t_user_party_member tupm on
                tu.user_id = tupm.user_id
                left join
            t_user_party_org tupo on
                tupm.user_id = tupo.user_id
                left join
            t_party_org tpo on
                tupo.party_org_id = tpo.party_org_id
                left join
            t_party_org tp on
                tp.party_org_id = tpo.parent_party_org_id
        where
            tupm.party_member_status = 1
          and party_status in (1, 2)
          and tpo.party_org_status = 1
          and tu.user_mobile = #{mobile} limit 1
    </select>

    <resultMap id="deptDtoResult" type="com.unicom.swdx.module.system.api.dept.dto.DeptDTO">
        <result column="name" property="dept_name"/>
        <result column="id" property="new_dept_id"/>
        <result column="parent_id" property="new_parent_id"/>
        <result column="sort" property="dept_num"/>
    </resultMap>

    <select id="selectSimpleDeptInfoList" resultMap="deptDtoResult">
        select
            a.name,
            a.id,
            a.parent_id,
            a.sort
        from system_dept a
        where a.deleted = '0'
        order by parent_id, id
    </select>





</mapper>
