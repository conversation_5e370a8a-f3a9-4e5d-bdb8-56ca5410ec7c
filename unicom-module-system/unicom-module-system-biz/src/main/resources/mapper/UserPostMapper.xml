<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.dept.UserPostMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectUsersByPostCode" resultType="java.lang.Long">
        select distinct user_id from system_user_post sup
            left join system_post sp on sup.post_id = sp.id
                left join system_users c on sup.user_id = c.id
        where c.tenant_id = #{tenantId}
        and c.status=0
        and c.deleted=0
        and sup.deleted=0
        and sp.deleted=0
        and sp.code in
        <foreach collection="postCodes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>

    <select id="selectUsersByPostDeptCode" resultType="java.lang.Long">
        SELECT DISTINCT s.id
        FROM system_user_post sup
        LEFT JOIN system_post sp ON sup.post_id = sp.id
        LEFT JOIN system_users s ON sup.user_id = s.id
        LEFT JOIN system_users c ON sup.user_id = c.id
        LEFT JOIN system_user_dept sud ON sup.user_id = sud.user_id
        WHERE c.tenant_id =  #{tenantId}
        AND sup.deleted = 0
        AND sp.code IN
        <foreach collection="postCodes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        AND (s.dept_id = #{deptId} OR sud.dept_id = #{deptId});

<!--        WITH eligible_users AS (-->
<!--        SELECT DISTINCT sup.user_id-->
<!--        FROM system_user_post sup-->
<!--        LEFT JOIN system_post sp ON sup.post_id = sp.id-->
<!--        LEFT JOIN system_users c ON sup.user_id = c.id-->
<!--        WHERE c.tenant_id = #{tenantId}-->
<!--        AND sup.deleted = 0-->
<!--        AND sp.code IN-->
<!--        <foreach collection="postCodes" item="code" open="(" close=")" separator=",">-->
<!--            #{code}-->
<!--        </foreach>-->
<!--        )-->
<!--        SELECT DISTINCT eu.*-->
<!--        FROM eligible_users eu-->
<!--        INNER JOIN system_users s ON eu.user_id = s.id AND s.dept_id = #{deptId}-->
<!--        UNION-->
<!--        SELECT DISTINCT eu.*-->
<!--        FROM eligible_users eu-->
<!--        INNER JOIN system_user_dept sud ON eu.user_id = sud.user_id AND sud.dept_id = #{deptId}-->

    </select>

</mapper>
