<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.message.MessageSendMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id = "selectGet" resultType="com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendRespGetVO">
        SELECT
            st.send_id,
            sm.if_timed_send,
            st.send_time,
            st.message,
            st.receiver,
            st.phone,
            st.success_message,
            st.fail_message,
            st.status
        FROM system_timed_task st
                 left join system_message_send sm on sm.id = st.send_id
        WHERE st.send_id= #{param.id}

    </select>
    <resultMap id="messageSendPageRespVO" type="com.unicom.swdx.module.system.controller.admin.message.vo.send.MessageSendPageRespVO">
        <result column="receiving_person_ids" property="receivingPersonIds" typeHandler="com.unicom.swdx.framework.mybatis.core.type.JsonLongSetTypeHandler"/>
        <result column="receiving_person_names" property="receivingPersonNames" typeHandler="com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler"/>
        <result column="receiving_mobile" property="receivingMobile" typeHandler="com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler"/>
    </resultMap>
    <select id = "selectMyPage" resultMap="messageSendPageRespVO">
        select
        u.nickname,
        sm.id,
        sm.if_timed_send ,
        sm.timed_send_time,
        sm.system_id,
        sm.template_name,
        sm.message_content,
        sm.template_content,
        sm.receiving_person_ids,
        sm.receiving_person_names,
        sm.receiving_mobile,
        sm.create_time,
        sm.message_success,
        sm.message_fail,
        sm.notice_ready,
        sm.message_ready,
        sm.send_mode,
        st.success_message,
        (st0.total-st.success_message) AS fail_message
        FROM system_message_send sm

        left join (
        SELECT
        send_id,
        count(success_message) as success_message
        FROM system_timed_task
        WHERE success_message=true
        GROUP BY send_id

        ) st on sm.id= st.send_id
        left join (
        SELECT
        send_id,
        count(*) as total
        FROM system_timed_task
        GROUP BY send_id

        ) st0 ON st.send_id= st0.send_id
        left join(
        select nickname,id from system_users
        ) u on sm.creator = u.id
        WHERE sm.deleted = 0

        <if test=" param.templateName != null ">
            and sm.template_name like CONCAT('%',#{param.templateName},'%')
        </if>
        <if test="param.createTime !=null  and param.createTime.length >0">
            and sm.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>
        <if test=" param.messageContent != null ">
            and (sm.template_content like CONCAT('%', #{param.messageContent},'%')
            or sm.message_content like CONCAT('%', #{param.messageContent},'%'))
        </if>
        <if test=" isAdmin != true and creator !=null ">
            and sm.creator = #{creator}
        </if>
        <if test=" hasSuperAdmin != true and tenantId !=null ">
            and sm.tenant_id = #{tenantId}
        </if>
        <if test=" nickname != null ">
            and u.nickname like CONCAT('%',#{nickname},'%')
        </if>

        order by sm.id DESC
    </select>

</mapper>
