<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectBaseRole" resultType="com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleUsersRespVO">
        select r.NAME,oa.NAME as applicationName
        from SYSTEM_ROLE as r
        left join SYSTEM_OAUTH2_CLIENT as oa on r.CLIENT_ID = oa.ID
        where r.DELETED = 0
        and r.ID = #{id};
    </select>

    <select id="selectAllRoleByUserId" resultType="java.lang.Long">
        SELECT
            r.id
        FROM
            system_users u
                INNER JOIN system_user_role sur ON u.id = sur.user_id
                INNER JOIN system_role r ON sur.role_id = r.id
        WHERE
            u.id = #{uid} AND sur.deleted != true
    </select>
</mapper>
