<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.databoard.DataboardMapper">

    <select id="selectAllTenantCount" resultType="Long">
        select count(id) as allTenant from system_tenant
        where deleted = 0 and registry_status = 0
    </select>

    <select id="selectNewTenantCount" resultType="Long">
        select count(id) as newTenant from system_tenant
        where deleted = 0 and registry_status = 0
          and create_time like CONCAT(#{localdate}, '%')
    </select>

    <select id="selectRegisteringTenantCount" resultType="Long">
        select count(id) as registeringTenant from system_tenant_approval
        where deleted = 0 and approval_status = 0;
    </select>

    <select id="selectAllUsersCount" resultType="Long">
        select ((select count(id) from system_users where deleted = 0) + (select count(id) from system_users_public where deleted = 0))
    </select>

    <select id="selectNewUsersCount" resultType="Long">
        select ((select count(id) from system_users where deleted = 0 and create_time like CONCAT(#{localdate}, '%')) + (select count(id) from system_users_public where deleted = 0 and create_time like CONCAT(#{localdate}, '%')))
    </select>

    <select id="selectActiveUsersCount" resultType="Long">
        select ((select count(id) from system_users where deleted = 0 and login_date like CONCAT(#{localdate}, '%')) + (select count(id) from system_users_public where deleted = 0 and login_date like CONCAT(#{localdate}, '%')))
    </select>

    <select id="selectAMonthTenantRespVO" resultType="com.unicom.swdx.module.system.controller.admin.databoard.vo.LastSixMonthTenantRespVO">
        select
            (select count(id) from system_tenant
            where registry_status = 0 and deleted = 0 and create_time &lt; #{yearMonth} and create_time &gt; DATE_ADD(#{yearMonth} ,interval '-1' month )) as newTenant,
            (select count(id) from system_tenant
             where registry_status = 0 and   deleted = 0 and create_time &lt; DATE_ADD(#{yearMonth} ,interval '-1' month )) as lastMonthTenant,
            (select count(id) from system_tenant
             where registry_status = 0 and  deleted = 0) as allTenant,
            DATE_ADD(#{yearMonth} ,interval '-1' month ) as yearMonth
    </select>

    <select id="selectAMonthUsersRespVO" resultType="com.unicom.swdx.module.system.controller.admin.databoard.vo.LastSixMonthUsersRespVO">
        select
        (select count(id) from system_users
        where deleted = 0 and create_time <![CDATA[<]]> #{yearMonth} ) as tenantUsers,
        (select count(id) from system_users_public
        where deleted = 0 and create_time <![CDATA[<]]> #{yearMonth}) as publicUsers,
        (select count(id) from system_users
        where deleted = 0 and create_time <![CDATA[<]]> #{yearMonth})
        +(select count(id) from system_users_public
        where deleted = 0 and create_time <![CDATA[<]]> #{yearMonth}) as allUsers,
        DATE_ADD(#{yearMonth} ,interval '-1' month ) as yearMonth
    </select>

    <select id="selectTenantTypeList" resultType="Long">
        select id from system_tenant_type
        where deleted = 0
    </select>

    <select id="selectTenantTypeName" resultType="String">
        select name from system_tenant_type
        where id = #{id}
    </select>






</mapper>
