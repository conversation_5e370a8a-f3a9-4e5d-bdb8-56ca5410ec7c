<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <update id="updateStatusByDeptIdList">
        update system_dept
        set status = #{status}
        where deleted = 0
        and id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getDeptIdByUserId" resultType="java.lang.Long">
        select
            id
        from
            system_dept sd
        where
            sd.status = 0
          and sd.deleted = 0
          and sd.parent_id =0
          and sd.tenant_id in (
            select
                tenant_id
            from
                system_users su
            where
                su.id = #{id}
              and su.status = 0
              and su.deleted = 0
        )
    </select>

    <resultMap id="deptDtoResult" type="com.unicom.swdx.module.system.api.dept.dto.DeptDTO">
        <result column="name" property="dept_name"/>
        <result column="id" property="new_dept_id"/>
        <result column="parent_id" property="new_parent_id"/>
        <result column="sort" property="dept_num"/>
    </resultMap>

    <select id="selectSimpleDeptInfoList" resultMap="deptDtoResult">
        select
            a.name,
            a.id,
            a.parent_id,
            a.sort
        from system_dept a
        where a.deleted = '0'
        order by parent_id, id
    </select>

    <select id="selectLeaderIdsByDeptId" resultType="Long">
        SELECT leader_user_id
        FROM system_dept
        WHERE id IN
        <foreach item="item" index="index" collection="leaderDeptIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and leader_user_id is not null
    </select>

    <select id="getIdByName" resultType="Long">
        SELECT id
        FROM system_dept
        WHERE name = #{name} and tenant_id = #{tenantId}
        and status = 0 and deleted != 1 limit 1;
    </select>

    <select id="selectByIdIgnoDeleted" resultType="com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO">
        SELECT *
        FROM system_dept
        WHERE id =  #{id};
    </select>

</mapper>
