<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.shortcut.ShortcutUserMapper">


    <!--<delete id="deleteByShortcutIds">
        delete midoffice_shortcut_user
        where shortcut_id in
        <foreach collection="shortcutIds" item="id" open="(" close=")" separator=",">
            id
        </foreach>
    </delete>-->

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectListByUserId"
            resultType="com.unicom.swdx.module.system.controller.admin.home.shortcut.vo.shortcut.ShortcutRespVO">
        select
            ms.id,ms.client_id,ms.name,ms.link_url,ms.remark,ms.svg_icon
        from midoffice_shortcut_user su
        left join midoffice_shortcut ms on su.shortcut_id = ms.id
        where su.user_id = #{userId}
        and ms.status = 0
        and ms.deleted = 0
        and su.deleted = 0
    </select>
</mapper>
