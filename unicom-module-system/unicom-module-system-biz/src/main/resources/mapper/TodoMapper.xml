<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.todo.TodoMapper">
    <update id="updateTodo">
        update midoffice_todo
        set status = 1
        where deleted = 0
        and subsystem_id = #{subsystemId}
        <if test="path != null and path != ''">
            and link_url = #{path}
        </if>
        and process_id = #{processId}
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="taskCode != null and taskCode != ''">
            and task_code = #{taskCode}
        </if>
        <if test="processStatus != null and processStatus != ''">
            and process_status = #{processStatus}
        </if>
        and todo_user_id in
        <foreach collection="todoUsers" item="todoUserId" open="(" close=")" separator=",">
            #{todoUserId}
        </foreach>
    </update>
    <update id="updateTodoRemark">
        update midoffice_todo
        set remark = #{remark}
        where deleted = 0
        and subsystem_id = #{subsystemId}
        <if test="path != null and path != ''">
            and link_url = #{path}
        </if>
        and process_id = #{processId}
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="taskCode != null and taskCode != ''">
            and task_code = #{taskCode}
        </if>
        <if test="processStatus != null and processStatus != ''">
            and process_status = #{processStatus}
        </if>
        and todo_user_id in
        <foreach collection="todoUsers" item="todoUserId" open="(" close=")" separator=",">
            #{todoUserId}
        </foreach>
    </update>

    <select id="selectTodoPage" resultType="com.unicom.swdx.module.system.dal.dataobject.todo.TodoDO">
        select
            a.id,
            a.type,
            a.urgency_level,
            a.title,
            a.submitter,
            a.receipt,
            a.START_TIME ,
            a.END_TIME ,
            a.MEETING_ADDRESS ,
            a.remark,
            a.submit_time,
            a.subsystem_id,
            a.link_url,
            a.process_id,
            a.process_type,
            a.process_status,
            a.task_code,
            a.status,
            a.RECEIVING_SOURCE_TENANT_ID,
            a.PEOPLE_INERACTION_SOURCE
        FROM midoffice_todo a
        left join SYSTEM_USERS u on a.SUBMITTER  = u.ID
        WHERE (START_TIME IS  NULL or START_TIME > CURRENT_TIMESTAMP)
        and (a.todo_user_id = #{userId}
            <if test="permissions != null and permissions.size > 0">
                or a.permission in
                <foreach collection="permissions" item="permission" open="(" close=")" separator=",">
                    #{permission}
                </foreach>
            </if>
             )
        and a.deleted = 0
        <if test=" param.status != null ">
            and a.status = #{param.status}
        </if>
        <if test=" param.type != null ">
            and a.type = #{param.type}
        </if>
        <if test=" param.title != null and param.title != ''">
            and (a.title like CONCAT('%',#{param.title},'%') or u.NICKNAME  like CONCAT('%',#{param.title},'%'))
        </if>
        <choose>
            <when test="param.sort != null and param.sort == 1 ">
                order by a.submit_time asc
            </when>
            <otherwise>
                order by a.submit_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getTodoListPage" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoListPageVO">
        (select
            key_work_content as task,
            launch_user_name as name,
            update_time as initiationTime,
            CAST(id AS VARCHAR) as processInstanceId,
            null as category,
            year_tag as yeartag,
            status,
            null as bteName,
            null as bteStatus,
        CASE WHEN now()>end_time and  (status=20 or (status=30 and is_complete=0 and (year_tag=1 or year_tag=0)) or (status=30 and year_tag=2) )  THEN 1 ELSE 0 END AS superviseStatus,
            COALESCE(CAST(is_complete AS INT), null) as shandle,
            end_time as endtime,
            null as createTime,
            null as row_num
         from
            oa_central_task yi
        where
        (id in (
        select
        infor_id
        from
        oa_central_task_extend
        where
        user_id = #{loginUserId}
        and user_type = 1 )
        and ((status = 30 and is_complete = 0) or status = 20 )
        and year_tag != 2 and deleted=0 and tenant_id =#{tenantId})
        or (status = 30
        and is_complete = 1 and deleted=0
        and launch_user_id=#{loginUserId}
        and year_tag!=2
        and tenant_id =#{tenantId})
        or (yi.id in (select octe.infor_id from oa_central_task_extend octe
        where user_type =4 and user_id =#{loginUserId})
        and  yi.year_tag=2 and deleted=0 and tenant_id =#{tenantId} and ((yi.status=4  and yi.head_approve=2) or (yi.status=30 and yi.is_complete=2 )))
        or (yi.id in (select octe.infor_id from oa_central_task_extend octe
        where user_type =1 and user_id =#{loginUserId})
        and  yi.year_tag=2 and deleted=0 and tenant_id =#{tenantId} and status in (4,30))
        <if test="hhandle !=null">
            <if test="hhandle ==0">
                or (id in (
                select
                infor_id
                from
                oa_central_task_extend yu
                where
                yu.user_id = #{loginUserId}
                and yu.dept_id in (select id from system_dept sd where sd.leader_user_id =#{loginUserId} and deleted=0 and
                status=0))
                and ((head_approve=0 and status=4) or (is_complete=1 and status=30)) and deleted=0 and year_tag =2 and tenant_id =#{tenantId} )
                or
                (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId})
            </if>
            <if test="hhandle ==1">
                or
                (head_approve = 1 AND status = 4 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId})
                or
                (is_complete =3 AND status = 30 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId})
                or
                (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId})
            </if>
            <if test="hhandle ==2">
                or (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId})
            </if>
            <if test="hhandle ==3">
                or
                (head_approve = 3 AND status = 4 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId})
                or
                (is_complete =4 AND status = 30 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId})
                or
                (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId})
            </if>
        </if>
          and deleted = 0)

        union

        (
        select sub.task,
        sub.name,
        sub.initiationTime,
        sub.processInstanceId,
        sub.category,
        sub.yeartag,
        sub.status,
        sub.bteName,
        sub.bteStatus,
        sub.superviseStatus,
        sub.shandle,
        sub.endtime,
        sub.createTime,
        sub.row_num
        from (
        select sdd."label" as task,
        su.nickname as name,
        a.InitiationTime as initiationTime,
        a.process_instance_id as processInstanceId,
        a.category as category,
        0 as yeartag,
        null as status,
        a.name as bteName,
        a.status as bteStatus,
        coalesce(cast(a.supervise_status as INT), 0) as superviseStatus,
        null as shandle,
        null as endtime,
        a.create_time as createTime,
        row_number() over (partition by sdd."label" order by a.create_time desc) as row_num
        from (
        select bpie.start_user_id,
        bpie.category,
        bpie.update_time as InitiationTime,
        bpie.process_instance_id,
        bte.name,
        bpie.status,
        bpie.end_time,
        bte.supervise_status,
        bpie.create_time
        from bpm_process_instance_ext bpie
        join bpm_task_ext bte on bpie.process_instance_id = bte.process_instance_id
        where bte.assignee_user_id = #{loginUserId}
        and bpie.deleted = 0
        and (bpie.status = 1 or bpie.status = 4)
        and bte.result = 1
        and (bpie.category != 13 or bte.name != '秘书科汇总')
        ) a
        left join system_users su on su.id = a.start_user_id and su.deleted = 0
        left join system_dict_data sdd on a.category = sdd.value and sdd.dict_type = 'bpm_model_category' and sdd.deleted = 0
        ) sub
        where (sub.task != '寒暑假坐值班'
        <if test="isApp==0">
            or (sub.task = '寒暑假坐值班' and sub.btename='秘书科汇总'and sub.row_num = 1)
        </if>
        or (sub.task = '寒暑假坐值班' and sub.btename !='秘书科汇总'))
        <if test="isApp==1">
            and sub.task != '一周工作安排汇总'
        </if>
        )
        order by superviseStatus desc, initiationTime desc
    </select>

<!--    <if test="leadleaders != null and leadleaders.size > 0">-->
<!--        (or id in-->
<!--        <foreach collection="leadleaders" item="leadleader" open="(" close=")" separator=",">-->
<!--            #{leadleader}-->
<!--        </foreach>-->
<!--        and status=4 and head_approve=2 and year_tag=2)-->
<!--        (or id in-->
<!--        <foreach collection="leadleaders" item="leadleader" open="(" close=")" separator=",">-->
<!--            #{leadleader}-->
<!--        </foreach>-->
<!--        and status=30 and is_complete=2 and year_tag=2)-->
<!--    </if>-->

    <select id="getTodoAppListPage" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoListPageVO">
        (select
             key_work_content as task,
             launch_user_name as name,
             create_time as initiationTime,
             CAST(id AS VARCHAR)  as processInstanceId,
             null as category,
             year_tag as yeartag,
             status,
             null as bteName,
             null as bteStatus,
             supervise_status as superviseStatus,
             COALESCE(CAST(is_complete AS INT), null) as shandle
         from
             oa_central_task yi
         where
        (id in (
        select
        infor_id
        from
        oa_central_task_extend
        where
        user_id = #{loginUserId}
        and user_type = 1  )
        and ((status = 30 and is_complete = 1)or status = 20 )
        and year_tag != 2
        and deleted = 0)
        or (status = 30
        and is_complete = 1
        and launch_user_id=#{loginUserId}
        and year_tag!=2
        and deleted = 0)
        or (id in (
        select
        infor_id
        from
        oa_central_task_extend
        where
        user_id = #{loginUserId}
        and user_type=1
        )
        and (status=30 and is_complete=0 )
        and year_tag=2
        and deleted = 0)
        <if test="hhandle !=null">
            or (id in (
            select
            infor_id
            from
            oa_central_task_extend
            where
            user_id = #{loginUserId}
            and  (user_type=11 or user_type=33)
            )
            <if test="hhandle ==0">
                and (status=4 or (status=30 and is_complete=0 )) and
                head_approve=#{hhandle}
            </if>
            <if test="hhandle ==1">
                and (status=4 or (status=30 and is_complete=1 )) and
                head_approve=#{hhandle}
            </if>
            and year_tag=2)
        </if>
           and deleted = 0)

        union

        (select
             distinct
             sdd."label" as task,
             su.nickname as name,
             a.InitiationTime as initiationTime,
             a.process_instance_id as processInstanceId,
             a.category as category,
             0 as yeartag,
             null as status,
             a.name as bteName,
             a.status as bteStatus,
             COALESCE(CAST(a.supervise_status AS INT), 0) as superviseStatus,
             null as shandle
         from
             (
                 select
                     bpie.start_user_id,
                     bpie.category,
                     bpie.sort_time_for_launch as InitiationTime,
                     bpie.process_instance_id,
                     bte.name,
                     bpie.status,
                     bpie.end_time,
                     bte.supervise_status
                 from
                     bpm_process_instance_ext bpie
                         join bpm_task_ext bte
                              on
                                  bpie.process_instance_id = bte.process_instance_id
                 where
                     bte.assignee_user_id = #{loginUserId}
                   and bpie.deleted=0
                   and (bpie.status = 1
                     or bpie.status = 4)
                   and bte.result = 1
             ) a
                 left join system_users su on
                         su.id = a.start_user_id
                     and su.deleted = 0
                 left join system_dict_data sdd on
                         a.category = sdd.value
                     and sdd.dict_type = 'bpm_model_category'
                     and sdd.deleted = 0)
        order by superviseStatus desc,initiationTime desc
    </select>

    <select id="getDoneListPage" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoListPageVO">
        (select
            key_work_title as task,
            launch_user_name as name,
            create_time as initiationTime,
            CAST(id AS VARCHAR)  as processInstanceId,
            null as category,
            year_tag as yeartag,
            status
        from
            oa_central_task yi
        where
                id in (
                select
                    infor_id
                from
                    oa_central_task_extend
                where
                    user_id = #{loginUserId}
                  and user_type = 1 )
          and status=50
          and deleted = 0)
        union

        (select
            distinct
            sdd."label" as task,
            su.nickname as name,
            a.InitiationTime as initiationTime,
            a.process_instance_id as processInstanceId,
            a.category as category,
            0 as yeartag,
            null as status
        from
            (
                select
                    bpie.start_user_id,
                    bpie.category,
                    bpie.sort_time_for_launch as InitiationTime,
                    bpie.process_instance_id
                from
                    bpm_process_instance_ext bpie
                        join bpm_task_ext bte
                             on
                                 bpie.process_instance_id = bte.process_instance_id
                where
                    bte.assignee_user_id = #{loginUserId}
                  and bpie.deleted=0
                  and (bte.result = 2 or bte.result = 3)

            ) a
                left join system_users su on
                        su.id = a.start_user_id
                    and su.deleted = 0
                left join system_dict_data sdd on
                        a.category = sdd.value
                    and sdd.dict_type = 'bpm_model_category'
                    and sdd.deleted = 0)
                    order by initiationTime desc
    </select>

    <select id="getAppTodoListPage" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.AppTodoListPageVO">
        select
            su.nickname as name,
            sdd."label" as task,
            a.InitiationTime,
            a.status,
            a.process_instance_id as processInstanceId,
            a.category
        from
            (
                select
                    bpie.start_user_id,
                    bpie.category,
                    bpie.sort_time_for_launch as InitiationTime,
                    bpie.status,
                    bpie.process_instance_id
                from
                    bpm_process_instance_ext bpie
                        join bpm_task_ext bte
                             on
                                 bpie.process_instance_id = bte.process_instance_id
                where
                            bte.assignee_user_id = #{loginUserId}
                        and (bpie.status = 1 or bpie.status = 4)
                        and bte.result = 1
                        and bpie.deleted=0
            ) a
                left join system_users su on
                        su.id = a.start_user_id
                    and su.deleted = 0
                left join system_dict_data sdd on
                        a.category = sdd.value
                    and sdd.dict_type = 'bpm_model_category'
                    and sdd.deleted = 0
    </select>

    <select id="getTodoListPageALL" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoListPageAllVO">
        ( select
        key_work_content as task,
        launch_user_name as name,
        launch_dept_name as deptName ,
        update_time as initiationTime,
        CAST(id AS VARCHAR) as processInstanceId,
        null as category,
        year_tag as yeartag,
        status,
        null as bteName,
        null as bteStatus,
        CASE WHEN now()>end_time and  (status=20 or (status=30 and is_complete=0 and (year_tag=1 or year_tag=0)) or (status=30 and year_tag=2) )  THEN 1 ELSE 0 END AS superviseStatus,
        COALESCE(CAST(is_complete AS INT), null)shandle,
        end_time as endtime,
        null as createTime,
        null as row_num,
        head_approve as hhandle,
        handle_count as handleCount,
        launch_user_id as suserid
        from
        oa_central_task yi
        where
        ((id in (
        select
        infor_id
        from
        oa_central_task_extend
        where
        user_id = #{loginUserId}
        and user_type = 1 )
        and ((status = 30 and is_complete = 0) or status = 20 )
        and year_tag != 2 and deleted=0 and tenant_id =#{tenantId}
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_content like concat('%',#{task1},'%')
            </when>
        </choose>
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when>
        </choose>
        )
        or (status = 30
        and is_complete = 1 and deleted=0
        and launch_user_id=#{loginUserId}
        and year_tag!=2
        and tenant_id =#{tenantId}
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_content like concat('%',#{task1},'%')
            </when>
        </choose>
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when>
        </choose>
        ))
        or (yi.id in (select octe.infor_id from oa_central_task_extend octe
        where user_type =4 and user_id =#{loginUserId})
        and  yi.year_tag=2 and deleted=0 and tenant_id =#{tenantId} and ((yi.status=4  and yi.head_approve=2) or (yi.status=30 and yi.is_complete=2 ))
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_content like concat('%',#{task1},'%')
            </when>
        </choose>
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when>
        </choose>)
        or (yi.id in (select octe.infor_id from oa_central_task_extend octe
        where user_type =1 and user_id =#{loginUserId})
        and  yi.year_tag=2 and deleted=0 and tenant_id =#{tenantId} and status in (4,30)
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_content like concat('%',#{task1},'%')
            </when>
        </choose>
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when>
        </choose>)
        <if test="hhandle !=null">
            <if test="hhandle ==0">
                or (id in (
                select
                infor_id
                from
                oa_central_task_extend yu
                where
                yu.user_id = #{loginUserId}
                and yu.dept_id in (select id from system_dept sd where sd.leader_user_id =#{loginUserId} and deleted=0 and
                status=0))
                and ((head_approve=0 and status=4) or (is_complete=1 and status=30)) and deleted=0  AND year_tag = 2 and tenant_id =#{tenantId})
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>
                or (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>
                )

            </if>
            <if test="hhandle ==1">
                or
                (head_approve = 1 AND status = 4 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>)
                or
                (is_complete =3 AND status = 30 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>)
                or
                (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>)
            </if>
            <if test="hhandle ==2">
                or (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>
                )
            </if>
            <if test="hhandle ==3">
                or
                (head_approve = 3 AND status = 4 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>)
                or
                (is_complete =4 AND status = 30 AND year_tag = 2 and deleted =0 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>)
                or
                (status=30 and is_complete=0 and deleted=0 and launch_user_id= #{loginUserId} and year_tag =2 and tenant_id =#{tenantId}
                <choose>
                    <when test="task1 !=null and task1 !='' ">
                        and key_work_content like concat('%',#{task1},'%')
                    </when>
                </choose>
                <choose>
                    <when test="name1 !=null and name1 !='' ">
                        and launch_user_name like concat('%',#{name1},'%')
                    </when>
                </choose>)
            </if>
        </if>
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_content like concat('%',#{task1},'%')
            </when>
        </choose>
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when>
        </choose>
        and deleted = 0
        )
        union
        (
        select sub.task,
        sub.name,
        sub.deptName,
        sub.initiationTime,
        sub.processInstanceId,
        sub.category,
        sub.yeartag,
        sub.status,
        sub.bteName,
        sub.bteStatus,
        sub.superviseStatus,
        sub.shandle,
        sub.endtime,
        sub.createTime,
        sub.row_num,
        null as hhandle,
        null as handleCount,
        null as suserid
        from (
        select sdd."label" as task,
        su.nickname as name,
        sd."name" as deptName,
        a.InitiationTime as initiationTime,
        a.process_instance_id as processInstanceId,
        a.category as category,
        0 as yeartag,
        null as status,
        a.name as bteName,
        a.status as bteStatus,
        COALESCE(CAST(a.supervise_status AS INT), 0) as superviseStatus,
        null as shandle,
        null as endtime,
        a.create_time as createTime,
        row_number() over (partition by sdd."label" order by a.create_time desc) as row_num
        from (
        select bpie.start_user_id,
        bpie.category,
        bpie.update_time as InitiationTime,
        bpie.process_instance_id,
        bte.name,
        bpie.status,
        bpie.end_time,
        bte.supervise_status,
        bpie.create_time
        from bpm_process_instance_ext bpie
        join bpm_task_ext bte on bpie.process_instance_id = bte.process_instance_id
        where bte.assignee_user_id = #{loginUserId}
        and bpie.deleted = 0
        and (bpie.status = 1 or bpie.status = 4)
        and bte.result = 1
        and
        IF( bpie.category = 13 ,
        bte.name != '秘书科汇总',
        true)
        ) a
        left join system_users su on
        su.id = a.start_user_id
        and su.deleted = 0
        <choose>
            <when test="name1 !=null and name1 !=''">
                and su.nickname like concat('%',#{name1},'%')
            </when>
        </choose>
        left join system_dept sd on
        su.dept_id=sd.id
        join system_dict_data sdd on
        a.category = sdd.value
        and sdd.dict_type = 'bpm_model_category'
        and sdd.deleted = 0
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and sdd.label like concat('%',#{task1},'%')
            </when>
        </choose>
        ) sub
        where (sub.task != '寒暑假坐值班'
        <if test="isApp==0">
            or (sub.task = '寒暑假坐值班' and sub.btename='秘书科汇总'and sub.row_num = 1)
        </if>
        or (sub.task = '寒暑假坐值班' and sub.btename !='秘书科汇总'))
        <if test="isApp==1">
            and sub.task != '一周工作安排汇总'
        </if>
        )
        order by superviseStatus desc, initiationTime desc
    </select>

    <select id="getTodoAppListPageALL" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.TodoListPageAllVO">
        ( select
        key_work_content as task,
        launch_user_name as name,
        launch_dept_name as deptName,
        create_time as initiationTime,
        CAST(id AS VARCHAR) as processInstanceId,
        null as category,
        year_tag as yeartag,
        status,
        null as bteName,
        null as bteStatus,
        supervise_status as superviseStatus,
        COALESCE(CAST(shandle AS INT), null)shandle
        from
        oa_central_task yi
        where
        (id in (
        select
        infor_id
        from
        oa_central_task_extend
        where
        user_id = #{loginUserId}
        and user_type = 1  )
        and ((status = 30 and is_complete = 1)or status = 20 )
        and year_tag != 2)
        or (status = 30
        and is_complete = 1
        and launch_user_id=#{loginUserId}
        and year_tag!=2)
        or (id in (
        select
        infor_id
        from
        oa_central_task_extend
        where
        user_id = #{loginUserId}
        and  user_type=1
        )
        and (status=30 and is_complete=0 )
        and year_tag=2)
        <if test="hhandle !=null">
            or (id in (
            select
            infor_id
            from
            oa_central_task_extend
            where
            user_id = #{loginUserId}
            and  (user_type=11 or user_type=33)
            )
            <if test="hhandle ==0">
                and (status=4 or (status=30 and is_complete=0 )) and
                head_approve=#{hhandle}
            </if>
            <if test="hhandle ==1">
                and (status=4 or (status=30 and is_complete=1 )) and
                head_approve=#{hhandle}
            </if>
            and year_tag=2)
        </if>
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_content like concat('%',#{task1},'%')
            </when>
        </choose>
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when>
        </choose>
        and deleted = 0
        )
        union

        (select
        distinct
        sdd."label" as task,
        su.nickname as name,
        sd."name" as deptName,
        a.InitiationTime as initiationTime,
        a.process_instance_id as processInstanceId,
        a.category as category,
        0 as yeartag,
        null as status,
        a.name as bteName,
        a.status as bteStatus,
        COALESCE(CAST(a.supervise_status AS INT), 0) as superviseStatus,
        null as shandle
        from
        (
        select
        bpie.start_user_id,
        bpie.category,
        bpie.sort_time_for_launch as InitiationTime,
        bpie.process_instance_id,
        bte.name,
        bpie.status,
        bte.supervise_status
        from
        bpm_process_instance_ext bpie
        join bpm_task_ext bte
        on
        bpie.process_instance_id = bte.process_instance_id
        where
        bte.assignee_user_id = #{loginUserId}
        and bpie.deleted=0
        and (bpie.status = 1
        or bpie.status = 4)
        and bte.result = 1
        ) a
        join system_users su on
        su.id = a.start_user_id
        and su.deleted = 0
        <choose>
            <when test="name1 !=null and name1 !=''">
                and su.nickname like concat('%',#{name1},'%')
            </when>
        </choose>

        left join system_dept sd on
        su.dept_id=sd.id
        join system_dict_data sdd on
        a.category = sdd.value
        and sdd.dict_type = 'bpm_model_category'
        and sdd.deleted = 0
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and sdd.label like concat('%',#{task1},'%')
            </when>
        </choose>
        )
        order by superviseStatus desc,initiationTime desc
    </select>

    <select id="getDoneListPageALL" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.DoneListPageAllVO">
        (select
        key_work_title as task,
        launch_user_name as name,
        launch_dept_name as deptName,
        create_time as initiationTime,
        CAST(id AS VARCHAR)  as processInstanceId,
        null as category,
        year_tag as yeartag,
        status
        from
            oa_central_task yi
        where
                id in (
                select
                    infor_id
                from
                    oa_central_task_extend
                where
                    user_id = #{loginUserId}
                  and user_type = 1 )
        <choose>
            <when test="name1 !=null and name1 !='' ">
                and launch_user_name like concat('%',#{name1},'%')
            </when >
        </choose>
          and status=50
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and key_work_title like concat('%',#{task1},'%')
            </when >
        </choose>
        and deleted = 0)
        union

        (select
        distinct
        sdd."label" as task,
        su.nickname as name,
        sd."name" as deptName,
        a.InitiationTime as initiationTime,
        a.process_instance_id as processInstanceId,
        a.category as category,
        0 as yeartag,
        null as status
        from
        (
        select
        bpie.start_user_id,
        bpie.category,
        bpie.sort_time_for_launch as InitiationTime,
        bpie.process_instance_id
        from
        bpm_process_instance_ext bpie
        join bpm_task_ext bte
        on
        bpie.process_instance_id = bte.process_instance_id
        where
        bte.assignee_user_id = #{loginUserId}
        and bpie.deleted=0
        and (bte.result = 2 or bte.result = 3)

        ) a
        join system_users su on
        su.id = a.start_user_id
        and su.deleted = 0
        <choose>
            <when test="name1 !=null and name1 !=''">
                and su.nickname like concat('%',#{name1},'%')
            </when>
        </choose>

        left join system_dept sd on
        su.dept_id=sd.id
        join system_dict_data sdd on
        a.category = sdd.value
        and sdd.dict_type = 'bpm_model_category'
        and sdd.deleted = 0
        <choose>
            <when test="task1 !=null and task1 !='' ">
                and sdd.label like concat('%',#{task1},'%')
            </when>
        </choose>)
        order by initiationTime desc
    </select>

    <select id="selectByTenantIdBpm" resultType="java.lang.Long">
        SELECT user_id FROM system_user_role WHERE role_id in (SELECT id FROM system_role WHERE code = 'office_admin' and deleted = 0 and status=0 and tenant_id = #{tenantId}) and deleted = 0
    </select>

    <select id="selectReportOfficerByTenantIdBpm" resultType="java.lang.Long">
        SELECT user_id FROM system_user_role WHERE role_id in (SELECT id FROM system_role WHERE code = 'oa-central-report-officer' and deleted = 0 and status=0 and tenant_id = #{tenantId} ) and deleted = 0
    </select>

    <select id="selectPrincipalByTenantIdBpm" resultType="java.lang.Long">
        SELECT user_id FROM system_user_role WHERE role_id in (SELECT id FROM system_role WHERE code = 'oa-vice-headmaster-daily-work' and deleted = 0 and status=0 and tenant_id = #{tenantId} ) and deleted = 0
    </select>

    <select id="selectUserinforByInforId" resultType="com.unicom.swdx.module.system.controller.admin.home.todo.vo.UserinforToDoVO">
        select id,user_id as uid ,user_name as uname,user_type as utype ,infor_id as inforid ,dept_id as dptid ,dept_name as dptname ,treecode ,tenant_id as tenantId from oa_central_task_extend octe
        where infor_id =#{inforId}
    </select>


</mapper>
