<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.oaNotice.NoticeReadRecordMapper">


    <select id="hadRecord" resultType="Integer">
        select
            count(0)
        FROM notice_read_record
        where notice_id=#{recordId}
        and user_id=#{userId}
        and deleted=0
        and create_time &lt; now()
    </select>


    <insert id="insertOne" parameterType="Long">
        INSERT INTO notice_read_record  ( notice_id,  user_id )  VALUES  ( #{recordId},  #{userId} )
    </insert>


    <update id="updateReadNum" >
        update oa_notice set read_number = read_number+1 where id=#{id}
    </update>


    <update id="emptyReadRecord" >
        update notice_read_record set deleted = 1 where notice_id=#{id}
    </update>

</mapper>
