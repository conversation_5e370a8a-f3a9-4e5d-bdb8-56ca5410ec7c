<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.dict.DictDataMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectAllChild" resultType="com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO">
        select
            id,sort,parent_id,label,value,dict_type,status,color_type,css_class,remark,create_time
        from system_dict_data
        <where>
            deleted = 0
            and parent_id in
            <foreach collection="idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
        connect by prior id = parent_id
    </select>

    <select id="selectAllChildByStatus" resultType="com.unicom.swdx.module.system.dal.dataobject.dict.DictDataDO">
        select
            distinct(id),sort,parent_id,label,value,dict_type,status,color_type,css_class,remark,create_time
        from system_dict_data
        <where>
            deleted = 0
            and parent_id in
            <foreach collection="idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        connect by prior id = parent_id
    </select>



    <delete id="deletedictById">
        DELETE FROM system_dict_data
        WHERE id = #{id}
    </delete>


</mapper>
