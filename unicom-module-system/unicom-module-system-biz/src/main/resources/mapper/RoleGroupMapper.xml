<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.permission.RoleGroupMapper">


    <select id="selectByNameAndApplication"
            resultType="com.unicom.swdx.module.system.dal.dataobject.permission.RoleGroupDO">
        SELECT * FROM
        system_role_group
        WHERE
        name = #{name}
        and deleted = false
        and tenant_id = #{tenantId}
        limit 1
    </select>
</mapper>
