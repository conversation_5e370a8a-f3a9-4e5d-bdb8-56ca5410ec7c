<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.permission.UserRoleGroupMapper">


    <select id="getUsersByRoleGroupIds" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        system_user_role_group
        WHERE
        group_id = #{id}
        and
        deleted = false
    </select>
    <select id="selectRoleGroupId" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT group_id
        FROM
        system_user_role_group
        WHERE
        user_id = #{userId}
        and
        deleted = false
    </select>
</mapper>
