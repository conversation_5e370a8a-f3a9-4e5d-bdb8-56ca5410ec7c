<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.logger.OperateLogMapper">

    <select id="selectOperateLogPage" resultType="com.unicom.swdx.module.system.dal.dataobject.logger.OperateLogDO">
        select
            a.id,
            a.module,
            a.name,
            a.type,
            u.username,
            a.result_msg,
            a.result_data,
            a.duration
        FROM system_operate_log a
        left join system_users u on a.user_id = u.id
        WHERE a.user_id = #{userIds}
        <if test=" param.module != null ">
            and (a.module like CONCAT('%',#{param.module},'%') or u.username like CONCAT('%',#{param.module},'%'))
        </if>
        order by a.id desc
    </select>


</mapper>
