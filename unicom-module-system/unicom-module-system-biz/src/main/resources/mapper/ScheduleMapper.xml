<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.schedule.ScheduleMapper">


    <select id="getScheduleByUserIdPId" resultType="com.unicom.swdx.module.system.dal.dataobject.schedule.ScheduleDO">
        select * from home_schedule hs
        where  hs.deleted=0
        and hs.user_id = #{param.userId}
        and hs.process_instance_id = #{param.processInstanceId}
    </select>


</mapper>
