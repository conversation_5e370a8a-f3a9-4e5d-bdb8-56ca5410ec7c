<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <resultMap id="selectUsersResult" type="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO" autoMapping="true">
        <result column="post_ids" property="postIds" typeHandler="com.unicom.swdx.framework.mybatis.core.type.JsonLongSetTypeHandler"/>
    </resultMap>

    <resultMap id="selectUsersResultA" type="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserVO" autoMapping="true">
        <result column="post_ids" property="postIds" typeHandler="com.unicom.swdx.framework.mybatis.core.type.JsonLongSetTypeHandler"/>
    </resultMap>

    <select id = "getUsersByDeptRoleIds" resultType="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO">
        select u.* from system_users u LEFT JOIN system_user_role r on u.id = r.user_id where  r.deleted='0' and u.deleted='0'
        and r.role_id = #{roleId}
        <if test="deptId != null">
            and u.dept_id = #{deptId}
        </if>
    </select>

    <select id="selectUsersByPostCode"
            resultType="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO">
        sele
    </select>

    <select id = "VeryUserAndMobile" resultType="java.lang.Long">
        select u.id
        from system_users u
        where u.username = #{username}
        and u.mobile = #{mobile}
    </select>

    <update id="updateUserMobile">
        update system_users
        set mobile = #{param.newMobile}
        where deleted = 0
          and id = #{param.id}
    </update>

    <update id="updateAppUserMobile">
        update system_users
        set mobile = #{param.newMobile}
        where deleted = 0
          and id = #{param.id}
    </update>

    <update id="updateUserLogin">
        update system_users
        set login_ip = #{param.loginIp},
            login_date = #{param.loginDate}
        where id = #{param.id}
    </update>

    <select id = "selectTenantIdByUserId" resultType="LONG">
        select tenant_id
        FROM system_users
        WHERE id = #{id}
    </select>

    <select id = "selectCountByDeptIdList" resultType="LONG">
        select count(*)
        FROM system_users
        WHERE deleted = 0
        and dept_id in
        <foreach collection="deptIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <sql id="userPageSql">
        SELECT  u.*,
                up.administrative_position_name,
                up.personnal_status,
                up.personnal_classification as personnalClassification
        FROM system_users u
        left join (
        select
        pb.user_id,
        pp.administrative_position_name,
        pp.administrative_position_rank,
        pb.person_status as personnal_status,
        pb.person_classification as personnal_classification
        from
        hr_personnel_basic_information pb
        inner join hr_personnel_position pp
        on
        pp.personnel_id = pb.id where pb.deleted = 0
        ) up on
        up.user_id = u.id
        left join
        system_dept d
        on
        u.dept_id = d.id
        WHERE u.DELETED  = 0 and u.id !=26
        <if test="param.tenantId != null">
            and u.tenant_id = #{param.tenantId}
            and d.tenant_id = #{param.tenantId}
        </if>
        <if test="param.deptId != null">
            and (u.dept_id = #{param.deptId}
            or u.id in (select user_id from system_user_dept
            where dept_id = #{param.deptId} and deleted != 1)
            )
        </if>
        <if test="param.status != null">
            and u.status = #{param.status}
        </if>
        <if test="param.createTime !=null  and param.createTime.length >0">
            and u.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>
        <if test=" param.userName != null ">
            and (u.mobile like CONCAT('%',#{param.username},'%')
            or u.username like CONCAT('%',#{param.username},'%')
            or u.nickname like CONCAT('%',#{param.username},'%'))
        </if>
        <if test="param.personStatus != null">
            and up.personnal_status = #{param.personStatus}
        </if>
        <if test="param.personClassification != null">
            and up.personnal_classification = #{param.personClassification}
        </if>
        <if test="param.isExternal != null and param.isExternal == true">
            and
            d.name IN ('招聘网站', '服务企业')
        </if>
        <if test="param.isExternal != null and param.isExternal == false">
            and
            d.name NOT IN ('招聘网站', '服务企业')
        </if>
        <if test="userId != null and userId != ''">
            and (u.creator = #{userId} or u.id = #{userId})
        </if>
        <if test="dataPermissionDepts != null and dataPermissionDepts.size > 0">
            and u.dept_id in
            <foreach collection="dataPermissionDepts" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size != 0">
            order by DECODE(u.dept_id,<foreach collection="deptIds" item="id" index="index" open="" close="" separator=",">#{id},#{index}</foreach>),
            u.sort,
            CASE
            WHEN up.administrative_position_name IN ('主任', '院长', '主任（校长）', '馆长', '专职副书记') THEN 1
            WHEN up.administrative_position_name IN ('副主任', '副院长', '副主任（副校长）', '副馆长', '副书记、机关纪委书记', '副书记') THEN 2
            ELSE 99
            END ASC,
            up.administrative_position_rank asc
        </if>
    </sql>
    <select id="selectUserPage" resultMap="selectUsersResult">
        <include refid="userPageSql"></include>
    </select>

    <select id="countByDeptIdAdminUserDos"  resultMap="selectUsersResult" >
        SELECT
        dept_id AS deptId,
        COUNT(*) AS id
        FROM
        system_users u
        left join (
        select
        pb.user_id,
        pb.person_status
        from
        hr_personnel_basic_information pb) b
        on b.user_id = u.id
        WHERE
        u.DELETED = 0 and u.id !=26
        <if test="personStatus != null">
            and b.person_status = #{personStatus}
        </if>
        <if test="tenantId != null">
            and u.tenant_id = #{tenantId}
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            <!-- 使用 MyBatis 的动态 SQL，在 IN 子句中动态拼接传入的部门 ID 列表 -->
            AND u.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        GROUP BY
        dept_id
    </select>
    <select id="countByDepts" resultType="com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptCountVO">
        select
            dept_id ,
            count(1)
        from
            (
                select
                    *
                from
                    system_user_dept sud
                        left join
                    (
                        select
                            u.id as uid,
                            u.dept_id as did
                        from
                            system_users u
                    ) u
                    on
                                u.uid = sud.user_id
                            and u.did = sud.dept_id
                where
                    sud.deleted != 1) su
        where
            su.uid is null
          and su.user_id in
              (
                  select
                      u.id
                  from
                      system_users u
                          left join (
                          select
                              pb.user_id,
                              pb.person_status
                          from
                              hr_personnel_basic_information pb) b
                                    on
                                        b.user_id = u.id
                  where
                      u.DELETED = 0
                    and u.id != 26
        <if test="personStatus != null">
            and b.person_status = #{personStatus}
        </if>
        <if test="tenantId != null">
            and u.tenant_id = #{tenantId}
        </if>
          )
        group by
            su.dept_id
    </select>

    <select id="selectUserPageAuthority" resultMap="selectUsersResult">
        SELECT  u.*,
        up.administrative_position_name as administrative_position_name
        FROM system_users u
        left join (
        select
        pb.user_id,
        pp.administrative_position_name,
        pp.administrative_position_rank
        from
        hr_personnel_basic_information pb
        inner join hr_personnel_position pp
        on
        pp.personnel_id = pb.id
        ) up on
        up.user_id = u.id
        WHERE u.DELETED  = 0
        and u.status = 0
        <if test="filter == null">
            and u.id !=26
        </if>
        <if test="param.tenantId != null">
            and u.tenant_id = #{param.tenantId}
        </if>
        <if test="param.deptId != null">
            and u.dept_id = #{param.deptId}
        </if>
        <if test="param.status != null">
            and u.status = #{param.status}
        </if>
        <if test="param.createTime !=null  and param.createTime.length >0">
            and u.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>
        <if test=" param.userName != null ">
            and (u.mobile like CONCAT('%',#{param.username},'%')
            or u.username like CONCAT('%',#{param.username},'%')
            or u.nickname like CONCAT('%',#{param.username},'%'))
        </if>
        <if test="userId != null and userId != ''">
            and (u.creator = #{userId} or u.id = #{userId})
        </if>
        <if test="dataPermissionDepts != null and dataPermissionDepts.size > 0">
            and u.dept_id in
            <foreach collection="dataPermissionDepts" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="ids != null and ids.size > 0">
            and u.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="leaderIds != null and leaderIds.size > 0">
            and u.id in
            <foreach collection="leaderIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size != 0">
            order by DECODE(u.dept_id,<foreach collection="deptIds" item="id" index="index" open="" close="" separator=",">#{id},#{index}</foreach>),
            u.sort,
            CASE
            WHEN up.administrative_position_name IN ('主任', '院长', '主任（校长）', '馆长', '专职副书记') THEN 1
            WHEN up.administrative_position_name IN ('副主任', '副院长', '副主任（副校长）', '副馆长', '副书记、机关纪委书记', '副书记') THEN 2
            ELSE 99
            END ASC,
            up.administrative_position_rank asc
        </if>
    </select>
    <select id="selectUserPageManage" resultMap="selectUsersResultA">
        SELECT  u.*,
        up.administrative_position_name as administrative_position_name
        FROM system_users u
        left join (
        select
        pb.user_id,
        pp.administrative_position_name,
        pp.administrative_position_rank,
        pb.person_status
        from
        hr_personnel_basic_information pb
        inner join hr_personnel_position pp
        on
        pp.personnel_id = pb.id
        ) up on
        up.user_id = u.id
        left join
        system_dept d
        on
        u.dept_id = d.id
        WHERE u.DELETED  = 0 and u.id !=26
        and
        (d.type is null or d.type &lt; 100)
        <if test="param.personStatus != null">
            and up.person_status = #{param.personStatus}
        </if>
        <if test="param.tenantId != null">
            and u.tenant_id = #{param.tenantId}
        </if>
        <if test="param.deptId != null">
            and (u.dept_id = #{param.deptId}
            or u.id in (select user_id from system_user_dept
            where dept_id = #{param.deptId} and deleted != 1)
            )
        </if>
        <if test="param.status != null">
            and u.status = #{param.status}
        </if>
        <if test="param.createTime !=null  and param.createTime.length >0">
            and u.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>
        <if test=" param.userName != null ">
            and (u.mobile like CONCAT('%',#{param.username},'%')
            or u.username like CONCAT('%',#{param.username},'%')
            or u.nickname like CONCAT('%',#{param.username},'%'))
        </if>
        <if test="userId != null and userId != ''">
            and (u.creator = #{userId} or u.id = #{userId})
        </if>
        <if test="dataPermissionDepts != null and dataPermissionDepts.size > 0">
            and u.dept_id in
            <foreach collection="dataPermissionDepts" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size != 0">
            order by DECODE(u.dept_id,<foreach collection="deptIds" item="id" index="index" open="" close="" separator=",">#{id},#{index}</foreach>),
            u.sort,
            CASE
            WHEN up.administrative_position_name IN ('主任', '院长', '主任（校长）', '馆长', '专职副书记') THEN 1
            WHEN up.administrative_position_name IN ('副主任', '副院长', '副主任（副校长）', '副馆长', '副书记、机关纪委书记', '副书记') THEN 2
            ELSE 99
            END ASC,
            up.administrative_position_rank asc
        </if>
    </select>
    <select id="selectExportUser" resultMap="selectUsersResult">
        <include refid="userPageSql"></include>
    </select>

    <select id="selectUsersByDeptIds" resultType="com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO">
        select u.ID ,u.NICKNAME ,d.NAME as deptName
        from SYSTEM_USERS as u
                 left join SYSTEM_DEPT  as d on u.DEPT_ID = d.ID
        where u.DELETED = 0
        and u.STATUS = 0
        and dept_id in
        <foreach collection="deptIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test=" nickname != null ">
            and u.NICKNAME like CONCAT('%',#{nickname},'%')
        </if>
    </select>

    <select id="selectSimpleUsersByIds" resultType="com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO">
        select distinct u.id, u.USERNAME ,u.NICKNAME ,d.NAME as deptName,u.TENANT_ID,t.NAME  as tenantName,u.sort,u.dept_id
        from SYSTEM_USERS as u
        left join SYSTEM_DEPT  as d on u.DEPT_ID = d.ID
        left join SYSTEM_TENANT as t on u.TENANT_ID = t.ID
        left join SYSTEM_USER_ROLE as sur on sur.USER_ID = u.ID
        where u.DELETED = 0
        and u.STATUS = 0
        and sur.DELETED = 0
        and sur.ROLE_ID = #{param.roleId}
        <if test="tenantId != null">
            and u.TENANT_ID = #{tenantId}
        </if>
        <if test="deptIds != null and deptIds.size > 0">
            order by DECODE(u.dept_id,<foreach collection="deptIds" item="id" index="index" open="" close="" separator=",">#{id},#{index}</foreach>)
            ,u.sort asc,u.id desc
        </if>
    </select>
    <select id="selectHistoryUserById"
            resultType="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO">
        SELECT id,
               username,
               password,
               nickname,
               remark,
               dept_id,
               post_ids,
               email,
               mobile,
               sex,
               avatar,
               status,
               login_ip,
               login_date,
               is_real_name_authentication,
               password_update_time,
               sort,
               app_cid,
               wxxcx_openid,
               wx_unionid,
               tenant_id,
               create_time,
               update_time,
               creator,
               updater,
               deleted
        FROM system_users
        WHERE id= #{id}
    </select>

    <select id="selectByUnitUsername"
            resultType="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO">
        select
            su.id,
            su.username,
            su."password",
            su.nickname,
            su.remark,
            su.dept_id,
            su.post_ids,
            su.email,
            su.mobile,
            su.sex,
            su.avatar,
            su.status,
            su.login_ip,
            su.login_date,
            su.tenant_id,
            su.is_real_name_authentication,
            su.password_update_time,
            su.sort,
            su.app_cid,
            su.wxxcx_openid,
            su.wx_unionid,
            su.othersystemid,
            su.has_login,
            su.logindefualt,
            su.system_id,
            su.employee_id,
            su.init_password_is_change
        from
            system_users su
                join edu_sign_up_unit esuu on esuu.user_id = su.id and esuu.deleted = 0 and esuu.template = 1
        where
            su.deleted = 0
          and esuu.username = #{username}
        order by su.create_time desc
        limit 1
    </select>

    <select id="getDeptIdsByUserIds" resultType="java.lang.Long">
        SELECT dept_id FROM system_users
        <where>
            <foreach collection="ids" item="id" open="AND id IN (" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="selectSimpleUsersByGroupIds"
            resultType="com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO">
        select DISTINCT u.ID, u.USERNAME ,u.NICKNAME ,d.NAME as deptName,u.TENANT_ID,t.NAME  as tenantName, u.SORT, u.DEPT_ID
        from SYSTEM_USERS as u
        left join SYSTEM_DEPT  as d on u.DEPT_ID = d.ID
        left join SYSTEM_TENANT as t on u.TENANT_ID = t.ID
        left join SYSTEM_USER_ROLE_GROUP as sur on sur.USER_ID = u.ID
        where u.DELETED = 0
        and u.STATUS = 0
        and sur.DELETED = 0
        and sur.GROUP_ID = #{param.groupId}
        <if test="tenantId != null">
            and u.TENANT_ID = #{tenantId}
        </if>
        <if test="deptIds != null and deptIds.size > 0">
            order by DECODE(u.dept_id,<foreach collection="deptIds" item="id" index="index" open="" close="" separator=",">#{id},#{index}</foreach>)
            ,u.sort asc,u.id desc
        </if>
    </select>
    <select id="selectSimpleUsersByPostUsers"
            resultType="com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO">
        select DISTINCT u.ID, u.USERNAME ,u.NICKNAME ,d.NAME as deptName,u.TENANT_ID,t.NAME  as tenantName, u.SORT, u.DEPT_ID
        from SYSTEM_USERS as u
        left join SYSTEM_DEPT  as d on u.DEPT_ID = d.ID
        left join SYSTEM_TENANT as t on u.TENANT_ID = t.ID
        left join SYSTEM_USER_POST as sup on sup.USER_ID = u.ID
        where u.DELETED = 0
        and u.STATUS = 0
        and sup.DELETED = 0
        and sup.post_ID = #{param.postId}
        <if test="tenantId != null">
            and u.TENANT_ID = #{tenantId}
        </if>
        <if test="deptIds != null and deptIds.size > 0">
            order by DECODE(u.dept_id,<foreach collection="deptIds" item="id" index="index" open="" close="" separator=",">#{id},#{index}</foreach>)
            ,u.sort asc,u.id desc
        </if>
    </select>



    <select id="selectUserIdsByGroupIds" resultType="java.lang.Long" parameterType="java.lang.Long">
        select DISTINCT u.ID
        from SYSTEM_USERS as u
        left join SYSTEM_USER_ROLE_GROUP as sur on sur.USER_ID = u.ID
        where u.DELETED = 0
        and u.STATUS = 0
        and sur.DELETED = 0
        and sur.GROUP_ID = #{groupId}
    </select>
    <select id="getUserBySystemId" resultType="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO">
        select * from system_users where system_id = #{id} and deleted = 0 limit 1
    </select>
    <select id="getUserByEmployeeId"
            resultType="com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO">
        select * from system_users where employee_id = #{employeeId} and deleted = 0 limit 1
    </select>
    <select id="getErrorUserIdList" resultType="java.lang.Long">
        SELECT
            id
        FROM
            system_users su
        WHERE
            su.deleted = 0
          AND su.system_id LIKE'Unexpected%'
    </select>
</mapper>
