<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.oaNotice.OaNoticeMapper">

    <select id="selectPageByStatusList" resultType="com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO">
        select
        a.*,
        a.notice_type as type,
        a.notice_status as status,
        a.file as fileUrl,
        a.receive_dept_id as recipientId,
        b.nickname as creator,
        c.name as deptName
        FROM oa_notice a
        left join
            system_users b
        on
            a.creator_id=b.id
        left join
            system_dept c
        on
            b.dept_id=c.id
        WHERE a.deleted = 0
          and a.notice_status=#{status}
        <if test="userId != null">
          and a.creator_id=#{userId}
        </if>
        <if test="param.removed != null">
            and a.removed = #{param.removed}
        </if>
        <if test="param.type != null">
            and a.notice_type = #{param.type}
        </if>

        <if test=" param.title != null ">
            and (a.title like CONCAT('%',#{param.title},'%')
            <if test="userIds.size()!=0">
                or
                a.creator_id in
                <foreach item="item" collection="userIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>

        <if test="param.startTime != null">
            and a.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and a.create_time &lt;= #{param.endTime}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>


        order by a.is_top_notice desc,a.top_time desc,a.create_time desc
        limit (#{param.pageNo}-1)*#{param.pageSize},#{param.pageSize}
    </select>

    <select id="selectRespById" resultType="com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO">
    select
    a.id,
    a.title,
    a.content,
    a.notice_type as type,
    a.notice_status as status,
    a.create_time,
    a.updater,
    a.update_time,
    a.deleted,
    a.file as file_url,
    a.file_authority,
    a.creator_id,
    a.receive_dept_id as recipient_id,
    a.updater_id,
    a.removed,
    a.recipient_user_id,
    a.read_number,
    a.is_top_notice,
    a.top_time,
    a.is_recruit,
    b.nickname as creator,
    c.name as dept_name
    FROM oa_notice a
    left join
    system_users b
    on
        a.creator_id=b.id
    left join
        system_dept c
    on
    b.dept_id=c.id
    where a.id=#{id}
    </select>

    <select id="selectPageByStatusNum" resultType="Long">
        select
        count(0)
        FROM oa_notice a
        left join
        system_users b
        on
        a.creator_id=b.id
        WHERE a.deleted = 0
        and a.notice_status=#{status}
        <if test="userId != null">
            and a.creator_id=#{userId}
        </if>
        <if test="param.removed != null">
            and a.removed = #{param.removed}
        </if>
        <if test="param.type != null">
            and a.notice_type = #{param.type}
        </if>

        <if test=" param.title != null ">
            and (a.title like CONCAT('%',#{param.title},'%')
            <if test="userIds.size()!=0">
                or
                a.creator_id in
                <foreach item="item" collection="userIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>

        <if test="param.startTime != null">
            and a.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and a.create_time &lt;= #{param.endTime}
        </if>


        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        order by a.create_time desc
    </select>

    <select id="selectPageByStatusUserIdList" resultType="com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO">
        select
        a.*,
        a.notice_type as type,
        a.notice_status as status,
        a.file as fileUrl,
        a.receive_dept_id as recipientId,
        b.nickname as creator,
        c.name as deptName
        FROM oa_notice a
        left join
        system_users b
        on
        a.creator_id=b.id
        left join
        system_dept c
        on
        b.dept_id=c.id
        WHERE a.deleted = 0
        and a.notice_status=#{status}
        <if test="userId != null">
            and a.creator_id=#{userId}
        </if>
        <if test="param.removed != null">
            and a.removed = #{param.removed}
        </if>
        <if test="param.type != null">
            and a.notice_type = #{param.type}
        </if>

        <if test=" param.title != null ">
            and (a.title like CONCAT('%',#{param.title},'%')
            )
        </if>

        <if test="param.startTime != null">
            and a.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and a.create_time &lt;= #{param.endTime}
        </if>


        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>


        order by a.is_top_notice desc,a.top_time desc,a.create_time desc
        limit (#{param.pageNo}-1)*#{param.pageSize},#{param.pageSize}
    </select>

    <select id="selectPageByStatusUserIdNum" resultType="Long">
        select
        count(0)
        FROM oa_notice a
        left join
        system_users b
        on
        a.creator_id=b.id
        WHERE a.deleted = 0
        and a.notice_status=#{status}
        <if test="userId != null">
            and a.creator_id=#{userId}
        </if>
        <if test="param.removed != null">
            and a.removed = #{param.removed}
        </if>
        <if test="param.type != null">
            and a.notice_type = #{param.type}
        </if>

        <if test=" param.title != null ">
            and (a.title like CONCAT('%',#{param.title},'%')
            )
        </if>

        <if test="param.startTime != null">
            and a.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and a.create_time &lt;= #{param.endTime}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>
        order by a.create_time desc
    </select>

    <select id="selectNoticePage" resultType="com.unicom.swdx.module.system.dal.dataobject.oaNotice.OaNoticeDO">
        select
            a.id,
            a.title,
            a.notice_type as type,
            a.notice_status as status,
            a.content,
            a.creator_id,
            a.create_time
        FROM oa_notice a
        WHERE a.deleted = 0
        <if test="param.status != null">
            and a.notice_status = #{param.status}
        </if>
        <if test="param.type != null">
            and a.notice_type = #{param.type}
        </if>
        <if test=" param.title != null ">
            and (a.title like CONCAT('%',#{param.title},'%') or a.creator like CONCAT('%',#{param.title},'%'))
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        order by a.id desc
    </select>

    <select id="selectPersonalNotice" resultType="com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO">
        select c.*,
        c.notice_type as type,
        c.notice_status as status,
        c.file as fileUrl,
        c.receive_dept_id as recipientId,
        cd.nickname as creator,
        rd.read as ru,
        sd.name as deptName,
        case
        when ru = '1' then 1
        else
        0
        end
        as read
        from
        (select d.* from oa_notice d
        where d.id in
        (select distinct aa.id as aid from
        (select * FROM oa_notice a
        WHERE a.deleted = 0
        and a.notice_type=2
        and a.notice_status=1
        and a.removed=0
        <if test="param.title != null">
            and a.title like concat('%',#{param.title},'%')
        </if>

        <if test="param.createTime !=null  and param.createTime.length >0">
            and a.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        <if test="param.deptId != null">
            union
            select *
            from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.receive_dept_id like concat('%|',#{param.deptId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>

        <if test="param.userId != null">
             union
             select * from oa_notice b
             where b.deleted=0
               and b.notice_type=1
               and b.notice_status=1
               and b.removed=0
               and b.recipient_user_id like concat('%|',#{param.userId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>
        )
        as aa)
             )
            as c
            left join
            system_users cd
                on
            c.creator_id=cd.id
                and
                cd.deleted=0
            <if test="param.creator != null">
                inner join
                system_users ccd
                on
                c.creator_id=ccd.id
                and
                ccd.deleted=0
                and ccd.nickname like concat('%', #{param.creator},'%')
            </if>
            left join
            notice_read_record rd
            on
            c.id=rd.notice_id
                and
                rd.user_id=#{param.userId}
                and
                rd.deleted=0
            left join
                system_dept sd
            on
                cd.dept_id=sd.id
                and
                sd.deleted=0
        order by c.is_top_notice desc,c.top_time desc,c.create_time desc
        limit (#{param.pageNo}-1)*#{param.pageSize},#{param.pageSize}
    </select>

    <select id="selectPersonalNoticeNum" resultType="Long">
        select count(0) from
            (select * FROM oa_notice a
             WHERE a.deleted = 0
               and a.notice_type=2
               and a.notice_status=1
               and a.removed=0
            <if test="param.title != null">
            and a.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
            and a.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        <if test="param.deptId != null">
             union
             select * from oa_notice b
             where b.deleted=0
               and b.notice_type=1
               and b.notice_status=1
               and b.removed=0
               and b.receive_dept_id like concat('%|',#{param.deptId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>

        </if>

        <if test="param.userId != null">
             union
             select * from oa_notice b
             where b.deleted=0
               and b.notice_type=1
               and b.notice_status=1
               and b.removed=0
               and b.recipient_user_id like concat('%|',#{param.userId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>
             )
                as c
        <if test="param.creator != null">
            inner join
            system_users cd
            on
            c.creator_id=cd.id
            and
            cd.deleted=0
            and cd.nickname  like concat('%', #{param.creator},'%')
        </if>
    </select>

    <update id="updateReadNum" >
        update oa_notice set read_number = read_number+1 where id=#{id}
    </update>

    <update id="emptyReadNum" >
        update oa_notice set read_number = 0 where id=#{id}
    </update>

    <select id="selectPersonalNoticeRead" resultType="com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO">
        select c.*,
        c.notice_type as type,
        c.notice_status as status,
        c.file as fileUrl,
        c.receive_dept_id as recipientId,
        cd.nickname as creator,
        rd.read as ru,
        sd.name as deptName,
        case
        when ru = '1' then 1
        else
        0
        end
        as read
        from
        (select d.* from oa_notice d
        where d.id in
        (select distinct aa.id as aid from
        (select * FROM oa_notice a
        WHERE a.deleted = 0
        and a.notice_type=2
        and a.notice_status=1
        and a.removed=0
        <if test="param.title != null">
            and a.title like concat('%',#{param.title},'%')
        </if>

        <if test="param.createTime !=null  and param.createTime.length >0">
            and a.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        <if test="param.deptId != null">
            union
            select *
            from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.receive_dept_id like concat('%|',#{param.deptId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>



            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>

        <if test="param.userId != null">
            union
            select * from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.recipient_user_id like concat('%|',#{param.userId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>



            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>
        )
        as aa)
        )
        as c
        left join
        system_users cd
        on
        c.creator_id=cd.id
        and
        cd.deleted=0
        <if test="param.creator != null">
            inner join
            system_users ccd
            on
            c.creator_id=ccd.id
            and
            ccd.deleted=0
            and ccd.nickname like concat('%', #{param.creator},'%')
        </if>
        inner join
        notice_read_record rd
        on
        c.id=rd.notice_id
        and
        rd.user_id=#{param.userId}
        and
        rd.deleted=0
        left join
        system_dept sd
        on
        cd.dept_id=sd.id
        and
        sd.deleted=0
        order by  c.is_top_notice desc,c.top_time desc,c.create_time desc
        limit (#{param.pageNo}-1)*#{param.pageSize},#{param.pageSize}
    </select>

    <select id="selectPersonalNoticeNumRead" resultType="Long">
        select count(0) from
        (select * FROM oa_notice a
        WHERE a.deleted = 0
        and a.notice_type=2
        and a.notice_status=1
        and a.removed=0
        <if test="param.title != null">
            and a.title like concat('%',#{param.title},'%')
        </if>

        <if test="param.createTime !=null  and param.createTime.length >0">
            and a.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        <if test="param.deptId != null">
            union
            select * from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.receive_dept_id like concat('%|',#{param.deptId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>


            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>

        <if test="param.userId != null">
            union
            select * from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.recipient_user_id like concat('%|',#{param.userId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>
        )
        as c
        <if test="param.creator != null">
            inner join
            system_users cd
            on
            c.creator_id=cd.id
            and
            cd.deleted=0
            and cd.nickname  like concat('%', #{param.creator},'%')
        </if>
        inner join
        notice_read_record rd
            on
            c.id=rd.notice_id
            and
            rd.deleted=0
            and
            rd.user_id=#{param.userId}
    </select>


    <select id="selectPersonalNoticeNotRead" resultType="com.unicom.swdx.module.system.controller.admin.oaNotice.vo.OaNoticeRespVO">
        select c.*,
        c.notice_type as type,
        c.notice_status as status,
        c.file as fileUrl,
        c.receive_dept_id as recipientId,
        cd.nickname as creator,
        sd.name as deptName,
        0
        as read
        from
        (select d.* from oa_notice d
        where d.id in
        (select distinct aa.id as aid from
        (select * FROM oa_notice a
        WHERE a.deleted = 0
        and a.notice_type=2
        and a.notice_status=1
        and a.removed=0
        <if test="param.title != null">
            and a.title like concat('%',#{param.title},'%')
        </if>

        <if test="param.createTime !=null  and param.createTime.length >0">
            and a.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        <if test="param.deptId != null">
            union
            select *
            from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.receive_dept_id like concat('%|',#{param.deptId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>

        <if test="param.userId != null">
            union
            select * from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.recipient_user_id like concat('%|',#{param.userId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>
        )
        as aa)
        )
        as c
        left join
        system_users cd
        on
        c.creator_id=cd.id
        and
        cd.deleted=0
        <if test="param.creator != null">
            inner join
            system_users ccd
            on
            c.creator_id=ccd.id
            and
            ccd.deleted=0
            and ccd.nickname like concat('%', #{param.creator},'%')
        </if>
        left join
        system_dept sd
        on
        cd.dept_id=sd.id
        and
        sd.deleted=0

        where
        c.id not in
        (
        select notice_id from
        notice_read_record rd
        where
        rd.user_id=#{param.userId}
        and
        rd.deleted=0)
        order by c.is_top_notice desc,c.top_time desc,c.create_time desc
        limit (#{param.pageNo}-1)*#{param.pageSize},#{param.pageSize}
    </select>

    <select id="selectPersonalNoticeNumNotRead" resultType="Long">
        select count(0) from
        (select * FROM oa_notice a
        WHERE a.deleted = 0
        and a.notice_type=2
        and a.notice_status=1
        and a.removed=0
        <if test="param.title != null">
            and a.title like concat('%',#{param.title},'%')
        </if>

        <if test="param.createTime !=null  and param.createTime.length >0">
            and a.create_time between #{param.createTime[0]} and #{param.createTime[1]}
        </if>

        <if test="tenantid != null">
            and a.tenant_id =  #{tenantid}
        </if>

        <if test="param.deptId != null">
            union
            select * from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.receive_dept_id like concat('%|',#{param.deptId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>

        <if test="param.userId != null">
            union
            select * from oa_notice b
            where b.deleted=0
            and b.notice_type=1
            and b.notice_status=1
            and b.removed=0
            and b.recipient_user_id like concat('%|',#{param.userId},'|%')
            <if test="param.title != null">
                and b.title like concat('%',#{param.title},'%')
            </if>

            <if test="param.createTime !=null  and param.createTime.length >0">
                and b.create_time between #{param.createTime[0]} and #{param.createTime[1]}
            </if>

            <if test="tenantid != null">
                and b.tenant_id =  #{tenantid}
            </if>
        </if>
        )
        as c
        <if test="param.creator != null">
            inner join
            system_users cd
            on
            c.creator_id=cd.id
            and
            cd.deleted=0
            and cd.nickname  like concat('%', #{param.creator},'%')
        </if>
        where
        c.id not in
        (
        select notice_id from
        notice_read_record rd
        where
        rd.user_id=#{param.userId}
        and
        rd.deleted=0)
    </select>

</mapper>
