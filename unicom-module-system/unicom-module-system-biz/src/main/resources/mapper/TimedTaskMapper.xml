<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.message.TimedTaskMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id = "updateStatus" >
        UPDATE system_timed_task SET status = 1 WHERE id = #{id};
    </select>
    <select id = "updateSuccess">
        UPDATE system_timed_task SET Success = 1 WHERE id = #{id} and type =1;
    </select>

    <resultMap id="TimedTaskDO" type="com.unicom.swdx.module.system.dal.dataobject.message.TimedTaskDO">
        <result column="phone" property="phone" typeHandler="com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler"/>
    </resultMap>
    <select id = "selectByStatus" resultMap="TimedTaskDO">
        SELECT *
            from system_timed_task where status = 0 and deleted = false;
    </select>
    <select id = "count" resultType="Integer">
        select count(success)
        from system_timed_task where success = true WHERE type = #{type};
    </select>
</mapper>
