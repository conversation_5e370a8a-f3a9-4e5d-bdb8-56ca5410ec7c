<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.permission.RoleGroupBindMapper">


    <select id="selectRoleId" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT
        role_id
        FROM
        system_role_group_bind
        WHERE
        group_id = #{id}
        and deleted = false
    </select>
</mapper>
