<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.tenant.TenantApprovalMapper">

    <resultMap id="selectChangePageResult" autoMapping="true" type="com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantChangePageRespVO">
        <result column="location_region" property="locationRegion" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="register_region" property="registerRegion" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="inst_user_type" property="instUserType" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="level_range" property="levelRange" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="legal_representative_ID_card" property="legalRepresentativeIdCard" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>

    <resultMap id="selectApprovalStatusPageResult" autoMapping="true" type="com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantApprovalStatusRespVO">
        <result column="location_region" property="locationRegion" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="register_region" property="registerRegion" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="inst_user_type" property="instUserType" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="level_range" property="levelRange" typeHandler="com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler"/>
        <result column="legal_representative_ID_card" property="legalRepresentativeIdCard" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>

    <select id="selectChangePage" resultMap="selectChangePageResult">
        select sta.id,sta.linked_tenant_id,sta.name as tenantName,sta.approval_type,sta.change_content,su1.nickname as applicant,
               sta.approval_time,su2.nickname as approver,sta.examine_time,sta.approval_status,
               sta.company_type,sta.unified_social_credit_code,sta.register_region,sta.register_address,
               sta.location_region,sta.location_address,sta.legal_representative_name,
               sta.legal_representative_ID_card,sta.inst_user_type,sta.business_license_url,
               sta.contact_nickname,sta.contact_mobile,sta.contact_name,
               sta.remark,sta.expire_time,
                sta.tenant_type,
                sta.tenant_level,
                sta.level_range,
                sta.guide_tenant_id
        from system_tenant_approval as sta
                 left join system_users as su1 on applicant_id = su1.id
                 left join system_users as su2 on approver_id = su2.id
        where sta.linked_tenant_id = #{param}
          and sta.deleted = 0
        order by sta.approval_time desc
    </select>

    <select id="selectTenantInfo" resultMap="selectApprovalStatusPageResult">
        select
               st.name as tenantName,st.company_type,st.unified_social_credit_code,st.register_region,
               st.register_address,st.location_region,st.location_address,st.legal_representative_name,
               st.legal_representative_ID_card,st.inst_user_type,st.business_license_url,
               st.id as linkedTenantId,st.expire_time,
               st.tenant_type,
               st.tenant_level,
               st.level_range,
               st.guide_tenant_id
        from
            system_tenant st
        where st.id = #{param}
    </select>


    <select id="selectApprovalStatus" resultMap="selectApprovalStatusPageResult">
        select sta.approval_type as lastApprovalType, sta.approval_status as lastApprovalStatus,
               sta.remark as lastApprovalRemark
        from
             system_tenant_approval sta
        where sta.linked_tenant_id = #{param}
          and sta.approval_time = (select MAX(sta2.approval_time) from system_tenant_approval sta2
            where sta2.linked_tenant_id = #{param})
    </select>

    <select id="selectApprovalPage" resultMap="selectChangePageResult">
        select sta.id,sta.linked_tenant_id,sta.name as tenantName,sta.approval_type,sta.change_content,su1.nickname as applicant,
               sta.approval_time,su2.nickname as approver,sta.examine_time,sta.approval_status,
               sta.company_type,sta.unified_social_credit_code,sta.register_region,sta.register_address,
               sta.location_region,sta.location_address,sta.legal_representative_name,
               sta.legal_representative_ID_card,sta.inst_user_type,sta.business_license_url,
               sta.contact_nickname,sta.contact_mobile,sta.contact_name,
               sta.remark,sta.expire_time,
                sta.tenant_type,
                sta.tenant_level,
                sta.level_range,
                sta.guide_tenant_id
        from system_tenant_approval as sta
                 left join system_users as su1 on applicant_id = su1.id
                 left join system_users as su2 on approver_id = su2.id
        <where>
            sta.deleted = 0
            <if test="param.tenantName != null and param.tenantName != ''">
                and sta.name like concat('%',#{param.tenantName},'%')
            </if>
            <if test="param.unifiedSocialCreditCode != null and param.unifiedSocialCreditCode != ''">
                and sta.unified_social_credit_code = #{param.unifiedSocialCreditCode}
            </if>
            <if test="param.legalRepresentativeName != null and param.legalRepresentativeName != ''">
                and sta.legal_representative_name like concat('%',#{param.legalRepresentativeName},'%')
            </if>
            <if test="param.companyType != null">
                and sta.company_type = #{param.companyType}
            </if>
            <if test="param.approvalType != null">
                and sta.approval_type = #{param.approvalType}
            </if>
            <if test="param.approvalStatus != null">
                and sta.approval_status = #{param.approvalStatus}
            </if>
            <if test="param.tenantType != null">
                and sta.tenant_type = #{param.tenantType}
            </if>
        </where>
        order by sta.approval_time desc
    </select>



</mapper>
