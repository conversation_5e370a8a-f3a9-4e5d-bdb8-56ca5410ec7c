<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.system.dal.mysql.logger.LoginLogMapper">

    <select id="selectLoginLogPage" resultType="com.unicom.swdx.module.system.dal.dataobject.logger.LoginLogDO">
        select
            a.id,
            a.log_type,
            a.username,
            a.user_ip,
            a.user_agent,
            a.result,
            a.create_time
        FROM system_login_log a
        WHERE a.deleted = 0
        <if test=" param.userIp != null ">
            and (a.user_ip like CONCAT('%',#{param.userIp},'%') or a.username like CONCAT('%',#{param.userIp},'%'))
        </if>
        and a.tenant_id = #{tenantId}
        order by a.id desc
    </select>


</mapper>
