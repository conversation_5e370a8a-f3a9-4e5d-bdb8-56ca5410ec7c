package com.unicom.swdx.module.oa.service.receive;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceivePageReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class ReceiveServiceImplTest {

    @Mock
    private BpmProcessInstanceApi mockBpmProcessInstanceApi;

    @InjectMocks
    private ReceiveServiceImpl receiveServiceImplUnderTest;

    @Test
    void testPage() {
        // Setup
        final ReceivePageReqVO reqVO = new ReceivePageReqVO();
        reqVO.setCategory("category");
        reqVO.setApplyDate(new LocalDate[]{LocalDate.of(2020, 1, 1)});
        reqVO.setIsRead(false);

        final PageResult<ReceiveDO> expectedResult = new PageResult<>(Arrays.asList(ReceiveDO.builder()
                .id(0L)
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .isRead(false)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .build()), 0L);

        // Run the test
        final PageResult<ReceiveDO> result = receiveServiceImplUnderTest.page(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
