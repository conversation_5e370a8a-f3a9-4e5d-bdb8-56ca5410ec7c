package com.unicom.swdx.module.oa.service.outReport;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OADealOutReportVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.dal.kingbase.OutReportMapper;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OutReportServiceImplTest {

    @Mock
    private OutReportMapper mockOutReportMapper;
    @Mock
    private DraftService mockDraftService;
    @Mock
    private OATaskService mockOaTaskService;
    @Mock
    private BpmProcessInstanceApi mockProcessInstanceApi;
    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private PostApi mockPostApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private BpmTaskServiceApi mockTaskServiceApi;
    @Mock
    private SmsSendApi mockSmsSendApi;
    @Mock
    private LectureMapper mockLectureMapper;
    @Mock
    private ScheduleServiceApi mockScheduleServiceApi;
    @Mock
    private ReceiveApi mockReceiveApi;

    @InjectMocks
    private OutReportServiceImpl outReportServiceImplUnderTest;

    @Test
    void testInit() {
        // Setup
        // Run the test
        outReportServiceImplUnderTest.init();

        // Verify the results
    }

    @Test
    void testSaveDraft() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final Long result = outReportServiceImplUnderTest.saveDraft(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
        verify(mockDraftService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    void testSaveDraft_AdminUserApiReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final Long result = outReportServiceImplUnderTest.saveDraft(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
    }

    @Test
    void testGetPostType() {
        // Setup
        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Run the test
        final Integer result = outReportServiceImplUnderTest.getPostType(0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testCreateOutReportProcess() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-OutReport");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));
        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = outReportServiceImplUnderTest.createOutReportProcess(reqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testCreateOutReportProcess_AdminUserApiReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-OutReport");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));
        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = outReportServiceImplUnderTest.createOutReportProcess(reqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testCreateOutReportProcess_BpmProcessInstanceApiCreateProcessInstanceReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-OutReport");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));
        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = outReportServiceImplUnderTest.createOutReportProcess(reqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testCreateOutReportProcess_BpmProcessInstanceApiSkipFirstTaskReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-OutReport");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = outReportServiceImplUnderTest.createOutReportProcess(reqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testCreateOutReportProcess_DeptApiReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-OutReport");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = outReportServiceImplUnderTest.createOutReportProcess(reqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testCreateOutReportProcess_BpmTaskServiceApiGetTaskListByProcessInstanceIdReturnsNoItems() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-OutReport");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId"))
                .thenReturn(Collections.emptyList());
        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = outReportServiceImplUnderTest.createOutReportProcess(reqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testCheckTime() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure OutReportMapper.selectListByUserId(...).
        final List<OutReportDO> outReportDOS = Arrays.asList(OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockOutReportMapper.selectListByUserId(0L)).thenReturn(outReportDOS);

        // Run the test
        final boolean result = outReportServiceImplUnderTest.checkTime(reqVO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckTime_OutReportMapperReturnsNoItems() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        when(mockOutReportMapper.selectListByUserId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = outReportServiceImplUnderTest.checkTime(reqVO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testCheckTime2() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Run the test
        final boolean result = outReportServiceImplUnderTest.checkTime2(reqVO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testGetByProcessInstanceId() {
        // Setup
        final OutReportDO expectedResult = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        final OutReportDO result = outReportServiceImplUnderTest.getByProcessInstanceId("processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGet() {
        // Setup
        final OutReportDO expectedResult = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();

        // Run the test
        final OutReportDO result = outReportServiceImplUnderTest.get(0L, "processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testRestartOutReport() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        when(mockBpmTaskServiceApi.restartProcess(bpmRestartDTO)).thenReturn(CommonResult.success(false));

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        // Run the test
        outReportServiceImplUnderTest.restartOutReport(0L, reqVO);

        // Verify the results
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testRestartOutReport_AdminUserApiReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        when(mockBpmTaskServiceApi.restartProcess(bpmRestartDTO)).thenReturn(CommonResult.success(false));

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        // Run the test
        outReportServiceImplUnderTest.restartOutReport(0L, reqVO);

        // Verify the results
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testRestartOutReport_BpmTaskServiceApiRestartProcessReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        when(mockBpmTaskServiceApi.restartProcess(bpmRestartDTO))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        // Run the test
        outReportServiceImplUnderTest.restartOutReport(0L, reqVO);

        // Verify the results
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testRestartOutReport_DeptApiReturnsError() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        when(mockBpmTaskServiceApi.restartProcess(bpmRestartDTO)).thenReturn(CommonResult.success(false));

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        // Run the test
        outReportServiceImplUnderTest.restartOutReport(0L, reqVO);

        // Verify the results
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testRestartOutReport_BpmTaskServiceApiGetTaskListByProcessInstanceIdReturnsNoItems() {
        // Setup
        final OutReportCreateReqVO reqVO = new OutReportCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        when(mockBpmTaskServiceApi.restartProcess(bpmRestartDTO)).thenReturn(CommonResult.success(false));

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId"))
                .thenReturn(Collections.emptyList());

        // Run the test
        outReportServiceImplUnderTest.restartOutReport(0L, reqVO);

        // Verify the results
        verify(mockDraftService).deleteByItemId("12", 0L);

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }

    @Test
    void testGetResp() {
        // Setup
        final OutReportRespVO expectedResult = new OutReportRespVO();
        expectedResult.setStartDate(LocalDate.of(2020, 1, 1));
        expectedResult.setEndDate(LocalDate.of(2020, 1, 1));
        expectedResult.setIsDraft(false);
        expectedResult.setDeptId(0L);
        expectedResult.setUserNickName("nickname");
        expectedResult.setDeptName("deptName");
        expectedResult.setResult(0);
        expectedResult.setTaskName("taskName");
        expectedResult.setOperateType(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockTaskServiceApi.getNeededTaskInfo("12", 0L, "processInstanceId", false, false))
                .thenReturn(new HashMap<>());

        // Run the test
        final OutReportRespVO result = outReportServiceImplUnderTest.getResp(0L, "processInstanceId", false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetResp_AdminUserApiReturnsError() {
        // Setup
        final OutReportRespVO expectedResult = new OutReportRespVO();
        expectedResult.setStartDate(LocalDate.of(2020, 1, 1));
        expectedResult.setEndDate(LocalDate.of(2020, 1, 1));
        expectedResult.setIsDraft(false);
        expectedResult.setDeptId(0L);
        expectedResult.setUserNickName("nickname");
        expectedResult.setDeptName("deptName");
        expectedResult.setResult(0);
        expectedResult.setTaskName("taskName");
        expectedResult.setOperateType(0);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockTaskServiceApi.getNeededTaskInfo("12", 0L, "processInstanceId", false, false))
                .thenReturn(new HashMap<>());

        // Run the test
        final OutReportRespVO result = outReportServiceImplUnderTest.getResp(0L, "processInstanceId", false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetResp_DeptApiReturnsError() {
        // Setup
        final OutReportRespVO expectedResult = new OutReportRespVO();
        expectedResult.setStartDate(LocalDate.of(2020, 1, 1));
        expectedResult.setEndDate(LocalDate.of(2020, 1, 1));
        expectedResult.setIsDraft(false);
        expectedResult.setDeptId(0L);
        expectedResult.setUserNickName("nickname");
        expectedResult.setDeptName("deptName");
        expectedResult.setResult(0);
        expectedResult.setTaskName("taskName");
        expectedResult.setOperateType(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockTaskServiceApi.getNeededTaskInfo("12", 0L, "processInstanceId", false, false))
                .thenReturn(new HashMap<>());

        // Run the test
        final OutReportRespVO result = outReportServiceImplUnderTest.getResp(0L, "processInstanceId", false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testUpdateResultById() {
        // Setup
        // Run the test
        final boolean result = outReportServiceImplUnderTest.updateResultById(0L, 0);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockOutReportMapper).updateById(OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
    }

    @Test
    void testGetDateById() {
        // Setup
        final Map<String, LocalDate> expectedResult = new HashMap<>();

        // Configure OutReportMapper.selectById(...).
        final OutReportDO outReportDO = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockOutReportMapper.selectById(0L)).thenReturn(outReportDO);

        // Run the test
        final Map<String, LocalDate> result = outReportServiceImplUnderTest.getDateById(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDeleteById() {
        // Setup
        when(mockOutReportMapper.deleteById(0L)).thenReturn(0);

        // Run the test
        final int result = outReportServiceImplUnderTest.deleteById(0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testDealOutReport() {
        // Setup
        final OADealOutReportVO dealOutReportVO = new OADealOutReportVO();
        dealOutReportVO.setId(0L);
        dealOutReportVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dealOutReportVO.setHandSignature("sign");

        // Configure OutReportMapper.selectById(...).
        final OutReportDO outReportDO = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockOutReportMapper.selectById(0L)).thenReturn(outReportDO);

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setId("id");
        bpmProcessInstanceRespDTO.setName("name");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        outReportServiceImplUnderTest.dealOutReport(dealOutReportVO);

        // Verify the results
        verify(mockLectureMapper).insertImage(0L, "sign");

        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("人事部销单");
        bpmTaskExtDTO.setTaskId("hr-deal-outReport");
        bpmTaskExtDTO.setTaskDefKey("hr-deal-outReport");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processInstanceId");
        bpmTaskExtDTO.setProcessDefinitionId("processInstanceId");
        bpmTaskExtDTO.setParamsMap(new HashMap<>());
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        bpmTaskExtDTO.setImageUrl("sign");
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);

        // Confirm ScheduleServiceApi.deleteScheduleOther(...).
        final ScheduleDto scheduleDto = new ScheduleDto();
        scheduleDto.setUserId(0L);
        scheduleDto.setTitle("title");
        scheduleDto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        scheduleDto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        scheduleDto.setProcessInstanceId("processInstanceId");
        verify(mockScheduleServiceApi).deleteScheduleOther(scheduleDto);
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());

        // Confirm ReceiveApi.saveLeave(...).
        final ReceiveDTO receiveDTO = new ReceiveDTO();
        receiveDTO.setPromoterUserId(0L);
        receiveDTO.setUserIds(Arrays.asList(0L));
        receiveDTO.setCategory("category");
        receiveDTO.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        receiveDTO.setProcessInstanceId("processInstanceId");
        verify(mockReceiveApi).saveLeave(receiveDTO);
    }

    @Test
    void testDealOutReport_BpmProcessInstanceApiReturnsError() {
        // Setup
        final OADealOutReportVO dealOutReportVO = new OADealOutReportVO();
        dealOutReportVO.setId(0L);
        dealOutReportVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dealOutReportVO.setHandSignature("sign");

        // Configure OutReportMapper.selectById(...).
        final OutReportDO outReportDO = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockOutReportMapper.selectById(0L)).thenReturn(outReportDO);

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        outReportServiceImplUnderTest.dealOutReport(dealOutReportVO);

        // Verify the results
        verify(mockLectureMapper).insertImage(0L, "sign");

        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("人事部销单");
        bpmTaskExtDTO.setTaskId("hr-deal-outReport");
        bpmTaskExtDTO.setTaskDefKey("hr-deal-outReport");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processInstanceId");
        bpmTaskExtDTO.setProcessDefinitionId("processInstanceId");
        bpmTaskExtDTO.setParamsMap(new HashMap<>());
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        bpmTaskExtDTO.setImageUrl("sign");
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);

        // Confirm ScheduleServiceApi.deleteScheduleOther(...).
        final ScheduleDto scheduleDto = new ScheduleDto();
        scheduleDto.setUserId(0L);
        scheduleDto.setTitle("title");
        scheduleDto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        scheduleDto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        scheduleDto.setProcessInstanceId("processInstanceId");
        verify(mockScheduleServiceApi).deleteScheduleOther(scheduleDto);
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());

        // Confirm ReceiveApi.saveLeave(...).
        final ReceiveDTO receiveDTO = new ReceiveDTO();
        receiveDTO.setPromoterUserId(0L);
        receiveDTO.setUserIds(Arrays.asList(0L));
        receiveDTO.setCategory("category");
        receiveDTO.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        receiveDTO.setProcessInstanceId("processInstanceId");
        verify(mockReceiveApi).saveLeave(receiveDTO);
    }

    @Test
    void testDealOutReport_AdminUserApiReturnsError() {
        // Setup
        final OADealOutReportVO dealOutReportVO = new OADealOutReportVO();
        dealOutReportVO.setId(0L);
        dealOutReportVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dealOutReportVO.setHandSignature("sign");

        // Configure OutReportMapper.selectById(...).
        final OutReportDO outReportDO = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockOutReportMapper.selectById(0L)).thenReturn(outReportDO);

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setId("id");
        bpmProcessInstanceRespDTO.setName("name");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        outReportServiceImplUnderTest.dealOutReport(dealOutReportVO);

        // Verify the results
        verify(mockLectureMapper).insertImage(0L, "sign");

        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("人事部销单");
        bpmTaskExtDTO.setTaskId("hr-deal-outReport");
        bpmTaskExtDTO.setTaskDefKey("hr-deal-outReport");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processInstanceId");
        bpmTaskExtDTO.setProcessDefinitionId("processInstanceId");
        bpmTaskExtDTO.setParamsMap(new HashMap<>());
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        bpmTaskExtDTO.setImageUrl("sign");
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);

        // Confirm ScheduleServiceApi.deleteScheduleOther(...).
        final ScheduleDto scheduleDto = new ScheduleDto();
        scheduleDto.setUserId(0L);
        scheduleDto.setTitle("title");
        scheduleDto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        scheduleDto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        scheduleDto.setProcessInstanceId("processInstanceId");
        verify(mockScheduleServiceApi).deleteScheduleOther(scheduleDto);
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());

        // Confirm ReceiveApi.saveLeave(...).
        final ReceiveDTO receiveDTO = new ReceiveDTO();
        receiveDTO.setPromoterUserId(0L);
        receiveDTO.setUserIds(Arrays.asList(0L));
        receiveDTO.setCategory("category");
        receiveDTO.setApplyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        receiveDTO.setProcessInstanceId("processInstanceId");
        verify(mockReceiveApi).saveLeave(receiveDTO);
    }

    @Test
    void testDealOutReportByProcessId() {
        // Setup
        // Configure OutReportMapper.selectByProcessId(...).
        final OutReportDO outReportDO = OutReportDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .userId(0L)
                .deptId(0L)
                .startDate(LocalDate.of(2020, 1, 1))
                .endDate(LocalDate.of(2020, 1, 1))
                .duration(0)
                .sign("sign")
                .isDraft(false)
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .result(0)
                .isDealt(false)
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
        when(mockOutReportMapper.selectByProcessId("processId")).thenReturn(outReportDO);

        // Configure PostApi.getMinSortPostByUser(...).
        final PostRespDTO postRespDTO = new PostRespDTO();
        postRespDTO.setId(0L);
        postRespDTO.setTenant_id(0L);
        postRespDTO.setName("name");
        postRespDTO.setCode("3");
        postRespDTO.setSort(0);
        when(mockPostApi.getMinSortPostByUser(0L, Arrays.asList("value"))).thenReturn(postRespDTO);

        // Run the test
        outReportServiceImplUnderTest.dealOutReportByProcessId("processId");

        // Verify the results
        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("人事部销单");
        bpmTaskExtDTO.setTaskId("hr-deal-outReport");
        bpmTaskExtDTO.setTaskDefKey("hr-deal-outReport");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processInstanceId");
        bpmTaskExtDTO.setProcessDefinitionId("processInstanceId");
        bpmTaskExtDTO.setParamsMap(new HashMap<>());
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        bpmTaskExtDTO.setImageUrl("sign");
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);
    }
}
