package com.unicom.swdx.module.oa.service;

import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OAApproveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import com.unicom.swdx.module.oa.service.lecture.LectureService;
import com.unicom.swdx.module.oa.service.outReport.OutReportService;
import com.unicom.swdx.module.oa.service.receive.ReceiveService;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.oa.service.summary.WeeklyWorkSummaryService;
import com.unicom.swdx.module.oa.service.vacationDuty.VacationDutyService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OACommonServiceImplTest {

    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private BpmProcessInstanceApi mockBpmProcessInstanceApi;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private SmsSendApi mockSmsSendApi;
    @Mock
    private OALeaveService mockLeaveService;
    @Mock
    private LectureService mockLectureService;
    @Mock
    private OutReportService mockOutReportService;
    @Mock
    private DraftService mockDraftService;
    @Mock
    private WeeklyWorkScheduleService mockWeeklyWorkScheduleService;
    @Mock
    private WeeklyWorkSummaryService mockWeeklyWorkSummaryService;
    @Mock
    private LectureMapper mockLectureMapper;
    @Mock
    private VacationDutyService mockDutyService;
    @Mock
    private ReceiveService mockReceiveService;
    @Mock
    private OATaskService mockOaTaskService;

    @InjectMocks
    private OACommonServiceImpl oaCommonServiceImplUnderTest;

    @Test
    void testGetNextTaskName() {
        // Setup
        when(mockLeaveService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockLectureService.getPostType(0L)).thenReturn(0);
        when(mockOutReportService.getPostType(0L)).thenReturn(0);
        when(mockDutyService.getPostType(0L)).thenReturn(0);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskName(0L, "category", 0L);

        // Verify the results
    }

    @Test
    void testGetNextTaskName_AdminUserApiReturnsError() {
        // Setup
        when(mockLeaveService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskName(0L, "category", 0L);

        // Verify the results
    }

    @Test
    void testGetNextTaskName_DeptApiReturnsError() {
        // Setup
        when(mockLeaveService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskName(0L, "category", 0L);

        // Verify the results
    }

    @Test
    void testGetNextTaskInfo() {
        // Setup
        when(mockBpmProcessInstanceApi.getStartUserId("processInstanceId")).thenReturn(CommonResult.success(0L));
        when(mockLeaveService.getPostType(0L)).thenReturn(0);
        when(mockLectureService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDeptByTenantAndName(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDeptByTenantAndName(0L, "教务部")).thenReturn(deptRespDTOCommonResult);

        when(mockOutReportService.getPostType(0L)).thenReturn(0);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskInfo("category", "name", 1L,
                "processInstanceId");

        // Verify the results
    }

    @Test
    void testGetNextTaskInfo_BpmProcessInstanceApiReturnsError() {
        // Setup
        when(mockBpmProcessInstanceApi.getStartUserId("processInstanceId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));
        when(mockLeaveService.getPostType(0L)).thenReturn(0);
        when(mockLectureService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDeptByTenantAndName(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDeptByTenantAndName(0L, "教务部")).thenReturn(deptRespDTOCommonResult);

        when(mockOutReportService.getPostType(0L)).thenReturn(0);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskInfo("category", "name", 1L,
                "processInstanceId");

        // Verify the results
    }

    @Test
    void testGetNextTaskInfo_AdminUserApiReturnsError() {
        // Setup
        when(mockBpmProcessInstanceApi.getStartUserId("processInstanceId")).thenReturn(CommonResult.success(0L));
        when(mockLectureService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDeptByTenantAndName(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDeptByTenantAndName(0L, "教务部")).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskInfo("category", "name", 1L,
                "processInstanceId");

        // Verify the results
    }

    @Test
    void testGetNextTaskInfo_DeptApiReturnsError() {
        // Setup
        when(mockBpmProcessInstanceApi.getStartUserId("processInstanceId")).thenReturn(CommonResult.success(0L));
        when(mockLectureService.getPostType(0L)).thenReturn(0);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDeptByTenantAndName(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDeptByTenantAndName(0L, "教务部")).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getNextTaskInfo("category", "name", 1L,
                "processInstanceId");

        // Verify the results
    }

    @Test
    void testApproveOrReject() {
        // Setup
        final OAApproveReqVO approveReqVO = new OAApproveReqVO();
        approveReqVO.setTaskId("id");
        approveReqVO.setProcessInstanceId("processInstanceId");
        approveReqVO.setResult(false);
        approveReqVO.setComment("comment");
        approveReqVO.setHandSignature("handSignature");
        approveReqVO.setCategory("category");
        approveReqVO.setUserIds(Arrays.asList(0L));
        approveReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        approveReqVO.setSelect(false);

        // Configure BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setReason("comment");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setProcessInstanceId("processInstanceId");
        when(mockBpmTaskServiceApi.approveTask(0L, reqVO)).thenReturn(CommonResult.success(false));

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        final UserDTO assigneeUser = new UserDTO();
        assigneeUser.setId(0L);
        assigneeUser.setNickname("nickname");
        bpmTaskRespDTO.setAssigneeUser(assigneeUser);
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Configure BpmTaskServiceApi.rejectTask(...).
        final BpmTaskRejectReqDTO reqVO1 = new BpmTaskRejectReqDTO();
        reqVO1.setId("id");
        reqVO1.setProcessInstanceId("processInstanceId");
        reqVO1.setTaskType(0);
        reqVO1.setHandSignature("handSignature");
        reqVO1.setReason("comment");
        when(mockBpmTaskServiceApi.rejectTask(0L, reqVO1)).thenReturn(CommonResult.success(false));

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Run the test
        final Boolean result = oaCommonServiceImplUnderTest.approveOrReject(approveReqVO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockLectureMapper).insertImage(0L, "handSignature");

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO2 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO2.setId("id");
        reqVO2.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO2);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockLeaveService).updateResult("processInstanceId", 0);
        verify(mockWeeklyWorkScheduleService).cancelMessage(WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build());
    }

    @Test
    void testApproveOrReject_BpmTaskServiceApiApproveTaskReturnsError() {
        // Setup
        final OAApproveReqVO approveReqVO = new OAApproveReqVO();
        approveReqVO.setTaskId("id");
        approveReqVO.setProcessInstanceId("processInstanceId");
        approveReqVO.setResult(false);
        approveReqVO.setComment("comment");
        approveReqVO.setHandSignature("handSignature");
        approveReqVO.setCategory("category");
        approveReqVO.setUserIds(Arrays.asList(0L));
        approveReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        approveReqVO.setSelect(false);

        // Configure BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setReason("comment");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setProcessInstanceId("processInstanceId");
        when(mockBpmTaskServiceApi.approveTask(0L, reqVO))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        final UserDTO assigneeUser = new UserDTO();
        assigneeUser.setId(0L);
        assigneeUser.setNickname("nickname");
        bpmTaskRespDTO.setAssigneeUser(assigneeUser);
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Run the test
        final Boolean result = oaCommonServiceImplUnderTest.approveOrReject(approveReqVO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockLectureMapper).insertImage(0L, "handSignature");

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    void testApproveOrReject_BpmTaskServiceApiGetTaskListByProcessInstanceIdReturnsNoItems() {
        // Setup
        final OAApproveReqVO approveReqVO = new OAApproveReqVO();
        approveReqVO.setTaskId("id");
        approveReqVO.setProcessInstanceId("processInstanceId");
        approveReqVO.setResult(false);
        approveReqVO.setComment("comment");
        approveReqVO.setHandSignature("handSignature");
        approveReqVO.setCategory("category");
        approveReqVO.setUserIds(Arrays.asList(0L));
        approveReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        approveReqVO.setSelect(false);

        // Configure BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setReason("comment");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setProcessInstanceId("processInstanceId");
        when(mockBpmTaskServiceApi.approveTask(0L, reqVO)).thenReturn(CommonResult.success(false));

        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId"))
                .thenReturn(Collections.emptyList());
        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Run the test
        final Boolean result = oaCommonServiceImplUnderTest.approveOrReject(approveReqVO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockLectureMapper).insertImage(0L, "handSignature");

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    void testApproveOrReject_BpmTaskServiceApiRejectTaskReturnsError() {
        // Setup
        final OAApproveReqVO approveReqVO = new OAApproveReqVO();
        approveReqVO.setTaskId("id");
        approveReqVO.setProcessInstanceId("processInstanceId");
        approveReqVO.setResult(false);
        approveReqVO.setComment("comment");
        approveReqVO.setHandSignature("handSignature");
        approveReqVO.setCategory("category");
        approveReqVO.setUserIds(Arrays.asList(0L));
        approveReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        approveReqVO.setSelect(false);

        // Configure BpmTaskServiceApi.rejectTask(...).
        final BpmTaskRejectReqDTO reqVO = new BpmTaskRejectReqDTO();
        reqVO.setId("id");
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setReason("comment");
        when(mockBpmTaskServiceApi.rejectTask(0L, reqVO))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Run the test
        final Boolean result = oaCommonServiceImplUnderTest.approveOrReject(approveReqVO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockLectureMapper).insertImage(0L, "handSignature");
        verify(mockLeaveService).updateResult("processInstanceId", 0);
        verify(mockWeeklyWorkScheduleService).cancelMessage(WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build());
    }

    @Test
    void testEnd() {
        // Setup
        final OARemoveReqVO reqVO = new OARemoveReqVO();
        reqVO.setTaskId("taskId");
        reqVO.setProcessInstanceId("processDefinitionId");
        reqVO.setCategory("category");

        when(mockBpmProcessInstanceApi.endProcess(0L, "processDefinitionId")).thenReturn(CommonResult.success("value"));

        // Run the test
        oaCommonServiceImplUnderTest.end(reqVO);

        // Verify the results
        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("结束");
        bpmTaskExtDTO.setTaskId("end");
        bpmTaskExtDTO.setTaskDefKey("end");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processDefinitionId");
        bpmTaskExtDTO.setProcessDefinitionId("processDefinitionId");
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);
    }

    @Test
    void testEnd_BpmProcessInstanceApiReturnsError() {
        // Setup
        final OARemoveReqVO reqVO = new OARemoveReqVO();
        reqVO.setTaskId("taskId");
        reqVO.setProcessInstanceId("processDefinitionId");
        reqVO.setCategory("category");

        when(mockBpmProcessInstanceApi.endProcess(0L, "processDefinitionId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        oaCommonServiceImplUnderTest.end(reqVO);

        // Verify the results
        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("结束");
        bpmTaskExtDTO.setTaskId("end");
        bpmTaskExtDTO.setTaskDefKey("end");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processDefinitionId");
        bpmTaskExtDTO.setProcessDefinitionId("processDefinitionId");
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);
    }

    @Test
    void testRemove() {
        // Setup
        final OARemoveReqVO reqVO = new OARemoveReqVO();
        reqVO.setTaskId("taskId");
        reqVO.setProcessInstanceId("processDefinitionId");
        reqVO.setCategory("category");

        when(mockBpmProcessInstanceApi.removeProcess(0L, "processDefinitionId"))
                .thenReturn(CommonResult.success("value"));

        // Run the test
        oaCommonServiceImplUnderTest.remove(reqVO);

        // Verify the results
        verify(mockLeaveService).removeById("id");
    }

    @Test
    void testRemove_BpmProcessInstanceApiReturnsError() {
        // Setup
        final OARemoveReqVO reqVO = new OARemoveReqVO();
        reqVO.setTaskId("taskId");
        reqVO.setProcessInstanceId("processDefinitionId");
        reqVO.setCategory("category");

        when(mockBpmProcessInstanceApi.removeProcess(0L, "processDefinitionId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        oaCommonServiceImplUnderTest.remove(reqVO);

        // Verify the results
        verify(mockLeaveService).removeById("id");
    }

    @Test
    void testCancelToStarter() {
        // Setup
        // Run the test
        oaCommonServiceImplUnderTest.cancelToStarter(0L, "processInstanceId", "category");

        // Verify the results
        // Confirm BpmTaskServiceApi.revokeProcess(...).
        final BpmTaskDTO bpmTaskVO = new BpmTaskDTO();
        bpmTaskVO.setTaskId("taskId");
        bpmTaskVO.setUserId("userId");
        bpmTaskVO.setComment("comment");
        bpmTaskVO.setInstanceId("processInstanceId");
        bpmTaskVO.setTargetKey("targetKey");
        verify(mockBpmTaskServiceApi).revokeProcess(bpmTaskVO);
        verify(mockLeaveService).updateResult("processInstanceId", 0);
    }

    @Test
    void testCancelToDraft() {
        // Setup
        when(mockBpmProcessInstanceApi.cancelProcessInstance(0L, "processInstanceId"))
                .thenReturn(CommonResult.success("value"));

        // Configure WeeklyWorkSummaryService.getByProcessInstanceId(...).
        final WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryDO.builder()
                .id(0L)
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .build();
        when(mockWeeklyWorkSummaryService.getByProcessInstanceId("processInstanceId")).thenReturn(weeklyWorkSummaryDO);

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Run the test
        oaCommonServiceImplUnderTest.cancelToDraft(0L, "processInstanceId", "category");

        // Verify the results
        verify(mockLeaveService).updateResult("processInstanceId", 0);
        verify(mockWeeklyWorkScheduleService).BatchUpdateStatusByIds(Arrays.asList(0L), 0);
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
        verify(mockWeeklyWorkScheduleService).cancelMessage(WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build());
    }

    @Test
    void testCancelToDraft_BpmProcessInstanceApiReturnsError() {
        // Setup
        when(mockBpmProcessInstanceApi.cancelProcessInstance(0L, "processInstanceId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure WeeklyWorkSummaryService.getByProcessInstanceId(...).
        final WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryDO.builder()
                .id(0L)
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .build();
        when(mockWeeklyWorkSummaryService.getByProcessInstanceId("processInstanceId")).thenReturn(weeklyWorkSummaryDO);

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Run the test
        oaCommonServiceImplUnderTest.cancelToDraft(0L, "processInstanceId", "category");

        // Verify the results
        verify(mockLeaveService).updateResult("processInstanceId", 0);
        verify(mockWeeklyWorkScheduleService).BatchUpdateStatusByIds(Arrays.asList(0L), 0);
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
        verify(mockWeeklyWorkScheduleService).cancelMessage(WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build());
    }

    @Test
    void testGetApproveList() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getTaskNextApprovals(0L, "taskId", "nextTaskName", 0L))
                .thenReturn(CommonResult.success(new HashMap<>()));

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getApproveList(0L, "taskId", "nextTaskName",
                0L);

        // Verify the results
    }

    @Test
    void testGetApproveList_AdminUserApiReturnsError() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getTaskNextApprovals(0L, "taskId", "nextTaskName", 0L))
                .thenReturn(CommonResult.success(new HashMap<>()));

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getApproveList(0L, "taskId", "nextTaskName",
                0L);

        // Verify the results
    }

    @Test
    void testGetApproveList_BpmTaskServiceApiReturnsError() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getTaskNextApprovals(0L, "taskId", "nextTaskName", 0L))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        final Map<String, Object> result = oaCommonServiceImplUnderTest.getApproveList(0L, "taskId", "nextTaskName",
                0L);

        // Verify the results
    }

    @Test
    void testSendUrgency() {
        // Setup
        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setCategory("category");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        startUser.setNickname("nickname");
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockBpmProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        when(mockLeaveService.getByProcessInstanceId("processInstanceId")).thenReturn(OALeaveDO.builder()
                .id(0L)
                .build());
        when(mockLectureService.getByProcessInstanceId("processInstanceId")).thenReturn(LectureDO.builder()
                .id(0L)
                .build());
        when(mockOutReportService.getByProcessInstanceId("processInstanceId")).thenReturn(OutReportDO.builder()
                .id(0L)
                .build());

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Configure WeeklyWorkSummaryService.getByProcessInstanceId(...).
        final WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryDO.builder()
                .id(0L)
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .build();
        when(mockWeeklyWorkSummaryService.getByProcessInstanceId("processInstanceId")).thenReturn(weeklyWorkSummaryDO);

        // Configure VacationDutyService.getByProcessInstanceId(...).
        final VacationDutyDO vacationDutyDO = VacationDutyDO.builder()
                .id(0L)
                .build();
        when(mockDutyService.getByProcessInstanceId("processInstanceId")).thenReturn(vacationDutyDO);

        when(mockBpmTaskServiceApi.superviseTask(0L, "processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        oaCommonServiceImplUnderTest.sendUrgency(0L, "processInstanceId", "urgeMode");

        // Verify the results
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockReceiveService).saveBatch(Arrays.asList(ReceiveDO.builder()
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .content("content")
                .build()));
    }

    @Test
    void testSendUrgency_BpmProcessInstanceApiReturnsError() {
        // Setup
        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockBpmProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        when(mockLeaveService.getByProcessInstanceId("processInstanceId")).thenReturn(OALeaveDO.builder()
                .id(0L)
                .build());
        when(mockLectureService.getByProcessInstanceId("processInstanceId")).thenReturn(LectureDO.builder()
                .id(0L)
                .build());
        when(mockOutReportService.getByProcessInstanceId("processInstanceId")).thenReturn(OutReportDO.builder()
                .id(0L)
                .build());

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Configure WeeklyWorkSummaryService.getByProcessInstanceId(...).
        final WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryDO.builder()
                .id(0L)
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .build();
        when(mockWeeklyWorkSummaryService.getByProcessInstanceId("processInstanceId")).thenReturn(weeklyWorkSummaryDO);

        // Configure VacationDutyService.getByProcessInstanceId(...).
        final VacationDutyDO vacationDutyDO = VacationDutyDO.builder()
                .id(0L)
                .build();
        when(mockDutyService.getByProcessInstanceId("processInstanceId")).thenReturn(vacationDutyDO);

        when(mockBpmTaskServiceApi.superviseTask(0L, "processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        oaCommonServiceImplUnderTest.sendUrgency(0L, "processInstanceId", "urgeMode");

        // Verify the results
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockReceiveService).saveBatch(Arrays.asList(ReceiveDO.builder()
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .content("content")
                .build()));
    }

    @Test
    void testSendUrgency_BpmTaskServiceApiReturnsNoItems() {
        // Setup
        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setCategory("category");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        startUser.setNickname("nickname");
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockBpmProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        when(mockLeaveService.getByProcessInstanceId("processInstanceId")).thenReturn(OALeaveDO.builder()
                .id(0L)
                .build());
        when(mockBpmTaskServiceApi.superviseTask(0L, "processInstanceId"))
                .thenReturn(CommonResult.success(Collections.emptyList()));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        oaCommonServiceImplUnderTest.sendUrgency(0L, "processInstanceId", "urgeMode");

        // Verify the results
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockReceiveService).saveBatch(Arrays.asList(ReceiveDO.builder()
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .content("content")
                .build()));
    }

    @Test
    void testSendUrgency_BpmTaskServiceApiReturnsError() {
        // Setup
        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setCategory("category");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        startUser.setNickname("nickname");
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockBpmProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        when(mockLeaveService.getByProcessInstanceId("processInstanceId")).thenReturn(OALeaveDO.builder()
                .id(0L)
                .build());
        when(mockBpmTaskServiceApi.superviseTask(0L, "processInstanceId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        oaCommonServiceImplUnderTest.sendUrgency(0L, "processInstanceId", "urgeMode");

        // Verify the results
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockReceiveService).saveBatch(Arrays.asList(ReceiveDO.builder()
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .content("content")
                .build()));
    }

    @Test
    void testSendUrgency_AdminUserApiReturnsNoItems() {
        // Setup
        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setCategory("category");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        startUser.setNickname("nickname");
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockBpmProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        when(mockLeaveService.getByProcessInstanceId("processInstanceId")).thenReturn(OALeaveDO.builder()
                .id(0L)
                .build());
        when(mockBpmTaskServiceApi.superviseTask(0L, "processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure AdminUserApi.getUsers(...).
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        oaCommonServiceImplUnderTest.sendUrgency(0L, "processInstanceId", "urgeMode");

        // Verify the results
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockReceiveService).saveBatch(Arrays.asList(ReceiveDO.builder()
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .content("content")
                .build()));
    }

    @Test
    void testSendUrgency_AdminUserApiReturnsError() {
        // Setup
        // Configure BpmProcessInstanceApi.getProcessInstanceInfo(...).
        final BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = new BpmProcessInstanceRespDTO();
        bpmProcessInstanceRespDTO.setCategory("category");
        bpmProcessInstanceRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UserDTO startUser = new UserDTO();
        startUser.setId(0L);
        startUser.setNickname("nickname");
        bpmProcessInstanceRespDTO.setStartUser(startUser);
        final CommonResult<BpmProcessInstanceRespDTO> bpmProcessInstanceRespDTOCommonResult = CommonResult.success(
                bpmProcessInstanceRespDTO);
        when(mockBpmProcessInstanceApi.getProcessInstanceInfo("processInstanceId"))
                .thenReturn(bpmProcessInstanceRespDTOCommonResult);

        when(mockLeaveService.getByProcessInstanceId("processInstanceId")).thenReturn(OALeaveDO.builder()
                .id(0L)
                .build());
        when(mockBpmTaskServiceApi.superviseTask(0L, "processInstanceId"))
                .thenReturn(CommonResult.success(Arrays.asList(0L)));

        // Configure AdminUserApi.getUsers(...).
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        oaCommonServiceImplUnderTest.sendUrgency(0L, "processInstanceId", "urgeMode");

        // Verify the results
        verify(mockSmsSendApi).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockReceiveService).saveBatch(Arrays.asList(ReceiveDO.builder()
                .category("category")
                .userId("userId")
                .applyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemId(0L)
                .processInstanceId("processInstanceId")
                .promoterUserId("promoterUserId")
                .content("content")
                .build()));
    }
}
