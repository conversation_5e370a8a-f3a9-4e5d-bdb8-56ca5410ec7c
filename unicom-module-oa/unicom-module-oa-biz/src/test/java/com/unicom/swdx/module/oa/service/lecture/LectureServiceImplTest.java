package com.unicom.swdx.module.oa.service.lecture;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureCreateReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LectureServiceImplTest {

    @Mock
    private DraftService mockDraftService;
    @Mock
    private OATaskService mockOaTaskService;
    @Mock
    private BpmProcessInstanceApi mockProcessInstanceApi;
    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private PostApi mockPostApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private BpmTaskServiceApi mockTaskServiceApi;
    @Mock
    private LectureMapper mockLectureMapper;

    @InjectMocks
    private LectureServiceImpl lectureServiceImplUnderTest;

    @Test
    void testInit() {
        // Setup
        // Run the test
        lectureServiceImplUnderTest.init();

        // Verify the results
    }

    @Test
    void testSaveDraft() {
        // Setup
        final LectureCreateReqVO reqVO = new LectureCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setSign("sign");
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final Long result = lectureServiceImplUnderTest.saveDraft(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
        verify(mockDraftService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    void testSaveDraft_AdminUserApiReturnsError() {
        // Setup
        final LectureCreateReqVO reqVO = new LectureCreateReqVO();
        reqVO.setStartDate(LocalDate.of(2020, 1, 1));
        reqVO.setEndDate(LocalDate.of(2020, 1, 1));
        reqVO.setSign("sign");
        reqVO.setIsDraft(false);
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final Long result = lectureServiceImplUnderTest.saveDraft(reqVO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
    }
}
