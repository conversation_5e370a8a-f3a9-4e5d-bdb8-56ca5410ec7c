package com.unicom.swdx.module.oa.controller.admin;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class WeeklyWorkSummaryControllerTest {

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void createWorkSummaryProcess() {
    }

    @Test
    void restartSummary() {
    }

    @Test
    void getWorkSchedule() {
    }

    @Test
    void getPreviewData() {
    }

    @Test
    void getWeeklyScheduleList() {
    }

    @Test
    void getDetail() {
    }

    @Test
    void deleteWorkSummary() {
    }

    @Test
    void end() {
    }
}