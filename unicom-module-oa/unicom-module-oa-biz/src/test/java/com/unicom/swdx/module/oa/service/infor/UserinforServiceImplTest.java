package com.unicom.swdx.module.oa.service.infor;

import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforUpdateReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;
import com.unicom.swdx.module.oa.dal.kingbase.UserinforMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserinforServiceImplTest {

    @Mock
    private UserinforMapper mockUserinforMapper;

    @InjectMocks
    private UserinforServiceImpl userinforServiceImplUnderTest;

    @Test
    void testCreateUserinfor() {
        // Setup
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();

        // Run the test
        final Integer result = userinforServiceImplUnderTest.createUserinfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockUserinforMapper).insert(UserinforDO.builder()
                .id(0)
                .tenantId(0L)
                .build());
    }

    @Test
    void testUpdateUserinfor() {
        // Setup
        final UserinforUpdateReqVO updateReqVO = new UserinforUpdateReqVO();
        updateReqVO.setId(0);

        // Configure UserinforMapper.selectById(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .id(0)
                .tenantId(0L)
                .build();
        when(mockUserinforMapper.selectById(0)).thenReturn(userinforDO);

        // Run the test
        userinforServiceImplUnderTest.updateUserinfor(updateReqVO);

        // Verify the results
        verify(mockUserinforMapper).updateById(UserinforDO.builder()
                .id(0)
                .tenantId(0L)
                .build());
    }

    @Test
    void testUpdateUserinfor_UserinforMapperSelectByIdReturnsNull() {
        // Setup
        final UserinforUpdateReqVO updateReqVO = new UserinforUpdateReqVO();
        updateReqVO.setId(0);

        when(mockUserinforMapper.selectById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> userinforServiceImplUnderTest.updateUserinfor(updateReqVO))
                .isInstanceOf(ServiceException.class);
    }
}
