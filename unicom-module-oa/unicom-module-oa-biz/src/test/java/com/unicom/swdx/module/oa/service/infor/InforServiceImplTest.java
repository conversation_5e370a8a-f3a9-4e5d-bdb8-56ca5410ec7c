package com.unicom.swdx.module.oa.service.infor;

import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.oa.controller.admin.infor.InforController;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.InforCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.InforUpdateReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.InforDO;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;
import com.unicom.swdx.module.oa.dal.dataobject.YeartaskOperation;
import com.unicom.swdx.module.oa.dal.dataobject.YeartaskRecord;
import com.unicom.swdx.module.oa.dal.kingbase.InforMapper;
import com.unicom.swdx.module.oa.dal.kingbase.UserinforMapper;
import com.unicom.swdx.module.oa.dal.kingbase.YeartaskOperationMapper;
import com.unicom.swdx.module.oa.dal.kingbase.YeartaskRecordMapper;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.sms.SmsQueApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.AsyncListenableTaskExecutor;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InforServiceImplTest {

    @Mock
    private UserinforService mockUserinforService;
    @Mock
    private UserinforMapper mockUserinforMapper;
    @Mock
    private InforController mockInforController;
    @Mock
    private InforMapper mockInforMapper;
    @Mock
    private AsyncListenableTaskExecutor mockTaskExecutor;
    @Mock
    private YeartaskOperationMapper mockOperationMapper;
    @Mock
    private YeartaskRecordMapper mockYeartaskRecordMapper;
    @Mock
    private SmsQueApi mockSmsQueApi;
    @Mock
    private AdminUserApi mockAdminUserApi;
    @Mock
    private SmsSendApi mockSmsSendService;
    @Mock
    private DeptApi mockDeptApi;

    @InjectMocks
    private InforServiceImpl inforServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        inforServiceImplUnderTest.userinforService = mockUserinforService;
        inforServiceImplUnderTest.userinforMapper = mockUserinforMapper;
        inforServiceImplUnderTest.inforController = mockInforController;
        inforServiceImplUnderTest.smsQueApi = mockSmsQueApi;
        inforServiceImplUnderTest.adminUserApi = mockAdminUserApi;
        inforServiceImplUnderTest.smsSendService = mockSmsSendService;
        inforServiceImplUnderTest.deptApi = mockDeptApi;
    }

    @Test
    void testCreateInfor() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_DeptApiGetDeptReturnsError() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_AdminUserApiGetUserReturnsError() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_UserinforMapperSelectByTenantIdReturnsNoItems() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Collections.emptyList());
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_UserinforMapperSelectPrincipalByTenantIdReturnsNoItems() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Collections.emptyList());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_UserinforMapperSelectUserinforDOListReturnsNoItems() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(Collections.emptyList());

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_AdminUserApiGetUsersReturnsNull() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(null);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_AdminUserApiGetUsersReturnsNoItems() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testCreateInfor_AdminUserApiGetUsersReturnsError() {
        // Setup
        final InforCreateReqVO createReqVO = new InforCreateReqVO();
        createReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        createReqVO.setSusername("susername");
        createReqVO.setSuserid(0);
        createReqVO.setSdeptid(0);
        createReqVO.setStatus(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockUserinforMapper.selectByTenantId(0L)).thenReturn(Arrays.asList(0L));
        when(mockUserinforMapper.selectPrincipalByTenantId(0L)).thenReturn(Arrays.asList(0L));
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final Integer result = inforServiceImplUnderTest.createInfor(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockInforMapper).insert(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO1 = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO1);
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testUpdateInfor() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList1 = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList1);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_InforMapperSelectByIdReturnsNull() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        when(mockInforMapper.selectById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inforServiceImplUnderTest.updateInfor(updateReqVO))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    void testUpdateInfor_AdminUserApiGetUserReturnsError() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList1 = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList1);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_DeptApiGetDeptReturnsError() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList1 = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList1);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_UserinforMapperSelectUserinforDOListReturnsNoItems() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(Collections.emptyList());

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_AdminUserApiGetUsersReturnsNull() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(null);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList1 = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList1);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_AdminUserApiGetUsersReturnsNoItems() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(Collections.emptyList());
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList1 = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList1);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_AdminUserApiGetUsersReturnsError() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        // Configure UserinforMapper.selectList(...).
        final List<UserinforDO> userinforDOList1 = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(userinforDOList1);

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());

        // Confirm YeartaskRecordMapper.insert(...).
        final YeartaskRecord entity = new YeartaskRecord();
        entity.setInforid(0L);
        entity.setRecordFiles("endfile");
        entity.setCreatetime("createtime");
        entity.setRecordContent("endcontent");
        entity.setUserName("nickname");
        entity.setUserid("userid");
        entity.setProcessStatus(0);
        verify(mockYeartaskRecordMapper).insert(entity);
    }

    @Test
    void testUpdateInfor_UserinforMapperSelectListReturnsNoItems() {
        // Setup
        final InforUpdateReqVO updateReqVO = new InforUpdateReqVO();
        updateReqVO.setStarttime("starttime");
        updateReqVO.setStatus(0);
        updateReqVO.setEndcontent("endcontent");
        updateReqVO.setEndfile("endfile");
        updateReqVO.setShandle(0);
        updateReqVO.setHhandle(0);
        updateReqVO.setYeartag(0);
        updateReqVO.setLeaderhandle(0);
        updateReqVO.setRejectionReason("rejectionreason");
        updateReqVO.setTaskProgress(0);
        updateReqVO.setId(0);
        updateReqVO.setUserinforDOList(Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build()));
        updateReqVO.setProcessStatus(0);

        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("dptname");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setTenantId(0L);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure UserinforMapper.selectUserinforDOList(...).
        final List<UserinforDO> userinforDOList = Arrays.asList(UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build());
        when(mockUserinforMapper.selectUserinforDOList(0, 1)).thenReturn(userinforDOList);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO1));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure UserinforMapper.selectOne(...).
        final UserinforDO userinforDO = UserinforDO.builder()
                .uid(0)
                .uname("nickname")
                .utype(0)
                .inforid(0)
                .dptid(0)
                .dptname("dptname")
                .build();
        when(mockUserinforMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(userinforDO);

        when(mockUserinforMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Run the test
        final String result = inforServiceImplUnderTest.updateInfor(updateReqVO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockInforMapper).updateById(InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build());
        verify(mockUserinforMapper).delete(any(LambdaQueryWrapperX.class));

        // Confirm UserinforService.createUserinfor(...).
        final UserinforCreateReqVO createReqVO = new UserinforCreateReqVO();
        verify(mockUserinforService).createUserinfor(createReqVO);
        verify(mockDeptApi).sendMessageRefresh("code");

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        verify(mockSmsQueApi).sendque(req);
        verify(mockOperationMapper).deletemax(0L);
        verify(mockOperationMapper).insertYeartaskOperation(YeartaskOperation.builder()
                .nickname("nickname")
                .userid("userid")
                .dptname("dptname")
                .dptid("dptid")
                .inforid(0L)
                .content("content")
                .rejectionreason("rejectionreason")
                .createtime("createtime")
                .build());
    }

    @Test
    void testDeleteInfor() {
        // Setup
        // Configure InforMapper.selectById(...).
        final InforDO inforDO = InforDO.builder()
                .id(0)
                .susername("nickname")
                .suserid(0)
                .sdeptid(0)
                .sdepname("dptname")
                .taskname("taskname")
                .tasktype(0)
                .starttime("starttime")
                .endtime("endtime")
                .taskcontent("taskcontent")
                .status(0)
                .yeartag(0)
                .shandle(0)
                .mobile("mobile")
                .hhandle(0)
                .leaderhandle(0)
                .handleCount(0)
                .isHandle(0)
                .build();
        when(mockInforMapper.selectById(0)).thenReturn(inforDO);

        // Run the test
        inforServiceImplUnderTest.deleteInfor(0);

        // Verify the results
        verify(mockInforMapper).deleteById(0);
    }

    @Test
    void testDeleteInfor_InforMapperSelectByIdReturnsNull() {
        // Setup
        when(mockInforMapper.selectById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inforServiceImplUnderTest.deleteInfor(0)).isInstanceOf(ServiceException.class);
    }
}
