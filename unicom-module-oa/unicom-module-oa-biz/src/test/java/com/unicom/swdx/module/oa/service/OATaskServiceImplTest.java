package com.unicom.swdx.module.oa.service;

import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskApproveReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskExtDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.UserDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.oa.service.summary.WeeklyWorkSummaryService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OATaskServiceImplTest {

    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private BpmProcessInstanceApi mockBpmProcessInstanceApi;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private WeeklyWorkSummaryService mockWeeklyWorkSummaryService;
    @Mock
    private WeeklyWorkScheduleService mockWeeklyWorkScheduleService;

    @InjectMocks
    private OATaskServiceImpl oaTaskServiceImplUnderTest;

    @Test
    void testGetTaskId() {
        // Setup
        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        final UserDTO assigneeUser = new UserDTO();
        assigneeUser.setId(0L);
        bpmTaskRespDTO.setAssigneeUser(assigneeUser);
        bpmTaskRespDTO.setResult(0);
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getTaskId("processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo("id");
    }

    @Test
    void testGetTaskId_BpmTaskServiceApiReturnsNoItems() {
        // Setup
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getTaskId("processInstanceId");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testPickApprovals() {
        // Setup
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("id");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getNextTaskName("id")).thenReturn("result");

        // Configure BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setTaskType(0);
        reqVO.setVariables(new HashMap<>());
        reqVO.setParamsMap(new HashMap<>());
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockBpmTaskServiceApi.approveTask(0L, reqVO)).thenReturn(CommonResult.success(false));

        // Run the test
        oaTaskServiceImplUnderTest.pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    void testPickApprovals_AdminUserApiReturnsError() {
        // Setup
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("id");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getNextTaskName("id")).thenReturn("result");

        // Configure BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setTaskType(0);
        reqVO.setVariables(new HashMap<>());
        reqVO.setParamsMap(new HashMap<>());
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockBpmTaskServiceApi.approveTask(0L, reqVO)).thenReturn(CommonResult.success(false));

        // Run the test
        oaTaskServiceImplUnderTest.pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    void testPickApprovals_BpmTaskServiceApiApproveTaskReturnsError() {
        // Setup
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("id");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getNextTaskName("id")).thenReturn("result");

        // Configure BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setTaskType(0);
        reqVO.setVariables(new HashMap<>());
        reqVO.setParamsMap(new HashMap<>());
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockBpmTaskServiceApi.approveTask(0L, reqVO))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        oaTaskServiceImplUnderTest.pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    void testEnd() {
        // Setup
        final OARemoveReqVO reqVO = new OARemoveReqVO();
        reqVO.setTaskId("taskId");
        reqVO.setProcessInstanceId("processDefinitionId");
        reqVO.setCategory("category");

        // Configure WeeklyWorkSummaryService.getByProcessInstanceId(...).
        final WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryDO.builder()
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .build();
        when(mockWeeklyWorkSummaryService.getByProcessInstanceId("processDefinitionId"))
                .thenReturn(weeklyWorkSummaryDO);

        when(mockBpmProcessInstanceApi.endProcess(0L, "processDefinitionId")).thenReturn(CommonResult.success("value"));

        // Run the test
        oaTaskServiceImplUnderTest.end(reqVO);

        // Verify the results
        verify(mockWeeklyWorkScheduleService).BatchUpdateStatusByIds(Arrays.asList(0L), 0);

        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setAssigneeUserId(0L);
        bpmTaskExtDTO.setName("结束");
        bpmTaskExtDTO.setTaskId("end");
        bpmTaskExtDTO.setTaskDefKey("end");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processDefinitionId");
        bpmTaskExtDTO.setProcessDefinitionId("processDefinitionId");
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);
    }

    @Test
    void testEnd_BpmProcessInstanceApiReturnsError() {
        // Setup
        final OARemoveReqVO reqVO = new OARemoveReqVO();
        reqVO.setTaskId("taskId");
        reqVO.setProcessInstanceId("processDefinitionId");
        reqVO.setCategory("category");

        // Configure WeeklyWorkSummaryService.getByProcessInstanceId(...).
        final WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryDO.builder()
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .build();
        when(mockWeeklyWorkSummaryService.getByProcessInstanceId("processDefinitionId"))
                .thenReturn(weeklyWorkSummaryDO);

        when(mockBpmProcessInstanceApi.endProcess(0L, "processDefinitionId"))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        oaTaskServiceImplUnderTest.end(reqVO);

        // Verify the results
        verify(mockWeeklyWorkScheduleService).BatchUpdateStatusByIds(Arrays.asList(0L), 0);

        // Confirm BpmTaskServiceApi.saveCustomTaskExt(...).
        final BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setAssigneeUserId(0L);
        bpmTaskExtDTO.setName("结束");
        bpmTaskExtDTO.setTaskId("end");
        bpmTaskExtDTO.setTaskDefKey("end");
        bpmTaskExtDTO.setResult(0);
        bpmTaskExtDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setProcessInstanceId("processDefinitionId");
        bpmTaskExtDTO.setProcessDefinitionId("processDefinitionId");
        bpmTaskExtDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskExtDTO.setTaskType(0);
        verify(mockBpmTaskServiceApi).saveCustomTaskExt(bpmTaskExtDTO);
    }

    @Test
    void testGetDeptLeaderName() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getDeptLeaderName(0L);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    void testGetDeptLeaderName_AdminUserApiReturnsError() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getDeptLeaderName(0L);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    void testGetDeptLeaderName_DeptApiReturnsError() {
        // Setup
        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getDeptLeaderName(0L);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    void testGetDeptLeader() {
        // Setup
        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getDeptLeader(0L);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    void testGetDeptLeader_DeptApiReturnsError() {
        // Setup
        // Configure DeptApi.getDept(...).
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setDeptIds(Arrays.asList(0L));
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getDeptLeader(0L);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    void testGetDeptLeader_AdminUserApiReturnsError() {
        // Setup
        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("name");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final String result = oaTaskServiceImplUnderTest.getDeptLeader(0L);

        // Verify the results
        assertThat(result).isEqualTo("");
    }
}
