package com.unicom.swdx.module.oa.service.summary;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmRestartDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskApproveReqDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.*;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import com.unicom.swdx.module.oa.dal.kingbase.WeeklyWorkSummaryMapper;
import com.unicom.swdx.module.oa.dal.kingbase.WorkScheduleMapper;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WeeklyWorkSummaryServiceImplTest {

    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private WeeklyWorkSummaryMapper mockWeeklyWorkSummaryMapper;
    @Mock
    private WeeklyWorkScheduleService mockWeeklyWorkScheduleService;
    @Mock
    private BpmProcessInstanceApi mockProcessInstanceApi;
    @Mock
    private DraftService mockDraftService;
    @Mock
    private OATaskService mockOaTaskService;
    @Mock
    private WorkScheduleMapper mockWorkScheduleMapper;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private PostApi mockPostApi;

    @InjectMocks
    private WeeklyWorkSummaryServiceImpl weeklyWorkSummaryServiceImplUnderTest;

    @Test
    void testCreateWorkSummaryProcess() {
        // 初始化
        final SummaryCreateReqVO createReqVO = new SummaryCreateReqVO();
        createReqVO.setWeeklyWorkScheduleIds("");
        createReqVO.setId(0L);
        createReqVO.setProcessInstanceId("processInstanceId");
        createReqVO.setTaskIds(Arrays.asList("value"));
        final SummaryToDoRespVO summaryToDoRespVO = new SummaryToDoRespVO();
        summaryToDoRespVO.setProcessInstanceId("processInstanceId");
        summaryToDoRespVO.setDeptId(0L);
        summaryToDoRespVO.setUserId(0L);
        summaryToDoRespVO.setUserNickName("nickname");
        summaryToDoRespVO.setDeptName("deptName");
        summaryToDoRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryToDoRespVO.setScheduleId(0L);
        summaryToDoRespVO.setType("撤回");
        summaryToDoRespVO.setSummaryId(0L);
        createReqVO.setData(Arrays.asList(summaryToDoRespVO));
        createReqVO.setUserIds(Arrays.asList(0L));
        createReqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // 配置 AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockPostApi.getUserByPost("oa-secretary", 0L)).thenReturn(Arrays.asList(0L));

        // 配置 WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // 配置 BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-weeklyworksummary");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));
        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // 运行测试
        final String result = weeklyWorkSummaryServiceImplUnderTest.createWorkSummaryProcess(createReqVO);

        // 验证结果
        assertThat(result).isEqualTo("processInstanceId");
        verify(mockDraftService).deleteByItemId("14", 0L);
        verify(mockWeeklyWorkScheduleService).updateStatusById(0L, 0);

        // 验证 BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setReason("reason");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setVariables(new HashMap<>());
        verify(mockBpmTaskServiceApi).approveTask(0L, reqVO);

        // 验证 OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    void testRestartSummary() {
        // Setup
        final SummaryCreateReqVO reqVO = new SummaryCreateReqVO();
        reqVO.setWeeklyWorkScheduleIds("weeklyWorkScheduleIds");
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setTaskIds(Arrays.asList("value"));
        final SummaryToDoRespVO summaryToDoRespVO = new SummaryToDoRespVO();
        summaryToDoRespVO.setProcessInstanceId("processInstanceId");
        summaryToDoRespVO.setDeptId(0L);
        summaryToDoRespVO.setUserId(0L);
        summaryToDoRespVO.setUserNickName("nickname");
        summaryToDoRespVO.setDeptName("deptName");
        summaryToDoRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryToDoRespVO.setScheduleId(0L);
        summaryToDoRespVO.setType("撤回");
        summaryToDoRespVO.setSummaryId(0L);
        reqVO.setData(Arrays.asList(summaryToDoRespVO));
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        when(mockBpmTaskServiceApi.restartProcess(bpmRestartDTO)).thenReturn(CommonResult.success(false));

        when(mockOaTaskService.getTaskId("processInstanceId")).thenReturn("taskId");

        // Run the test
        weeklyWorkSummaryServiceImplUnderTest.restartSummary(0L, reqVO);

        // Verify the results
        // Confirm BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO1 = new BpmTaskApproveReqDTO();
        reqVO1.setId("id");
        reqVO1.setReason("reason");
        reqVO1.setTaskType(0);
        reqVO1.setHandSignature("handSignature");
        reqVO1.setVariables(new HashMap<>());
        verify(mockBpmTaskServiceApi).approveTask(0L, reqVO1);

        // Confirm OATaskService.pickApprovals(...).
        final OApickApprovalsReqVO pickApprovalsReqVO = new OApickApprovalsReqVO();
        pickApprovalsReqVO.setTaskId("taskId");
        pickApprovalsReqVO.setUserIds(Arrays.asList(0L));
        pickApprovalsReqVO.setChargeLeaderSeq("chargeLeaderSeq");
        verify(mockOaTaskService).pickApprovals(pickApprovalsReqVO, LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockWeeklyWorkScheduleService).BatchUpdateStatusByIds(Arrays.asList(0L), 0);
    }


    @Test
    void testGet() {
        // Setup
        final SummaryRespVO expectedResult = new SummaryRespVO();
        expectedResult.setWeeklyWorkScheduleIds("weeklyWorkScheduleIds");
        expectedResult.setUserNickName("nickname");
        expectedResult.setWorkSchedules(Arrays.asList(WorkScheduleDO.builder()
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .build()));
        expectedResult.setResult(0);
        expectedResult.setTaskName("taskName");
        expectedResult.setOperateType(0);

        // Configure WorkScheduleMapper.selectList(...).
        final List<WorkScheduleDO> workScheduleDOS = Arrays.asList(WorkScheduleDO.builder()
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .build());
        when(mockWorkScheduleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(workScheduleDOS);

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockBpmTaskServiceApi.getNeededTaskInfo("14", 0L, "processInstanceId", false, false))
                .thenReturn(new HashMap<>());

        // Run the test
        final SummaryRespVO result = weeklyWorkSummaryServiceImplUnderTest.get(0L, "processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }


    @Test
    void testGetPreviewData() {
        // Setup
        final List<WorkScheduleDO> expectedResult = Arrays.asList(WorkScheduleDO.builder()
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .build());

        // Configure WorkScheduleMapper.selectList(...).
        final List<WorkScheduleDO> workScheduleDOS = Arrays.asList(WorkScheduleDO.builder()
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .build());
        when(mockWorkScheduleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(workScheduleDOS);

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        final List<WorkScheduleDO> result = weeklyWorkSummaryServiceImplUnderTest.getPreviewData(Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }



    @Test
    void testGetByProcessInstanceId() {
        // Setup
        final WeeklyWorkSummaryDO expectedResult = WeeklyWorkSummaryDO.builder()
                .id(0L)
                .processInstanceId("processInstanceId")
                .weeklyWorkScheduleIds("weeklyWorkScheduleIds")
                .userId(0L)
                .build();

        // Run the test
        final WeeklyWorkSummaryDO result = weeklyWorkSummaryServiceImplUnderTest.getByProcessInstanceId(
                "processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetWeeklyScheduleList() {
        // Setup
        final SummaryScheduleRespVO expectedResult = new SummaryScheduleRespVO();
        final SummaryToDoRespVO summaryToDoRespVO = new SummaryToDoRespVO();
        summaryToDoRespVO.setProcessInstanceId("processInstanceId");
        summaryToDoRespVO.setDeptId(0L);
        summaryToDoRespVO.setUserId(0L);
        summaryToDoRespVO.setUserNickName("nickname");
        summaryToDoRespVO.setDeptName("deptName");
        summaryToDoRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryToDoRespVO.setScheduleId(0L);
        summaryToDoRespVO.setType("撤回");
        summaryToDoRespVO.setSummaryId(0L);
        expectedResult.setSummary(Arrays.asList(summaryToDoRespVO));
        expectedResult.setCount(0);
        expectedResult.setCountReject(0);
        expectedResult.setCountCancel(0);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final SummaryScheduleRespVO result = weeklyWorkSummaryServiceImplUnderTest.getWeeklyScheduleList();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockWeeklyWorkScheduleService).BatchUpdateStatusByIds(Arrays.asList(0L), 0);
    }


    @Test
    void testGetWorkScheduleId() {
        // Setup
        // Run the test
        final String result = weeklyWorkSummaryServiceImplUnderTest.getWorkScheduleId("processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo("weeklyWorkScheduleIds");
    }

    @Test
    void testGetDetail() {
        // Setup
        final ScheduleRespVO scheduleRespVO = new ScheduleRespVO();
        scheduleRespVO.setId(0L);
        scheduleRespVO.setProcessInsatnceId("processInsatnceId");
        scheduleRespVO.setUserNickName("userNickName");
        scheduleRespVO.setStatus(0);
        scheduleRespVO.setType("desc");
        final List<ScheduleRespVO> expectedResult = Arrays.asList(scheduleRespVO);

        // Configure WeeklyWorkScheduleService.get(...).
        final ScheduleRespVO scheduleRespVO1 = new ScheduleRespVO();
        scheduleRespVO1.setId(0L);
        scheduleRespVO1.setProcessInsatnceId("processInsatnceId");
        scheduleRespVO1.setUserNickName("userNickName");
        scheduleRespVO1.setStatus(0);
        scheduleRespVO1.setType("desc");
        when(mockWeeklyWorkScheduleService.get(0L, "processInstanceId")).thenReturn(scheduleRespVO1);

        // Run the test
        final List<ScheduleRespVO> result = weeklyWorkSummaryServiceImplUnderTest.getDetail("workScheduleIds");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDeleteSummary() {
        // Setup
        // Run the test
        weeklyWorkSummaryServiceImplUnderTest.deleteSummary(0);

        // Verify the results
        verify(mockProcessInstanceApi).removeProcess(0L, "processInstanceId");
    }

    @Test
    void testEnd() {
        // Setup
        final SummaryCreateReqVO createReqVO = new SummaryCreateReqVO();
        createReqVO.setWeeklyWorkScheduleIds("weeklyWorkScheduleIds");
        createReqVO.setId(0L);
        createReqVO.setProcessInstanceId("processInstanceId");
        createReqVO.setTaskIds(Arrays.asList("value"));
        final SummaryToDoRespVO summaryToDoRespVO = new SummaryToDoRespVO();
        summaryToDoRespVO.setProcessInstanceId("processInstanceId");
        summaryToDoRespVO.setDeptId(0L);
        summaryToDoRespVO.setUserId(0L);
        summaryToDoRespVO.setUserNickName("nickname");
        summaryToDoRespVO.setDeptName("deptName");
        summaryToDoRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryToDoRespVO.setScheduleId(0L);
        summaryToDoRespVO.setType("撤回");
        summaryToDoRespVO.setSummaryId(0L);
        createReqVO.setData(Arrays.asList(summaryToDoRespVO));
        createReqVO.setUserIds(Arrays.asList(0L));
        createReqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setTenantId(0L);
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        when(mockPostApi.getUserByPost("oa-secretary", 0L)).thenReturn(Arrays.asList(0L));

        // Configure WeeklyWorkScheduleService.getByProcessInstanceId(...).
        final WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .build();
        when(mockWeeklyWorkScheduleService.getByProcessInstanceId("processInstanceId"))
                .thenReturn(weeklyWorkScheduleDO);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-weeklyworksummary");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        when(mockProcessInstanceApi.skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(CommonResult.success(false));

        // Run the test
        weeklyWorkSummaryServiceImplUnderTest.end(createReqVO);

        // Verify the results
        verify(mockDraftService).deleteByItemId("14", 0L);
        verify(mockWeeklyWorkScheduleService).updateStatusById(0L, 0);

        // Confirm BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setReason("reason");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setVariables(new HashMap<>());
        verify(mockBpmTaskServiceApi).approveTask(0L, reqVO);

        // Confirm OATaskService.end(...).
        final OARemoveReqVO reqVO1 = new OARemoveReqVO();
        reqVO1.setTaskId("taskId");
        reqVO1.setProcessInstanceId("processInstanceId");
        reqVO1.setCategory("14");
        verify(mockOaTaskService).end(reqVO1);
    }

    @Test
    void testRestartEnd() {
        // Setup
        final SummaryCreateReqVO createReqVO = new SummaryCreateReqVO();
        createReqVO.setWeeklyWorkScheduleIds("weeklyWorkScheduleIds");
        createReqVO.setId(0L);
        createReqVO.setProcessInstanceId("processInstanceId");
        createReqVO.setTaskIds(Arrays.asList("value"));
        final SummaryToDoRespVO summaryToDoRespVO = new SummaryToDoRespVO();
        summaryToDoRespVO.setProcessInstanceId("processInstanceId");
        summaryToDoRespVO.setDeptId(0L);
        summaryToDoRespVO.setUserId(0L);
        summaryToDoRespVO.setUserNickName("nickname");
        summaryToDoRespVO.setDeptName("deptName");
        summaryToDoRespVO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryToDoRespVO.setScheduleId(0L);
        summaryToDoRespVO.setType("撤回");
        summaryToDoRespVO.setSummaryId(0L);
        createReqVO.setData(Arrays.asList(summaryToDoRespVO));
        createReqVO.setUserIds(Arrays.asList(0L));
        createReqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Run the test
        weeklyWorkSummaryServiceImplUnderTest.restartEnd(createReqVO);

        // Verify the results
        // Confirm BpmTaskServiceApi.approveTask(...).
        final BpmTaskApproveReqDTO reqVO = new BpmTaskApproveReqDTO();
        reqVO.setId("id");
        reqVO.setReason("reason");
        reqVO.setTaskType(0);
        reqVO.setHandSignature("handSignature");
        reqVO.setVariables(new HashMap<>());
        verify(mockBpmTaskServiceApi).approveTask(0L, reqVO);

        // Confirm OATaskService.end(...).
        final OARemoveReqVO reqVO1 = new OARemoveReqVO();
        reqVO1.setTaskId("taskId");
        reqVO1.setProcessInstanceId("processInstanceId");
        reqVO1.setCategory("14");
        verify(mockOaTaskService).end(reqVO1);
    }

    @Test
    void testGetDateById() {
        // Setup
        final Map<String, LocalDate> expectedResult = new HashMap<>();
        when(mockWeeklyWorkSummaryMapper.getWeekIdsByPId("processInstanceId")).thenReturn("result");

        // Configure WeeklyWorkSummaryMapper.getDateById(...).
        final SummaryDateVO summaryDateVO = new SummaryDateVO();
        summaryDateVO.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryDateVO.setLastStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        summaryDateVO.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWeeklyWorkSummaryMapper.getDateById(Arrays.asList(0L))).thenReturn(summaryDateVO);

        // Run the test
        final Map<String, LocalDate> result = weeklyWorkSummaryServiceImplUnderTest.getDateById("processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }


    @Test
    void testGetUsersById() {
        // Setup
        when(mockWeeklyWorkSummaryMapper.getWeekIdsByPId("processInstanceId")).thenReturn("result");
        when(mockWeeklyWorkSummaryMapper.getPersonnelIdsById(Arrays.asList(0L))).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<Long> result = weeklyWorkSummaryServiceImplUnderTest.getUsersById("processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList(0L));
    }
}
