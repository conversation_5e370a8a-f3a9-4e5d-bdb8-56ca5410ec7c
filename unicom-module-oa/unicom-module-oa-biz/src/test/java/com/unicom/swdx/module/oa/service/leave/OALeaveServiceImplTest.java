package com.unicom.swdx.module.oa.service.leave;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveCreateReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.dal.kingbase.OALeaveMapper;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.common.CalendarWnlApi;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OALeaveServiceImplTest {

    @Mock
    private OALeaveMapper mockLeaveMapper;
    @Mock
    private DraftService mockDraftService;
    @Mock
    private OATaskService mockOaTaskService;
    @Mock
    private BpmProcessInstanceApi mockProcessInstanceApi;
    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private PostApi mockPostApi;
    @Mock
    private CalendarWnlApi mockCalendarWnlApi;
    @Mock
    private PermissionApi mockPermissionApi;
    @Mock
    private SmsSendApi mockSmsSendApi;
    @Mock
    private LectureMapper mockLectureMapper;
    @Mock
    private ScheduleServiceApi mockScheduleServiceApi;
    @Mock
    private ReceiveApi mockReceiveApi;

    @InjectMocks
    private OALeaveServiceImpl oaLeaveServiceImplUnderTest;

    @Test
    void testInit() {
        // Setup
        // Run the test
        oaLeaveServiceImplUnderTest.init();

        // Verify the results
    }

    @Test
    void testCalculateLeaveDay() {
        // Setup
        when(mockCalendarWnlApi.calculateWorkDay(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1)))
                .thenReturn(CommonResult.success(0));

        // Run the test
        final Integer result = oaLeaveServiceImplUnderTest.calculateLeaveDay(LocalDate.of(2020, 1, 1),
                LocalDate.of(2020, 1, 1));

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testCalculateLeaveDay_CalendarWnlApiReturnsError() {
        // Setup
        when(mockCalendarWnlApi.calculateWorkDay(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1)))
                .thenReturn(CommonResult.error(new ServiceException(0, "message")));

        // Run the test
        final Integer result = oaLeaveServiceImplUnderTest.calculateLeaveDay(LocalDate.of(2020, 1, 1),
                LocalDate.of(2020, 1, 1));

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testSaveDraft() {
        // Setup
        final OALeaveCreateReqVO reqVO = new OALeaveCreateReqVO();
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setDay(1);
        reqVO.setDeptId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setIsDraft(false);
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final boolean result = oaLeaveServiceImplUnderTest.saveDraft(0L, reqVO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockLeaveMapper).insert(OALeaveDO.builder()
                .id(0L)
                .userId(0L)
                .deptId(0L)
                .type(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                //.returnWorkTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .day(1)
                .result(0)
                .isDraft(false)
                .isDealt(false)
                .processInstanceId("proId")
                .build());
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
        verify(mockLeaveMapper).updateById(OALeaveDO.builder()
                .id(0L)
                .userId(0L)
                .deptId(0L)
                .type(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                //.returnWorkTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .day(1)
                .result(0)
                .isDraft(false)
                .isDealt(false)
                .processInstanceId("proId")
                .build());
        verify(mockDraftService).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    void testSaveDraft_AdminUserApiReturnsError() {
        // Setup
        final OALeaveCreateReqVO reqVO = new OALeaveCreateReqVO();
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setDay(1);
        reqVO.setDeptId(0L);
        reqVO.setProcessInstanceId("processInstanceId");
        reqVO.setIsDraft(false);
        reqVO.setUserIds(Arrays.asList(0L));
        reqVO.setChargeLeaderSeq("chargeLeaderSeq");

        // Configure AdminUserApi.getUser(...).
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.error(
                new ServiceException(0, "message"));
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Run the test
        final boolean result = oaLeaveServiceImplUnderTest.saveDraft(0L, reqVO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockLeaveMapper).insert(OALeaveDO.builder()
                .id(0L)
                .userId(0L)
                .deptId(0L)
                .type(0)
                .reason("reason")
                .startTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .launchTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .dealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                //.returnWorkTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .day(1)
                .result(0)
                .isDraft(false)
                .isDealt(false)
                .processInstanceId("proId")
                .build());
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
    }
}
