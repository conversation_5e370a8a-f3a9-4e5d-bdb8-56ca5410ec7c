package com.unicom.swdx.module.oa.controller.admin;

import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureTestVO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class WeeklyWorkScheduleControllerTest {

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void saveDraft() {
        LectureTestVO lectureTestVO = new LectureTestVO();
    }

    @Test
    void createWorkScheduleProcess() {
    }

    @Test
    void restartSchedule() {
    }

    @Test
    void editSchedule() {
    }

    @Test
    void getWorkSchedule() {
    }

    @Test
    void statics() {
    }

    @Test
    void staticsDetail() {
    }

    @Test
    void getById() {
    }

    @Test
    void getPersonnel() {
    }

    @Test
    void deleteWorkSchedule() {
    }
}