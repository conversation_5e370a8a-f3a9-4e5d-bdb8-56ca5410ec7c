package com.unicom.swdx.module.oa.service.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmRestartDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskUpdateAssigneeReqDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.*;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import com.unicom.swdx.module.oa.dal.kingbase.WorkScheduleMapper;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.oaNotice.OaNoticeApi;
import com.unicom.swdx.module.system.api.sms.SmsQueApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.AsyncListenableTaskExecutor;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WeeklyWorkScheduleServiceImplTest {

    @Mock
    private DraftService mockDraftService;
    @Mock
    private OATaskService mockOaTaskService;
    @Mock
    private BpmTaskServiceApi mockBpmTaskServiceApi;
    @Mock
    private BpmProcessInstanceApi mockProcessInstanceApi;
    @Mock
    private WorkScheduleMapper mockWorkScheduleMapper;
    @Mock
    private BpmProcessInstanceApi mockBpmProcessInstanceApi;
    @Mock
    private AsyncListenableTaskExecutor mockTaskExecutor;
    @Mock
    private AdminUserApi mockUserApi;
    @Mock
    private DeptApi mockDeptApi;
    @Mock
    private SmsQueApi mockSmsQueApi;
    @Mock
    private OaNoticeApi mockOaNoticeApi;
    @Mock
    private SmsSendApi mockSmsSendService;
    @Mock
    private AdminUserApi mockAdminUserApi;
    @Mock
    private BpmTaskServiceApi mockTaskServiceApi;

    @InjectMocks
    private WeeklyWorkScheduleServiceImpl weeklyWorkScheduleServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        weeklyWorkScheduleServiceImplUnderTest.smsQueApi = mockSmsQueApi;
        weeklyWorkScheduleServiceImplUnderTest.oaNoticeApi = mockOaNoticeApi;
        weeklyWorkScheduleServiceImplUnderTest.smsSendService = mockSmsSendService;
        weeklyWorkScheduleServiceImplUnderTest.adminUserApi = mockAdminUserApi;
    }

    @Test
    void testSaveDraft() {
        // 初始化
        final ScheduleCreateReqVO createReqVO = new ScheduleCreateReqVO();
        createReqVO.setWorkSchedules(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));
        createReqVO.setIsDraft(false);
        //createReqVO.setCopyToName("copyToName");
        createReqVO.setDeptId(0L);
        createReqVO.setId(0L);
        createReqVO.setProcessInstanceId("processInstanceId");

        // 配置 AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // 运行测试
        final Long result = weeklyWorkScheduleServiceImplUnderTest.saveDraft(createReqVO);

        // 验证结果
        assertThat(result).isEqualTo(0L);
        verify(mockWorkScheduleMapper).insertBatch(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));
        verify(mockDraftService).save(DraftDO.builder()
                .category("category")
                .userId("userId")
                .itemId(0L)
                .build());
        verify(mockDraftService).update(any(LambdaUpdateWrapper.class));
        verify(mockWorkScheduleMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    void testCreateWorkScheduleProcess() {
        // Setup
        final ScheduleCreateReqVO createReqVO = new ScheduleCreateReqVO();
        createReqVO.setWorkSchedules(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));
        createReqVO.setIsDraft(false);
        //createReqVO.setCopyToName("copyToName");
        createReqVO.setDeptId(0L);
        createReqVO.setId(0L);
        createReqVO.setProcessInstanceId("processInstanceId");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult1 = CommonResult.success(adminUserRespDTO1);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult1);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO2 = new AdminUserRespDTO();
        adminUserRespDTO2.setId(0L);
        adminUserRespDTO2.setNickname("nickname");
        adminUserRespDTO2.setStatus(0);
        adminUserRespDTO2.setDeptId(0L);
        adminUserRespDTO2.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO2));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure BpmProcessInstanceApi.createProcessInstance(...).
        final BpmProcessInstanceCreateReqDTO reqDTO = new BpmProcessInstanceCreateReqDTO();
        reqDTO.setProcessDefinitionKey("OA-weeklyworkschedule");
        reqDTO.setVariables(new HashMap<>());
        reqDTO.setBusinessKey("businessKey");
        reqDTO.setProcessDefinitionName("processDefinitionName");
        when(mockProcessInstanceApi.createProcessInstance(0L, reqDTO)).thenReturn(CommonResult.success("value"));

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        when(mockOaTaskService.getDeptLeader(0L)).thenReturn("result");

        // Run the test
        final String result = weeklyWorkScheduleServiceImplUnderTest.createWorkScheduleProcess(createReqVO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockWorkScheduleMapper).insertBatch(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setTaskId(0L);
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        req.setTime("time");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOaNoticeApi).sendNotice("title", "message", Arrays.asList("value"));
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDraftService).deleteByItemId("13", 0L);
        verify(mockWorkScheduleMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockProcessInstanceApi).skipFirstTask("proinstanceId", LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO = new BpmTaskUpdateAssigneeReqDTO();
        reqVO.setId("id");
        reqVO.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO);
    }
    @Test
    void testRestartSchedule() {
        // Setup
        final ScheduleCreateReqVO reqVO = new ScheduleCreateReqVO();
        reqVO.setWorkSchedules(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));
        reqVO.setIsDraft(false);
        //reqVO.setCopyToName("copyToName");
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult1 = CommonResult.success(adminUserRespDTO1);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult1);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO2 = new AdminUserRespDTO();
        adminUserRespDTO2.setId(0L);
        adminUserRespDTO2.setNickname("nickname");
        adminUserRespDTO2.setStatus(0);
        adminUserRespDTO2.setDeptId(0L);
        adminUserRespDTO2.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO2));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        // Configure BpmTaskServiceApi.getTaskListByProcessInstanceId(...).
        final BpmTaskRespDTO bpmTaskRespDTO = new BpmTaskRespDTO();
        bpmTaskRespDTO.setId("id");
        bpmTaskRespDTO.setName("name");
        bpmTaskRespDTO.setDefinitionKey("definitionKey");
        bpmTaskRespDTO.setClaimTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmTaskRespDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BpmTaskRespDTO> bpmTaskRespDTOS = Arrays.asList(bpmTaskRespDTO);
        when(mockBpmTaskServiceApi.getTaskListByProcessInstanceId("processInstanceId")).thenReturn(bpmTaskRespDTOS);

        // Run the test
        weeklyWorkScheduleServiceImplUnderTest.restartSchedule(0L, reqVO);

        // Verify the results
        verify(mockWorkScheduleMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockWorkScheduleMapper).insertBatch(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setTaskId(0L);
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        req.setTime("time");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOaNoticeApi).sendNotice("title", "message", Arrays.asList("value"));
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDraftService).deleteByItemId("13", 0L);

        // Confirm BpmTaskServiceApi.restartProcess(...).
        final BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
        bpmRestartDTO.setLoginUserId(0L);
        bpmRestartDTO.setProcessInstanceId("processInstanceId");
        bpmRestartDTO.setTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bpmRestartDTO.setVariables(new HashMap<>());
        verify(mockBpmTaskServiceApi).restartProcess(bpmRestartDTO);

        // Confirm BpmTaskServiceApi.updateTaskAssignee(...).
        final BpmTaskUpdateAssigneeReqDTO reqVO1 = new BpmTaskUpdateAssigneeReqDTO();
        reqVO1.setId("id");
        reqVO1.setAssigneeUserId(0L);
        verify(mockBpmTaskServiceApi).updateTaskAssignee(0L, reqVO1);
    }
    @Test
    void testEditSchedule() {
        // Setup
        final ScheduleCreateReqVO reqVO = new ScheduleCreateReqVO();
        reqVO.setWorkSchedules(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));
        reqVO.setIsDraft(false);
        //reqVO.setCopyToName("copyToName");
        reqVO.setDeptId(0L);
        reqVO.setId(0L);
        reqVO.setProcessInstanceId("processInstanceId");

        // Configure WorkScheduleMapper.selectList(...).
        final List<WorkScheduleDO> workScheduleDOS = Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build());
        when(mockWorkScheduleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(workScheduleDOS);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult1 = CommonResult.success(adminUserRespDTO1);
        when(mockAdminUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult1);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO2 = new AdminUserRespDTO();
        adminUserRespDTO2.setId(0L);
        adminUserRespDTO2.setNickname("nickname");
        adminUserRespDTO2.setStatus(0);
        adminUserRespDTO2.setDeptId(0L);
        adminUserRespDTO2.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult = CommonResult.success(
                Arrays.asList(adminUserRespDTO2));
        when(mockAdminUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Run the test
        weeklyWorkScheduleServiceImplUnderTest.editSchedule(0L, reqVO);

        // Verify the results
        verify(mockDeptApi).sendMessageRefreshList(Arrays.asList("value"));
        verify(mockWorkScheduleMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockWorkScheduleMapper).insertBatch(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));

        // Confirm SmsQueApi.sendque(...).
        final SmsSendReq req = new SmsSendReq();
        req.setTaskId(0L);
        req.setType(0);
        req.setMessage("message");
        req.setPhone(Arrays.asList("value"));
        req.setSendTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        req.setTitle("title");
        req.setTime("time");
        verify(mockSmsQueApi).sendque(req);
        verify(mockSmsSendService).sendSingleSms("mobile", 0L, 0, "admin-sms-login-new", new HashMap<>());
        verify(mockOaNoticeApi).sendNotice("title", "message", Arrays.asList("value"));
        verify(mockTaskExecutor).submit(any(Runnable.class));
    }


    @Test
    void testGet() {
        // Setup
        final ScheduleRespVO expectedResult = new ScheduleRespVO();
        expectedResult.setWorkSchedules(Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build()));
        expectedResult.setIsDraft(false);
        //expectedResult.setCopyToName("copyToName");
        expectedResult.setDeptId(0L);
        expectedResult.setProcessInsatnceId("processInstanceId");
        expectedResult.setUserNickName("nickname");
        expectedResult.setDeptName("deptName");
        expectedResult.setResult(0);
        expectedResult.setTaskName("taskName");
        expectedResult.setOperateType(0);

        // Configure WorkScheduleMapper.selectList(...).
        final List<WorkScheduleDO> workScheduleDOS = Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build());
        when(mockWorkScheduleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(workScheduleDOS);

        // Configure DeptApi.getDepts(...).
        final DeptRespDTO deptRespDTO = new DeptRespDTO();
        deptRespDTO.setId(0L);
        deptRespDTO.setName("deptName");
        deptRespDTO.setParentId(0L);
        deptRespDTO.setLeaderUserId(0L);
        deptRespDTO.setStatus(0);
        final CommonResult<List<DeptRespDTO>> listCommonResult = CommonResult.success(Arrays.asList(deptRespDTO));
        when(mockDeptApi.getDepts(Arrays.asList(0L))).thenReturn(listCommonResult);

        // Configure AdminUserApi.getUsers(...).
        final AdminUserRespDTO adminUserRespDTO = new AdminUserRespDTO();
        adminUserRespDTO.setId(0L);
        adminUserRespDTO.setNickname("nickname");
        adminUserRespDTO.setStatus(0);
        adminUserRespDTO.setDeptId(0L);
        adminUserRespDTO.setMobile("mobile");
        final CommonResult<List<AdminUserRespDTO>> listCommonResult1 = CommonResult.success(
                Arrays.asList(adminUserRespDTO));
        when(mockUserApi.getUsers(Arrays.asList(0L))).thenReturn(listCommonResult1);

        // Configure AdminUserApi.getUser(...).
        final AdminUserRespDTO adminUserRespDTO1 = new AdminUserRespDTO();
        adminUserRespDTO1.setId(0L);
        adminUserRespDTO1.setNickname("nickname");
        adminUserRespDTO1.setStatus(0);
        adminUserRespDTO1.setDeptId(0L);
        adminUserRespDTO1.setMobile("mobile");
        final CommonResult<AdminUserRespDTO> adminUserRespDTOCommonResult = CommonResult.success(adminUserRespDTO1);
        when(mockUserApi.getUser(0L)).thenReturn(adminUserRespDTOCommonResult);

        // Configure DeptApi.getDept(...).
        final DeptRespDTO deptRespDTO1 = new DeptRespDTO();
        deptRespDTO1.setId(0L);
        deptRespDTO1.setName("deptName");
        deptRespDTO1.setParentId(0L);
        deptRespDTO1.setLeaderUserId(0L);
        deptRespDTO1.setStatus(0);
        final CommonResult<DeptRespDTO> deptRespDTOCommonResult = CommonResult.success(deptRespDTO1);
        when(mockDeptApi.getDept(0L)).thenReturn(deptRespDTOCommonResult);

        when(mockTaskServiceApi.getNeededTaskInfo("13", 0L, "processInstanceId", false, false))
                .thenReturn(new HashMap<>());

        // Run the test
        final ScheduleRespVO result = weeklyWorkScheduleServiceImplUnderTest.get(0L, "processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetByProcessInstanceId() {
        // Setup
        final WeeklyWorkScheduleDO expectedResult = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .userId(0L)
                .processInstanceId("processInstanceId")
                .deptId(0L)
                .workSchedules(Arrays.asList(WorkScheduleDO.builder()
                        .id(0L)
                        .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .weeklyWorkScheduleId(0L)
                        .location("location")
                        .activity("activity")
                        .filledIn("filledIn")
                        .personnelIds("personnelIds")
                        .deptIds("deptIds")
                        .deptNames(Arrays.asList("value"))
                        .personnelName("personnelName")
                        .personnel(new HashMap<>())
                        .deptNameString("deptNameString")
                        .mobileActivity("mobileActivity")
                        .notify(false)
                        .notifyTime("notifyTime")
                        .build()))
                .isDraft(false)
                //.copyTo("copyTo")
                .status(0)
                .build();

        // Run the test
        final WeeklyWorkScheduleDO result = weeklyWorkScheduleServiceImplUnderTest.getByProcessInstanceId(
                "processInstanceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetScheduleStatisticsList() {
        // Setup
        final SchedulePageReqVO reqVO = new SchedulePageReqVO();
        reqVO.setDeptId(0L);
        reqVO.setDeptName("deptName");
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setIdList(Arrays.asList(0L));

        final ScheduleStatisticRespVO scheduleStatisticRespVO = new ScheduleStatisticRespVO();
        scheduleStatisticRespVO.setDeptId(0L);
        scheduleStatisticRespVO.setDeptName("deptName");
        scheduleStatisticRespVO.setTotal(0);
        scheduleStatisticRespVO.setSchoolSchedule(0);
        scheduleStatisticRespVO.setExternalTrain(0);
        final PageResult<ScheduleStatisticRespVO> expectedResult = new PageResult<>(
                Arrays.asList(scheduleStatisticRespVO), 0L);

        // Run the test
        final PageResult<ScheduleStatisticRespVO> result = weeklyWorkScheduleServiceImplUnderTest.getScheduleStatisticsList(
                reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetScheduleStatisticsDetail() {
        // Setup
        final SchedulePageReqVO reqVO = new SchedulePageReqVO();
        reqVO.setDeptId(0L);
        reqVO.setDeptName("deptName");
        reqVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqVO.setIdList(Arrays.asList(0L));

        final ScheduleDetailRespVO scheduleDetailRespVO = new ScheduleDetailRespVO();
        scheduleDetailRespVO.setFilledIn("filledIn");
        scheduleDetailRespVO.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        scheduleDetailRespVO.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        scheduleDetailRespVO.setLocation("location");
        scheduleDetailRespVO.setActivity("activity");
        final PageResult<ScheduleDetailRespVO> expectedResult = new PageResult<>(Arrays.asList(scheduleDetailRespVO),
                0L);

        // Run the test
        final PageResult<ScheduleDetailRespVO> result = weeklyWorkScheduleServiceImplUnderTest.getScheduleStatisticsDetail(
                reqVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetScheduleDetailById() {
        // Setup
        final WorkScheduleDO expectedResult = WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build();

        // Configure WorkScheduleMapper.selectById(...).
        final WorkScheduleDO workScheduleDO = WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build();
        when(mockWorkScheduleMapper.selectById(0L)).thenReturn(workScheduleDO);

        // Run the test
        final WorkScheduleDO result = weeklyWorkScheduleServiceImplUnderTest.getScheduleDetailById(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testUpdateStatusById() {
        // Setup
        // Run the test
        weeklyWorkScheduleServiceImplUnderTest.updateStatusById(0L, 0);

        // Verify the results
    }

    @Test
    void testBatchUpdateStatusByIds() {
        // Setup
        // Run the test
        weeklyWorkScheduleServiceImplUnderTest.BatchUpdateStatusByIds(Arrays.asList(0L), 0);

        // Verify the results
    }

//    @Test
//    void testGetCopyTo() {
//        // Setup
//        // Run the test
//        final String result = weeklyWorkScheduleServiceImplUnderTest.getCopyTo(0L);
//
//        // Verify the results
//        assertThat(result).isEqualTo("copyTo");
//    }

    @Test
    void testGetProcessInstanceId() {
        // Setup
        // Run the test
        final String result = weeklyWorkScheduleServiceImplUnderTest.getProcessInstanceId(0L);

        // Verify the results
        assertThat(result).isEqualTo("processInstanceId");
    }

    @Test
    void testGetPersonnel() {
        // Setup
        final PageParam pageParam = new PageParam();
        pageParam.setPageNo(0);
        pageParam.setPageSize(0);

        final SchedulePersonnelRespVO schedulePersonnelRespVO = new SchedulePersonnelRespVO();
        schedulePersonnelRespVO.setUserId(0L);
        schedulePersonnelRespVO.setUserName("userName");
        schedulePersonnelRespVO.setUserNickName("userNickName");
        schedulePersonnelRespVO.setDeptName("deptName");
        schedulePersonnelRespVO.setDeptId("deptId");
        final PageResult<SchedulePersonnelRespVO> expectedResult = new PageResult<>(
                Arrays.asList(schedulePersonnelRespVO), 0L);

        // Configure WorkScheduleMapper.selectById(...).
        final WorkScheduleDO workScheduleDO = WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build();
        when(mockWorkScheduleMapper.selectById(0L)).thenReturn(workScheduleDO);

        // Configure WorkScheduleMapper.selectPersonnelInfo(...).
        final SchedulePersonnelRespVO schedulePersonnelRespVO1 = new SchedulePersonnelRespVO();
        schedulePersonnelRespVO1.setUserId(0L);
        schedulePersonnelRespVO1.setUserName("userName");
        schedulePersonnelRespVO1.setUserNickName("userNickName");
        schedulePersonnelRespVO1.setDeptName("deptName");
        schedulePersonnelRespVO1.setDeptId("deptId");
        final List<SchedulePersonnelRespVO> schedulePersonnelRespVOS = Arrays.asList(schedulePersonnelRespVO1);
        final PageParam pageParam1 = new PageParam();
        pageParam1.setPageNo(0);
        pageParam1.setPageSize(0);
        when(mockWorkScheduleMapper.selectPersonnelInfo(Arrays.asList(0L), pageParam1))
                .thenReturn(schedulePersonnelRespVOS);

        // Configure WorkScheduleMapper.selectPersonnelNum(...).
        final PageParam pageParam2 = new PageParam();
        pageParam2.setPageNo(0);
        pageParam2.setPageSize(0);
        when(mockWorkScheduleMapper.selectPersonnelNum(Arrays.asList(0L), pageParam2)).thenReturn(0L);

        // Run the test
        final PageResult<SchedulePersonnelRespVO> result = weeklyWorkScheduleServiceImplUnderTest.getPersonnel(0L,
                pageParam);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testDeleteSchedule() {
        // Setup
        // Configure WorkScheduleMapper.selectList(...).
        final List<WorkScheduleDO> workScheduleDOS = Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build());
        when(mockWorkScheduleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(workScheduleDOS);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return CompletableFuture.completedFuture(null);
        }).when(mockTaskExecutor).submit(any(Runnable.class));

        // Run the test
        weeklyWorkScheduleServiceImplUnderTest.deleteSchedule(0);

        // Verify the results
        verify(mockTaskExecutor).submit(any(Runnable.class));
        verify(mockDeptApi).sendMessageRefreshList(Arrays.asList("value"));
        verify(mockBpmProcessInstanceApi).removeProcess(0L, "processInstanceId");
    }

    @Test
    void testCancelMessage2() {
        // Setup
        final WeeklyWorkScheduleDO schedules = WeeklyWorkScheduleDO.builder()
                .id(0L)
                .userId(0L)
                .processInstanceId("processInstanceId")
                .deptId(0L)
                .workSchedules(Arrays.asList(WorkScheduleDO.builder()
                        .id(0L)
                        .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .weeklyWorkScheduleId(0L)
                        .location("location")
                        .activity("activity")
                        .filledIn("filledIn")
                        .personnelIds("personnelIds")
                        .deptIds("deptIds")
                        .deptNames(Arrays.asList("value"))
                        .personnelName("personnelName")
                        .personnel(new HashMap<>())
                        .deptNameString("deptNameString")
                        .mobileActivity("mobileActivity")
                        .notify(false)
                        .notifyTime("notifyTime")
                        .build()))
                .isDraft(false)
                //.copyTo("copyTo")
                .status(0)
                .build();

        // Configure WorkScheduleMapper.selectList(...).
        final List<WorkScheduleDO> workScheduleDOS = Arrays.asList(WorkScheduleDO.builder()
                .id(0L)
                .startDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .endDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .weeklyWorkScheduleId(0L)
                .location("location")
                .activity("activity")
                .filledIn("filledIn")
                .personnelIds("personnelIds")
                .deptIds("deptIds")
                .deptNames(Arrays.asList("value"))
                .personnelName("personnelName")
                .personnel(new HashMap<>())
                .deptNameString("deptNameString")
                .mobileActivity("mobileActivity")
                .notify(false)
                .notifyTime("notifyTime")
                .build());
        when(mockWorkScheduleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(workScheduleDOS);

        // Run the test
        weeklyWorkScheduleServiceImplUnderTest.cancelMessage(schedules);

        // Verify the results
        verify(mockDeptApi).sendMessageRefreshList(Arrays.asList("value"));
    }
}
