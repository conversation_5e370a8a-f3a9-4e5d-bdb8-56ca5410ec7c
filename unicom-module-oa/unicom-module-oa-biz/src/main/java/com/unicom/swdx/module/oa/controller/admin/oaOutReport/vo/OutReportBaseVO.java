package com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class OutReportBaseVO {

    /**
     * 讲学地点
     */
    @ApiModelProperty("外出地点")
    private String outAddress;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate endDate;

    /**
     * 时长
     */
    @ApiModelProperty("时长")
    private Integer duration;

    /**
     * 授课内容
     */
    @ApiModelProperty("外出内容")
    private String outContent;

    /**
     * 文件
     */
    @ApiModelProperty("文件")
    private List<String> files;

    /**
     * 签字
     */
    @ApiModelProperty("签字")
    private String sign;

    /**
     * 是否草稿
     */
    @ApiModelProperty("是否草稿")
    private Boolean isDraft;

    /**
     * 是否销假
     */
    @ApiModelProperty("是否销假")
    private Boolean isDealt;

    /**
     * 请假天数
     */
    @ApiModelProperty("请假天数")
    private Integer day;

    /**
     * 销假日期
     */
    @ApiModelProperty("销假日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime dealTime;

//    /**
//     * 返回工作日期
//     */
//    @ApiModelProperty("返回工作日期")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime returnWorkTime;

    @ApiModelProperty("所属部门")
    private Long deptId;
}
