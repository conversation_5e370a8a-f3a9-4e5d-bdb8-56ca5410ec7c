package com.unicom.swdx.module.oa.service.lecture;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmRestartDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskUpdateAssigneeReqDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureRespVO;
import com.unicom.swdx.module.oa.convert.LectureConvert;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.oa.enums.PostTypeEnum.*;

@Service
public class LectureServiceImpl extends ServiceImpl<LectureMapper, LectureDO> implements LectureService {

    /**
     * 外出讲学 对应的流程定义 KEY
     */
    public static final String PROCESS_KEY = "OA-outforgivelectures";

    @Resource
    private DraftService draftService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private PostApi postApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private BpmTaskServiceApi taskServiceApi;

    @Resource
    private LectureMapper lectureMapper;

    /**
     * key = 岗位标识，value = postType
     */
    private Map<String, Integer> postCodeMap;

    private List<String> postCodeList;

    @PostConstruct
    public void init() {
        postCodeMap = new HashMap<>();
        postCodeMap.put(VICE_CHANCELLOR.getPostCode(), 1); // 分管日常工作的副校长  1
        postCodeMap.put(SCHOOL_LEADER.getPostCode(), 2); //校院委成员  4
        postCodeMap.put(INSPECTOR_1.getPostCode(), 2); // 一级巡视员  4
        postCodeMap.put(INSPECTOR_2.getPostCode(), 2); // 二级巡视员  4
        postCodeMap.put(DIRECTOR.getPostCode(), 3); // 处室负责人 5
        postCodeMap.put(CLERK.getPostCode(), 4); // 教职工  6
        postCodeMap.put(VICE_DIRECTOR.getPostCode(), 4); // 副主任  4

        postCodeList = CollUtil.newArrayList(postCodeMap.keySet());
    }

    /**
     * 保存草稿
     * @param reqVO 外出讲学
     * @return 草稿id
     */
    @Override
    public Long saveDraft(LectureCreateReqVO reqVO) {
        LectureDO lecture = LectureConvert.INSTANCE.convert(reqVO);
        if (Objects.isNull(lecture.getId())) {
            lecture.setUserId(getLoginUserId());
            AdminUserRespDTO userDTO = userApi.getUser(lecture.getUserId()).getCheckedData();
            if(reqVO.getDeptId() != null) {
                lecture.setDeptId(reqVO.getDeptId());
            } else {
            lecture.setDeptId(userDTO.getDeptId());
            }
            lecture.setIsDraft(true);
            baseMapper.insert(lecture);

            // 写入草稿表
            DraftDO draftDO = new DraftDO();
            draftDO.setCategory(OACategoryConstants.LECTURE);
            draftDO.setUserId(lecture.getUserId().toString());
            draftDO.setItemId(lecture.getId());
            draftDO.setUpdateTime(LocalDateTime.now());
            draftService.save(draftDO);
        } else {
            baseMapper.updateById(lecture);
            draftService.update(new LambdaUpdateWrapper<DraftDO>().eq(DraftDO::getItemId,lecture.getId())
                    .set(DraftDO::getUpdateTime,LocalDateTime.now()));
        }

        return lecture.getId();
    }

    @Override
    public Integer getPostType(Long userId) {
        PostRespDTO post = postApi.getMinSortPostByUser(userId, postCodeList);
        if (Objects.isNull(post)) {
            throw exception(POST_IS_NULL);
        }
        List<Integer> list = Arrays.asList(1, 2, 3, 4);
        if(!list.contains(postCodeMap.get(post.getCode()))){
            throw exception(POST_IS_NULL);
        }
        return postCodeMap.get(post.getCode());
    }
    @Override
    public Map<String, LocalDate> getDateById(Long id) {
        Map<String,LocalDate> map = new HashMap<>();
        LectureDO lectureDO = lectureMapper.selectById(id);
        // map.put("id",outReportDO.getId().toString());
        map.put("startDate",lectureDO.getStartDate());
        map.put("endDate",lectureDO.getEndDate());
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLectureProcess(LectureCreateReqVO reqVO) {
        // 获取岗位类型
        Integer postType = getPostType(getLoginUserId());
        // 校验已提交的流程是否有重叠的时间区间
        List<LectureDO> lectureDOS = baseMapper.selectListBetweenDate(getLoginUserId(),reqVO.getStartDate(), reqVO.getEndDate());
        if (CollUtil.isNotEmpty(lectureDOS)) {
            throw exception(LECTURE_TIME_OVERLAP);
        }

        //更新签名
        lectureMapper.insertImage(WebFrameworkUtils.getLoginUserId(),reqVO.getSign());

        LectureDO lecture = LectureConvert.INSTANCE.convert(reqVO);
        long day = Duration.between(LocalDateTime.of(reqVO.getStartDate(), LocalTime.MIN),
                LocalDateTime.of(reqVO.getEndDate(), LocalTime.MIN)).toDays()+1;
        lecture.setDuration((int) day);
        lecture.setIsDraft(false);
        lecture.setUserId(getLoginUserId());
        AdminUserRespDTO userDTO = userApi.getUser(lecture.getUserId()).getCheckedData();
        if(reqVO.getDeptId() != null) {
            lecture.setDeptId(reqVO.getDeptId());
        } else {
            lecture.setDeptId(userDTO.getDeptId());
        }
        lecture.setLaunchTime(LocalDateTime.now());
        if (Objects.isNull(lecture.getId())) {
            // 新增
            this.save(lecture);
        } else {
            // id不为空表示从草稿箱发起，可能是未发起过的草稿，也可能是撤回后的草稿
            // 删除草稿记录
            draftService.deleteByItemId(OACategoryConstants.LECTURE, lecture.getId());
            if(Objects.isNull(this.getById(lecture.getId()).getProcessInstanceId())){
                // 未发起的草稿，直接修改
                this.updateById(lecture);
            }else {
                // 撤回后的草稿，新增一条新的
                lecture.setId(null);
                this.save(lecture);
            }
        }

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        processInstanceVariables.put("postType", postType);
        String processInstanceId = processInstanceApi.createProcessInstance(getLoginUserId(),
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(lecture.getId()))).getCheckedData();

        // 将工作流的编号，更新到 OA 外出讲学表单中
        baseMapper.updateById(new LectureDO().setId(lecture.getId()).setProcessInstanceId(processInstanceId));

        LocalDateTime time = LocalDateTime.now().withSecond(0);
        if(processInstanceApi.skipFirstTask(processInstanceId, time).getCheckedData()){
            //判断下一步是否为选择审批人
            if(postType==2 || postType==3){
                OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
                pick.setUserIds(reqVO.getUserIds());
                pick.setChargeLeaderSeq(reqVO.getChargeLeaderSeq());
                oaTaskService.pickApprovals(pick, time.plusSeconds(1L));
            }else if(postType==4){
                //不需要选择审批人时自动设置部门负责人审批
                //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
                if(reqVO.getDeptId() != null) {
                    Long defaultLeader = deptApi.getDept(userDTO.getDeptId()).getCheckedData().getLeaderUserId();
                    List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
                    BpmTaskRespDTO task = bpmTasks.get(1);
                    Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                    bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                            .setAssigneeUserId(currentLeader)
                            .setId(task.getId()));
                }
                //下一步为部门负责人审批
                String deptLeaderName = oaTaskService.getDeptLeader(lecture.getDeptId());
                if(StrUtil.isEmpty(deptLeaderName)){
                    throw exception(APPROVAL_NOT_FOUND);
                }
            }
        }

//        return lecture.getId();
        return processInstanceId;
    }

    @Override
    public LectureDO getByProcessInstanceId(String processInstanceId) {
        return baseMapper.selectOne(LectureDO::getProcessInstanceId,processInstanceId);
    }

    @Override
    public LectureDO get(Long id, String processInstanceId) {
        if (Objects.nonNull(id)) {
            return this.getById(id);
        } else if (StrUtil.isNotBlank(processInstanceId)) {
            return getByProcessInstanceId(processInstanceId);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartLecture(Long loginUserId, LectureCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        LectureDO lecture = LectureConvert.INSTANCE.convert(reqVO);
        long day = Duration.between(LocalDateTime.of(reqVO.getStartDate(), LocalTime.MIN),
                LocalDateTime.of(reqVO.getEndDate(), LocalTime.MIN)).toDays()+1;
        lecture.setDuration((int) day);
        lecture.setIsDraft(false);
        lecture.setUserId(loginUserId);
        AdminUserRespDTO userDTO = userApi.getUser(lecture.getUserId()).getCheckedData();
        if (reqVO.getDeptId() != null) {
            lecture.setDeptId(reqVO.getDeptId());
        } else {
            lecture.setDeptId(userDTO.getDeptId());
        }

        // 校验已提交的流程是否有重叠的时间区间
        List<LectureDO> lectureDOS = baseMapper.selectListBetweenDate(loginUserId,reqVO.getStartDate(), reqVO.getEndDate());
        if (CollUtil.isNotEmpty(lectureDOS)) {
            throw exception(LECTURE_TIME_OVERLAP);
        }

        if (Objects.isNull(lecture.getId())) {
            throw exception(LECTURE_NOT_EXIST);
        } else {
            // 修改
            this.updateById(lecture);
            if(reqVO.getIsDraft()) {
                draftService.deleteByItemId(OACategoryConstants.LECTURE, lecture.getId());
            }
            // 2.设置一下流程流转参数variables，参数没有改变的不用设置
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 3.重新发起流程
            String processInstanceId = reqVO.getProcessInstanceId();
            if(Objects.isNull(reqVO.getProcessInstanceId())){
                processInstanceId=this.getById(reqVO.getId()).getProcessInstanceId();
            }

            BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
            bpmRestartDTO.setLoginUserId(loginUserId);
            bpmRestartDTO.setProcessInstanceId(processInstanceId);
            bpmRestartDTO.setVariables(processInstanceVariables);
            LocalDateTime time = LocalDateTime.now();
            bpmRestartDTO.setTime(time);
            if(bpmTaskServiceApi.restartProcess(bpmRestartDTO).getCheckedData()){
                // 获取岗位类型
                Integer postType = getPostType(loginUserId);
                //判断下一步是否为选择审批人
                if(postType==2 || postType==3){
                    OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                    pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
                    pick.setUserIds(reqVO.getUserIds());
                    pick.setChargeLeaderSeq(reqVO.getChargeLeaderSeq());
                    oaTaskService.pickApprovals(pick, time.plusSeconds(1L));
                }else {
                    //不需要选择审批人时自动设置部门负责人审批
                    //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
                    if(reqVO.getDeptId() != null) {
                        Long defaultLeader = deptApi.getDept(userApi.getUser(loginUserId).getCheckedData().getDeptId()).getCheckedData().getLeaderUserId();
                        List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(reqVO.getProcessInstanceId());
                        //重新发起以后最新的节点
                        BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
                        Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                        bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                                .setAssigneeUserId(currentLeader)
                                .setId(task.getId()));
                    }
                }
            }
        }
    }

    @Override
    public String getImage(Long id) {
        String sign = baseMapper.getImage(id);
        return sign;
    }

    @Override
    public LectureRespVO getResp(Long id, String processInstanceId) {
        LectureDO lectureDO = this.get(id,processInstanceId);
        LectureRespVO respVO = LectureConvert.INSTANCE.convert(lectureDO);
        try {
            respVO.setUserNickName(userApi.getUser(lectureDO.getUserId()).getCheckedData().getNickname());
        }catch (Exception e){
            e.printStackTrace();
        }

        respVO.setDeptName(deptApi.getDept(lectureDO.getDeptId()).getCheckedData().getName());
        if(StrUtil.isNotEmpty(lectureDO.getProcessInstanceId())){
            Map<String, String> taskInfo = taskServiceApi.getNeededTaskInfo(OACategoryConstants.LECTURE, getLoginUserId(), processInstanceId, null, null);
            respVO.setTaskName(taskInfo.get("taskName"));
            //返回给前端的result和status字段，统一用processInstanceStatusEnum
            if (StrUtil.isNotBlank(taskInfo.get("status"))) {
                respVO.setResult(Integer.parseInt(taskInfo.get("status")));
            }
            if(StrUtil.isNotBlank(taskInfo.get("operateType"))){
                respVO.setOperateType(Integer.parseInt(taskInfo.get("operateType")));
            }
        }
        return respVO;
    }
}
