package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.util.List;

/**
 * 一周工作安排
 */
@TableName(value = "oa_weekly_work_schedule",autoResultMap = true)
@Data
@KeySequence("oa_weekly_work_schedule_id_seq")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeeklyWorkScheduleDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 填报人的用户编号
     *
     * 关联 AdminUserDO 的 id 属性
     */
    @TableField(value = "launch_user_id")
    private Long userId;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 填报人的部门编号
     *
     * 关联 AdminUserDO 的 deptId 属性
     */
    private Long deptId;

    /**
     * 工作安排
     */
    @TableField(exist = false)
    private List<WorkScheduleDO> workSchedules;


    /**
     * 是否草稿
     */
    private Boolean isDraft;


    /**
     * 抄送人员
     */
    private String copyTo;

    /**
     * 汇总状态
     */
    private Integer status;
}
