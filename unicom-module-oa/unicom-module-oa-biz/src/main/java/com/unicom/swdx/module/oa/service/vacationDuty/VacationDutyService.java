package com.unicom.swdx.module.oa.service.vacationDuty;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.*;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.*;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaTenantDTO;

import java.util.List;

/**
 * <AUTHOR>
 */

public interface VacationDutyService extends IService<VacationDutyDO> {

    Long saveDraft(DutyCreateReqVO createReqVO);

    Integer getPostType(Long userId);

    String createVacationDutyProcess(DutyCreateReqVO createReqVO);

    DutyRespVO get(Long id, String processInstanceId);

    DutyRespVO getForEdit(Long id, String processInstanceId);

//    VacationDutyDO getByProcessInstanceId(String processInstanceId);

//    PageResult<ScheduleStatisticRespVO> getScheduleStatisticsList(SchedulePageReqVO reqVO);

//    PageResult<ScheduleDetailRespVO> getScheduleStatisticsDetail(SchedulePageReqVO reqVO);

//    WorkScheduleDO getScheduleDetailById(Long id);

    void restartDuty(Long loginUserId, DutyCreateReqVO reqVO);

    void editDuty(Long loginUserId, DutyCreateReqVO reqVO);

    void end(List<OARemoveReqVO> oaRemoveReqVOList);

    SummaryDutyRespVO getDutyList();

//    void updateStatusById(Long id, Integer status);

//    void BatchUpdateStatusByIds(List<Long> ids, Integer status);

//    String getCopyTo(Long scheduleId);
//
//    String getProcessInstanceId(Long scheduleId);
//
    List<DutyPersonnelRespVO> getPersonnel(Long deptId);

    PageResult<DutyPersonnelRespVO> getPagePersonnel(Long deptId, String userName, Integer pageNum, Integer pageSize);

    List<DutyPersonnelRespVO> getPersonnelLeader();

    List<DutyFormBaseVO> getPreviewData(List<Long> idList, String dutyType);

    DutyFormTotalVO getAllPreview(String ids);

    Integer saveLeaderForm(List<VacationDutyFormLeaderDO> createReqVO, String ids);

    Integer cancelLeaderForm(String ids);

    List<DutyFormExcelRespVO> importDutyForm(List<DutyFormExcelVO> list, Long deptId);

    Long getDeptIdByProcessInstanceId(String processInstanceId);

    VacationDutyDO getByProcessInstanceId(String processInstanceId);

//
//    void deleteSchedule(Integer id);
//
//    void cancelMessage(WeeklyWorkScheduleDO schedules);
}
