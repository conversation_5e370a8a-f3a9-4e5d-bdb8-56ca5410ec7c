package com.unicom.swdx.module.oa.service;

import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;

import java.time.LocalDateTime;

public interface OATaskService {

    String getTaskId(String processInstanceId);

    void pickApprovals(OApickApprovalsReqVO pickApprovalsReqVO, LocalDateTime time);

    void end(OARemoveReqVO reqVO);

    String getDeptLeaderName(Long userId);

    String getDeptLeader(Long deptId);

}
