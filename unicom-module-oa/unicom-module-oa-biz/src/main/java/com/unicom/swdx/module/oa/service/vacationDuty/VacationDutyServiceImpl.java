package com.unicom.swdx.module.oa.service.vacationDuty;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.*;
import com.unicom.swdx.module.oa.convert.VacationDutyConvert;
import com.unicom.swdx.module.oa.convert.WeeklyWorkScheduleConvert;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.VacationDutyFormMapper;
import com.unicom.swdx.module.oa.dal.kingbase.VacationDutyMapper;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.enums.ScheduleMessageEnum;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.api.oaNotice.OaNoticeApi;
import com.unicom.swdx.module.system.api.sms.SmsQueApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import jodd.util.StringUtil;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.oa.enums.PostTypeEnum.*;

@Service
public class VacationDutyServiceImpl extends ServiceImpl<VacationDutyMapper, VacationDutyDO> implements VacationDutyService {

    /**
     * 一周工作安排 对应的流程定义 KEY
     */
    public static final String PROCESS_KEY = "vacations_duty";

    @Resource
    private DraftService draftService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private VacationDutyFormMapper vacationDutyFormMapper;

    @Resource
    private VacationDutyMapper vacationDutyMapper;

    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;

    @Resource
    private AsyncListenableTaskExecutor taskExecutor;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    SmsQueApi smsQueApi;

    @Resource
    OaNoticeApi oaNoticeApi;

    @Resource
    public SmsSendApi smsSendService;

    @Resource
    public AdminUserApi adminUserApi;

    @Resource
    private BpmTaskServiceApi taskServiceApi;

    private List<String> postCodeList;

    private Map<String, Integer> postCodeMap;

    @Resource
    private PostApi postApi;


    @PostConstruct
    public void init() {
        postCodeMap = new HashMap<>();


        postCodeMap.put(CLERK.getPostCode(), 1); // 教职工  1
        postCodeMap.put(DIRECTOR.getPostCode(), 2);
        postCodeMap.put(SCHOOL_LEADER.getPostCode(), 3);
        postCodeMap.put(OA_SECRETARY.getPostCode(), 4);

        postCodeList = CollUtil.newArrayList(postCodeMap.keySet());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDraft(DutyCreateReqVO createReqVO) {
        VacationDutyDO vacationDutyDo = VacationDutyConvert.INSTANCE.convert(createReqVO);
        if (Objects.isNull(createReqVO.getId())) {
            vacationDutyDo.setUserId(getLoginUserId());
            if(createReqVO.getVacationDutyFormList().isEmpty()){
                throw exception(VACATION_DUTY_LENGTH_ERROR);
            }
            else{
                vacationDutyDo.setDeptId(Long.valueOf(createReqVO.getVacationDutyFormList().get(0).getDeptId()));
            }
            vacationDutyDo.setIsDraft(true);
            baseMapper.insert(vacationDutyDo);

            List<VacationDutyFormDO> vacationDutyFormList = createReqVO.getVacationDutyFormList();
            for (VacationDutyFormDO vacationDutyForm : vacationDutyFormList) {
                vacationDutyForm.setVacationDutyId(vacationDutyDo.getId());
            }
            vacationDutyFormMapper.insertBatch(vacationDutyFormList);
            // 写入草稿表
            DraftDO draftDO = new DraftDO();
            draftDO.setCategory(OACategoryConstants.DUTY);
            draftDO.setUserId(vacationDutyDo.getUserId().toString());
            draftDO.setItemId(vacationDutyDo.getId());
            draftDO.setUpdateTime(LocalDateTime.now());
            draftService.save(draftDO);
        } else {
            baseMapper.updateById(vacationDutyDo);
            draftService.update(new LambdaUpdateWrapper<DraftDO>().eq(DraftDO::getItemId,vacationDutyDo.getId())
                    .set(DraftDO::getUpdateTime,LocalDateTime.now()));
            LambdaQueryWrapper<VacationDutyFormDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, createReqVO.getId());
            vacationDutyFormMapper.delete(queryWrapper);
            List<VacationDutyFormDO> vacationDutyFormList = createReqVO.getVacationDutyFormList();
            for (VacationDutyFormDO vacationDutyForm : vacationDutyFormList) {
                vacationDutyForm.setVacationDutyId(vacationDutyDo.getId());
                vacationDutyForm.setId(null);
            }
            vacationDutyFormMapper.insertBatch(vacationDutyFormList);
        }

        return vacationDutyDo.getId();
    }

    @Override
    public Integer getPostType(Long userId) {
        Integer postType = null;
        Boolean isDeptLeader = deptApi.isLeaderUser(userId).getData();
        PostRespDTO post = postApi.getMinSortPostByUser(userId, postCodeList);

        if (Objects.isNull(post)) {
            //判断是否是部门负责人
            if(isDeptLeader){
                postType = 3;
            }else {
                throw exception(POST_ERROR);
            }
        }else {
            if(isDeptLeader){
                postType = Math.max(3,postCodeMap.get(post.getCode()));
            }else {
                postType = postCodeMap.get(post.getCode());
            }
        }
        List<Integer> list = Arrays.asList(1, 2, 3, 4 ,5);
        if(!list.contains(postType)){
            throw exception(POST_ERROR);
        }
        return postType;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createVacationDutyProcess(DutyCreateReqVO createReqVO) {
        AdminUserRespDTO loginUser = userApi.getUser(getLoginUserId()).getCheckedData();
        DeptRespDTO deptRespDTO = deptApi.getDept(loginUser.getDeptId()).getCheckedData();
        if (Objects.isNull(deptRespDTO.getLeaderUserId())) {
            throw exception(LEADER_IS_NULL);
        }
        VacationDutyDO vacationDutyDO = VacationDutyConvert.INSTANCE.convert(createReqVO);
        vacationDutyDO.setIsDraft(false);
        vacationDutyDO.setUserId(getLoginUserId());

        if(createReqVO.getVacationDutyFormList().size() == 0){
            throw exception(VACATION_DUTY_LENGTH_ERROR);
        }
        else{
            vacationDutyDO.setDeptId(Long.valueOf(createReqVO.getVacationDutyFormList().get(0).getDeptId()));
        }
        if (Objects.isNull(vacationDutyDO.getId())) {
            // 新增
            this.save(vacationDutyDO);
            List<VacationDutyFormDO> vacationDutyFormList = createReqVO.getVacationDutyFormList();
            for (VacationDutyFormDO vacationDutyForm : vacationDutyFormList) {
                vacationDutyForm.setVacationDutyId(vacationDutyDO.getId());
                vacationDutyForm.setDeptId(vacationDutyDO.getDeptId().toString());
            }
            vacationDutyFormMapper.insertBatch(vacationDutyFormList);
        } else {
            // id不为空表示从草稿箱发起，可能是未发起过的草稿，也可能是撤回后的草稿
            // 删除草稿记录
            draftService.deleteByItemId(OACategoryConstants.DUTY, vacationDutyDO.getId());
            if(Objects.isNull(this.getById(vacationDutyDO.getId()).getProcessInstanceId())){
                // 未发起的草稿，直接修改
                this.updateById(vacationDutyDO);
            }else {
                // 撤回后的草稿，新增一条新的
                vacationDutyDO.setId(null);
                this.save(vacationDutyDO);
            }
            LambdaQueryWrapper<VacationDutyFormDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, createReqVO.getId());
            vacationDutyFormMapper.delete(queryWrapper);
            List<VacationDutyFormDO> vacationDutyFormList = createReqVO.getVacationDutyFormList();
            for (VacationDutyFormDO vacationDutyFormDO : vacationDutyFormList) {
                vacationDutyFormDO.setVacationDutyId(vacationDutyDO.getId());
                vacationDutyFormDO.setId(null);
                vacationDutyFormDO.setDeptId(vacationDutyDO.getDeptId().toString());
            }
            vacationDutyFormMapper.insertBatch(vacationDutyFormList);
        }

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(getLoginUserId(),
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(vacationDutyDO.getId()))).getCheckedData();
        processInstanceApi.skipFirstTask(processInstanceId, LocalDateTime.now());

        // 将工作流的编号，更新到 OA 一周工作安排表单中
        baseMapper.updateById(new VacationDutyDO().setId(vacationDutyDO.getId()).setProcessInstanceId(processInstanceId));

        //不需要选择审批人时自动设置部门负责人审批
        //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
        if(createReqVO.getDeptId() != null) {
            //当前登录用户的默认部门的负责人
            Long defaultLeader = deptApi.getDept(loginUser.getDeptId()).getCheckedData().getLeaderUserId();
            List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
            BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size()-1);
            //流程实际选择的部门的负责人
            Long currentLeader = deptApi.getDept(createReqVO.getDeptId()).getCheckedData().getLeaderUserId();
            bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                    .setAssigneeUserId(currentLeader)
                    .setId(task.getId()));
        }

        String deptLeaderName = oaTaskService.getDeptLeaderName(vacationDutyDO.getUserId());
        if(StrUtil.isEmpty(deptLeaderName)){
            throw exception(APPROVAL_NOT_FOUND);
        }

//        return weeklyWorkScheduleDO.getId();
        return processInstanceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartDuty(Long loginUserId, DutyCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        VacationDutyDO vacationDutyDO = VacationDutyConvert.INSTANCE.convert(reqVO);
        vacationDutyDO.setIsDraft(false);
        vacationDutyDO.setUserId(loginUserId);
        vacationDutyDO.setDeptId(reqVO.getDeptId());

        if (Objects.isNull(vacationDutyDO.getId())) {
            throw exception(VACATION_DUTY_NOT_EXIST);
        } else {
            // 修改
            this.updateById(vacationDutyDO);
            LambdaQueryWrapper<VacationDutyFormDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, reqVO.getId());
            vacationDutyFormMapper.delete(queryWrapper);
            List<VacationDutyFormDO> vacationDutyFormList = reqVO.getVacationDutyFormList();
            for (VacationDutyFormDO vacationDutyFormDO : vacationDutyFormList) {
                vacationDutyFormDO.setVacationDutyId(vacationDutyDO.getId());
                vacationDutyFormDO.setId(null);
                vacationDutyFormDO.setDeptId(String.valueOf(vacationDutyDO.getDeptId()));
            }
            vacationDutyFormMapper.insertBatch(vacationDutyFormList);

            if(reqVO.getIsDraft()) {
                draftService.deleteByItemId(OACategoryConstants.DUTY, vacationDutyDO.getId());
            }
            // 2.设置一下流程流转参数variables，参数没有改变的不用设置
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 3.重新发起流程
            String processInstanceId = reqVO.getProcessInstanceId();
            if(Objects.isNull(reqVO.getProcessInstanceId())){
                processInstanceId=this.getById(reqVO.getId()).getProcessInstanceId();
            }
            BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
            bpmRestartDTO.setLoginUserId(loginUserId);
            bpmRestartDTO.setProcessInstanceId(processInstanceId);
            bpmRestartDTO.setVariables(processInstanceVariables);
            LocalDateTime time = LocalDateTime.now();
            bpmRestartDTO.setTime(time);
            bpmTaskServiceApi.restartProcess(bpmRestartDTO);
            //不需要选择审批人时自动设置部门负责人审批
            //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
            if(reqVO.getDeptId() != null) {
                Long defaultLeader = deptApi.getDept(userApi.getUser(loginUserId).getCheckedData().getDeptId()).getCheckedData().getLeaderUserId();
                List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(reqVO.getProcessInstanceId());
                //重新发起以后最新的节点
                BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
                Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                        .setAssigneeUserId(currentLeader)
                        .setId(task.getId()));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDuty(Long loginUserId, DutyCreateReqVO reqVO) {
        VacationDutyDO vacationDutyDO = VacationDutyConvert.INSTANCE.convert(reqVO);
        String processInstanceId = reqVO.getProcessInstanceId();
        LambdaQueryWrapper<VacationDutyFormDO> queryWrapper = new LambdaQueryWrapper<>();
        if(Objects.nonNull(reqVO.getId())) {
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, reqVO.getId());
            vacationDutyFormMapper.delete(queryWrapper);
        } else if(Objects.nonNull(reqVO.getProcessInstanceId())) {
            Long id = baseMapper.getByProcessInstanceId(processInstanceId);
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, id);
            vacationDutyFormMapper.delete(queryWrapper);
            vacationDutyDO.setId(id);
        }
        baseMapper.updateById(vacationDutyDO);
        List<VacationDutyFormDO> vacationDutyFormList = reqVO.getVacationDutyFormList();
        for (VacationDutyFormDO dutyFormDO : vacationDutyFormList) {
            dutyFormDO.setVacationDutyId(vacationDutyDO.getId());
            dutyFormDO.setDeptId(vacationDutyDO.getDeptId().toString());
        }
        vacationDutyFormMapper.insertBatch(vacationDutyFormList);
    }

    @Override
    public void end(List<OARemoveReqVO> oaRemoveReqVOList){
        for(OARemoveReqVO oaTaskReqVO : oaRemoveReqVOList){
            endVacation(oaTaskReqVO);
        }
    }

    @Override
    public DutyRespVO get(Long id, String processInstanceId) {
        VacationDutyDO vacationDutyDO = new VacationDutyDO();
        List<VacationDutyFormDO> vacationDutyFormListSit = new ArrayList<>();
        List<VacationDutyFormDO> vacationDutyFormListDut = new ArrayList<>();
        DutySignatureVO dutySignature = new DutySignatureVO();
        List<Map<String,Object>> logList = new ArrayList<>();
        // 分别获取坐班值班的数据
        if (Objects.nonNull(id)) {
            vacationDutyDO = this.getById(id);
            vacationDutyFormListSit = vacationDutyFormMapper.getVacationDutyById(id, "1");
            vacationDutyFormListDut = vacationDutyFormMapper.getVacationDutyById(id, "2");

            // 获取签名信息
            dutySignature = vacationDutyFormMapper.getMajorSigntureInfoById(id);
        } else if (StrUtil.isNotBlank(processInstanceId)) {
            vacationDutyDO = getByProcessInstanceId(processInstanceId);
            LambdaQueryWrapper<VacationDutyFormDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, vacationDutyDO.getId());
            vacationDutyFormListSit = vacationDutyFormMapper.getVacationDutyByInstanceId(processInstanceId, "1");
            vacationDutyFormListDut = vacationDutyFormMapper.getVacationDutyByInstanceId(processInstanceId, "2");

            // 获取签名信息
            dutySignature = vacationDutyFormMapper.getMajorSigntureInfo(processInstanceId);

            logList = bpmTaskServiceApi.getTaskLogByProcessInstanceId(processInstanceId);
        }

        // 合并表单数据
        List<DutyFormBaseVO> dutyFormSitList = combineForm(vacationDutyFormListSit);
        List<DutyFormBaseVO> dutyFormDepList = combineForm(vacationDutyFormListDut);
        List<DutyFormBaseVO> dutyFormBaseList = new ArrayList<>(dutyFormSitList);
        dutyFormBaseList.addAll(dutyFormDepList);

        DutyRespVO respVO = VacationDutyConvert.INSTANCE.convert(vacationDutyDO);
        respVO.setDutyFormSitList(dutyFormSitList);
        respVO.setDutyFormDepList(dutyFormDepList);
        respVO.setDutyFormList(dutyFormBaseList);

        // 判断是否需要显示主管签名
        if(!logList.isEmpty()){
            try{
                Map<String,Object> logData = logList.get(logList.size() - 1);
                if(dutySignature != null && !StringUtil.equals("发起审批", logData.get("taskName").toString()) && !StringUtil.equals("部门负责人审批", logData.get("taskName").toString())){
                    respVO.setMajorHandSignature(dutySignature.getMajorHandSignature());
                    respVO.setMajorEndTime(dutySignature.getMajorEndTime());
                }
            }
            catch (Exception e){
                respVO.setMajorHandSignature(null);
                respVO.setMajorEndTime(null);
            }

            // 特殊判断驳回后结束，不显示签名
            try{
                Map<String,Object> logData = logList.get(logList.size() - 1);
                Map<String,Object> logData2 = logList.get(logList.size() - 2);
                if(dutySignature != null && StringUtil.equals("结束", logData.get("taskName").toString()) && (int)logData2.get("result") == 3){
                    respVO.setMajorHandSignature(null);
                    respVO.setMajorEndTime(null);
                }
            }
            catch (Exception e){
                // 此处选择不处理。原因：针对驳回后结束做特殊判断，若报错则表明不属于该类型。
            }

        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        respVO.setLaunchTime(vacationDutyDO.getCreateTime().format(formatter));
        respVO.setProcessInstanceId(vacationDutyDO.getProcessInstanceId());
        respVO.setUserNickName(userApi.getUser(vacationDutyDO.getUserId()).getCheckedData().getNickname());
        respVO.setDeptName(deptApi.getDept(vacationDutyDO.getDeptId()).getCheckedData().getName());
        if (StrUtil.isNotEmpty(vacationDutyDO.getProcessInstanceId())) {
            Map<String, String> taskInfo = taskServiceApi.getNeededTaskInfo(OACategoryConstants.DUTY, getLoginUserId(), vacationDutyDO.getProcessInstanceId(), null, null);
            respVO.setTaskName(taskInfo.get("taskName"));
            if(StringUtil.isNotBlank(taskInfo.get("status"))){
                respVO.setResult(Integer.parseInt(taskInfo.get("status")));
            }
            if(StringUtil.isNotBlank(taskInfo.get("operateType"))){
                respVO.setOperateType(Integer.parseInt(taskInfo.get("operateType")));
            }
        }
        return respVO;
    }

    public SummaryDutyRespVO getDutyList(){
        Long userId = getLoginUserId();
        //尚未进行汇总的填报单信息
        List<DutyRespVO> myToDo = baseMapper.getMyToDo(userId);

        myToDo = myToDo.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(DutyRespVO::getProcessInstanceId)
                        .thenComparing(DutyRespVO::getType, Comparator.nullsLast(String::compareTo)))), ArrayList::new));
        for (DutyRespVO summary : myToDo) {
            summary.setDeptName(deptApi.getDept(summary.getDeptId()).getCheckedData().getName());
            summary.setUserNickName(userApi.getUser(summary.getUserId()).getCheckedData().getNickname());
        }
        SummaryDutyRespVO summaryDutyRespVO = new SummaryDutyRespVO();
        myToDo.sort(Comparator.comparing(DutyRespVO::getCreateTime).reversed());
        summaryDutyRespVO.setSummary(myToDo);
        return summaryDutyRespVO;
    }

    @Override
    public DutyRespVO getForEdit(Long id, String processInstanceId) {
        VacationDutyDO vacationDutyDO = new VacationDutyDO();
        List<VacationDutyFormDO> vacationDutyFormListSit = new ArrayList<>();
        if (Objects.nonNull(id)) {
            vacationDutyDO = this.getById(id);
            vacationDutyFormListSit = vacationDutyFormMapper.getVacationDutyFormById(id);
        } else if (StrUtil.isNotBlank(processInstanceId)) {
            vacationDutyDO = getByProcessInstanceId(processInstanceId);
            LambdaQueryWrapper<VacationDutyFormDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VacationDutyFormDO::getVacationDutyId, vacationDutyDO.getId());
            vacationDutyFormListSit = vacationDutyFormMapper.getVacationDutyFormByInstanceId(processInstanceId);
        }
        DutyRespVO respVO = VacationDutyConvert.INSTANCE.convert(vacationDutyDO);
        respVO.setVacationDutyFormList(vacationDutyFormListSit);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        respVO.setLaunchTime(vacationDutyDO.getCreateTime().format(formatter));
        respVO.setProcessInstanceId(vacationDutyDO.getProcessInstanceId());
        respVO.setUserNickName(userApi.getUser(vacationDutyDO.getUserId()).getCheckedData().getNickname());
        respVO.setDeptName(deptApi.getDept(vacationDutyDO.getDeptId()).getCheckedData().getName());
        if (StrUtil.isNotEmpty(vacationDutyDO.getProcessInstanceId())) {
            Map<String, String> taskInfo = taskServiceApi.getNeededTaskInfo(OACategoryConstants.DUTY, getLoginUserId(), vacationDutyDO.getProcessInstanceId(), null, null);
            respVO.setTaskName(taskInfo.get("taskName"));
            if(StringUtil.isNotBlank(taskInfo.get("status"))){
                respVO.setResult(Integer.parseInt(taskInfo.get("status")));
            }
            if(StringUtil.isNotBlank(taskInfo.get("operateType"))){
                respVO.setOperateType(Integer.parseInt(taskInfo.get("operateType")));
            }
        }
        return respVO;
    }

    @Override
    public VacationDutyDO getByProcessInstanceId(String processInstanceId) {
        return baseMapper.selectOne(VacationDutyDO::getProcessInstanceId,processInstanceId);
    }


    public List<DutyFormBaseVO> combineForm(List<VacationDutyFormDO> vacationDutyFormList) {
        Map<String, DutyFormBaseVO> mergedDutyFormMap = new LinkedHashMap<>();
        for(VacationDutyFormDO formDO : vacationDutyFormList){
            String personnelId = formDO.getPersonnel() + formDO.getPhone() + formDO.getDeptId();
            String dateString = formDO.getStartDate().substring(formDO.getStartDate().indexOf('-') + 1).replace("-", ".") + "--" + formDO.getEndDate().substring(formDO.getEndDate().indexOf('-') + 1).replace("-", ".");
            if(!mergedDutyFormMap.containsKey(personnelId)){
                DutyFormBaseVO dutyFormBaseVO = new DutyFormBaseVO();
                dutyFormBaseVO.setPersonnelId(formDO.getPersonnelId());
                dutyFormBaseVO.setDutyType(formDO.getDutyType());
                dutyFormBaseVO.setPhone(formDO.getPhone());
                dutyFormBaseVO.setTelephone(formDO.getTelephone());
                dutyFormBaseVO.setDeptName(formDO.getDeptName());
                dutyFormBaseVO.setPersonnel(formDO.getPersonnel());
                dutyFormBaseVO.setDateRangeStr(Collections.singletonList(dateString));
                dutyFormBaseVO.setTelephoneList(Collections.singletonList(formDO.getTelephone()));
                dutyFormBaseVO.setStartDate(formDO.getStartDate());
                dutyFormBaseVO.setDeptId(formDO.getDeptId());
                mergedDutyFormMap.put(personnelId,dutyFormBaseVO);
            }else{
                DutyFormBaseVO dutyFormBaseVO = mergedDutyFormMap.get(personnelId);
                List<String> dateRangeStr = new ArrayList<>(dutyFormBaseVO.getDateRangeStr());
                // 去重
                if (!dateRangeStr.contains(dateString)){
                    dateRangeStr.add(dateString);
                }
                dutyFormBaseVO.setDateRangeStr(dateRangeStr);

                List<String> telephoneList = new ArrayList<>(dutyFormBaseVO.getTelephoneList());
                if(!telephoneList.contains(formDO.getTelephone())){
                    telephoneList.add(formDO.getTelephone());
                }
                dutyFormBaseVO.setTelephoneList(telephoneList);
                mergedDutyFormMap.replace(personnelId,dutyFormBaseVO);
            }
        }
        List<DutyFormBaseVO> data = mergedDutyFormMap.values().stream().collect(Collectors.toList());
        return data;
    }



//
//    @Override
//    public PageResult<ScheduleStatisticRespVO> getScheduleStatisticsList(SchedulePageReqVO reqVO) {
//        List<Long> scheduleIds = baseMapper.getScheduleIdByInstanceId();
//        if (scheduleIds.isEmpty()) {
//            return null;
//        }
//        reqVO.setIdList(scheduleIds);
//        IPage<ScheduleStatisticRespVO> myPage = MyBatisUtils.buildPage(reqVO);
//        List<ScheduleStatisticRespVO> list = this.baseMapper.selectStatisticsPage(myPage,reqVO);
//        return new PageResult<>(list,myPage.getTotal());
//    }
//
//    @Override
//    public PageResult<ScheduleDetailRespVO> getScheduleStatisticsDetail(SchedulePageReqVO reqVO) {
//        List<Long> scheduleIds = baseMapper.getScheduleIdByInstanceId();
//        if (scheduleIds.isEmpty()) {
//            return null;
//        }
//        reqVO.setIdList(scheduleIds);
//        IPage<ScheduleDetailRespVO> myPage = MyBatisUtils.buildPage(reqVO);
//        List<ScheduleDetailRespVO> list = this.baseMapper.selectDetailPage(myPage,reqVO);
//        return new PageResult<>(list,myPage.getTotal());
//    }
//
//    @Override
//    public VacationDutyFormDO getScheduleDetailById(Long id) {
//        return workScheduleMapper.selectById(id);
//    }
//
//    @Override
//    public void updateStatusById(Long id, Integer status) {
//        WeeklyWorkScheduleDO weeklyWorkScheduleDO = new WeeklyWorkScheduleDO();
//        weeklyWorkScheduleDO.setId(id);
//        weeklyWorkScheduleDO.setStatus(status);
//        baseMapper.updateById(weeklyWorkScheduleDO);
//    }
//
//    @Override
//    public void BatchUpdateStatusByIds(List<Long> ids, Integer status) {
//        UpdateWrapper<WeeklyWorkScheduleDO> updateWrapper = new UpdateWrapper<>();
//        updateWrapper.in("id", ids)
//                .set("status",status);
//        baseMapper.update(null,updateWrapper);
//    }
//
//    @Override
//    public String getCopyTo(Long scheduleId) {
//        WeeklyWorkScheduleDO scheduleDO = baseMapper.selectById(scheduleId);
//        return scheduleDO.getCopyTo();
//    }
//
//    @Override
//    public String getProcessInstanceId(Long scheduleId) {
//        WeeklyWorkScheduleDO scheduleDO = baseMapper.selectById(scheduleId);
//        return scheduleDO.getProcessInstanceId();
//    }
//
    @Override
    public List<DutyPersonnelRespVO> getPersonnel(Long deptId) {
        List<DutyPersonnelRespVO> dutyPersonnelRespVOS = vacationDutyFormMapper.selectPersonnelInfo(deptId);
        return dutyPersonnelRespVOS;
    }

    @Override
    public PageResult<DutyPersonnelRespVO> getPagePersonnel(Long deptId, String userName, Integer pageNum, Integer pageSize) {
        // 计算偏移量
        Integer offset = (pageNum - 1) * pageSize;

        // 分页查询
        List<DutyPersonnelRespVO> dutyPersonnelRespVOS = vacationDutyFormMapper.selectPersonnelInfoByPage(deptId, userName, offset, pageSize);

        // 查询总记录数，以便计算总页数等信息
        Integer total = vacationDutyFormMapper.countPersonnelByDeptId(deptId, userName); // 假设你有这样一个计数方法

        // 构建分页结果对象
        PageResult<DutyPersonnelRespVO> pageResult = new PageResult<>();
        pageResult.setList(dutyPersonnelRespVOS);
        pageResult.setTotal(Long.valueOf(total));

        return pageResult;
    }

    @Override
    public List<DutyPersonnelRespVO> getPersonnelLeader() {
        return vacationDutyFormMapper.selectPersonnelLeaderInfo();
    }

    @Override
    public List<DutyFormBaseVO> getPreviewData(List<Long> ids, String dutyType) {
        List<VacationDutyFormDO> vacationDutyFormList = vacationDutyFormMapper.getVacationDutyByIdAndType(ids, dutyType);
        return combineForm(vacationDutyFormList);
    }

    @Override
    public DutyFormTotalVO getAllPreview(String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());

        // 领导数据
        List<VacationDutyFormDO> vacationDutyFormLeaderSitList = vacationDutyFormMapper.getLeaderForm2(ids, "1");
        List<VacationDutyFormDO> vacationDutyFormLeaderDepList = vacationDutyFormMapper.getLeaderForm2(ids, "2");
        List<DutyFormBaseVO> formLeaderSitList = combineForm(vacationDutyFormLeaderSitList);
        List<DutyFormBaseVO> formLeaderDepList = combineForm(vacationDutyFormLeaderDepList);

        // 基础数据
        List<VacationDutyFormDO> vacationDutyFormSitList = vacationDutyFormMapper.getVacationDutyByIdAndType(idList, "1");
        List<VacationDutyFormDO> vacationDutyFormDepList = vacationDutyFormMapper.getVacationDutyByIdAndType(idList, "2");

        // 找出最大最小日期
        List<VacationDutyFormDO> sitList = new ArrayList<>(vacationDutyFormLeaderSitList);
        sitList.addAll(vacationDutyFormSitList);
        List<VacationDutyFormDO> depList = new ArrayList<>(vacationDutyFormLeaderDepList);
        depList.addAll(vacationDutyFormDepList);

        // 对于vacationDutyFormSitList列表
        Optional<String> minDateSit = sitList.stream()
                .map(VacationDutyFormDO::getStartDate)
                .min(String::compareTo);
        Optional<String> maxDateSit = sitList.stream()
                .map(VacationDutyFormDO::getEndDate)
                .max(String::compareTo);

        // 对于vacationDutyFormDepList列表
        Optional<String> minDateDep = depList.stream()
                .map(VacationDutyFormDO::getStartDate)
                .min(String::compareTo);
        Optional<String> maxDateDep = depList.stream()
                .map(VacationDutyFormDO::getEndDate)
                .max(String::compareTo);


        // 转换
        formLeaderSitList.addAll(combineForm(vacationDutyFormSitList));
        formLeaderDepList.addAll(combineForm(vacationDutyFormDepList));

        // 设置数据
        DutyFormTotalVO dutyFormTotalVO = new DutyFormTotalVO();
        dutyFormTotalVO.setVacationDutyFormSitList(formLeaderSitList);
        dutyFormTotalVO.setVacationDutyFormDepList(formLeaderDepList);
        if(minDateSit.isPresent()){
            dutyFormTotalVO.setMinDateSit(minDateSit.get());
        }

        if(maxDateSit.isPresent()){
            dutyFormTotalVO.setMaxDateSit(maxDateSit.get());
        }

        if(minDateDep.isPresent()){
            dutyFormTotalVO.setMinDateDep(minDateDep.get());
        }

        if (maxDateDep.isPresent()){
            dutyFormTotalVO.setMaxDateDep(maxDateDep.get());
        }

        return dutyFormTotalVO;
    }

    public Integer saveLeaderForm(List<VacationDutyFormLeaderDO> createReqVO, String ids){
        vacationDutyFormMapper.cancelLeaderForm(ids);
        return vacationDutyFormMapper.saveLeaderForm(createReqVO);
    }

    public Integer cancelLeaderForm(String ids){
        return vacationDutyFormMapper.cancelLeaderForm(ids);
    }

    public void endVacation(OARemoveReqVO reqVO) {
        bpmTaskServiceApi.approveTask(WebFrameworkUtils.getLoginUserId(),
                new BpmTaskApproveReqDTO()
                        .setId(reqVO.getTaskId()));
    }

    @Override
    public List<DutyFormExcelRespVO> importDutyForm(List<DutyFormExcelVO> list, Long deptId) {
        // 限制50条数量
        if (list.size() > 50) {
            throw exception(VACATION_DUTY_IMPORT_OVERFLOW_ERROR);
        }

            String errorMessage = "请核对以下人员信息: ";
        Boolean errorFlag = false;
        List<String> errorName = new ArrayList<>();
        StringBuilder separatedNames = new StringBuilder();
        AdminUserRespDTO userDTO = userApi.getUser(getLoginUserId()).getCheckedData();
        List<DutyFormExcelRespVO> dutyFormExcelRespVOS = new ArrayList<>();

        // 非空校验
        if(list.isEmpty()){
            throw exception(VACATION_DUTY_IMPORT_LENGTH_ERROR);
        }

        // 校验数据，根据名字和手机号码确定人员，若无法确定则报错并返回错误数据
        for (DutyFormExcelVO dutyFormExcelVO : list) {
            // 必填项校验
            if(dutyFormExcelVO.getPersonnel() == null|| dutyFormExcelVO.getPersonnel() == null||
                    dutyFormExcelVO.getDutyType() == null || dutyFormExcelVO.getDutyType().equals("")||
                    dutyFormExcelVO.getPhone() == null|| dutyFormExcelVO.getPhone().equals("")||
                    dutyFormExcelVO.getStartDate() == null || dutyFormExcelVO.getEndDate() == null)
            {
                throw exception(VACATION_DUTY_IMPORT_NOT_NULL_ERROR); // 包装异常并提供更具体的错误信息
            }

            // 日期校验
            DutyFormExcelRespVO dutyFormExcelRespVO = new DutyFormExcelRespVO();
            dutyFormExcelRespVO.setPersonnel(dutyFormExcelVO.getPersonnel());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            // 若起始日期大于结束日期则交换
            if(dutyFormExcelVO.getStartDate().isAfter(dutyFormExcelVO.getEndDate())){
                // 交换日期
                LocalDateTime tempDate = dutyFormExcelVO.getStartDate();
                dutyFormExcelVO.setStartDate(dutyFormExcelVO.getEndDate());
                dutyFormExcelVO.setEndDate(tempDate);
            }

            try {
                dutyFormExcelRespVO.setStartDate(dutyFormExcelVO.getStartDate().format(formatter));
                dutyFormExcelRespVO.setEndDate(dutyFormExcelVO.getEndDate().format(formatter));
            }
            catch (DateTimeParseException e) { // 假设异常源于日期格式化失败
                throw exception(VACATION_DUTY_IMPORT_TIME_ERROR); // 包装异常并提供更具体的错误信息
            }
            catch (NullPointerException e) { // 如果startDate或endDate为null可能会抛此异常
                throw exception(VACATION_DUTY_IMPORT_NOT_NULL_ERROR);
            }
            dutyFormExcelRespVO.setTelephone(dutyFormExcelVO.getTelephone());
            dutyFormExcelRespVO.setPhone(dutyFormExcelVO.getPhone());
            if(Objects.equals(dutyFormExcelVO.getDutyType(), "坐班")){
                dutyFormExcelRespVO.setDutyType("1");
            }
            else if(Objects.equals(dutyFormExcelVO.getDutyType(), "值班")){
                dutyFormExcelRespVO.setDutyType("2");
            }
            else{
                throw exception(VACATION_DUTY_IMPORT_TYPE_ERROR);
            }


            DutyPersonnelRespVO dutyPersonnelRespVO = vacationDutyFormMapper.getPersonnelInfoByNameAndPhone(dutyFormExcelVO.getPersonnel(), dutyFormExcelVO.getPhone(), deptId);
            if (dutyPersonnelRespVO != null) {
                dutyFormExcelRespVO.setPersonnelId(String.valueOf(dutyPersonnelRespVO.getUserId()));
                dutyFormExcelRespVO.setDeptId(String.valueOf(dutyPersonnelRespVO.getDeptId()));
                dutyFormExcelRespVO.setDeptName(dutyPersonnelRespVO.getDeptName());
                dutyFormExcelRespVOS.add(dutyFormExcelRespVO);
            } else {
                errorFlag = true;
                if(!errorName.contains(dutyFormExcelVO.getPersonnel())){
                    errorName.add(dutyFormExcelVO.getPersonnel());
                }
            }

        }
        if(errorFlag) {
            for(String name: errorName){
                separatedNames.append(name).append(",");
            }
            separatedNames.setLength(separatedNames.length() - 1);
            errorMessage += separatedNames.toString();
            throw exception(new ErrorCode(1005004100, errorMessage));
        }
        return dutyFormExcelRespVOS;
    }

    public boolean isValidDate(String dateStr, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            LocalDate.parse(dateStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    @Override
    public Long getDeptIdByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapperX<VacationDutyDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(VacationDutyDO::getProcessInstanceId, processInstanceId);
        VacationDutyDO vacationDutyDO = vacationDutyMapper.selectOne(wrapperX);
        return vacationDutyDO.getDeptId();
    }
}
