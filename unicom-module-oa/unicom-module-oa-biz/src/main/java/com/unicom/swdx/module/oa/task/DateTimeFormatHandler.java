package com.unicom.swdx.module.oa.task;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

public class DateTimeFormatHandler implements SheetWriteHandler {

    private final CellStyle cellStyle;

    public DateTimeFormatHandler(Workbook workbook) {
        this.cellStyle = createDateTimeCellStyle(workbook);
    }

    private CellStyle createDateTimeCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(workbook.createDataFormat().getFormat("yyyy/mm/dd hh:mm:ss"));
        return style;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // Do nothing
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        writeSheetHolder.getSheet().setDefaultColumnStyle(0, cellStyle); // Assuming you want to apply this to the first column
    }
}

