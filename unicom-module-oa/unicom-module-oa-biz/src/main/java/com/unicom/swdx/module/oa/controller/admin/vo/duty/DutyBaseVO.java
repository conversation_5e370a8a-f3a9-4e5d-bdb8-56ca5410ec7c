package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyDO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DutyBaseVO {
    /**
     * 寒暑假坐值班表单列表
     */
    @ApiModelProperty("寒暑假坐值班表单列表")
    private List<VacationDutyFormDO> vacationDutyFormList;

    /**
     * 是否草稿
     */
    @ApiModelProperty("是否草稿")
    private Boolean isDraft;


    /**
     * 抄送人员
     */
    @ApiModelProperty("抄送人员")
    private String copyTo;

    /**
     * 抄送人员名称
     */
    @ApiModelProperty("抄送人员")
    private String copyToName;
}
