package com.unicom.swdx.module.oa.controller.admin.oaOutReport;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OADealOutReportVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportRespVO;
import com.unicom.swdx.module.oa.service.outReport.OutReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 外出报告
 */
@Api(tags = "办公OA - 外出报告")
@RestController
@RequestMapping("/oa/outReport")
public class OutReportController {

    @Resource
    private OutReportService outReportService;

    @PostMapping("/saveDraft")
    @PreAuthorize("@ss.hasPermission('oa:outReport:create')")
    @ApiOperation("外出报告保存草稿")
    public CommonResult<Long> saveDraft(@Valid @RequestBody OutReportCreateReqVO createReqVO) {
        return success(outReportService.saveDraft(createReqVO));
    }

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('oa:outReport:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @ApiOperation("发起外出报告审批申请")
    public CommonResult<String> createOutReportProcess(@Valid @RequestBody OutReportCreateReqVO createReqVO) {

        //校验开始与截止时间
        if(!outReportService.checkTime2(createReqVO)){
            return error(400,"开始日期不能大于截止日期");
        }

        //校验是否已有相同申请时段或交叉时段的外出报告
        if(!outReportService.checkTime(createReqVO)){
            return error(400,"已有相同申请时段或交叉时段的外出报告");
        }

        return success(outReportService.createOutReportProcess(createReqVO));
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('oa:outReport:create')")
    @ApiOperation("驳回或撤销后重新编辑再发起外出报告流程")
    public CommonResult<Boolean> restartLeave(@RequestBody OutReportCreateReqVO reqVO) {

        //校验开始与截止时间
        if(!outReportService.checkTime2(reqVO)){
            return error(400,"开始日期不能大于截止日期");
        }

        //校验是否已有相同申请时段或交叉时段的外出报告
        if(!outReportService.checkTime(reqVO)){
            return error(400,"已有相同申请时段或交叉时段的外出报告");
        }

        outReportService.restartOutReport(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('oa:outReport:query')")
    @ApiOperation("获得外出报告审批申请")
    public CommonResult<OutReportRespVO> getOutReport(@RequestParam(value = "id",required = false) Long id,
                                                      @RequestParam(value = "processInstanceId",required = false) String processInstanceId,
                                                      @RequestParam(value = "isReceived",required = false) boolean isReceived) {
        OutReportRespVO respVO = outReportService.getResp(id, processInstanceId,isReceived);
        return success(respVO);
    }

    @PostMapping("/delete")
    @PreAuthorize("@ss.hasPermission('oa:outReport:query')")
    @ApiOperation("删除外出报告审批申请")
    public CommonResult<Integer> deleteOutReport(@RequestParam(value = "id",required = false) Long id)
    {
        return success(outReportService.deleteById(id));

    }

    @PostMapping("/dealOutReport")
//    @PreAuthorize("@ss.hasPermission('oa:leave:dealLeave')")
    @ApiOperation("假期变更/销假")
    public CommonResult<Boolean> dealOutReport(@Valid @RequestBody OADealOutReportVO dealOutReportVO) {

        outReportService.dealOutReport(dealOutReportVO);
        return success(true);

    }
}
