package com.unicom.swdx.module.oa.controller.admin;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureRespVO;
import com.unicom.swdx.module.oa.service.lecture.LectureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 外出讲学
 */
@Api(tags = "办公OA - 外出讲学")
@RestController
@RequestMapping("/oa/lecture")
public class LectureController {

    @Resource
    private LectureService lectureService;

    @PostMapping("/saveDraft")
    @PreAuthorize("@ss.hasPermission('oa:lecture:create')")
    @ApiOperation("外出讲学保存草稿")
    public CommonResult<Long> saveDraft(@Valid @RequestBody LectureCreateReqVO createReqVO) {
        return success(lectureService.saveDraft(createReqVO));
    }

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('oa:lecture:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @ApiOperation("发起外出讲学审批申请")
    public CommonResult<String> createLectureProcess(@Valid @RequestBody LectureCreateReqVO createReqVO) {
        return success(lectureService.createLectureProcess(createReqVO));
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('oa:lecture:create')")
    @ApiOperation("驳回或撤销后重新编辑再发起外出讲学流程")
    public CommonResult<Boolean> restartLecture(@RequestBody LectureCreateReqVO reqVO) {
        lectureService.restartLecture(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('oa:lecture:query')")
    @ApiOperation("获得外出讲学审批申请")
    public CommonResult<LectureRespVO> getLecture(@RequestParam(value = "id",required = false) Long id,
                                                  @RequestParam(value = "processInstanceId",required = false) String processInstanceId) {
        LectureRespVO respVO = lectureService.getResp(id, processInstanceId);
        return success(respVO);
    }


    @GetMapping("/get_image")
//    @PreAuthorize("@ss.hasPermission('oa:lecture:query')")
    @ApiOperation("获取用户最新签名照片")
    public CommonResult<String> getImage(@RequestParam(value = "id",required = false) Long id) {
        String sign = lectureService.getImage(id);
        return success(sign);
    }

}
