package com.unicom.swdx.module.oa.controller.admin.vo.lecture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ApiModel("办公OA - 外出讲学详情 Response VO")
public class LectureRespVO extends LectureBaseVO{

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("授课人姓名")
    private String userNickName;

    @ApiModelProperty("部门名")
    private String deptName;

    @ApiModelProperty(value = "状态", notes = "参见 bpm_process_instance_result 枚举")
    private Integer result;

    @ApiModelProperty(value = "发起时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime launchTime;

    @ApiModelProperty(value = "当前查看的任务节点名称")
    private String taskName;

    @ApiModelProperty(value = "审批/选择审批人/假期变更按钮的出现与否")
    private Integer operateType;
}
