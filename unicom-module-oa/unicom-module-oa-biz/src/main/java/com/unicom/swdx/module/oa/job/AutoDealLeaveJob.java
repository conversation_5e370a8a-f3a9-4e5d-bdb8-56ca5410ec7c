package com.unicom.swdx.module.oa.job;

import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class AutoDealLeaveJob {

    @Resource
    private OALeaveService oaLeaveService;

    @XxlJob("autoDealLeaveJob")
    public void execute() {

        log.info("---------开始每日零点系统定时销假任务---------");
        oaLeaveService.autoDealLeave();

    }
}
