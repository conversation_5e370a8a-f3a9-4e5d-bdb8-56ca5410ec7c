package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryToDoRespVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */

@Data
@ApiModel("办公OA - 寒暑假坐值班表 Response VO")
public class SummaryDutyRespVO {
    List<DutyRespVO> summary;

    Integer count;

    Integer countReject;

    Integer countCancel;
}
