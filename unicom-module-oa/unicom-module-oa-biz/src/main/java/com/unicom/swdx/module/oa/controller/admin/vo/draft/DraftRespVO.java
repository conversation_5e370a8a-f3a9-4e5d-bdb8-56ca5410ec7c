package com.unicom.swdx.module.oa.controller.admin.vo.draft;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("OA - 草稿箱分页查询 Response VO")
public class DraftRespVO {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 流程分类
     */
    @ApiModelProperty("流程分类")
    private String category;

    /**
     * 发起人
     */
    @ApiModelProperty("发起人")
    private String userNickName;

    /**
     * 事项id
     */
    @ApiModelProperty("事项id")
    private Long itemId;

    /**
     * 保存草稿时间
     */
    @ApiModelProperty("保存草稿时间")
    private LocalDateTime updateTime;

}
