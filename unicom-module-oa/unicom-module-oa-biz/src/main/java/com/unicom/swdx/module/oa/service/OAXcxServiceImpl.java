package com.unicom.swdx.module.oa.service;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.oa.controller.xcx.vo.XcxSignReqVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class OAXcxServiceImpl implements OAXcxService {

    @Resource
    private RedisUtil redisUtil;

    private final String SIGN_REDIS_KEY = "xcx:sign:url:";

    private final String USER_ID = "user_id";
    private final String URL = "url";

    @Override
    public String checkSignUser(String uniqueCode) {
        Object userIdObj = redisUtil.hGet(SIGN_REDIS_KEY + uniqueCode, USER_ID);
        if (Objects.isNull(userIdObj)) {
            return null;
        }
        String userId = userIdObj.toString();
        if (StrUtil.equals(userId, SecurityFrameworkUtils.getLoginUserId().toString())) {
            return userId;
        }
        return null;
    }

    @Override
    public void bindSign(XcxSignReqVO reqVO) {
        redisUtil.hSet(SIGN_REDIS_KEY + reqVO.getUniqueCode(),URL, reqVO.getSignUrl(), 3600);
    }

    @Override
    public String getSignUrl(String uniqueCode) {
        Object urlObj = redisUtil.hGet(SIGN_REDIS_KEY + uniqueCode, URL);
        return Objects.nonNull(urlObj) ? urlObj.toString() : null;
    }



}
