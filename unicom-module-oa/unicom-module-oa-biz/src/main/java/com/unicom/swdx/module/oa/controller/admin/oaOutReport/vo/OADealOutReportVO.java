package com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class OADealOutReportVO {

    @ApiModelProperty("请假记录id")
    @NotNull
    private Long id;

    @ApiModelProperty("结束时间,格式为2024-03-05 00:00:00,时分秒均为0")
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

//    @ApiModelProperty("上班时间,格式为2024-03-05 00:00:00,时分秒均为0")
//    @NotNull(message = "上班时间不能为空")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime returnWorkTime;

    @ApiModelProperty(value = "移动端签字")
    private String handSignature;

}
