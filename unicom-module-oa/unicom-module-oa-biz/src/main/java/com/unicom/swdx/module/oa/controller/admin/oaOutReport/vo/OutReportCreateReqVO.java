package com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("办公OA - 外出报告发起审批 Request VO")
public class OutReportCreateReqVO extends OutReportBaseVO {

    @ApiModelProperty(value = "主键",notes = "当有草稿时必传")
    private Long id;

    @ApiModelProperty(value = "重新发起时必传流程id")
    private String processInstanceId;

    @ApiModelProperty("选择的下一任务审批人列表")
    private List<Long> userIds;

    @ApiModelProperty("审批方式 1顺序 2会签")
    private String chargeLeaderSeq;

}
