package com.unicom.swdx.module.oa.convert;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.api.dto.ScheduleDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.DutyCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.DutyRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface VacationDutyConvert {
    VacationDutyConvert INSTANCE = Mappers.getMapper(VacationDutyConvert.class);

    VacationDutyDO convert(DutyCreateReqVO bean);

    DutyRespVO convert(VacationDutyDO bean);

    List<ScheduleRespVO> convertList(List<WeeklyWorkScheduleDO> list);

    PageResult<ScheduleRespVO> convertPage(PageResult<WeeklyWorkScheduleDO> page);

    ScheduleDTO convertDTO(WeeklyWorkScheduleDO bean);
}
