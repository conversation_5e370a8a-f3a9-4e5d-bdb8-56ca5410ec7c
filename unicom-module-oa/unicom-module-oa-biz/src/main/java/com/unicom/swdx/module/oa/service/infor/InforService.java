package com.unicom.swdx.module.oa.service.infor;

import java.util.*;
import javax.validation.*;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.*;
import com.unicom.swdx.module.oa.dal.dataobject.InforDO;

/**
 * 重点工作信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InforService extends IService<InforDO> {

    /**
     * 创建重点工作信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createInfor(@Valid InforCreateReqVO createReqVO);

    /**
     * 更新重点工作信息
     *
     * @param updateReqVO 更新信息
     */
    String updateInfor(@Valid InforUpdateReqVO updateReqVO);

    /**
     * 删除重点工作信息
     *
     * @param id 编号
     */
    void deleteInfor(Integer id);

    /**
     * 获得重点工作信息
     *
     * @param id 编号
     * @return 重点工作信息
     */
    InforDO getInfor(Integer id);

    /**
     * 获得重点工作信息列表
     *
     * @param ids 编号
     * @return 重点工作信息列表
     */
    List<InforDO> getInforList(Collection<Integer> ids);

    List<InforRespVO> getInforList(InforPageReqVO pageReqVO);

    /**
     * 获得重点工作信息分页
     *
     * @param pageReqVO 分页查询
     * @return 重点工作信息分页
     */
    PageResult<InforRespVO> getInforPage(InforPageReqVO pageReqVO);

     Set<Integer> getLeadleaders(Integer uType);

    /**
     * 获得重点工作信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 重点工作信息列表
     */
    List<InforDO> getInforList(InforExportReqVO exportReqVO);

    InforRoleVO judgeRole();

    List<Integer> selectAllChildrenDeptIds(Integer parentId);

}
