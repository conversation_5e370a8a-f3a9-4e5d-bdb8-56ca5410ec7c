package com.unicom.swdx.module.oa.service.lecture;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;

import java.time.LocalDate;
import java.util.Map;

public interface LectureService extends IService<LectureDO> {

    Long saveDraft(LectureCreateReqVO reqVO);

    String createLectureProcess(LectureCreateReqVO reqVO);

    LectureDO getByProcessInstanceId(String processInstanceId);

    LectureDO get(Long id, String processInstanceId);

    LectureRespVO getResp(Long id, String processInstanceId);

    void restartLecture(Long loginUserId, LectureCreateReqVO reqVO);


    String getImage(Long id);

    Integer getPostType(Long userId);

    /**
     * 更新状态
     * @param id
     * @param
     * @return
     */
    Map<String, LocalDate> getDateById(Long id);
}
