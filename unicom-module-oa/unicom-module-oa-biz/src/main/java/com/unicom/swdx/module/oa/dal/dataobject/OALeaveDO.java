package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "oa_leave",autoResultMap = true)
@KeySequence("oa_leave_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OALeaveDO extends BaseDO {
    /**
     * 请假表单主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 申请人的用户编号
     *
     * 关联 AdminUserDO 的 id 属性
     */
    @TableField(value = "launch_user_id")
    private Long userId;

    /**
     * 申请人的部门编号
     *
     * 关联 AdminUserDO 的 deptId 属性
     */
    private Long deptId;

    /**
     * 请假类型
     */
    @TableField("leave_type")
    private Integer type;

    /**
     * 原因
     */
    private String reason;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 发起时间
     */
    private LocalDateTime launchTime;

    /**
     * 销假时间
     */
    private LocalDateTime dealTime;

//    /**
//     * 上班时间
//     */
//    private LocalDateTime returnWorkTime;

    /**
     * 请假天数
     */
    private Integer day;

    /**
     * 请假的结果
     */
    private Integer result;

    /**
     * 是否草稿
     */
    private Boolean isDraft;

    /**
     * 是否销假
     */
    private Boolean isDealt;

    /**
     * 对应的流程编号
     *
     * 关联 ProcessInstance 的 id 属性
     */
    private String processInstanceId;

    /**
     * 请假附件
     */
    @TableField(value = "files", typeHandler = StringListTypeHandler.class)
    private List<String> filesPath;

    /**
     * 租户id
     */
    private Long tenantId;
}
