package com.unicom.swdx.module.oa.api;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;
import com.unicom.swdx.module.oa.service.receive.ReceiveService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class ReceiveApiImpl implements ReceiveApi {

    @Resource
    private ReceiveService receiveService;

    @Override
    public void save(ReceiveDTO receiveDTO) {
        if (CollUtil.isEmpty(receiveDTO.getUserIds())) {
            return;
        }
        List<ReceiveDO> list = new ArrayList<>();
        receiveDTO.getUserIds().forEach(u -> {
            ReceiveDO receiveDO = new ReceiveDO();
            receiveDO.setCategory(receiveDTO.getCategory());
            receiveDO.setItemId(receiveDTO.getItemId());
            receiveDO.setApplyTime(receiveDTO.getApplyTime());
            receiveDO.setUserId(u.toString());
            receiveDO.setProcessInstanceId(receiveDTO.getProcessInstanceId());
            receiveDO.setPromoterUserId(receiveDTO.getPromoterUserId().toString());
            list.add(receiveDO);
        });
        receiveService.saveBatch(list);
    }

    @Override
    public void saveLeave(ReceiveDTO receiveDTO) {
//        if (CollUtil.isEmpty(receiveDTO.getUserIds())) {
//            return;
//        }
        ReceiveDO receiveDO = new ReceiveDO();
        //待办内容
        receiveDO.setContent(receiveDTO.getCategory());
        Boolean r = receiveDTO.getUserIds() != null;
        //请假审批
        if(r && receiveDTO.getUserIds().contains(12L)){
            //外出报告
            receiveDO.setCategory("12");
        }else {
            //请假审批
            receiveDO.setCategory("10");
        }
        receiveDO.setItemId(receiveDTO.getItemId());
        receiveDO.setApplyTime(receiveDTO.getApplyTime());
        receiveDO.setProcessInstanceId(receiveDTO.getProcessInstanceId());
        receiveDO.setPromoterUserId(receiveDTO.getPromoterUserId().toString());
        receiveDO.setUserId(receiveDTO.getPromoterUserId().toString());
        receiveService.save(receiveDO);
    }

    @Override
    public void saveWorkSummary(ReceiveDTO receiveDTO) {
//        if (CollUtil.isEmpty(receiveDTO.getUserIds())) {
//            return;
//        }
        List<ReceiveDO> receiveDOS = new ArrayList<>();
        List<Long> userids = receiveDTO.getUserIds();
        for (Long userid : userids) {
            ReceiveDO receiveDO = new ReceiveDO();
            //待办内容
            receiveDO.setContent(receiveDTO.getCategory());
            //一周工作安排汇总
            receiveDO.setCategory("14");
            receiveDO.setItemId(receiveDTO.getItemId());
            receiveDO.setApplyTime(receiveDTO.getApplyTime());
            receiveDO.setProcessInstanceId(receiveDTO.getProcessInstanceId());
            receiveDO.setUserId(userid.toString());
            receiveDOS.add(receiveDO);
        }
//        System.out.println(receiveDOS);
//        List<ReceiveDO> firstTwoReceiveDOs = receiveDOS.stream()
//                .limit(2)
//                .collect(Collectors.toList());
//        receiveService.saveBatch(firstTwoReceiveDOs);
        receiveService.saveBatch(receiveDOS);
    }

    @Override
    public CommonResult<List<Long>> getReceivedUserIds(String processInstanceId) {
        return success(receiveService.getReceivedUserIds(processInstanceId));
    }


}
