package com.unicom.swdx.module.oa.controller.admin;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceivePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceiveRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceiveToUsersReqVO;
import com.unicom.swdx.module.oa.convert.ReceiveConvert;
import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;
import com.unicom.swdx.module.oa.service.receive.ReceiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "办公OA - 我收到的")
@RestController
@RequestMapping("/oa/receive")
public class ReceiveController {

    @Resource
    private ReceiveService receiveService;

    @GetMapping("/page")
    @ApiOperation("获得我收到的分页列表")
    public CommonResult<PageResult<ReceiveRespVO>> page(ReceivePageReqVO reqVO) {
        PageResult<ReceiveDO> page = receiveService.page(reqVO);
        return success(ReceiveConvert.INSTANCE.convertPage(page));
    }

    @GetMapping("/getNoReadNum")
    @ApiOperation("获取我收到的未读数量")
    public CommonResult<Long> getNoReadNum() {
        return success(receiveService.getNoReadNum());
    }

    @GetMapping("/changeReadStatus")
    @ApiOperation("已读")
    public CommonResult<Boolean> changeReadStatus(@RequestParam Long id) {
        receiveService.changeReadStatus(id);
        return success(true);
    }

    @PostMapping("/toUsers")
    @ApiOperation("抄送给其他人")
    public CommonResult<Boolean> toUsers(@RequestBody ReceiveToUsersReqVO reqVO) {
        receiveService.toUsers(reqVO);
        return success(true);
    }

}
