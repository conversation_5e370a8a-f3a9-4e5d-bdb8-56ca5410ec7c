package com.unicom.swdx.module.oa.controller.xcx.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("OA - 小程序绑定签字 Request VO")
public class XcxSignReqVO {

    @ApiModelProperty(value = "uniqueCode",required = true)
    @NotBlank(message = "uniqueCode不能为空")
    private String uniqueCode;

    @ApiModelProperty(value = "签字图片url",required = true)
    @NotBlank(message = "签字图片url不能为空")
    private String signUrl;

}
