
package com.unicom.swdx.module.oa.service.vacationDuty;

import cn.hutool.json.JSONUtil;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;

import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.DutyFormExcelVO;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
public class DutyListener implements ReadListener<DutyFormExcelVO> {

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSONUtil.toJsonStr(headMap));
        if (context.readRowHolder().getRowIndex() == 0) {
            String[] expectedHeaders = {
                    "姓名\n（必填）",
                    "类型\n（必填）",
                    "开始时间\n格式示例：2024/01/01\n（必填）",
                    "结束时间\n格式示例：2024/01/01\n（必填）",
                    "办公电话",
                    "手机号码\n（必填）"
            };
            for (int i = 0; i < expectedHeaders.length; i++) {
                if (!headMap.get(i).getStringValue().equals(expectedHeaders[i])) {
                    throw exception(new ErrorCode(1005004009, "导入数据文件错误"));
                }
            }
        }
    }

    @Override
    public void invoke(DutyFormExcelVO dutyFormExcelVO, AnalysisContext analysisContext) {

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
