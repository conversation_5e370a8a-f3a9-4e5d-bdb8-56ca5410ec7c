package com.unicom.swdx.module.oa.service.receive;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceivePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceiveToUsersReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;

import java.util.List;

public interface ReceiveService extends IService<ReceiveDO> {

    PageResult<ReceiveDO> page(ReceivePageReqVO reqVO);

    Long getNoReadNum();


    void changeReadStatus(Long id);

    void toUsers(ReceiveToUsersReqVO reqVO);

    List<Long> getReceivedUserIds(String processInstanceId);

}
