package com.unicom.swdx.module.oa.controller.admin.vo.receive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("OA - 我收到的分页查询 Response VO")
public class ReceiveRespVO {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 流程分类
     */
    @ApiModelProperty("流程分类")
    private String category;

    /**
     * 发起人
     */
    @ApiModelProperty("发起人")
    private String userNickName;

    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private LocalDateTime applyTime;

    /**
     * 事项id
     */
    @ApiModelProperty("事项id")
    private Long itemId;

    /**
     * 是否已查看
     */
    @ApiModelProperty("是否已查看")
    private Boolean isRead;

    /**
     * 流程id
     */
    @ApiModelProperty("流程id")
    private String processInstanceId;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 待办内容
     */
    @ApiModelProperty("待办内容")
    private String content;

    /**
     * 流程是否已结束
     */
    @ApiModelProperty("流程是否已结束")
    private Boolean isEnd;

}
