package com.unicom.swdx.module.oa.dal.kingbase;

import java.util.*;


import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforExportReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforPageReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;
import com.unicom.swdx.module.oa.dal.dataobject.RoletempDO;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 重点任务关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserinforMapper extends BaseMapperX<UserinforDO> {

    default PageResult<UserinforDO> selectPage(UserinforPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserinforDO>()
                .eqIfPresent(UserinforDO::getUid, reqVO.getUid())
                .likeIfPresent(UserinforDO::getUname, reqVO.getUname())
                .eqIfPresent(UserinforDO::getUtype, reqVO.getUtype())
                .eqIfPresent(UserinforDO::getInforid, reqVO.getInforid())
                .eqIfPresent(UserinforDO::getDptid, reqVO.getDptid())
                .likeIfPresent(UserinforDO::getDptname, reqVO.getDptname())
                .orderByDesc(UserinforDO::getId));
    }

    default List<UserinforDO> selectList(UserinforExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UserinforDO>()
                .eqIfPresent(UserinforDO::getUid, reqVO.getUid())
                .likeIfPresent(UserinforDO::getUname, reqVO.getUname())
                .eqIfPresent(UserinforDO::getUtype, reqVO.getUtype())
                .eqIfPresent(UserinforDO::getInforid, reqVO.getInforid())
                .eqIfPresent(UserinforDO::getDptid, reqVO.getDptid())
                .likeIfPresent(UserinforDO::getDptname, reqVO.getDptname())
                .orderByDesc(UserinforDO::getId));
    }

    //办公室管理员
    List<Long> selectByTenantId(@Param("tenantId") Long tenantId);

    //办公室填报员
    List<Long> selectReportOfficerByTenantId(@Param("tenantId") Long tenantId);

    //副校长
    List<Long> selectPrincipalByTenantId(@Param("tenantId") Long tenantId);

    List<Long> selectLeaderByTenantId(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<UserinforDO> selectUserinforDOList(@Param("inforid") Integer inforid,@Param("utype") Integer utype);
}
