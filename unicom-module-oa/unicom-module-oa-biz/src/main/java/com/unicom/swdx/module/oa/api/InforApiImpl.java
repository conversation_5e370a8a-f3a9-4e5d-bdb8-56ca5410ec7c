//package com.unicom.swdx.module.oa.api;
//
//import cn.hutool.core.collection.CollUtil;
//import com.unicom.swdx.framework.common.pojo.CommonResult;
//import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
//import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;
//import com.unicom.swdx.module.oa.service.infor.InforService;
//import com.unicom.swdx.module.oa.service.receive.ReceiveService;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//
//import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
//import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;
//
//@RestController // 提供 RESTful API 接口，给 Feign 调用
//@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
//@Validated
//public class InforApiImpl implements InforApi {
//
//
//    @Autowired
//    private InforService inforService;
//
//    @Override
//    public Set<Integer> getLeadleaders(){
//        return inforService.getLeadleaders();
//    }
//}
