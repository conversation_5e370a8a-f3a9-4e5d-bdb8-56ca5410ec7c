package com.unicom.swdx.module.oa.controller.admin.vo.duty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DutyFormBaseVO {
    /**
     * 坐值班人员id
     */
    private String personnelId;

    /**
     * 处室id
     */
    private String deptId;

    /**
     * 处室名称
     */
    private String deptName;

    /**
     * 坐值班人员名称
     */
    private String personnel;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 办公电话
     */
    private String telephone;

    /**
     * 坐值班类型 1坐班 2值班
     */
    private String dutyType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 日期列表
     */
    private List<String> dateRangeStr;

    /**
     * 办公室列表
     */
    private List<String> telephoneList;
}
