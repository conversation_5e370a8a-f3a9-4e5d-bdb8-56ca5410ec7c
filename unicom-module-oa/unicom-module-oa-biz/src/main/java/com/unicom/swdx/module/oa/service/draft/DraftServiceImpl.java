package com.unicom.swdx.module.oa.service.draft;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.oa.controller.admin.vo.draft.DraftPageReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.kingbase.DraftMapper;
import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import com.unicom.swdx.module.oa.service.lecture.LectureService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Service
public class DraftServiceImpl extends ServiceImpl<DraftMapper, DraftDO> implements DraftService {

    @Resource
    private LectureService lectureService;
    @Resource
    private OALeaveService leaveService;

    /**
     * 根据事项类型和事项id删除草稿
     *
     * @param category 事项类型
     * @param itemId   事项id
     */
    @Override
    public void deleteByItemId(String category, Long itemId) {
        this.remove(new LambdaQueryWrapperX<DraftDO>()
                .eq(DraftDO::getCategory, category)
                .eq(DraftDO::getItemId, itemId));
    }

    /**
     * 分页查询
     *
     * @param reqVO 分页条件
     * @return 结果
     */
    @Override
    public PageResult<DraftDO> page(DraftPageReqVO reqVO) {
        return baseMapper.selectPage(reqVO, new LambdaQueryWrapperX<DraftDO>()
                .eq(DraftDO::getUserId, getLoginUserId())
                .eqStrIfPresent(DraftDO::getCategory, reqVO.getCategory())
                .betweenIfPresent(DraftDO::getUpdateTime, reqVO.getUpdateTime())
                .orderByDesc(DraftDO::getUpdateTime)
        );
    }

    /**
     * 删除草稿
     *
     * @param id 草稿id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDraft(Long id) {
        DraftDO draft = this.getById(id);
        if (Objects.isNull(draft)) {
            return;
        }
        this.removeById(id);

        // 删除关联的草稿
        if (StrUtil.equals("11", draft.getCategory())) {
            try {
                // 外出讲学
                if (lectureService.getById(draft.getItemId()).getIsDraft()) {
                    lectureService.removeById(draft.getItemId());
                }
            }catch (Exception e){
                e.printStackTrace();
            }

        } else if (StrUtil.equals("10", draft.getCategory())) {
            try {
                // 请假审批
                if (leaveService.getLeave(draft.getItemId()).getIsDraft()) {
                    lectureService.removeById(draft.getItemId());
                }
            }catch (Exception e){
                e.printStackTrace();
            }

        }

    }

}
