package com.unicom.swdx.module.oa.controller.admin.vo.summary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SummaryBaseVO {

    /**
     * 汇总的一周工作安排ids
     */
    private String weeklyWorkScheduleIds;

    /**
     * 年
     */
    private Integer year;

    /**
     * 学期
     */
    private String semester;

    /**
     * 周
     */
    private Integer week;

    /**
     * 抄送人员
     */
    @ApiModelProperty("抄送人员")
    private String copyTo;

    /**
     * 抄送人员名称
     */
    @ApiModelProperty("抄送人员")
    private String copyToName;

}
