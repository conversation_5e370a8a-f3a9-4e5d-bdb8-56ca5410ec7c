package com.unicom.swdx.module.oa.dal.kingbase;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface LectureMapper extends BaseMapperX<LectureDO> {


    List<LectureDO> selectListBetweenDate(@Param("userId") Long userId,@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    String getImage(@Param("userId") Long userId);

    void insertImage(@Param("userId") Long userId,@Param("sign") String sign);

}
