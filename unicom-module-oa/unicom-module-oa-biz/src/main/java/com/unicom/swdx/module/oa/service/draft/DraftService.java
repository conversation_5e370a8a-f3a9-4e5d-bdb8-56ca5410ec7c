package com.unicom.swdx.module.oa.service.draft;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.draft.DraftPageReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;

public interface DraftService extends IService<DraftDO> {

    /**
     * 根据事项类型和事项id删除草稿
     * @param category 事项类型
     * @param itemId 事项id
     */
    void deleteByItemId(String category, Long itemId);

    /**
     * 分页查询
     * @param reqVO 分页条件
     * @return 结果
     */
    PageResult<DraftDO> page(DraftPageReqVO reqVO);

    /**
     * 删除草稿
     * @param id 草稿id
     */
    void deleteDraft(Long id);

}
