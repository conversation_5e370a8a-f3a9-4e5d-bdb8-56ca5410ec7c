package com.unicom.swdx.module.oa.controller.admin.vo.lecture;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class LectureBaseVO {

    /**
     * 专题名称
     */
    @ApiModelProperty("专题名称")
    private String subjectName;

    /**
     * 邀请单位
     */
    @ApiModelProperty("邀请单位")
    private String inviteUnit;

    /**
     * 讲学地点
     */
    @ApiModelProperty("讲学地点")
    private String lectureAddress;

    /**
     * 听课群众
     */
    @ApiModelProperty("听课群众")
    private String audience;

    /**
     * 课时量
     */
    @ApiModelProperty("课时量")
    private String classPeriod;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate endDate;

    /**
     * 时长
     */
    @ApiModelProperty("时长")
    private Integer duration;

    /**
     * 授课内容
     */
    @ApiModelProperty("授课内容")
    private String lectureContent;

    /**
     * 文件
     */
    @ApiModelProperty("文件")
    private List<String> files;

    /**
     * 签字
     */
    @ApiModelProperty("签字")
    private String sign;

    /**
     * 是否草稿
     */
    @ApiModelProperty("是否草稿")
    private Boolean isDraft;

    @ApiModelProperty("所属部门")
    private Long deptId;
}
