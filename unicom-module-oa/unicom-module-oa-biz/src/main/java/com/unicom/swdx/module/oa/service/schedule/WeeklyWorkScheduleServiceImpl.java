package com.unicom.swdx.module.oa.service.schedule;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmRestartDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskUpdateAssigneeReqDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.*;
import com.unicom.swdx.module.oa.convert.WeeklyWorkScheduleConvert;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.WeeklyWorkScheduleMapper;
import com.unicom.swdx.module.oa.dal.kingbase.WorkScheduleMapper;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.enums.ScheduleMessageEnum;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.oaNotice.OaNoticeApi;
import com.unicom.swdx.module.system.api.sms.SmsQueApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import jodd.util.StringUtil;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;

@Service
public class WeeklyWorkScheduleServiceImpl extends ServiceImpl<WeeklyWorkScheduleMapper, WeeklyWorkScheduleDO> implements WeeklyWorkScheduleService {

    /**
     * 一周工作安排 对应的流程定义 KEY
     */
    public static final String PROCESS_KEY = "OA-weeklyworkschedule";

    @Resource
    private DraftService draftService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private WorkScheduleMapper workScheduleMapper;

    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;

    @Resource
    private AsyncListenableTaskExecutor taskExecutor;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    SmsQueApi smsQueApi;

    @Resource
    OaNoticeApi oaNoticeApi;

    @Resource
    public SmsSendApi smsSendService;

    @Resource
    public AdminUserApi adminUserApi;

    @Resource
    private BpmTaskServiceApi taskServiceApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDraft(ScheduleCreateReqVO createReqVO) {
        WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleConvert.INSTANCE.convert(createReqVO);
        if (Objects.isNull(createReqVO.getId())) {
            weeklyWorkScheduleDO.setUserId(getLoginUserId());
            AdminUserRespDTO userDTO = userApi.getUser(weeklyWorkScheduleDO.getUserId()).getCheckedData();
            if(createReqVO.getDeptId() != null) {
                weeklyWorkScheduleDO.setDeptId(createReqVO.getDeptId());
            } else {
                weeklyWorkScheduleDO.setDeptId(userDTO.getDeptId());
            }
            weeklyWorkScheduleDO.setIsDraft(true);
            baseMapper.insert(weeklyWorkScheduleDO);

            List<WorkScheduleDO> workSchedules = createReqVO.getWorkSchedules();
            for (WorkScheduleDO schedule : workSchedules) {
                schedule.setWeeklyWorkScheduleId(weeklyWorkScheduleDO.getId());
            }
            workScheduleMapper.insertBatch(workSchedules);
            // 写入草稿表
            DraftDO draftDO = new DraftDO();
            draftDO.setCategory(OACategoryConstants.SCHEDULE);
            draftDO.setUserId(weeklyWorkScheduleDO.getUserId().toString());
            draftDO.setItemId(weeklyWorkScheduleDO.getId());
            draftDO.setUpdateTime(LocalDateTime.now());
            draftService.save(draftDO);
        } else {
            baseMapper.updateById(weeklyWorkScheduleDO);
            draftService.update(new LambdaUpdateWrapper<DraftDO>().eq(DraftDO::getItemId,weeklyWorkScheduleDO.getId())
                    .set(DraftDO::getUpdateTime,LocalDateTime.now()));
            LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, createReqVO.getId());
            workScheduleMapper.delete(queryWrapper);
            List<WorkScheduleDO> workSchedules = createReqVO.getWorkSchedules();
            for (WorkScheduleDO schedule : workSchedules) {
                schedule.setWeeklyWorkScheduleId(weeklyWorkScheduleDO.getId());
            }
            workScheduleMapper.insertBatch(workSchedules);
        }

        return weeklyWorkScheduleDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createWorkScheduleProcess(ScheduleCreateReqVO createReqVO) {
        AdminUserRespDTO loginUser = userApi.getUser(getLoginUserId()).getCheckedData();
        DeptRespDTO deptRespDTO = new DeptRespDTO();
        if (createReqVO.getDeptId() != null) {
            deptRespDTO = deptApi.getDept(createReqVO.getDeptId()).getCheckedData();
        } else {
            deptRespDTO = deptApi.getDept(loginUser.getDeptId()).getCheckedData();
        }
        if (Objects.isNull(deptRespDTO.getLeaderUserId())) {
            throw exception(LEADER_IS_NULL);
        }
        WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleConvert.INSTANCE.convert(createReqVO);
        weeklyWorkScheduleDO.setIsDraft(false);
        weeklyWorkScheduleDO.setUserId(getLoginUserId());
        weeklyWorkScheduleDO.setDeptId(deptRespDTO.getId());
        if (Objects.isNull(weeklyWorkScheduleDO.getId())) {
            // 新增
            this.save(weeklyWorkScheduleDO);
            List<WorkScheduleDO> workSchedules = createReqVO.getWorkSchedules();
            for (WorkScheduleDO schedule : workSchedules) {
                schedule.setWeeklyWorkScheduleId(weeklyWorkScheduleDO.getId());

            }
            workScheduleMapper.insertBatch(workSchedules);
            for (WorkScheduleDO schedule : workSchedules) {
                if (schedule.getEndDate() == null) {
                    sendMessageToInitiator(schedule, false);
                    // 发送消息提醒
                    if (schedule.isNotify()) {
                        taskExecutor.submit(new Runnable() {
                            @Override
                            public void run() {
                                sendMessageToParticipant(schedule);

                            }
                        });
                    }
                }
            }
        } else {
            // id不为空表示从草稿箱发起，可能是未发起过的草稿，也可能是撤回后的草稿
            // 删除草稿记录
            draftService.deleteByItemId(OACategoryConstants.SCHEDULE, weeklyWorkScheduleDO.getId());
            if(Objects.isNull(this.getById(weeklyWorkScheduleDO.getId()).getProcessInstanceId())){
                // 未发起的草稿，直接修改
                this.updateById(weeklyWorkScheduleDO);
            }else {
                // 撤回后的草稿，新增一条新的
                weeklyWorkScheduleDO.setId(null);
                this.save(weeklyWorkScheduleDO);
            }
            LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, createReqVO.getId());
            workScheduleMapper.delete(queryWrapper);
            List<WorkScheduleDO> workSchedules = createReqVO.getWorkSchedules();
            for (WorkScheduleDO schedule : workSchedules) {
                schedule.setWeeklyWorkScheduleId(weeklyWorkScheduleDO.getId());
            }
            workScheduleMapper.insertBatch(workSchedules);
            for (WorkScheduleDO schedule : workSchedules) {
                if (schedule.getEndDate() == null) {
                    sendMessageToInitiator(schedule, false);
                    // 发送消息提醒
                    if (schedule.isNotify()) {
                        taskExecutor.submit(new Runnable() {
                            @Override
                            public void run() {
                                sendMessageToParticipant(schedule);

                            }
                        });
                    }
                }
            }

        }

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(getLoginUserId(),
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(weeklyWorkScheduleDO.getId()))).getCheckedData();
        processInstanceApi.skipFirstTask(processInstanceId, LocalDateTime.now());

        // 将工作流的编号，更新到 OA 一周工作安排表单中
        baseMapper.updateById(new WeeklyWorkScheduleDO().setId(weeklyWorkScheduleDO.getId()).setProcessInstanceId(processInstanceId));
        //不需要选择审批人时自动设置部门负责人审批
        //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
        if(createReqVO.getDeptId() != null) {
            //当前登录用户的默认部门的负责人
            Long defaultLeader = deptApi.getDept(loginUser.getDeptId()).getCheckedData().getLeaderUserId();
            List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
            BpmTaskRespDTO task = bpmTasks.get(1);
            //流程实际选择的部门的负责人
            Long currentLeader = deptApi.getDept(createReqVO.getDeptId()).getCheckedData().getLeaderUserId();
            bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                    .setAssigneeUserId(currentLeader)
                    .setId(task.getId()));
        }
        String deptLeaderName = oaTaskService.getDeptLeader(weeklyWorkScheduleDO.getDeptId());
        if(StrUtil.isEmpty(deptLeaderName)){
            throw exception(APPROVAL_NOT_FOUND);
        }

//        return weeklyWorkScheduleDO.getId();
        return processInstanceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartSchedule(Long loginUserId, ScheduleCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        WeeklyWorkScheduleDO weeklyWorkSchedule = WeeklyWorkScheduleConvert.INSTANCE.convert(reqVO);
        weeklyWorkSchedule.setIsDraft(false);
        weeklyWorkSchedule.setUserId(loginUserId);
        AdminUserRespDTO userDTO = userApi.getUser(weeklyWorkSchedule.getUserId()).getCheckedData();
        if(reqVO.getDeptId() != null) {
            weeklyWorkSchedule.setDeptId(reqVO.getDeptId());
        } else {
            weeklyWorkSchedule.setDeptId(userDTO.getDeptId());
        }

        if (Objects.isNull(weeklyWorkSchedule.getId())) {
            throw exception(SCHEDULE_NOT_EXIST);
        } else {
            // 修改
            this.updateById(weeklyWorkSchedule);
            LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, reqVO.getId());
            workScheduleMapper.delete(queryWrapper);
            List<WorkScheduleDO> workSchedules = reqVO.getWorkSchedules();
            for (WorkScheduleDO schedule : workSchedules) {
                schedule.setWeeklyWorkScheduleId(weeklyWorkSchedule.getId());
            }
            workScheduleMapper.insertBatch(workSchedules);
            for (WorkScheduleDO schedule : workSchedules) {
                if (schedule.getEndDate() == null) {
                    sendMessageToInitiator(schedule, false);
                    // 发送消息提醒
                    if (schedule.isNotify()) {
                        taskExecutor.submit(new Runnable() {
                            @Override
                            public void run() {
                                sendMessageToParticipant(schedule);

                            }
                        });
                    }
                }
            }

            if(reqVO.getIsDraft()) {
                draftService.deleteByItemId(OACategoryConstants.SCHEDULE, weeklyWorkSchedule.getId());
            }
            // 2.设置一下流程流转参数variables，参数没有改变的不用设置
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 3.重新发起流程
            String processInstanceId = reqVO.getProcessInstanceId();
            if(Objects.isNull(reqVO.getProcessInstanceId())){
                processInstanceId=this.getById(reqVO.getId()).getProcessInstanceId();
            }
            BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
            bpmRestartDTO.setLoginUserId(loginUserId);
            bpmRestartDTO.setProcessInstanceId(processInstanceId);
            bpmRestartDTO.setVariables(processInstanceVariables);
            LocalDateTime time = LocalDateTime.now();
            bpmRestartDTO.setTime(time);
            bpmTaskServiceApi.restartProcess(bpmRestartDTO);
            //不需要选择审批人时自动设置部门负责人审批
            //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
            if(reqVO.getDeptId() != null) {
                Long defaultLeader = deptApi.getDept(userApi.getUser(loginUserId).getCheckedData().getDeptId()).getCheckedData().getLeaderUserId();
                List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(reqVO.getProcessInstanceId());
                //重新发起以后最新的节点
                BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
                Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                        .setAssigneeUserId(currentLeader)
                        .setId(task.getId()));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editSchedule(Long loginUserId, ScheduleCreateReqVO reqVO) {
        WeeklyWorkScheduleDO weeklyWorkScheduleDO = WeeklyWorkScheduleConvert.INSTANCE.convert(reqVO);
        cancelMessage(weeklyWorkScheduleDO);
        String processInstanceId = reqVO.getProcessInstanceId();
        LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
        if(Objects.nonNull(reqVO.getId())) {
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, reqVO.getId());
            workScheduleMapper.delete(queryWrapper);
        } else if(Objects.nonNull(reqVO.getProcessInstanceId())) {
            Long id = baseMapper.getByProcessInstanceId(processInstanceId);
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, id);
            workScheduleMapper.delete(queryWrapper);
            weeklyWorkScheduleDO.setId(id);
        }
        baseMapper.updateById(weeklyWorkScheduleDO);
        List<WorkScheduleDO> workSchedules = reqVO.getWorkSchedules();
        for (WorkScheduleDO schedule : workSchedules) {
            schedule.setWeeklyWorkScheduleId(weeklyWorkScheduleDO.getId());
        }
        workScheduleMapper.insertBatch(workSchedules);
        for (WorkScheduleDO schedule : workSchedules) {
            if (schedule.getEndDate() == null) {
                sendMessageToInitiator(schedule, true);
                // 发送消息提醒
                if (schedule.isNotify()) {
                    taskExecutor.submit(new Runnable() {
                        @Override
                        public void run() {
                            sendMessageToParticipant(schedule);

                        }
                    });
                }
            }
        }
    }

    @Override
    public ScheduleRespVO get(Long id, String processInstanceId) {
        WeeklyWorkScheduleDO weeklyWorkScheduleDO = new WeeklyWorkScheduleDO();
        Map<Long, String> person = new HashMap<>();
        if (Objects.nonNull(id)) {
            weeklyWorkScheduleDO = this.getById(id);
            LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, id);
            List<WorkScheduleDO> workSchedules = workScheduleMapper.selectList(queryWrapper);
            for (WorkScheduleDO workSchedule : workSchedules) {
                if (workSchedule.getActivity() != null) {
                    if (workSchedule.getActivity().equals("0")){
                        workSchedule.setMobileActivity("校内安排");
                    } else{
                        workSchedule.setMobileActivity("对外培训");
                    }
                }
                if (workSchedule.getDeptIds() != null) {
                    String deptIds = workSchedule.getDeptIds();
                    List<Long> idList = Arrays.stream(deptIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<DeptRespDTO> depts = deptApi.getDepts(idList).getCheckedData();
                    workSchedule.setDeptNames(depts.stream().map(DeptRespDTO::getName).collect(Collectors.toList()));
                    workSchedule.setDeptNameString(StringUtil.join(depts.stream().map(DeptRespDTO::getName).toArray(), ","));
                }
                if(workSchedule.getPersonnelIds() != null) {
                    String personnelIds = workSchedule.getPersonnelIds();
                    List<Long> personnelIdList = Arrays.stream(personnelIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<AdminUserRespDTO> personnel = userApi.getUsers(personnelIdList).getCheckedData();
                    workSchedule.setPersonnelName(StringUtil.join(personnel.stream().map(AdminUserRespDTO::getNickname).toArray(), ","));
                    person = personnel.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, AdminUserRespDTO::getNickname));
                    workSchedule.setPersonnel(person);
                }
            }
            weeklyWorkScheduleDO.setWorkSchedules(workSchedules);
        } else if (StrUtil.isNotBlank(processInstanceId)) {
            weeklyWorkScheduleDO = getByProcessInstanceId(processInstanceId);
            LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, weeklyWorkScheduleDO.getId());
            List<WorkScheduleDO> workSchedules = workScheduleMapper.selectList(queryWrapper);
            for (WorkScheduleDO workSchedule : workSchedules) {
                if (workSchedule.getActivity().equals("0")) {
                    workSchedule.setMobileActivity("校内安排");
                } else {
                    workSchedule.setMobileActivity("对外培训");
                }
                String deptIds = workSchedule.getDeptIds();
                if(deptIds != null) {
                    List<Long> idList = Arrays.stream(deptIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<DeptRespDTO> depts = deptApi.getDepts(idList).getCheckedData();
                    workSchedule.setDeptNames(depts.stream().map(DeptRespDTO::getName).collect(Collectors.toList()));
                    workSchedule.setDeptNameString(StringUtil.join(depts.stream().map(DeptRespDTO::getName).toArray(), ","));
                }
                String personnelIds = workSchedule.getPersonnelIds();
                if (personnelIds != null) {
                    List<Long> personnelIdList = Arrays.stream(personnelIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<AdminUserRespDTO> personnel = userApi.getUsers(personnelIdList).getCheckedData();
                    workSchedule.setPersonnelName(StringUtil.join(personnel.stream().map(AdminUserRespDTO::getNickname).toArray(), ","));
                    person = personnel.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, AdminUserRespDTO::getNickname));
                }
                workSchedule.setPersonnel(person);
            }
            weeklyWorkScheduleDO.setWorkSchedules(workSchedules);
        }
        ScheduleRespVO respVO = WeeklyWorkScheduleConvert.INSTANCE.convert(weeklyWorkScheduleDO);
//        String copyTo = weeklyWorkScheduleDO.getCopyTo();
//        if (Objects.nonNull(copyTo)) {
//            List<Long> copy = Arrays.stream(copyTo.split(",")).map(Long::parseLong).collect(Collectors.toList());
//            List<AdminUserRespDTO> copyToUser = userApi.getUsers(copy).getCheckedData();
//            //respVO.setCopyToName(StringUtil.join(copyToUser.stream().map(AdminUserRespDTO::getNickname).toArray(), ","));
//        }
        respVO.setProcessInsatnceId(weeklyWorkScheduleDO.getProcessInstanceId());
        respVO.setUserNickName(userApi.getUser(weeklyWorkScheduleDO.getUserId()).getCheckedData().getNickname());
        respVO.setDeptName(deptApi.getDept(weeklyWorkScheduleDO.getDeptId()).getCheckedData().getName());
        if (StrUtil.isNotEmpty(weeklyWorkScheduleDO.getProcessInstanceId())) {
            Map<String, String> taskInfo = taskServiceApi.getNeededTaskInfo(OACategoryConstants.SCHEDULE, getLoginUserId(), processInstanceId, null, null);
            respVO.setTaskName(taskInfo.get("taskName"));
            if(StringUtil.isNotBlank(taskInfo.get("status"))){
                respVO.setResult(Integer.parseInt(taskInfo.get("status")));
            }
            if(StringUtil.isNotBlank(taskInfo.get("operateType"))){
                respVO.setOperateType(Integer.parseInt(taskInfo.get("operateType")));
            }
        }
        return respVO;
    }

    public WeeklyWorkScheduleDO getByProcessInstanceId(String processInstanceId) {
        return baseMapper.selectOne(WeeklyWorkScheduleDO::getProcessInstanceId,processInstanceId);
    }

    @Override
    public PageResult<ScheduleStatisticRespVO> getScheduleStatisticsList(SchedulePageReqVO reqVO) {
        List<Long> scheduleIds = baseMapper.getScheduleIdByInstanceId();
        if (scheduleIds.isEmpty()) {
            return null;
        }
        reqVO.setIdList(scheduleIds);
        IPage<ScheduleStatisticRespVO> myPage = MyBatisUtils.buildPage(reqVO);
        List<ScheduleStatisticRespVO> list = this.baseMapper.selectStatisticsPage(myPage,reqVO);
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    public PageResult<ScheduleDetailRespVO> getScheduleStatisticsDetail(SchedulePageReqVO reqVO) {
        List<Long> scheduleIds = baseMapper.getScheduleIdByInstanceId();
        if (scheduleIds.isEmpty()) {
            return null;
        }
        reqVO.setIdList(scheduleIds);
        IPage<ScheduleDetailRespVO> myPage = MyBatisUtils.buildPage(reqVO);
        List<ScheduleDetailRespVO> list = this.baseMapper.selectDetailPage(myPage,reqVO);
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    public WorkScheduleDO getScheduleDetailById(Long id) {
        return workScheduleMapper.selectById(id);
    }

    @Override
    public void updateStatusById(Long id, Integer status) {
        WeeklyWorkScheduleDO weeklyWorkScheduleDO = new WeeklyWorkScheduleDO();
        weeklyWorkScheduleDO.setId(id);
        weeklyWorkScheduleDO.setStatus(status);
        baseMapper.updateById(weeklyWorkScheduleDO);
    }

    @Override
    public void BatchUpdateStatusByIds(List<Long> ids, Integer status) {
        UpdateWrapper<WeeklyWorkScheduleDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                .set("status",status);
        baseMapper.update(null,updateWrapper);
    }

    @Override
    public String getCopyTo(Long scheduleId) {
        WeeklyWorkScheduleDO scheduleDO = baseMapper.selectById(scheduleId);
        return scheduleDO.getCopyTo();
    }

    @Override
    public String getProcessInstanceId(Long scheduleId) {
        WeeklyWorkScheduleDO scheduleDO = baseMapper.selectById(scheduleId);
        return scheduleDO.getProcessInstanceId();
    }

    @Override
    public PageResult<SchedulePersonnelRespVO> getPersonnel(Long id, PageParam pageParam) {
        WorkScheduleDO workSchedule = workScheduleMapper.selectById(id);
        String personnelIds = workSchedule.getPersonnelIds();
        List<Long> idList = Arrays.stream(personnelIds.split(",")).map(Long::parseLong).collect(Collectors.toList());

        PageResult<SchedulePersonnelRespVO> result = new PageResult<>();
        List<SchedulePersonnelRespVO> list = workScheduleMapper.selectPersonnelInfo(idList, pageParam);
        result.setList(list);
        result.setTotal(workScheduleMapper.selectPersonnelNum(idList, pageParam));
        return result;
    }

    private void sendMessageToParticipant(WorkScheduleDO schedule) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime notifyTime = LocalDateTime.parse(schedule.getNotifyTime(), fmt);
        Random rd = new Random();
        String[][] strArray = {{"MONDAY", "一"}, {"TUESDAY", "二"}, {"WEDNESDAY", "三"}, {"THURSDAY", "四"}, {"FRIDAY", "五"}, {"SATURDAY", "六"}, {"SUNDAY", "日"}};
        LocalDateTime startDate = schedule.getStartDate();
        LocalDate date = startDate.toLocalDate();
        LocalTime time = startDate.toLocalTime();
        String dayOfWeek = String.valueOf(startDate.getDayOfWeek());
        for (int i = 0; i < strArray.length; i++) {
            if (dayOfWeek.equals(strArray[i][0])) {
                dayOfWeek = strArray[i][1];
                break;
            }
        }
        String userIds = schedule.getPersonnelIds();
        List<Long> userIdList = Arrays.stream(userIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        CommonResult<List<AdminUserRespDTO>> userinfoList  =  adminUserApi.getUsers(userIdList);

        if(userinfoList!=null && userinfoList.getData()!=null){

            List<String> mobiles = userinfoList.getData().stream().map(AdminUserRespDTO::getMobile).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            String sms =  "【" + date + "】（星期"+ dayOfWeek +"）【" + time + "】，在【" + schedule.getLocation() + "】，您有一项【" + schedule.getFilledIn() + "】，请及时参与"  ;
            for (String it : mobiles) {
                DateTime now = DateUtil.parse(DateUtil.now());
                DateTime startTime = DateUtil.offsetDay(Date.from(notifyTime.toInstant(ZoneOffset.ofHours(8))), 0);
                String times = startTime.toString();

                Date sendTime = null;
                if( now.compareTo(startTime) < 0 ){
                    //延时发出
                    sendTime = startTime.toJdkDate();
                    //发短信
                    SmsSendReq req = new SmsSendReq();
                    req.setTaskId(rd.nextLong());
                    req.setType(1);
                    req.setPhone(ListUtil.of(it));
                    req.setMessage(sms);
                    req.setSendTime(sendTime);
                    req.setTime(times);
                    req.setTitle(ScheduleMessageEnum.SENDMESSAGE.getTag() + schedule.getId()); //发送消息提醒短信 + scheduleId
                    smsQueApi.sendque(req);
                    //发通知
                    SmsSendReq notificationReq = new SmsSendReq();
                    notificationReq.setTaskId(rd.nextLong());
                    notificationReq.setType(2);
                    notificationReq.setPhone(ListUtil.of(it));
                    notificationReq.setMessage(sms);
                    notificationReq.setSendTime(sendTime);
                    notificationReq.setTime(times);
                    notificationReq.setTitle(ScheduleMessageEnum.SENDNOTIFICATION.getTag() + schedule.getId()); //发送消息提醒短信 + scheduleId
                    smsQueApi.sendque(notificationReq);
                }else if(now.compareTo(startTime) == 0){
                    //立即发出短信
                    Map<String,Object> map = new HashMap<>();
                    map.put("arg1", sms);
                    smsSendService.sendSingleSms(it, null,null ,"admin-sms-login-new",map);
                    //立即发出通知
                    String title = ScheduleMessageEnum.SENDNOTIFICATION.getTag() + schedule.getId();
                    oaNoticeApi.sendNotice(title, sms, ListUtil.of(it));
                }
            }

        }

    }

    private void sendMessageToInitiator(WorkScheduleDO schedule, Boolean isEdit) {
        Random rd = new Random();
        CommonResult<AdminUserRespDTO> userinfo;

        AdminUserRespDTO loginUser = userApi.getUser(getLoginUserId()).getCheckedData();
        if(Boolean.TRUE.equals(isEdit)) {
            Long weeklyWorkScheduleId = schedule.getWeeklyWorkScheduleId();
            WeeklyWorkScheduleDO weeklyWorkSchedule = getById(weeklyWorkScheduleId);
            Long userId = weeklyWorkSchedule.getUserId();
            userinfo = adminUserApi.getUser(userId);
        } else {
            userinfo = adminUserApi.getUser(loginUser.getId());
        }

        if(userinfo!=null){

            String mobile = userinfo.getCheckedData().getMobile();

            String sms =  "您有一项【" + schedule.getFilledIn() + "】，还有24小时即将开始，请知晓";

            DateTime now = DateUtil.parse(DateUtil.now());
            DateTime startTime = DateUtil.offsetDay(Date.from(schedule.getStartDate().toInstant(ZoneOffset.ofHours(8))), -1);
            String time = startTime.toString();

            Date sendTime = null;
            //开始时间前24小时内不发消息提醒
            if( now.compareTo(startTime) < 0 ){
                //延时发出
                sendTime = startTime.toJdkDate();
                //发短信
                SmsSendReq req = new SmsSendReq();
                req.setTaskId(rd.nextLong());
                req.setType(1);
                req.setPhone(ListUtil.of(mobile));
                req.setMessage(sms);
                req.setSendTime(sendTime);
                req.setTime(time);
                req.setTitle(ScheduleMessageEnum.SENDMESSAGE.getTag() + schedule.getId()); //发送消息提醒短信 + scheduleId
                smsQueApi.sendque(req);
                //发通知
                SmsSendReq notificationReq = new SmsSendReq();
                notificationReq.setTaskId(rd.nextLong());
                notificationReq.setType(2);
                notificationReq.setPhone(ListUtil.of(mobile));
                notificationReq.setMessage(sms);
                notificationReq.setSendTime(sendTime);
                notificationReq.setTime(time);
                notificationReq.setTitle(ScheduleMessageEnum.SENDNOTIFICATION.getTag() + schedule.getId()); //发送消息提醒短信 + scheduleId
                smsQueApi.sendque(notificationReq);
            }else if(now.compareTo(startTime) == 0){
                //刚还离开始时间24小时立即发出短信
                Map<String,Object> map = new HashMap<>();
                map.put("arg1", sms);
                smsSendService.sendSingleSms(mobile, null,null ,"admin-sms-login-new",map);
                //立即发出通知
                String title = ScheduleMessageEnum.SENDNOTIFICATION.getTag() + schedule.getId();
                oaNoticeApi.sendNotice(title, sms, ListUtil.of(mobile));
            }

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSchedule(Integer id) {
        WeeklyWorkScheduleDO weeklyWorkSchedule = baseMapper.selectById(id);
        if(weeklyWorkSchedule != null) {
            //该填报单包含的工作内容中配置的消息提醒同步取消
            LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, id);
            List<WorkScheduleDO> workSchedules = workScheduleMapper.selectList(queryWrapper);
            for (WorkScheduleDO schedule : workSchedules) {
                // 取消消息提醒
                if (schedule.isNotify()) {
                    taskExecutor.submit(new Runnable() {
                        @Override
                        public void run() {
                            cancelMessage(schedule);
                        }
                    });
                }
            }
            String processInstanceId = weeklyWorkSchedule.getProcessInstanceId();
            bpmProcessInstanceApi.removeProcess(WebFrameworkUtils.getLoginUserId(),processInstanceId);
            baseMapper.deleteById(id);
        }
    }

    private void cancelMessage(WorkScheduleDO schedule) {
        String messageTitle = ScheduleMessageEnum.SENDMESSAGE.getTag() + schedule.getId();
        String notificationTitle = ScheduleMessageEnum.SENDNOTIFICATION.getTag() + schedule.getId();
        deptApi.sendMessageRefreshList(ListUtil.of(messageTitle, notificationTitle));
    }

    public void cancelMessage(WeeklyWorkScheduleDO schedules) {
        LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
        if (schedules.getId() == null) {
            schedules.setId(baseMapper.getByProcessInstanceId(schedules.getProcessInstanceId()));
        }
        queryWrapper.eq(WorkScheduleDO::getWeeklyWorkScheduleId, schedules.getId());
        List<WorkScheduleDO> workSchedules = workScheduleMapper.selectList(queryWrapper);

        for (WorkScheduleDO schedule : workSchedules) {
            String messageTitle = ScheduleMessageEnum.SENDMESSAGE.getTag() + schedule.getId();
            String notificationTitle = ScheduleMessageEnum.SENDNOTIFICATION.getTag() + schedule.getId();
            deptApi.sendMessageRefreshList(ListUtil.of(messageTitle, notificationTitle));
        }
    }
}
