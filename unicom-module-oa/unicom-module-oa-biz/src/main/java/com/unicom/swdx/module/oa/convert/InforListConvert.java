package com.unicom.swdx.module.oa.convert;


import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.InforExcelVO;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.InforRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.extern.slf4j.Slf4j;


import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.module.oa.convert.ReceiveConvert.deptApi;
import static jdk.nashorn.internal.objects.NativeString.indexOf;

@Slf4j
public class InforListConvert {


    public static void convertList(List<InforExcelVO> datas  ,List<InforRespVO> list  ,List<DictDataRespDTO>  dict ,List<DictDataRespDTO>  digreetype) {


        for (int i = 0; i < datas.size(); i++) {
            InforExcelVO temp = datas.get(i);
            InforRespVO  inforRespVO  = list.get(i);



            try {
                List<UserinforDO> userinforDO = inforRespVO.getUserinforDOList().stream().filter(it->it.getUtype() ==4).collect(Collectors.toList());
//                if(userinforDO.size()==1){
//                    temp.setMleader(userinforDO.get(0).getUname());
//                    temp.setMleaderId(userinforDO.get(0).getUid());
//                }else{
//                    temp.setMleader(  userinforDO.stream().map(it-> it.getUname()) .reduce( (k,v)-> k +"," + v  ).get());
                if(userinforDO.size()!=0){
                       temp.setMleader(userinforDO.get(0).getUname());
                       temp.setMleaderId(userinforDO.get(0).getUid());
                }

            }catch (Exception e){
                log.info("InforListConvert setMleader::  {}" , e.getMessage());
            }

            try {
                List<UserinforDO> userinforDO = inforRespVO.getUserinforDOList().stream().filter(it->it.getUtype() ==5).collect(Collectors.toList());

                if(userinforDO.size()==1){
                    temp.setSleader(userinforDO.get(0).getUname());
                }else{
                    temp.setSleader(  userinforDO.stream().map(it-> it.getUname()) .reduce( (k,v)-> k +"," + v  ).get());
                }

            }catch (Exception e){
                log.info("InforListConvert setSleader::  {}" , e.getMessage());
            }




            Optional<DictDataRespDTO>      optionalx   = dict.stream().filter(it -> Integer.parseInt(it.getValue()) == inforRespVO.getTasktype()  ).findFirst() ;

            if(optionalx.isPresent()){
                DictDataRespDTO  dictDataRespDTO =       optionalx.get();
                temp.setTasktype(dictDataRespDTO.getLabel());
            }else{
                temp.setTasktype(inforRespVO.getTasktype().toString());
            }





            Optional<UserinforDO>  optional  = inforRespVO.getUserinforDOList().stream().filter(it->it.getUtype().intValue()==0).findFirst();


            if(optional.isPresent()){

                UserinforDO dptdo  =     optional .get();


                String uname  = inforRespVO.getUserinforDOList().stream().filter(it->it.getUtype().intValue()==1).map(it->it.getUname()).reduce((k, v)-> k +"," + v  ).get();

                List<String> sdept =  inforRespVO.getUserinforDOList().stream().filter(it->it.getUtype().intValue()==3).map(it->it.getDptname()).collect(Collectors.toList());

                List<DeptRespDTO> deptsList = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId()).getData();
                List<String> deptsNameList = deptsList.stream().map(it -> it.getName()).collect(Collectors.toList());

                Collections.sort(sdept, Comparator.comparingInt(a -> indexOf(deptsNameList, a)));

                if(sdept.contains("相关部室")){
                    sdept.remove("相关部室");
                    sdept.add("相关部室");
                }

                if(sdept.contains("各教研部门")){
                    sdept.remove("各教研部门");
                    sdept.add("各教研部门");
                }
                List<String> sdeptId =  inforRespVO.getUserinforDOList().stream().filter(it->it.getUtype().intValue()==3).map(it->it.getDptid()).map(Object::toString).collect(Collectors.toList());

//                Set<String> sdeptset = new HashSet<>(sdept);
                LinkedHashSet<String> sdeptset = new LinkedHashSet<>(sdept);
//                LinkedHashSet<String> sdeptIdset = new LinkedHashSet<>(sdeptId);
                Set<String> sdeptIdset = new HashSet<>(sdeptId);
;
                Optional<String>  optional1  = sdeptset.stream().filter(Objects::nonNull).reduce((k, v)-> k +"," + v  );
                Optional<String>  optional2  = sdeptIdset.stream().filter(Objects::nonNull).reduce((k, v)-> k +"," + v  );

                if(optional1.isPresent()){
                    String surperdpt  =  optional1.get();
                    temp.setSuperdpt(surperdpt);
                }

                if(optional2.isPresent()){
                    String surperdptId  =  optional2.get();
                    temp.setSurperdptId(surperdptId);
                }

                temp.setMdepname(dptdo.getDptname());
                temp.setMusername(uname);


            }




        }

    }


}
