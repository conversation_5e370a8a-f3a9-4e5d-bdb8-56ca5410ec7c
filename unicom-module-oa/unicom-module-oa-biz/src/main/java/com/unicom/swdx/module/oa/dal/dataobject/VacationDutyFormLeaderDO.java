package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 寒暑假坐值班表单
 * <AUTHOR>
 */
@TableName(value = "oa_vacation_duty_form_leader",autoResultMap = true)
@Data
@KeySequence("oa_vacation_duty_form_leader_id_seq")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VacationDutyFormLeaderDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 对应的寒暑假值班id
     */
    private String vacationDutyIds;

    /**
     * 坐值班人员id
     */
    private String personnelId;

    /**
     * 处室id
     */
    private String deptId;

    /**
     * 处室名称
     */
    private String deptName;

    /**
     * 坐值班人员名称
     */
    private String personnel;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 办公电话
     */
    private String telephone;

    /**
     * 坐值班类型 1坐班 2值班
     */
    private String dutyType;
}
