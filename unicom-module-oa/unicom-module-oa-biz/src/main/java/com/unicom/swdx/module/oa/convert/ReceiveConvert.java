package com.unicom.swdx.module.oa.convert;

import cn.hutool.extra.spring.SpringUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceiveRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

@Mapper
public interface ReceiveConvert {

    ReceiveConvert INSTANCE = Mappers.getMapper(ReceiveConvert.class);

    AdminUserApi userApi = SpringUtil.getBean(AdminUserApi.class);

    DeptApi deptApi = SpringUtil.getBean(DeptApi.class);

    PageResult<ReceiveRespVO> convertPage(PageResult<ReceiveDO> page);

    default ReceiveRespVO receiveDOToReceiveRespVO(ReceiveDO receiveDO) {
        if (receiveDO == null) {
            return null;
        } else {
            ReceiveRespVO receiveRespVO = new ReceiveRespVO();
            receiveRespVO.setId(receiveDO.getId());
            receiveRespVO.setCategory(receiveDO.getCategory());
            receiveRespVO.setApplyTime(receiveDO.getApplyTime());
            receiveRespVO.setItemId(receiveDO.getItemId());
            receiveRespVO.setContent(receiveDO.getContent());
            if (Objects.nonNull(receiveDO.getPromoterUserId())) {
                AdminUserRespDTO user = userApi.getUser(Long.parseLong(receiveDO.getPromoterUserId())).getCheckedData();
                if(Objects.nonNull(user)){
                    receiveRespVO.setUserNickName(user.getNickname());

                    DeptRespDTO deptRespDTO = deptApi.getDept(user.getDeptId()).getCheckedData();
                    if(Objects.nonNull(deptRespDTO)){
                        receiveRespVO.setDeptName(deptApi.getDept(user.getDeptId()).getCheckedData().getName());
                    }else{
                        receiveRespVO.setDeptName("已删除该部门");
                    }


                }else {
                    receiveRespVO.setUserNickName("已删除该用户");
                }

            }
            receiveRespVO.setIsRead(receiveDO.getIsRead());
            receiveRespVO.setIsEnd(receiveDO.getIsEnd());
            receiveRespVO.setProcessInstanceId(receiveDO.getProcessInstanceId());
            return receiveRespVO;
        }
    }

}
