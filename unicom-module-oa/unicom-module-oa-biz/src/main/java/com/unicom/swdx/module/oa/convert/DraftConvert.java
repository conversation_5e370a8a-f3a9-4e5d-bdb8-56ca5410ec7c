package com.unicom.swdx.module.oa.convert;

import cn.hutool.extra.spring.SpringUtil;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.draft.DraftRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DraftConvert {

    DraftConvert INSTANCE = Mappers.getMapper(DraftConvert.class);

    AdminUserApi userApi = SpringUtil.getBean(AdminUserApi.class);

    PageResult<DraftRespVO> convertPage(PageResult<DraftDO> page);

    default DraftRespVO draftDOToDraftRespVO(DraftDO draftDO) {
        if (draftDO == null) {
            return null;
        } else {
            DraftRespVO draftRespVO = new DraftRespVO();
            draftRespVO.setId(draftDO.getId());
            draftRespVO.setCategory(draftDO.getCategory());
            draftRespVO.setItemId(draftDO.getItemId());
            draftRespVO.setUserNickName(userApi.getUser(Long.parseLong(draftDO.getUserId())).getCheckedData().getNickname());
            draftRespVO.setUpdateTime(draftDO.getUpdateTime());
            return draftRespVO;
        }
    }


}
