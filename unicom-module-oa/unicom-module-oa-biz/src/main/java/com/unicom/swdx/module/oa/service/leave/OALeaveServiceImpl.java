package com.unicom.swdx.module.oa.service.leave;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.*;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.convert.OALeaveConvert;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.dal.kingbase.OALeaveMapper;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.common.CalendarWnlApi;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

import static com.unicom.swdx.module.bpm.enums.ErrorCodeConstants.OA_LEAVE_NOT_EXISTS;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.oa.enums.PostTypeEnum.*;

/**
 * OA 请假申请 Service 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Service
public class OALeaveServiceImpl extends ServiceImpl<OALeaveMapper, OALeaveDO> implements OALeaveService {

    /**
     * OA 请假对应的流程定义 KEY
     */
    public static final String PROCESS_KEY = "OA-askforleave";

    @Resource
    private OALeaveMapper leaveMapper;


    @Resource
    private DraftService draftService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private PostApi postApi;

    @Resource
    private CalendarWnlApi calendarWnlApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private SmsSendApi smsSendApi;

    @Resource
    private LectureMapper lectureMapper;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Resource
    private ReceiveApi receiveApi;


    /**
     * key = 岗位标识，value = postType
     */
    private Map<String, Integer> postCodeMap;

    private List<String> postCodeList;

    @PostConstruct
    public void init() {
        postCodeMap = new HashMap<>();
        postCodeMap.put(CLERK.getPostCode(), 1);
        postCodeMap.put(INVESTIGATOR.getPostCode(), 2);
        postCodeMap.put(VICE_DIRECTOR.getPostCode(), 2);
        postCodeMap.put(DIRECTOR.getPostCode(), 3);
        postCodeMap.put(SCHOOL_LEADER.getPostCode(), 4);
        postCodeMap.put(INSPECTOR_2.getPostCode(), 3);
        postCodeMap.put(INSPECTOR_1.getPostCode(), 4);

        postCodeList = CollUtil.newArrayList(postCodeMap.keySet());
    }

    @Override
    public Integer calculateLeaveDay(LocalDate startTime, LocalDate endTime){
        Integer day = calendarWnlApi.calculateWorkDay(startTime, endTime).getCheckedData();
        if(day>30){
            throw exception(LEAVE_DAY_GT_30);
        }
        if(day==0){
            throw exception(LEAVE_DAY_CAN_NOT_BE_0);
        }
        return day;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDraft(Long userId, OALeaveCreateReqVO reqVO) {
        // 存草稿咯
        OALeaveDO leave = OALeaveConvert.INSTANCE.convert(reqVO);
        leave.setIsDraft(true);
        if(Objects.isNull(leave.getId())){
            AdminUserRespDTO userDTO = userApi.getUser(userId).getCheckedData();
            leave.setUserId(userId);
            if(reqVO.getDeptId() != null) {
                leave.setDeptId(reqVO.getDeptId());
            } else {
                leave.setDeptId(userDTO.getDeptId());
            }
            leaveMapper.insert(leave);

            // 写入草稿表
            DraftDO draftDO = new DraftDO();
            draftDO.setCategory(OACategoryConstants.LEAVE);
            draftDO.setUserId(userId.toString());
            draftDO.setItemId(leave.getId());
            draftDO.setUpdateTime(LocalDateTime.now());
            draftService.save(draftDO);
        }else {
            // 编辑草稿
            leaveMapper.updateById(leave);
            draftService.update(new LambdaUpdateWrapper<DraftDO>().eq(DraftDO::getItemId,leave.getId())
                    .set(DraftDO::getUpdateTime,LocalDateTime.now()));
        }
//        DraftDO draftDO = new DraftDO();
//        if(Objects.isNull(reqVO.getIsDraft()) || !reqVO.getIsDraft()){
//            // 写入草稿表
//        }
        return true;
    }

    @Override
    public Integer getPostType(Long userId) {
        Integer postType = null;
        Boolean isDeptLeader = deptApi.isLeaderUser(userId).getData();
        PostRespDTO post = postApi.getMinSortPostByUser(userId, postCodeList);

        if (Objects.isNull(post)) {
            //判断是否是部门负责人
            if(isDeptLeader){
                postType = 3;
            }else {
                throw exception(POST_ERROR);
            }
        }else {
            if(isDeptLeader){
                postType = Math.max(3,postCodeMap.get(post.getCode()));
            }else {
                postType = postCodeMap.get(post.getCode());
            }
        }
        List<Integer> list = Arrays.asList(1, 2, 3, 4);
        if(!list.contains(postType)){
            throw exception(POST_ERROR);
        }
        return postType;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLeave(Long userId, @Valid OALeaveCreateReqVO createReqVO) {

        Integer day = calculateLeaveDay(createReqVO.getStartTime().toLocalDate(), createReqVO.getEndTime().toLocalDate());
        createReqVO.setDay(day);

        Integer postType = getPostType(userId);

        //转换实体类
        OALeaveDO leave = OALeaveConvert.INSTANCE.convert(createReqVO)
                .setLaunchTime(LocalDateTime.now())
                .setUserId(userId)
                .setResult(BpmProcessInstanceResultEnum.PROCESS.getResult())
                .setIsDraft(false);

        leave.setEndTime(LocalDateTime.of(leave.getEndTime().toLocalDate(), LocalTime.of(23,59,59)));
        checkLeaveTimeOverlap(userId, leave.getStartTime(),leave.getEndTime());

        AdminUserRespDTO userDTO = userApi.getUser(leave.getUserId()).getCheckedData();
        if(createReqVO.getDeptId() != null) {
            leave.setDeptId(createReqVO.getDeptId());
        }else {
            leave.setDeptId(userDTO.getDeptId());
        }

        if (Objects.isNull(leave.getId())) {
            // 新增
            // 插入 OA 请假单
            this.save(leave);
        } else {
            // id不为空表示从草稿箱发起，可能是未发起过的草稿，也可能是撤回后的草稿
            // 删除草稿记录
            draftService.deleteByItemId(OACategoryConstants.LEAVE, leave.getId());
            if(Objects.isNull(this.getLeave(leave.getId()).getProcessInstanceId())){
                // 未发起的草稿，直接修改
                this.updateById(leave);
            }else {
                // 撤回后的草稿，新增一条新的
                leave.setId(null);
                this.save(leave);
            }
        }

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();

        processInstanceVariables.put("postType", postType);
        processInstanceVariables.put("leaveDay", createReqVO.getDay());

        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(String.valueOf(leave.getId()))).getCheckedData();

        LocalDateTime time = LocalDateTime.now().withSecond(0);
        if(processInstanceApi.skipFirstTask(processInstanceId, time).getCheckedData()){
            // 将工作流的编号，更新到 OA 请假单中
            leaveMapper.updateById(new OALeaveDO().setId(leave.getId()).setProcessInstanceId(processInstanceId));

            //判断下一步是否为选择审批人
            if(postType>2){
                OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
                pick.setUserIds(createReqVO.getUserIds());
                pick.setChargeLeaderSeq(createReqVO.getChargeLeaderSeq());
                oaTaskService.pickApprovals(pick, time.plusSeconds(1L));
            }else {
                //不需要选择审批人时自动设置部门负责人审批
                //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
                if(createReqVO.getDeptId() != null) {
                    Long defaultLeader = deptApi.getDept(userDTO.getDeptId()).getCheckedData().getLeaderUserId();
                    List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
                    BpmTaskRespDTO task = bpmTasks.get(1);
                    Long currentLeader = deptApi.getDept(createReqVO.getDeptId()).getCheckedData().getLeaderUserId();
                    bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                            .setAssigneeUserId(currentLeader)
                            .setId(task.getId()));
                }
                String deptLeaderName = oaTaskService.getDeptLeader(leave.getDeptId());
                if(StrUtil.isEmpty(deptLeaderName)){
                    throw exception(APPROVAL_NOT_FOUND);
                }
            }
        }
//        return leave.getId();
        return processInstanceId;
    }

    private void checkLeaveTimeOverlap(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        List<OALeaveDO> list = leaveMapper.getMutilData(startTime.toLocalDate().toString(), endTime.toLocalDate().toString(), userId, null);
        if(CollUtil.isNotEmpty(list)){
            throw exception(LEAVE_TIME_OVERLAP);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartLeave(Long loginUserId, OALeaveCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        OALeaveDO leave = OALeaveConvert.INSTANCE.convert(reqVO)
                .setResult(BpmProcessInstanceResultEnum.PROCESS.getResult()).setIsDraft(false);
        if (Objects.isNull(leave.getId())) {
            throw exception(LEAVE_NOT_EXIST);
        } else {
            // 校验天数
            Integer day = calculateLeaveDay(reqVO.getStartTime().toLocalDate(), reqVO.getEndTime().toLocalDate());
            leave.setDay(day);
            reqVO.setDay(day);
            if(reqVO.getDay()>30){
                throw exception(LEAVE_DAY_GT_30);
            }
            leave.setEndTime(LocalDateTime.of(leave.getEndTime().toLocalDate(), LocalTime.of(23,59,59)));
            checkLeaveTimeOverlap(loginUserId, leave.getStartTime(),leave.getEndTime());
            // 修改
            leave.setResult(BpmProcessInstanceStatusEnum.RUNNING.getStatus());
            this.updateById(leave);
            if(reqVO.getIsDraft()) {
                draftService.deleteByItemId(OACategoryConstants.LEAVE, leave.getId());
            }
            // 2.设置一下流程流转参数variables
            Map<String, Object> processInstanceVariables = new HashMap<>();
            processInstanceVariables.put("leaveDay", day);
            // 3.重新发起流程
//            LocalDateTime time = bpmTaskServiceApi.getTime(reqVO.getProcessInstanceId()).getCheckedData();
            BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
            bpmRestartDTO.setLoginUserId(loginUserId);
            bpmRestartDTO.setProcessInstanceId(reqVO.getProcessInstanceId());
            bpmRestartDTO.setVariables(processInstanceVariables);
            LocalDateTime time = LocalDateTime.now();
            bpmRestartDTO.setTime(time);
            if(bpmTaskServiceApi.restartProcess(bpmRestartDTO).getCheckedData()){
                // 获取岗位类型
                Integer postType = getPostType(loginUserId);
                //判断下一步是否为选择审批人
                if(postType>2){
                    OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                    pick.setTaskId(oaTaskService.getTaskId(reqVO.getProcessInstanceId()));
                    pick.setUserIds(reqVO.getUserIds());
                    pick.setChargeLeaderSeq(reqVO.getChargeLeaderSeq());
                    oaTaskService.pickApprovals(pick,time.plusSeconds(1L));
                } else {
                    //不需要选择审批人时自动设置部门负责人审批
                    //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
                    if(reqVO.getDeptId() != null) {
                        Long defaultLeader = deptApi.getDept(userApi.getUser(loginUserId).getCheckedData().getDeptId()).getCheckedData().getLeaderUserId();
                        List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(reqVO.getProcessInstanceId());
                        //重新发起以后最新的节点
                        BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
                        Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                        bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                                .setAssigneeUserId(currentLeader)
                                .setId(task.getId()));
                    }
                }
            }
        }
    }

    @Override
    public void updateLeaveResult(Long id, Integer result) {

        validateLeaveExists(id);
        leaveMapper.updateById(new OALeaveDO().setId(id).setResult(result));

    }

    private void validateLeaveExists(Long id) {

        if (leaveMapper.selectById(id) == null) {
            throw exception(OA_LEAVE_NOT_EXISTS);
        }
    }

    @Override
    public OALeaveDO getLeave(Long id) {
        return leaveMapper.selectById(id);
    }

    @Override
    public boolean getMutilData(String startTime, String endTime, String processInstanceId) {
        List<OALeaveDO> result = leaveMapper.getMutilData(startTime + " 00:00:00", endTime + " 23:59:59", getLoginUserId(), processInstanceId);
        if (CollUtil.isNotEmpty(result)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean saveVaribale(Map<String, Object> map, String proId) {
        List<String> times = (List<String>) map.get("time");
        OALeaveDO oaLeaveDO = new OALeaveDO();
        oaLeaveDO.setDay(Integer.valueOf(map.get("days").toString()));
        oaLeaveDO.setResult(1);
        oaLeaveDO.setProcessInstanceId(proId);
        oaLeaveDO.setUserId(getLoginUserId());
        oaLeaveDO.setType(1);
        oaLeaveDO.setReason("1");
        oaLeaveDO.setStartTime(DateUtil.parseLocalDateTime(times.get(0) + " 00:00:00",DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        oaLeaveDO.setEndTime(DateUtil.parseLocalDateTime(times.get(1) + " 23:59:59",DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        leaveMapper.insert(oaLeaveDO);
        return true;
    }

    @Override
    public boolean updateResult(String proId, Integer result) {
        leaveMapper.updateResultByProcId(proId,result);
        return true;
    }

    @Override
    public boolean updateResultById(Long id, Integer result) {
        OALeaveDO oaLeaveDO = new OALeaveDO();
        oaLeaveDO.setId(id);
        oaLeaveDO.setResult(result);
        leaveMapper.updateById(oaLeaveDO);
        return true;
    }

    @Override
    public Map<String, LocalDate> getDateById(Long id) {
        Map<String,LocalDate> map = new HashMap<>();
        OALeaveDO oaLeaveDO = leaveMapper.selectById(id);
        // map.put("id",outReportDO.getId().toString());
        map.put("startDate",oaLeaveDO.getStartTime().toLocalDate());
        map.put("endDate",oaLeaveDO.getEndTime().toLocalDate());
        return map;
    }

    @Override
    public boolean judgeIfDirector(Long loginUserId) {
        Boolean superAdmin = permissionApi.hasAnyRoles(loginUserId, "super_admin").getCheckedData();

        if(superAdmin || deptApi.isLeaderUser(loginUserId).getCheckedData() || postApi.isHRDirector(loginUserId).getCheckedData()){
            return true;
        }
        return false;
    }

    @Override
    @TenantIgnore
    public PageResult<LeaveStatisticsRespVO> getLeaveStatisticsList(Long loginUserId, OALeaveStatisticPageReqVO reqVO) {
        Long tenantId = SecurityFrameworkUtils.getTenantId();
        // 超管
        boolean isSuperAdmin = permissionApi.hasAnyRoles(loginUserId, "super_admin").getCheckedData();
        if(!isSuperAdmin){
            reqVO.setTenantId(tenantId);
        }
        // 如果登陆人不是部门负责人或者人事处相关负责人，也不是超管，直接设置为个人视角
        if(!isSuperAdmin && !judgeIfDirector(loginUserId)){
            reqVO.setIsPersonal(true);
        }
        //先判断是否为个人视角
        if(reqVO.getIsPersonal()){
            reqVO.setUserId(loginUserId);
        }
        if(Objects.isNull(reqVO.getDeptId())){
            //判断是否为部门负责人
            if(deptApi.isLeaderUser(loginUserId).getData()){
                List<Long> deptIds = new ArrayList<>();
                Long deptId = userApi.getUser(loginUserId).getCheckedData().getDeptId();
                deptIds.add(deptId);
                List<Long> childDept = CollectionUtils.convertList(deptApi.getAllChildrenDept(deptId).getCheckedData(), DeptRespDTO::getId);
                CollUtil.addAll(deptIds, childDept);
                reqVO.setDeptList(deptIds);
            }
            //判断是否为人事部相关负责人
            if(isSuperAdmin || postApi.isHRDirector(loginUserId).getData()){
                reqVO.setDeptList(null);
                reqVO.setDeptId(null);
            }
        }
        if(StrUtil.isEmpty(reqVO.getOrderBy())){
            reqVO.setOrderBy("total");
        }
        if(StrUtil.isEmpty(reqVO.getSeq())){
            reqVO.setSeq("desc");
        }else {
            if(Objects.equals("0",reqVO.getSeq())){
                reqVO.setSeq("asc");
            }else {
                reqVO.setSeq("desc");
            }
        }
        IPage<LeaveStatisticsRespVO> myPage = MyBatisUtils.buildPage(reqVO);
        List<LeaveStatisticsRespVO> list = this.baseMapper.selectLeaveStatisticsPage(myPage,reqVO);
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    @TenantIgnore
    public Map<String, Object> getPersonalLeaveCount(Long userId, String startTime, String endTime) {
        Map<String,Object> map = leaveMapper.selectLeaveCountByUserId(userId, startTime, endTime);
        if(CollUtil.isEmpty(map)){
            map = new HashMap<>();
            map.put("userId",userId);
            map.put("total",0);
            map.put("ill",0);
            map.put("annual",0);
            map.put("nursing",0);
            map.put("childcare",0);
            map.put("parental",0);
            map.put("breastfeeding",0);
            map.put("onlychild",0);
            map.put("overtime",0);
            map.put("busy",0);
            map.put("marry",0);
            map.put("birth",0);
            map.put("death",0);
            map.put("hurt",0);
            map.put("visit",0);
            map.put("other",0);
            map.put("counts",0);
            map.put("longest",0);
        }
        return map;
    }

    @Override
    @TenantIgnore
    public PageResult<OALeaveRespVO> getLeaveInfoList(OALeavePageReqVO reqVO) {
        IPage<OALeaveRespVO> myPage = MyBatisUtils.buildPage(reqVO);
        List<OALeaveRespVO> list = this.baseMapper.selectLeaveInfoPage(myPage,reqVO);
        return new PageResult<>(list,myPage.getTotal());
    }

    @Override
    @TenantIgnore
    public OALeaveDO getByProcessInstanceId(String processInstanceId) {

        return baseMapper.selectOne(OALeaveDO::getProcessInstanceId,processInstanceId);
    }

    @Override
    @TenantIgnore
    public OALeaveRespVO get(Long id, String processInstanceId, Boolean isReceived) {
        OALeaveDO leaveDO = new OALeaveDO();
        if (Objects.nonNull(id)) {
            leaveDO =  this.getById(id);
        } else if (StrUtil.isNotBlank(processInstanceId)) {
            leaveDO = getByProcessInstanceId(processInstanceId);
        }
        OALeaveRespVO leaveRespVO = OALeaveConvert.INSTANCE.convert(leaveDO);

        leaveRespVO.setDeptName(deptApi.getDept(leaveDO.getDeptId()).getCheckedData().getName());
        leaveRespVO.setNickname(userApi.getUser(leaveDO.getUserId()).getCheckedData().getNickname());

        if(StrUtil.isNotEmpty(leaveDO.getProcessInstanceId())){
            Map<String,String> info = bpmTaskServiceApi.getNeededTaskInfo(OACategoryConstants.LEAVE,getLoginUserId(),
                    processInstanceId, isReceived, leaveDO.getIsDealt());
            leaveRespVO.setTaskName(info.get("taskName"));
            if(Objects.nonNull(info.get("operateType"))){
                leaveRespVO.setOperateType(Integer.valueOf(info.get("operateType")));
            }
            // 用流程的Status赋值给请假的Result字段
            if(Objects.nonNull(info.get("status"))){
                leaveRespVO.setResult(Integer.valueOf(info.get("status")));
            }
        }
        return leaveRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealLeave(OADealLeaveVO dealLeaveVO) {
        //更新签名
        lectureMapper.insertImage(WebFrameworkUtils.getLoginUserId(),dealLeaveVO.getHandSignature());

        OALeaveDO leaveDO = new OALeaveDO();
        leaveDO.setId(dealLeaveVO.getId());


        OALeaveDO leave = getLeave(dealLeaveVO.getId());
        if(leave.getEndTime().toLocalDate().isBefore(dealLeaveVO.getEndTime())) {
            throw exception(LEAVE_DEAL_TIME_AFTER_END_TIME);
        }
        leave.setEndTime(LocalDateTime.of(dealLeaveVO.getEndTime(), LocalTime.of(23,59,59)));

        if(leave.getEndTime().isBefore(leave.getStartTime())){
            throw exception(LEAVE_END_TIME_BEFORE_START_TIME);
        }

        // 添加销假时间
        leaveDO.setDealTime(LocalDateTime.now());

        // 更新假期真实结束时间为人事指定日期
        leaveDO.setEndTime(leave.getEndTime());

        // 更新请假天数为新的结束时间到开始时间的差值,除去节假日
        Integer day = calculateLeaveDay(leave.getStartTime().toLocalDate(), dealLeaveVO.getEndTime());
        leaveDO.setDay(day);

        // 添加上班时间
        //leaveDO.setReturnWorkTime(LocalDateTime.of(dealLeaveVO.getReturnWorkTime(), LocalTime.of(0,0,0)));

        // 设置为已销假
        leaveDO.setIsDealt(true);

        this.baseMapper.updateById(leaveDO);

        //销假完成后日程同步更改id和endTime  //leave.getProcessInstanceId()这个是流程id
        ScheduleDto scheduleDto = new ScheduleDto();
        scheduleDto.setProcessInstanceId(leave.getProcessInstanceId());
        BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO=
                processInstanceApi.getProcessInstanceInfo(leave.getProcessInstanceId()).getCheckedData();
        scheduleDto.setEndTime(Date.from(leaveDO.getEndTime().atZone(ZoneId.systemDefault()).toInstant()));
        scheduleDto.setUserId(bpmProcessInstanceRespDTO.getStartUser().getId());
        scheduleServiceApi.deleteScheduleOther(scheduleDto);


        // 添加一条任务记录
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("人事处销假");
        bpmTaskExtDTO.setTaskDefKey("hr-deal-leave");
        bpmTaskExtDTO.setTaskId("hr-deal-leave");
        bpmTaskExtDTO.setResult(2);
        bpmTaskExtDTO.setProcessInstanceId(leave.getProcessInstanceId());
        bpmTaskExtDTO.setProcessDefinitionId(leave.getProcessInstanceId());
        LocalDateTime now = LocalDateTime.now();
        bpmTaskExtDTO.setCreateTime(now);
        bpmTaskExtDTO.setEndTime(now);
        bpmTaskExtDTO.setTaskType(5);
        Map<String, Object> map = new HashMap<>();
        map.put("dealWay","人事处销假");
//        AdminUserRespDTO user = userApi.getUser(getLoginUserId()).getCheckedData();
        // 手写签字图片
        bpmTaskExtDTO.setImageUrl(dealLeaveVO.getHandSignature());
//        map.put("handSignature",user.getNickname());
        String leaveStart = leave.getStartTime().toLocalDate().toString();
        String leaveEnd = dealLeaveVO.getEndTime().toString();
        map.put("leaveStart",leaveStart);
        map.put("leaveEnd",leaveEnd);
        bpmTaskExtDTO.setParamsMap(map);
        bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);

        // 发送短信通知
        // 短信内容———— 【申请人姓名】，您申请的假期变更已完成，假期开始时间：【假期开始时间】，假期结束时间：【假期结束时间】
        AdminUserRespDTO applyUser = userApi.getUser(leave.getUserId()).getCheckedData();
        String nickname = applyUser.getNickname();
        String mobile = applyUser.getMobile();
        String message =  String.format("【湖南省委党校】【%s】，您申请的假期变更已完成，假期开始时间：【%s】，假期结束时间：【%s】"
                , nickname, leaveStart, leaveEnd);

        Map<String,Object> smsmap = new HashMap<>();
        smsmap.put("arg1", message);
        smsSendApi.sendSingleSms(mobile, null,null ,"admin-sms-login-new",smsmap);

        //删除姓名
        message =  String.format("假期变更通知\n您申请的假期变更已完成，假期开始时间：【%s】，假期结束时间：【%s】"
                ,  leaveStart, leaveEnd);

        //待办事项未解决，删除日程已经解决
        //待办事项办添加
        // 抄送给人事处
        ReceiveDTO receiveDTO = new ReceiveDTO();
        receiveDTO.setProcessInstanceId(leaveDO.getProcessInstanceId());
        //设置
        receiveDTO.setCategory(message);

        //发起时间
        receiveDTO.setApplyTime(bpmProcessInstanceRespDTO.getCreateTime());

        //事项id
//        receiveDTO.setItemId();
        List<Long> userIds = null;
        // 备案给人事处请假负责人（设置一个专门的postType
//        userIds = postApi.getUserByPost("oa-hr-leave-deal", leaveDTO.getTenantId());
//        List<AdminUserRespDTO> users = userApi.getUsersByDeptName("组织人事部", leaveDTO.getTenantId()).getCheckedData();
//        userIds = users.stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
        receiveDTO.setUserIds(userIds);
        //发起人id
        receiveDTO.setPromoterUserId(bpmProcessInstanceRespDTO.getStartUser().getId());
        receiveDTO.setProcessInstanceId(leave.getProcessInstanceId());
//        receiveApi.save(receiveDTO);
        receiveApi.saveLeave(receiveDTO);

    }

    @Override
    @TenantIgnore
    public void autoDealLeave() {
        //定时任务销假，result为2，isDealt为false，假期结束时间小于今天的请假记录
        LocalDateTime time = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        List<OALeaveDO> list = leaveMapper.selectList(new LambdaQueryWrapperX<OALeaveDO>()
                .eq(OALeaveDO::getResult,2)
                .isNull(OALeaveDO::getIsDealt)
                .le(OALeaveDO::getEndTime,time));

        leaveMapper.update(null,
                new LambdaUpdateWrapper<OALeaveDO>()
                        .eq(OALeaveDO::getResult,2)
                        .isNull(OALeaveDO::getIsDealt)
                        .le(OALeaveDO::getEndTime,time)
                        .set(OALeaveDO::getIsDealt, true)
                        .set(OALeaveDO::getDealTime,time));

        //添加系统销假日志
        List<BpmTaskExtDTO> records = new ArrayList<>();
        list.forEach(leaveDO -> {
            BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
            bpmTaskExtDTO.setName("系统销假");
            bpmTaskExtDTO.setTaskDefKey("system-auto-deal-leave");
            bpmTaskExtDTO.setTaskId("system-auto-deal-leave");
            bpmTaskExtDTO.setResult(2);
            bpmTaskExtDTO.setProcessInstanceId(leaveDO.getProcessInstanceId());
            bpmTaskExtDTO.setProcessDefinitionId(leaveDO.getProcessInstanceId());
            bpmTaskExtDTO.setCreateTime(time);
            bpmTaskExtDTO.setEndTime(time);
            bpmTaskExtDTO.setTaskType(5);
            Map<String, Object> map = new HashMap<>();
            map.put("dealWay","系统销假");
//            map.put("handSignature",null);
            map.put("leaveStart",leaveDO.getStartTime().toLocalDate().toString());
            map.put("leaveEnd",leaveDO.getEndTime().toLocalDate().toString());
            bpmTaskExtDTO.setParamsMap(map);
            records.add(bpmTaskExtDTO);
        });
        bpmTaskServiceApi.saveBatchCustomTaskExt(records);
    }

    @Override
    public void removeByProcessInstanceId(String processInstanceId) {
        leaveMapper.delete(new LambdaQueryWrapperX<OALeaveDO>().eq(OALeaveDO::getProcessInstanceId,processInstanceId));
    }
}
