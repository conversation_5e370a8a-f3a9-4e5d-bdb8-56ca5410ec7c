package com.unicom.swdx.module.oa.dal.kingbase;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OutReportMapper extends BaseMapperX<OutReportDO> {

    OutReportDO selectByProcessId(@Param("processId") String processId);


    List<OutReportDO> selectListByUserId(@Param("userId") Long userId);

}
