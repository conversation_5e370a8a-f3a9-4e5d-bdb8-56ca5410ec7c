package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import jodd.typeconverter.impl.LocalDateTimeConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 寒暑假坐值班 Excel 导入 VO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class DutyFormExcelVO {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名\n（必填）")
    private String personnel;

    /**
     * 类型
     * 枚举
     */
    @ExcelProperty(value = "类型\n（必填）")
    private String dutyType;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间\n格式示例：2024/01/01\n（必填）")
    private LocalDateTime startDate;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间\n格式示例：2024/01/01\n（必填）")
    private LocalDateTime endDate;

    /**
     * 办公电话
     */
    @ExcelProperty(value = "办公电话")
    private String telephone;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码\n（必填）")
    private String phone;

}
