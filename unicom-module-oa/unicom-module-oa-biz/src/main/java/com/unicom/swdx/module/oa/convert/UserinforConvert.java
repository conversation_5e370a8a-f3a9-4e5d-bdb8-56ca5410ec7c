package com.unicom.swdx.module.oa.convert;

import java.util.*;



import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforExcelVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforRespVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforUpdateReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 重点任务关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UserinforConvert {

    UserinforConvert INSTANCE = Mappers.getMapper(UserinforConvert.class);

    UserinforDO convert(UserinforCreateReqVO bean);


    UserinforCreateReqVO convertuc(UserinforDO bean);

    UserinforDO convert(UserinforUpdateReqVO bean);

    UserinforRespVO convert(UserinforDO bean);

    List<UserinforRespVO> convertList(List<UserinforDO> list);

    PageResult<UserinforRespVO> convertPage(PageResult<UserinforDO> page);

    List<UserinforExcelVO> convertList02(List<UserinforDO> list);

}
