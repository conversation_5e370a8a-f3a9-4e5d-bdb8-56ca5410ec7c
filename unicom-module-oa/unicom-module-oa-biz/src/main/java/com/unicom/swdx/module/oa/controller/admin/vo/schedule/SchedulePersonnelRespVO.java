package com.unicom.swdx.module.oa.controller.admin.vo.schedule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("周工作安排人员分页查询 response VO")
@Data
@ToString(callSuper = true)
public class SchedulePersonnelRespVO {

    @ApiModelProperty("人员id")
    private Long userId;

    @ApiModelProperty("人员名称")
    private String userName;

    @ApiModelProperty("账号昵称")
    private String userNickName;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("部门id")
    private String deptId;
}
