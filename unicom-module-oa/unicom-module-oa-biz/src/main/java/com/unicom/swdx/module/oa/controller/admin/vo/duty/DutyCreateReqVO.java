package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("办公OA - 寒暑假坐值班 Request VO")
public class DutyCreateReqVO extends DutyBaseVO {
    @ApiModelProperty(value = "主键",notes = "当有草稿时必传")
    private Long id;

    @ApiModelProperty("所属部门")
    private Long deptId;


    @ApiModelProperty(value = "重新发起时必传流程id")
    private String processInstanceId;
}
