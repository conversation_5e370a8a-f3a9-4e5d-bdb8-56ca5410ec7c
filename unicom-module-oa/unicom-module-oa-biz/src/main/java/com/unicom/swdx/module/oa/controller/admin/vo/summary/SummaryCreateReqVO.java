package com.unicom.swdx.module.oa.controller.admin.vo.summary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("办公OA - 一周工作汇总发起审批 Request VO")
public class SummaryCreateReqVO extends SummaryBaseVO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "重新发起时必传流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "汇总时通过对应的上报任务")
    private List<String> taskIds;

    @ApiModelProperty(value = "汇总对应的数据")
    private List<SummaryToDoRespVO> data;

    @ApiModelProperty("选择的下一任务审批人列表")
    private List<Long> userIds;

    @ApiModelProperty("审批方式 1顺序 2会签")
    private String chargeLeaderSeq;

    @ApiModelProperty("是否通知")
    private Boolean isNotice;

    @ApiModelProperty("是否抄送")
    private Boolean isCopy;
}
