package com.unicom.swdx.module.oa.dal.kingbase;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.SchedulePersonnelRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WorkScheduleMapper extends BaseMapperX<WorkScheduleDO> {
    List<SchedulePersonnelRespVO> selectPersonnelInfo(@Param("idList") List<Long> idList, @Param("pageParam") PageParam pageParam);

    Long selectPersonnelNum(@Param("idList") List<Long> idList, @Param("pageParam") PageParam pageParam);
}
