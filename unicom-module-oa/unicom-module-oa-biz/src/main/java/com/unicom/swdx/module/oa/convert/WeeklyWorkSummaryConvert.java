package com.unicom.swdx.module.oa.convert;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.api.dto.ScheduleDTO;
import com.unicom.swdx.module.oa.api.dto.SummaryDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface WeeklyWorkSummaryConvert {
    WeeklyWorkSummaryConvert INSTANCE = Mappers.getMapper(WeeklyWorkSummaryConvert.class);

    WeeklyWorkSummaryDO convert(SummaryCreateReqVO bean);

    SummaryRespVO convert(WeeklyWorkSummaryDO bean);

    List<SummaryRespVO> convertList(List<WeeklyWorkSummaryDO> list);

    PageResult<SummaryRespVO> convertPage(PageResult<WeeklyWorkSummaryDO> page);

    SummaryDTO convertDTO(WeeklyWorkSummaryDO bean);
}
