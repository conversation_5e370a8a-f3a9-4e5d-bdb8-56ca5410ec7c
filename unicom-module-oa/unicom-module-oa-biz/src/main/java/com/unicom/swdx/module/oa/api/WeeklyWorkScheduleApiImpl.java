package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.api.dto.ScheduleDTO;
import com.unicom.swdx.module.oa.convert.OALeaveConvert;
import com.unicom.swdx.module.oa.convert.WeeklyWorkScheduleConvert;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.kingbase.WeeklyWorkScheduleMapper;
import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class WeeklyWorkScheduleApiImpl implements ScheduleApi {
    @Resource
    private WeeklyWorkScheduleService weeklyWorkScheduleService;

    @Resource
    private WeeklyWorkScheduleMapper weeklyWorkScheduleMapper;

    @Override
    public ScheduleDTO getItemId(String processInstanceId) {
        return WeeklyWorkScheduleConvert.INSTANCE.convertDTO(weeklyWorkScheduleService.getByProcessInstanceId(processInstanceId));
    }

    @Override
    public CommonResult<Long> getDeptId(String processInstanceId) {
        return success(weeklyWorkScheduleService.getByProcessInstanceId(processInstanceId).getDeptId());
    }

    @Override
    public void updateStatusById(Long id, Integer status) {
        weeklyWorkScheduleService.updateStatusById(id, status);
    }

    @Override
    public String getCopyTo(Long scheduleId) {
        WeeklyWorkScheduleDO scheduleDO = weeklyWorkScheduleMapper.selectById(scheduleId);
        return scheduleDO.getCopyTo();
    }

    @Override
    public String getProcessInstanceId(Long scheduleId) {
        WeeklyWorkScheduleDO scheduleDO = weeklyWorkScheduleMapper.selectById(scheduleId);
        return scheduleDO.getProcessInstanceId();
    }
}
