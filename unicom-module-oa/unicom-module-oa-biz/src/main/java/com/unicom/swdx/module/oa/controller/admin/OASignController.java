package com.unicom.swdx.module.oa.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.service.OAXcxService;
import com.unicom.swdx.module.system.api.xcx.WxXcxApi;
import com.unicom.swdx.module.system.api.xcx.dto.WxXcxSignQRCodeRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "办公OA - 移动端签字")
@RestController
@RequestMapping("/oa/sign")
public class OASignController {

    @Resource
    private WxXcxApi wxXcxApi;

    @Resource
    private OAXcxService oaXcxService;

    @GetMapping("/getSignQRCode")
    @ApiOperation("查询移动端签字的小程序二维码")
    public CommonResult<WxXcxSignQRCodeRespVO> getSignQRCode() {
        return success(wxXcxApi.getSignQRCode().getCheckedData());
    }

    @GetMapping("/getSignUrl")
    @ApiOperation("查询移动端签字的图片链接")
    public CommonResult<String> getSignUrl(@RequestParam("uniqueCode") String uniqueCode) {
        String signUrl = oaXcxService.getSignUrl(uniqueCode);
        if (StrUtil.isBlank(signUrl)) {
            return error(1004, "获取签字图片等待中...");
        }
        return success(signUrl);
    }

}
