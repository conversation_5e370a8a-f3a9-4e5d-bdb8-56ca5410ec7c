package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.LectureDTO;
import com.unicom.swdx.module.oa.convert.LectureConvert;
import com.unicom.swdx.module.oa.service.lecture.LectureService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class LectureApiImpl implements LectureApi{

    @Resource
    private LectureService lectureService;

    @Override
    public LectureDTO getItemId(String processInstanceId) {
        return LectureConvert.INSTANCE.convertDTO(lectureService.getByProcessInstanceId(processInstanceId));
    }

    @Override
    public Map<String, LocalDate> getDateById(Long id) {
        return lectureService.getDateById(id);
    }

    @Override
    public CommonResult<Long> getDeptId(String processInstanceId) {
        return success(lectureService.getByProcessInstanceId(processInstanceId).getDeptId());
    }
}
