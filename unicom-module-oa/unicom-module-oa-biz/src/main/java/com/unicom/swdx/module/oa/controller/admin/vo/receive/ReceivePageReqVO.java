package com.unicom.swdx.module.oa.controller.admin.vo.receive;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Data
@ApiModel("OA - 我收到的分页查询 Request VO")
public class ReceivePageReqVO extends PageParam {

    @ApiModelProperty("事项类型")
    private String category;

    @ApiModelProperty("申请日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] applyDate;

    @ApiModelProperty("是否已读")
    private Boolean isRead;

}
