package com.unicom.swdx.module.oa.convert;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.api.dto.LectureDTO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveDraftReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 请假申请 Convert
 *
 */
@Mapper
public interface OALeaveConvert {

    OALeaveConvert INSTANCE = Mappers.getMapper(OALeaveConvert.class);

    OALeaveDO convert(OALeaveCreateReqVO bean);

    OALeaveRespVO convert(OALeaveDO bean);

    List<OALeaveRespVO> convertList(List<OALeaveDO> list);

    PageResult<OALeaveRespVO> convertPage(PageResult<OALeaveDO> page);

    LeaveDTO convertDTO(OALeaveDO bean);

//    OALeaveDO convertDTO2DO(LeaveDTO bean);

}
