package com.unicom.swdx.module.oa.controller.admin.oaLeave;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.*;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Api(tags = "办公OA - 请假审批")
@RestController
@RequestMapping("/oa/approval/leave")
public class OALeaveController {

    @Resource
    private OALeaveService leaveService;

    @PostMapping("/saveDraft")
    @PreAuthorize("@ss.hasPermission('oa:leave:create')")
    @ApiOperation("存草稿")
    public CommonResult<Boolean> saveDraft(@RequestBody OALeaveCreateReqVO draftReqVO) {
        return success(leaveService.saveDraft(getLoginUserId(), draftReqVO));
    }

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('oa:leave:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @ApiOperation("创建请假流程")
    public CommonResult<String> createLeave(@Valid @RequestBody OALeaveCreateReqVO createReqVO) {
        return success(leaveService.createLeave(getLoginUserId(), createReqVO));
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('oa:leave:create')")
    @ApiOperation("驳回或撤销后重新编辑再发起请假流程")
    public CommonResult<Boolean> restartLeave(@RequestBody OALeaveCreateReqVO reqVO) {
        leaveService.restartLeave(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/get")
//    @PreAuthorize("@ss.hasPermission('oa:leave:query')")
    @PreAuthorize("@ss.hasPermission('oa:leave:create')")
    @ApiOperation("获得请假申请详情")
    public CommonResult<OALeaveRespVO> getLeave(@RequestParam(value = "id",required = false) Long id,
                                                @RequestParam(value = "processInstanceId",required = false) String processInstanceId,
                                                @RequestParam(value = "isReceived", required = false) Boolean isReceived) {
        OALeaveRespVO resp = leaveService.get(id, processInstanceId, isReceived);
        return success(resp);
    }

    @PostMapping("/dealLeave")
//    @PreAuthorize("@ss.hasPermission('oa:leave:dealLeave')")
    @PreAuthorize("@ss.hasPermission('oa:leave:create')")
    @ApiOperation("假期变更/销假")
    public CommonResult<Boolean> dealLeave(@Valid @RequestBody OADealLeaveVO dealLeaveVO) {
        leaveService.dealLeave(dealLeaveVO);
        return success(true);

    }

    @GetMapping("/judge")
    @PreAuthorize("@ss.hasPermission('oa:leave:statistics')")
    @ApiOperation("判断是否具备管理员视角")
    public CommonResult<Boolean> judge() {

        return success(leaveService.judgeIfDirector(getLoginUserId()));

    }

    @PostMapping("/getLeaveStatistics")
    @PreAuthorize("@ss.hasPermission('oa:leave:statistics')")
    @ApiOperation("请假天数统计")
    public CommonResult<PageResult<LeaveStatisticsRespVO>> statistics(@RequestBody OALeaveStatisticPageReqVO reqVO) {

        PageResult<LeaveStatisticsRespVO> resp = leaveService.getLeaveStatisticsList(getLoginUserId(),reqVO);
        return success(resp);
    }

    @PostMapping("/getLeaveInfoList")
    @PreAuthorize("@ss.hasPermission('oa:leave:statistics')")
    @ApiOperation("请假信息")
    public CommonResult<PageResult<OALeaveRespVO>> leaveInfo(@Valid @RequestBody OALeavePageReqVO reqVO) {

        PageResult<OALeaveRespVO> resp = leaveService.getLeaveInfoList(reqVO);
        return success(resp);
    }

    @GetMapping("/getPersonalLeaveCount")
    @PreAuthorize("@ss.hasPermission('oa:leave:statistics')")
    @ApiOperation("个人视角请假天数")
    public CommonResult<Map<String, Object>> getLeaveCount(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam("startTime") String startTime,
            @RequestParam("endTime") String endTime) {

        if(Objects.isNull(userId)){
            userId = getLoginUserId();
        }
        Map<String, Object> resp = leaveService.getPersonalLeaveCount(userId,startTime,endTime);
        return success(resp);
    }

    @GetMapping("/calculateWorkDay")
    @ApiOperation("计算指定时间范围内的工作日天数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始日期", example = "2022-01-01", required = true, dataTypeClass = LocalDate.class),
            @ApiImplicitParam(name = "endTime", value = "结束日期", example = "2022-01-30", required = true, dataTypeClass = LocalDate.class),
    })
    @PreAuthorize("@ss.hasPermission('oa:leave:create')")
    public CommonResult<Integer> calculateWorkDay(@RequestParam("startTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate startTime,
                                                  @RequestParam("endTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd") LocalDate endTime) {

        return success(leaveService.calculateLeaveDay(startTime,endTime));
    }

//    @GetMapping("/test")
//    @ApiOperation("测试接口")
//    public CommonResult<Boolean> test() {
//
//        leaveService.autoDealLeave();
//        return success(true);
//
//    }
}
