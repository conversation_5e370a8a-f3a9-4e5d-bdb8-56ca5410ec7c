package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.module.oa.api.dto.SummaryDTO;
import com.unicom.swdx.module.oa.convert.WeeklyWorkSummaryConvert;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import com.unicom.swdx.module.oa.dal.kingbase.WeeklyWorkSummaryMapper;
import com.unicom.swdx.module.oa.service.summary.WeeklyWorkSummaryService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class SummaryApiImpl implements SummaryApi{

    @Resource
    private WeeklyWorkSummaryService weeklyWorkSummaryService;

    @Resource
    private WeeklyWorkSummaryMapper weeklyWorkSummaryMapper;

    @Override
    public SummaryDTO getItemId(String processInstanceId) {
        return WeeklyWorkSummaryConvert.INSTANCE.convertDTO(weeklyWorkSummaryService.getByProcessInstanceId(processInstanceId));
    }

    @Override
    public void updateStatusById(Long id, Integer status) {

    }

    @Override
    public String getWorkScheduleId(String processInstanceId) {
        WeeklyWorkSummaryDO workSummaryDO = weeklyWorkSummaryMapper.selectOne(WeeklyWorkSummaryDO::getProcessInstanceId, processInstanceId);
        return workSummaryDO.getWeeklyWorkScheduleIds();
    }


    @Override
    public Integer getYear(String processInstanceId) {
        WeeklyWorkSummaryDO summaryDO = weeklyWorkSummaryService.getByProcessInstanceId(processInstanceId);
        return summaryDO.getYear();
    }

    @Override
    public String getSemester(String processInstanceId) {
        WeeklyWorkSummaryDO summaryDO = weeklyWorkSummaryService.getByProcessInstanceId(processInstanceId);
        return summaryDO.getSemester();
    }

    @Override
    public Integer getWeek(String processInstanceId) {
        WeeklyWorkSummaryDO summaryDO = weeklyWorkSummaryService.getByProcessInstanceId(processInstanceId);
        return summaryDO.getWeek();
    }

    @Override
    public Map<String, LocalDate> getDateById(String processInstanceId) {
        return weeklyWorkSummaryService.getDateById(processInstanceId);
    }
    @Override
    public List<Long> getUsersById(String processInstanceId) {
        return weeklyWorkSummaryService.getUsersById(processInstanceId);
    }

    @Override
    public String getCopyTo(Long summaryId) {
        WeeklyWorkSummaryDO summaryDO = weeklyWorkSummaryMapper.selectById(summaryId);
        return summaryDO.getCopyTo();
    }
}
