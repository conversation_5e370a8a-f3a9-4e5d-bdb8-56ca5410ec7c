package com.unicom.swdx.module.oa.dal.dataobject;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@TableName("yeartask_endtime")
@Data
@Builder
public class InforEndtimeDO implements Serializable {



    @TableId(type = IdType.INPUT) // 指定主键，并设置主键生成策略为NONE
    Long tenant_id;

    String endtime;

}
