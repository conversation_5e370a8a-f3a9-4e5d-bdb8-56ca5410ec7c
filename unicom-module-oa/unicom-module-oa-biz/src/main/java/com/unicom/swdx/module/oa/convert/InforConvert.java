package com.unicom.swdx.module.oa.convert;

import java.util.*;


import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.*;
import com.unicom.swdx.module.oa.dal.dataobject.InforDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 重点工作信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InforConvert {

    InforConvert INSTANCE = Mappers.getMapper(InforConvert.class);

    InforDO convert(InforCreateReqVO bean);

    InforDO convert(InforUpdateReqVO bean);

    InforRespVO convert(InforDO bean);

    List<InforRespVO> convertList(List<InforDO> list);

    List<InforCreateReqVO>  convertListMonthToCreate( List<InforImportVO> list);

    PageResult<InforRespVO> convertPage(PageResult<InforDO> page);

    List<InforExcelVO> convertList02(List<InforDO> list);

    List<InforExcelVO> convertList03(List<InforRespVO> list);

    List<InforExceldraftVO> convertList043(List<InforExcelVO> list);

    List<InforExcelNewVO> convertRespVOToExcelNewVO(List<InforRespVO> list);
    List<InforExcelNewVO> convertExcelVOToExcelNewVO(List<InforExcelVO> list);

    List<InforExceldraftNewVO> convertDraftVOToDraftNewVO(List<InforExceldraftVO> list);

    List<InforUpdateReqVO> convertList05(List<InforRespVO> list);

}
