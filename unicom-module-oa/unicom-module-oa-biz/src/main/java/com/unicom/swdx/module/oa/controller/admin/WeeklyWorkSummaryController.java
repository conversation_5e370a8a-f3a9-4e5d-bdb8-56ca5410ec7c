package com.unicom.swdx.module.oa.controller.admin;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.SchedulePersonnelRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryToDoRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import com.unicom.swdx.module.oa.service.summary.WeeklyWorkSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 一周工作汇总
 */
@Api(tags = "办公OA - 一周工作汇总")
@RestController
@RequestMapping("/oa/workSummary")
public class WeeklyWorkSummaryController {

    @Resource
    private WeeklyWorkSummaryService weeklyWorkSummaryService;


    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('oa:workSummary:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @ApiOperation("发起一周工作汇总审批申请")
    public CommonResult<String> createWorkSummaryProcess(@Valid @RequestBody SummaryCreateReqVO createReqVO) {
        return success(weeklyWorkSummaryService.createWorkSummaryProcess(createReqVO));
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('oa:workSummary:create')")
    @ApiOperation("驳回后重新编辑再发起流程")
    public CommonResult<Boolean> restartSummary(@RequestBody SummaryCreateReqVO reqVO) {
        weeklyWorkSummaryService.restartSummary(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('oa:workSummary:create')")
    @ApiOperation("获得一周工作安排汇总申请")
    public CommonResult<SummaryRespVO> getWorkSchedule(@RequestParam(value = "id",required = false) Long id,
                                                       @RequestParam(value = "processInstanceId",required = false) String processInstanceId) {
        SummaryRespVO respVO = weeklyWorkSummaryService.get(id, processInstanceId);;
        return success(respVO);
    }

    @GetMapping("/getPreview")
    @PreAuthorize("@ss.hasPermission('oa:workSummary:create')")
    @ApiOperation("获得一周工作安排汇总预览")
    public CommonResult<List<WorkScheduleDO>> getPreviewData(@RequestParam(value = "ids",required = false) String ids) {
        List<Long> idList = new ArrayList<>(Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        List<WorkScheduleDO> result = weeklyWorkSummaryService.getPreviewData(idList);
        return success(result);
    }

    @GetMapping("/getScheduleList")
    @PreAuthorize("@ss.hasPermission('oa:workSummary:create')")
    @ApiOperation("获取未汇总的、驳回到汇总人的所有填报单")
    public CommonResult<SummaryScheduleRespVO> getWeeklyScheduleList() {
        SummaryScheduleRespVO weeklyWorkScheduleList = weeklyWorkSummaryService.getWeeklyScheduleList();
        return success(weeklyWorkScheduleList);
    }

    @GetMapping("/getDetail")
    @PreAuthorize("@ss.hasPermission('oa:workSummary:create')")
    @ApiOperation("获取填报单")
    public CommonResult<List<ScheduleRespVO>> getDetail(@RequestParam(value = "workScheduleIds",required = false) String workScheduleIds) {
        List<ScheduleRespVO> respVO = weeklyWorkSummaryService.getDetail(workScheduleIds);
        return success(respVO);
    }

    @PostMapping("/delete")
    //@PreAuthorize("@ss.hasPermission('oa:workSummary:delete')")
    @ApiOperation("删除工作安排汇总")
    public CommonResult<Boolean> deleteWorkSummary(@RequestParam("id") Integer id) {
        weeklyWorkSummaryService.deleteSummary(id);
        return success(true);
    }

    @PostMapping("/end")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("结束")
    public CommonResult<Boolean> end(@RequestBody SummaryCreateReqVO reqVO) {
        weeklyWorkSummaryService.end(reqVO);
        return success(true);
    }

    @PostMapping("/restartEnd")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("重新发起结束")
    public CommonResult<Boolean> restartEndSummary(@RequestBody SummaryCreateReqVO reqVO) {
        weeklyWorkSummaryService.restartEnd(reqVO);
        return success(true);
    }
}
