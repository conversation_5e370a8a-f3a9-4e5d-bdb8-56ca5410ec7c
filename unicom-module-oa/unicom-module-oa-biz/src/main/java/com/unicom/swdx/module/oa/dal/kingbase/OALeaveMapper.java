package com.unicom.swdx.module.oa.dal.kingbase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.LeaveStatisticsRespVO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeavePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveRespVO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveStatisticPageReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 请假申请 Mapper
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Mapper
public interface OALeaveMapper extends BaseMapperX<OALeaveDO> {

    default PageResult<OALeaveDO> selectPage(Long userId, OALeavePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OALeaveDO>()
                .eqIfPresent(OALeaveDO::getUserId, userId)
//                .eqIfPresent(OALeaveDO::getResult, reqVO.getResult())
//                .eqIfPresent(OALeaveDO::getType, reqVO.getType())
//                .likeIfPresent(OALeaveDO::getReason, reqVO.getReason())
//                .betweenIfPresent(OALeaveDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(OALeaveDO::getId));
    }



    List<LeaveStatisticsRespVO> selectLeaveStatisticsPage(IPage page, @Param("reqVO") OALeaveStatisticPageReqVO reqVO);

    List<OALeaveRespVO> selectLeaveInfoPage(IPage page, @Param("reqVO") OALeavePageReqVO reqVO);

    @Select("<script> select * FROM oa_leave" +
            " <where>" +
                " (result = 1 or result = 2) and deleted=0 and launch_user_id=#{userId} and ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime}) or (start_time &lt;= #{endTime} and end_time &gt;= #{endTime})) " +
                " <if test='processInstanceId !=null and processInstanceId !=\"\"'> and process_instance_id != #{processInstanceId}</if> " +
            " </where> </script>"
            )
    List<OALeaveDO> getMutilData(@Param("startTime") String startTime, @Param("endTime") String endTime,@Param("userId") Long userId,@Param("processInstanceId") String processInstanceId);

    Map<String, Object> selectLeaveCountByUserId(@Param("userId")Long userId, @Param("startTime")String startTime, @Param("endTime")String endTime);

    void updateResultByProcId(@Param("processInstanceId") String processInstanceId, @Param("result") Integer result);
}

