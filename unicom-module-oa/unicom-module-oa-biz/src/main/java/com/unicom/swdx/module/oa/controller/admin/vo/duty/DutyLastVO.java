package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 传输类
 * <AUTHOR>
 * @data 2024/3/6 9:05
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "RecruitBasicInfoNewVO", description = "简历基本信息(新)")
public class DutyLastVO extends PageParam implements Serializable {

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private Integer sex;
    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    private String cardNo;
    /**
     * 所属民族
     */
    @ApiModelProperty(value = "所属民族")
    private String nation;
    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "出生日期")
    private LocalDateTime birthday;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String politicsStatus;
    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    private String highestEducationLevel;
    /**
     * 最高学位
     */
    @ApiModelProperty(value = "最高学位")
    private String highestAcademicDegree;
    /**
     * 最高学历毕业院校
     */
    @ApiModelProperty(value = "最高学历毕业院校")
    private String highestGraduateSchool;
    /**
     * 最后毕业时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "最后毕业时间")
    private LocalDateTime lastGraduationTime;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String major;
    /**
     * 职称、执（职）业资格
     */
    @ApiModelProperty(value = "职称")
    private String professionalTitle;
    /**
     * 户籍所在地
     */
    @ApiModelProperty(value = "户籍所在地")
    private String domicile;
    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String nativePlace;
    /**
     * 是否应届生
     */
    @ApiModelProperty(value = "是否应届生")
    private String freshGraduate;
    /**
     * 档案保管单位
     */
    @ApiModelProperty(value = "档案保管单位")
    private String archivalCustodianUnit;
    /**
     * 特长
     */
    @ApiModelProperty(value = "特长")
    private String strongPoint;
    /**
     * 现所在单位及职务
     */
    @ApiModelProperty(value = "现所在单位及职务")
    private String currentUnitPosition;
    /**
     * 职称、执（职）业资格取得时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "执（职）业资格取得时间")
    private LocalDateTime acquisitionTime;
    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;
    /**
     * 简历信息
     */
    @ApiModelProperty(value = "简历信息")
    private String resumeInfo;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String phone;
    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    private String mailbox;
    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    private String address;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;
    /**
     * 审核状态
     *       1-待审核
     *       2-不通过
     *       3-待面试审核
     *       4-面试审核不通过
     *       5-拟录用
     *       6-草稿
     *       7-退回修改
     *       8-退回修改后重新申请
     */
    @ApiModelProperty(value = "审核状态")
    private Integer status;
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String jobTitle;
    /**
     * 岗位类别
     *       1-专业技术岗位
     *       2-管理岗位
     *       3-工勤技能岗位
     *       4-其他岗位
     */
    @ApiModelProperty(value = "岗位类别")
    private Integer jobCategory;
    /**
     * 申请号
     */
    @ApiModelProperty(value = "申请号")
    private String applicationNumber;
    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;
    /**
     * 招聘单位
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "招聘单位")
    private String recruitmentUnit;
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String headUrl;
    /**
     * 取得的成绩
     */
    @ApiModelProperty(value = "取得的成绩")
    private String grade;
    /**
     * 资格审查
     *    通过与不通过时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "资格审查-通过与不通过时间")
    private LocalDateTime titleExaminationTime;
    /**
     * 面试通过时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "面试通过时间")
    private LocalDateTime passInterviewTime;
    /**
     * 资格审查
     *    审批人
     */
    @ApiModelProperty(value = "资格审查-审批人")
    private String firstApprove;
    /**
     * 面试审查
     *      审批人
     */
    @ApiModelProperty(value = "面试审查-审批人")
    private String faceApprove;

    /**
     * 应聘者id
     */
    @ApiModelProperty(value = "应聘者id")
    private Long userId;
    /**
     * 参加工作时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "参加工作时间")
    private LocalDateTime employmentTime;
    /**
     * 入党时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "入党时间")
    private LocalDateTime partyJoinTime;
    /**
     * 户口性质
     */
    @ApiModelProperty(value = "户口性质")
    private String householdType;
    /**
     * 居住所在地
     */
    @ApiModelProperty(value = "居住所在地")
    private String residence;
    /**
     * 职业资格
     */
    @ApiModelProperty(value = "职业资格")
    private String qualificationCertificate;
    /**
     * 曾受到何种奖励或处分
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    private String rewardAndPunishment;
    /**
     * 修改意见
     */
    @ApiModelProperty(value = "修改意见")
    private String modifySuggestion;

//    /**
//     * 时间格式
//     */
//    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
//
//    /**
//     * 主键id，自增
//     */
//    @TableId(value = "id", type = IdType.AUTO)
//    @ApiModelProperty(value = "主键id", required = true, example = "1")
//    private Integer id;
//    /**
//     * 招聘单位
//     */
//    @ApiModelProperty(value = "招聘单位")
//    private String recruitmentUnit;
//    /**
//     * 招聘部门
//     */
//    @ApiModelProperty(value = "招聘部门")
//    private String recruitmentDepartment;
//    /**
//     * 岗位名称
//     */
//    @ApiModelProperty(value = "岗位名称")
//    private String positionName;
//    /**
//     * 岗位代码
//     */
//    @ApiModelProperty(value = "岗位代码")
//    private String postCode;
//    /**
//     * 岗位类别，1-专业技术岗位,2-管理岗位,3-工勤技能岗位,4-其他岗位
//     */
//    @ApiModelProperty(value = "岗位类别")
//    private Integer jobCategory;
//    /**
//     * 招聘计划
//     */
//    @ApiModelProperty(value = "招聘计划")
//    private String recruitmentPlan;
//    /**
//     * 招聘类别:1为高层次，2为非高层次
//     */
//    @ApiModelProperty(value = "招聘类别")
//    private Integer recruitmentCategory;
//    /**
//     * 学历学位
//     */
//    @ApiModelProperty(value = "学历学位")
//    private String academicDegree;
//    /**
//     * 年龄
//     */
//    @ApiModelProperty(value = "年龄")
//    private String age;
//    /**
//     * 专业
//     */
//    @ApiModelProperty(value = "专业")
//    private String profession;
//    /**
//     * 其它
//     */
//    @ApiModelProperty(value = "其它")
//    private String other;
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value = "备注")
//    private String remark;
//    /**
//     * 任职要求
//     */
//    @ApiModelProperty(value = "任职要求")
//    private String jobRequirements;
//    /**
//     * 岗位职责
//     */
//    @ApiModelProperty(value = "岗位职责")
//    private String jobResponsibility;
//    /**
//     * 发布开始时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布开始时间")
//    private LocalDateTime startTime;
//    /**
//     * 发布截止时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布截止时间")
//    private LocalDateTime cutTime;
//    /**
//     * 发布状态,1-未发布,2-招聘中,3-完成
//     */
//    @ApiModelProperty(value = "发布状态")
//    private String releaseStatus;
//    /**
//     * 所属批次,与批次管理表的id对应
//     */
//    @ApiModelProperty(value = "所属批次")
//    private Integer owningBatch;
//    /**
//     * 创建时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "创建时间")
//    private LocalDateTime createTime;
}
