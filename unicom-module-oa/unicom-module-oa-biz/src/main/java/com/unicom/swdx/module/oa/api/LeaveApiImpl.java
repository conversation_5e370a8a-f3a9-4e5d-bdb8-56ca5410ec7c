package com.unicom.swdx.module.oa.api;

import cn.hutool.core.bean.BeanUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.convert.OALeaveConvert;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;
import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class LeaveApiImpl implements LeaveApi{

    @Resource
    private OALeaveService leaveService;

    @Override
    public LeaveDTO getItemId(String processInstanceId) {
        return OALeaveConvert.INSTANCE.convertDTO(leaveService.getByProcessInstanceId(processInstanceId));
    }

    @Override
    public void updateResultById(Long id, Integer result) {
        leaveService.updateResultById(id, result);
    }

    @Override
    public void updateById(LeaveDTO leaveInfo) {
        OALeaveDO leaveDO = new OALeaveDO();
        BeanUtil.copyProperties(leaveInfo,leaveDO);
//        OALeaveDO leaveDO = OALeaveConvert.INSTANCE.convertDTO2DO(leaveInfo);
        leaveService.updateById(leaveDO);
    }

    @Override
    public Map<String, LocalDate> getDateById(Long id) {
        return leaveService.getDateById(id);
    }

    @Override
    public CommonResult<Long> getDeptId(String processInstanceId) {
        return success(leaveService.getByProcessInstanceId(processInstanceId).getDeptId());
    }
}
