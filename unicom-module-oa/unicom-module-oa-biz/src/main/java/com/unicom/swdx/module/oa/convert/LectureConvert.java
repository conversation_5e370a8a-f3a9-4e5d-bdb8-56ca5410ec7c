package com.unicom.swdx.module.oa.convert;

import com.unicom.swdx.module.oa.api.dto.LectureDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.lecture.LectureRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LectureConvert {

    LectureConvert INSTANCE = Mappers.getMapper(LectureConvert.class);

    LectureDO convert(LectureCreateReqVO bean);

    LectureRespVO convert(LectureDO bean);

    LectureDTO convertDTO(LectureDO bean);

}
