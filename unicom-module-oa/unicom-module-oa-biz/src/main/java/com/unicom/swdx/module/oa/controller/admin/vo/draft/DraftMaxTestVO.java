package com.unicom.swdx.module.oa.controller.admin.vo.draft;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 传输类
 * <AUTHOR>
 * @data 2024/3/6 9:05
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "RecruitBasicInfoNewVO", description = "简历基本信息(新)")
public class DraftMaxTestVO extends PageParam implements Serializable {

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Integer id;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private Integer sex;
    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    private String cardNo;
    /**
     * 所属民族
     */
    @ApiModelProperty(value = "所属民族")
    private String nation;
    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "出生日期")
    private LocalDateTime birthday;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String politicsStatus;
    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    private String highestEducationLevel;
    /**
     * 最高学位
     */
    @ApiModelProperty(value = "最高学位")
    private String highestAcademicDegree;
    /**
     * 最高学历毕业院校
     */
    @ApiModelProperty(value = "最高学历毕业院校")
    private String highestGraduateSchool;
    /**
     * 最后毕业时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "最后毕业时间")
    private LocalDateTime lastGraduationTime;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String major;
    /**
     * 职称、执（职）业资格
     */
    @ApiModelProperty(value = "职称")
    private String professionalTitle;
    /**
     * 户籍所在地
     */
    @ApiModelProperty(value = "户籍所在地")
    private String domicile;
    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String nativePlace;
    /**
     * 是否应届生
     */
    @ApiModelProperty(value = "是否应届生")
    private String freshGraduate;
    /**
     * 档案保管单位
     */
    @ApiModelProperty(value = "档案保管单位")
    private String archivalCustodianUnit;
    /**
     * 特长
     */
    @ApiModelProperty(value = "特长")
    private String strongPoint;
    /**
     * 现所在单位及职务
     */
    @ApiModelProperty(value = "现所在单位及职务")
    private String currentUnitPosition;
    /**
     * 职称、执（职）业资格取得时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "执（职）业资格取得时间")
    private LocalDateTime acquisitionTime;
    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;
    /**
     * 简历信息
     */
    @ApiModelProperty(value = "简历信息")
    private String resumeInfo;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String phone;
    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    private String mailbox;
    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    private String address;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;
    /**
     * 审核状态
     *       1-待审核
     *       2-不通过
     *       3-待面试审核
     *       4-面试审核不通过
     *       5-拟录用
     *       6-草稿
     *       7-退回修改
     *       8-退回修改后重新申请
     */
    @ApiModelProperty(value = "审核状态")
    private Integer status;
    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String jobTitle;
    /**
     * 岗位类别
     *       1-专业技术岗位
     *       2-管理岗位
     *       3-工勤技能岗位
     *       4-其他岗位
     */
    @ApiModelProperty(value = "岗位类别")
    private Integer jobCategory;
    /**
     * 申请号
     */
    @ApiModelProperty(value = "申请号")
    private String applicationNumber;
    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;
    /**
     * 招聘单位
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "招聘单位")
    private String recruitmentUnit;
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String headUrl;
    /**
     * 取得的成绩
     */
    @ApiModelProperty(value = "取得的成绩")
    private String grade;
    /**
     * 资格审查
     *    通过与不通过时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "资格审查-通过与不通过时间")
    private LocalDateTime titleExaminationTime;
    /**
     * 面试通过时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "面试通过时间")
    private LocalDateTime passInterviewTime;
    /**
     * 资格审查
     *    审批人
     */
    @ApiModelProperty(value = "资格审查-审批人")
    private String firstApprove;
    /**
     * 面试审查
     *      审批人
     */
    @ApiModelProperty(value = "面试审查-审批人")
    private String faceApprove;

    /**
     * 应聘者id
     */
    @ApiModelProperty(value = "应聘者id")
    private Long userId;
    /**
     * 参加工作时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "参加工作时间")
    private LocalDateTime employmentTime;
    /**
     * 入党时间
     */
    @DateTimeFormat(pattern = DATE)
    @ApiModelProperty(value = "入党时间")
    private LocalDateTime partyJoinTime;
    /**
     * 户口性质
     */
    @ApiModelProperty(value = "户口性质")
    private String householdType;
    /**
     * 居住所在地
     */
    @ApiModelProperty(value = "居住所在地")
    private String residence;
    /**
     * 职业资格
     */
    @ApiModelProperty(value = "职业资格")
    private String qualificationCertificate;
    /**
     * 曾受到何种奖励或处分
     */
    @ApiModelProperty(value = "曾受到何种奖励或处分")
    private String rewardAndPunishment;
    /**
     * 修改意见
     */
    @ApiModelProperty(value = "修改意见")
    private String modifySuggestion;

//    public SchedulePostVO() {
//    }
//
//    public SchedulePostVO(Integer id, String name, Integer sex, String cardNo, String nation, LocalDateTime birthday, String politicsStatus, String highestEducationLevel, String highestAcademicDegree, String highestGraduateSchool, LocalDateTime lastGraduationTime, String major, String professionalTitle, String domicile, String nativePlace, String freshGraduate, String archivalCustodianUnit, String strongPoint, String currentUnitPosition, LocalDateTime acquisitionTime, String maritalStatus, String resumeInfo, String phone, String mailbox, String address, String postalCode, Integer status, String jobTitle, Integer jobCategory, String applicationNumber, LocalDateTime applyTime, String recruitmentUnit, String headUrl, String grade, LocalDateTime titleExaminationTime, LocalDateTime passInterviewTime, String firstApprove, String faceApprove, Long userId, LocalDateTime employmentTime, LocalDateTime partyJoinTime, String householdType, String residence, String qualificationCertificate, String rewardAndPunishment, String modifySuggestion) {
//        this.id = id;
//        this.name = name;
//        this.sex = sex;
//        this.cardNo = cardNo;
//        this.nation = nation;
//        this.birthday = birthday;
//        this.politicsStatus = politicsStatus;
//        this.highestEducationLevel = highestEducationLevel;
//        this.highestAcademicDegree = highestAcademicDegree;
//        this.highestGraduateSchool = highestGraduateSchool;
//        this.lastGraduationTime = lastGraduationTime;
//        this.major = major;
//        this.professionalTitle = professionalTitle;
//        this.domicile = domicile;
//        this.nativePlace = nativePlace;
//        this.freshGraduate = freshGraduate;
//        this.archivalCustodianUnit = archivalCustodianUnit;
//        this.strongPoint = strongPoint;
//        this.currentUnitPosition = currentUnitPosition;
//        this.acquisitionTime = acquisitionTime;
//        this.maritalStatus = maritalStatus;
//        this.resumeInfo = resumeInfo;
//        this.phone = phone;
//        this.mailbox = mailbox;
//        this.address = address;
//        this.postalCode = postalCode;
//        this.status = status;
//        this.jobTitle = jobTitle;
//        this.jobCategory = jobCategory;
//        this.applicationNumber = applicationNumber;
//        this.applyTime = applyTime;
//        this.recruitmentUnit = recruitmentUnit;
//        this.headUrl = headUrl;
//        this.grade = grade;
//        this.titleExaminationTime = titleExaminationTime;
//        this.passInterviewTime = passInterviewTime;
//        this.firstApprove = firstApprove;
//        this.faceApprove = faceApprove;
//        this.userId = userId;
//        this.employmentTime = employmentTime;
//        this.partyJoinTime = partyJoinTime;
//        this.householdType = householdType;
//        this.residence = residence;
//        this.qualificationCertificate = qualificationCertificate;
//        this.rewardAndPunishment = rewardAndPunishment;
//        this.modifySuggestion = modifySuggestion;
//    }
//
//    public Integer getId() {
//        return id;
//    }
//
//    public void setId(Integer id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public Integer getSex() {
//        return sex;
//    }
//
//    public void setSex(Integer sex) {
//        this.sex = sex;
//    }
//
//    public String getCardNo() {
//        return cardNo;
//    }
//
//    public void setCardNo(String cardNo) {
//        this.cardNo = cardNo;
//    }
//
//    public String getNation() {
//        return nation;
//    }
//
//    public void setNation(String nation) {
//        this.nation = nation;
//    }
//
//    public LocalDateTime getBirthday() {
//        return birthday;
//    }
//
//    public void setBirthday(LocalDateTime birthday) {
//        this.birthday = birthday;
//    }
//
//    public String getPoliticsStatus() {
//        return politicsStatus;
//    }
//
//    public void setPoliticsStatus(String politicsStatus) {
//        this.politicsStatus = politicsStatus;
//    }
//
//    public String getHighestEducationLevel() {
//        return highestEducationLevel;
//    }
//
//    public void setHighestEducationLevel(String highestEducationLevel) {
//        this.highestEducationLevel = highestEducationLevel;
//    }
//
//    public String getHighestAcademicDegree() {
//        return highestAcademicDegree;
//    }
//
//    public void setHighestAcademicDegree(String highestAcademicDegree) {
//        this.highestAcademicDegree = highestAcademicDegree;
//    }
//
//    public String getHighestGraduateSchool() {
//        return highestGraduateSchool;
//    }
//
//    public void setHighestGraduateSchool(String highestGraduateSchool) {
//        this.highestGraduateSchool = highestGraduateSchool;
//    }
//
//    public LocalDateTime getLastGraduationTime() {
//        return lastGraduationTime;
//    }
//
//    public void setLastGraduationTime(LocalDateTime lastGraduationTime) {
//        this.lastGraduationTime = lastGraduationTime;
//    }
//
//    public String getMajor() {
//        return major;
//    }
//
//    public void setMajor(String major) {
//        this.major = major;
//    }
//
//    public String getProfessionalTitle() {
//        return professionalTitle;
//    }
//
//    public void setProfessionalTitle(String professionalTitle) {
//        this.professionalTitle = professionalTitle;
//    }
//
//    public String getDomicile() {
//        return domicile;
//    }
//
//    public void setDomicile(String domicile) {
//        this.domicile = domicile;
//    }
//
//    public String getNativePlace() {
//        return nativePlace;
//    }
//
//    public void setNativePlace(String nativePlace) {
//        this.nativePlace = nativePlace;
//    }
//
//    public String getFreshGraduate() {
//        return freshGraduate;
//    }
//
//    public void setFreshGraduate(String freshGraduate) {
//        this.freshGraduate = freshGraduate;
//    }
//
//    public String getArchivalCustodianUnit() {
//        return archivalCustodianUnit;
//    }
//
//    public void setArchivalCustodianUnit(String archivalCustodianUnit) {
//        this.archivalCustodianUnit = archivalCustodianUnit;
//    }
//
//    public String getStrongPoint() {
//        return strongPoint;
//    }
//
//    public void setStrongPoint(String strongPoint) {
//        this.strongPoint = strongPoint;
//    }
//
//    public String getCurrentUnitPosition() {
//        return currentUnitPosition;
//    }
//
//    public void setCurrentUnitPosition(String currentUnitPosition) {
//        this.currentUnitPosition = currentUnitPosition;
//    }
//
//    public LocalDateTime getAcquisitionTime() {
//        return acquisitionTime;
//    }
//
//    public void setAcquisitionTime(LocalDateTime acquisitionTime) {
//        this.acquisitionTime = acquisitionTime;
//    }
//
//    public String getMaritalStatus() {
//        return maritalStatus;
//    }
//
//    public void setMaritalStatus(String maritalStatus) {
//        this.maritalStatus = maritalStatus;
//    }
//
//    public String getResumeInfo() {
//        return resumeInfo;
//    }
//
//    public void setResumeInfo(String resumeInfo) {
//        this.resumeInfo = resumeInfo;
//    }
//
//    public String getPhone() {
//        return phone;
//    }
//
//    public void setPhone(String phone) {
//        this.phone = phone;
//    }
//
//    public String getMailbox() {
//        return mailbox;
//    }
//
//    public void setMailbox(String mailbox) {
//        this.mailbox = mailbox;
//    }
//
//    public String getAddress() {
//        return address;
//    }
//
//    public void setAddress(String address) {
//        this.address = address;
//    }
//
//    public String getPostalCode() {
//        return postalCode;
//    }
//
//    public void setPostalCode(String postalCode) {
//        this.postalCode = postalCode;
//    }
//
//    public Integer getStatus() {
//        return status;
//    }
//
//    public void setStatus(Integer status) {
//        this.status = status;
//    }
//
//    public String getJobTitle() {
//        return jobTitle;
//    }
//
//    public void setJobTitle(String jobTitle) {
//        this.jobTitle = jobTitle;
//    }
//
//    public Integer getJobCategory() {
//        return jobCategory;
//    }
//
//    public void setJobCategory(Integer jobCategory) {
//        this.jobCategory = jobCategory;
//    }
//
//    public String getApplicationNumber() {
//        return applicationNumber;
//    }
//
//    public void setApplicationNumber(String applicationNumber) {
//        this.applicationNumber = applicationNumber;
//    }
//
//    public LocalDateTime getApplyTime() {
//        return applyTime;
//    }
//
//    public void setApplyTime(LocalDateTime applyTime) {
//        this.applyTime = applyTime;
//    }
//
//    public String getRecruitmentUnit() {
//        return recruitmentUnit;
//    }
//
//    public void setRecruitmentUnit(String recruitmentUnit) {
//        this.recruitmentUnit = recruitmentUnit;
//    }
//
//    public String getHeadUrl() {
//        return headUrl;
//    }
//
//    public void setHeadUrl(String headUrl) {
//        this.headUrl = headUrl;
//    }
//
//    public String getGrade() {
//        return grade;
//    }
//
//    public void setGrade(String grade) {
//        this.grade = grade;
//    }
//
//    public LocalDateTime getTitleExaminationTime() {
//        return titleExaminationTime;
//    }
//
//    public void setTitleExaminationTime(LocalDateTime titleExaminationTime) {
//        this.titleExaminationTime = titleExaminationTime;
//    }
//
//    public LocalDateTime getPassInterviewTime() {
//        return passInterviewTime;
//    }
//
//    public void setPassInterviewTime(LocalDateTime passInterviewTime) {
//        this.passInterviewTime = passInterviewTime;
//    }
//
//    public String getFirstApprove() {
//        return firstApprove;
//    }
//
//    public void setFirstApprove(String firstApprove) {
//        this.firstApprove = firstApprove;
//    }
//
//    public String getFaceApprove() {
//        return faceApprove;
//    }
//
//    public void setFaceApprove(String faceApprove) {
//        this.faceApprove = faceApprove;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public LocalDateTime getEmploymentTime() {
//        return employmentTime;
//    }
//
//    public void setEmploymentTime(LocalDateTime employmentTime) {
//        this.employmentTime = employmentTime;
//    }
//
//    public LocalDateTime getPartyJoinTime() {
//        return partyJoinTime;
//    }
//
//    public void setPartyJoinTime(LocalDateTime partyJoinTime) {
//        this.partyJoinTime = partyJoinTime;
//    }
//
//    public String getHouseholdType() {
//        return householdType;
//    }
//
//    public void setHouseholdType(String householdType) {
//        this.householdType = householdType;
//    }
//
//    public String getResidence() {
//        return residence;
//    }
//
//    public void setResidence(String residence) {
//        this.residence = residence;
//    }
//
//    public String getQualificationCertificate() {
//        return qualificationCertificate;
//    }
//
//    public void setQualificationCertificate(String qualificationCertificate) {
//        this.qualificationCertificate = qualificationCertificate;
//    }
//
//    public String getRewardAndPunishment() {
//        return rewardAndPunishment;
//    }
//
//    public void setRewardAndPunishment(String rewardAndPunishment) {
//        this.rewardAndPunishment = rewardAndPunishment;
//    }
//
//    public String getModifySuggestion() {
//        return modifySuggestion;
//    }
//
//    public void setModifySuggestion(String modifySuggestion) {
//        this.modifySuggestion = modifySuggestion;
//    }

    //    /**
//     * 时间格式
//     */
//    private static final String DATE = "yyyy-MM-dd HH:mm:ss";
//
//    /**
//     * 主键id，自增
//     */
//    @TableId(value = "id", type = IdType.AUTO)
//    @ApiModelProperty(value = "主键id", required = true, example = "1")
//    private Integer id;
//    /**
//     * 招聘单位
//     */
//    @ApiModelProperty(value = "招聘单位")
//    private String recruitmentUnit;
//    /**
//     * 招聘部门
//     */
//    @ApiModelProperty(value = "招聘部门")
//    private String recruitmentDepartment;
//    /**
//     * 岗位名称
//     */
//    @ApiModelProperty(value = "岗位名称")
//    private String positionName;
//    /**
//     * 岗位代码
//     */
//    @ApiModelProperty(value = "岗位代码")
//    private String postCode;
//    /**
//     * 岗位类别，1-专业技术岗位,2-管理岗位,3-工勤技能岗位,4-其他岗位
//     */
//    @ApiModelProperty(value = "岗位类别")
//    private Integer jobCategory;
//    /**
//     * 招聘计划
//     */
//    @ApiModelProperty(value = "招聘计划")
//    private String recruitmentPlan;
//    /**
//     * 招聘类别:1为高层次，2为非高层次
//     */
//    @ApiModelProperty(value = "招聘类别")
//    private Integer recruitmentCategory;
//    /**
//     * 学历学位
//     */
//    @ApiModelProperty(value = "学历学位")
//    private String academicDegree;
//    /**
//     * 年龄
//     */
//    @ApiModelProperty(value = "年龄")
//    private String age;
//    /**
//     * 专业
//     */
//    @ApiModelProperty(value = "专业")
//    private String profession;
//    /**
//     * 其它
//     */
//    @ApiModelProperty(value = "其它")
//    private String other;
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value = "备注")
//    private String remark;
//    /**
//     * 任职要求
//     */
//    @ApiModelProperty(value = "任职要求")
//    private String jobRequirements;
//    /**
//     * 岗位职责
//     */
//    @ApiModelProperty(value = "岗位职责")
//    private String jobResponsibility;
//    /**
//     * 发布开始时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布开始时间")
//    private LocalDateTime startTime;
//    /**
//     * 发布截止时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布截止时间")
//    private LocalDateTime cutTime;
//    /**
//     * 发布状态,1-未发布,2-招聘中,3-完成
//     */
//    @ApiModelProperty(value = "发布状态")
//    private String releaseStatus;
//    /**
//     * 所属批次,与批次管理表的id对应
//     */
//    @ApiModelProperty(value = "所属批次")
//    private Integer owningBatch;
//    /**
//     * 创建时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "创建时间")
//    private LocalDateTime createTime;
//    public SchedulePostVO() {
//    }
//
//    public SchedulePostVO(Integer id, String name, Integer sex, String cardNo, String nation, LocalDateTime birthday, String politicsStatus, String highestEducationLevel, String highestAcademicDegree, String highestGraduateSchool, LocalDateTime lastGraduationTime, String major, String professionalTitle, String domicile, String nativePlace, String freshGraduate, String archivalCustodianUnit, String strongPoint, String currentUnitPosition, LocalDateTime acquisitionTime, String maritalStatus, String resumeInfo, String phone, String mailbox, String address, String postalCode, Integer status, String jobTitle, Integer jobCategory, String applicationNumber, LocalDateTime applyTime, String recruitmentUnit, String headUrl, String grade, LocalDateTime titleExaminationTime, LocalDateTime passInterviewTime, String firstApprove, String faceApprove, Long userId, LocalDateTime employmentTime, LocalDateTime partyJoinTime, String householdType, String residence, String qualificationCertificate, String rewardAndPunishment, String modifySuggestion) {
//        this.id = id;
//        this.name = name;
//        this.sex = sex;
//        this.cardNo = cardNo;
//        this.nation = nation;
//        this.birthday = birthday;
//        this.politicsStatus = politicsStatus;
//        this.highestEducationLevel = highestEducationLevel;
//        this.highestAcademicDegree = highestAcademicDegree;
//        this.highestGraduateSchool = highestGraduateSchool;
//        this.lastGraduationTime = lastGraduationTime;
//        this.major = major;
//        this.professionalTitle = professionalTitle;
//        this.domicile = domicile;
//        this.nativePlace = nativePlace;
//        this.freshGraduate = freshGraduate;
//        this.archivalCustodianUnit = archivalCustodianUnit;
//        this.strongPoint = strongPoint;
//        this.currentUnitPosition = currentUnitPosition;
//        this.acquisitionTime = acquisitionTime;
//        this.maritalStatus = maritalStatus;
//        this.resumeInfo = resumeInfo;
//        this.phone = phone;
//        this.mailbox = mailbox;
//        this.address = address;
//        this.postalCode = postalCode;
//        this.status = status;
//        this.jobTitle = jobTitle;
//        this.jobCategory = jobCategory;
//        this.applicationNumber = applicationNumber;
//        this.applyTime = applyTime;
//        this.recruitmentUnit = recruitmentUnit;
//        this.headUrl = headUrl;
//        this.grade = grade;
//        this.titleExaminationTime = titleExaminationTime;
//        this.passInterviewTime = passInterviewTime;
//        this.firstApprove = firstApprove;
//        this.faceApprove = faceApprove;
//        this.userId = userId;
//        this.employmentTime = employmentTime;
//        this.partyJoinTime = partyJoinTime;
//        this.householdType = householdType;
//        this.residence = residence;
//        this.qualificationCertificate = qualificationCertificate;
//        this.rewardAndPunishment = rewardAndPunishment;
//        this.modifySuggestion = modifySuggestion;
//    }
//
//    public Integer getId() {
//        return id;
//    }
//
//    public void setId(Integer id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public Integer getSex() {
//        return sex;
//    }
//
//    public void setSex(Integer sex) {
//        this.sex = sex;
//    }
//
//    public String getCardNo() {
//        return cardNo;
//    }
//
//    public void setCardNo(String cardNo) {
//        this.cardNo = cardNo;
//    }
//
//    public String getNation() {
//        return nation;
//    }
//
//    public void setNation(String nation) {
//        this.nation = nation;
//    }
//
//    public LocalDateTime getBirthday() {
//        return birthday;
//    }
//
//    public void setBirthday(LocalDateTime birthday) {
//        this.birthday = birthday;
//    }
//
//    public String getPoliticsStatus() {
//        return politicsStatus;
//    }
//
//    public void setPoliticsStatus(String politicsStatus) {
//        this.politicsStatus = politicsStatus;
//    }
//
//    public String getHighestEducationLevel() {
//        return highestEducationLevel;
//    }
//
//    public void setHighestEducationLevel(String highestEducationLevel) {
//        this.highestEducationLevel = highestEducationLevel;
//    }
//
//    public String getHighestAcademicDegree() {
//        return highestAcademicDegree;
//    }
//
//    public void setHighestAcademicDegree(String highestAcademicDegree) {
//        this.highestAcademicDegree = highestAcademicDegree;
//    }
//
//    public String getHighestGraduateSchool() {
//        return highestGraduateSchool;
//    }
//
//    public void setHighestGraduateSchool(String highestGraduateSchool) {
//        this.highestGraduateSchool = highestGraduateSchool;
//    }
//
//    public LocalDateTime getLastGraduationTime() {
//        return lastGraduationTime;
//    }
//
//    public void setLastGraduationTime(LocalDateTime lastGraduationTime) {
//        this.lastGraduationTime = lastGraduationTime;
//    }
//
//    public String getMajor() {
//        return major;
//    }

    //     * 招聘单位
//     */
//    @ApiModelProperty(value = "招聘单位")
//    private String recruitmentUnit;
//    /**
//     * 招聘部门
//     */
//    @ApiModelProperty(value = "招聘部门")
//    private String recruitmentDepartment;
//    /**
//     * 岗位名称
//     */
//    @ApiModelProperty(value = "岗位名称")
//    private String positionName;
//    /**
//     * 岗位代码
//     */
//    @ApiModelProperty(value = "岗位代码")
//    private String postCode;
//    /**
//     * 岗位类别，1-专业技术岗位,2-管理岗位,3-工勤技能岗位,4-其他岗位
//     */
//    @ApiModelProperty(value = "岗位类别")
//    private Integer jobCategory;
//    /**
//     * 招聘计划
//     */
//    @ApiModelProperty(value = "招聘计划")
//    private String recruitmentPlan;
//    /**
//     * 招聘类别:1为高层次，2为非高层次
//     */
//    @ApiModelProperty(value = "招聘类别")
//    private Integer recruitmentCategory;
//    /**
//     * 学历学位
//     */
//    @ApiModelProperty(value = "学历学位")
//    private String academicDegree;
//    /**
//     * 年龄
//     */
//    @ApiModelProperty(value = "年龄")
//    private String age;
//    /**
//     * 专业
//     */
//    @ApiModelProperty(value = "专业")
//    private String profession;
//    /**
//     * 其它
//     */
//    @ApiModelProperty(value = "其它")
//    private String other;
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value = "备注")
//    private String remark;
//    /**
//     * 任职要求
//     */
//    @ApiModelProperty(value = "任职要求")
//    private String jobRequirements;
//    /**
//     * 岗位职责
//     */
//    @ApiModelProperty(value = "岗位职责")
//    private String jobResponsibility;
//    /**
//     * 发布开始时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布开始时间")
//    private LocalDateTime startTime;
//    /**
//     * 发布截止时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布截止时间")
//    private LocalDateTime cutTime;
//    /**
//     * 发布状态,1-未发布,2-招聘中,3-完成
//     */
//    @ApiModelProperty(value = "发布状态")
//    private String releaseStatus;
//    /**
//     * 所属批次,与批次管理表的id对应
//     */
//    @ApiModelProperty(value = "所属批次")
//    private Integer owningBatch;
//    /**
//     * 创建时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "创建时间")
//    private LocalDateTime createTime;
//    public SchedulePostVO() {
//    }
//
//    public SchedulePostVO(Integer id, String name, Integer sex, String cardNo, String nation, LocalDateTime birthday, String politicsStatus, String highestEducationLevel, String highestAcademicDegree, String highestGraduateSchool, LocalDateTime lastGraduationTime, String major, String professionalTitle, String domicile, String nativePlace, String freshGraduate, String archivalCustodianUnit, String strongPoint, String currentUnitPosition, LocalDateTime acquisitionTime, String maritalStatus, String resumeInfo, String phone, String mailbox, String address, String postalCode, Integer status, String jobTitle, Integer jobCategory, String applicationNumber, LocalDateTime applyTime, String recruitmentUnit, String headUrl, String grade, LocalDateTime titleExaminationTime, LocalDateTime passInterviewTime, String firstApprove, String faceApprove, Long userId, LocalDateTime employmentTime, LocalDateTime partyJoinTime, String householdType, String residence, String qualificationCertificate, String rewardAndPunishment, String modifySuggestion) {
//        this.id = id;
//        this.name = name;
//        this.sex = sex;
//        this.cardNo = cardNo;
//        this.nation = nation;
//        this.birthday = birthday;
//        this.politicsStatus = politicsStatus;
//        this.highestEducationLevel = highestEducationLevel;
//        this.highestAcademicDegree = highestAcademicDegree;
//        this.highestGraduateSchool = highestGraduateSchool;
//        this.lastGraduationTime = lastGraduationTime;
//        this.major = major;
//        this.professionalTitle = professionalTitle;
//        this.domicile = domicile;
//        this.nativePlace = nativePlace;
//        this.freshGraduate = freshGraduate;
//        this.archivalCustodianUnit = archivalCustodianUnit;
//        this.strongPoint = strongPoint;
//        this.currentUnitPosition = currentUnitPosition;
//        this.acquisitionTime = acquisitionTime;
//        this.maritalStatus = maritalStatus;
//        this.resumeInfo = resumeInfo;
//        this.phone = phone;
//        this.mailbox = mailbox;
//        this.address = address;
//        this.postalCode = postalCode;
//        this.status = status;
//        this.jobTitle = jobTitle;
//        this.jobCategory = jobCategory;
//        this.applicationNumber = applicationNumber;
//        this.applyTime = applyTime;
//        this.recruitmentUnit = recruitmentUnit;
//        this.headUrl = headUrl;
//        this.grade = grade;
//        this.titleExaminationTime = titleExaminationTime;
//        this.passInterviewTime = passInterviewTime;
//        this.firstApprove = firstApprove;
//        this.faceApprove = faceApprove;
//        this.userId = userId;
//        this.employmentTime = employmentTime;
//        this.partyJoinTime = partyJoinTime;
//        this.householdType = householdType;
//        this.residence = residence;
//        this.qualificationCertificate = qualificationCertificate;
//        this.rewardAndPunishment = rewardAndPunishment;
//        this.modifySuggestion = modifySuggestion;
//    }
//
//    public Integer getId() {
//        return id;
//    }
//
//    public void setId(Integer id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public Integer getSex() {
//        return sex;
//    }
//
//    public void setSex(Integer sex) {
//        this.sex = sex;
//    }
//
//    public String getCardNo() {
//        return cardNo;
//    }
//
//    public void setCardNo(String cardNo) {
//        this.cardNo = cardNo;
//    }
//
//    public String getNation() {
//        return nation;
//    }
//
//    public void setNation(String nation) {
//        this.nation = nation;
//    }
//
//    public LocalDateTime getBirthday() {
//        return birthday;
//    }
//
//    public void setBirthday(LocalDateTime birthday) {
//        this.birthday = birthday;
//    }
//
//    public String getPoliticsStatus() {
//        return politicsStatus;
//    }
//
//    public void setPoliticsStatus(String politicsStatus) {
//        this.politicsStatus = politicsStatus;
//    }
//
//    public String getHighestEducationLevel() {
//        return highestEducationLevel;
//    }
//
//    public void setHighestEducationLevel(String highestEducationLevel) {
//        this.highestEducationLevel = highestEducationLevel;
//    }
//
//    public String getHighestAcademicDegree() {
//        return highestAcademicDegree;
//    }
//
//    public void setHighestAcademicDegree(String highestAcademicDegree) {
//        this.highestAcademicDegree = highestAcademicDegree;
//    }
//
//    public String getHighestGraduateSchool() {
//        return highestGraduateSchool;
//    }
//
//    public void setHighestGraduateSchool(String highestGraduateSchool) {
//        this.highestGraduateSchool = highestGraduateSchool;
//    }
//
//    public LocalDateTime getLastGraduationTime() {
//        return lastGraduationTime;
//    }
//
//    public void setLastGraduationTime(LocalDateTime lastGraduationTime) {
//        this.lastGraduationTime = lastGraduationTime;
//    }
//
//    public String getMajor() {
//        return major;
//    }
    //    public void setCardNo(String cardNo) {
//        this.cardNo = cardNo;
//    }
//
//    public String getNation() {
//        return nation;
//    }
//
//    public void setNation(String nation) {
//        this.nation = nation;
//    }
//
//    public LocalDateTime getBirthday() {
//        return birthday;
//    }
//
//    public void setBirthday(LocalDateTime birthday) {
//        this.birthday = birthday;
//    }
//
//    public String getPoliticsStatus() {
//        return politicsStatus;
//    }
//
//    public void setPoliticsStatus(String politicsStatus) {
//        this.politicsStatus = politicsStatus;
//    }
//
//    public String getHighestEducationLevel() {
//        return highestEducationLevel;
//    }
//
//    public void setHighestEducationLevel(String highestEducationLevel) {
//        this.highestEducationLevel = highestEducationLevel;
//    }
//
//    public String getHighestAcademicDegree() {
//        return highestAcademicDegree;
//    }
//
//    public void setHighestAcademicDegree(String highestAcademicDegree) {
//        this.highestAcademicDegree = highestAcademicDegree;
//    }
//
//    public String getHighestGraduateSchool() {
//        return highestGraduateSchool;
//    }
//
//    public void setHighestGraduateSchool(String highestGraduateSchool) {
//        this.highestGraduateSchool = highestGraduateSchool;
//    }
//
//    public LocalDateTime getLastGraduationTime() {
//        return lastGraduationTime;
//    }
//
//    public void setLastGraduationTime(LocalDateTime lastGraduationTime) {
//        this.lastGraduationTime = lastGraduationTime;
//    }
//
//    public String getMajor() {
//        return major;
//    }

    //     * 招聘单位
//     */
//    @ApiModelProperty(value = "招聘单位")
//    private String recruitmentUnit;
//    /**
//     * 招聘部门
//     */
//    @ApiModelProperty(value = "招聘部门")
//    private String recruitmentDepartment;
//    /**
//     * 岗位名称
//     */
//    @ApiModelProperty(value = "岗位名称")
//    private String positionName;
//    /**
//     * 岗位代码
//     */
//    @ApiModelProperty(value = "岗位代码")
//    private String postCode;
//    /**
//     * 岗位类别，1-专业技术岗位,2-管理岗位,3-工勤技能岗位,4-其他岗位
//     */
//    @ApiModelProperty(value = "岗位类别")
//    private Integer jobCategory;
//    /**
//     * 招聘计划
//     */
//    @ApiModelProperty(value = "招聘计划")
//    private String recruitmentPlan;
//    /**
//     * 招聘类别:1为高层次，2为非高层次
//     */
//    @ApiModelProperty(value = "招聘类别")
//    private Integer recruitmentCategory;
//    /**
//     * 学历学位
//     */
//    @ApiModelProperty(value = "学历学位")
//    private String academicDegree;
//    /**
//     * 年龄
//     */
//    @ApiModelProperty(value = "年龄")
//    private String age;
//    /**
//     * 专业
//     */
//    @ApiModelProperty(value = "专业")
//    private String profession;
//    /**
//     * 其它
//     */
//    @ApiModelProperty(value = "其它")
//    private String other;
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value = "备注")
//    private String remark;
//    /**
//     * 任职要求
//     */
//    @ApiModelProperty(value = "任职要求")
//    private String jobRequirements;
//    /**
//     * 岗位职责
//     */
//    @ApiModelProperty(value = "岗位职责")
//    private String jobResponsibility;
//    /**
//     * 发布开始时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布开始时间")
//    private LocalDateTime startTime;
//    /**
//     * 发布截止时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "发布截止时间")
//    private LocalDateTime cutTime;
//    /**
//     * 发布状态,1-未发布,2-招聘中,3-完成
//     */
//    @ApiModelProperty(value = "发布状态")
//    private String releaseStatus;
//    /**
//     * 所属批次,与批次管理表的id对应
//     */
//    @ApiModelProperty(value = "所属批次")
//    private Integer owningBatch;
//    /**
//     * 创建时间
//     */
//    @DateTimeFormat(pattern = DATE)
//    @ApiModelProperty(value = "创建时间")
//    private LocalDateTime createTime;
//    public SchedulePostVO() {
//    }
//
//    public SchedulePostVO(Integer id, String name, Integer sex, String cardNo, String nation, LocalDateTime birthday, String politicsStatus, String highestEducationLevel, String highestAcademicDegree, String highestGraduateSchool, LocalDateTime lastGraduationTime, String major, String professionalTitle, String domicile, String nativePlace, String freshGraduate, String archivalCustodianUnit, String strongPoint, String currentUnitPosition, LocalDateTime acquisitionTime, String maritalStatus, String resumeInfo, String phone, String mailbox, String address, String postalCode, Integer status, String jobTitle, Integer jobCategory, String applicationNumber, LocalDateTime applyTime, String recruitmentUnit, String headUrl, String grade, LocalDateTime titleExaminationTime, LocalDateTime passInterviewTime, String firstApprove, String faceApprove, Long userId, LocalDateTime employmentTime, LocalDateTime partyJoinTime, String householdType, String residence, String qualificationCertificate, String rewardAndPunishment, String modifySuggestion) {
//        this.id = id;
//        this.name = name;
//        this.sex = sex;
//        this.cardNo = cardNo;
//        this.nation = nation;
//        this.birthday = birthday;
//        this.politicsStatus = politicsStatus;
//        this.highestEducationLevel = highestEducationLevel;
//        this.highestAcademicDegree = highestAcademicDegree;
//        this.highestGraduateSchool = highestGraduateSchool;
//        this.lastGraduationTime = lastGraduationTime;
//        this.major = major;
//        this.professionalTitle = professionalTitle;
//        this.domicile = domicile;
//        this.nativePlace = nativePlace;
//        this.freshGraduate = freshGraduate;
//        this.archivalCustodianUnit = archivalCustodianUnit;
//        this.strongPoint = strongPoint;
//        this.currentUnitPosition = currentUnitPosition;
//        this.acquisitionTime = acquisitionTime;
//        this.maritalStatus = maritalStatus;
//        this.resumeInfo = resumeInfo;
//        this.phone = phone;
//        this.mailbox = mailbox;
//        this.address = address;
//        this.postalCode = postalCode;
//        this.status = status;
//        this.jobTitle = jobTitle;
//        this.jobCategory = jobCategory;
//        this.applicationNumber = applicationNumber;
//        this.applyTime = applyTime;
//        this.recruitmentUnit = recruitmentUnit;
//        this.headUrl = headUrl;
//        this.grade = grade;
//        this.titleExaminationTime = titleExaminationTime;
//        this.passInterviewTime = passInterviewTime;
//        this.firstApprove = firstApprove;
//        this.faceApprove = faceApprove;
//        this.userId = userId;
//        this.employmentTime = employmentTime;
//        this.partyJoinTime = partyJoinTime;
//        this.householdType = householdType;
//        this.residence = residence;
//        this.qualificationCertificate = qualificationCertificate;
//        this.rewardAndPunishment = rewardAndPunishment;
//        this.modifySuggestion = modifySuggestion;
//    }
//
//    public Integer getId() {
//        return id;
//    }
//
//    public void setId(Integer id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public Integer getSex() {
//        return sex;
//    }
//
//    public void setSex(Integer sex) {
//        this.sex = sex;
//    }
//
//    public String getCardNo() {
//        return cardNo;
//    }
//
//    public void setCardNo(String cardNo) {
//        this.cardNo = cardNo;
//    }
//
//    public String getNation() {
//        return nation;
//    }
//
//    public void setNation(String nation) {
//        this.nation = nation;
//    }
//
//    public LocalDateTime getBirthday() {
//        return birthday;
//    }
//
//    public void setBirthday(LocalDateTime birthday) {
//        this.birthday = birthday;
//    }
//
//    public String getPoliticsStatus() {
//        return politicsStatus;
//    }
//
//    public void setPoliticsStatus(String politicsStatus) {
//        this.politicsStatus = politicsStatus;
//    }
//
//    public String getHighestEducationLevel() {
//        return highestEducationLevel;
//    }
//
//    public void setHighestEducationLevel(String highestEducationLevel) {
//        this.highestEducationLevel = highestEducationLevel;
//    }
//
//    public String getHighestAcademicDegree() {
//        return highestAcademicDegree;
//    }
//
//    public void setHighestAcademicDegree(String highestAcademicDegree) {
//        this.highestAcademicDegree = highestAcademicDegree;
//    }
//
//    public String getHighestGraduateSchool() {
//        return highestGraduateSchool;
//    }
//
//    public void setHighestGraduateSchool(String highestGraduateSchool) {
//        this.highestGraduateSchool = highestGraduateSchool;
//    }
//
//    public LocalDateTime getLastGraduationTime() {
//        return lastGraduationTime;
//    }
//
//    public void setLastGraduationTime(LocalDateTime lastGraduationTime) {
//        this.lastGraduationTime = lastGraduationTime;
//    }
//
//    public String getMajor() {
//        return major;
//    public void setHighestAcademicDegree(String highestAcademicDegree) {
//        this.highestAcademicDegree = highestAcademicDegree;
//    }
//
//    public String getHighestGraduateSchool() {
//        return highestGraduateSchool;
//    }
//
//    public void setHighestGraduateSchool(String highestGraduateSchool) {
//        this.highestGraduateSchool = highestGraduateSchool;
//    }
//
//    public LocalDateTime getLastGraduationTime() {
//        return lastGraduationTime;
//    }
//
//    public void setLastGraduationTime(LocalDateTime lastGraduationTime) {
//        this.lastGraduationTime = lastGraduationTime;
//    }
//
//    public String getMajor() {
//        return major;
}
