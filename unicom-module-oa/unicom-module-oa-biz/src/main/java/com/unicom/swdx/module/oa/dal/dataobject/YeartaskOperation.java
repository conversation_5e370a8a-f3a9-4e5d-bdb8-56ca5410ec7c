package com.unicom.swdx.module.oa.dal.dataobject;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

/**
 * 【请填写功能名称】对象 oa_central_task_operation
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@TableName("oa_central_task_operation")
@KeySequence("oa_central_task_operation_seq")
@Data
@Builder
public class YeartaskOperation
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 操作人昵称 */

    private String nickname;

    /** 操作人id */
    @TableField(value = "operation_user_id")
    private String userid;

    /** 操作人部门名称 */
    @TableField(value = "operation_dept_name")
    private String dptname;

    /** 操作人部门id */
    @TableField(value = "operation_dept_id")
    private String dptid;

    /** 状态 */

    private String status;

    /** 文件 */

    private String files;

    /** 中心工作id */
    @TableField(value = "infor_id")
    private Long inforid;

    /** 提交信息 */

    private String content;

    /**
     * 退回意见*
     * */
    @TableField(value = "rejection_reason")
    private String rejectionreason;

    @TableField(value = "create_time")
    private String createtime;

}
