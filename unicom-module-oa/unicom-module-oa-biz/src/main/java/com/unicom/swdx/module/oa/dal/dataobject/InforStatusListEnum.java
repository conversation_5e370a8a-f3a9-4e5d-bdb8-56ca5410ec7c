package com.unicom.swdx.module.oa.dal.dataobject;

import cn.hutool.core.collection.ListUtil;
import lombok.Builder;

import java.util.List;

//public enum InforStatusListEnum {
//
////    //待提交  草稿
////    CREATE(ListUtil.of(new InforStatusUser(InforUserEnum.STARTUSER , "创建"  , 1 ) )),
////
////    //关闭
////    CLOSE(ListUtil.of(new InforStatusUser(InforUserEnum.STARTUSER , "关闭"  , 2 ) )),
////
////    //待提交  草稿  处长已审批
////    CREATE_DPTLEADER_AGREE(ListUtil.of(new InforStatusUser(InforUserEnum.STARTUSER , "待提交处长已审批"  , 4 ) )),
//
//    //待提交  草稿 办公室管理员已审批
////    commitpre(ListUtil.of(new InforStatusUser(InforUserEnum.STARTUSER , "待提交"  , 10 ) )),
//
//
////    SENDSTATUS30(ListUtil.of("发送即将逾期短信"));
//
//
//
//    private List<InforStatusUser> tag;
//
//    InforStatusListEnum(List<InforStatusUser> tag) {
//        this.tag = tag;
//    }
//
//    public List<InforStatusUser> getTag() {
//        return tag;
//    }
//
//
//
//    @Builder
//    public static class InforStatusUser{
//
//        InforUserEnum inforUserEnum;
//
//        String statusStr;
//
//        int status;
//
//        public InforStatusUser(InforUserEnum inforUserEnum, String statusStr, int status) {
//            this.inforUserEnum = inforUserEnum;
//            this.statusStr = statusStr;
//            this.status = status;
//        }
//    }
//}
