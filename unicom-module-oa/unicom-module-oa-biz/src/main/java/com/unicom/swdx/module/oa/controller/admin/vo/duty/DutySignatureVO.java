package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("办公OA - 寒暑假坐值班部门主管签名信息")
public class DutySignatureVO {

    /**
     * 部门负责人审批时间
     */
    private LocalDateTime majorEndTime;

    /**
     * 部门负责人审批签名链接
     */
    private String majorHandSignature;
}
