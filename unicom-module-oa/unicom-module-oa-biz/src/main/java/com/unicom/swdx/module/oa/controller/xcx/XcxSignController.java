package com.unicom.swdx.module.oa.controller.xcx;

import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.controller.xcx.vo.XcxSignReqVO;
import com.unicom.swdx.module.oa.service.OAXcxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.USER_NOT_MATCH;

@Api(tags = "办公OA - 移动端签字 - 小程序 ")
@RestController
@RequestMapping("/oa/xcx/sign")
public class XcxSignController {

    @Resource
    private OAXcxService oaXcxService;

    @GetMapping("/checkSignUser")
    @ApiOperation("校验签字的用户是否匹配")
    public CommonResult<Boolean> checkSignUser(@RequestParam("uniqueCode") String uniqueCode) {
        String userId = oaXcxService.checkSignUser(uniqueCode);
        if (StrUtil.isBlank(userId)) {
            return CommonResult.error(USER_NOT_MATCH);
        }
        return CommonResult.success(true);
    }

    @PostMapping("/bind")
    @ApiOperation("签字图片绑定uniqueCode")
    public CommonResult<Boolean> bindSign(@RequestBody XcxSignReqVO reqVO) {
        oaXcxService.bindSign(reqVO);
        return CommonResult.success(true);
    }

}
