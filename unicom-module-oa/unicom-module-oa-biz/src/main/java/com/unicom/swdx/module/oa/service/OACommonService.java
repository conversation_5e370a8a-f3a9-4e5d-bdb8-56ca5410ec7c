package com.unicom.swdx.module.oa.service;

import com.unicom.swdx.module.oa.controller.admin.vo.common.OAApproveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;

import java.util.Map;

public interface OACommonService {
    Boolean approveOrReject(OAApproveReqVO approveReqVO);

    Map<String, Object> getApproveList(Long loginUserId, String taskId, String nextTaskName, Long deptId);

    void cancelToStarter(Long loginUserId, String processInstanceId ,String category);

    void cancelToDraft(Long loginUserId, String processInstanceId, String category);

    void sendUrgency(Long loginUserId, String processInstanceId, String urgeMode);

    void end(OARemoveReqVO reqVO);

    void remove(OARemoveReqVO reqVO);

    Map<String, Object> getNextTaskName(Long loginUserId, String category, Long deptId);

    Map<String, Object> getNextTaskInfo(String category, String name, Long day, String processInstanceId);
}
