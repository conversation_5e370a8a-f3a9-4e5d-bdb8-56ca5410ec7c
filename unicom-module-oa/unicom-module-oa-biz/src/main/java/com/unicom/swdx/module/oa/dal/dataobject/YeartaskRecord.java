package com.unicom.swdx.module.oa.dal.dataobject;


import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.Builder;
import lombok.Data;

/**
 * 【请填写功能名称】对象 oa_central_task_operation
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@TableName("oa_central_task_record")
@KeySequence("oa_central_task_record_seq")
@Data
public class YeartaskRecord
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 中心工作id */
    @TableField(value = "infor_id")
    private Long inforid;

    /** 文件 */
    @TableField(value = "files")
    private String recordFiles;

    @TableField(value = "create_time")
    private String createtime;

    /** 提交信息 */
    @TableField(value = "end_content")
    private String recordContent;

    /** 操作人昵称 */
    @TableField(value = "user_name")
    private String userName;

    /** 操作人id */
    @TableField(value = "user_id")
    private String userid;

    /** 状态 */
    @TableField(value = "process_status")
    private Integer processStatus=0;

    private Boolean deleted=false;








}
