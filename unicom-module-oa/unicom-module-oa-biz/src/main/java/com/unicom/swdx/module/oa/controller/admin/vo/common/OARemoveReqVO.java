package com.unicom.swdx.module.oa.controller.admin.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("办公OA - 审批中心 结束和删除 Request VO")
public class OARemoveReqVO {

    @ApiModelProperty(value = "任务id", required = true)
    private String taskId;

    @ApiModelProperty(value = "正在审批的任务Id", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "流程的业务主键", required = true)
    private String category;

}
