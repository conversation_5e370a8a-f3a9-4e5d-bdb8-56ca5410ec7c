package com.unicom.swdx.module.oa.dal.kingbase;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.DutyPersonnelRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.DutySignatureVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.SchedulePersonnelRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyDO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormDO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormLeaderDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface VacationDutyFormMapper extends BaseMapperX<VacationDutyFormDO> {
    List<DutyPersonnelRespVO> selectPersonnelInfo(@Param("deptId") Long deptId);

    List<DutyPersonnelRespVO> selectPersonnelInfoByPage(@Param("deptId") Long deptId,
                                                        @Param("userName") String userName,
                                                        @Param("offset") Integer offset,
                                                        @Param("limit") Integer limit);

    Integer countPersonnelByDeptId(@Param("deptId") Long deptId, @Param("userName") String userName);

    List<DutyPersonnelRespVO> selectPersonnelLeaderInfo();

    DutyPersonnelRespVO getPersonnelInfoByNameAndPhone(@Param("name") String name, @Param("phone") String phone, @Param("deptId") Long deptId);

    List<VacationDutyFormDO> getVacationDutyById(@Param("dutyId") Long dutyId, @Param("dutyType") String dutyType);

    List<VacationDutyFormDO> getVacationDutyFormById(@Param("dutyId") Long dutyId);

    List<VacationDutyFormDO> getVacationDutyFormByInstanceId(@Param("processInstanceId") String processInstanceId);

    List<VacationDutyFormDO> getVacationDutyByInstanceId(@Param("processInstanceId") String processInstanceId, @Param("dutyType") String dutyType);

    DutySignatureVO getMajorSigntureInfo(@Param("processInstanceId") String processInstanceId);

    DutySignatureVO getMajorSigntureInfoById(@Param("id") Long id);

    Integer saveLeaderForm(List<VacationDutyFormLeaderDO> vacationDutyFormList);

    Integer cancelLeaderForm(String ids);

    List<VacationDutyFormDO> getVacationDutyByIdAndType(@Param("dutyId") List<Long> dutyId, @Param("dutyType") String dutyType);

    List<VacationDutyFormDO> getLeaderForm(@Param("vacationDutyId") String vacationDutyIds, @Param("dutyType") String dutyType);

    List<VacationDutyFormDO> getLeaderForm2(@Param("vacationDutyId") String vacationDutyIds, @Param("dutyType") String dutyType);
}
