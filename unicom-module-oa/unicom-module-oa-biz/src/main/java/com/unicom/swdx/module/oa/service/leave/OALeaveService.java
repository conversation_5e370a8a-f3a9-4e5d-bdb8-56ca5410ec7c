package com.unicom.swdx.module.oa.service.leave;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.*;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface OALeaveService extends IService<OALeaveDO> {

    /**
     * 创建请假申请
     *
     * @param userId 用户编号
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createLeave(Long userId, @Valid OALeaveCreateReqVO createReqVO);

    /**
     * 重新发起请假申请
     *
     * @param userId 用户编号
     * @param reqVO 请假信息
     */
    void restartLeave(Long userId, @Valid OALeaveCreateReqVO reqVO);

    /**
     * 更新请假申请的状态
     *
     * @param id 编号
     * @param result 结果
     */
    void updateLeaveResult(Long id, Integer result);

    /**
     * 获得请假申请
     *
     * @param id 编号
     * @return 请假申请
     */
    OALeaveDO getLeave(Long id);

//    /**
//     * 获得请假申请分页
//     *
//     * @param userId 用户编号
//     * @param pageReqVO 分页查询
//     * @return 请假申请分页
//     */
//    PageResult<OALeaveDO> getLeavePage(Long userId, OALeavePageReqVO pageReqVO);

    boolean getMutilData(String startTime, String endTime, String processInstanceId);


    boolean saveVaribale(Map<String, Object> map, String proId);

    /**
     * 更新状态
     * @param proId
     * @param result
     * @return
     */
    boolean updateResult(String proId,Integer result);

    /**
     * 更新状态
     * @param id
     * @param result
     * @return
     */
    public boolean updateResultById(Long id, Integer result);

    /**
     * 更新状态
     * @param id
     * @param
     * @return
     */
    Map<String, LocalDate> getDateById(Long id);

    boolean saveDraft(Long loginUserId, OALeaveCreateReqVO draftReqVO);

    boolean judgeIfDirector(Long loginUserId);

    PageResult<LeaveStatisticsRespVO> getLeaveStatisticsList(Long loginUserId, OALeaveStatisticPageReqVO reqVO);

    PageResult<OALeaveRespVO> getLeaveInfoList(OALeavePageReqVO reqVO);

    OALeaveDO getByProcessInstanceId(String processInstanceId);

    OALeaveRespVO get(Long id, String processInstanceId, Boolean isReceived);

    Map<String, Object> getPersonalLeaveCount(Long userId, String startTime, String endTime);

    void dealLeave(OADealLeaveVO dealLeaveVO);

    Integer calculateLeaveDay(LocalDate startTime, LocalDate endTime);

    void autoDealLeave();

    void removeByProcessInstanceId(String processInstanceId);

    Integer getPostType(Long userId);
}
