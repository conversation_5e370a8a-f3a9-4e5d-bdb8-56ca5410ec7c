package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.service.lecture.LectureService;
import com.unicom.swdx.module.oa.service.vacationDuty.VacationDutyService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class VacationDutyApiImpl implements VacationDutyApi {
    @Resource
    private VacationDutyService vacationDutyService;

    @Override
    public CommonResult<Long> getDeptId(String processInstanceId) {
        return success(vacationDutyService.getDeptIdByProcessInstanceId(processInstanceId));
    }

}
