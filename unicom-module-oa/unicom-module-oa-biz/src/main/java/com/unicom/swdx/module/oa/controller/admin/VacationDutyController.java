package com.unicom.swdx.module.oa.controller.admin;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.*;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.*;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryScheduleRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyDO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormDO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormLeaderDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.oa.service.vacationDuty.DutyListener;
import com.unicom.swdx.module.oa.service.vacationDuty.VacationDutyService;
import com.unicom.swdx.module.oa.task.GetDutyTypeSheetWriteHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.VACATION_DUTY_IMPORT_OVERFLOW_ERROR;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.DEPT_TIME_ERROR;

/**
 * 寒暑假坐值班
 * <AUTHOR>
 */
@Api(tags = "办公OA - 寒暑假坐值班")
@RestController
@Slf4j
@RequestMapping("/oa/vacationDuty")
public class VacationDutyController {
    @Resource
    private VacationDutyService vacationDutyService;

    @Resource
    private OATaskService oaTaskService;


    @PostMapping("/saveDraft")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("寒暑假坐值班保存草稿")
    public CommonResult<Long> saveDraft(@Valid @RequestBody DutyCreateReqVO createReqVO) {
        return success(vacationDutyService.saveDraft(createReqVO));
    }

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @ApiOperation("发起寒暑假坐值班审批申请")
    public CommonResult<String> createWorkScheduleProcess(@Valid @RequestBody DutyCreateReqVO createReqVO) {
        return success(vacationDutyService.createVacationDutyProcess(createReqVO));
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("驳回或撤销后重新编辑再发起流程")
    public CommonResult<Boolean> restartSchedule(@RequestBody DutyCreateReqVO reqVO) {
        vacationDutyService.restartDuty(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/edit")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("重新编辑寒暑假坐值班")
    public CommonResult<Boolean> editSchedule(@RequestBody DutyCreateReqVO reqVO) {
        vacationDutyService.editDuty(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/end")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("结束寒暑假坐值班")
    public CommonResult<Boolean> endDuty(@RequestBody Map<String, List<OARemoveReqVO>> reqVO) {
        List<OARemoveReqVO> oaRemoveReqVOList = reqVO.get("selectIds");
        vacationDutyService.end(oaRemoveReqVOList);
        return success(true);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获得寒暑假坐值班审批申请")
    public CommonResult<DutyRespVO> getDuty(@RequestParam(value = "id",required = false) Long id,
                                                        @RequestParam(value = "processInstanceId",required = false) String processInstanceId) {
        DutyRespVO respVO = vacationDutyService.get(id, processInstanceId);
        return success(respVO);
    }

    @GetMapping("/getForEdit")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获得寒暑假坐值班审批申请-给编辑回显接口")
    public CommonResult<DutyRespVO> getDutyForEdit(@RequestParam(value = "id",required = false) Long id,
                                                    @RequestParam(value = "processInstanceId",required = false) String processInstanceId) {
        DutyRespVO respVO = vacationDutyService.getForEdit(id, processInstanceId);
        return success(respVO);
    }

    @GetMapping("/getDutyList")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获取未汇总的所有寒暑假坐值班列表")
    public CommonResult<SummaryDutyRespVO> getDutyList() {
        SummaryDutyRespVO weeklyWorkScheduleList = vacationDutyService.getDutyList();
        return success(weeklyWorkScheduleList);
    }

    @GetMapping("/getPersonnel")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("通过部门id获得寒暑假排班表人员列表")
    public CommonResult<List<DutyPersonnelRespVO>> getPersonnel(@RequestParam(value = "deptId") Long deptId) {
        return success(vacationDutyService.getPersonnel(deptId));
    }

    @GetMapping("/getPagePersonnel")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("通过部门id获得寒暑假排班表人员列表")
    public CommonResult<PageResult<DutyPersonnelRespVO>> getPagePersonnel(@RequestParam(value = "deptId") Long deptId,
                                                                          @RequestParam(value = "username", required = false) String username,
                                                                          @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        return success(vacationDutyService.getPagePersonnel(deptId, username, pageNo, pageSize));
    }

    @GetMapping("/getPersonnelLeader")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获得寒暑假排班表领导列表")
    public CommonResult<List<DutyPersonnelRespVO>> getPersonnelLeader() {
        return success(vacationDutyService.getPersonnelLeader());
    }

    @GetMapping("/getPreview")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获得寒暑假坐值班表汇总预览")
    public CommonResult<List<DutyFormBaseVO>> getPreviewData(@RequestParam(value = "ids",required = false) String ids,
                                                             @RequestParam(value = "dutyType", required = false) String dutyType) {
        List<Long> idList = new ArrayList<>(Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        List<DutyFormBaseVO> result = vacationDutyService.getPreviewData(idList, dutyType);
        return success(result);
    }

    @GetMapping("/getAllPreview")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获得全部寒暑假坐值班表汇总预览")
    public CommonResult<DutyFormTotalVO> getAllPreview(@RequestParam(value = "ids",required = false) String ids) {
        return success(vacationDutyService.getAllPreview(ids));
    }

    @PostMapping("/saveLeaderForm")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("保存领导寒暑假坐值班表")
    public CommonResult<Integer> saveLeaderForm(@Valid @RequestBody List<VacationDutyFormLeaderDO> createReqVO) {
//        List<VacationDutyFormLeaderDO> createReqVO = createReqVOMap.get("vacationDutyFormLeaderList");
        String ids = "";
        if(!createReqVO.isEmpty()){
            ids = createReqVO.get(0).getVacationDutyIds();
        }
        else{
            return success(0);
        }
        return success(vacationDutyService.saveLeaderForm(createReqVO, ids));
    }

    @GetMapping("/cancelLeaderForm")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
        @ApiOperation("删除领导寒暑假坐值班表")
    public CommonResult<Boolean> cancelLeaderForm(@RequestParam(value = "ids",required = false) String ids) {
        vacationDutyService.cancelLeaderForm(ids);
        return success(true);
    }

    @GetMapping("/get-import-template")
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    @ApiOperation("获得导入寒暑假排班表模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        String filename = "寒暑假坐值班模板.xls";
        String sheetName = "寒暑假坐值班";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), DutyFormExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new GetDutyTypeSheetWriteHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(Collections.emptyList());
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入寒暑假排班表")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "deptId", value = "部门编号", required = true, dataTypeClass = Long.class)
    })
    @PreAuthorize("@ss.hasPermission('oa:holiday:create')")
    public CommonResult<List<DutyFormExcelRespVO>> importExcel(@RequestParam("file") MultipartFile file,
                                                               @RequestParam Long deptId) throws Exception {
        List<DutyFormExcelVO> list = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader excelReader = EasyExcel.read(inputStream, DutyFormExcelVO.class, new DutyListener()).build();
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
            excelReader.finish();
        } catch (Exception e) {
            throw exception(VACATION_DUTY_IMPORT_FILE_TYPE_ERROR);
        }
        try{
            list = ExcelUtils.read(file, DutyFormExcelVO.class);
            ExcelValidator.valid(list,1);
        }
        catch (Exception e){
            throw exception(VACATION_DUTY_IMPORT_FILE_ERROR);
        }
        return success(vacationDutyService.importDutyForm(list, deptId));
    }
}
