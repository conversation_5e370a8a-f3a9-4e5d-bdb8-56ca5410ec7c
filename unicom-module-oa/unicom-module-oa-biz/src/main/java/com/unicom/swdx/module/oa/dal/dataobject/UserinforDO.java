package com.unicom.swdx.module.oa.dal.dataobject;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;


/**
 * 重点任务关系 DO
 *
 * <AUTHOR>
 */
@TableName("oa_central_task_extend")
@KeySequence("oa_central_task_extend_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserinforDO  {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 办理人id
     */
    @TableField(value = "user_id")
    private Integer uid;
    /**
     * 办理人名称
     */
    @TableField(value = "user_name")
    private String uname;
    /**
     * 办理人类型
     */
    @TableField(value = "user_type")
    private Integer utype;
    /**
     * 任务id
     */
    @TableField(value = "infor_id")
    private Integer inforid;
    /**
     * 办理人部门id
     */
    @TableField(value = "dept_id")
    private Integer dptid;
    /**
     * 办理人部门名称
     */
    @TableField(value = "dept_name")
    private String dptname;

    private Integer treecode;


    private Long tenantId;




}
