package com.unicom.swdx.module.oa.dal.kingbase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.DutyRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.duty.SummaryDutyRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleDetailRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.SchedulePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleStatisticRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryToDoRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryWithdrawRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyDO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VacationDutyMapper extends BaseMapperX<VacationDutyDO> {

    List<DutyRespVO> getMyToDo(Long userId);

    List<String> getRejected(Long userId);

    List<SummaryWithdrawRespVO> getWithdraw(Long userId);
    Long getByProcessInstanceId(String processInstanceId);
}
