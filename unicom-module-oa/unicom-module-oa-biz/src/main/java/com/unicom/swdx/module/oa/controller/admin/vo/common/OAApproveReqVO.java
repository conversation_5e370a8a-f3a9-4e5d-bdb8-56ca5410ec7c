package com.unicom.swdx.module.oa.controller.admin.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("办公OA - 审批中心 审批 Request VO")
public class OAApproveReqVO {

    @ApiModelProperty(value = "正在审批的任务Id", required = true)
    private String taskId;

    @ApiModelProperty(value = "正在审批的任务Id", required = true)
    private String processInstanceId;

    @ApiModelProperty("审批结果 true同意 false不同意")
    private Boolean result;

    @ApiModelProperty("审批意见")
    private String comment;

    @ApiModelProperty("移动端签字")
    private String handSignature;

    @ApiModelProperty("流程类别")
    private String category;

    @ApiModelProperty("选择的下一任务审批人列表")
    private List<Long> userIds;

    @ApiModelProperty("审批方式 1顺序 2会签")
    private String chargeLeaderSeq;

    @ApiModelProperty("下一步是否需要选择审批人")
    private Boolean select;

}
