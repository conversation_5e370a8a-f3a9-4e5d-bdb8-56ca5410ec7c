package com.unicom.swdx.module.oa.dal.dataobject;

import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Objects;


/**
 * 重点工作信息 DO
 *
 * <AUTHOR>
 */
@TableName("oa_central_task")
@KeySequence("oa_central_task_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InforDO extends BaseDO {

    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 发起人名字
     */
    @TableField(value = "launch_user_name")
    private String susername;
    /**
     * 发起人id
     */
    @TableField(value = "launch_user_id")
    private Integer suserid;
    /**
     * 发起人部门id
     */
    @TableField(value = "launch_dept_id")
    private Integer sdeptid;
    /**
     * 发起人部门名称
     */
    @TableField(value = "launch_dept_name")
    private String sdepname;
    /**
     * 任务名称
     */
    @TableField(value = "key_work_title")
    private String taskname;
    /**
     * 任务类型 1-7
     */
    @TableField(value = "key_work_type")
    private Integer tasktype;
    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private String starttime;
    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private String endtime;
    /**
     * 重要程度
     */
    @TableField(value = "urgency")
    private Integer degree;
    /**
     * 任务内容
     */
    @TableField(value = "key_work_content")
    private String taskcontent;
    /**
     * 任务文件
     */
    @TableField(value = "file")
    private String taskfile;
    /**
     * 状态 10
     */
    private Integer status;

    @TableField(value = "end_content")
    private String endcontent;

    @TableField(value = "end_file")
    private String endfile;

    @TableField(value = "year_tag")
    private Integer yeartag;

    @TableField(value = "is_complete")
    private  Integer shandle;

    private String systemId;

    private Integer superviseStatus;

    private String mobile;

    @TableField(value = "head_approve")
    private Integer hhandle;

    @TableField(value = "master_handle")
    private Integer masterhandle;

    @TableField(value = "minister_handle")
    private Integer ministerhandle;

    @TableField(value = "leader_handle")
    private Integer leaderhandle;

    @TableField(value = "principal_handle")
    private Integer principalhandle;

    @TableField(value = "handle_count")
    private Integer handleCount;

    @TableField(value = "is_handle")
    private Integer isHandle;

    @TableField(value = "process_status")
    private Integer processStatus=1;

    @TableField(value ="tenant_id" )
    private Long tenantId;

    /**
     * 是否在已办中展示
     */
    private Integer isShow;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 撤回意见
     */
//    private String rejectionReason;

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        InforDO student = (InforDO) o;
        return Objects.equals(id, student.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
