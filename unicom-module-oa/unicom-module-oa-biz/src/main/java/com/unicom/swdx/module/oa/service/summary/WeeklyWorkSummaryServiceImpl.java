package com.unicom.swdx.module.oa.service.summary;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceRespDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmRestartDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskApproveReqDTO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.oa.api.ReceiveApiImpl;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.api.dto.SummaryDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.*;
import com.unicom.swdx.module.oa.convert.WeeklyWorkSummaryConvert;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.WeeklyWorkSummaryMapper;
import com.unicom.swdx.module.oa.dal.kingbase.WorkScheduleMapper;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.oa.service.receive.ReceiveService;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import jodd.util.StringUtil;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;

@Service
public class WeeklyWorkSummaryServiceImpl extends ServiceImpl<WeeklyWorkSummaryMapper, WeeklyWorkSummaryDO> implements WeeklyWorkSummaryService {

    /**
     * 一周工作安排 对应的流程定义 KEY
     */
    public static final String PROCESS_KEY = "OA-weeklyworksummary";

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;

    @Resource
    private SmsSendApi smsSendApi;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Resource
    private AsyncListenableTaskExecutor taskExecutor;

    @Resource
    private WeeklyWorkSummaryMapper weeklyWorkSummaryMapper;

    @Resource
    private WeeklyWorkScheduleService weeklyWorkScheduleService;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private DraftService draftService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private WorkScheduleMapper workScheduleMapper;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private PostApi postApi;

    @Resource
    private ReceiveService receiveService;

    @Resource
    private ReceiveApiImpl receiveApiImpl;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createWorkSummaryProcess(SummaryCreateReqVO createReqVO) {
        String processInstanceId = start(createReqVO);
        // 设置下一步的审批人
        OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
        pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
        pick.setUserIds(createReqVO.getUserIds());
        pick.setChargeLeaderSeq(createReqVO.getChargeLeaderSeq());
        oaTaskService.pickApprovals(pick, LocalDateTime.now().withSecond(1));

//        return weeklyWorkSummaryDO.getId();
        return processInstanceId;
    }

    private String start(SummaryCreateReqVO createReqVO) {
        Long tenantId = userApi.getUser(getLoginUserId()).getCheckedData().getTenantId();
        // 获取岗位类型
        if (!postApi.getUserByPost("oa-secretary", tenantId).contains(getLoginUserId())) {

            //岗位不符直接抛出异常，不让发起流程
            throw exception(SUMMARY_POST_ERROR);
        }
        List<SummaryToDoRespVO> data = createReqVO.getData();
        for (SummaryToDoRespVO summary : data) {
            if (Objects.equals(summary.getType(),"撤回")||Objects.equals(summary.getType(),"驳回")) {

                if(summary.getSummaryId() != null) {
                    // 删除草稿记录
                    draftService.deleteByItemId(OACategoryConstants.SUMMARY, summary.getSummaryId());
                }

                String processInstanceId = summary.getProcessInstanceId();
                WeeklyWorkScheduleDO workScheduleDO = weeklyWorkScheduleService.getByProcessInstanceId(processInstanceId);
                weeklyWorkScheduleService.updateStatusById(workScheduleDO.getId(),BpmProcessInstanceResultEnum.SUMMARIZE.getResult());

            }
        }
        WeeklyWorkSummaryDO weeklyWorkSummaryDO = WeeklyWorkSummaryConvert.INSTANCE.convert(createReqVO);
        weeklyWorkSummaryDO.setUserId(getLoginUserId());
        this.save(weeklyWorkSummaryDO);
        if (createReqVO.getTaskIds() != null) {
            for (String taskId : createReqVO.getTaskIds()) {
                bpmTaskServiceApi.approveTask(WebFrameworkUtils.getLoginUserId(), new BpmTaskApproveReqDTO().setId(taskId));
            }
        }
        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(getLoginUserId(),
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(weeklyWorkSummaryDO.getId()))).getCheckedData();
        if(processInstanceApi.skipFirstTask(processInstanceId, LocalDateTime.now().withSecond(0)).getCheckedData()){
            // 将工作流的编号，更新到 OA 一周工作安排表单中
            baseMapper.updateById(new WeeklyWorkSummaryDO().setId(weeklyWorkSummaryDO.getId()).setProcessInstanceId(processInstanceId));
        }
        return processInstanceId;
    }

    private void restart(Long loginUserId,SummaryCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        WeeklyWorkSummaryDO weeklyWorkSummary = WeeklyWorkSummaryConvert.INSTANCE.convert(reqVO);
        weeklyWorkSummary.setUserId(loginUserId);

        if (reqVO.getTaskIds() != null) {
            for (String taskId : reqVO.getTaskIds()) {
                bpmTaskServiceApi.approveTask(WebFrameworkUtils.getLoginUserId(), new BpmTaskApproveReqDTO().setId(taskId));
            }
        }

        if (Objects.isNull(weeklyWorkSummary.getId())) {
            throw exception(SUMMARY_NOT_EXIST);
        } else {
            // 修改
            this.updateById(weeklyWorkSummary);
            // 2.设置一下流程流转参数variables，参数没有改变的不用设置
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 3.重新发起流程
            String processInstanceId = reqVO.getProcessInstanceId();
            if(Objects.isNull(reqVO.getProcessInstanceId())){
                processInstanceId=this.getById(reqVO.getId()).getProcessInstanceId();
            }
            OARemoveReqVO removeReqVO = new OARemoveReqVO();

            removeReqVO.setCategory("14");
            removeReqVO.setProcessInstanceId(reqVO.getProcessInstanceId());
            oaTaskService.end(removeReqVO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartSummary(Long loginUserId, SummaryCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        WeeklyWorkSummaryDO weeklyWorkSummary = WeeklyWorkSummaryConvert.INSTANCE.convert(reqVO);
        weeklyWorkSummary.setUserId(loginUserId);

        if (reqVO.getTaskIds() != null) {
            for (String taskId : reqVO.getTaskIds()) {
                bpmTaskServiceApi.approveTask(WebFrameworkUtils.getLoginUserId(), new BpmTaskApproveReqDTO().setId(taskId));
            }
        }

        if (Objects.isNull(weeklyWorkSummary.getId())) {
            throw exception(SUMMARY_NOT_EXIST);
        } else {
            // 修改
            this.updateById(weeklyWorkSummary);
            // 2.设置一下流程流转参数variables，参数没有改变的不用设置
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 3.重新发起流程
            String processInstanceId = reqVO.getProcessInstanceId();
            if(Objects.isNull(reqVO.getProcessInstanceId())){
                processInstanceId=this.getById(reqVO.getId()).getProcessInstanceId();
            }
            BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
            bpmRestartDTO.setLoginUserId(loginUserId);
            bpmRestartDTO.setProcessInstanceId(processInstanceId);
            bpmRestartDTO.setVariables(processInstanceVariables);
            LocalDateTime time = LocalDateTime.now();
            bpmRestartDTO.setTime(time);
            if(bpmTaskServiceApi.restartProcess(bpmRestartDTO).getCheckedData()){
                // 设置下一步的审批人
                OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
                pick.setUserIds(reqVO.getUserIds());
                pick.setChargeLeaderSeq(reqVO.getChargeLeaderSeq());
                oaTaskService.pickApprovals(pick, time.plusSeconds(1L));
            }
        }
        // 更新填报单状态为汇总中
        List<Long> ids = Arrays.stream(reqVO.getWeeklyWorkScheduleIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        weeklyWorkScheduleService.BatchUpdateStatusByIds(ids, BpmProcessInstanceResultEnum.SUMMARIZE.getResult());
    }

    @Override
    public SummaryRespVO get(Long id, String processInstanceId) {
        WeeklyWorkSummaryDO summaryDO = new WeeklyWorkSummaryDO();
        if (Objects.nonNull(id)) {
            summaryDO =  this.getById(id);
        } else if (CharSequenceUtil.isNotBlank(processInstanceId)) {
            summaryDO = getByProcessInstanceId(processInstanceId);
        }
        SummaryRespVO summaryRespVO = WeeklyWorkSummaryConvert.INSTANCE.convert(summaryDO);
        String weeklyWorkScheduleIds = summaryRespVO.getWeeklyWorkScheduleIds();
        List<Long> idList = Arrays.stream(weeklyWorkScheduleIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<WorkScheduleDO> sortedSchedules = getPreviewData(idList);
        summaryRespVO.setWorkSchedules(sortedSchedules);
        summaryRespVO.setUserNickName(userApi.getUser(summaryDO.getUserId()).getCheckedData().getNickname());

        if(CharSequenceUtil.isNotEmpty(summaryDO.getProcessInstanceId())){
            Map<String,String> info = bpmTaskServiceApi.getNeededTaskInfo(OACategoryConstants.SUMMARY, WebFrameworkUtils.getLoginUserId(), processInstanceId, null, null);
            summaryRespVO.setTaskName(info.get("taskName"));
            if(StringUtil.isNotBlank(info.get("operateType"))){
                summaryRespVO.setOperateType(Integer.valueOf(info.get("operateType")));
            }
            if(StringUtil.isNotBlank(info.get("status"))){
                summaryRespVO.setResult(Integer.valueOf(info.get("status")));
            }
        }
        String copyTo = summaryDO.getCopyTo();
        if (Objects.nonNull(copyTo)) {
            List<Long> copy = Arrays.stream(copyTo.split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<AdminUserRespDTO> copyToUser = userApi.getUsers(copy).getCheckedData();
            summaryRespVO.setCopyToName(StringUtil.join(copyToUser.stream().map(AdminUserRespDTO::getNickname).toArray(), ","));
        }
        return summaryRespVO;
    }

    @Override
    public List<WorkScheduleDO> getPreviewData(List<Long> ids) {
        LambdaQueryWrapper<WorkScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkScheduleDO::getWeeklyWorkScheduleId, ids)
                .orderByAsc(WorkScheduleDO::getStartDate);
        List<WorkScheduleDO> workSchedules = workScheduleMapper.selectList(queryWrapper);
        //longSchedules:有startDate和endDate
        //shortSchedules:没有endDate
        List<WorkScheduleDO> longSchedules = new ArrayList<>();
        List<WorkScheduleDO> shortSchedules = new ArrayList<>();
        List<WorkScheduleDO> sortedSchedules = new ArrayList<>();
        for (WorkScheduleDO workSchedule : workSchedules) {
            String deptIds = workSchedule.getDeptIds();
            List<Long> deptIdList = Arrays.stream(deptIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Long> distinctDeptIds = deptIdList.stream().distinct().collect(Collectors.toList());
            List<DeptRespDTO> deptData = deptApi.getDepts(distinctDeptIds).getCheckedData();
            List<String> deptNames = deptData.stream().map(DeptRespDTO::getName).collect(Collectors.toList());
            workSchedule.setDeptNames(deptNames);
            if (Objects.nonNull(workSchedule.getEndDate())) {
                longSchedules.add(workSchedule);
            } else {
                shortSchedules.add(workSchedule);
            }
        }
        sortedSchedules.addAll(longSchedules);
        sortedSchedules.addAll(shortSchedules);

        return sortedSchedules;
    }

    public WeeklyWorkSummaryDO getByProcessInstanceId(String processInstanceId) {
        return baseMapper.selectOne(WeeklyWorkSummaryDO::getProcessInstanceId, processInstanceId);
    }

    @Override
    public SummaryScheduleRespVO getWeeklyScheduleList() {
        Long userId = getLoginUserId();
        Long tenantId = getTenantId();
        //是否有正在进行中的汇总单/驳回的汇总单
        Integer count = baseMapper.getCount(tenantId);
        Integer countReject = baseMapper.getCountReject(tenantId);
        Integer countCancel = baseMapper.getCountCancel(tenantId);
        //尚未进行汇总的填报单信息
        List<SummaryToDoRespVO> myToDo = baseMapper.getMyToDo(userId);
        //被驳回的填报单ids
        List<String> rejectedIds = baseMapper.getRejected(userId);
        //撤回的汇总单信息
        List<SummaryWithdrawRespVO> summaryWithdraws = baseMapper.getWithdraw(userId);
        //被驳回的填报单
        if (rejectedIds != null) {
            //更新对应填报单的状态信息
            for (String rejected : rejectedIds) {
                List<Long> ids = Arrays.stream(rejected.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                weeklyWorkScheduleService.BatchUpdateStatusByIds(ids, BpmProcessInstanceResultEnum.BACK.getResult());
            }
        }
        //获取被驳回的填报单
        List<SummaryToDoRespVO> back = baseMapper.getBack(userId);
        back.forEach(resp -> resp.setType("驳回"));

        myToDo.addAll(back);

        //获取撤回的填报单
        List<SummaryToDoRespVO> withdraw = baseMapper.getWithdrawSchedule();

        if (summaryWithdraws != null) {
            for (SummaryWithdrawRespVO withdraws: summaryWithdraws) {
                List<Long> ids = Arrays.stream(withdraws.getScheduleIds().split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
                for (SummaryToDoRespVO schedule : withdraw) {
                    if (ids.contains(schedule.getScheduleId())) {
                        schedule.setSummaryId(withdraws.getSummaryId());
                        schedule.setType("撤回");
                    }
                }
            }
        }
        myToDo.addAll(withdraw);
        myToDo = myToDo.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(SummaryToDoRespVO::getProcessInstanceId)
                                .thenComparing(SummaryToDoRespVO::getType, Comparator.nullsLast(String::compareTo)))), ArrayList::new));
        for (SummaryToDoRespVO summary : myToDo) {
            summary.setDeptName(deptApi.getDept(summary.getDeptId()).getCheckedData().getName());
            summary.setUserNickName(userApi.getUser(summary.getUserId()).getCheckedData().getNickname());
        }
        SummaryScheduleRespVO summaryScheduleRespVO = new SummaryScheduleRespVO();
        Collections.sort(myToDo, Comparator.comparing(SummaryToDoRespVO::getCreateTime).reversed());
        summaryScheduleRespVO.setSummary(myToDo);
        summaryScheduleRespVO.setCount(count);
        summaryScheduleRespVO.setCountReject(countReject);
        summaryScheduleRespVO.setCountCancel(countCancel);
        return summaryScheduleRespVO;
    }

    @Override
    public String getWorkScheduleId(String processInstanceId) {
        WeeklyWorkSummaryDO summaryDO = getByProcessInstanceId(processInstanceId);
        return summaryDO.getWeeklyWorkScheduleIds();
    }

    @Override
    public List<ScheduleRespVO> getDetail(String workScheduleIds) {
        List<Long> ids = new ArrayList<>(Arrays.stream(workScheduleIds.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        List<Long> distinctIds = ids.stream().distinct().collect(Collectors.toList());
        List<ScheduleRespVO> result = new ArrayList<>();
        for (Long id : distinctIds) {
            ScheduleRespVO respVO = weeklyWorkScheduleService.get(id, null);
            if(respVO.getStatus().equals(BpmProcessInstanceResultEnum.SUMMARIZE.getResult())) {
                respVO.setType(BpmProcessInstanceResultEnum.SUMMARIZE.getDesc());
            }
            if(respVO.getStatus().equals(BpmProcessInstanceResultEnum.BACK.getResult())) {
                respVO.setType(BpmProcessInstanceResultEnum.BACK.getDesc());
            }
            if(respVO.getStatus().equals(BpmProcessInstanceResultEnum.WITHDRAW.getResult())) {
                respVO.setType(BpmProcessInstanceResultEnum.WITHDRAW.getDesc());
            }
            result.add(respVO);

        }
        return result;
    }

    @Override
    public void deleteSummary(Integer id) {
        WeeklyWorkSummaryDO weeklyWorkSummary = baseMapper.selectById(id);
        if(weeklyWorkSummary != null) {
            String processInstanceId = weeklyWorkSummary.getProcessInstanceId();
            processInstanceApi.removeProcess(WebFrameworkUtils.getLoginUserId(), processInstanceId);
            baseMapper.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void end(SummaryCreateReqVO createReqVO) {
        //先发起流程
        String processInstanceId = start(createReqVO);
        //再直接结束流程
        OARemoveReqVO removeReqVO = new OARemoveReqVO();

        removeReqVO.setCategory("14");
        removeReqVO.setProcessInstanceId(processInstanceId);
        oaTaskService.end(removeReqVO);

        if(createReqVO.getIsCopy() != null && createReqVO.getIsCopy()) {
            String scheduleIds = weeklyWorkSummaryMapper.selectOne(WeeklyWorkSummaryDO::getProcessInstanceId, processInstanceId).getWeeklyWorkScheduleIds();
            SummaryDTO summaryDTO = WeeklyWorkSummaryConvert.INSTANCE.convertDTO(getByProcessInstanceId(processInstanceId));
            List<Long> userIds = new ArrayList<>();
            // 改变安排上报表的结果为已完成
            if(scheduleIds != null) {
                List<Long> ids = new ArrayList<>(Arrays.stream(scheduleIds.split(",")).map(Long::parseLong).collect(Collectors.toList()));
                for (Long scheduleId : ids) {
                    weeklyWorkScheduleService.updateStatusById(scheduleId, BpmProcessInstanceResultEnum.APPROVE.getResult());
                    //不通知时取消通知短信
                    if(createReqVO.getIsNotice() != null && !createReqVO.getIsNotice()) {
                        WeeklyWorkScheduleDO workScheduleDO = weeklyWorkScheduleService.getById(scheduleId);
                        weeklyWorkScheduleService.cancelMessage(workScheduleDO);
                    }
                }
                // 抄送给抄送人员
                String copyTo= weeklyWorkSummaryMapper.selectById(summaryDTO.getId()).getCopyTo();
                if(copyTo != null) {
                    List<Long> userId = Arrays.stream(copyTo.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    userIds.addAll(userId);
                }
                userIds = userIds.stream().distinct().collect(Collectors.toList());
                BpmProcessInstanceRespDTO processInstanceRespDTO = bpmProcessInstanceApi.getProcessInstanceInfo(processInstanceId).getCheckedData();
                ReceiveDTO receiveDTO = new ReceiveDTO();
                receiveDTO.setProcessInstanceId(processInstanceId);
                receiveDTO.setCategory(processInstanceRespDTO.getCategory());
                receiveDTO.setApplyTime(processInstanceRespDTO.getCreateTime());

                receiveDTO.setItemId(summaryDTO.getId());

                receiveDTO.setUserIds(userIds);
                receiveDTO.setPromoterUserId(processInstanceRespDTO.getStartUser().getId());
                if (CollUtil.isEmpty(receiveDTO.getUserIds())) {
                    return;
                }
                List<ReceiveDO> list = new ArrayList<>();
                receiveDTO.getUserIds().forEach(u -> {
                    ReceiveDO receiveDO = new ReceiveDO();
                    receiveDO.setCategory(receiveDTO.getCategory());
                    receiveDO.setItemId(receiveDTO.getItemId());
                    receiveDO.setApplyTime(receiveDTO.getApplyTime());
                    receiveDO.setUserId(u.toString());
                    receiveDO.setProcessInstanceId(receiveDTO.getProcessInstanceId());
                    receiveDO.setPromoterUserId(receiveDTO.getPromoterUserId().toString());
                    list.add(receiveDO);
                });
                receiveService.saveBatch(list);
                //发送短信
                Integer year = createReqVO.getYear();
                Integer week = createReqVO.getWeek();
                String message =  String.format("【湖南省委党校】%s年第%s周工作安排已汇总，请尽快登录湖南省党校系统一体化信息平台进行查阅。"
                        , year, week);
                List<AdminUserRespDTO> users  =  userApi.getUsers(userIds).getCheckedData();

                if(CollUtil.isNotEmpty(users)){

                    String mobile = users.stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                            .reduce( (k,v)-> k +"," + v  ).get();

                    Map<String,Object> map = new HashMap<>();
                    map.put("arg1", message);

                    smsSendApi.sendSingleSms(mobile, null,null ,"admin-sms-login-new",map);

                }
                //创建日程已经完成
                //id为pid
                //userIds是抄送人列表
                List<Long> personIds =  getUsersById(processInstanceId);
                // 合并两个列表并取并集（去重）
                List<Long> unionIds = Stream.of(userIds, personIds)
                        .flatMap(List::stream)
                        .distinct()
                        .collect(Collectors.toList());
                //添加一周报告汇总时间到日程
                Map<String, LocalDate> dateMap  = getDateById(processInstanceId);
                ScheduleDto scheduleDto = new ScheduleDto();
                //操作所有参与人和抄送人
                scheduleDto.setUserIds(unionIds);
                //设置发起人id这边不需要这个字段
                scheduleDto.setUserId(processInstanceRespDTO.getStartUser().getId());
                //设置发起时间dateMap.get("startMap")
                LocalDate startDate =dateMap.get("startDate");
                LocalDate endDate =dateMap.get("endDate");
                scheduleDto.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                scheduleDto.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                //传流程id赋值到
                scheduleDto.setProcessInstanceId(processInstanceId);
                //传类型传14为一周安排汇总
                scheduleDto.setType(14);
                scheduleDto.setWeek(week);
                scheduleDto.setYear(year);
                scheduleServiceApi.createSchedulesOther(scheduleDto);

//                //删除姓名
//                message =  String.format("假期变更通知\n您申请的假期变更已完成，假期开始时间：【%s】，假期结束时间：【%s】"
//                        ,  leaveStart, leaveEnd);
//
                message =  String.format("待阅提醒\n %s年第%s周工作安排已汇总，请尽快进入OA系统进行查阅",
                        year,week);
                //待办事项办添加
                //设置内容
                receiveDTO.setCategory(message);

                //参与人和抄送人
                receiveDTO.setUserIds(personIds);
                //流程id
                receiveDTO.setProcessInstanceId(processInstanceId);
                //
                //应该把发起人和时间加上,上面抄送已经加上

//        receiveApi.save(receiveDTO);
                receiveApiImpl.saveWorkSummary(receiveDTO);

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartEnd(SummaryCreateReqVO createReqVO) {
        //先发起流程
        restart(getLoginUserId(), createReqVO);
        //再直接结束流程

    }
    @Override
    public Map<String, LocalDate> getDateById(String processInstanceId) {
        Map<String,LocalDate> map = new HashMap<>();
        //weekly_work_schedule_ids一周工作安排ids
        String idSt = weeklyWorkSummaryMapper.getWeekIdsByPId(processInstanceId);
        List<Long> ids = new ArrayList<>();
        if (idSt != null && !idSt.isEmpty()) {
            String[] idParts = idSt.split(",");
            for (String idPart : idParts) {
                // 去除字符串两端的空白字符
                idPart = idPart.trim();

                // 检查idPart是否可以转换为Long
                if (!idPart.isEmpty()) {
                    try {
                        Long id = Long.parseLong(idPart);
                        ids.add(id);
                    } catch (NumberFormatException e) {
                        System.out.println(idParts);
                        // 如果转换失败，可以记录日志或进行其他错误处理
                        e.printStackTrace(); // 例如，打印堆栈跟踪
                    }
                }
            }
        }
        //根据ids查最早开始和最晚结束
        SummaryDateVO summaryDateVO = weeklyWorkSummaryMapper.getDateById(ids);
        if(summaryDateVO.getEndDate()==null){
            map.put("endDate",summaryDateVO.getLastStartDate().toLocalDate());
        }else {
            map.put("endDate",summaryDateVO.getEndDate().toLocalDate());
        }
        // map.put("id",outReportDO.getId().toString());
        map.put("startDate",summaryDateVO.getStartDate().toLocalDate());
        return map;
    }
    @Override
    public List<Long> getUsersById(String processInstanceId) {
        Map<String,LocalDate> map = new HashMap<>();
        //weekly_work_schedule_ids一周工作安排ids
        String idSt = weeklyWorkSummaryMapper.getWeekIdsByPId(processInstanceId);
        List<Long> ids = new ArrayList<>();
        if (idSt != null && !idSt.isEmpty()) {
            String[] idParts = idSt.split(",");
            for (String idPart : idParts) {
                // 去除字符串两端的空白字符
                idPart = idPart.trim();

                // 检查idPart是否可以转换为Long
                if (!idPart.isEmpty()) {
                    try {
                        Long id = Long.parseLong(idPart);
                        ids.add(id);
                    } catch (NumberFormatException e) {
                        System.out.println(idParts);
                        // 如果转换失败，可以记录日志或进行其他错误处理
                        e.printStackTrace(); // 例如，打印堆栈跟踪
                    }
                }
            }
        }
        //根据ids查所有参与人
        List<String> personIds = weeklyWorkSummaryMapper.getPersonnelIdsById(ids);
        // 使用Stream API筛选非空字符串
        personIds = personIds.stream()
                .filter(id -> id != null && !id.isEmpty()) // 过滤出非null且非空的字符串
                .collect(Collectors.toList()); // 收集结果到新的List中
        //返回的参与人列表
        // 使用Stream API分割ID字符串，去重，并转换为Long类型，收集到新的List中
        List<Long> persons = personIds.stream()
                .flatMap(idString -> Arrays.stream(idString.split(","))) // 分割每个字符串为ID数组
                .map(String::trim) // 去除ID两侧的空格（如果有的话）
                .filter(id -> !id.isEmpty()) // 过滤掉空字符串
                .map(Long::parseLong) // 将ID字符串转换为Long类型
                .distinct() // 去重
                .collect(Collectors.toList()); // 收集到新的List中
        return persons;
    }


    @Override
    public String getCopyTo(Long summaryId) {
        WeeklyWorkSummaryDO summaryDO = baseMapper.selectById(summaryId);
        return summaryDO.getCopyTo();
    }
}
