package com.unicom.swdx.module.oa.framework.rpc.config;

import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.system.api.common.CalendarWnlApi;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.message.MessageApi;
import com.unicom.swdx.module.system.api.oaNotice.OaNoticeApi;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.permission.RoleApi;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.sms.SmsQueApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.xcx.WxXcxApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { SmsQueApi.class, SmsSendApi.class, DictDataApi.class ,FileApi.class, BpmProcessInstanceApi.class,
        BpmTaskServiceApi.class, AdminUserApi.class, DeptApi.class, PostApi.class, CalendarWnlApi.class, WxXcxApi.class, PermissionApi.class,
        PermissionApi.class, OaNoticeApi.class, MessageApi.class, ScheduleServiceApi.class
})
public class RpcConfiguration {



}
