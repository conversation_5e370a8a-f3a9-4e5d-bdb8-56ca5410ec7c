package com.unicom.swdx.module.oa.controller.admin.oaLeave.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "OA办公 - 请假天数统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OALeaveStatisticPageReqVO extends PageParam {

    @ApiModelProperty(value = "是否个人视角")
    private Boolean isPersonal;

    @ApiModelProperty(value = "筛选开始时间", example = "2024-01-01")
    private String startTime;

    @ApiModelProperty(value = "筛选结束时间", example = "2024-01-01")
    private String endTime;

    @ApiModelProperty(value = "部门")
    private String deptName;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    @ApiModelProperty(value = "升序0 降序1")
    private String seq;

    //查看人为个人视角或者非部门负责人和人事处负责人的默认视角用到此参数
    @ApiModelProperty(value = "无需前端传入")
    private Long userId;

    //查看人为部门负责人需传入此参数
    @ApiModelProperty(value = "按部门搜索时需前端传入")
    private Long deptId;

    //机构隔离
    @ApiModelProperty(value = "无需前端传入")
    private Long tenantId;

    private List<Long> deptList;
}
