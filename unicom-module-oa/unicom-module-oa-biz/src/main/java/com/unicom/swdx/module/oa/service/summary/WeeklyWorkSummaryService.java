package com.unicom.swdx.module.oa.service.summary;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryScheduleRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryToDoRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface WeeklyWorkSummaryService {

    String createWorkSummaryProcess(SummaryCreateReqVO createReqVO);

    void restartSummary(Long loginUserId, SummaryCreateReqVO reqVO);

    SummaryRespVO get(Long id, String processInstanceId);

    List<WorkScheduleDO> getPreviewData(List<Long> ids);

    SummaryScheduleRespVO getWeeklyScheduleList();

    WeeklyWorkSummaryDO getByProcessInstanceId(String processInstanceId);

    String getWorkScheduleId(String id);

    List<ScheduleRespVO> getDetail(String workScheduleIds);

    void deleteSummary(Integer id);

    void end(SummaryCreateReqVO createReqVO);

    void restartEnd(SummaryCreateReqVO createReqVO);

    /**
     * 更新状态
     * @param processInstanceId
     * @param
     * @return
     */
    Map<String, LocalDate> getDateById(String processInstanceId);

    /**
     * 更新状态
     * @param processInstanceId
     * @param
     * @return
     */
    List<Long> getUsersById(String processInstanceId);

    String getCopyTo(Long scheduleId);
}
