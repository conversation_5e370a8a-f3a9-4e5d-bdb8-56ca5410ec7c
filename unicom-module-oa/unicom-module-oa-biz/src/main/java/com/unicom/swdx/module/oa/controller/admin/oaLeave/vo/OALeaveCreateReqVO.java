package com.unicom.swdx.module.oa.controller.admin.oaLeave.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.AssertTrue;
import java.util.List;

@ApiModel(description = "办公OA - 请假申请创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OALeaveCreateReqVO extends OALeaveBaseVO {

    @ApiModelProperty(value = "主键",notes = "当有草稿/重新发起时必传")
    private Long id;

    @ApiModelProperty(value = "重新发起时必传流程id")
    private String processInstanceId;

    @ApiModelProperty(value = "restart接口时必传是否草稿，saveDraft接口如果是草稿箱页面编辑则传true")
    private Boolean isDraft;

    @ApiModelProperty("选择的下一任务审批人列表")
    private List<Long> userIds;

    @ApiModelProperty("审批方式 1顺序 2会签")
    private String chargeLeaderSeq;

    @AssertTrue(message = "结束时间，需要在开始时间之后")
    public boolean isEndTimeValid() {
        return !getEndTime().isBefore(getStartTime());
    }

}