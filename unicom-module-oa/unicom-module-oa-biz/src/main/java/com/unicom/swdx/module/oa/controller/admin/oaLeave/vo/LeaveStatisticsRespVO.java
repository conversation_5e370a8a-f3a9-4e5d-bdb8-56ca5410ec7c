package com.unicom.swdx.module.oa.controller.admin.oaLeave.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("请假天数统计")
@Data
@ToString(callSuper = true)
public class LeaveStatisticsRespVO {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("姓名")
    private String nickname;

    @ApiModelProperty("部门id")
    private Long deptId;

    @ApiModelProperty("部门")
    private String deptName;

    @ApiModelProperty("请假总天数")
    private Integer total;

    @ApiModelProperty("婚假")
    private Integer marry;

    @ApiModelProperty("产假")
    private Integer birth;

    @ApiModelProperty("丧假")
    private Integer death;

    @ApiModelProperty("病假")
    private Integer ill;

    @ApiModelProperty("工伤")
    private Integer hurt;

    @ApiModelProperty("探亲")
    private Integer visit;

    @ApiModelProperty("事假")
    private Integer busy;

    @ApiModelProperty("其他")
    private Integer other;

    @ApiModelProperty("年休假")
    private Integer annual;

    @ApiModelProperty("护理假")
    private Integer nursing;

    @ApiModelProperty("育儿假")
    private Integer childcare;

    @ApiModelProperty("亲子假")
    private Integer parental;

    @ApiModelProperty("哺乳假")
    private Integer breastfeeding;

    @ApiModelProperty("独生子女父母护理假")
    private Integer onlychild;

    @ApiModelProperty("加班补修假")
    private Integer overtime;

}
