package com.unicom.swdx.module.oa.dal.kingbase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleDetailRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.SchedulePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleStatisticRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WeeklyWorkScheduleMapper extends BaseMapperX<WeeklyWorkScheduleDO> {
    List<ScheduleStatisticRespVO> selectStatisticsPage(IPage page, @Param("reqVO") SchedulePageReqVO reqVO);

    List<Long> getScheduleIdByInstanceId();

    List<ScheduleDetailRespVO> selectDetailPage(IPage<ScheduleDetailRespVO> myPage, @Param("reqVO") SchedulePageReqVO reqVO);

    Long getByProcessInstanceId(String processInstanceId);
}
