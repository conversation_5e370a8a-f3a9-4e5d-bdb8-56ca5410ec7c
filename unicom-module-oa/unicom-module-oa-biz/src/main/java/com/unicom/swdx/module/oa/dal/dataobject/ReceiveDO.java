package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("oa_receive")
@KeySequence("oa_receive_seq")
public class ReceiveDO extends TenantBaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程分类
     */
    @TableField(value = "process_category")
    private String category;

    /**
     * 抄送接收人
     */
    @TableField(value = "copy_user_id")
    private String userId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 事项id
     */
    private Long itemId;

    /**
     * 是否已查看
     */
    private Boolean isRead;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 流程发起人
     */
    @TableField(value = "launch_user_id")
    private String promoterUserId;

    /**
     * 待办内容
     */
    private String content;

    /**
     * 流程是否已结束
     */
    @ApiModelProperty("流程是否已结束")
    @TableField(exist = false)
    private Boolean isEnd;


}
