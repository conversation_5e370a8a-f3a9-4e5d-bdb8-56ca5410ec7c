package com.unicom.swdx.module.oa.task;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.util.CellRangeAddressList;

/**
 * 自定义拦截器.对第一列第一行和第二行的数据新增下拉框，显示 测试1 测试2
 *
 * <AUTHOR>
 */

public class CustomSheetHandler implements SheetWriteHandler {

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {


        // 区间设置 第一列第一行和第二行的数据。由于第一行是头，所以第一、二行的数据实际上是第二三行
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 10, 2, 2);
        DataValidationHelper helper = context.getWriteSheetHolder().getSheet().getDataValidationHelper();
//        DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[] {"测试1", "测试2"});
/**
 *只在这里做了改动 创建数字约束
 helper.createNumericConstraint(p1,p2,p3,p4)
 p1:验证类型 ANY(任意类型)、INTEGER、DECIMAL...
 p2:符类型（大于、小于、不等于、区间...）
 p3:@param expr1日期公式(当第一个字符为'='时)或格式化数字值
 p4:@param expr2日期公式(当第一个字符为'='时)或格式化数字值
 */

        DataValidationConstraint numericConstraint =
                helper.createNumericConstraint(
                        DataValidationConstraint.ValidationType.DECIMAL,
                        DataValidationConstraint.OperatorType.BETWEEN,
                        "1",
                        "100");
        //对单元格数据进行验证
        DataValidation dataValidation = helper.createValidation(numericConstraint, cellRangeAddressList);
        //创建提示框 这里创建的是错误提示框
        dataValidation.createErrorBox("类型有误","请填写1-100之间的数据");
        dataValidation.setShowErrorBox(true);
        context.getWriteSheetHolder().getSheet().addValidationData(dataValidation);
    }
}

