package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.util.List;

/**
 * 寒暑假排班表
 * <AUTHOR>
 */
@TableName(value = "oa_vacation_duty",autoResultMap = true)
@Data
@KeySequence("oa_vacation_duty_id_seq")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VacationDutyDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 填报人的用户编号
     * <p>
     * 关联 AdminUserDO 的 id 属性
     */
    private Long userId;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 填报人的部门编号
     *
     * 关联 AdminUserDO 的 deptId 属性
     */
    private Long deptId;

    /**
     * 寒暑假坐值班人员表单
     */
    @TableField(exist = false)
    private List<VacationDutyFormDO> vacationDutyFormList;


    /**
     * 是否草稿
     */
    private Boolean isDraft;


    /**
     * 抄送人员
     */
    private String copyTo;

    /**
     * 汇总状态
     */
    private Integer status;
}
