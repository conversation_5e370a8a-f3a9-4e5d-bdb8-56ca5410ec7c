package com.unicom.swdx.module.oa.controller.admin.vo.summary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("办公OA - 一周工作汇总 Response VO")
public class SummaryToDoRespVO {
    private String taskId;

    private String processInstanceId;

    private Long deptId;

    private Long userId;

    @ApiModelProperty("填报人姓名")
    private String userNickName;

    @ApiModelProperty("填报处室名")
    private String deptName;

    private LocalDateTime createTime;

    private Long scheduleId;

    private String type;

    private Long summaryId;
}
