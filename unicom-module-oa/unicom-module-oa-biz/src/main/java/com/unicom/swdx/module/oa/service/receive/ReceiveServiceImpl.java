package com.unicom.swdx.module.oa.service.receive;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceRespDTO;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceivePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.receive.ReceiveToUsersReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.ReceiveDO;
import com.unicom.swdx.module.oa.dal.kingbase.ReceiveMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.PARAMETER_ERROR;

@Service
public class ReceiveServiceImpl extends ServiceImpl<ReceiveMapper, ReceiveDO> implements ReceiveService {

    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;

    @Override
    public PageResult<ReceiveDO> page(ReceivePageReqVO reqVO) {
        LocalDateTime[] applyDate = null;
        if (Objects.nonNull(reqVO.getApplyDate())) {
            // 申请时间转localdatetime
            applyDate = new LocalDateTime[2];
            applyDate[0] = reqVO.getApplyDate()[0].atStartOfDay();
            applyDate[1] = reqVO.getApplyDate()[1].atTime(23,59,59);
        }

        PageResult<ReceiveDO> receiveDOPageResult = baseMapper.selectPage(reqVO, new LambdaQueryWrapperX<ReceiveDO>()
                .eq(ReceiveDO::getUserId, getLoginUserId())
                .eqStrIfPresent(ReceiveDO::getCategory, reqVO.getCategory())
                .betweenIfPresent(ReceiveDO::getApplyTime, applyDate)
                .eqIfPresent(ReceiveDO::getIsRead, reqVO.getIsRead())
                .orderByAsc(ReceiveDO::getIsRead)
                .orderByDesc(ReceiveDO::getApplyTime)
        );
        List<ReceiveDO> list = receiveDOPageResult.getList();
        for (ReceiveDO receive : list) {
            BpmProcessInstanceRespDTO data = bpmProcessInstanceApi.getProcessInstanceInfo(receive.getProcessInstanceId()).getCheckedData();
            if(data.getStatus() == 1) {
                receive.setIsEnd(false);
            } else {
                receive.setIsEnd(true);
            }
        }


        return new PageResult<>(list, receiveDOPageResult.getTotal());
    }

    @Override
    public Long getNoReadNum() {
        return baseMapper.selectCount(new LambdaQueryWrapperX<ReceiveDO>()
                .eq(ReceiveDO::getUserId, getLoginUserId())
                .eq(ReceiveDO::getIsRead, Boolean.FALSE)
        );
    }

    @Override
    public void changeReadStatus(Long id) {
        ReceiveDO receiveDO = new ReceiveDO();
        receiveDO.setId(id);
        receiveDO.setIsRead(true);
        this.updateById(receiveDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toUsers(ReceiveToUsersReqVO reqVO) {
        ReceiveDO receive = new ReceiveDO();
        List<ReceiveDO> inserts = new ArrayList<>();

        if(Objects.nonNull(reqVO.getId())){
            // 我收到的页面抄送给其他人
            ReceiveDO receiveDO = this.getById(reqVO.getId());
            receive.setItemId(receiveDO.getItemId());
            receive.setCategory(receiveDO.getCategory());
            receive.setApplyTime(receiveDO.getApplyTime());
            receive.setPromoterUserId(receiveDO.getPromoterUserId());
            receive.setProcessInstanceId(receiveDO.getProcessInstanceId());
            receive.setIsRead(false);
        }else {
            // 非我收到的页面的抄送
            if(Objects.nonNull(reqVO.getProcessInstanceId())){
                BpmProcessInstanceRespDTO procInsDTO = bpmProcessInstanceApi.getProcessInstanceInfo(reqVO.getProcessInstanceId()).getCheckedData();
                receive.setItemId(Long.parseLong(procInsDTO.getBusinessKey()));
                receive.setCategory(procInsDTO.getCategory());
                receive.setApplyTime(procInsDTO.getCreateTime());
                receive.setPromoterUserId(procInsDTO.getStartUser().getId().toString());
                receive.setProcessInstanceId(reqVO.getProcessInstanceId());
                receive.setIsRead(false);
            }else {
                throw exception(PARAMETER_ERROR);
            }
        }
        for (Long userId : reqVO.getUserIds()) {
            ReceiveDO receiveDO = new ReceiveDO();
            BeanUtil.copyProperties(receive, receiveDO);
            receiveDO.setUserId(userId.toString());
            inserts.add(receiveDO);
        }
        this.saveBatch(inserts);
    }

    @Override
    @TenantIgnore
    public List<Long> getReceivedUserIds(String processInstanceId) {
        List<ReceiveDO> receiveDOS = baseMapper.selectList(ReceiveDO::getProcessInstanceId, processInstanceId);
        return receiveDOS.stream().map(r -> Long.parseLong(r.getUserId())).collect(Collectors.toList());
    }
}
