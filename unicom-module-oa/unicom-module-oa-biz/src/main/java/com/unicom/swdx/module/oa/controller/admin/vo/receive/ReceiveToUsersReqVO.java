package com.unicom.swdx.module.oa.controller.admin.vo.receive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("OA - 抄送给其他人 Request VO")
public class ReceiveToUsersReqVO {

    @ApiModelProperty("抄送记录主键id")
    private Long id;

    @ApiModelProperty("需要抄送的流程实例id")
    private String processInstanceId;

    @ApiModelProperty("抄送给其他人的用户id")
    private List<Long> userIds;

}
