package com.unicom.swdx.module.oa.controller.admin.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("办公OA - 审批中心 下一任务审批人 Request VO")
public class OApickApprovalsReqVO {
    @ApiModelProperty("当前任务Id")
    private String taskId;
    @ApiModelProperty("选择的下一任务审批人列表")
    private List<Long> userIds;
    @ApiModelProperty("审批方式 1顺序 2会签")
    private String chargeLeaderSeq;
}
