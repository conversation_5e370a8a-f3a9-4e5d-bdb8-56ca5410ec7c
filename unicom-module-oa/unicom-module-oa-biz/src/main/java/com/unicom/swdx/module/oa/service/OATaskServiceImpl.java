package com.unicom.swdx.module.oa.service;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskApproveReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskExtDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskRespDTO;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import com.unicom.swdx.module.oa.enums.Consts;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.oa.service.summary.WeeklyWorkSummaryService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.APPROVALS_IS_NULL;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.FLOW_CONF_ERROR;

@Service
public class OATaskServiceImpl implements OATaskService{
    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;
    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;
    @Resource
    private AdminUserApi userApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private WeeklyWorkSummaryService weeklyWorkSummaryService;
    @Resource
    private WeeklyWorkScheduleService weeklyWorkScheduleService;

    @Override
    public String getTaskId(String processInstanceId) {
        List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
        List<BpmTaskRespDTO> list = bpmTasks.stream().filter(t -> t.getResult() == 1).collect(Collectors.toList());
        if(list.isEmpty()){
            return null;
        } else if (list.size()==1) {
            return list.get(0).getId();
        } else {
            return list.stream().filter(t -> Objects.equals(t.getAssigneeUser().getId(),getLoginUserId())).findFirst().orElse(new BpmTaskRespDTO()).getId();
        }
    }

    @Override
    public void pickApprovals(OApickApprovalsReqVO pickApprovalsReqVO, LocalDateTime time) {
        //设置下一任务节点的处理人
        BpmTaskApproveReqDTO bpmTaskApproveReqDTO = new BpmTaskApproveReqDTO();
        bpmTaskApproveReqDTO.setId(pickApprovalsReqVO.getTaskId());
        bpmTaskApproveReqDTO.setTaskType(1);

        Map<String, Object> variables = new HashMap<>();
        variables.put("flag", Consts.ONE); // 多实例标识
        List<Long> approvals = pickApprovalsReqVO.getUserIds();
        if(CollUtil.isEmpty(approvals)){
            throw exception(APPROVALS_IS_NULL);
        }
        variables.put("approvals",approvals);
        variables.put("chargeLeaderSeq",pickApprovalsReqVO.getChargeLeaderSeq());
        if(approvals.size()>1){
            if(Objects.equals(pickApprovalsReqVO.getChargeLeaderSeq(),"1")){
                //顺序
                variables.put("completed",0);
            } else if (Objects.equals(pickApprovalsReqVO.getChargeLeaderSeq(),"2")) {
                //会签
                List<String> unApproved = new ArrayList<>();
                approvals.forEach(u->{
                    unApproved.add(u.toString());
                });
                variables.put("unapproved",unApproved);
                //多人会签任务
                variables.put("taskType","多人会签");
            }
        }else {
            variables.put("completed",null);
        }

        bpmTaskApproveReqDTO.setVariables(variables);

        Map<String, Object> paramsMap = new HashMap<>();
        StringBuilder approvalNames = new StringBuilder();
        approvals.forEach(userId->{
            approvalNames.append(userApi.getUser(userId).getCheckedData().getNickname()).append("，");
        });
        paramsMap.put("approvals",approvalNames.substring(0,approvalNames.length()-1));
        paramsMap.put("approveWay",pickApprovalsReqVO.getChargeLeaderSeq());
        paramsMap.put("nextTaskName",bpmTaskServiceApi.getNextTaskName(pickApprovalsReqVO.getTaskId()));
        bpmTaskApproveReqDTO.setParamsMap(paramsMap);

        if(Objects.nonNull(time)){
            bpmTaskApproveReqDTO.setEndTime(time);
        }

        //设置选择审批人这一任务为通过
        try {
            bpmTaskServiceApi.approveTask(getLoginUserId(),bpmTaskApproveReqDTO).getCheckedData(); //先推进流程引擎
        }catch (Exception e){
            throw exception(FLOW_CONF_ERROR);
        }
    }

    @Override
    public void end(OARemoveReqVO reqVO) {
        Long userId = getLoginUserId();
        // 修改一周工作安排填报单状态为手动结束
        if(Objects.equals(OACategoryConstants.SUMMARY, reqVO.getCategory())) {
            WeeklyWorkSummaryDO summary = weeklyWorkSummaryService.getByProcessInstanceId(reqVO.getProcessInstanceId());
            String scheduleIds = summary.getWeeklyWorkScheduleIds();
            List<Long> ids = Arrays.stream(scheduleIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
            weeklyWorkScheduleService.BatchUpdateStatusByIds(ids, BpmProcessInstanceResultEnum.FINISH.getResult());
        }
        String itemId = bpmProcessInstanceApi.endProcess(userId,reqVO.getProcessInstanceId()).getCheckedData();
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("结束");
        bpmTaskExtDTO.setAssigneeUserId(userId);
        bpmTaskExtDTO.setTaskDefKey("end");
        bpmTaskExtDTO.setTaskId("end");
        bpmTaskExtDTO.setResult(2);
        bpmTaskExtDTO.setProcessInstanceId(reqVO.getProcessInstanceId());
        bpmTaskExtDTO.setProcessDefinitionId(reqVO.getProcessInstanceId());
        LocalDateTime now = LocalDateTime.now();
        bpmTaskExtDTO.setCreateTime(now);
        bpmTaskExtDTO.setEndTime(now);
        bpmTaskExtDTO.setTaskType(6);
        bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);
    }

    @Override
    public String getDeptLeaderName(Long userId){
        AdminUserRespDTO userDTO = userApi.getUser(userId).getCheckedData();
        Long leaderUserId = deptApi.getDept(userDTO.getDeptId()).getCheckedData().getLeaderUserId();
        if(Objects.nonNull(leaderUserId)){
            return userApi.getUser(leaderUserId).getCheckedData().getNickname();
        }
        return "";
    }

    public String getDeptLeader(Long deptId){
        Long leaderUserId = deptApi.getDept(deptId).getCheckedData().getLeaderUserId();
        if(Objects.nonNull(leaderUserId)){
            return userApi.getUser(leaderUserId).getCheckedData().getNickname();
        }
        return "";
    }

}
