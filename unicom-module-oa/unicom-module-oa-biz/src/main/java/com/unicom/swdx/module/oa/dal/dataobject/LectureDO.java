package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 外出讲学审批 DO
 */
@TableName(value = "oa_lecture", autoResultMap = true)
@KeySequence("oa_lecture_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LectureDO extends TenantBaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 用户id
     */
    @TableField(value = "launch_user_id")
    private Long userId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 专题名称
     */
    private String subjectName;

    /**
     * 邀请单位
     */
    private String inviteUnit;

    /**
     * 讲学地点
     */
    private String lectureAddress;

    /**
     * 听课群众
     */
    private String audience;

    /**
     * 课时量
     */
    private String classPeriod;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 时长（天）
     */
    @TableField(value = "day")
    private Integer duration;

    /**
     * 授课内容
     */
    private String lectureContent;

    /**
     * 文件
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> files;

    /**
     * 签字
     */
    @TableField(value = "sign_image")
    private String sign;

    /**
     * 是否草稿
     */
    private Boolean isDraft;

    /**
     * 发起时间
     */
    private LocalDateTime launchTime;

}
