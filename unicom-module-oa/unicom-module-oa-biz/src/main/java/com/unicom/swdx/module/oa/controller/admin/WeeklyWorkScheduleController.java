package com.unicom.swdx.module.oa.controller.admin;

import cn.hutool.core.date.DateUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeavePageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.*;
import com.unicom.swdx.module.oa.convert.WeeklyWorkScheduleConvert;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.DEPT_NOT_LEADER;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.DEPT_TIME_ERROR;

/**
 * 一周工作安排
 */
@Api(tags = "办公OA - 一周工作安排")
@RestController
@Slf4j
@RequestMapping("/oa/workSchedule")
public class WeeklyWorkScheduleController {
    @Resource
    private WeeklyWorkScheduleService weeklyWorkScheduleService;


    @PostMapping("/saveDraft")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @ApiOperation("一周工作安排保存草稿")
    public CommonResult<Long> saveDraft(@Valid @RequestBody ScheduleCreateReqVO createReqVO) {
        return success(weeklyWorkScheduleService.saveDraft(createReqVO));
    }

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @ApiOperation("发起一周工作安排审批申请")
    public CommonResult<String> createWorkScheduleProcess(@Valid @RequestBody ScheduleCreateReqVO createReqVO) {

        for (int i = 0; i < createReqVO.getWorkSchedules().size(); i++) {
            WorkScheduleDO workScheduleDO = createReqVO.getWorkSchedules().get(i);
            try {
                if(DateUtil.compare(DateUtil.parseDate(DateUtil.formatLocalDateTime(workScheduleDO.getStartDate())) ,
                        DateUtil.parseDate(DateUtil.formatLocalDateTime(workScheduleDO.getEndDate())))>0){
                    throw  exception(DEPT_TIME_ERROR);
                }
            } catch (Exception e) {
                log.info("没有选择endDate");
            }


        }


        return success(weeklyWorkScheduleService.createWorkScheduleProcess(createReqVO));
    }

    @PostMapping("/restart")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @ApiOperation("驳回或撤销后重新编辑再发起流程")
    public CommonResult<Boolean> restartSchedule(@RequestBody ScheduleCreateReqVO reqVO) {
        weeklyWorkScheduleService.restartSchedule(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/edit")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @ApiOperation("重新编辑一周工作安排")
    public CommonResult<Boolean> editSchedule(@RequestBody ScheduleCreateReqVO reqVO) {
        weeklyWorkScheduleService.editSchedule(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @ApiOperation("获得一周工作安排审批申请")
    public CommonResult<ScheduleRespVO> getWorkSchedule(@RequestParam(value = "id",required = false) Long id,
                                                        @RequestParam(value = "processInstanceId",required = false) String processInstanceId) {
        ScheduleRespVO respVO = weeklyWorkScheduleService.get(id, processInstanceId);
        return success(respVO);
    }

    @PostMapping("/getStatistics")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:statistics')")
    @ApiOperation("获得一周工作安排统计")
    public CommonResult<PageResult<ScheduleStatisticRespVO>> statics(@RequestBody SchedulePageReqVO reqVO) {
        PageResult<ScheduleStatisticRespVO> resp = weeklyWorkScheduleService.getScheduleStatisticsList(reqVO);
        return success(resp);
    }

    @PostMapping("/getStatisticsDeatil")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:statistics')")
    @ApiOperation("获得一周工作安排统计详情")
    public CommonResult<PageResult<ScheduleDetailRespVO>> staticsDetail(@RequestBody SchedulePageReqVO reqVO) {
        PageResult<ScheduleDetailRespVO> resp = weeklyWorkScheduleService.getScheduleStatisticsDetail(reqVO);
        return success(resp);
    }

    @GetMapping("/getScheduleDetailById")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @ApiOperation("根据id获得工作安排详情")
    public CommonResult<WorkScheduleDO> getById(@RequestParam(value = "id",required = true) Long id) {
        WorkScheduleDO resp = weeklyWorkScheduleService.getScheduleDetailById(id);
        return success(resp);
    }

    @GetMapping("/getPersonnel")
    @PreAuthorize("@ss.hasPermission('oa:workSchedule:create')")
    @ApiOperation("获得工作安排人员详情")
    public CommonResult<PageResult<SchedulePersonnelRespVO>> getPersonnel(@RequestParam(value = "id",required = false) Long id,
                                                                 @RequestParam(value = "pageSize") Integer pageSize,
                                                                          @RequestParam(value = "pageNo") Integer pageNo) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(pageNo);
        pageParam.setPageSize(pageSize);
        PageResult<SchedulePersonnelRespVO> respVO = weeklyWorkScheduleService.getPersonnel(id, pageParam);
        return success(respVO);
    }

    @PostMapping("/delete")
    //@PreAuthorize("@ss.hasPermission('oa:workSchedule:delete')")
    @ApiOperation("删除工作安排填报单")
    public CommonResult<Boolean> deleteWorkSchedule(@RequestParam("id") Integer id) {
        weeklyWorkScheduleService.deleteSchedule(id);
        return success(true);
    }
}
