package com.unicom.swdx.module.oa.controller.admin.vo.lecture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
@ApiModel("OA - 外出讲学审批信息 Request VO")
public class LectureApproveReqVO {

    @ApiModelProperty(value = "任务编号", required = true, example = "1024")
    @NotBlank(message = "任务编号不能为空")
    private String id;

    @ApiModelProperty(value = "审批意见", example = "不错不错！")
    private String reason;

    /**
     * 审批过程中参数信息
     */
    @ApiModelProperty(value = "审批参数")
    private Map<String, Object> variables;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

}
