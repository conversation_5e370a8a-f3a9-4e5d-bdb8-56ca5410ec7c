package com.unicom.swdx.module.oa.controller.admin.vo.schedule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("周工作安排统计")
@Data
@ToString(callSuper = true)
public class ScheduleStatisticRespVO {

    @ApiModelProperty("部门id")
    private Long deptId;

    @ApiModelProperty("部门")
    private String deptName;

    @ApiModelProperty("上报周工作安排总数")
    private Integer total;

    @ApiModelProperty("校内安排数")
    private Integer schoolSchedule;

    @ApiModelProperty("对外培训数")
    private Integer externalTrain;
}
