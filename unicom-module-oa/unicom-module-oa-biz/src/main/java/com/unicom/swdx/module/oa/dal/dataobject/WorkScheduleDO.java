package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 工作安排
 */
@TableName(value = "oa_work_schedule",autoResultMap = true)
@Data
@KeySequence("oa_work_schedule_id_seq")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkScheduleDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDate;

    /**
     * 对应的一周工作安排id
     */
    private Long weeklyWorkScheduleId;

    /**
     * 对应的一周工作安排的processInstanceId
     */
    @TableField(exist = false)
    private String processInstanceId;

    /**
     * 地点
     */
    private String location;

    /**
     * 活动
     */
    private String activity;

    /**
     * 填报事项
     */
    @TableField(value = "fill_item")
    private String filledIn;

    /**
     * 参加人员
     */
    private String enrolledPersonnel;

    /**
     * 选择的参加人员
     */
    private String personnelIds;

    /**
     * 承办处室
     */
    private String deptIds;

    /**
     * 承办处室名称
     */
    @TableField(exist = false)
    private List<String> deptNames;

    /**
     * 参加人员名称
     */
    @TableField(exist = false)
    private String personnelName;

    /**
     * 参加人员id和名称对应
     */
    @TableField(exist = false)
    private Map<Long, String> personnel;

    /**
     * 移动端承办处室名称
     */
    @TableField(exist = false)
    private String deptNameString;

    /**
     * 移动端活动名称
     */
    @TableField(exist = false)
    private String mobileActivity;

    /**
     * 是否通知
     */
    private boolean notify;

    /**
     * 通知时间
     */
    private String notifyTime;
}
