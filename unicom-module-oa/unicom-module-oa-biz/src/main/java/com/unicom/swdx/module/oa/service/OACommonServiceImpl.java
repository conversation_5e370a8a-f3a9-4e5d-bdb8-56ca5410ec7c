package com.unicom.swdx.module.oa.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import com.unicom.swdx.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OAApproveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.enums.Consts;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.oa.service.leave.OALeaveService;
import com.unicom.swdx.module.oa.service.lecture.LectureService;
import com.unicom.swdx.module.oa.service.outReport.OutReportService;
import com.unicom.swdx.module.oa.service.receive.ReceiveService;
import com.unicom.swdx.module.oa.service.schedule.WeeklyWorkScheduleService;
import com.unicom.swdx.module.oa.service.summary.WeeklyWorkSummaryService;
import com.unicom.swdx.module.oa.service.vacationDuty.VacationDutyService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;

@Service
@Validated
public class OACommonServiceImpl implements OACommonService{

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private SmsSendApi smsSendApi;

    @Resource
    private OALeaveService leaveService;

    @Resource
    private LectureService lectureService;

    @Resource
    private OutReportService outReportService;

    @Resource
    private DraftService draftService;

    @Resource
    private WeeklyWorkScheduleService weeklyWorkScheduleService;

    @Resource
    private WeeklyWorkSummaryService weeklyWorkSummaryService;

    @Resource
    private LectureMapper lectureMapper;

    @Resource
    private VacationDutyService dutyService;

    @Resource
    private ReceiveService receiveService;

    @Resource
    private OATaskService oaTaskService;

    @Override
    public Map<String, Object> getNextTaskName(Long userId, String category, Long deptId) {
        String nextTaskName = "";
        String nextApproval = "";
        Boolean select = false;

        if(StrUtil.isEmpty(category)){
            return null;
        }

        if(Objects.equals(category,OACategoryConstants.LEAVE)){
//            calculateLeaveDay(reqVO.getStartTime().toLocalDate(), reqVO.getEndTime().toLocalDate());
            Integer postType = leaveService.getPostType(userId);
            if(postType==1 || postType==2){
                nextTaskName = "部门负责人审批";
                if (deptId == null) {
                    nextApproval = getDeptLeaderName(userId);
                } else {
                    Long leaderUserId = deptApi.getDept(deptId).getCheckedData().getLeaderUserId();
                    if (leaderUserId == null) {
                        throw exception(APPROVAL_NOT_FOUND);
                    }
                    nextApproval = userApi.getUser(leaderUserId).getCheckedData().getNickname();
                }
            } else if (postType == 3){
                nextTaskName = "分管校（院）领导审批";
                nextApproval = "";
                select = true;
            } else if (postType == 4) {
                nextTaskName = "常务副校（院）长审批";
                nextApproval = "";
                select = true;
            }
        } else if (Objects.equals(category, OACategoryConstants.LECTURE)) {
            Integer postType = lectureService.getPostType(userId);
            if(postType==1){
                nextTaskName = "无需审批";
                nextApproval = "";
            } else if(postType==2){
                nextTaskName = "分管日常工作的副校长（副院长）审批";
                nextApproval = "";
                select = true;
            } else if(postType==3){
                nextTaskName = "分管校（院）领导审批";
                nextApproval = "";
                select = true;
            } else {
                nextTaskName = "部门负责人审批";
                if (deptId == null) {
                    nextApproval = getDeptLeaderName(userId);
                } else  {
                    Long leaderUserId = deptApi.getDept(deptId).getCheckedData().getLeaderUserId();
                    if (leaderUserId == null) {
                        throw exception(APPROVAL_NOT_FOUND);
                    }
                    nextApproval = userApi.getUser(leaderUserId).getCheckedData().getNickname();
                }
            }
        } else if (Objects.equals(category, OACategoryConstants.OUTREPORT)) {
            Integer postType = outReportService.getPostType(userId);
            if (postType==1){
                nextTaskName = "分管日常工作的副校长（副院长）审批";
                nextApproval = "";
                select = true;
            } else if(postType==2){
                nextTaskName = "分管校（院）领导审批";
                nextApproval = "";
                select = true;
            } else if(postType==3){
                // 副主任外出
                nextTaskName = "部门主要负责人审批";
                if (deptId == null) {
                    nextApproval = getDeptLeaderName(userId);
                } else  {
                    Long leaderUserId = deptApi.getDept(deptId).getCheckedData().getLeaderUserId();
                    if (leaderUserId == null) {
                        throw exception(APPROVAL_NOT_FOUND);
                    }
                    nextApproval = userApi.getUser(leaderUserId).getCheckedData().getNickname();
                }
            } else {
                nextTaskName = "部门主要负责人审批";
                nextApproval = "";
                select = true;
            }
        } else if (Objects.equals(category, OACategoryConstants.SCHEDULE)) {
            nextTaskName = "部门负责人审批";
            if (deptId == null) {
                nextApproval = getDeptLeaderName(userId);
            } else  {
                Long leaderUserId = deptApi.getDept(deptId).getCheckedData().getLeaderUserId();
                if (leaderUserId == null) {
                    throw exception(APPROVAL_NOT_FOUND);
                }
                nextApproval = userApi.getUser(leaderUserId).getCheckedData().getNickname();
            }
        } else if (Objects.equals(category, OACategoryConstants.SUMMARY)) {
            nextTaskName = "分管副主任审批";
            nextApproval = "";
            select = true;
        } else if (Objects.equals(category, OACategoryConstants.DUTY)) {
            Integer postType = dutyService.getPostType(userId);
            nextApproval = getDeptLeaderName(userId);
            if(postType==1 || postType==3){
                nextTaskName = "部门负责人审批";
                if (deptId == null) {
                    nextApproval = getDeptLeaderName(userId);
                } else  {
                    Long leaderUserId = deptApi.getDept(deptId).getCheckedData().getLeaderUserId();
                    if (leaderUserId == null) {
                        throw exception(APPROVAL_NOT_FOUND);
                    }
                    nextApproval = userApi.getUser(leaderUserId).getCheckedData().getNickname();
                }
                select = false;
            } else if (postType == 4){
                nextTaskName = "分管校（院）领导审批";
//                nextApproval = "";
//                select = true;
            } else if (postType == 5) {
                nextTaskName = "秘书科汇总";
//                nextApproval = "";
//                select = true;
            }
            select = false;
        }

        if(!select){
            if(!Objects.equals("无需审批", nextTaskName) && StrUtil.isEmpty(nextApproval)){
                throw exception(APPROVAL_NOT_FOUND);
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("nextTaskName",nextTaskName);
        map.put("nextApproval",nextApproval);
        map.put("needSelect",select);

        return map;
    }

    @Override
    public Map<String, Object> getNextTaskInfo(String category, String name, Long day, String processInstanceId) {
        String nextTaskName = "";
        String nextApproval = "";
        Boolean select = false;
        Map<String, Object> map = new HashMap<>();

        if(StrUtil.isEmpty(category)){
            return null;
        }

        Long startUserId = bpmProcessInstanceApi.getStartUserId(processInstanceId).getCheckedData();


        if(Objects.equals(category,OACategoryConstants.LEAVE)){
            Integer postType = leaveService.getPostType(startUserId);
            if(Objects.equals(name, "常务副校（院）长审批")) {
                map.put("nextTaskName","结束");
                return map;
            }
            if(postType == 1) {
                if(Objects.equals(name, "部门负责人审批") && day > 5) {
                    nextTaskName = "分管校（院）领导审批";
                    select = true;
                }
                if(Objects.equals(name, "部门负责人审批") && day <= 5) {
                    map.put("nextTaskName","结束");
                    return map;
                }
                if(Objects.equals(name, "分管校（院）领导审批")) {
                    map.put("nextTaskName","结束");
                    return map;
                }
            } else if (postType == 2){
                if(Objects.equals(name, "部门负责人审批") && day > 3) {
                    nextTaskName = "分管校（院）领导审批";
                    select = true;
                }
                if(Objects.equals(name, "部门负责人审批") && day <= 3) {
                    map.put("nextTaskName","结束");
                    return map;
                }
                if(Objects.equals(name, "分管校（院）领导审批") && day > 5) {
                    nextTaskName = "常务副校（院）长审批";
                    select = true;
                }
                if(Objects.equals(name, "分管校（院）领导审批") && day > 3 && day <= 5) {
                    map.put("nextTaskName","结束");
                    return map;
                }

            } else if (postType == 3) {
                if(Objects.equals(name, "分管校（院）领导审批") && day > 1) {
                    nextTaskName = "常务副校（院）长审批";
                    select = true;
                } else {
                    map.put("nextTaskName","结束");
                    return map;
                }
            }
            if(day > 30 && Objects.equals(name, "校（院）委会议审批")) {
                map.put("nextTaskName","结束");
                return map;
            }
        } else if (Objects.equals(category, OACategoryConstants.LECTURE)) {
            Integer postType = lectureService.getPostType(startUserId);
            if(Objects.equals(name, "分管校（院）领导审批")) {
                nextTaskName = "分管日常工作的副校长（副院长）审批";
                select = true;
            } else {
                nextTaskName = "教务部审批";
                AdminUserRespDTO startUser = userApi.getUser(startUserId).getCheckedData();
                Long educationDeptId = deptApi.getDeptByTenantAndName(startUser.getTenantId(), "教务部").getCheckedData().getLeaderUserId();
                nextApproval = userApi.getUser(educationDeptId).getCheckedData().getNickname();
            }
            if (Objects.equals(name, "教务部审批") || (Objects.equals(name, "分管日常工作的副校长（副院长）审批") && postType == 2)) {
                map.put("nextTaskName","结束");
                return map;
            }
        } else if (Objects.equals(category, OACategoryConstants.OUTREPORT)) {
            Integer postType = outReportService.getPostType(startUserId);
            if (postType==2){
                if(Objects.equals(name, "分管校（院）领导审批")) {
                    nextTaskName = "分管日常工作的副校长（副院长）审批";
                    select = true;
                }
            } else if(postType==3 && (Objects.equals(name, "部门主要负责人审批"))) {
                nextTaskName = "分管校（院）领导审批";
                select = true;
            }
            if(Objects.equals(name, "分管日常工作的副校长（副院长）审批")) {
                map.put("nextTaskName","结束");
                return map;
            }
            if(postType == 4 && (Objects.equals(name, "部门负责人审批"))) {
                map.put("nextTaskName","结束");
                return map;
            }
            if(postType == 3 && (Objects.equals(name, "分管校（院）领导审批"))) {
                map.put("nextTaskName","结束");
                return map;
            }
        } else if (Objects.equals(category, OACategoryConstants.SUMMARY)) {
            if(Objects.equals(name, "分管副主任审批")) {
                nextTaskName = "分管主任审批";
                select = true;
            } else if(Objects.equals(name, "分管主任审批")) {
                nextTaskName = "校领导审批";
                select = true;
            }
            if(Objects.equals(name, "校领导审批")) {
                map.put("nextTaskName","结束");
                return map;
            }
        } else if (Objects.equals(category, OACategoryConstants.DUTY)) {
            if(Objects.equals(name, "部门负责人审批")) {
                nextTaskName = "分管校（院）领导审批";
                select = true;
            }
            if(Objects.equals(name, "分管校（院）领导审批")) {
                map.put("nextTaskName","秘书科汇总");
                map.put("needSelect",false);
                return map;
            }
            if(Objects.equals(name, "秘书科汇总")) {
                map.put("nextTaskName","结束");
                return map;
            }
        }

        if(!select){
            if(!Objects.equals("无需审批", nextTaskName) && StrUtil.isEmpty(nextApproval)){
                throw exception(APPROVAL_NOT_FOUND);
            }
        }


        map.put("nextTaskName",nextTaskName);
        map.put("nextApproval",nextApproval);
        map.put("needSelect",select);

        return map;
    }


    private String getDeptLeaderName(Long userId){
        AdminUserRespDTO userDTO = userApi.getUser(userId).getCheckedData();
        Long leaderUserId = deptApi.getDept(userDTO.getDeptId()).getCheckedData().getLeaderUserId();
        if(Objects.nonNull(leaderUserId)){
            return userApi.getUser(leaderUserId).getCheckedData().getNickname();
        }
        return "";
    }

    @Override
    public Boolean approveOrReject(OAApproveReqVO approveReqVO) {

        //更新签名
        lectureMapper.insertImage(getLoginUserId(),approveReqVO.getHandSignature());

        if(approveReqVO.getResult()) {
            Boolean result = bpmTaskServiceApi.approveTask(getLoginUserId(),
                    new BpmTaskApproveReqDTO()
                            .setId(approveReqVO.getTaskId())
                            .setProcessInstanceId(approveReqVO.getProcessInstanceId())
                            .setHandSignature(approveReqVO.getHandSignature())
                            .setReason(approveReqVO.getComment())).getCheckedData();

            if(approveReqVO.getSelect() != null && approveReqVO.getSelect()) {
                List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(approveReqVO.getProcessInstanceId());
                    BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
                    UserDTO assigneeUser = task.getAssigneeUser();

                    bpmTaskServiceApi.updateTaskAssignee(assigneeUser.getId(), new BpmTaskUpdateAssigneeReqDTO()
                            .setAssigneeUserId(getLoginUserId())
                            .setId(task.getId()));
                LocalDateTime time = LocalDateTime.now();
                OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                pick.setTaskId(oaTaskService.getTaskId(approveReqVO.getProcessInstanceId()));
                pick.setUserIds(approveReqVO.getUserIds());
                pick.setChargeLeaderSeq(approveReqVO.getChargeLeaderSeq());
                oaTaskService.pickApprovals(pick, time.plusSeconds(5L));
            }
            return result;
            //外出报告部门分管负责人审批通过特殊处理
//            if (Objects.equals(approveReqVO.getCategory(), OACategoryConstants.OUTREPORT)) {
//                Map<String, Object> nextTaskName = getNextTaskName(getLoginUserId(), approveReqVO.getCategory(), null);
//                String taskName = nextTaskName.get("nextTaskName").toString();
//                if(Objects.equals(taskName, "部门主要负责人审批")) {
//                    //实际部门和默认部门不一致时更新部门主要负责人审批
//                    OutReportDO outReport = outReportService.getByProcessInstanceId(approveReqVO.getProcessInstanceId());
//                    AdminUserRespDTO loginUser = userApi.getUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();
//                    List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(approveReqVO.getProcessInstanceId());
//                    BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
//                    UserDTO assigneeUser = task.getAssigneeUser();
//                    if (!Objects.equals(outReport.getDeptId(), assigneeUser.getDeptId())) {
//                        //流程实际选择的部门的负责人
//                        Long currentLeader = deptApi.getDept(outReport.getDeptId()).getCheckedData().getLeaderUserId();
//                        bpmTaskServiceApi.updateTaskAssignee(assigneeUser.getId(), new BpmTaskUpdateAssigneeReqDTO()
//                                .setAssigneeUserId(currentLeader)
//                                .setId(task.getId()));
//                    }
//                }
//            }
//            return true;
        }else {
            // 审批不通过，则驳回到发起人
            if(bpmTaskServiceApi.rejectTask(getLoginUserId(),
                    new BpmTaskRejectReqDTO()
                            .setId(approveReqVO.getTaskId())
                            .setProcessInstanceId(approveReqVO.getProcessInstanceId())
                            .setHandSignature(approveReqVO.getHandSignature())
                            .setReason(approveReqVO.getComment())).getCheckedData()){
                if(Objects.equals(OACategoryConstants.LEAVE,approveReqVO.getCategory())){
                    leaveService.updateResult(approveReqVO.getProcessInstanceId(), BpmProcessInstanceStatusEnum.REJECT.getStatus());
                }
                if(Objects.equals(OACategoryConstants.SCHEDULE, approveReqVO.getCategory())) {
                    WeeklyWorkScheduleDO workScheduleDO = weeklyWorkScheduleService.getByProcessInstanceId(approveReqVO.getProcessInstanceId());
                    weeklyWorkScheduleService.cancelMessage(workScheduleDO);
                }
            }
        }
        return true;
    }

    @Override
    public void end(OARemoveReqVO reqVO) {
        String itemId = bpmProcessInstanceApi.endProcess(getLoginUserId(),reqVO.getProcessInstanceId()).getCheckedData();
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();
        bpmTaskExtDTO.setName("结束");
        bpmTaskExtDTO.setTaskDefKey("end");
        bpmTaskExtDTO.setTaskId("end");
        bpmTaskExtDTO.setResult(2);
        bpmTaskExtDTO.setProcessInstanceId(reqVO.getProcessInstanceId());
        bpmTaskExtDTO.setProcessDefinitionId(reqVO.getProcessInstanceId());
        LocalDateTime now = LocalDateTime.now();
        bpmTaskExtDTO.setCreateTime(now);
        bpmTaskExtDTO.setEndTime(now);
        bpmTaskExtDTO.setTaskType(6);
        bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);
    }

    @Override
    public void remove(OARemoveReqVO reqVO) {
        //设置流程扩展表的deleted为1
        String itemId = bpmProcessInstanceApi.removeProcess(getLoginUserId(),reqVO.getProcessInstanceId()).getCheckedData();
        //修改请假表里的deleted为1
        if(Objects.equals(OACategoryConstants.LEAVE,reqVO.getCategory())){
            leaveService.removeById(itemId);
        }
    }

    @Override
    public void cancelToStarter(Long loginUserId, String processInstanceId, String category) {
//        bpmProcessInstanceApi.cancelProcessInstance(loginUserId, processInstanceId);
        bpmTaskServiceApi.revokeProcess(new BpmTaskDTO()
                .setInstanceId(processInstanceId)
                .setUserId(loginUserId.toString()));
        if(Objects.equals(OACategoryConstants.LEAVE,category)){
            leaveService.updateResult(processInstanceId,BpmProcessInstanceStatusEnum.CANCEL.getStatus());
        }
    }

    @Override
    public void cancelToDraft(Long loginUserId, String processInstanceId, String category) {
        // 先撤销
        String itemId = bpmProcessInstanceApi.cancelProcessInstance(loginUserId, processInstanceId).getCheckedData();
        // 修改请假表里的result为已取消
        if(Objects.equals(OACategoryConstants.LEAVE,category)){
            leaveService.updateResult(processInstanceId,BpmProcessInstanceStatusEnum.CANCEL.getStatus());
        }
        // 修改一周工作安排填报单状态为处理中
        if(Objects.equals(OACategoryConstants.SUMMARY,category)) {
            WeeklyWorkSummaryDO summary = weeklyWorkSummaryService.getByProcessInstanceId(processInstanceId);
            String scheduleIds = summary.getWeeklyWorkScheduleIds();
            List<Long> ids = Arrays.stream(scheduleIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
            weeklyWorkScheduleService.BatchUpdateStatusByIds(ids,BpmProcessInstanceResultEnum.PROCESS.getResult());
        }
        // 后存草稿
        DraftDO draftDO = new DraftDO();
        // 写入草稿表
        draftDO.setCategory(category);
        draftDO.setUserId(loginUserId.toString());
        draftDO.setItemId(Long.parseLong(itemId));
        draftService.save(draftDO);
        if(Objects.equals(OACategoryConstants.SCHEDULE, category)) {
            //取消发送消息
            WeeklyWorkScheduleDO workScheduleDO = weeklyWorkScheduleService.getByProcessInstanceId(processInstanceId);
            weeklyWorkScheduleService.cancelMessage(workScheduleDO);
        }
    }

    @Override
    public Map<String, Object> getApproveList(Long loginUserId, String taskId, String nextTaskName, Long deptId) {
//        Map<String,Object> reqMap = new HashMap<>();
//        reqMap.put("loginUserId",loginUserId);
//        reqMap.put("taskId",taskId);
        if(deptId == null) {
            AdminUserRespDTO checkedData = userApi.getUser(loginUserId).getCheckedData();
            deptId = checkedData.getDeptId();
        }
        return bpmTaskServiceApi.getTaskNextApprovals(loginUserId,taskId,nextTaskName,deptId).getData();
    }

    @Override
    public void sendUrgency(Long loginUserId, String processInstanceId, String urgeMode) {

        //您有一条【申请人姓名】发起的xxx事项待处理，请尽快登录OA系统进行处理;
        BpmProcessInstanceRespDTO procInsDTO = bpmProcessInstanceApi.getProcessInstanceInfo(processInstanceId).getCheckedData();
        String category = "";
        Long itemId = null;

        switch (procInsDTO.getCategory()) {
            case OACategoryConstants.LEAVE:
                category = OACategoryConstants.LEAVE_STR;
                itemId = leaveService.getByProcessInstanceId(processInstanceId).getId();
                break;
            case OACategoryConstants.LECTURE:
                category = OACategoryConstants.LECTURE_STR;
                itemId = lectureService.getByProcessInstanceId(processInstanceId).getId();
                break;
            case OACategoryConstants.OUTREPORT:
                category = OACategoryConstants.OUTREPORT_STR;
                itemId = outReportService.getByProcessInstanceId(processInstanceId).getId();
                break;
            case OACategoryConstants.SCHEDULE:
                category = OACategoryConstants.SCHEDULE_STR;
                itemId = weeklyWorkScheduleService.getByProcessInstanceId(processInstanceId).getId();
                break;
            case OACategoryConstants.SUMMARY:
                category = OACategoryConstants.SUMMARY_STR;
                itemId = weeklyWorkSummaryService.getByProcessInstanceId(processInstanceId).getId();
                break;
            case OACategoryConstants.DUTY:
                category = OACategoryConstants.DUTY_STR;
                itemId = dutyService.getByProcessInstanceId(processInstanceId).getId();
                break;
            default:
        }
        //发送短信
        if (urgeMode.equals("0")) {
            String message = String.format("【湖南省委党校】您有一条【%s】发起的%s事项待处理，请尽快登录湖南省党校系统一体化信息平台进行处理"
                    , procInsDTO.getStartUser().getNickname(), category);

            // 给任务添加催办标签，需考虑到多人会签任务
            List<Long> assigneeList = bpmTaskServiceApi.superviseTask(loginUserId, processInstanceId).getCheckedData();

            List<AdminUserRespDTO> users = userApi.getUsers(assigneeList).getCheckedData();

            if (CollUtil.isNotEmpty(users)) {

                String mobile = users.stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                        .reduce((k, v) -> k + "," + v).get();

                Map<String, Object> map = new HashMap<>();
                map.put("arg1", message);

                smsSendApi.sendSingleSms(mobile, null, null, "admin-sms-login-new", map);

            }
        }
        //发送待阅
        if (urgeMode.equals("1")) {
            String content = String.format("您有一条【%s】发起的%s事项待处理，请尽快进入OA办公系统进行处理"
                    , procInsDTO.getStartUser().getNickname(), category);

            // 给任务添加催办标签，需考虑到多人会签任务
            List<Long> assigneeList = bpmTaskServiceApi.superviseTask(loginUserId, processInstanceId).getCheckedData();

            List<AdminUserRespDTO> users = userApi.getUsers(assigneeList).getCheckedData();
            if(CollUtil.isNotEmpty(users)) {
                List<ReceiveDO> list = new ArrayList<>();
                for (AdminUserRespDTO user : users) {
                    ReceiveDO receiveDO = new ReceiveDO();
                    receiveDO.setCategory(procInsDTO.getCategory());
                    receiveDO.setItemId(itemId);
                    receiveDO.setApplyTime(procInsDTO.getCreateTime());
                    receiveDO.setUserId(user.getId().toString());
                    receiveDO.setProcessInstanceId(processInstanceId);
                    receiveDO.setContent(content);
                    receiveDO.setPromoterUserId(procInsDTO.getStartUser().getId().toString());
                    list.add(receiveDO);
                }
                receiveService.saveBatch(list);
            }
        }
        //发送短信以及待阅
        if(urgeMode.equals("2")) {
            String message = String.format("【湖南省委党校】您有一条【%s】发起的%s事项待处理，请尽快登录湖南省党校系统一体化信息平台进行处理"
                    , procInsDTO.getStartUser().getNickname(), category);
            String content = String.format("您有一条【%s】发起的%s事项待处理，请尽快进入OA办公系统进行处理"
                    , procInsDTO.getStartUser().getNickname(), category);

            // 给任务添加催办标签，需考虑到多人会签任务
            List<Long> assigneeList = bpmTaskServiceApi.superviseTask(loginUserId, processInstanceId).getCheckedData();

            List<AdminUserRespDTO> users = userApi.getUsers(assigneeList).getCheckedData();

            if (CollUtil.isNotEmpty(users)) {
                //发送短信
                String mobile = users.stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                        .reduce((k, v) -> k + "," + v).get();

                Map<String, Object> map = new HashMap<>();
                map.put("arg1", message);

                smsSendApi.sendSingleSms(mobile, null, null, "admin-sms-login-new", map);

                //发送待阅
                List<ReceiveDO> list = new ArrayList<>();
                for (AdminUserRespDTO user : users) {
                    ReceiveDO receiveDO = new ReceiveDO();
                    receiveDO.setCategory(procInsDTO.getCategory());
                    receiveDO.setItemId(itemId);
                    receiveDO.setApplyTime(procInsDTO.getCreateTime());
                    receiveDO.setUserId(user.getId().toString());
                    receiveDO.setProcessInstanceId(processInstanceId);
                    receiveDO.setContent(content);
                    receiveDO.setPromoterUserId(procInsDTO.getStartUser().getId().toString());
                    list.add(receiveDO);
                }
                receiveService.saveBatch(list);

            }
        }
    }

}
