package com.unicom.swdx.module.oa.service.outReport;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OADealOutReportVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;

import java.time.LocalDate;
import java.util.Map;

public interface OutReportService extends IService<OutReportDO> {

    Long saveDraft(OutReportCreateReqVO reqVO);

    Integer getPostType(Long userId);

    String createOutReportProcess(OutReportCreateReqVO reqVO);

    boolean checkTime(OutReportCreateReqVO reqVO);

    boolean checkTime2(OutReportCreateReqVO reqVO);

    OutReportDO getByProcessInstanceId(String processInstanceId);

    OutReportDO get(Long id, String processInstanceId);

    OutReportRespVO getResp(Long id, String processInstanceId,boolean isReceived);

    void restartOutReport(Long loginUserId, OutReportCreateReqVO reqVO);

    /**
     * 更新状态
     * @param id
     * @param result
     * @return
     */
    public boolean updateResultById(Long id, Integer result);

    /**
     * 更新状态
     * @param id
     * @param
     * @return
     */
    Map<String, LocalDate> getDateById(Long id);

    int deleteById(Long id);

    void dealOutReport(OADealOutReportVO dealOutReportVO);

    void dealOutReportByProcessId(String processId);

}
