package com.unicom.swdx.module.oa.dal.kingbase;


import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.dal.dataobject.YeartaskOperation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
@Mapper
public interface YeartaskOperationMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public YeartaskOperation selectYeartaskOperationById(Long id);

    @TenantIgnore
    public int deletemax(Long inforid);

    @TenantIgnore
    public int deletemaxRecord(Long inforid);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param yeartaskOperation 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<YeartaskOperation> selectYeartaskOperationList(YeartaskOperation yeartaskOperation);

    /**
     * 新增【请填写功能名称】
     *
     * @param yeartaskOperation 【请填写功能名称】
     * @return 结果
     */
    public int insertYeartaskOperation(YeartaskOperation yeartaskOperation);

    /**
     * 修改【请填写功能名称】
     *
     * @param yeartaskOperation 【请填写功能名称】
     * @return 结果
     */
    public int updateYeartaskOperation(YeartaskOperation yeartaskOperation);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteYeartaskOperationById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYeartaskOperationByIds(Long[] ids);

    List<String> selectRepeatNameList(@Param("tenantId") Long tenantId);
}
