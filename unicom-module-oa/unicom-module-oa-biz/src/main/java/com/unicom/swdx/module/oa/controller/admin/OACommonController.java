package com.unicom.swdx.module.oa.controller.admin;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import com.unicom.swdx.module.bpm.api.task.dto.BpmTaskRespDTO;
import com.unicom.swdx.module.oa.controller.admin.oaLeave.vo.OALeaveCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OAApproveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OARemoveReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.service.OACommonService;
import com.unicom.swdx.module.oa.service.OATaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Api(tags = "办公OA - 审批中心")
@RestController
@RequestMapping("/oa/approval")
@Validated
public class OACommonController {

    @Resource
    private OACommonService oaService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

//    @Resource
//    private BpmProcessInstanceApi processInstanceApi;

    @GetMapping("/getNextTaskName")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("获得下一任务节点")
    public CommonResult<Map<String,Object>> getApproveList(String category, Long deptId) {
        return success(oaService.getNextTaskName(getLoginUserId(), category, deptId));
    }

    @GetMapping("/getNextTaskInfo")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("获得下一任务节点")
    public CommonResult<Map<String,Object>> getNextTaskInfo(String category,String name, Long day, String processInstanceId) {
        return success(oaService.getNextTaskInfo(category, name, day, processInstanceId));
    }

    @GetMapping("/isLastSequence")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("查询是否是顺序签的最后一个审批人")
    public CommonResult<Map<String,Object>> getNextTaskInfo(String processInstanceId) {
        return bpmTaskServiceApi.isSequenceLast(processInstanceId);
    }


    @PostMapping("/approveOrReject")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("审批")
    public CommonResult<Boolean> approveOrReject(@RequestBody OAApproveReqVO approveReqVO) {
//        oaService.approveOrReject(approveReqVO);
        return success(oaService.approveOrReject(approveReqVO));
    }

    @PostMapping("/cancel")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("撤销")
    public CommonResult<Boolean> cancel(@RequestParam("processInstanceId") String processInstanceId,
                                        @RequestParam(value = "category",required = false) String category) {
        oaService.cancelToDraft(getLoginUserId(), processInstanceId, category);
        return success(true);
    }

    @PostMapping("/end")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("结束")
    public CommonResult<Boolean> end(@RequestBody OARemoveReqVO reqVO) {
        oaTaskService.end(reqVO);
        return success(true);
    }

    @PostMapping("/remove")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("删除")
    public CommonResult<Boolean> remove(@RequestBody OARemoveReqVO reqVO) {
        oaService.remove(reqVO);
        return success(true);
    }

    @GetMapping("/list-by-process-instance-id")
    @ApiOperation(value = "获得指定流程实例的任务列表", notes = "包括完成的、未完成的")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    public CommonResult<List<Map<String,Object>>> getTaskListByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {

        List<Map<String,Object>> res = bpmTaskServiceApi.getTaskLogByProcessInstanceId(processInstanceId);
        // 过滤掉名称为选择审批人的审批任务
//        return success(bpmTasks.stream().filter(t -> !StrUtil.equals("选择审批人",t.getName())).collect(Collectors.toList()));
        return success(res);
    }

    @GetMapping("/getTaskId")
    @ApiOperation(value = "获得指定流程实例的进行中的taskId")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    public CommonResult<String> getTaskId(@RequestParam("processInstanceId") String processInstanceId) {
        String taskId = oaTaskService.getTaskId(processInstanceId);
        return success(taskId);
    }

    @GetMapping("/getApproveList")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("获得下一任务节点审批人")
    public CommonResult<Map<String,Object>> getApproveList(@RequestParam(value = "taskId",required = false) String taskId,
                                                           @RequestParam(value = "nextTaskName",required = false) String nextTaskName,
                                                           @RequestParam(value = "deptId",required = false) Long deptId) {
        return success(oaService.getApproveList(getLoginUserId(),taskId,nextTaskName,deptId));
    }

    @PostMapping("/pickApprovals")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    @ApiOperation("提交选择的审批人")
    public CommonResult<Boolean> pickApprovals(@RequestBody OApickApprovalsReqVO pickApprovalsReqVO) {
        oaTaskService.pickApprovals(pickApprovalsReqVO, null);
        return success(true);
    }

    @GetMapping("/sendUrgency")
    @ApiOperation("待审批催办")
    @PreAuthorize("@ss.hasPermission('oa:approval:create')")
    public CommonResult<Boolean> sendUrgency(@RequestParam("processInstanceId") String processInstanceId, @RequestParam("urgeMode") String urgeMode) {
        oaService.sendUrgency(getLoginUserId(), processInstanceId, urgeMode);
        return success(true);
    }

//    @PostMapping("/create")
//    @ApiOperation("创建测试流程")
//    public CommonResult<Boolean> create() {
//        processInstanceApi.createProcessInstance(getLoginUserId(),
//                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey("lx_test1"));
//        return success(true);
//    }
}
