package com.unicom.swdx.module.oa.service.schedule;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.*;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkScheduleDO;
import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;

import java.util.List;

public interface WeeklyWorkScheduleService extends IService<WeeklyWorkScheduleDO> {

    Long saveDraft(ScheduleCreateReqVO createReqVO);

    String createWorkScheduleProcess(ScheduleCreateReqVO createReqVO);

    ScheduleRespVO get(Long id, String processInstanceId);

    WeeklyWorkScheduleDO getByProcessInstanceId(String processInstanceId);

    PageResult<ScheduleStatisticRespVO> getScheduleStatisticsList(SchedulePageReqVO reqVO);

    PageResult<ScheduleDetailRespVO> getScheduleStatisticsDetail(SchedulePageReqVO reqVO);

    WorkScheduleDO getScheduleDetailById(Long id);

    void restartSchedule(Long loginUserId, ScheduleCreateReqVO reqVO);

    void editSchedule(Long loginUserId, ScheduleCreateReqVO reqVO);

    void updateStatusById(Long id, Integer status);

    void BatchUpdateStatusByIds(List<Long> ids, Integer status);

    String getCopyTo(Long scheduleId);

    String getProcessInstanceId(Long scheduleId);

    PageResult<SchedulePersonnelRespVO> getPersonnel(Long id, PageParam pageParam);

    void deleteSchedule(Integer id);

    void cancelMessage(WeeklyWorkScheduleDO schedules);
}
