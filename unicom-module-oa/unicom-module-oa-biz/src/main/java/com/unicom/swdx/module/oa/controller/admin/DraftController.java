package com.unicom.swdx.module.oa.controller.admin;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.vo.draft.DraftPageReqVO;
import com.unicom.swdx.module.oa.controller.admin.vo.draft.DraftRespVO;
import com.unicom.swdx.module.oa.convert.DraftConvert;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "办公OA - 草稿箱")
@RestController
@RequestMapping("/oa/draft")
public class DraftController {

    @Resource
    private DraftService draftService;

    @GetMapping("/page")
    @ApiOperation("获得草稿箱分页列表")
    public CommonResult<PageResult<DraftRespVO>> page(DraftPageReqVO reqVO) {
        return success(DraftConvert.INSTANCE.convertPage(draftService.page(reqVO)));
    }

    @PostMapping("/delete")
    @ApiOperation("删除草稿")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        draftService.deleteDraft(id);
        return success(true);
    }

}
