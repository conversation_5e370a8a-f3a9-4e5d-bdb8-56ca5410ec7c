package com.unicom.swdx.module.oa.service.infor;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforExportReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforPageReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforUpdateReqVO;
import com.unicom.swdx.module.oa.convert.UserinforConvert;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;
import com.unicom.swdx.module.oa.dal.kingbase.UserinforMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.USERINFOR_NOT_EXISTS;


/**
 * 重点任务关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserinforServiceImpl implements UserinforService {

    @Resource
    private UserinforMapper userinforMapper;

    @Override
    public Integer createUserinfor(UserinforCreateReqVO createReqVO) {
        // 插入
        UserinforDO userinfor = UserinforConvert.INSTANCE.convert(createReqVO);
        userinfor.setTenantId(SecurityFrameworkUtils.getTenantId());
        userinforMapper.insert(userinfor);
        // 返回
        return userinfor.getId();
    }

    @Override
    public void updateUserinfor(UserinforUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateUserinforExists(updateReqVO.getId());
        // 更新
        UserinforDO updateObj = UserinforConvert.INSTANCE.convert(updateReqVO);
        userinforMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserinfor(Integer id) {
        // 校验存在
        this.validateUserinforExists(id);
        // 删除
        userinforMapper.deleteById(id);
    }

    private void validateUserinforExists(Integer id) {
        if (userinforMapper.selectById(id) == null) {
            throw exception(USERINFOR_NOT_EXISTS);
        }
    }



    //缓存30s用户信息避免频繁请求
    Cache<Integer, UserinforDO> resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(128*50)   // 设定最大容量
                    .expireAfterWrite(30L, TimeUnit.SECONDS) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    @Override
    public UserinforDO getUserinfor(Integer id) {
        UserinforDO userinforDO = null;
        try {
            userinforDO = resultCache.get(id, () -> {

                return userinforMapper.selectById(id);
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return userinforDO;
    }

    @Override
    public List<UserinforDO> getUserinforList(Collection<Integer> ids) {
        return userinforMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<UserinforDO> getUserinforPage(UserinforPageReqVO pageReqVO) {
        return userinforMapper.selectPage(pageReqVO);
    }

    @Override
    public List<UserinforDO> getUserinforList(UserinforExportReqVO exportReqVO) {
        return userinforMapper.selectList(exportReqVO);
    }

}
