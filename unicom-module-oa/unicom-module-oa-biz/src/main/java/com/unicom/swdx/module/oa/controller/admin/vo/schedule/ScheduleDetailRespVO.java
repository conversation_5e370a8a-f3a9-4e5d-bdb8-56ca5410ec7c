package com.unicom.swdx.module.oa.controller.admin.vo.schedule;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("周工作安排统计详情")
@Data
@ToString(callSuper = true)
public class ScheduleDetailRespVO {
    /**
     * 填报事项
     */
    private String filledIn;


    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDate;

    /**
     * 地点
     */
    private String location;

    /**
     * 活动
     */
    private String activity;

    /**
     * 参加人员
     */
    private String enrolledPersonnel;

    /**
     * 对应id
     */
    private Long id;

    private String processInstanceId;
}
