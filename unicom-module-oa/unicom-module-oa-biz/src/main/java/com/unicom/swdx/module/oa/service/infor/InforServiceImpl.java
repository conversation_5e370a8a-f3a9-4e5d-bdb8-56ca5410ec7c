package com.unicom.swdx.module.oa.service.infor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.controller.admin.infor.InforController;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.*;
import com.unicom.swdx.module.oa.convert.InforConvert;
import com.unicom.swdx.module.oa.convert.UserinforConvert;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.InforMapper;
import com.unicom.swdx.module.oa.dal.kingbase.UserinforMapper;
import com.unicom.swdx.module.oa.dal.kingbase.YeartaskOperationMapper;
import com.unicom.swdx.module.oa.dal.kingbase.YeartaskRecordMapper;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;

import com.unicom.swdx.module.system.api.sms.SmsQueApi;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.sms.dto.que.SmsSendReq;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.*;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.INFOR_NOT_EXISTS;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.USERINFOR_NOT_NULL;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * 重点工作信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InforServiceImpl  extends ServiceImpl<InforMapper, InforDO>  implements InforService {


    @Resource
    UserinforService userinforService;

    @Resource
    UserinforMapper userinforMapper;

    @Resource
    InforController inforController;

    @Resource
    private InforMapper inforMapper;

    @Resource(name = "taskScheduler")
    private AsyncListenableTaskExecutor taskExecutor;


    @Resource
    private YeartaskOperationMapper operationMapper;

    @Resource
    private YeartaskRecordMapper yeartaskRecordMapper;



    @Override
    public Integer createInfor(InforCreateReqVO createReqVO) {


        List<UserinforDO> userinforDOList  =  createReqVO.getUserinforDOList();
        //查看主办人不能为null
        if(CollectionUtil.isEmpty(userinforDOList)){
            throw exception(USERINFOR_NOT_NULL);
        }else{
            boolean hasFieldEqualToOne = CollUtil.isNotEmpty(CollUtil.filterNew(userinforDOList, yourObject ->
                    yourObject.getUtype() == 1 && yourObject.getUtype()!=null
                    &&  yourObject.getDptid()!=null &&
                            StrUtil.isAllNotEmpty(  yourObject.getUname(),
                                     yourObject.getDptname() ) ));

            if(hasFieldEqualToOne ==false){
                throw exception(USERINFOR_NOT_NULL);
            }
        }

        log.info("100");
        if(NumberUtil.equals(1 ,createReqVO.getStatus().intValue())||NumberUtil.equals(4 ,createReqVO.getStatus().intValue())){
            //提交到处长  需要检查处室有没有处长
            DeptRespDTO parentDept = deptApi.getDept(Long.valueOf(createReqVO.getSdeptid())).getCheckedData();
            if(parentDept.getLeaderUserId()==null){
                throw  exception(DEPT_NOT_LEADER);
            }

            AdminUserRespDTO userRespDTO =  adminUserApi.getUser(parentDept.getLeaderUserId()).getCheckedData();

            if(ObjectUtil.isEmpty(userRespDTO)){
                throw  exception(DEPT_NOT_LEADER);
            }

            UserinforDO userinforDO = new UserinforDO();
            userinforDO.setUtype(11);
            userinforDO.setUid(Math.toIntExact(userRespDTO.getId()));
            userinforDO.setUname(userRespDTO.getNickname());
            userinforDO.setDptid(Math.toIntExact(parentDept.getId()));
            userinforDO.setDptname(parentDept.getName());




            userinforDOList.add(userinforDO);   //插入处长




            //管理员
            List<Long> roletempDOList = userinforMapper.selectByTenantId(parentDept.getTenantId());

            if(CollectionUtil.isEmpty(roletempDOList)){
                throw  exception(DEPT_NOT_LEADER1);
            }

            for (int i = 0; i < roletempDOList.size(); i++) {
                Long userid  =  roletempDOList.get(i);

                AdminUserRespDTO userlead =  adminUserApi.getUser(userid).getCheckedData();

                if(ObjectUtil.isEmpty(userlead)){
                    continue;
                }

                DeptRespDTO leaderdept = deptApi.getDept(Long.valueOf(userlead.getDeptId())).getCheckedData();

                if(ObjectUtil.isEmpty(leaderdept)){
                    continue;
                }

                UserinforDO leaderinfor = new UserinforDO();
                leaderinfor.setUtype(33);
                leaderinfor.setUid(Math.toIntExact(userlead.getId()));
                leaderinfor.setUname(userlead.getNickname());
                leaderinfor.setDptid(Math.toIntExact(leaderdept.getId()));
                leaderinfor.setDptname(leaderdept.getName());

                userinforDOList.add(leaderinfor);

            }

            //副校长
            List<Long> roletempList = userinforMapper.selectPrincipalByTenantId(parentDept.getTenantId());

            if(CollectionUtil.isEmpty(roletempList)){
                throw  exception(DEPT_NOT_LEADER2);
            }

            for (int i = 0; i < roletempList.size(); i++) {
                Long userid  =  roletempList.get(i);

                AdminUserRespDTO userlead =  adminUserApi.getUser(userid).getCheckedData();

                if(ObjectUtil.isEmpty(userlead)){
                    continue;
                }

                DeptRespDTO leaderdept = deptApi.getDept(Long.valueOf(userlead.getDeptId())).getCheckedData();

                if(ObjectUtil.isEmpty(leaderdept)){
                    continue;
                }

                UserinforDO headmasterInfor = new UserinforDO();
                headmasterInfor.setUtype(55);
                headmasterInfor.setUid(Math.toIntExact(userlead.getId()));
                headmasterInfor.setUname(userlead.getNickname());
                headmasterInfor.setDptid(Math.toIntExact(leaderdept.getId()));
                headmasterInfor.setDptname(leaderdept.getName());

                userinforDOList.add(headmasterInfor);

            }

        }


        // 插入
        InforDO infor = InforConvert.INSTANCE.convert(createReqVO);


        AdminUserRespDTO users =  adminUserApi.getUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();
        infor.setMobile(users.getMobile());
        inforMapper.insert(infor);


        userinforDOList.forEach(it ->{
            it.setInforid(infor.getId());
            userinforService.createUserinfor(UserinforConvert.INSTANCE.convertuc(it));
        });

        log.info("179");
        if(infor.getStatus().intValue() == 20){
            log.info("中心工作创建开始发短信");
            log.info("182");
            taskExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    log.info("开始发短信");
                    sendmessage(infor.getId() , infor.getStarttime() , infor.getEndtime() ,infor.getStatus() ,infor.getYeartag());
                    log.info("结束发短信");
                }
            });

        }

        if(createReqVO.getStatus()!=null  && createReqVO.getStatus().intValue() == 4){

            YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(infor.getSdeptid()+"")
                    .dptname(infor.getSdepname())
                    .nickname(infor.getSusername())
                    .userid(infor.getSuserid()+"")
                    .createtime(DateUtil.date().toString())
                    .inforid(Long.valueOf(infor.getId()))
                    .content("提交").build();


            operationMapper.insertYeartaskOperation(yeartaskOperation);


            UserinforDO userinforDO =   userinforDOList.stream().filter(it -> it.getUtype().intValue() == 11).findFirst().get();


            YeartaskOperation yeartaskOperation_after = YeartaskOperation.builder().dptid(userinforDO.getDptid()+"")
                    .dptname(userinforDO.getDptname())
                    .nickname(userinforDO.getUname())
                    .userid(userinforDO.getUid()+"")
                    .createtime(DateUtil.date().toString())
                    .inforid(Long.valueOf(infor.getId()))
                    .content("待审批").build();

            operationMapper.insertYeartaskOperation(yeartaskOperation_after);
        }



        log.info("222");
        // 返回
        return infor.getId();
    }

    @Resource
    SmsQueApi smsQueApi;

    @Resource
    public AdminUserApi adminUserApi;

    @Resource
    public SmsSendApi smsSendService;

    @Resource
    public DeptApi deptApi;




    @Override
    public String updateInfor(InforUpdateReqVO updateReqVO) {
        // 校验存在
        InforDO PREDAO = this.validateInforExistsDao(updateReqVO.getId());

//        if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2&&updateReqVO.getStatus()==4&&updateReqVO.getHhandle()==2){
//            updateReqVO.setHhandle(1);
//            updateReqVO.setLeaderhandle(1);
//        }
//
//        if(PREDAO.getStatus()==30&&PREDAO.getShandle()==2&&PREDAO.getYeartag()==2){
//            updateReqVO.setShandle(3);
//        }
//
//        if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2&&updateReqVO.getStatus()==2){
//            updateReqVO.setLeaderhandle(1);
//        }
//
//        if(PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==4&&updateReqVO.getStatus()==50){
//            if(PREDAO.getProcessStatus()==1){
//                updateReqVO.setStatus(30);
//                updateReqVO.setShandle(0);
//            }
//            if(PREDAO.getProcessStatus()==2){
//                updateReqVO.setStatus(50);
//            }
//        }


        AdminUserRespDTO adminuser  = adminUserApi.getUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();
        DeptRespDTO admindpt =  deptApi.getDept(adminuser.getDeptId()).getCheckedData();



        if(updateReqVO.getUserinforDOList()!=null){




            List<UserinforDO> userinforDOList  =  updateReqVO.getUserinforDOList();
            //查看主办人不能为null
            if(CollectionUtil.isEmpty(userinforDOList)){
                throw exception(USERINFOR_NOT_NULL);
            }else{
                boolean hasFieldEqualToOne = CollUtil.isNotEmpty(CollUtil.filterNew(userinforDOList, yourObject ->
                        yourObject.getUtype() == 1 && yourObject.getUtype()!=null
                                &&  yourObject.getDptid()!=null &&
                                StrUtil.isAllNotEmpty(  yourObject.getUname(),
                                        yourObject.getDptname() ) ));

                if(hasFieldEqualToOne ==false){
                    throw exception(USERINFOR_NOT_NULL);
                }
            }




        }



        // 更新
        InforDO updateObj = InforConvert.INSTANCE.convert(updateReqVO);










        try {
            if(updateObj.getId()!=null&&20 == updateReqVO.getStatus().intValue() && 10 == PREDAO.getStatus().intValue() ){
                updateObj.setCreateTime(LocalDateTime.now());
            }
        }catch (Exception e){
            log.error("updateInfor : {}" ,  e.getMessage());
        }

        inforMapper.updateById(updateObj);





        if(updateObj.getId()!=null&&updateReqVO.getUserinforDOList()!=null&&updateReqVO.getUserinforDOList()!=null){
            //要修改办理人信息
            LambdaQueryWrapperX<UserinforDO> deletewrapper  = new LambdaQueryWrapperX();
            deletewrapper.eq(UserinforDO::getInforid , updateReqVO.getId());
            deletewrapper.ne(UserinforDO::getUtype , 11);
            deletewrapper.ne(UserinforDO::getUtype , 33);
            deletewrapper.ne(UserinforDO::getUtype , 55);
            userinforMapper.delete(deletewrapper);

            //添加
            updateReqVO.getUserinforDOList().forEach(it->{
                it.setInforid(updateObj.getId());
                userinforService.createUserinfor(UserinforConvert.INSTANCE.convertuc(it));
            });


        }


        if(updateReqVO.getEndcontent()!=null&&PREDAO.getShandle()!=0){
            LambdaQueryWrapper<YeartaskRecord> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(YeartaskRecord::getInforid,PREDAO.getId());
            lambdaQueryWrapper.orderByDesc(YeartaskRecord::getId).last("limit 1");
            YeartaskRecord yeartaskRecord = yeartaskRecordMapper.selectOne(lambdaQueryWrapper);
            if(yeartaskRecord!=null){
                yeartaskRecord.setRecordContent(updateReqVO.getEndcontent());
                yeartaskRecordMapper.updateById(yeartaskRecord);
            }
        }

        if(updateReqVO.getEndfile()!=null){
            LambdaQueryWrapper<YeartaskRecord> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(YeartaskRecord::getInforid,PREDAO.getId());
            lambdaQueryWrapper.orderByDesc(YeartaskRecord::getId).last("limit 1");
            YeartaskRecord yeartaskRecord = yeartaskRecordMapper.selectOne(lambdaQueryWrapper);
            if(yeartaskRecord!=null){
                yeartaskRecord.setRecordFiles(updateReqVO.getEndfile());
                yeartaskRecordMapper.updateById(yeartaskRecord);
            }
        }


        if(updateObj.getId()!=null&& updateReqVO.getStatus() !=null && 20 == updateReqVO.getStatus().intValue() ){
         log.info("中心工作创建开始发短信");
            try {
                sendmessage( updateObj.getId() , updateObj.getStarttime()==null?PREDAO.getStarttime():updateObj.getStarttime() ,
                        updateObj.getEndtime()==null?PREDAO.getEndtime():updateObj.getEndtime() ,updateReqVO.getStatus() , PREDAO.getYeartag() );
            }catch (Exception e){
                log.info("中心工作发短信失败： {}" , e.getMessage());
            }


        }

//        if((updateObj.getId()!=null&& updateReqVO.getStatus() !=null && 30 == updateReqVO.getStatus().intValue() )
//        ){
//
//            try {
//                sendmessage( updateObj.getId() , updateObj.getStarttime()==null?PREDAO.getStarttime():updateObj.getStarttime() ,
//                        updateObj.getEndtime()==null?PREDAO.getEndtime():updateObj.getEndtime() ,updateReqVO.getStatus() );
//            }catch (Exception e){
//                log.info("中心工作发短信失败： {}" , e.getMessage());
//            }
//
//
//        }


        if(updateObj.getId()!=null&& updateReqVO.getStatus() !=null && ((30 == updateReqVO.getStatus().intValue() && PREDAO.getStatus().intValue()==4 && PREDAO.getYeartag().intValue()==2)
                || (30 == updateReqVO.getStatus().intValue() && PREDAO.getYeartag().intValue()<=1))
               ){

            try {
                sendmessage( updateObj.getId() , updateObj.getStarttime()==null?PREDAO.getStarttime():updateObj.getStarttime() ,
                        updateObj.getEndtime()==null?PREDAO.getEndtime():updateObj.getEndtime() ,updateReqVO.getStatus() ,PREDAO.getYeartag());
            }catch (Exception e){
                log.info("中心工作发短信失败： {}" , e.getMessage());
            }


        }


        try {






            if(updateReqVO.getHhandle()!=null&&updateReqVO.getHhandle().intValue()==1){
                updateReqVO.setStatus(4);
            }

            if(updateReqVO.getShandle()!=null&&updateReqVO.getShandle().intValue()==1){
                updateReqVO.setStatus(30);
            }

            //插入日志
            if(updateReqVO.getStatus()!=null ){

                if(PREDAO.getStatus().intValue() ==1 && PREDAO.getHhandle().intValue() ==0
                        && updateReqVO.getStatus().intValue() == 4  ){
                    DateTime now = DateUtil.date();

                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("提交").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);


                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 11);

                    UserinforDO userinforDO =   userinforMapper.selectOne(queryWrapperX.last("limit 1"));

                    YeartaskOperation yeartaskOperation_after = YeartaskOperation.builder().dptid(userinforDO.getDptid()+"")
                            .dptname(userinforDO.getDptname())
                            .nickname(userinforDO.getUname())
                            .userid(userinforDO.getUid()+"")
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);



                }

                if(PREDAO.getStatus().intValue() ==2 && updateReqVO.getStatus().intValue() == 4  ){

                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));
                    DateTime now = DateUtil.date();

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("提交").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);


                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 11);

                    UserinforDO userinforDO =   userinforMapper.selectOne(queryWrapperX.last("limit 1"));


                    YeartaskOperation yeartaskOperation_after = YeartaskOperation.builder().dptid(userinforDO.getDptid()+"")
                            .dptname(userinforDO.getDptname())
                            .nickname(userinforDO.getUname())
                            .userid(userinforDO.getUid()+"")
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);



                }

                //status 4 -> 30
                if(PREDAO.getStatus().intValue() ==4 && updateReqVO.getStatus().intValue() == 30  ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);



//                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
//                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
//                    queryWrapperX.eq(UserinforDO::getUtype , 1);
//
//                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
//                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
//                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
//                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
//                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(PREDAO.getSdeptid()+"")
                            .dptname(PREDAO.getSdepname())
                            .nickname(PREDAO.getSusername())
                            .userid(String.valueOf(PREDAO.getSuserid()))
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待办理").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);



                }

                if(PREDAO.getStatus().intValue() ==4 && updateReqVO.getStatus().intValue() == 1  ) {
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId() + "")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId() + "")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("撤回").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(adminuser.getDeptId() + "")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId() + "")
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待提交").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);
                }

                //办理情况退回
                if( (((PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==1)||(PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==2) ||(PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==3)) &&
                updateReqVO.getStatus().intValue()==30 && updateReqVO.getShandle().intValue() == 0)
                        //单独对赵校长办理情况退回做处理
                        ||(  PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==4 && updateReqVO.getStatus().intValue()==30 && updateReqVO.getShandle().intValue() == 0 && !updateReqVO.getIsApprove())) {
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));
                    operationMapper.deletemaxRecord(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId() + "")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId() + "")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("退回")
                            .rejectionreason(updateReqVO.getRejectionReason()).build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(PREDAO.getSdeptid()+"")
                            .dptname(PREDAO.getSdepname())
                            .nickname(PREDAO.getSusername())
                            .userid(String.valueOf(PREDAO.getSuserid()))
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待办理").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);

                        //退回，可办次数+1
                        if(PREDAO.getHandleCount()<=1){
                            updateObj.setHandleCount(PREDAO.getHandleCount()+1);
                            updateObj.setIsHandle(1);
                            inforMapper.updateById(updateObj);
                        }
                }

                //赵校长通过正在推进的任务
                if(  PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==4 && updateReqVO.getStatus().intValue()==30 && updateReqVO.getShandle().intValue() == 0 && updateReqVO.getIsApprove()){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);


                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(PREDAO.getSdeptid()+"")
                            .dptname(PREDAO.getSdepname())
                            .nickname(PREDAO.getSusername())
                            .userid(String.valueOf(PREDAO.getSuserid()))
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待办理").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);
                }


                //status=30 shandle1->2
                if( PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==1
                        && updateReqVO.getStatus().intValue() == 30  && updateReqVO.getShandle().intValue() == 2 ) {
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId() + "")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId() + "")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 4);

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);
                }

                if( PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==2
                        && updateReqVO.getStatus().intValue() == 30  && updateReqVO.getShandle().intValue() == 3 ) {
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId() + "")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId() + "")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 33);
                    queryWrapperX.eq(UserinforDO::getTenantId,getTenantId());

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);
                }

                if( PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==3
                        && updateReqVO.getStatus().intValue() == 30  && updateReqVO.getShandle().intValue() == 4 ) {
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId() + "")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId() + "")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 55);
                    queryWrapperX.eq(UserinforDO::getTenantId,getTenantId());

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);
                }


                if(  (PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==0
                        && updateReqVO.getStatus().intValue() == 4  && (updateReqVO.getHhandle()!=null && updateReqVO.getHhandle().intValue() ==2))

                        ||


                        (PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==1
                                && updateReqVO.getStatus().intValue() == 10 )

                ){


                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);



                    if(PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==0
                            && updateReqVO.getStatus().intValue() == 4  && (updateReqVO.getHhandle()!=null && updateReqVO.getHhandle().intValue() ==2)){


                        LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                        queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                        queryWrapperX.eq(UserinforDO::getUtype , 4);

                        List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                        String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                        String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                        String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                        String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                        YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                                .dptname(alldptname)
                                .nickname(alluname)
                                .userid(allid)
                                .createtime(DateUtil.offsetSecond(now,1).toString())
                                .inforid(Long.valueOf(PREDAO.getId()))
                                .content("待审批").build();

                        operationMapper.insertYeartaskOperation(yeartaskOperation_after);

                    }


                    if(PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==1
                            && updateReqVO.getStatus().intValue() == 10){


                        LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                        queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                        queryWrapperX.eq(UserinforDO::getUtype , 33);
                        queryWrapperX.eq(UserinforDO::getTenantId,getTenantId());

                        List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                        String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                        String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                        String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                        String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                        YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                                .dptname(alldptname)
                                .nickname(alluname)
                                .userid(allid)
                                .createtime(DateUtil.offsetSecond(now,1).toString())
                                .inforid(Long.valueOf(PREDAO.getId()))
                                .content("待开始").build();

                        operationMapper.insertYeartaskOperation(yeartaskOperation_after);

                    }


                }

                if(  PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==2
                        && updateReqVO.getStatus().intValue() == 4  && (updateReqVO.getHhandle()!=null && updateReqVO.getHhandle().intValue() ==1)



                ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 33);
                    queryWrapperX.eq(UserinforDO::getTenantId,getTenantId());

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                }

                if(  PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==1
                        && updateReqVO.getStatus().intValue() == 4  && (updateReqVO.getHhandle()!=null && updateReqVO.getHhandle().intValue() ==3)



                ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 55);
                    queryWrapperX.eq(UserinforDO::getTenantId,getTenantId());

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待审批").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                }


                if(   (PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==0
                        && updateReqVO.getStatus().intValue() == 2)

                        ||


                        (PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==1
                                && updateReqVO.getStatus().intValue() == 2 )

                        ||


                        (PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==2
                                && updateReqVO.getStatus().intValue() == 2 )

                        ||


                        (PREDAO.getStatus().intValue() ==4 && PREDAO.getHhandle().intValue() ==3
                                && updateReqVO.getStatus().intValue() == 2 )

                        ||


                        (PREDAO.getStatus().intValue() ==10
                                && updateReqVO.getStatus().intValue() == 2 )

                ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("退回")
                            .rejectionreason(updateReqVO.getRejectionReason()).build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(PREDAO.getSdeptid()+"")
                            .dptname(PREDAO.getSdepname())
                            .nickname(PREDAO.getSusername())
                            .userid(String.valueOf(PREDAO.getSuserid()))
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待提交").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                }



                if(PREDAO.getStatus().intValue() ==10
                        && updateReqVO.getStatus().intValue() == 20  ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));


                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("开始").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);





                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 1);

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待签收").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                }

                if(PREDAO.getStatus().intValue() ==10
                        && updateReqVO.getStatus().intValue() == 20  ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));


                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("开始").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);





                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 1);

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待签收").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                }


                if(PREDAO.getStatus().intValue() ==10
                        && updateReqVO.getStatus().intValue() == 30  ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));


                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("开始").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);


                    LambdaQueryWrapperX<UserinforDO> queryWrapperX  = new LambdaQueryWrapperX();
                    queryWrapperX.eq(UserinforDO::getInforid , updateReqVO.getId());
                    queryWrapperX.eq(UserinforDO::getUtype , 1);

                    List<UserinforDO> userinforDO =   userinforMapper.selectList(queryWrapperX);
                    String alldptid =userinforDO.stream().map(it-> it.getDptid().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alldptname =userinforDO.stream().map(it-> it.getDptname()) .reduce( (k,v)-> k +"," + v  ).get();
                    String alluname =userinforDO.stream().map(it-> it.getUname().toString()) .reduce( (k,v)-> k +"," + v  ).get();
                    String allid =userinforDO.stream().map(it-> it.getUid().toString()) .reduce( (k,v)-> k +"," + v  ).get();

                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder().dptid(alldptid)
                            .dptname(alldptname)
                            .nickname(alluname)
                            .userid(allid)
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待办理").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                }


                if(PREDAO.getStatus().intValue() ==20
                        && updateReqVO.getStatus().intValue() == 30  ){

                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(DateUtil.date().toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("签收").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    deptApi.sendMessageRefresh(InforMessageEnum.SENDWAIT.getTag() + updateReqVO.getId());



                    YeartaskOperation yeartaskOperation_after = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(DateUtil.date().toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("待办理").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);

                }


                if( PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==0
                        && updateReqVO.getStatus().intValue() == 30  && updateReqVO.getShandle().intValue() == 1 ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(now.toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("办理").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation);

                    deptApi.sendMessageRefresh(InforMessageEnum.SENDSTATUS30.getTag() + updateReqVO.getId());

                    if(PREDAO.getYeartag() ==2){


                        LambdaQueryWrapperX<UserinforDO> queryWrapperX = new LambdaQueryWrapperX();
                        queryWrapperX.eq(UserinforDO::getInforid, updateReqVO.getId());
                        queryWrapperX.eq(UserinforDO::getUtype, 11);

                        UserinforDO userinforDO = userinforMapper.selectOne(queryWrapperX.last("limit 1"));

                        YeartaskOperation yeartaskOperation_after = YeartaskOperation.builder().dptid(userinforDO.getDptid() + "")
                                .dptname(userinforDO.getDptname())
                                .nickname(userinforDO.getUname())
                                .userid(userinforDO.getUid() + "")
                                .createtime(DateUtil.offsetSecond(now,1).toString())
                                .inforid(Long.valueOf(PREDAO.getId()))
                                .content("待审批").build();

                        operationMapper.insertYeartaskOperation(yeartaskOperation_after);


                        YeartaskRecord yeartaskRecord=new YeartaskRecord();
                        yeartaskRecord.setCreatetime(DateUtil.date().toString());
                        yeartaskRecord.setRecordContent(updateReqVO.getEndcontent());
                        yeartaskRecord.setRecordFiles(updateReqVO.getEndfile());
                        yeartaskRecord.setInforid(Long.valueOf(PREDAO.getId()));
                        yeartaskRecord.setProcessStatus(updateReqVO.getProcessStatus());
                        yeartaskRecord.setUserName(adminuser.getNickname());
                        yeartaskRecord.setUserid(adminuser.getId()+"");
                        yeartaskRecordMapper.insert(yeartaskRecord);

                        //办理后办理次数-1，是否可办置为1
                        if(PREDAO.getHandleCount()>=1){
                            updateObj.setHandleCount(PREDAO.getHandleCount()-1);
                            updateObj.setIsHandle(1);
                            inforMapper.updateById(updateObj);
                        }


                    }else{

                        operationMapper.deletemax(Long.valueOf(PREDAO.getId()));


                        YeartaskOperation yeartaskOperation_after = YeartaskOperation.builder().dptid(PREDAO.getSdeptid()+"")
                                .dptname(PREDAO.getSdepname())
                                .nickname(PREDAO.getSusername())
                                .userid(PREDAO.getSuserid()+"")
                                .createtime(DateUtil.offsetSecond(now,1).toString())
                                .inforid(Long.valueOf(PREDAO.getId()))
                                .content("待办结").build();

                        operationMapper.insertYeartaskOperation(yeartaskOperation_after);






                    }



                }



                if( PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==4
                        && updateReqVO.getStatus().intValue() == 50   ){
                    DateTime now = DateUtil.date();
                    operationMapper.deletemax(Long.valueOf(PREDAO.getId()));

                    YeartaskOperation yeartaskOperation = YeartaskOperation.builder().dptid(adminuser.getDeptId()+"")
                            .dptname(admindpt.getName())
                            .nickname(adminuser.getNickname())
                            .userid(adminuser.getId()+"")
                            .createtime(DateUtil.date().toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("通过").build();



                    operationMapper.insertYeartaskOperation(yeartaskOperation);


                    YeartaskOperation yeartaskOperation_after  = YeartaskOperation.builder()
                            .createtime(DateUtil.offsetSecond(now,1).toString())
                            .inforid(Long.valueOf(PREDAO.getId()))
                            .content("办结").build();

                    operationMapper.insertYeartaskOperation(yeartaskOperation_after);

                    deptApi.sendMessageRefresh(InforMessageEnum.SENDWAIT.getTag() + updateReqVO.getId());
                    deptApi.sendMessageRefresh(InforMessageEnum.SENDSTATUS30.getTag() + updateReqVO.getId());
//                    LambdaQueryWrapperX<UserinforDO> selectwrapper  = new LambdaQueryWrapperX();
//                    selectwrapper.eq(UserinforDO::getInforid , updateReqVO.getId());
//                    selectwrapper.eq(UserinforDO::getUtype , 1);
//                    List<UserinforDO> all = userinforMapper.selectList(selectwrapper);
//
//
//                    List<Long> uids = all.stream().map(it -> Long.valueOf(it.getUid())).collect(Collectors.toList());
//                    List<AdminUserRespDTO> userinfors  =  adminUserApi.getUsers(uids).getCheckedData();
//
//                    List<String> mobiles = userinfors.stream().map(it -> it.getMobile()).filter(Objects::nonNull)
//                            .collect(Collectors.toList());
//
//
//                    mobiles.forEach(it ->{
//
//                    });

                }

            }

        }catch (Exception e){
            log.info("中心工作插入日志失败： {}" , e.getMessage());
        }

        return "success";
    }

    private void sendmessage(Integer inforid , String start , String end ,int status ,Integer yeartag) {

        if(yeartag==2){
            InforDO inforDO = inforMapper.selectInforDoById(inforid);

            Long uid= Long.valueOf(inforDO.getSuserid());

            log.info("租户id{}",SecurityFrameworkUtils.getTenantId());
//        List<Long> uids = all.stream().filter(Objects::nonNull).filter(it-> it.getUid()!=null).map(it -> Long.valueOf(it.getUid())).collect(Collectors.toList());
            log.info("861");
//        CommonResult<List<AdminUserRespDTO>> userinfors  =  adminUserApi.getUsers(uids);
            CommonResult<AdminUserRespDTO> user = adminUserApi.getUser(uid);
            CommonResult<List<AdminUserRespDTO>> userinfors=new CommonResult<>();
            List<AdminUserRespDTO> users=new ArrayList<>();
            users.add(user.getData());
            userinfors.setData(users);
            log.info("863");
//        log.info("主办人用户信息 {}" ,userinfors.getCheckedData());
            if(userinfors!=null && userinfors.getData()!=null){

                List<String> mobiles = userinfors.getData().stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                        .collect(Collectors.toList());

                String sms =  "【湖南省委党校】您好！您有一件重点工作任务【"+inforDO.getTaskcontent()+"】待办理，请及时进行处理。 "  ;

                String sms1 =  "【湖南省委党校】您好！您有一件即将逾期的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时进行处理。 "  ;

                mobiles.forEach(it -> {

                    if(status == 30){
                        DateTime time1 = DateUtil.parse(DateUtil.now());
//                    DateTime time2 = DateUtil.offsetDay(DateUtil.parse(start), -1);
                        DateTime time2 = DateUtil.parse(start);
                        log.info("当前时间 {}",time1);
                        log.info("开始时间 {}",time2);
                        Date sendTime = null;
                        if( time1.compareTo(time2) < 0 ){

                            deptApi.sendMessageRefresh(InforMessageEnum.SENDWAIT.getTag() + inforid); //修改之前删除原来的短信

                            //延时发出
                            sendTime = time2.toJdkDate();
                            SmsSendReq req = new SmsSendReq();
                            req.setType(1);
                            req.setPhone(ListUtil.of(it));
                            req.setMessage(sms);
                            req.setSendTime( sendTime );
                            req.setTitle(InforMessageEnum.SENDWAIT.getTag() + inforid); //发送待签收短信 + inforid
                            smsQueApi.sendque(req);
                            log.info("短信延迟发出");
                        }else {
                            //立即发出
                            Map<String,Object> map = new HashMap<>();
                            map.put("arg1", sms);
                            smsSendService.sendSingleSms(it, null,null ,"admin-sms-login-new",map);
                            log.info("短信立即发出");
                        }
                    }



                    if(status == 30){
                        DateTime time3 = DateUtil.parse(DateUtil.now());
                        DateTime time4 = DateUtil.offsetDay(DateUtil.parse(end), -1);

                        Date sendTime1 = null;
                        if( time3.compareTo(time4) < 0 ){

                            deptApi.sendMessageRefresh(InforMessageEnum.SENDSTATUS30.getTag() + inforid); //修改之前删除原来的短信

                            //延时发出
                            sendTime1 = time4.toJdkDate();
                            SmsSendReq req = new SmsSendReq();
                            req.setType(1);
                            req.setPhone(ListUtil.of(it));
                            req.setMessage(sms1);
                            req.setSendTime( sendTime1 );
                            req.setTitle(InforMessageEnum.SENDSTATUS30.getTag() + inforid); //发送待签收短信 + inforid
                            smsQueApi.sendque(req);
                        }else {
                            //立即发出
                            Map<String,Object> map = new HashMap<>();
                            map.put("arg1", sms1);
                            smsSendService.sendSingleSms(it, null,null ,"admin-sms-login-new",map);
                        }

                    }


                });

            }
        }

        if(yeartag==0||yeartag==1){
//            LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
//            lambdaQueryWrapper.eq(UserinforDO::getInforid , inforid);
//            lambdaQueryWrapper.eq(UserinforDO::getUtype , 1);
//            List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
            log.info("临时年度租户id{}",SecurityFrameworkUtils.getTenantId());
            InforDO inforDO = inforMapper.selectInforDoById(inforid);
            List<UserinforDO> all=userinforMapper.selectUserinforDOList(inforid,1);
            List<Long> uids = all.stream().filter(Objects::nonNull).filter(it-> it.getUid()!=null).map(it -> Long.valueOf(it.getUid())).collect(Collectors.toList());
            CommonResult<List<AdminUserRespDTO>> userinfors  =  adminUserApi.getUsers(uids);
            if(userinfors!=null && userinfors.getData()!=null){

                List<String> mobiles = userinfors.getData().stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                        .collect(Collectors.toList());

                String sms =  "【湖南省委党校】您好！您有一件待签收的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时签收。"  ;

                String sms1 =  "【湖南省委党校】您好！您有一件即将逾期的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时进行处理。" ;

                mobiles.forEach(it -> {

                    if(status == 20){
                        DateTime time1 = DateUtil.parse(DateUtil.now());
                        DateTime time2 = DateUtil.offsetDay(DateUtil.parse(start), -1);
                        log.info("当前时间 {}",time1);
                        log.info("开始时间前一天 {}",time2);
                        Date sendTime = null;
                        if( time1.compareTo(time2) < 0 ){

                            deptApi.sendMessageRefresh(InforMessageEnum.SENDWAIT.getTag() + inforid); //修改之前删除原来的短信

                            //延时发出
                            sendTime = time2.toJdkDate();
                            SmsSendReq req = new SmsSendReq();
                            req.setType(1);
                            req.setPhone(ListUtil.of(it));
                            req.setMessage(sms);
                            req.setSendTime( sendTime );
                            req.setTitle(InforMessageEnum.SENDWAIT.getTag() + inforid); //发送待签收短信 + inforid
                            smsQueApi.sendque(req);
                            log.info("开始短信延迟发出");
                        }else {
                            //立即发出
                            Map<String,Object> map = new HashMap<>();
                            map.put("arg1", sms);
                            smsSendService.sendSingleSms(it, null,null ,"admin-sms-login-new",map);
                            log.info("开始短信立即发出");
                        }
                    }



                    if(status == 30){
                        DateTime time3 = DateUtil.parse(DateUtil.now());
                        DateTime time4 = DateUtil.offsetDay(DateUtil.parse(end), -1);

                        Date sendTime1 = null;
                        if( time3.compareTo(time4) < 0 ){

                            deptApi.sendMessageRefresh(InforMessageEnum.SENDSTATUS30.getTag() + inforid); //修改之前删除原来的短信

                            //延时发出
                            sendTime1 = time4.toJdkDate();
                            SmsSendReq req = new SmsSendReq();
                            req.setType(1);
                            req.setPhone(ListUtil.of(it));
                            req.setMessage(sms1);
                            req.setSendTime( sendTime1 );
                            req.setTitle(InforMessageEnum.SENDSTATUS30.getTag() + inforid); //发送待签收短信 + inforid
                            smsQueApi.sendque(req);
                        }else {
                            //立即发出
                            Map<String,Object> map = new HashMap<>();
                            map.put("arg1", sms1);
                            smsSendService.sendSingleSms(it, null,null ,"admin-sms-login-new",map);
                        }

                    }


                });

            }
        }


    }

    @Override
    public void deleteInfor(Integer id) {
        // 校验存在
        this.validateInforExists(id);
        // 删除
        inforMapper.deleteById(id);
    }

    private void validateInforExists(Integer id) {
        if (inforMapper.selectById(id) == null) {
            throw exception(INFOR_NOT_EXISTS);
        }
    }

    private InforDO validateInforExistsDao(Integer id) {
        InforDO inforDO = inforMapper.selectById(id);
        if (inforDO == null) {
            throw exception(INFOR_NOT_EXISTS);
        }else {
            return inforDO;
        }
    }

    @Override
    @TenantIgnore
    public InforDO getInfor(Integer id) {
        return inforMapper.selectById(id);
    }

    @Override
    public List<InforDO> getInforList(Collection<Integer> ids) {
        return inforMapper.selectBatchIds(ids);
    }

    @Override
    public List<InforRespVO> getInforList(InforPageReqVO pageReqVO) {
        List<InforDO> pageResult = inforMapper.selectList(pageReqVO);
        List<InforDO> inforDOS =getInforDOList(pageReqVO);
        //加入牵头领导数据
        if(pageResult==null||pageResult.size()==0){
            pageResult=inforDOS;
        }else {
            pageResult.addAll(inforDOS);
        }
        List<InforRespVO> inforDOPageResult =new ArrayList<>();
        if(  CollectionUtil.isNotEmpty(pageResult)){

            List<Integer> idlist = pageResult.stream().map(InforDO::getId)
                    .collect(Collectors.toList());

            if(CollectionUtil.isNotEmpty(idlist)){
                LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                lambdaQueryWrapper.in(UserinforDO::getInforid , idlist);
                List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);

//                List<UserinforDO> collect = all.stream().filter(a -> a.getUtype() == 4).collect(Collectors.toList());
                if(pageReqVO.getDeptId()!=null){
                    List<Integer> allChildrenDeptIds = selectAllChildrenDeptIds(pageReqVO.getDeptId());
                    List<Integer> deptids=new ArrayList<>();
                    deptids.add(pageReqVO.getDeptId());
                    if (allChildrenDeptIds.size()>0){
                        deptids.addAll(allChildrenDeptIds);
                    }
                    all=all.stream().filter(a->(a.getUtype()==0&&deptids.contains(a.getDptid())||a.getUtype()>0)).collect(Collectors.toList());
                }


                if(all!=null){

                    for (int i = 0; i < pageResult.size(); i++) {
                        InforDO it = pageResult.get(i);
                        InforRespVO inforRespVO =  InforConvert.INSTANCE.convert(it);
                        inforRespVO.setUserinforDOList( all.stream().
                                filter( uinfor ->
                                        uinfor.getInforid().intValue() ==  it.getId().intValue() )
                                .collect(Collectors.toList()));
//                        inforRespVO.setUserinforDOList(inforRespVO.getUserinforDOList().stream().filter(a->a.getUtype()==4).collect(Collectors.toList()));
                        if(pageReqVO.getMleaderId()!=null){
                            for (UserinforDO userinforDO : inforRespVO.getUserinforDOList()) {
                                if(userinforDO.getUtype()==4&&userinforDO.getUid().equals(pageReqVO.getMleaderId())){
                                    inforDOPageResult.add(inforRespVO);
                                    break;
                                }
                            }
                        }else {
                            inforDOPageResult.add(inforRespVO);
                        }
                    }



                }

            }



        }

        inforDOPageResult=inforDOPageResult.stream().distinct().collect(Collectors.toList());

        return inforDOPageResult;

    }

    public List<InforDO> getInforDOList(InforPageReqVO pageReqVO){
        List<InforDO> inforDOS =new ArrayList<>();

        Set<Integer> idset = getLeadleaders(4);

        Set<Integer> inforUtype = getLeadleaders(1);

        //筛选条件
        LambdaQueryWrapper<InforDO> lqw=getInforDOLambdaQueryWrapper(pageReqVO);

        //填报审批加入牵头领导数据
        if(idset.size()>0&&pageReqVO.getHhandle()!=null&&pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()==2){
            lqw.in(idset.size()>0,InforDO::getId,idset);
            lqw.eq(InforDO::getStatus,4);
            lqw.eq(InforDO::getHhandle,2);
            lqw.eq(InforDO::getYeartag,2);
            inforDOS = inforMapper.selectList(lqw);
        }

        //办理审批加入牵头领导数据
        if(idset.size()>0&&pageReqVO.getShandle()!=null&&pageReqVO.getShandle()>0&&pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()==2){
            lqw.in(idset.size()>0,InforDO::getId,idset);
            lqw.eq(InforDO::getStatus,30);
            lqw.eq(InforDO::getShandle,2);
            lqw.eq(InforDO::getYeartag,2);
            inforDOS = inforMapper.selectList(lqw);
        }

        //已审批加入牵头领导数据
        if(idset.size()>0&&pageReqVO.getApproval()!=null&&pageReqVO.getApproval()==1&&(pageReqVO.getYeartag()==null||(pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()==2))){
            lqw.in(idset.size()>0,InforDO::getId,idset);
            lqw.eq(InforDO::getLeaderhandle,1);
            lqw.eq(InforDO::getYeartag,2);
            inforDOS = inforMapper.selectList(lqw);
        }


        if(idset.size()>0&&((pageReqVO.getSusersee()!=null&&pageReqVO.getSusersee()==1)||
                (pageReqVO.getLeadersee()!=null&&pageReqVO.getLeadersee()==1)||
                (pageReqVO.getMastersee()!=null&&pageReqVO.getMastersee()==1))){
            lqw.in(idset.size()>0,InforDO::getId,idset)
                            .and(wrapper ->{

                wrapper.and(wrapper1 -> {
                            wrapper1.eq(InforDO::getStatus,30).eq(InforDO::getYeartag,2);
                        })


                        .or(wrapper2 ->{
                            wrapper2.eq(InforDO::getStatus,4).eq(InforDO::getYeartag,2);
                        });

            });
            inforDOS=inforMapper.selectList(lqw);

        }

       //进行中加入重点事项承办人为自己的数据
        if(inforUtype.size()>0&&pageReqVO.getStatusList()!=null&&pageReqVO.getStatusList().size()==2&&pageReqVO.getStatusList().get(0)==4
                &&pageReqVO.getStatusList().get(1)==30&&pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()==2){
            lqw.in(inforUtype.size()>0,InforDO::getId,inforUtype);
            lqw.in(InforDO::getStatus,4,30);
            lqw.eq(InforDO::getYeartag,2);
            inforDOS = inforMapper.selectList(lqw);
        }

        //进行中加入重点事项承办人为自己的数据，小程序待办只展示status为30的数据
        if(inforUtype.size()>0&&pageReqVO.getXcxTodo()==1){
            LambdaQueryWrapper<InforDO> cql=getInforDOLambdaQueryWrapper(pageReqVO);
            cql.in(inforUtype.size()>0,InforDO::getId,inforUtype);
            cql.eq(InforDO::getStatus,30);
            cql.eq(InforDO::getShandle,0);
            cql.eq(InforDO::getYeartag,2);
            List<InforDO> inforDOS1 = inforMapper.selectList(cql);
            if(inforDOS==null||inforDOS.size()==0){
                inforDOS=inforDOS1;
            }else {
                inforDOS.addAll(inforDOS1);
            }
        }

        //已办加入重点事项承办人为自己的数据
        if(pageReqVO.getSuperuserid()==null&&inforUtype.size()>0&&pageReqVO.getStatus()!=null&&pageReqVO.getStatus()==50&&pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()==2&&pageReqVO.getApproval()==0){
            lqw.in(inforUtype.size()>0,InforDO::getId,inforUtype);
            lqw.eq(InforDO::getStatus,50);
            lqw.eq(InforDO::getYeartag,2);
            inforDOS = inforMapper.selectList(lqw);
        }

        //已办加入牵头领导为自己的任务
        if(pageReqVO.getCompleted()==1&&idset.size()>0&&pageReqVO.getStatus()!=null&&pageReqVO.getStatus()==50&&pageReqVO.getYeartag()!=null){
            LambdaQueryWrapper<InforDO> cql=getInforDOLambdaQueryWrapper(pageReqVO);
            cql.in(InforDO::getId,idset);
            cql.eq(InforDO::getStatus,50);
            cql.eq(InforDO::getYeartag,pageReqVO.getYeartag());
            List<InforDO> inforDOS1 = inforMapper.selectList(cql);
            if(inforDOS==null||inforDOS.size()==0){
                inforDOS=inforDOS1;
            }else {
                inforDOS.addAll(inforDOS1);
            }
        }

        //因小程序传参问题专对小程序做处理，小程序已办加入重点事项承办人为自己的数据
        if(inforUtype.size()>0&&pageReqVO.getHandleDone()==1){
            LambdaQueryWrapper<InforDO> cql=getInforDOLambdaQueryWrapper(pageReqVO);
            cql.in(inforUtype.size()>0,InforDO::getId,inforUtype);
            cql.eq(InforDO::getStatus,50);
            cql.eq(InforDO::getYeartag,2);
            List<InforDO> inforDOS1 = inforMapper.selectList(cql);
            if(inforDOS==null||inforDOS.size()==0){
                inforDOS=inforDOS1;
            }else {
                inforDOS.addAll(inforDOS1);
            }
        }

        //因小程序传参问题专对小程序做处理，小程序已办加入重点事项承办人为自己的数据
        if(idset.size()>0&&pageReqVO.getHandleDone()==1){
            LambdaQueryWrapper<InforDO> cql=getInforDOLambdaQueryWrapper(pageReqVO);
            cql.in(idset.size()>0,InforDO::getId,idset);
            cql.eq(InforDO::getStatus,50);
            List<InforDO> inforDOS1 = inforMapper.selectList(cql);
            if(inforDOS==null||inforDOS.size()==0){
                inforDOS=inforDOS1;
            }else {
                inforDOS.addAll(inforDOS1);
            }
        }


        if(inforUtype.size()>0&&((pageReqVO.getSusersee()!=null&&pageReqVO.getSusersee()==1)||
                (pageReqVO.getLeadersee()!=null&&pageReqVO.getLeadersee()==1)||
                (pageReqVO.getMastersee()!=null&&pageReqVO.getMastersee()==1))){
            LambdaQueryWrapper<InforDO> cql=getInforDOLambdaQueryWrapper(pageReqVO);
            cql.in(inforUtype.size()>0,InforDO::getId,inforUtype);
            cql.in(InforDO::getStatus,4,30);
            cql.eq(InforDO::getYeartag,2);
            List<InforDO> inforDOS1 = inforMapper.selectList(cql);
            if(inforDOS==null||inforDOS.size()==0){
                inforDOS=inforDOS1;
            }else {
                inforDOS.addAll(inforDOS1);
            }
        }

        return inforDOS;
    }

    @Override
    public Set<Integer> getLeadleaders(Integer uType) {
        LambdaQueryWrapper<UserinforDO> userinforDOLambdaQueryWrapper=new LambdaQueryWrapper<>();
        userinforDOLambdaQueryWrapper.eq(UserinforDO::getUtype,uType);
        userinforDOLambdaQueryWrapper.eq(UserinforDO::getUid,SecurityFrameworkUtils.getLoginUserId().intValue());
        List<UserinforDO> userinforDOList =  userinforMapper.selectList(userinforDOLambdaQueryWrapper);
        Set<Integer> idset = userinforDOList.stream().map(UserinforDO::getInforid).collect(Collectors.toSet());
        return idset;
    }

    @Override
    public PageResult<InforRespVO> getInforPage(InforPageReqVO pageReqVO) {

        int start = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
        Integer size = pageReqVO.getPageSize();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(1000000);

        PageResult<InforDO> pageResult = inforMapper.selectPage(pageReqVO);
        List<InforDO> inforDOS =getInforDOList(pageReqVO);

        //年度，临时已办增加发起人是自己的数据
//        if(pageReqVO.getStatus()==50&&pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()!=2&&pageReqVO.isXcxdb()==false){
//            InforPageReqVO ipr = new InforPageReqVO();
//            BeanUtils.copyProperties(pageReqVO, ipr);
//            ipr.setMuserid(null);
//            ipr.setSuserid(pageReqVO.getMuserid());
//            ipr.setIdlist(null);
//            PageResult<InforDO> idpr = inforMapper.selectPage(ipr);
//            // 创建一个新的 ArrayList 对象，用于存储结果
//            List<InforDO> mergedList = new ArrayList<>(pageResult.getList());
//
//            // 将新查询到的列表中的元素添加到 mergedList 中
//            mergedList.addAll(idpr.getList());
//
//            // 更新 pageResult 中的列表为新的 mergedList
//            pageResult.setList(mergedList);
//
//            pageResult.setTotal((long) pageResult.getList().size());
//
//        }

        //小程序我发起的增加月度的提交数据
        if(pageReqVO.getIsXcx()&&(pageReqVO.getYeartag()==null||pageReqVO.getYeartag()==2)){
            //如果yeartag为2,则筛选只为2的，去除其它的
            if(pageReqVO.getYeartag()!=null&&pageReqVO.getYeartag()==2){
                pageResult.getList().clear();
                pageResult.setTotal(0L);
            }
            InforPageReqVO ipr = new InforPageReqVO();
            BeanUtils.copyProperties(pageReqVO, ipr);
            ipr.setYeartag(2);
            ipr.setStatuslecode(50);
            PageResult<InforDO> inforDOPageResult = inforMapper.selectPage(ipr);
            //过滤草稿数据
            inforDOPageResult.setList(inforDOPageResult.getList().stream().filter(f->f.getStatus()!=1).collect(Collectors.toList()));
            inforDOPageResult.setTotal((long) inforDOPageResult.getList().size());

            List<InforDO> mergedList = new ArrayList<>(pageResult.getList());

            // 将新查询到的列表中的元素添加到 mergedList 中
            mergedList.addAll(inforDOPageResult.getList());

            // 更新 pageResult 中的列表为新的 mergedList
            pageResult.setList(mergedList);
            pageResult.setTotal(pageResult.getTotal()+inforDOPageResult.getTotal());
//            pageResult.setList(pageResult.getList().stream().sorted(Comparator.comparing(InforDO::getCreateTime,Comparator.reverseOrder())).collect(Collectors.toList()));

        }

        //小程序待办
        if (pageReqVO.getXcxTodo() == 1 && (pageReqVO.getYeartag() == null || pageReqVO.getYeartag() == 2)) {
            pageResult.setList(pageResult.getList().stream().filter(p -> p.getYeartag() != 2).collect(Collectors.toList()));
            PageResult<InforDO> idpr=new PageResult<>();
            if(!judgeRole().getIsHeadmaster()&&(judgeRole().getIsAdmin()||judgeRole().getReportOfficer()||judgeRole().getIsDirector())){
                InforPageReqVO ipr = new InforPageReqVO();
                BeanUtils.copyProperties(pageReqVO, ipr);
                ipr.setYeartag(2);
                ipr.setMuserid(null);
                ipr.setXcxdb(false);
                ipr.setXcxTodo(0);
                if (judgeRole().getIsAdmin()){
                    ipr.setSuseridLeader(pageReqVO.getMuserid());
                }else if(judgeRole().getReportOfficer()){
                    ipr.setSuserid(pageReqVO.getMuserid());
                }else if(judgeRole().getIsDirector()){
                    ipr.setSuserid(pageReqVO.getMuserid());
//                    ipr.setMuseridLeader(pageReqVO.getMuserid());
                }
                ipr.setShandle(0);
                ipr.setIdlist(null);
                idpr = inforMapper.selectPage(ipr);
            }
            List<InforDO> mergedList = new ArrayList<>(pageResult.getList());

            if(idpr.getList()!=null){
                // 将新查询到的列表中的元素添加到 mergedList 中
                mergedList.addAll(idpr.getList());

                // 更新 pageResult 中的列表为新的 mergedList
                pageResult.setList(mergedList);
                pageResult.setTotal((long) pageResult.getList().size());
            }
        }

        //小程序待审批（填报审批和办理情况审批放在一起）
        if (pageReqVO.getXcxApproval() == 1 && (pageReqVO.getYeartag() == null || pageReqVO.getYeartag() == 2)) {

            InforPageReqVO ipr = new InforPageReqVO();
            BeanUtils.copyProperties(pageReqVO, ipr);
            ipr.setStatus(30);
            ipr.setHhandle(null);
            if (pageReqVO.getHhandle() == 0) {
                ipr.setShandle(1);
            } else if (pageReqVO.getHhandle() == 1) {
                ipr.setShandle(3);
            }
            PageResult<InforDO> idpr = inforMapper.selectPage(ipr);
            // 创建一个新的 ArrayList 对象，用于存储结果
            List<InforDO> mergedList = new ArrayList<>(pageResult.getList());

           // 将新查询到的列表中的元素添加到 mergedList 中
            mergedList.addAll(idpr.getList());

           // 更新 pageResult 中的列表为新的 mergedList
            pageResult.setList(mergedList);

            pageResult.setTotal((long) pageResult.getList().size());
        }

        //小程序我督办
        if(pageReqVO.getXcxSupervise()==1&& (pageReqVO.getYeartag() == null || pageReqVO.getYeartag() == 2)){
            pageResult.setList(pageResult.getList().stream().filter(p -> p.getYeartag() != 2).collect(Collectors.toList()));
            InforPageReqVO ipr = new InforPageReqVO();
            BeanUtils.copyProperties(pageReqVO, ipr);
            ipr.setYeartag(2);
            ipr.setNeeddraft(1);
            PageResult<InforDO> idpr = inforMapper.selectPage(ipr);
            // 创建一个新的 ArrayList 对象，用于存储结果
            List<InforDO> mergedList = new ArrayList<>(pageResult.getList());

            // 将新查询到的列表中的元素添加到 mergedList 中
            mergedList.addAll(idpr.getList());

            // 更新 pageResult 中的列表为新的 mergedList
            pageResult.setList(mergedList);

            pageResult.setTotal((long) pageResult.getList().size());
        }

        //小程序已办
        if (pageReqVO.getXcxDone() == 1 && (pageReqVO.getYeartag() == null || pageReqVO.getYeartag() == 2)) {
            pageResult.setList(pageResult.getList().stream().filter(p -> p.getYeartag() != 2).collect(Collectors.toList()));
            InforPageReqVO ipr = new InforPageReqVO();
            BeanUtils.copyProperties(pageReqVO, ipr);
            ipr.setYeartag(2);
            ipr.setMuserid(null);
            ipr.setXcxdb(false);
            if (judgeRole().getIsAdmin()){
                ipr.setSuseridLeader(pageReqVO.getMuserid());
            }else if(judgeRole().getReportOfficer()){
                ipr.setSuserid(pageReqVO.getMuserid());
            }else if(judgeRole().getIsDirector()){
                ipr.setMuseridLeader(pageReqVO.getMuserid());
            }
            ipr.setIdlist(null);
            ipr.setNeeddraft(1);
            inforController.paevoconvert(ipr);
            PageResult<InforDO> idpr = inforMapper.selectPage(ipr);
            List<InforDO> mergedList = new ArrayList<>(pageResult.getList());

            // 将新查询到的列表中的元素添加到 mergedList 中
            mergedList.addAll(idpr.getList());

            // 更新 pageResult 中的列表为新的 mergedList
            pageResult.setList(mergedList);
            pageResult.setTotal((long) pageResult.getList().size());
        }

        //status=30 and is_complete>0 and year_tag = 2 AND tenant_id = 25 and launch_user_id=9482
        //已办加入办理了的数据
        List<InforDO> inforDOS1=new ArrayList<>();
        if(pageReqVO.getHandleDone()==1){
            LambdaQueryWrapper<InforDO> inforlqw = getInforDOLambdaQueryWrapper(pageReqVO);

            inforlqw.eq(InforDO::getStatus,30);
            inforlqw.eq(InforDO::getYeartag,2);
            inforlqw.eq(InforDO::getSuserid,getLoginUserId().intValue());
            inforlqw.and(wrapper->{
                wrapper.gt(InforDO::getShandle,0)
                        .or(wrapper1->{
                            wrapper1.eq(InforDO::getShandle,0);
                            wrapper1.eq(InforDO::getIsHandle,1);
                        });
            });

            inforDOS1 = inforMapper.selectList(inforlqw);

            LambdaQueryWrapper<InforDO> inforDOLambdaQueryWrapper=getInforDOLambdaQueryWrapper(pageReqVO);

            inforDOLambdaQueryWrapper.eq(InforDO::getStatus,50);
            inforDOLambdaQueryWrapper.eq(InforDO::getYeartag,2);
            inforDOLambdaQueryWrapper.eq(InforDO::getSuserid,getLoginUserId().intValue());
            List<InforDO> inforDOS2 = inforMapper.selectList(inforDOLambdaQueryWrapper);

            if(inforDOS1==null||inforDOS1.size()==0){
                inforDOS1=inforDOS2;
            }else {
                inforDOS1.addAll(inforDOS2);
            }
        }


        //加入牵头领导数据
        List<InforDO> mergedList = pageResult.getList();
        if(mergedList==null||mergedList.size()==0){
            pageResult.setList(inforDOS);

        }else {
            mergedList.addAll(inforDOS);
            mergedList=mergedList.stream().distinct().collect(Collectors.toList());
            pageResult.setList(mergedList);
        }

        //已办加入办理了的数据
        List<InforDO> mergedList1 = pageResult.getList();
        if(mergedList1==null||mergedList1.size()==0){
            pageResult.setList(inforDOS1);

        }else {
            mergedList1.addAll(inforDOS1);
            mergedList1=mergedList1.stream().distinct().collect(Collectors.toList());
            pageResult.setList(mergedList1);
        }



        //提交页过滤sshandle=0的数据
        if(pageReqVO.getStatuslecode()!=null&&pageReqVO.getStatuslecode()==30){
            pageResult.setList(pageResult.getList().stream().filter(p->(p.getStatus()==30&&p.getShandle()!=0)||p.getStatus()<30).collect(Collectors.toList()));
            pageResult.setTotal((long) pageResult.getList().size());
        }

        PageResult<InforRespVO> inforDOPageResult =new PageResult<>();
        if(pageResult!=null&& CollectionUtil.isNotEmpty(pageResult.getList())){
                List<InforDO> pagelist = pageResult.getList() ;
                List<Integer> idlist = pagelist.stream().map(InforDO::getId)
                    .collect(Collectors.toList());

                if(CollectionUtil.isNotEmpty(idlist)){
                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                    lambdaQueryWrapper.in(UserinforDO::getInforid , idlist);
                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
                    //获取子部门的所有id
                    if(pageReqVO.getDeptId()!=null){
                        List<Integer> allChildrenDeptIds = selectAllChildrenDeptIds(pageReqVO.getDeptId());
                        List<Integer> deptids=new ArrayList<>();
                        deptids.add(pageReqVO.getDeptId());
                        if (allChildrenDeptIds.size()>0){
                            deptids.addAll(allChildrenDeptIds);
                        }
                        all=all.stream().filter(a->(a.getUtype()==0&&deptids.contains(a.getDptid())||a.getUtype()>0)).collect(Collectors.toList());
                    }


                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime now = LocalDateTime.now();


                    List<InforRespVO> respVOS = new ArrayList<>() ;
                    inforDOPageResult.setTotal(pageResult.getTotal());


                    if(all!=null){
                        for (InforDO it : pagelist) {
                            InforRespVO inforRespVO =  InforConvert.INSTANCE.convert(it);
                            if(it.getYeartag()==2&&it.getStatus()==30){
                                LocalDateTime starttime = LocalDateTime.parse(it.getStarttime(),formatter);
                                LocalDateTime endtime = LocalDateTime.parse(it.getEndtime(),formatter);
//                                LocalDateTime currentDateTime = LocalDateTime.now();

                                long totalDurationMinutes = ChronoUnit.MINUTES.between(starttime, endtime);
                                long eightyPercentRange = (long) (totalDurationMinutes * 0.8);

                                LocalDateTime eightyPercentStart = starttime.plusMinutes(eightyPercentRange);
                                //任务进展为正常
                                if(now.isBefore(eightyPercentStart)){
                                    inforRespVO.setTaskProgress(1);
                                }
                                //任务进展为告警
                                else if (now.isAfter(eightyPercentStart)&&now.isBefore(endtime)){
                                    inforRespVO.setTaskProgress(2);
                                }
                                //任务进展为逾期
                                else {
                                    inforRespVO.setTaskProgress(3);
                                }
                            }
//                            if(now.isAfter(endtime)){
//                                inforRespVO.setTaskProgress(3);
//                            }
                            inforRespVO.setYeartag(it.getYeartag());
                            inforRespVO.setUserinforDOList( all.stream().
                                    filter( uinfor ->
                                            uinfor.getInforid().intValue() ==  it.getId().intValue() )
                                    .collect(Collectors.toList()));
                            respVOS.add(inforRespVO);
                        }

                    }
                    List<InforRespVO> respVOList = new ArrayList<>() ;
                    inforDOPageResult.setList(respVOS);

                    //过滤任务进展数据
                    if(pageReqVO.getTaskProgress()!=null&&(pageReqVO.getTaskProgress()==1||pageReqVO.getTaskProgress()==2||pageReqVO.getTaskProgress()==3)){
                        respVOList=respVOS.stream().filter(inforRespVO -> pageReqVO.getTaskProgress().equals(inforRespVO.getTaskProgress())).collect(Collectors.toList());
                        inforDOPageResult.setList(respVOList);
                        inforDOPageResult.setTotal( (long)respVOList.size());
                    }
                    if(pageReqVO.getIsUnstarted()!=null&&pageReqVO.getIsUnstarted()==1){
                        respVOList=respVOS.stream().filter(v->LocalDateTime.parse(v.getStarttime(), formatter).isAfter(now)).collect(Collectors.toList());
                        inforDOPageResult.setList(respVOList);
                        inforDOPageResult.setTotal( (long) respVOList.size());
                    }

                    //如果userinforDOList里没有utype=0的数据，则将去除(牵头部门筛选)
                    if(pageReqVO.getDeptId()!=null){
                        Boolean isRemove=true;
//                        PageResult<InforRespVO> temp =inforDOPageResult;
                        List<InforRespVO> temp=inforDOPageResult.getList();
                        List<InforRespVO> removeTemp=new ArrayList<>();
                        for (InforRespVO inforRespVO : temp) {
                            List<UserinforDO> temp1=inforRespVO.getUserinforDOList();
                            for (UserinforDO userinforDO : temp1) {
                                if(userinforDO.getUtype()==0){
                                    isRemove=false;
                                    break;
                                }
                            }
                            if(isRemove){
                                removeTemp.add(inforRespVO);
                            }
                            isRemove=true;
                        }
                        inforDOPageResult.getList().removeAll(removeTemp);
                        inforDOPageResult.setTotal(inforDOPageResult.getTotal()-removeTemp.size());
                    }
                }

            if(pageReqVO.getOrder()==0){
                inforDOPageResult.setList(inforDOPageResult.getList().stream().sorted(Comparator.comparing(InforRespVO::getCreateTime,Comparator.reverseOrder())).collect(Collectors.toList()));
//                pageResult.setList(pageResult.getList().stream().sorted(Comparator.comparing(InforDO::getCreateTime,Comparator.reverseOrder())).collect(Collectors.toList()));
            } else if(pageReqVO.getOrder()==1){
                inforDOPageResult.setList(inforDOPageResult.getList().stream().sorted(Comparator.comparing(InforRespVO::getUpdateTime,Comparator.reverseOrder())).collect(Collectors.toList()));
//                pageResult.setList(pageResult.getList().stream().sorted(Comparator.comparing(InforDO::getUpdateTime,Comparator.reverseOrder())).collect(Collectors.toList()));
            }

            if(pageReqVO.getHandleDone()==1){
                List<InforRespVO> inforRespVOList = inforDOPageResult.getList().stream().filter(inforRespVO -> inforRespVO.getStatus() != 1).collect(Collectors.toList());
                inforDOPageResult.setList(inforRespVOList);
            }

            inforDOPageResult.setTotal((long)inforDOPageResult.getList().size());

            List<InforRespVO> result = new ArrayList<>();
            int i = 0;
            for (InforRespVO inforRespVO : inforDOPageResult.getList()) {
                if (i >= start + size) {
                    break;
                }
                if (i >= start) {
                    result.add(inforRespVO);
                }
                i++;
            }
            if(pageReqVO.getOrder()==0){
                result=result.stream().sorted(Comparator.comparing(InforRespVO::getCreateTime,Comparator.reverseOrder())).collect(Collectors.toList());
            } else if(pageReqVO.getOrder()==1){
                result=result.stream().sorted(Comparator.comparing(InforRespVO::getUpdateTime,Comparator.reverseOrder())).collect(Collectors.toList());
            }
            inforDOPageResult.setList(result);


//            Integer pageStart=(pageReqVO.getPageNo()-1)*pageReqVO.getPageSize();
//            Integer pageEnd=pageStart+pageReqVO.getPageSize();
//            if(inforDOPageResult.getTotal()<pageEnd){
//                    pageEnd= Math.toIntExact(inforDOPageResult.getTotal());
//            }
//
//
//            inforDOPageResult.setList(inforDOPageResult.getList().subList(pageStart,pageEnd));

            return inforDOPageResult;
        }else {
            inforDOPageResult.setList(new ArrayList<>());
            inforDOPageResult.setTotal(0l);
            return inforDOPageResult;
        }


    }

    private LambdaQueryWrapper<InforDO> getInforDOLambdaQueryWrapper(InforPageReqVO pageReqVO) {
        LambdaQueryWrapper<InforDO> inforlqw=new LambdaQueryWrapperX<InforDO>()
                .likeIfPresent(InforDO::getSusername, pageReqVO.getSusername())
                .likeIfPresent(InforDO::getSdepname, pageReqVO.getSdepname())
                .and(StrUtil.isNotEmpty(pageReqVO.getTaskname()), wrapper ->{

                    wrapper.like(InforDO::getTaskname, pageReqVO.getTaskname())
                            .or()
                            .like(InforDO::getTaskcontent, pageReqVO.getTaskname());

                })
                .likeIfPresent(InforDO::getTaskcontent, pageReqVO.getTaskcontent())

                .eqIfPresent(InforDO::getTasktype, pageReqVO.getTasktype())
                .eqIfPresent(InforDO::getStatus, pageReqVO.getStatus())
                .geIfPresent(InforDO::getCreateTime, pageReqVO.getStarttime())
                .eqIfPresent(InforDO::getSdeptid, pageReqVO.getSdeptid())
                .leIfPresent(InforDO::getCreateTime, pageReqVO.getEndtime() )
                .eqIfPresent(InforDO::getSuserid, pageReqVO.getPromoter())
                .leIfPresent(InforDO::getCreateTime, pageReqVO.getSendtime() )
                .geIfPresent(InforDO::getEndtime, pageReqVO.getSstarttime())
                .eqIfPresent(InforDO::getShandle, pageReqVO.getShandleEq())
                .neIfPresent(InforDO::getShandle, pageReqVO.getShandleNe());
        return inforlqw;
    }

    @Override
    public List<InforDO> getInforList(InforExportReqVO exportReqVO) {
        return inforMapper.selectList(exportReqVO);
    }


    public  List<Integer> selectAllChildrenDeptIds(Integer parentId){
        List<Integer> allChildrenDeptIds = inforMapper.selectAllChildrenDeptIds(parentId);
        return allChildrenDeptIds;
    }

    public InforRoleVO judgeRole() {
        InforRoleVO inforRoleVO = new InforRoleVO();
        AdminUserRespDTO userRespDTO = adminUserApi.getUser(getLoginUserId()).getCheckedData();
        DeptRespDTO parentDept = deptApi.getDept(userRespDTO.getDeptId()).getCheckedData();
        //判断是否为处长
        if (parentDept.getLeaderUserId() != null && (parentDept.getLeaderUserId().intValue() == userRespDTO.getId())) {
            inforRoleVO.setIsDirector(true);
        }
        Long uid = SecurityFrameworkUtils.getLoginUserId();
        List<Long> roletempDOList = userinforMapper.selectByTenantId(SecurityFrameworkUtils.getTenantId());
        //判断是否为管理员
        if (roletempDOList.contains(uid)) {
            inforRoleVO.setIsAdmin(true);
        }
//        System.out.println(SecurityFrameworkUtils.getTenantId());
        List<Long> reportOfficerList = userinforMapper.selectReportOfficerByTenantId(SecurityFrameworkUtils.getTenantId());
        //判断是否为中心工作填报员
        if (reportOfficerList.contains(uid)) {
            inforRoleVO.setReportOfficer(true);
        }

        List<Long> headmasterList = userinforMapper.selectPrincipalByTenantId(getTenantId());
        //判断是否为副校长
        if(headmasterList.contains(uid)){
            inforRoleVO.setIsHeadmaster(true);
        }

        List<Long> leaderList = userinforMapper.selectLeaderByTenantId(getTenantId());
        //判断是否为牵头领导
        if(leaderList.contains(uid)){
            inforRoleVO.setIsLeader(true);
        }

        return inforRoleVO;
    }

}
