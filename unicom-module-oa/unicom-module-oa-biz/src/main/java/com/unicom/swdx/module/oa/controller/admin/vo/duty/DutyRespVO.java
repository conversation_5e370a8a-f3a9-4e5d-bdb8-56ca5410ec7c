package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.unicom.swdx.module.oa.controller.admin.vo.schedule.ScheduleBaseVO;
import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("办公OA - 寒暑假坐值班详情 Response VO")
public class DutyRespVO extends DutyBaseVO {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("taskId")
    private String taskId;

    @ApiModelProperty("processInstanceId")
    private String processInstanceId;

    @ApiModelProperty("填报人姓名")
    private String userNickName;

    @ApiModelProperty("填报人姓名id")
    private Long userId;

    @ApiModelProperty("填报处室名")
    private String deptName;

    @ApiModelProperty("填报处室id")
    private Long deptId;


    @ApiModelProperty(value = "状态", notes = "参见 bpm_process_instance_result 枚举")
    private Integer result;

    @ApiModelProperty(value = "发起时间")
    private String launchTime;

    @ApiModelProperty(value = "当前查看的任务节点名称")
    private String taskName;

    @ApiModelProperty(value = "审批/选择审批人/假期变更按钮的出现与否")
    private Integer operateType;

    @ApiModelProperty(value = "状态", notes = "参见 bpm_process_instance_result 枚举")
    private Integer status;

    private String type;

    /**
     * 寒暑假坐值班表单列表
     */
    @ApiModelProperty("寒暑假坐值班表单列表")
    private List<DutyFormBaseVO> dutyFormList;

    @ApiModelProperty("寒暑假坐班表单列表")
    private List<DutyFormBaseVO> dutyFormSitList;

    @ApiModelProperty("寒暑假值班表单列表")
    private List<DutyFormBaseVO> dutyFormDepList;

    @ApiModelProperty("类型名称")
    private String categoryName = "寒暑假坐值班";

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 部门负责人审批时间
     */
    private LocalDateTime majorEndTime;

    /**
     * 部门负责人审批签名链接
     */
    private String majorHandSignature;
}
