package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 外出讲学审批 DO
 */
@TableName(value = "oa_out_report", autoResultMap = true)
@KeySequence("oa_out_report_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutReportDO extends TenantBaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 用户id
     */
    @TableField(value = "launch_user_id")
    private Long userId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 外出地点
     */
    private String outAddress;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 时长（天）
     */
    private Integer duration;

    /**
     * 外出内容
     */
    private String outContent;

    /**
     * 文件
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> files;

    /**
     * 签字
     */
    private String sign;

    /**
     * 是否草稿
     */
    private Boolean isDraft;

    /**
     * 发起时间
     */
    private LocalDateTime launchTime;

    /**
     * 报备的结果
     */
    private Integer result;

    /**
     * 是否销假
     */
    private Boolean isDealt;

    /**
     * 请假天数
     */
    private Integer day;

    /**
     * 销假日期
     */
    private LocalDateTime dealTime;

//    /**
//     * 返回工作日期
//     */
//    private LocalDateTime returnWorkTime;

}
