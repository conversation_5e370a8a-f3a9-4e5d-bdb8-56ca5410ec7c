package com.unicom.swdx.module.oa.controller.admin.vo.schedule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("办公OA - 一周工作安排发起审批 Request VO")
public class ScheduleCreateReqVO extends ScheduleBaseVO {
    @ApiModelProperty(value = "主键",notes = "当有草稿时必传")
    private Long id;

    @ApiModelProperty(value = "重新发起时必传流程id")
    private String processInstanceId;
}
