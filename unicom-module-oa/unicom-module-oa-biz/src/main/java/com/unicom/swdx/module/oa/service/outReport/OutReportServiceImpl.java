package com.unicom.swdx.module.oa.service.outReport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.bpm.api.task.BpmProcessInstanceApi;
import com.unicom.swdx.module.bpm.api.task.BpmTaskServiceApi;
import com.unicom.swdx.module.bpm.api.task.dto.*;
import com.unicom.swdx.module.oa.api.ReceiveApi;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OADealOutReportVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.common.OApickApprovalsReqVO;
import com.unicom.swdx.module.oa.convert.OutReportConvert;
import com.unicom.swdx.module.oa.dal.dataobject.DraftDO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;
import com.unicom.swdx.module.oa.dal.kingbase.LectureMapper;
import com.unicom.swdx.module.oa.dal.kingbase.OutReportMapper;
import com.unicom.swdx.module.oa.enums.OACategoryConstants;
import com.unicom.swdx.module.oa.service.OACommonService;
import com.unicom.swdx.module.oa.service.OATaskService;
import com.unicom.swdx.module.oa.service.draft.DraftService;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.PostApi;
import com.unicom.swdx.module.system.api.dept.dto.PostRespDTO;
import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.*;
import java.util.*;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.oa.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.oa.enums.PostTypeEnum.*;

@Service
//@Slf4j
public class OutReportServiceImpl extends ServiceImpl<OutReportMapper, OutReportDO> implements OutReportService {

    /**
     * 外出讲学 对应的流程定义 KEY
     */
    public static final String PROCESS_KEY = "OA-OutReport";

    @Resource
    private OutReportMapper outReportMapper;

    @Resource
    private DraftService draftService;

    @Resource
    private OATaskService oaTaskService;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private BpmTaskServiceApi bpmTaskServiceApi;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private PostApi postApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private BpmTaskServiceApi taskServiceApi;

    @Resource
    private SmsSendApi smsSendApi;

    @Resource
    private LectureMapper lectureMapper;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Resource
    private ReceiveApi receiveApi;

    /**
     * key = 岗位标识，value = postType
     */
    private Map<String, Integer> postCodeMap;
    private List<String> postCodeList;

    private Map<String, Integer> postCodeDealMap;
    private List<String> postDealCodeList;
    @PostConstruct
    public void init() {
        postCodeMap = new HashMap<>();
        postCodeMap.put(PROVINCE_LEADER.getPostCode(), 1); // 省管领导干部  1
        postCodeMap.put(INSPECTOR_1.getPostCode(), 1); // 一级巡视员  1
        postCodeMap.put(INSPECTOR_2.getPostCode(), 1); // 二级巡视员  1
        postCodeMap.put(SCHOOL_LEADER.getPostCode(), 1);//校（院）领导 1
        postCodeMap.put(DIRECTOR.getPostCode(), 2); // 主任  2
        postCodeMap.put(VICE_DIRECTOR.getPostCode(), 3); // 副主任  3
        postCodeMap.put(CLERK.getPostCode(), 4); // 其他人员，对应科级及以下  4

        postCodeList = CollUtil.newArrayList(postCodeMap.keySet());

        postCodeDealMap = new HashMap<>();
        postCodeDealMap.put(HR_OUT_REPORT_DEAL.getPostCode(), 1); // 人事部外出报告备案负责人  1
        postCodeDealMap.put(OFFICE_OUT_REPORT_DEAL.getPostCode(), 2); // 办公室外出报告备案负责人  2
        postDealCodeList= CollUtil.newArrayList(postCodeDealMap.keySet());
    }

    /**
     * 保存草稿
     * @param reqVO 外出讲学
     * @return 草稿id
     */
    @Override
    public Long saveDraft(OutReportCreateReqVO reqVO) {
        OutReportDO outReport = OutReportConvert.INSTANCE.convert(reqVO);
        if (Objects.isNull(outReport.getId())) {
            outReport.setUserId(getLoginUserId());
            AdminUserRespDTO userDTO = userApi.getUser(outReport.getUserId()).getCheckedData();
            if(reqVO.getDeptId() != null) {
                outReport.setDeptId(reqVO.getDeptId());
            } else {
                outReport.setDeptId(userDTO.getDeptId());
            }
            outReport.setIsDraft(true);
            baseMapper.insert(outReport);

            // 写入草稿表
            DraftDO draftDO = new DraftDO();
            draftDO.setCategory("12");
            draftDO.setUserId(outReport.getUserId().toString());
            draftDO.setItemId(outReport.getId());
            draftDO.setUpdateTime(LocalDateTime.now());
            draftService.save(draftDO);
        } else {
            baseMapper.updateById(outReport);
            draftService.update(new LambdaUpdateWrapper<DraftDO>().eq(DraftDO::getItemId,outReport.getId())
                    .set(DraftDO::getUpdateTime,LocalDateTime.now()));
        }

        return outReport.getId();
    }

    @Override
    public Integer getPostType(Long userId) {
        Integer postType = null;
        // 获取岗位类型
        PostRespDTO post = postApi.getMinSortPostByUser(userId, postCodeList);
        if (Objects.isNull(post)) {

            //当作其他人员
            post=new PostRespDTO();
            post.setCode("4");

            //岗位不符直接抛出异常，不让发起流程
            throw exception(POST_ERROR_OUT_REPORT);
        }
        postType = postCodeMap.get(post.getCode());
        return postType;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createOutReportProcess(OutReportCreateReqVO reqVO) {
        Integer postType = getPostType(getLoginUserId());
        OutReportDO outReport = OutReportConvert.INSTANCE.convert(reqVO);
        long day = Duration.between(LocalDateTime.of(reqVO.getStartDate(), LocalTime.MIN),
                LocalDateTime.of(reqVO.getEndDate(), LocalTime.MIN)).toDays()+1;
        outReport.setDuration((int) day);
        outReport.setIsDraft(false);
        outReport.setUserId(getLoginUserId());
        AdminUserRespDTO userDTO = userApi.getUser(outReport.getUserId()).getCheckedData();
        if(reqVO.getDeptId() != null) {
            outReport.setDeptId(reqVO.getDeptId());
        }else {
            outReport.setDeptId(userDTO.getDeptId());
        }
        outReport.setLaunchTime(LocalDateTime.now());
        if (Objects.isNull(outReport.getId())) {
            // 新增
            this.save(outReport);
        } else {
            // id不为空表示从草稿箱发起，可能是未发起过的草稿，也可能是撤回后的草稿
            // 删除草稿记录
            draftService.deleteByItemId(OACategoryConstants.OUTREPORT, outReport.getId());
            if(Objects.isNull(this.getById(outReport.getId()).getProcessInstanceId())){
                // 未发起的草稿，直接修改
                this.updateById(outReport);
            }else {
                // 撤回后的草稿，新增一条新的
                outReport.setId(null);
                this.save(outReport);
            }
        }

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        processInstanceVariables.put("postType", postType);
        String processInstanceId = processInstanceApi.createProcessInstance(outReport.getUserId(),
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(outReport.getId()))).getCheckedData();

        LocalDateTime time = LocalDateTime.now().withSecond(0);
        if(processInstanceApi.skipFirstTask(processInstanceId, time).getCheckedData()){
            // 将工作流的编号，更新到 OA 外出讲学表单中
            baseMapper.updateById(new OutReportDO().setId(outReport.getId()).setProcessInstanceId(processInstanceId));

            //判断下一步是否为选择审批人
            if(postType!=3){
                OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
                pick.setUserIds(reqVO.getUserIds());
                pick.setChargeLeaderSeq(reqVO.getChargeLeaderSeq());
                oaTaskService.pickApprovals(pick, time.plusSeconds(1L));
            }else {
                //不需要选择审批人时自动设置部门负责人审批
                //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
                if(reqVO.getDeptId() != null) {
                    Long defaultLeader = deptApi.getDept(userDTO.getDeptId()).getCheckedData().getLeaderUserId();
                    List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
                    BpmTaskRespDTO task = bpmTasks.get(1);
                    Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                    bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                            .setAssigneeUserId(currentLeader)
                            .setId(task.getId()));
                }
                String deptLeaderName = oaTaskService.getDeptLeader(outReport.getDeptId());
                if(StrUtil.isEmpty(deptLeaderName)){
                    throw exception(APPROVAL_NOT_FOUND);
                }
            }
        }

//        return outReport.getId();
        return processInstanceId;
    }

    @Override
    public boolean checkTime(OutReportCreateReqVO reqVO){


        List<OutReportDO> outReportDOList=outReportMapper.selectListByUserId(getLoginUserId());
        boolean result=true;

        for(OutReportDO a:outReportDOList){
            //开始时间小于等于截止时间，且截止时间大于等于开始时间，这样就有交叉
            if((reqVO.getStartDate().isBefore(a.getEndDate())
                    || reqVO.getStartDate().isEqual(a.getEndDate()))
                    && (reqVO.getEndDate().isAfter(a.getStartDate())
                    || reqVO.getEndDate().isEqual(a.getStartDate()))) {
                    result = false;
            }
        }
        return result;
    }


    @Override
    public boolean checkTime2(OutReportCreateReqVO reqVO){

        boolean result=true;

        if(reqVO.getStartDate().isAfter(reqVO.getEndDate())){
            result=false;
        }

        return result;
    }

    @Override
    public OutReportDO getByProcessInstanceId(String processInstanceId) {
        return baseMapper.selectOne(OutReportDO::getProcessInstanceId,processInstanceId);
    }

    @Override
    public OutReportDO get(Long id, String processInstanceId) {
        if (Objects.nonNull(id)) {
            return this.getById(id);
        } else if (CharSequenceUtil.isNotBlank(processInstanceId)) {
            return getByProcessInstanceId(processInstanceId);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartOutReport(Long loginUserId, OutReportCreateReqVO reqVO) {
        // 发起人重新发起审批
        // 1.保存新一份修改的信息
        OutReportDO outReport = OutReportConvert.INSTANCE.convert(reqVO);
        long day = Duration.between(LocalDateTime.of(reqVO.getStartDate(), LocalTime.MIN),
                LocalDateTime.of(reqVO.getEndDate(), LocalTime.MIN)).toDays()+1;
        outReport.setDuration((int) day);
        outReport.setIsDraft(false);
        outReport.setUserId(getLoginUserId());
        AdminUserRespDTO userDTO = userApi.getUser(outReport.getUserId()).getCheckedData();
        if(reqVO.getDeptId() != null) {
            outReport.setDeptId(reqVO.getDeptId());
        }else {
            outReport.setDeptId(userDTO.getDeptId());
        }

        if (Objects.isNull(outReport.getId())) {
            throw exception(LECTURE_NOT_EXIST);
        } else {
            // 修改
            this.updateById(outReport);
            if(Boolean.TRUE.equals(reqVO.getIsDraft())) {
                draftService.deleteByItemId(OACategoryConstants.OUTREPORT, outReport.getId());
            }
            // 2.设置一下流程流转参数variables，参数没有改变的不用设置
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 3.重新发起流程
            String processInstanceId = reqVO.getProcessInstanceId();
            if(Objects.isNull(reqVO.getProcessInstanceId())){
                processInstanceId=this.getById(reqVO.getId()).getProcessInstanceId();
//                log.info("---------------processInstanceId:"+processInstanceId);
            }
            BpmRestartDTO bpmRestartDTO = new BpmRestartDTO();
            bpmRestartDTO.setLoginUserId(loginUserId);
            bpmRestartDTO.setProcessInstanceId(processInstanceId);
            bpmRestartDTO.setVariables(processInstanceVariables);
            LocalDateTime time = LocalDateTime.now();
            bpmRestartDTO.setTime(time);
            if(bpmTaskServiceApi.restartProcess(bpmRestartDTO).getCheckedData()){
                Integer postType = getPostType(loginUserId);
                //判断下一步是否为选择审批人
                if(postType!=3){
                    OApickApprovalsReqVO pick = new OApickApprovalsReqVO();
                    pick.setTaskId(oaTaskService.getTaskId(processInstanceId));
                    pick.setUserIds(reqVO.getUserIds());
                    pick.setChargeLeaderSeq(reqVO.getChargeLeaderSeq());
                    oaTaskService.pickApprovals(pick, time.plusSeconds(1L));
                }else {
                    //不需要选择审批人时自动设置部门负责人审批
                    //选择的部门和用户默认的部门不一致时将部门负责人审批转派给选择的部门的负责人
                    if(reqVO.getDeptId() != null) {
                        Long defaultLeader = deptApi.getDept(userApi.getUser(loginUserId).getCheckedData().getDeptId()).getCheckedData().getLeaderUserId();
                        List<BpmTaskRespDTO> bpmTasks = bpmTaskServiceApi.getTaskListByProcessInstanceId(processInstanceId);
                        //重新发起以后最新的节点
                        BpmTaskRespDTO task = bpmTasks.get(bpmTasks.size() - 1);
                        Long currentLeader = deptApi.getDept(reqVO.getDeptId()).getCheckedData().getLeaderUserId();
                        bpmTaskServiceApi.updateTaskAssignee(defaultLeader, new BpmTaskUpdateAssigneeReqDTO()
                                .setAssigneeUserId(currentLeader)
                                .setId(task.getId()));
                    }
                }
            }
        }
    }

    @Override
    public OutReportRespVO getResp(Long id, String processInstanceId,boolean isReceived) {
        OutReportDO outReportDO = this.get(id,processInstanceId);
        OutReportRespVO respVO = OutReportConvert.INSTANCE.convert(outReportDO);
        respVO.setUserNickName(userApi.getUser(outReportDO.getUserId()).getCheckedData().getNickname());
        respVO.setDeptName(deptApi.getDept(outReportDO.getDeptId()).getCheckedData().getName());
        if(CharSequenceUtil.isNotEmpty(outReportDO.getProcessInstanceId())){
            Map<String, String> taskInfo = taskServiceApi.getNeededTaskInfo(OACategoryConstants.OUTREPORT, getLoginUserId(),
                    processInstanceId, isReceived, outReportDO.getIsDealt());
            respVO.setTaskName(taskInfo.get("taskName"));
            if(StringUtil.isNotBlank(taskInfo.get("status"))){
                respVO.setResult(Integer.parseInt(taskInfo.get("status")));
            }
            if(StringUtil.isNotBlank(taskInfo.get("operateType"))){
                respVO.setOperateType(Integer.parseInt(taskInfo.get("operateType")));
            }
        }
        return respVO;
    }

    @Override
    public boolean updateResultById(Long id, Integer result) {
        OutReportDO outReportDO = new OutReportDO();
        outReportDO.setId(id);
        outReportDO.setResult(result);
        outReportMapper.updateById(outReportDO);
        return true;
    }

    @Override
    public Map<String,LocalDate> getDateById(Long id) {
        Map<String,LocalDate> map = new HashMap<>();
        OutReportDO outReportDO = outReportMapper.selectById(id);
        // map.put("id",outReportDO.getId().toString());
        map.put("startDate",outReportDO.getStartDate());
        map.put("endDate",outReportDO.getEndDate());
        return map;
    }

    @Override
    public int deleteById(Long id) {
        return outReportMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealOutReport(OADealOutReportVO dealOutReportVO) {
        //更新签名
        lectureMapper.insertImage(WebFrameworkUtils.getLoginUserId(),dealOutReportVO.getHandSignature());

        OutReportDO outReportDO;
        outReportDO=outReportMapper.selectById(dealOutReportVO.getId());


        // 添加销假时间
        outReportDO.setDealTime(LocalDateTime.now());
        if(outReportDO.getEndDate().isBefore(dealOutReportVO.getEndTime().toLocalDate())) {
            throw exception(OUT_REPORT_DEAL_TIME_AFTER_END_TIME);
        }

        // 更新假期真实结束时间为人事指定日期
        outReportDO.setEndDate(dealOutReportVO.getEndTime().toLocalDate());

        // 更新请假天数为新的结束时间到开始时间的差值,除去节假日


        // 添加上班时间
        //outReportDO.setReturnWorkTime(dealOutReportVO.getReturnWorkTime());

        // 设置为已销假
        outReportDO.setIsDealt(true);

        this.baseMapper.updateById(outReportDO);

        // 添加一条任务记录
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();

        // 获取岗位类型
        PostRespDTO post = postApi.getMinSortPostByUser(getLoginUserId(), postDealCodeList);
        if (Objects.isNull(post)) {

            //当作其他人员
            post=new PostRespDTO();
            post.setCode("3");
        }

        bpmTaskExtDTO.setResult(2);
        bpmTaskExtDTO.setProcessInstanceId(outReportDO.getProcessInstanceId());
        bpmTaskExtDTO.setProcessDefinitionId(outReportDO.getProcessInstanceId());
        LocalDateTime now = LocalDateTime.now();
        bpmTaskExtDTO.setCreateTime(now);
        bpmTaskExtDTO.setEndTime(now);
        bpmTaskExtDTO.setTaskType(5);
        Map<String, Object> map = new HashMap<>();

        //1是办公室销单
        if(post.getCode().equals("oa-office-charge-out")  ) {
            bpmTaskExtDTO.setName("办公室销单");
            bpmTaskExtDTO.setTaskDefKey("office-deal-outReport");
            bpmTaskExtDTO.setTaskId("office-deal-outReport");
            map.put("dealWay","办公室销单");
        }
        //2是人事部销单
        if(post.getCode().equals("oa-hr-charge-out")  ) {
            bpmTaskExtDTO.setName("人事部销单");
            bpmTaskExtDTO.setTaskDefKey("hr-deal-outReport");
            bpmTaskExtDTO.setTaskId("hr-deal-outReport");
            map.put("dealWay","人事部销单");
        }

        /*
        //暂时用电子版人名，后续改成手写签字图片

         */
        // 手写签字图片
        bpmTaskExtDTO.setImageUrl(dealOutReportVO.getHandSignature());

        map.put("leaveStart",outReportDO.getStartDate());
        map.put("leaveEnd",outReportDO.getEndDate());
        bpmTaskExtDTO.setParamsMap(map);
        bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);
        //删除日程
        //销假完成后日程同步更改id和endTime  //leave.getProcessInstanceId()这个是流程id
        ScheduleDto scheduleDto = new ScheduleDto();
        scheduleDto.setProcessInstanceId(outReportDO.getProcessInstanceId());
        BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO=
                processInstanceApi.getProcessInstanceInfo(outReportDO.getProcessInstanceId()).getCheckedData();
        scheduleDto.setEndTime(Date.from(dealOutReportVO.getEndTime().atZone(ZoneId.systemDefault()).toInstant()));
        scheduleDto.setUserId(bpmProcessInstanceRespDTO.getStartUser().getId());
        scheduleServiceApi.deleteScheduleOther(scheduleDto);


        // 发送短信通知
        // 短信内容———— 【申请人姓名】，您的外出报告销单已完成，销单时间：【销单时间】，上班时间：【上班时间】
        AdminUserRespDTO applyUser = userApi.getUser(outReportDO.getUserId()).getCheckedData();
        String nickname = applyUser.getNickname();
        String mobile = applyUser.getMobile();
        //换成开始时间和结束时间
        String startTime = outReportDO.getStartDate().toString();
        String endTime = outReportDO.getEndDate().toString();
        String dealTime = outReportDO.getDealTime().toLocalDate().toString();
        //String workTime = outReportDO.getReturnWorkTime().toLocalDate().toString();
        String message =  String.format("【湖南省委党校】【%s】，您的外出报告销单已完成，开始时间：【%s】，结束时间：【%s】"
                , nickname, startTime, endTime);

        Map<String,Object> smsmap = new HashMap<>();
        smsmap.put("arg1", message);
        smsSendApi.sendSingleSms(mobile, null,null ,"admin-sms-login-new",smsmap);

        //删除姓名
        message =  String.format("销单通知,您的外出报告销单已完成，开始时间：【%s】，结束时间：【%s】"
                , startTime, endTime);

        //待办事项未解决，删除日程已经解决
        //待办事项办添加
        // 抄送给人事处
        ReceiveDTO receiveDTO = new ReceiveDTO();
        receiveDTO.setProcessInstanceId(outReportDO.getProcessInstanceId());
        //设置
        receiveDTO.setCategory(message);

        //发起时间
        receiveDTO.setApplyTime(bpmProcessInstanceRespDTO.getCreateTime());

        //事项id
//        receiveDTO.setItemId();
        List<Long> userIds = new ArrayList<>();
        // 备案给人事处请假负责人（设置一个专门的postType
//        userIds = postApi.getUserByPost("oa-hr-leave-deal", leaveDTO.getTenantId());
//        List<AdminUserRespDTO> users = userApi.getUsersByDeptName("组织人事部", leaveDTO.getTenantId()).getCheckedData();
//        userIds = users.stream().map(AdminUserRespDTO::getId).collect(Collectors.toList());
        //12识别未外出报告
        userIds.add(12L);
        receiveDTO.setUserIds(userIds);
        //发起人id
        receiveDTO.setPromoterUserId(bpmProcessInstanceRespDTO.getStartUser().getId());
        receiveDTO.setProcessInstanceId(outReportDO.getProcessInstanceId());
//        receiveApi.save(receiveDTO);
        receiveApi.saveLeave(receiveDTO);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealOutReportByProcessId(String processId) {

        OutReportDO outReportDO;
        outReportDO=outReportMapper.selectByProcessId(processId);


        // 添加销假时间
        outReportDO.setDealTime(LocalDateTime.now());

        // 更新假期真实结束时间为人事指定日期


        // 更新请假天数为新的结束时间到开始时间的差值,除去节假日


        // 添加上班时间
        //outReportDO.setReturnWorkTime(outReportDO.getEndDate().plusDays(1).atStartOfDay());

        // 设置为已销假
        outReportDO.setIsDealt(true);

        this.baseMapper.updateById(outReportDO);

        // 添加一条任务记录
        BpmTaskExtDTO bpmTaskExtDTO = new BpmTaskExtDTO();

        // 获取岗位类型
        PostRespDTO post = postApi.getMinSortPostByUser(getLoginUserId(), postDealCodeList);
        if (Objects.isNull(post)) {

            //当作其他人员
            post=new PostRespDTO();
            post.setCode("3");
            throw exception(400,"没有外出报告销单的对应岗位");
        }

        bpmTaskExtDTO.setResult(2);
        bpmTaskExtDTO.setProcessInstanceId(outReportDO.getProcessInstanceId());
        bpmTaskExtDTO.setProcessDefinitionId(outReportDO.getProcessInstanceId());
        LocalDateTime now = LocalDateTime.now();
        bpmTaskExtDTO.setCreateTime(now);
        bpmTaskExtDTO.setEndTime(now);
        bpmTaskExtDTO.setTaskType(5);
        Map<String, Object> map = new HashMap<>();

        //1是办公室销单
        if(post.getCode().equals("oa-office-charge-out")  ) {
            bpmTaskExtDTO.setName("办公室销单");
            bpmTaskExtDTO.setTaskDefKey("office-deal-outReport");
            bpmTaskExtDTO.setTaskId("office-deal-outReport");
            map.put("dealWay","办公室销单");
        }
        //2是人事部销单
        if(post.getCode().equals("oa-hr-charge-out")  ) {
            bpmTaskExtDTO.setName("人事部销单");
            bpmTaskExtDTO.setTaskDefKey("hr-deal-outReport");
            bpmTaskExtDTO.setTaskId("hr-deal-outReport");
            map.put("dealWay","人事部销单");
        }

        /*
        //暂时用电子版人名，后续改成手写签字图片

         */
        // 手写签字图片
        bpmTaskExtDTO.setImageUrl(outReportDO.getSign());

        map.put("leaveStart",outReportDO.getStartDate());
        map.put("leaveEnd",outReportDO.getEndDate());
        bpmTaskExtDTO.setParamsMap(map);
        bpmTaskServiceApi.saveCustomTaskExt(bpmTaskExtDTO);
    }


}
