package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryToDoRespVO;
import lombok.*;

import java.util.List;

/**
 * 一周工作汇总
 */
@TableName(value = "oa_weekly_work_summary",autoResultMap = true)
@Data
@KeySequence("oa_weekly_work_summary_id_seq")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeeklyWorkSummaryDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 汇总的一周工作安排ids
     */
    private String weeklyWorkScheduleIds;

    /**
     * 年
     */
    private Integer year;

    /**
     * 学期
     */
    private String semester;

    /**
     * 周
     */
    private Integer week;

    /**
     * 发起人id
     */
    @TableField(value = "launch_user_id")
    private Long userId;

    @TableField(exist = false)
    private List<SummaryToDoRespVO> data;

    /**
     * 抄送人员
     */
    private String copyTo;
}
