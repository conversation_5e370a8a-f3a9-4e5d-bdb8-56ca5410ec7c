package com.unicom.swdx.module.oa.dal.kingbase;

import java.util.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.dal.dataobject.InforDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 重点工作信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InforMapper extends BaseMapperX<InforDO> {

    default PageResult<InforDO>     selectPage(InforPageReqVO reqVO) {

        List<Integer> idlist  = null;
        if((reqVO.getMuserid()!=null)||reqVO.getSuperuserid()!=null||reqVO.getMdeptid()!=null  ||reqVO.getMuseridLeader()!=null || reqVO.getSuseridLeader()!=null){
            if(CollUtil.isNotEmpty(reqVO.getIdlist())){
                idlist = reqVO.getIdlist();
            }else{
                idlist = new ArrayList<>();
                idlist.add(-1); //指定不存在的id、保证限定的数据返回为null ,不设置-1 ，会返回全部
            }
        }else{
            if(CollectionUtil.isNotEmpty(reqVO.getIdlist())){
                idlist = reqVO.getIdlist();
            }
        }

        int order = 0 ;
        try {
            if(reqVO.getStatus()!=null&&reqVO.getStatus().intValue()==10){
                order=1;
            }
        }catch (Exception e){

        }
        if(reqVO.getOrder().intValue()!=0){
            order=reqVO.getOrder();
        }


        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapperX<InforDO>()
                .likeIfPresent(InforDO::getSusername, reqVO.getSusername())
                .eqIfPresent(InforDO::getSuserid, reqVO.getSuserid())
                .ne(reqVO.getSuserid()!=null && !(reqVO.getYeartag()!=null && reqVO.getYeartag().intValue() ==2)  ,InforDO::getYeartag  ,2 )
                .inIfPresent(  InforDO::getYeartag, reqVO.getYeartaglist() )
                .eqIfPresent(InforDO::getSdeptid, reqVO.getSdeptid())
                .inIfPresent(InforDO::getSdeptid, reqVO.getQueryDeptIds())
                .likeIfPresent(InforDO::getSdepname, reqVO.getSdepname())
                .and(StrUtil.isNotEmpty(reqVO.getTaskname()), wrapper ->{

                    wrapper.like(InforDO::getTaskname, reqVO.getTaskname())
                            .or()
                            .like(InforDO::getTaskcontent, reqVO.getTaskname());

                })
                .eqIfPresent(InforDO::getHhandle ,reqVO.getHhandle() )
                .eqIfPresent(InforDO::getTasktype, reqVO.getTasktype())
                .leIfPresent(InforDO::getCreateTime, reqVO.getEndtime() )
                .geIfPresent(InforDO::getCreateTime, reqVO.getStarttime())
                .eqIfPresent(InforDO::getMasterhandle, reqVO.getMasterhandle())
                .eqIfPresent(InforDO::getMinisterhandle, reqVO.getMinisterhandle())
                .eqIfPresent(InforDO::getPrincipalhandle,reqVO.getPrincipalhandle())
                .eqIfPresent(InforDO::getSuserid,reqVO.getPromoter())

                .leIfPresent(InforDO::getCreateTime, reqVO.getSendtime() )
                .geIfPresent(InforDO::getEndtime, reqVO.getSstarttime())

                .eqIfPresent(InforDO::getDegree, reqVO.getDegree())
                .eqIfPresent(InforDO::getShandle, reqVO.getShandle())
                .neIfPresent(InforDO::getShandle,reqVO.getShandleNe())
                .eqIfPresent(InforDO::getShandle, reqVO.getShandleEq())
                .likeIfPresent(InforDO::getTaskcontent, reqVO.getTaskcontent())
                .eqIfPresent(InforDO::getTaskfile, reqVO.getTaskfile())
                .eqIfPresent(InforDO::getStatus, reqVO.getStatus())
                //获取填报人进行中页面增加status为4的数据
                .gtCondition(  reqVO.getStatuslecode()==null&& reqVO.getStatusgecode()==null&&reqVO.getYeartag()!=null&&reqVO.getYeartag()==2  && (reqVO.getNeeddraft().intValue()  ==0 || ((reqVO.getSuserid() !=null||reqVO.getSuperuserid()!=null )&& reqVO.getStatus()==null)) ,    InforDO::getStatus ,  2 )
                .gtCondition(  reqVO.getStatusList()!=null&&reqVO.getStatusList().size()==0&&reqVO.getHandleDone()==0&&reqVO.getStatuslecode()==null&& reqVO.getStatusgecode()==null&&reqVO.getSusersee()==null&&reqVO.getLeadersee()==null&&reqVO.getMastersee()==null&&(reqVO.getYeartag()==null||(reqVO.getYeartag()!=null&&(reqVO.getYeartag()==0 ||reqVO.getYeartag()==1))) && (reqVO.getNeeddraft().intValue()  ==0 || ((reqVO.getSuserid() !=null||reqVO.getSuperuserid()!=null )&& reqVO.getStatus()==null)) ,    InforDO::getStatus ,  10 ) //查询发起人的 必须大于草稿
                .leIfPresent(InforDO::getStatus , reqVO.getStatuslecode())
                .geIfPresent(InforDO::getStatus , reqVO.getStatusgecode())
                .inIfPresent(InforDO::getStatus, reqVO.getStatusList())
                .betweenIfPresent(InforDO::getCreateTime, reqVO.getCreateTime())
                .inIfPresent( InforDO::getId , idlist)
                .eqIfPresent(InforDO::getYeartag , reqVO.getYeartag())
                .orderByDesc(order==0,InforDO::getCreateTime)
                .orderByDesc( order ==1 , InforDO::getUpdateTime)
                .orderByDesc( order ==2 , InforDO::getCreateTime )
                .orderByAsc(order==3,InforDO::getStatus)
                .orderByAsc(order==3,InforDO::getShandle)
                .orderByDesc( order==3 , InforDO::getEndtime);


        susersee(reqVO.getSusersee() , lambdaQueryWrapper , SecurityFrameworkUtils.getLoginUserId().intValue());
        leadersee(reqVO.getLeadersee() , lambdaQueryWrapper , SecurityFrameworkUtils.getLoginUserId().intValue() ,  reqVO.getDeptids() );
        mastersee(reqVO.getMastersee() , lambdaQueryWrapper , SecurityFrameworkUtils.getLoginUserId().intValue(), reqVO.getDeptids());

        return selectPage(reqVO, lambdaQueryWrapper);


    }

    default List<InforDO> selectList(InforExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InforDO>()
                .likeIfPresent(InforDO::getSusername, reqVO.getSusername())
                .eqIfPresent(InforDO::getSuserid, reqVO.getSuserid())
                .ne(reqVO.getSuserid()!=null && !(reqVO.getStatus()!=null && reqVO.getStatus().intValue() ==2),InforDO::getYeartag  ,2 )
                .eqIfPresent(InforDO::getSdeptid, reqVO.getSdeptid())
                .likeIfPresent(InforDO::getSdepname, reqVO.getSdepname())
                .likeIfPresent(InforDO::getTaskname, reqVO.getTaskname())
                .eqIfPresent(InforDO::getTasktype, reqVO.getTasktype())
                .gtIfPresent(InforDO::getStarttime, reqVO.getEndtime())
                .ltIfPresent(InforDO::getEndtime, reqVO.getCreateTime())
                .eqIfPresent(InforDO::getDegree, reqVO.getDegree())
                .eqIfPresent(InforDO::getTaskcontent, reqVO.getTaskcontent())
                .eqIfPresent(InforDO::getTaskfile, reqVO.getTaskfile())
                .eqIfPresent(InforDO::getStatus, reqVO.getStatus())
                .gtCondition(  reqVO.getSuserid() !=null&& reqVO.getStatus()==null ,    InforDO::getStatus ,  10 ) //查询发起人的 必须大于草稿
                .betweenIfPresent(InforDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InforDO::getCreateTime));
    }


    default List<InforDO> selectList(InforPageReqVO reqVO) {

        List<Integer> idlist  = null;
        if((reqVO.getMuserid()!=null)||reqVO.getSuperuserid()!=null||reqVO.getMdeptid()!=null  ||reqVO.getMuseridLeader()!=null || reqVO.getSuseridLeader()!=null){
            if(CollUtil.isNotEmpty(reqVO.getIdlist())){
                idlist = reqVO.getIdlist();
            }else{
                idlist = new ArrayList<>();
                idlist.add(-1); //指定不存在的id、保证限定的数据返回为null ,不设置-1 ，会返回全部
            }
        }else{
            if(CollectionUtil.isNotEmpty(reqVO.getIdlist())){
                idlist = reqVO.getIdlist();
            }
        }

        int order = 0 ;
        try {
            if(reqVO.getStatus()!=null&&reqVO.getStatus().intValue()==10){
                order=1;
            }
        }catch (Exception e){

        }
        if(reqVO.getOrder().intValue()!=0){
            order=reqVO.getOrder();
        }


        LambdaQueryWrapper lambdaQueryWrapper =  new LambdaQueryWrapperX<InforDO>()
                .likeIfPresent(InforDO::getSusername, reqVO.getSusername())
                .eqIfPresent(InforDO::getSuserid, reqVO.getSuserid())
                .ne(reqVO.getSuserid()!=null && !(reqVO.getYeartag()!=null && reqVO.getYeartag().intValue() ==2)  ,InforDO::getYeartag  ,2 )
                .inIfPresent(  InforDO::getYeartag, reqVO.getYeartaglist() )
                .eqIfPresent(InforDO::getSdeptid, reqVO.getSdeptid())
                .likeIfPresent(InforDO::getSdepname, reqVO.getSdepname())
                .and(StrUtil.isNotEmpty(reqVO.getTaskname()), wrapper ->{

                    wrapper.like(InforDO::getTaskname, reqVO.getTaskname())
                            .or()
                            .like(InforDO::getTaskcontent, reqVO.getTaskname());

                })
                .eqIfPresent(InforDO::getHhandle ,reqVO.getHhandle() )
                .eqIfPresent(InforDO::getTasktype, reqVO.getTasktype())
                .leIfPresent(InforDO::getCreateTime, reqVO.getEndtime() )
                .geIfPresent(InforDO::getCreateTime, reqVO.getStarttime())
                .eqIfPresent(InforDO::getMasterhandle, reqVO.getMasterhandle())

                .leIfPresent(InforDO::getCreateTime, reqVO.getSendtime() )
                .geIfPresent(InforDO::getEndtime, reqVO.getSstarttime())

                .eqIfPresent(InforDO::getDegree, reqVO.getDegree())
                .eqIfPresent(InforDO::getShandle, reqVO.getShandle())
                .eqIfPresent(InforDO::getSuserid,reqVO.getPromoter())
                .neIfPresent(InforDO::getShandle,reqVO.getShandleNe())
                .likeIfPresent(InforDO::getTaskcontent, reqVO.getTaskcontent())
                .eqIfPresent(InforDO::getTaskfile, reqVO.getTaskfile())
                .eqIfPresent(InforDO::getShandle, reqVO.getShandleEq())
                .eqIfPresent(InforDO::getStatus, reqVO.getStatus())
                //获取填报人进行中页面增加status为4的数据
                .gtCondition(  reqVO.getStatuslecode()==null&& reqVO.getStatusgecode()==null&&reqVO.getYeartag()!=null&&reqVO.getYeartag()==2  && (reqVO.getNeeddraft().intValue()  ==0 || ((reqVO.getSuserid() !=null||reqVO.getSuperuserid()!=null )&& reqVO.getStatus()==null)) ,    InforDO::getStatus ,  2 )
                .gtCondition(  reqVO.getStatuslecode()==null&& reqVO.getStatusgecode()==null&&reqVO.getSusersee()==null&&reqVO.getLeadersee()==null&&reqVO.getMastersee()==null&&(reqVO.getYeartag()==null||(reqVO.getYeartag()!=null&&(reqVO.getYeartag()==0 ||reqVO.getYeartag()==1))) && (reqVO.getNeeddraft().intValue()  ==0 || ((reqVO.getSuserid() !=null||reqVO.getSuperuserid()!=null )&& reqVO.getStatus()==null)) ,    InforDO::getStatus ,  10 ) //查询发起人的 必须大于草稿
                .leIfPresent(InforDO::getStatus , reqVO.getStatuslecode())
                .geIfPresent(InforDO::getStatus , reqVO.getStatusgecode())
                .inIfPresent(InforDO::getStatus, reqVO.getStatusList())
                .betweenIfPresent(InforDO::getCreateTime, reqVO.getCreateTime())
                .inIfPresent( InforDO::getId , idlist)
                .eqIfPresent(InforDO::getYeartag , reqVO.getYeartag())
                .orderByDesc(order==0,InforDO::getCreateTime)
                .orderByDesc( order ==1 , InforDO::getUpdateTime)
                .orderByDesc( order ==2 , InforDO::getCreateTime )
                .orderByAsc(order==3,InforDO::getStatus)
                .orderByAsc(order==3,InforDO::getShandle)
                .orderByDesc( order==3 , InforDO::getEndtime);

        susersee(reqVO.getSusersee() , lambdaQueryWrapper , SecurityFrameworkUtils.getLoginUserId().intValue());
        leadersee(reqVO.getLeadersee() , lambdaQueryWrapper , SecurityFrameworkUtils.getLoginUserId().intValue() ,  reqVO.getDeptids() );
        mastersee(reqVO.getMastersee() , lambdaQueryWrapper , SecurityFrameworkUtils.getLoginUserId().intValue(),  reqVO.getDeptids());

        return selectList(lambdaQueryWrapper);


    }


    public List<InforRespVO> testdouble();

    List<Integer> selectAllChildrenDeptIds(Integer parentId);


    List<Integer> userinforlist(@Param("userid")  Integer userid);

    List<Integer> deptuserinforlist(@Param("userid") Integer userid,@Param("deptids") List<Long> deptids);

    List<Integer> userinforlistMasterSee(@Param("userid") Integer userid,@Param("deptids") List<Long> deptids);
    /**
     *填报员要看
     * 月度中发起人是他，且status处在4~30之间的数据
     * 临时和年度中查看办理人是他的数据
     * @return
     */
    default  void susersee(Integer  susersee ,   LambdaQueryWrapper<InforDO>  lambdaQueryWrapper ,Integer userid){

        if(susersee==null){
            return;
        }

        List<Integer> list =  userinforlist(userid);

        lambdaQueryWrapper.and(susersee!=null, wrapper ->{

            wrapper.and(wrapperand->{
                        wrapperand.eq(InforDO::getYeartag, 2).eq(InforDO::getSuserid, userid).in(InforDO::getStatus , 4 ,30);
            })

                    .or(CollectionUtil.isNotEmpty(list))
                    .in(CollectionUtil.isNotEmpty(list) ,  InforDO::getId  ,  list);

        });
    }

    /**
     *处长要看
     * 月度中自己部门下 status处于4~30的数据
     * 临时和年度中查看办理人是他的数据
     * @return
     */
    default  void leadersee(Integer  leadersee ,   LambdaQueryWrapper<InforDO>  lambdaQueryWrapper ,Integer userid ,List<Long> deptids ){

        if(leadersee==null){
            return;
        }

        List<Integer> list =  deptuserinforlist(userid, deptids);

        lambdaQueryWrapper.and(leadersee !=null, wrapper ->{

            wrapper.and(wrapperand->{

                        wrapperand.eq(InforDO::getYeartag, 2).in(CollectionUtil.isNotEmpty(deptids) ,InforDO::getSdeptid, deptids ).in(InforDO::getStatus , 4 ,30);

                    })
                    .or(CollectionUtil.isNotEmpty(list))
                    .in(CollectionUtil.isNotEmpty(list) ,  InforDO::getId  ,  list);

        });
    }

    default  void   mastersee(Integer  mastersee ,   LambdaQueryWrapper<InforDO>  lambdaQueryWrapper ,Integer userid,List<Long> deptids){

        if(mastersee==null){
            return;
        }

        List<Integer> list =  userinforlistMasterSee(userid, deptids);

        lambdaQueryWrapper.and(mastersee!=null, wrapper ->{

            wrapper.and(wrapperand->{
                        wrapperand.eq(InforDO::getYeartag, 2).in(CollectionUtil.isNotEmpty(deptids),InforDO::getSdeptid, deptids ).in(InforDO::getStatus , 4 ,30);
            })

                    .or(CollectionUtil.isNotEmpty(list))
                    .in(CollectionUtil.isNotEmpty(list) ,  InforDO::getId  ,  list);

        });
    }

    @TenantIgnore
    void updateBatch(@Param("inforDOList") List<InforDO> inforDOList);

    @TenantIgnore
    List<InforDO> selectMonthInfor();
    @TenantIgnore
    InforDO selectInforDoById(@Param("id") Integer id);
}
