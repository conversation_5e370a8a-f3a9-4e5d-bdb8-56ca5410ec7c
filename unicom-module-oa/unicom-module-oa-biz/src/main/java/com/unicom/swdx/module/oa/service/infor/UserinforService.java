package com.unicom.swdx.module.oa.service.infor;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforExportReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforPageReqVO;
import com.unicom.swdx.module.oa.controller.admin.infor.uvo.UserinforUpdateReqVO;
import com.unicom.swdx.module.oa.dal.dataobject.UserinforDO;

/**
 * 重点任务关系 Service 接口
 *
 * <AUTHOR>
 */
public interface UserinforService {

    /**
     * 创建重点任务关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserinfor(@Valid UserinforCreateReqVO createReqVO);

    /**
     * 更新重点任务关系
     *
     * @param updateReqVO 更新信息
     */
    void updateUserinfor(@Valid UserinforUpdateReqVO updateReqVO);

    /**
     * 删除重点任务关系
     *
     * @param id 编号
     */
    void deleteUserinfor(Integer id);

    /**
     * 获得重点任务关系
     *
     * @param id 编号
     * @return 重点任务关系
     */
    UserinforDO getUserinfor(Integer id);

    /**
     * 获得重点任务关系列表
     *
     * @param ids 编号
     * @return 重点任务关系列表
     */
    List<UserinforDO> getUserinforList(Collection<Integer> ids);

    /**
     * 获得重点任务关系分页
     *
     * @param pageReqVO 分页查询
     * @return 重点任务关系分页
     */
    PageResult<UserinforDO> getUserinforPage(UserinforPageReqVO pageReqVO);

    /**
     * 获得重点任务关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 重点任务关系列表
     */
    List<UserinforDO> getUserinforList(UserinforExportReqVO exportReqVO);

}
