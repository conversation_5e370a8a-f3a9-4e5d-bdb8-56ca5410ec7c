package com.unicom.swdx.module.oa.controller.admin.vo.schedule;

import com.unicom.swdx.module.oa.dal.dataobject.WorkScheduleDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ScheduleBaseVO {
    /**
     * 工作安排
     */
    @ApiModelProperty("工作安排")
    private List<WorkScheduleDO> workSchedules;

    /**
     * 是否草稿
     */
    @ApiModelProperty("是否草稿")
    private Boolean isDraft;


    @ApiModelProperty("所属部门")
    private Long deptId;
}
