package com.unicom.swdx.module.oa.dal.kingbase;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryDateVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryToDoRespVO;
import com.unicom.swdx.module.oa.controller.admin.vo.summary.SummaryWithdrawRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.LectureDO;
import com.unicom.swdx.module.oa.dal.dataobject.WeeklyWorkSummaryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WeeklyWorkSummaryMapper extends BaseMapperX<WeeklyWorkSummaryDO> {
    Integer getCount(Long TenantId);

    List<SummaryToDoRespVO> getMyToDo(Long userId);

    List<String> getRejected(Long userId);

    List<SummaryWithdrawRespVO> getWithdraw(Long userId);

    List<SummaryToDoRespVO> getBack(Long userId);

    List<SummaryToDoRespVO> getWithdrawSchedule();

    Integer getCountReject(Long TenantId);

    Integer getCountCancel(Long TenantId);


    //获取ids
    String getWeekIdsByPId(String processInstanceId);

    //最早开始和最晚结束时间
    SummaryDateVO getDateById(@Param("ids") List<Long> ids);

    //参与人
    List<String> getPersonnelIdsById(@Param("ids") List<Long> ids);
}