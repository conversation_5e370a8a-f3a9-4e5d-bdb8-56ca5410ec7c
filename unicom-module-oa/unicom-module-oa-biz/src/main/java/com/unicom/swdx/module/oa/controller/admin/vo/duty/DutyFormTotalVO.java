package com.unicom.swdx.module.oa.controller.admin.vo.duty;

import com.unicom.swdx.module.oa.dal.dataobject.VacationDutyFormDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DutyFormTotalVO {
    /**
     * 寒暑假坐值班表单列表-坐班
     */
    @ApiModelProperty("寒暑假坐值班表单列表-坐班")
    public List<DutyFormBaseVO> vacationDutyFormSitList;

    /**
     * 寒暑假坐值班最早日期-坐班
     */
    @ApiModelProperty("寒暑假坐值班最早日期-坐班")
    public String minDateSit;

    /**
     * 寒暑假坐值班最晚日期-坐班
     */
    @ApiModelProperty("寒暑假坐值班最晚日期-坐班")
    public String maxDateSit;

    /**
     * 寒暑假坐值班表单列表-坐班
     */
    @ApiModelProperty("寒暑假坐值班表单列表-值班")
    public List<DutyFormBaseVO> vacationDutyFormDepList;

    /**
     * 寒暑假坐值班最早日期-坐班
     */
    @ApiModelProperty("寒暑假坐值班最早日期-值班")
    public String minDateDep;

    /**
     * 寒暑假坐值班最晚日期-坐班
     */
    @ApiModelProperty("寒暑假坐值班最晚日期-值班")
    public String maxDateDep;
}
