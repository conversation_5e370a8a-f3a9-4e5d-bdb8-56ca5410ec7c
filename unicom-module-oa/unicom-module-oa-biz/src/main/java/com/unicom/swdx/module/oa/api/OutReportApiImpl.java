package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.OutReportDTO;
import com.unicom.swdx.module.oa.convert.OutReportConvert;
import com.unicom.swdx.module.oa.service.outReport.OutReportService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class OutReportApiImpl implements OutReportApi{

    @Resource
    private OutReportService outReportService;

    @Override
    public OutReportDTO getItemId(String processInstanceId) {
        return OutReportConvert.INSTANCE.convertDTO(outReportService.getByProcessInstanceId(processInstanceId));
    }

    @Override
    public void updateResultById(Long id, Integer result) {
        outReportService.updateResultById(id, result);
    }

    @Override
    public void dealOutReportByProcessId(String id) {
        outReportService.dealOutReportByProcessId(id);
    }

    @Override
    public Map<String, LocalDate> getDateById(Long id) {
        return outReportService.getDateById(id);
    }

    @Override
    public CommonResult<Long> getDeptId(String processInstanceId) {
        return success(outReportService.getByProcessInstanceId(processInstanceId).getDeptId());
    }

}
