package com.unicom.swdx.module.oa.controller.admin.infor;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;

import cn.hutool.db.Entity;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.ImportOAResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.oa.controller.admin.infor.vo.*;
import com.unicom.swdx.module.oa.convert.InforConvert;
import com.unicom.swdx.module.oa.convert.InforListConvert;
import com.unicom.swdx.module.oa.dal.dataobject.*;
import com.unicom.swdx.module.oa.dal.kingbase.*;
import com.unicom.swdx.module.oa.service.infor.InforService;
import com.unicom.swdx.module.oa.task.CustomSheetWriteHandler;
import com.unicom.swdx.module.oa.task.DateTimeFormatHandler;
import com.unicom.swdx.module.system.api.dept.DeptApi;
import com.unicom.swdx.module.system.api.dept.dto.DeptRespDTO;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;

import com.unicom.swdx.module.system.api.schedule.ScheduleServiceApi;
import com.unicom.swdx.module.system.api.schedule.dto.ScheduleDto;
import com.unicom.swdx.module.system.api.sms.SmsSendApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.boot.autoconfigure.klock.model.LockType;
import org.springframework.boot.autoconfigure.klock.model.ReleaseTimeoutStrategy;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.*;


@Api(tags = "办公OA - 中心工作管理")
@RestController
@RequestMapping("/oa/taskinfor")
@Slf4j
public class InforController {

    @Resource
    private InforService inforService;

    @Resource
    private InforMapper inforMapper;


    @Resource
    private YeartaskOperationMapper operationMapper;

    @Resource
    private DeptApi deptApi;


    @Resource
    private AsyncListenableTaskExecutor taskExecutor;

    @Resource
    InforEndtimeMapper endtimeMapper;

    @Resource
    private ScheduleServiceApi scheduleServiceApi;

    @Resource
    private YeartaskRecordMapper yeartaskRecordMapper;


    @PostMapping("/test")
    @ApiOperation("创建重点工作信息测试")
    public CommonResult<List<InforRespVO>> test() {
        return success(inforMapper.testdouble());
    }




    @PostMapping("/create")
    @ApiOperation("创建重点工作信息")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @PreAuthorize("@ss.hasPermission('yeartask:infor:create')")
    public CommonResult<Integer> createInfor(@Valid @RequestBody InforCreateReqVO createReqVO) throws ParseException {

        InforEndtimeDO endtimeDO =  selectEndtime(SecurityFrameworkUtils.getTenantId()).getCheckedData();
        try {
            if(createReqVO.getYeartag().intValue()==2&&createReqVO.getStatus()==4){
                if(endtimeDO==null){
                    setEndtime(SecurityFrameworkUtils.getTenantId(), "2024-05-26");
                    endtimeDO =  selectEndtime(SecurityFrameworkUtils.getTenantId()).getCheckedData();
                }



                if( DateUtil.compare(DateUtil.parseDate(endtimeDO.getEndtime())  , DateUtil.date()  ,"yyyy-MM-dd") < 0){
                    return error(500 , "超过截止时间");
                }
            }
        }catch (Exception e){
            log.error("创建重点工作信息  {}" , e.getMessage());
        }

        if(createReqVO.getStatus()==null){
            createReqVO.setStatus(20);
        }
        // 10 草稿  20 代签收 30 待办理 40 待办结 50 已办结1142
        Integer id = inforService.createInfor(createReqVO);
        //审批到办理状态增加日程status4-30/月度中心工作是这个yeartag 0临时 1年度 2月度 //月度是更新的时候，临时和年度是创建的时候
        if((createReqVO.getYeartag() == 0 || createReqVO.getYeartag() == 1)&& createReqVO.getStatus() == 20){
            //办理人类表
            if(createReqVO.getUserinforDOList()!=null){
                List<UserinforDO> userinforDOList = createReqVO.getUserinforDOList();
                userinforDOList = userinforDOList.stream().filter(userinforDO -> userinforDO.getUtype() == 1)
                        .collect(Collectors.toList());
                ScheduleDto scheduleDto = new ScheduleDto();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if(createReqVO.getStarttime()!= null && createReqVO.getEndtime()!= null){
                    //开始和结束
                    scheduleDto.setStartTime(sdf.parse(createReqVO.getStarttime()));
                    scheduleDto.setEndTime(sdf.parse(createReqVO.getEndtime()));
                    //任务名称
                    scheduleDto.setTaskname(createReqVO.getTaskcontent());
                    //取代pid
                    scheduleDto.setInforId(id);
                    //year
                    scheduleDto.setYeartag(createReqVO.getYeartag());
                    //类型
                    scheduleDto.setYeartag(createReqVO.getYeartag());
                    //办理人id
                    for (UserinforDO userinforDO : userinforDOList){
                        scheduleDto.setUserId(Long.valueOf(userinforDO.getUid()));
                        scheduleServiceApi.createScheduleInfor(scheduleDto);
                    }
                }
            }

        }

        return success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新重点工作信息")
//    @Transactional
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:update')")
    public CommonResult<Boolean> updateInfor(@Valid @RequestBody InforUpdateReqVO updateReqVO) throws ParseException {

        CommonResult<InforRespVO> infor = getInfor(updateReqVO.getId());
        InforEndtimeDO endtimeDO =  selectEndtime(SecurityFrameworkUtils.getTenantId()).getCheckedData();
        try {
            if(updateReqVO.getYeartag().intValue()==2&&infor.getData().getStatus()==1&&updateReqVO.getStatus()==4){
                if(endtimeDO==null){
                    setEndtime(SecurityFrameworkUtils.getTenantId(), "2024-05-26");
                    endtimeDO =  selectEndtime(SecurityFrameworkUtils.getTenantId()).getCheckedData();
                }



                if( DateUtil.compare(DateUtil.parseDate(endtimeDO.getEndtime())  , DateUtil.date()  ,"yyyy-MM-dd") < 0){
                    return error(500 , "超过截止时间");
                }
            }
        }catch (Exception e){
            log.error("更新重点工作信息  {}" , e.getMessage());
        }
        //审批到办理状态增加日程status4-30/月度中心工作是这个yeartag 0临时 1年度 2月度
        // 月度是更新的时候，临时和年度是创建的时候
        if(infor.getData().getStatus()==4 && updateReqVO.getStatus()!=null &&
                updateReqVO.getStatus()==30 && infor.getData().getYeartag() == 2){
            ScheduleDto scheduleDto = new ScheduleDto();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if(infor.getData().getStarttime()!= null && infor.getData().getEndtime()!= null){
                //开始和结束
                scheduleDto.setStartTime(sdf.parse(infor.getData().getStarttime()));
                scheduleDto.setEndTime(sdf.parse(infor.getData().getEndtime()));
                //取代pid
                scheduleDto.setInforId(infor.getData().getId());
                //发起人id
                scheduleDto.setUserId(Long.valueOf(infor.getData().getSuserid()));
                //类型在service里面加
                //任务名称
                scheduleDto.setTaskname(infor.getData().getTaskcontent());
                //year
                scheduleDto.setYeartag(infor.getData().getYeartag());
            }
            scheduleServiceApi.createScheduleInfor(scheduleDto);
        }
        //月度办理状态更改时间日程status30，日程同步修改
        //或者临时和年度更改就更新（办理人的）
        //传了时间才会更新月度工作
        if( infor.getData().getStatus()==30
                && infor.getData().getYeartag() == 2
        ){
            ScheduleDto scheduleDto = new ScheduleDto();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //添加最新的日程
            if( updateReqVO.getEndtime() != null
                    && !updateReqVO.getEndtime().equals(infor.getData().getEndtime())){
                //删除之前的日程
                ScheduleDto deletedDto = new ScheduleDto();

                deletedDto.setInforId(infor.getData().getId());
                scheduleServiceApi.deleteScheduleInfor(deletedDto);
                //开始和结束
                scheduleDto.setStartTime(sdf.parse(infor.getData().getStarttime()));
                scheduleDto.setEndTime(sdf.parse(updateReqVO.getEndtime()));
                //取代pid
                scheduleDto.setInforId(infor.getData().getId());
                //发起人id
                scheduleDto.setUserId(Long.valueOf(infor.getData().getSuserid()));
                //类型在service里面加
                //任务名称
                scheduleDto.setTaskname(infor.getData().getTaskcontent());
                //year
                scheduleDto.setYeartag(infor.getData().getYeartag());
                scheduleServiceApi.createScheduleInfor(scheduleDto);
            }


        }
        if(updateReqVO.getStatus()!=null && infor.getData().getYeartag() != 2 &&
        updateReqVO.getStarttime()!= null && updateReqVO.getEndtime()!= null){
            //删除之前的日程
            ScheduleDto deletedDto = new ScheduleDto();
            deletedDto.setInforId(infor.getData().getId());
            scheduleServiceApi.deleteScheduleInfor(deletedDto);
//办理人类表
            if(updateReqVO.getUserinforDOList()!=null){
                List<UserinforDO> userinforDOList = updateReqVO.getUserinforDOList();
                userinforDOList = userinforDOList.stream().filter(userinforDO -> userinforDO.getUtype() == 1)
                        .collect(Collectors.toList());
                ScheduleDto scheduleDto = new ScheduleDto();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if(updateReqVO.getStarttime()!= null && updateReqVO.getEndtime()!= null){
                    //开始和结束
                    scheduleDto.setStartTime(sdf.parse(updateReqVO.getStarttime()));
                    scheduleDto.setEndTime(sdf.parse(updateReqVO.getEndtime()));
                    //任务名称
                    scheduleDto.setTaskname(updateReqVO.getTaskcontent());
                    //取代pid
                    scheduleDto.setInforId(infor.getData().getId());
                    //year
                    scheduleDto.setYeartag(updateReqVO.getYeartag());
                    //类型
                    scheduleDto.setYeartag(updateReqVO.getYeartag());
                    //办理人id
                    for (UserinforDO userinforDO : userinforDOList){
                        scheduleDto.setUserId(Long.valueOf(userinforDO.getUid()));
                        scheduleServiceApi.createScheduleInfor(scheduleDto);
                    }
                }
            }

        }
        //已办结到办理状态删除日程status 30-50
        if(infor.getData().getStatus()==30 && updateReqVO.getStatus()!=null
                &&updateReqVO.getStatus()==50){
            ScheduleDto scheduleDto = new ScheduleDto();
            scheduleDto.setInforId(infor.getData().getId());
            scheduleServiceApi.deleteScheduleInfor(scheduleDto);
        }

        inforService.updateInfor(updateReqVO);
        return success(true);
    }


    @PostMapping("/updatebatchs")
    @ApiOperation("更新重点工作信息batch")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    public CommonResult<Boolean> updateInforbatch(@Valid @RequestBody List<InforUpdateReqVO> updateReqVObatch) {

        if(CollectionUtil.isNotEmpty(updateReqVObatch)){

            updateReqVObatch.forEach(it->{
                inforService.updateInfor(it);
            });

        }

        return success(true);
    }



    @PostMapping("/updateAll")
    @ApiOperation("更新重点工作信息all")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    public CommonResult<UpdateAll> updateInforAll(@RequestBody  InforPageAllReqVO pageallVO ) {

        InforPageReqVO pageVO = pageallVO.getPageVO();

        Long status = pageallVO.getStatus() ;

        Long hhandle = pageallVO.getHhandle();

        List<Long> ids =pageallVO.getIds();

        Integer shandle = pageallVO.getShandle();

        Integer masterhandle = pageallVO.getMasterhandle();

        Integer ministerhandle = pageallVO.getMinisterhandle();

        Integer leaderhandle = pageallVO.getLeaderhandle();

        Integer principalhandle = pageallVO.getPrincipalhandle();

        paevoconvert(pageVO);

        List<InforRespVO> alllist =  inforService.getInforList(pageVO);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();

        for (InforRespVO inforRespVO : alllist) {
            if(inforRespVO.getYeartag()==2&&inforRespVO.getStatus()==30){
                LocalDateTime starttime = LocalDateTime.parse(inforRespVO.getStarttime(),formatter);
                LocalDateTime endtime = LocalDateTime.parse(inforRespVO.getEndtime(),formatter);
                //LocalDateTime currentDateTime = LocalDateTime.now();
                long totalDurationMinutes = ChronoUnit.MINUTES.between(starttime, endtime);
                long eightyPercentRange = (long) (totalDurationMinutes * 0.8);

                LocalDateTime eightyPercentStart = starttime.plusMinutes(eightyPercentRange);
                //任务进展为正常
                if(now.isBefore(eightyPercentStart)){
                    inforRespVO.setTaskProgress(1);
                }
                //任务进展为告警
                else if (now.isAfter(eightyPercentStart)&&now.isBefore(endtime)){
                    inforRespVO.setTaskProgress(2);
                }
                //任务进展为逾期
                else {
                    inforRespVO.setTaskProgress(3);
                }
            }
        }


        //如果导出的数据里没有utype=0的数据，则将去除(牵头部门筛选)
        if(pageVO.getDeptId()!=null){
            Boolean isRemove=true;
//                        PageResult<InforRespVO> temp =inforDOPageResult;
            List<InforRespVO> temp=alllist;
            List<InforRespVO> removeTemp=new ArrayList<>();
            for (InforRespVO inforRespVO : temp) {
                List<UserinforDO> temp1=inforRespVO.getUserinforDOList();
                for (UserinforDO userinforDO : temp1) {
                    if(userinforDO.getUtype()==0){
                        isRemove=false;
                        break;
                    }
                }
                if(isRemove){
                    removeTemp.add(inforRespVO);
                }
                isRemove=true;
            }
            alllist.removeAll(removeTemp);
        }

        if(pageVO.getTaskProgress()!=null&&(pageVO.getTaskProgress()==1||pageVO.getTaskProgress()==2||pageVO.getTaskProgress()==3)){
            alllist=alllist.stream().filter(it-> pageVO.getTaskProgress().equals(it.getTaskProgress())).collect(Collectors.toList());
        }

        alllist.forEach(it->it.setRejectionReason(pageallVO.getRejectionReason()));

        int success = 0;
        int fail =0;

        if(CollectionUtil.isNotEmpty(alllist)){

            int size = alllist.size();
            if(ids!=null){

                List<InforRespVO> filterall =  alllist.stream().filter(it -> !ids.contains(Long.valueOf(it.getId()))).collect(Collectors.toList());
                size = filterall.size();

//                if(status!=null){
//
//                    if(status ==50){
//                        filterall =   filterall.stream().filter(it-> it.getShandle().intValue()==1 && it.getStatus().intValue() ==30).collect(Collectors.toList());
//                        if(hhandle!=null){
//                            filterall =   filterall.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//                        }
//
//                        if(ministerhandle!=null){
//                            filterall =   filterall.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//                        }
//
//                        if(masterhandle!=null){
//                            filterall =   filterall.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//                        }
//                    }
//                }


                alllist.forEach(list -> list.setShandle(null));
                List<InforUpdateReqVO> updateReqVObatch = InforConvert.INSTANCE.convertList05(filterall);

                if(status != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setStatus(status.intValue()));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setStatus(null));
                }
                if (masterhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMasterhandle(masterhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMasterhandle(null));
                }
                if (ministerhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMinisterhandle(ministerhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMinisterhandle(null));
                }
                if (hhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setHhandle(hhandle.intValue()));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setHhandle(null));
                }
                if (shandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setShandle(shandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setShandle(null));
                }
                if (leaderhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setLeaderhandle(leaderhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setLeaderhandle(null));
                }
                if (principalhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setPrincipalhandle(principalhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setPrincipalhandle(null));
                }
                updateReqVObatch.forEach(udateReqVO -> udateReqVO.setUserinforDOList(null));

                if(CollectionUtil.isNotEmpty(updateReqVObatch)){
                    for (InforUpdateReqVO updateReqVO : updateReqVObatch) {
                        InforDO PREDAO = inforService.getInfor(updateReqVO.getId());

                        //填报审批根据当前状态自动更新
                        if(!(updateReqVO.getStatus()==2&&updateReqVO.getHhandle()==0)){
                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==0&&PREDAO.getYeartag()==2){
                                updateReqVO.setHhandle(2);
                                updateReqVO.setStatus(4);
                            }

                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2){
                                updateReqVO.setHhandle(1);
                                updateReqVO.setLeaderhandle(1);
                                updateReqVO.setStatus(4);
                            }

                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==1&&PREDAO.getYeartag()==2){
                                updateReqVO.setHhandle(3);
                                updateReqVO.setStatus(4);
                            }
                        }

                        //对赵校长批量通过的牵头领导的数据单独处理
                        if(updateReqVO.getStatus()==30&&updateReqVO.getPrincipalhandle()!=null&&updateReqVO.getPrincipalhandle()==1){
                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2){
                                updateReqVO.setStatus(4);
                                updateReqVO.setHhandle(1);
                                updateReqVO.setLeaderhandle(1);
                                updateReqVO.setPrincipalhandle(0);
                            }
                        }

                        if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2&&updateReqVO.getStatus()==2){
                            updateReqVO.setLeaderhandle(1);
                        }

                        //办理审批根据当前状态自动更新
                        if(!(updateReqVO.getStatus()==30&&(updateReqVO.getShandle()==null||updateReqVO.getShandle()==0))){
                            if(PREDAO.getStatus()==30&&PREDAO.getShandle()==1&&PREDAO.getYeartag()==2){
                                updateReqVO.setShandle(2);
                                updateReqVO.setStatus(30);
                            }

                            if(PREDAO.getStatus()==30&&PREDAO.getShandle()==2&&PREDAO.getYeartag()==2){
                                updateReqVO.setShandle(3);
                                updateReqVO.setStatus(30);
                            }

                            if(PREDAO.getStatus()==30&&PREDAO.getShandle()==3&&PREDAO.getYeartag()==2){
                                updateReqVO.setShandle(4);
                                updateReqVO.setStatus(30);
                            }
                        }

                        //对赵校长办理情况审批判断是正在推进还是已办结
                        if(PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==4&&updateReqVO.getStatus()==50){
                            if(PREDAO.getProcessStatus()==1){
                                updateReqVO.setIsApprove(true);
                                updateReqVO.setStatus(30);
                                updateReqVO.setShandle(0);
                            }
                            if(PREDAO.getProcessStatus()==2){
                                updateReqVO.setStatus(50);
                            }
                        }

                        String result = inforService.updateInfor(updateReqVO);
                        if (Objects.equals(result, "success")) {
                            success++;
                        }
                    }

                }
                fail = size - success;

//                if(status!=null){
//
//                    if(status ==50){
//                        List<InforRespVO> filterone =   filterall.stream().filter(it-> it.getShandle().intValue()==1 && it.getStatus().intValue() ==30).collect(Collectors.toList());
//
//                        LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                        lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                        lambdaQueryWrapperX.set(InforDO::getStatus , status);
//                        inforService.update(lambdaQueryWrapperX);
//
//                        success = filterone.size();
//                        fail = filterall.size() - success;
//                    }else{
//                        LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                        lambdaQueryWrapperX.in(InforDO::getId  ,  filterall.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                        lambdaQueryWrapperX.set(InforDO::getStatus , status);
//                        inforService.update(lambdaQueryWrapperX);
//
//                        success = filterall.size();
//                        fail = filterall.size() - success;
//                    }
//
//
//                }
//
//                if(hhandle!=null){
//                    List<InforRespVO> filterone =   filterall.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getHhandle , hhandle);
//                    inforService.update(lambdaQueryWrapperX);
//
//                    success = filterone.size();
//                    fail = filterall.size() - success;
//                }
//
//                if(ministerhandle!=null){
//                    List<InforRespVO> filterone =   filterall.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getMinisterhandle , ministerhandle);
//                    inforService.update(lambdaQueryWrapperX);
//
//                    success = filterone.size();
//                    fail = filterall.size() - success;
//                }
//
//                if(masterhandle!=null){
//                    List<InforRespVO> filterone =   filterall.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getMasterhandle , masterhandle);
//                    inforService.update(lambdaQueryWrapperX);
//
//                    success = filterone.size();
//                    fail = filterall.size() - success;
//                }

            }else{
                if(status!=null){
                    alllist =   alllist.stream().filter(it-> it.getShandle().intValue()==1 && it.getStatus().intValue() ==30).collect(Collectors.toList());
                    if(hhandle!=null){
                        alllist =   alllist.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
                    }

                    if(ministerhandle!=null){
                        alllist =   alllist.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
                    }

                    if(masterhandle!=null){
                        alllist =   alllist.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
                    }
                }


                alllist.forEach(list -> list.setShandle(null));
                List<InforUpdateReqVO> updateReqVObatch = InforConvert.INSTANCE.convertList05(alllist);

                if(status != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setStatus(status.intValue()));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setStatus(null));
                }
                if (masterhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMasterhandle(masterhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMasterhandle(null));
                }
                if (ministerhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMinisterhandle(ministerhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setMinisterhandle(null));
                }
                if (hhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setHhandle(hhandle.intValue()));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setHhandle(null));
                }
                if (shandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setShandle(shandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setShandle(null));
                }
                if (leaderhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setLeaderhandle(leaderhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setLeaderhandle(null));
                }
                if (principalhandle != null) {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setPrincipalhandle(principalhandle));
                } else {
                    updateReqVObatch.forEach(updateReqVO -> updateReqVO.setPrincipalhandle(null));
                }
                updateReqVObatch.forEach(udateReqVO -> udateReqVO.setUserinforDOList(null));

                if(CollectionUtil.isNotEmpty(updateReqVObatch)){
                    for (InforUpdateReqVO updateReqVO : updateReqVObatch) {
                        InforDO PREDAO = inforService.getInfor(updateReqVO.getId());

                        //填报审批根据当前状态自动更新
                        if(!(updateReqVO.getStatus()==2&&updateReqVO.getHhandle()==0)){
                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==0&&PREDAO.getYeartag()==2){
                                updateReqVO.setHhandle(2);
                                updateReqVO.setStatus(4);
                            }

                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2){
                                updateReqVO.setHhandle(1);
                                updateReqVO.setLeaderhandle(1);
                                updateReqVO.setStatus(4);
                            }

                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==1&&PREDAO.getYeartag()==2){
                                updateReqVO.setHhandle(3);
                                updateReqVO.setStatus(4);
                            }
                        }

                        //对赵校长批量通过的牵头领导的数据单独处理
                        if(updateReqVO.getStatus()==30&&updateReqVO.getPrincipalhandle()!=null&&updateReqVO.getPrincipalhandle()==1){
                            if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2){
                                updateReqVO.setStatus(4);
                                updateReqVO.setHhandle(1);
                                updateReqVO.setLeaderhandle(1);
                                updateReqVO.setPrincipalhandle(0);
                            }
                        }

                        if(PREDAO.getStatus()==4&&PREDAO.getHhandle()==2&&PREDAO.getYeartag()==2&&updateReqVO.getStatus()==2){
                            updateReqVO.setLeaderhandle(1);
                        }

                        //办理审批根据当前状态自动更新
                        if(!(updateReqVO.getStatus()==30&&(updateReqVO.getShandle()==null||updateReqVO.getShandle()==0))){
                            if(PREDAO.getStatus()==30&&PREDAO.getShandle()==1&&PREDAO.getYeartag()==2){
                                updateReqVO.setShandle(2);
                                updateReqVO.setStatus(30);
                            }

                            if(PREDAO.getStatus()==30&&PREDAO.getShandle()==2&&PREDAO.getYeartag()==2){
                                updateReqVO.setShandle(3);
                                updateReqVO.setStatus(30);
                            }

                            if(PREDAO.getStatus()==30&&PREDAO.getShandle()==3&&PREDAO.getYeartag()==2){
                                updateReqVO.setShandle(4);
                                updateReqVO.setStatus(30);
                            }
                        }

                        //对赵校长办理情况审批判断是正在推进还是已办结
                        if(PREDAO.getStatus().intValue() ==30 &&PREDAO.getShandle().intValue() ==4&&updateReqVO.getStatus()==50){
                            if(PREDAO.getProcessStatus()==1){
                                updateReqVO.setIsApprove(true);
                                updateReqVO.setStatus(30);
                                updateReqVO.setShandle(0);
                            }
                            if(PREDAO.getProcessStatus()==2){
                                updateReqVO.setStatus(50);
                            }
                        }

                        String result = inforService.updateInfor(updateReqVO);
                        if (Objects.equals(result, "success")) {
                            success++;
                        }
                    }

                }
                fail = size - success;


//                if(status!=null){
//
//                    List<InforRespVO> filterone =   alllist.stream().filter(it-> it.getShandle().intValue()==1 && it.getStatus().intValue() ==30).collect(Collectors.toList());
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  alllist.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getStatus , status);
//                    inforService.update(lambdaQueryWrapperX);
//                    success = filterone.size();
//                    fail = alllist.size() - success;
//                }
//
//
//                if(hhandle!=null){
//                    List<InforRespVO> filterone =   alllist.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getHhandle , hhandle);
//                    inforService.update(lambdaQueryWrapperX);
//
//                    success = filterone.size();
//                    fail = alllist.size() - success;
//
//                }
//
//                if(ministerhandle!=null){
//                    List<InforRespVO> filterone =   alllist.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getMinisterhandle , ministerhandle);
//                    inforService.update(lambdaQueryWrapperX);
//
//                    success = filterone.size();
//                    fail = alllist.size() - success;
//
//                }
//
//                if(masterhandle!=null){
//                    List<InforRespVO> filterone =   alllist.stream().filter(it-> it.getShandle().intValue()==0 && it.getStatus().intValue() ==4).collect(Collectors.toList());
//
//                    LambdaUpdateWrapper<InforDO> lambdaQueryWrapperX = new LambdaUpdateWrapper();
//                    lambdaQueryWrapperX.in(InforDO::getId  ,  filterone.stream().map(it-> it.getId()).collect(Collectors.toList()) );
//                    lambdaQueryWrapperX.set(InforDO::getMasterhandle , masterhandle);
//                    inforService.update(lambdaQueryWrapperX);
//
//                    success = filterone.size();
//                    fail = alllist.size() - success;
//
//                }
            }

        }

        return success(UpdateAll.builder().success(success).fail(fail).build());
    }

    @PostMapping("/delete")
    @ApiOperation("删除重点工作信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:delete')")
    public CommonResult<Boolean> deleteInfor(@RequestParam("id") Integer id) {
        inforService.deleteInfor(id);
        return success(true);
    }

    @Resource
    UserinforMapper userinforMapper;


    @GetMapping("/get")
    @ApiOperation("获得重点工作信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:query')")
    public CommonResult<InforRespVO> getInfor(@RequestParam("id") Integer id) {
        InforDO infor = inforService.getInfor(id);

        InforRespVO respVO  = InforConvert.INSTANCE.convert(infor);

        try {
            LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
            lambdaQueryWrapper.eq(UserinforDO::getInforid , infor.getId());
            List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
            respVO.setUserinforDOList(all);
        }catch (Exception E){
            E.printStackTrace();
        }
        return success(respVO);
    }

    @GetMapping("/list")
    @ApiOperation("获得重点工作信息列表")
    // @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:query')")
    public CommonResult< List<InforExcelVO>> getInforList(@Valid InforPageReqVO pageVO) {

        paevoconvert(pageVO);


        List<String> types = ListUtil.of("oa-work-new-taskType");
        List<DictDataRespDTO>  list1 = dictDataApi.getByDictTypes(types).getData();

        List<String> types1 = ListUtil.of("oa-work-new-urgencyLevel");
        List<DictDataRespDTO>  digreetype = dictDataApi.getByDictTypes(types1).getData();


        List<InforRespVO> list = inforService.getInforList(pageVO);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(pageVO.getYeartag()!=null&&pageVO.getYeartag()==2){
            list.removeIf(info -> ((info.getStatus() == 20) && (info.getYeartag() == 0 || info.getYeartag() == 1) && LocalDateTime.parse(info.getStarttime(), df).isAfter(LocalDateTime.now())));
        }


        // 导出 Excel
        List<InforExcelVO> datas = InforConvert.INSTANCE.convertList03(list);
        for (int i = 0; i < datas.size(); i++) {
            datas.get(i).setCreateTime(list.get(i).getCreateTime());
            if (list.get(i).getStatus().intValue() == 50) {
                datas.get(i).setTxtstatus("已办结");
            } else if(list.get(i).getShandle().intValue() == 1) {
                datas.get(i).setTxtstatus("完成");
            } else {
                datas.get(i).setTxtstatus("正在推进");
            }
        }
        InforListConvert.convertList(datas ,list , list1 ,digreetype);
        if(pageVO.getMleaderId()!=null){
            datas=datas.stream().filter(d->d.getMleaderId().equals(pageVO.getMleaderId())).collect(Collectors.toList());
        }
        datas=datas.stream().sorted(Comparator.comparing(InforExcelVO::getEndtime)).collect(Collectors.toList());
        return success(datas);
    }

    @Resource
    DictDataApi dictDataApi;

        @GetMapping("/count")
    @ApiOperation("获得重点工作信息统计")
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:query')")
    public CommonResult<InforCountRespVO> getInforCount(@Valid InforPageReqVO pageVO) {

        if(pageVO.getIdlist()==null){



            paevoconvert(pageVO);

//            if(pageVO.getMdeptid()!=null){
//
//
//                List<String> alldptstr = ListUtil.of("oa_taskinfor_dept");
//
//
//
//                List<DictDataRespDTO>  alldpts = dictDataApi.getByDictTypes(alldptstr).getData();
//
//                DictDataRespDTO rootid = alldpts.stream().filter(it -> ObjectUtil.equals("root_dept" ,it.getLabel())).findFirst().get();
//
//                if( NumberUtil.equals(Long.valueOf(pageVO.getMdeptid()) , Long.valueOf( rootid.getValue()))){
//
//                    CommonResult<List<DeptRespDTO>> allchild =  deptApi.getAllChildrenDept(Long.valueOf(rootid.getValue()));
//                    List<Long> ids = allchild.getData().stream().map(it-> it.getId()).collect(Collectors.toList());
//                    ids.add(Long.valueOf(rootid.getValue()));
//
//                    pageVO.setDeptids(ids);
//
//                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
//                    lambdaQueryWrapper.in(UserinforDO::getDptid , ids);
//                    lambdaQueryWrapper.eq(UserinforDO::getUtype , 1);
//                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
//                    pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
//
//                }else{
//                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
//                    lambdaQueryWrapper.eq(UserinforDO::getDptid , pageVO.getMdeptid());
//                    lambdaQueryWrapper.eq(UserinforDO::getUtype , 1);
//                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
//                    pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
//                }
//
//            }
        }

        //单独对中共湖南省委党校做处理，选择中共湖南省委党校时变成全选
            if(pageVO.getSdeptid()!=null&&pageVO.getSdeptid()==106){
                pageVO.setSdeptid(null);
            }

        LambdaQueryWrapper<UserinforDO> lqw =new LambdaQueryWrapper<>();
        lqw.eq(UserinforDO::getTenantId,getTenantId());
        lqw.eq(UserinforDO::getUtype,1);
        lqw.eq(UserinforDO::getUid,getLoginUserId());

        List<UserinforDO> userinforDOList = userinforMapper.selectList(lqw);

        Set<Integer> idSet = userinforDOList.stream().map(UserinforDO::getInforid).collect(Collectors.toSet());

        AdminUserRespDTO userRespDTO =  adminUserApi.getUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();

        LambdaQueryWrapper<InforDO> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InforDO::getStatus,50);
        lambdaQueryWrapper.eq(InforDO::getTenantId,getTenantId());
        lambdaQueryWrapper.eq(InforDO::getYeartag,2);
        lambdaQueryWrapper.eq(pageVO.getSdeptid()!=null,InforDO::getSdeptid,pageVO.getSdeptid());
        lambdaQueryWrapper.le(pageVO.getEndtime()!=null,InforDO::getCreateTime, pageVO.getEndtime());

        if(inforService.judgeRole().getIsDirector()){

            lambdaQueryWrapper.eq(InforDO::getSdeptid,userRespDTO.getDeptId());

        }else if(inforService.judgeRole().getIsHeadmaster()||inforService.judgeRole().getIsAdmin()){

        }else {
            lambdaQueryWrapper.eq(InforDO::getSuserid,getLoginUserId());
        }

        if(idSet.size()>0){
            lambdaQueryWrapper.or(wrapper->{
                wrapper.eq(InforDO::getStatus,50);
                wrapper.in(InforDO::getId,idSet);
                wrapper.in(InforDO::getYeartag,0,1);
                wrapper.eq(pageVO.getSdeptid()!=null,InforDO::getSdeptid,pageVO.getSdeptid());
                wrapper.le(pageVO.getEndtime()!=null,InforDO::getCreateTime, pageVO.getEndtime());
            });
        }

            List<InforDO> inforDOList = inforMapper.selectList(lambdaQueryWrapper);


            List<InforRespVO> list = inforService.getInforList(pageVO);

        InforCountRespVO results = new InforCountRespVO();
        results.setCompleted(0l);
        results.setCountall(0l);
        results.setPercent(BigDecimal.valueOf(0));

        Integer countAll=list.size()+inforDOList.size();

        if(countAll>0){

//            Map<Integer, Long> countByOccupation = list.stream()
//                    .filter(person -> ObjectUtil.isNotEmpty(person.getStatus())&&50 == person.getStatus()) // 过滤掉空属性
//                    .collect(Collectors.groupingBy(InforRespVO::getStatus, Collectors.counting()));

            if(CollectionUtil.isEmpty(inforDOList)){
                results.setCompleted(0l);
                results.setCountall(Long.valueOf(countAll));
                results.setPercent(BigDecimal.valueOf(0));
            }else{
                results.setCompleted((long) inforDOList.size());
                results.setCountall(Long.valueOf(countAll));


                BigDecimal result = BigDecimal.valueOf(inforDOList.size())
                        .divide(BigDecimal.valueOf(countAll), 2, BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
                results.setPercent(result);
            }






            List<String> types = ListUtil.of("oa-work-new-taskType");
            List<DictDataRespDTO>  list1 = dictDataApi.getByDictTypes(types).getData();

            List<String> key =  list1.stream().map(it->it.getLabel()).collect(Collectors.toList());
            List<String> value =  list1.stream().map(it->it.getValue()).collect(Collectors.toList());

            for (int i = 0; i < value.size(); i++) {

                Integer inti = Integer.valueOf(value.get(i));
                Map<Integer, Long> countByO= list.stream()
                        .filter(person -> ObjectUtil.isNotEmpty(person.getTasktype())&& inti == person.getTasktype()) // 过滤掉空属性
                        .collect(Collectors.groupingBy(InforRespVO::getTasktype, Collectors.counting()));


                if(countByO.get(inti)!=null){
                    Entity temp = new Entity();
                    temp.set(key.get(i) , countByO.get(inti) );

                    results.getTypes().add(temp);
                }

            }



        }

        return success(results);
    }

    @GetMapping("/page")
    @ApiOperation("获得重点工作信息分页")
    //////////@PreAuthorize("@ss.hasPermission('yeartask:infor:query')")
    public CommonResult<PageResult<InforRespVO>> getInforPage(@Valid InforPageReqVO pageVO) {




            paevoconvert(pageVO);



        PageResult<InforRespVO> pageResult = inforService.getInforPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/inforSmsBatch")
    @ApiOperation("一键催办")
    //////////@PreAuthorize("@ss.hasPermission('yeartask:infor:query')")
    public CommonResult<List<String>> inforSmsBatch(@Valid InforPageReqVO pageVO) {
        pageVO.setPageNo(1);
        pageVO.setPageSize(100000);

        paevoconvert(pageVO);



        PageResult<InforRespVO> pageResult = inforService.getInforPage(pageVO);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        for (InforRespVO inforRespVO : pageResult.getList()) {
            if((inforRespVO.getYeartag()==2&&inforRespVO.getStatus()==30)||(inforRespVO.getYeartag()!=2&&(inforRespVO.getStatus()==20||inforRespVO.getStatus()==30))){
                LocalDateTime starttime = LocalDateTime.parse(inforRespVO.getStarttime(),formatter);
                LocalDateTime endtime = LocalDateTime.parse(inforRespVO.getEndtime(),formatter);
                //LocalDateTime currentDateTime = LocalDateTime.now();
                long totalDurationMinutes = ChronoUnit.MINUTES.between(starttime, endtime);
                long eightyPercentRange = (long) (totalDurationMinutes * 0.8);

                LocalDateTime eightyPercentStart = starttime.plusMinutes(eightyPercentRange);
                //任务进展为正常
                if(now.isBefore(eightyPercentStart)){
                    inforRespVO.setTaskProgress(1);
                }
                //任务进展为告警
                else if (now.isAfter(eightyPercentStart)&&now.isBefore(endtime)){
                    inforRespVO.setTaskProgress(2);
                }
                //任务进展为逾期
                else {
                    inforRespVO.setTaskProgress(3);
                }
            }
        }


            pageResult.setList(pageResult.getList().stream().filter(it->it.getTaskProgress()!=null&&it.getTaskProgress()==3).collect(Collectors.toList()));



        List<Integer> ids=new ArrayList<>();
        List<String> taskContentList=new ArrayList<>();
        if(pageResult!=null){
             ids = pageResult.getList().stream().map(InforRespVO::getId).collect(Collectors.toList());
        }
        if (!ids.isEmpty()){
            for (Integer id : ids) {
                String message = null;
                InforDO inforDO = inforMapper.selectById(id);

                CommonResult<InforRespVO> result =  getInfor(id);

                if(result!=null && 0 == result.getCode()){

                    if(result.getData().getYeartag()==2){
                        if (30 ==result.getData().getStatus()&&result.getData().getShandle()==0) {
                            message ="【湖南省委党校】 您好！您有一件已逾期的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时进行处理。"; //待办-
                        }
                    }
                    if(result.getData().getYeartag()==0 ||result.getData().getYeartag()==1){
                        if( 20 ==result.getData().getStatus() ){
                            message = "【湖南省委党校】您好！您有一件待签收的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时签收。"; //待签收-
                        } else if (30 ==result.getData().getStatus()) {
                            message ="【湖南省委党校】您好！您有一件已逾期的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时进行处理。"; //待办-
                        }
                    }



                    if(message!=null){
                        CommonResult<List<AdminUserRespDTO>> userinfors=new CommonResult<>();

                        if(result.getData().getYeartag()==0 ||result.getData().getYeartag()==1){
                            List<UserinforDO> userinforDOList = result.getData().getUserinforDOList().stream().filter(it -> 1 ==it.getUtype() ).collect(Collectors.toList());
                            List<Long> uids = userinforDOList.stream().map(it -> Long.valueOf(it.getUid())).collect(Collectors.toList());
                            userinfors  =  adminUserApi.getUsers(uids);
                        }

                        if(result.getData().getYeartag()==2){
                            Long uid= Long.valueOf(inforDO.getSuserid());
                            CommonResult<AdminUserRespDTO> user = adminUserApi.getUser(uid);
                            List<AdminUserRespDTO> users=new ArrayList<>();
                            users.add(user.getData());
                            userinfors.setData(users);
                        }


                        if(userinfors!=null && userinfors.getData()!=null){



                            if(CollectionUtil.isEmpty(userinfors.getData())){
                            }

                            String mobile = userinfors.getData().stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                                    .reduce( (k,v)-> k +"," + v  ).get();


                            Map<String,Object> map = new HashMap<>();
                            map.put("arg1", message);

                            taskExecutor.submit(new Runnable() {
                                @Override
                                public void run() {
                                    Long messageid  = smsSendService.sendSingleSms(mobile, null,null ,"admin-sms-login-new",map);
                                }
                            });


                            taskContentList.add(result.getData().getTaskcontent());


                            InforDO  updateReqVO = new InforDO();
                            updateReqVO.setId(id) ;
                            updateReqVO.setSuperviseStatus(1);
                            inforMapper.updateById(updateReqVO);



                        }
                    }


                }


            }
        }

        return CommonResult.success(taskContentList);

    }

    public void paevoconvert(InforPageReqVO pageVO) {

        if(pageVO.getIdlist()==null){






            AdminUserRespDTO userRespDTO =  adminUserApi.getUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();

            List<DeptRespDTO> parentDepts=new ArrayList<>();
            if(userRespDTO.getDeptIds().size()==0){
                parentDepts.add(deptApi.getDept(userRespDTO.getDeptId()).getCheckedData());
            }else {
                parentDepts = deptApi.getDepts(userRespDTO.getDeptIds()).getCheckedData();
            }
            if(pageVO.getLeadersee()!=null){
                pageVO.setDeptids(parentDepts.stream().map(it-> it.getId()).collect(Collectors.toList()));
            }


            if(pageVO.getLeadersee()!=null){
                pageVO.setDeptids(parentDepts.stream().map(it-> it.getId()).collect(Collectors.toList()));
            }







            boolean isleaderuser =false;
            for (int i = 0; i < parentDepts.size(); i++) {
                if(parentDepts.get(i).getLeaderUserId()!=null && parentDepts.get(i).getLeaderUserId().intValue()==userRespDTO.getId().intValue()){
                    isleaderuser =true;
                    break;
                }
            }

                //待办理 已办结  待审批  （状态集合）
            if(pageVO.getStatusList()!=null || pageVO.getStatus()!=null &&  (pageVO.getStatus().intValue()== 30  ||  pageVO.getStatus().intValue()== 4  ||  pageVO.getStatus().intValue()== 50)){

                if(pageVO.isXcxdb() ==false){
                    if( (pageVO.getYeartag()!=null && pageVO.getYeartag().intValue()==2) ){
                        Long uid  = SecurityFrameworkUtils.getLoginUserId();
                        List<Long> roletempDOList = userinforMapper.selectByTenantId(SecurityFrameworkUtils.getTenantId());
                        List<Long> principalList = userinforMapper.selectPrincipalByTenantId(SecurityFrameworkUtils.getTenantId());
                        roletempDOList.addAll(principalList);

                        //办公室管理员看租户全部，抹掉前端传的用户限制
                        if(roletempDOList.contains(uid)){
                            if(pageVO.getSuperuserid()==null){
                                pageVO.setSuperuserid(null);
                            }
                            pageVO.setMuserid(null);
                            pageVO.setMuseridLeader(null);
                            pageVO.setSuseridLeader(null);
                            if(pageVO.getSuserid()==null){
                                pageVO.setSuserid(null);
                            }
                        }
                    }
                }else{



                    Long uid  = SecurityFrameworkUtils.getLoginUserId();
                    List<Long> roletempDOList = userinforMapper.selectByTenantId(SecurityFrameworkUtils.getTenantId());
                    if(roletempDOList.contains(uid)){

                        List<InforDO> inforDOList=new ArrayList<>();
                        if(pageVO.getStatus()==50){
                            LambdaQueryWrapper<InforDO> lqw=new LambdaQueryWrapper<>();
                            lqw.eq(InforDO::getSuserid,pageVO.getMuserid());
                            inforDOList = inforMapper.selectList(lqw);
                        }

                        //登录用户是办公室管理员
                        pageVO.setMuserid(null);
                        LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                        lambdaQueryWrapper.eq(UserinforDO::getUid , SecurityFrameworkUtils.getLoginUserId() );
                        lambdaQueryWrapper.eq(UserinforDO::getUtype , 1);

                        //查询办公室管理员自己作为主办人的数据
                        List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);

                        Set idset = new HashSet(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));

                        idset.addAll(inforDOList.stream().map(InforDO::getId).collect(Collectors.toList()));


                        LambdaQueryWrapperX<InforDO> lambdaQueryWrapperx = new LambdaQueryWrapperX();
                        lambdaQueryWrapperx.eq(InforDO::getStatus , pageVO.getStatus());
                        lambdaQueryWrapperx.eq(InforDO::getYeartag , 2);

                        //查询租户指定status 的 月度中心工作
                        List<InforDO> allx =  inforMapper.selectList(lambdaQueryWrapperx);

                        idset.addAll(allx.stream().map(it->it.getId()).collect(Collectors.toList()));

                        //idlist 用于限制返回结果
                        pageVO.setIdlist(new ArrayList<>(idset));

                    }



                }


            }


            //前端认为是处长
            if(pageVO.getIsDirector()!=null && pageVO.getIsDirector().intValue()==1){




                if(isleaderuser){
                    //后端也认为是处长
                    List<DeptRespDTO> deptList = deptApi.getByLeaderUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();
                    List<Long> deptIds = deptList.stream().map(DeptRespDTO::getId).collect(Collectors.toList());

                    pageVO.setQueryDeptIds(deptIds);

                    //处长查看整个部门的 把前端传的人员限制条件抹掉
                    pageVO.setSdeptid(null);
                    pageVO.setHhandle(null);
                    pageVO.setSuperuserid(null);
                    pageVO.setMuserid(null);
                    pageVO.setMuseridLeader(null);
                    pageVO.setSuseridLeader(null);
                    pageVO.setSuserid(null);


//                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
//                    lambdaQueryWrapper.eq(UserinforDO::getUid , userRespDTO.getId() );
//                    lambdaQueryWrapper.eq(UserinforDO::getDptid , userRespDTO.getDeptId());
//                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
//                    List<Integer> allids =   all.stream().map(it->it.getInforid()).collect(Collectors.toList());
//
//
//                    LambdaQueryWrapperX<InforDO> lambdaQueryWrapperxx = new LambdaQueryWrapperX();
//                    lambdaQueryWrapperxx.in(InforDO::getId , allids );
//
//                    List<InforDO> inforallids = null ;
//
//                    if(CollectionUtil.isNotEmpty(allids)){
//                        inforallids =  inforMapper.selectList(lambdaQueryWrapperxx);
//                        LambdaQueryWrapperX<InforDO> lambdaQueryWrapperx = new LambdaQueryWrapperX();
//                        lambdaQueryWrapperx.in(InforDO::getStatus , ListUtil.of(1,4));
//                        lambdaQueryWrapperx.eq(InforDO::getHhandle , 0);
//                        lambdaQueryWrapperx.in(InforDO::getId , allids );
//
//                        List<InforDO> redude =null ;
//                        try {
//                            if(CollectionUtil.isNotEmpty(allids)){
//                                redude  =  inforMapper.selectList(lambdaQueryWrapperx);
//
//                                if(redude!=null){
//                                    inforallids.removeAll(redude);
//                                }
//
//                            }
//                        }catch (Exception e){
//                            e.printStackTrace();
//                        }
//
//                    }
//
//                    if(CollectionUtil.isNotEmpty(inforallids)){
//                        pageVO.setIdlist(inforallids.stream().map(it->it.getId()).collect(Collectors.toList()));
//                    }




                }
                else{
                    //不是处长的人，查看自己作为处长的历史数据 （曾经可能当过处长）
                    pageVO.setMuseridLeader(SecurityFrameworkUtils.getLoginUserId().intValue());
                }
            }




            if(pageVO.getSuperuserid()!=null){

                //指定督办人 查看全部作为督办人的id  in（ids）

                pageVO.setSuperuserid(Math.toIntExact(SecurityFrameworkUtils.getLoginUserId()));

                LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                lambdaQueryWrapper.eq(UserinforDO::getUid , pageVO.getSuperuserid());
                lambdaQueryWrapper.eq(UserinforDO::getUtype , 2);
                List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
                pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
            }

            if(pageVO.getMuserid()!=null){

                //指定主办人 查看全部作为主办人的id  in（ids）

                pageVO.setMuserid(Math.toIntExact(SecurityFrameworkUtils.getLoginUserId()));

                LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                lambdaQueryWrapper.eq(UserinforDO::getUid , pageVO.getMuserid());
                if( (pageVO.getStatus()!=null&&pageVO.getStatus().intValue()==50)  && (pageVO.getYeartag()!=null && pageVO.getYeartag().intValue()==2)){

                    //指定的33的主办部门负责人 可以 查看到 主办人的（已办结 和 已关闭）
                    lambdaQueryWrapper.in(UserinforDO::getUtype , 1 ,  33 );
                }else{

                    //其他状态看自己的
                    lambdaQueryWrapper.eq(UserinforDO::getUtype , 1);
                }
                List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);

                List<InforDO> inforDOList=new ArrayList<>();
                if(pageVO.getStatus()!=null&&pageVO.getStatus()==50){
                    LambdaQueryWrapper<InforDO> lqw=new LambdaQueryWrapper<>();
                    lqw.eq(InforDO::getSuserid,pageVO.getMuserid());
                    inforDOList = inforMapper.selectList(lqw);
                }
                Set<Integer> idList=new HashSet<>();
                idList.addAll(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
                idList.addAll(inforDOList.stream().map(it->it.getId()).collect(Collectors.toList()));
                pageVO.setIdlist(new ArrayList<>(idList) );

//                if(pageVO.getStatus()!=null&&pageVO.getStatus().intValue()==50){
//
//                        //已办结的 主办人能看到 发起人也必须看到 在这里添加 发起人能看到的
//
//                    LambdaQueryWrapperX<InforDO> inforDOLambdaQueryWrapperX = new LambdaQueryWrapperX();
//                    inforDOLambdaQueryWrapperX.eq(InforDO::getSuserid , pageVO.getMuserid());
//                    inforDOLambdaQueryWrapperX.eq(InforDO::getStatus , 50);
//                    List<InforDO> allinfor =  inforMapper.selectList(inforDOLambdaQueryWrapperX);
//
//                    pageVO.getIdlist().addAll(allinfor.stream().map(it->it.getId()).collect(Collectors.toList()));
//
//                }
            }


            if(pageVO.getMdeptid()!=null){

                //指定主办部门
                LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                lambdaQueryWrapper.eq(UserinforDO::getDptid , pageVO.getMdeptid());
                lambdaQueryWrapper.eq(UserinforDO::getUtype , 1);
                List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
                pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
            }



            if(pageVO.getMuseridLeader()!=null){


                //处长看当前部门的 不是看自己的
                pageVO.setMuseridLeader(Math.toIntExact(SecurityFrameworkUtils.getLoginUserId()));


                //是处长看当前部门的
                if(isleaderuser){


                    List<DeptRespDTO> deptList = deptApi.getByLeaderUser(SecurityFrameworkUtils.getLoginUserId()).getCheckedData();

                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                    lambdaQueryWrapper.eq(UserinforDO::getUid , pageVO.getMuseridLeader());
                    lambdaQueryWrapper.eq(UserinforDO::getUtype , 11);
                    lambdaQueryWrapper.in(UserinforDO::getDptid , deptList.stream().map(it->it.getId()).collect(Collectors.toList()));
                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
                    pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));

                }else{
                    //不是处长看自己的
                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                    lambdaQueryWrapper.eq(UserinforDO::getUid , pageVO.getMuseridLeader());
                    lambdaQueryWrapper.eq(UserinforDO::getUtype , 11);
                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
                    pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
                }



            }


            if(pageVO.getSuseridLeader()!=null){

                pageVO.setSuseridLeader(Math.toIntExact(SecurityFrameworkUtils.getLoginUserId()));

                List<Long> roletempDOList = userinforMapper.selectByTenantId(userRespDTO.getTenantId());
                List<Long> principalList = userinforMapper.selectPrincipalByTenantId(SecurityFrameworkUtils.getTenantId());
                roletempDOList.addAll(principalList);

                if(roletempDOList.contains(userRespDTO.getId())){
                    pageVO.setSuseridLeader(null);
                }else{

                    LambdaQueryWrapperX<UserinforDO> lambdaQueryWrapper = new LambdaQueryWrapperX();
                    lambdaQueryWrapper.eq(UserinforDO::getUid , pageVO.getSuseridLeader());
                    lambdaQueryWrapper.eq(UserinforDO::getUtype , 33);
                    List<UserinforDO> all =  userinforMapper.selectList(lambdaQueryWrapper);
                    pageVO.setIdlist(all.stream().map(it->it.getInforid()).collect(Collectors.toList()));
                }


            }







        }




    }

    @GetMapping("/export-excel")
    @ApiOperation("导出重点工作信息 Excel")
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:export')")
    @OperateLog(type = EXPORT)
    public void exportInforExcel(@Valid InforPageReqVO pageVO,
              HttpServletResponse response   ) throws IOException {


            paevoconvert(pageVO);





        List<String> types = ListUtil.of("oa-work-new-taskType");
        List<DictDataRespDTO>  list1 = dictDataApi.getByDictTypes(types).getData();

        List<String> types1 = ListUtil.of("oa-work-new-urgencyLevel");
        List<DictDataRespDTO>  digreetype = dictDataApi.getByDictTypes(types1).getData();


            List<InforRespVO> list = inforService.getInforList(pageVO);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime now = LocalDateTime.now();

        for (InforRespVO inforRespVO : list) {
            if(inforRespVO.getYeartag()==2&&inforRespVO.getStatus()==30){
                LocalDateTime starttime = LocalDateTime.parse(inforRespVO.getStarttime(),formatter);
                LocalDateTime endtime = LocalDateTime.parse(inforRespVO.getEndtime(),formatter);
                //LocalDateTime currentDateTime = LocalDateTime.now();
                long totalDurationMinutes = ChronoUnit.MINUTES.between(starttime, endtime);
                long eightyPercentRange = (long) (totalDurationMinutes * 0.8);

                LocalDateTime eightyPercentStart = starttime.plusMinutes(eightyPercentRange);
                //任务进展为正常
                if(now.isBefore(eightyPercentStart)){
                    inforRespVO.setTaskProgress(1);
                }
                //任务进展为告警
                else if (now.isAfter(eightyPercentStart)&&now.isBefore(endtime)){
                    inforRespVO.setTaskProgress(2);
                }
                //任务进展为逾期system/oaNotice/personalNotice
                else {
                    inforRespVO.setTaskProgress(3);
                }
            }

            if(inforRespVO.getIsHandle()==1&&inforRespVO.getHandleCount()==1){
                inforRespVO.setEndcontent(null);
            }
        }


        //如果导出的数据里没有utype=0的数据，则将去除(牵头部门筛选)
        if(pageVO.getDeptId()!=null){
            Boolean isRemove=true;
//                        PageResult<InforRespVO> temp =inforDOPageResult;
            List<InforRespVO> temp=list;
            List<InforRespVO> removeTemp=new ArrayList<>();
            for (InforRespVO inforRespVO : temp) {
                List<UserinforDO> temp1=inforRespVO.getUserinforDOList();
                for (UserinforDO userinforDO : temp1) {
                    if(userinforDO.getUtype()==0){
                        isRemove=false;
                        break;
                    }
                }
                if(isRemove){
                    removeTemp.add(inforRespVO);
                }
                isRemove=true;
            }
            list.removeAll(removeTemp);
        }


            try {
                if(CollectionUtil.isNotEmpty(list)&&CollectionUtil.isNotEmpty(pageVO.getNocheckidlist())){
                    list = list.stream().filter(it ->  !pageVO.getNocheckidlist().contains(it.getId())).collect(Collectors.toList());
                }
            }catch (Exception e){
                log.info("中心工作导出：非选集合  ：  {}"  , e.getMessage());
            }


        List<DeptRespDTO> deptsList = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId()).getData();


        // 导出 Excel
        List<InforExcelVO> datas = InforConvert.INSTANCE.convertList03(list);
        if(pageVO.getYeartag()==2){
            for (int i = 0; i < datas.size(); i++) {
                datas.get(i).setCreateTime(list.get(i).getCreateTime());
                if (list.get(i).getStatus().intValue() == 50) {
                    datas.get(i).setTxtstatus("已办结");
                }

//            if(list.get(i).getStatus().intValue() == 20) {
//                datas.get(i).setTxtstatus("待签收");
//            }
                if(list.get(i).getStatus().intValue() == 4) {
                    datas.get(i).setTxtstatus("填报审批");
                }
                if(list.get(i).getStatus().intValue() == 30) {
                    if(list.get(i).getShandle().intValue() == 0) {
                        datas.get(i).setTxtstatus("办理中");
                    }else {
                        datas.get(i).setTxtstatus("办理情况审批");
                    }
                }
//            if(list.get(i).getStatus().intValue() == 10) {
//                datas.get(i).setTxtstatus("未开始");
//            }
            }
        }

        if(pageVO.getYeartag()<=1){
            for (int i = 0; i < datas.size(); i++) {
                datas.get(i).setCreateTime(list.get(i).getCreateTime());
                if (list.get(i).getStatus().intValue() == 50) {
                    datas.get(i).setTxtstatus("已办结");
                }

                if(list.get(i).getStatus().intValue() == 20) {
                    if( now.isAfter(LocalDateTime.parse(datas.get(i).getStarttime(),formatter))){
                        datas.get(i).setTxtstatus("待签收");
                    }else {
                        datas.get(i).setTxtstatus("未开始");
                    }

                }
                if(list.get(i).getStatus().intValue() == 30) {
                    if(list.get(i).getShandle().intValue() == 1) {
                        datas.get(i).setTxtstatus("完成");
                    }else {
                        datas.get(i).setTxtstatus("办理中");
                    }
                }
//                if(list.get(i).getStatus().intValue() == 10) {
//                    datas.get(i).setTxtstatus("未开始");
//                }
            }
        }



        InforListConvert.convertList(datas ,list , list1 ,digreetype);

//        List<String> deptsNameList = deptsList.stream().map(it -> it.getName()).collect(Collectors.toList());


        if(pageVO.getTaskProgress()!=null&&(pageVO.getTaskProgress()==1||pageVO.getTaskProgress()==2||pageVO.getTaskProgress()==3)){
            datas=datas.stream().filter(it-> pageVO.getTaskProgress().equals(it.getTaskProgress())).collect(Collectors.toList());
        }


        try {
            if(NumberUtil.equals(pageVO.getStatus() ,10)){
                List<InforExceldraftVO> datasdraft = InforConvert.INSTANCE.convertList043(datas);
                for (int i = 0; i < datasdraft.size(); i++) {
                    datasdraft.get(i).setCreateTime(list.get(i).getUpdateTime());
                }
                List<InforExceldraftNewVO> inforExceldraftNewVOS = InforConvert.INSTANCE.convertDraftVOToDraftNewVO(datasdraft);
                ExcelUtils.write(response, "重点工作信息.xls", "数据", InforExceldraftNewVO.class, inforExceldraftNewVOS);
                return;
            }
        }catch (Exception e){
            log.error("导出重点工作信息:{}" , e.getMessage());
        }

        List<InforExcelNewVO> inforExcelNewVOS = InforConvert.INSTANCE.convertExcelVOToExcelNewVO(datas);
        ExcelUtils.write(response, "重点工作信息.xls", "数据", InforExcelNewVO.class, inforExcelNewVOS);
    }


    @Resource
    public SmsSendApi smsSendService;

    @Resource
    public AdminUserApi adminUserApi;




    @GetMapping("/inforsms")
    @ApiOperation("重点工作催办")
    //@PreAuthorize("@ss.hasPermission('yeartask:infor:query')")
    public CommonResult<Long> inforsms(@Valid Integer id  , @RequestParam(required = false,name="userids")  List<Long>  userids) {

        String message = null;
        InforDO inforDO = inforMapper.selectById(id);

        CommonResult<InforRespVO> result =  getInfor(id);

        if(result!=null && 0 == result.getCode()){

            if(result.getData().getYeartag()==2){
                if (30 ==result.getData().getStatus()) {
                    message ="【湖南省委党校】 您好！您有一件已逾期的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时进行处理。"; //待办-
                }
            }
            if(result.getData().getYeartag()==0 ||result.getData().getYeartag()==1){
                if( 20 ==result.getData().getStatus() ){
                    message = "【湖南省委党校】您好！您有一件待签收的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时签收。"; //待签收-
                } else if (30 ==result.getData().getStatus()) {
                    message ="【湖南省委党校】您好！您有一件已逾期的重点工作任务【"+inforDO.getTaskcontent()+"】，请及时进行处理。"; //待办-
                }
            }



            if(message!=null){
                CommonResult<List<AdminUserRespDTO>> userinfors=new CommonResult<>();

                  if(result.getData().getYeartag()==0 ||result.getData().getYeartag()==1){
                      List<UserinforDO> userinforDOList = result.getData().getUserinforDOList().stream().filter(it -> 1 ==it.getUtype() ).collect(Collectors.toList());
                      List<Long> uids = userinforDOList.stream().map(it -> Long.valueOf(it.getUid())).collect(Collectors.toList());
                        userinfors  =  adminUserApi.getUsers(uids);
                  }

                  if(result.getData().getYeartag()==2){
                      Long uid= Long.valueOf(inforDO.getSuserid());
                      CommonResult<AdminUserRespDTO> user = adminUserApi.getUser(uid);
                      List<AdminUserRespDTO> users=new ArrayList<>();
                      users.add(user.getData());
                      userinfors.setData(users);
                  }


                if(userinfors!=null && userinfors.getData()!=null){

                    if(userids!=null){
                        userinfors.setData( userinfors.getData().stream().filter(it -> userids.contains(it.getId())).collect(Collectors.toList()) );
                    }

                    if(CollectionUtil.isEmpty(userinfors.getData())){
                        return CommonResult.success(0l);
                    }

                    String mobile = userinfors.getData().stream().map(it -> it.getMobile()).filter(Objects::nonNull)
                            .reduce( (k,v)-> k +"," + v  ).get();


                    Map<String,Object> map = new HashMap<>();
                    map.put("arg1", message);

                    taskExecutor.submit(new Runnable() {
                        @Override
                        public void run() {
                            Long messageid  = smsSendService.sendSingleSms(mobile, null,null ,"admin-sms-login-new",map);
                        }
                    });





                    InforDO  updateReqVO = new InforDO();
                    updateReqVO.setId(id) ;
                    updateReqVO.setSuperviseStatus(1);
                    inforMapper.updateById(updateReqVO);


                    return CommonResult.success(0l);
                }
            }


        }

        return CommonResult.success(0l);


    }


    @PostMapping("/operationLog")
    @ApiOperation("重点工作信息操作日志")
    public CommonResult<List<YeartaskOperation>> operationLog(@Valid  Long inforid) {

        YeartaskOperation yeartaskOperation = YeartaskOperation.builder().inforid(inforid).build();

        return success(operationMapper.selectYeartaskOperationList(yeartaskOperation));
    }


    @PostMapping("/recordLog")
    @ApiOperation("办理情况信息操作日志")
    public CommonResult<List<YeartaskRecord>> recordLog(@Valid  Long id) {

        LambdaQueryWrapper<YeartaskRecord> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(YeartaskRecord::getInforid,id);
        lambdaQueryWrapper.orderByAsc(YeartaskRecord::getCreatetime);
        List<YeartaskRecord> yeartaskRecordList = yeartaskRecordMapper.selectList(lambdaQueryWrapper);

        return success(yeartaskRecordList);
    }


    @PostMapping("/selectEndtime")
    @ApiOperation("重点工作信息查看截止时间")
    public CommonResult<InforEndtimeDO> selectEndtime(@Valid  Long id) {

        InforEndtimeDO  pre =  endtimeMapper.selectById(id);

        if(pre==null){
            return  success(null);
        }


        DateTime formatDateTime = DateUtil.parseDate(pre.getEndtime());

        DateTime now = DateUtil.dateSecond();

        if(DateUtil.isSameMonth(formatDateTime , now)){
            return success(pre);
        }else{
            int targetYear = now.year();
            int targetMonth = now.month()+1; // 目标月份



            // 获取目标月份的最后一天
            int lastDayOfMonth = DateUtil.lengthOfMonth(targetMonth,  DateUtil.isLeapYear(targetYear) );

            // 如果原始日期的天数大于目标月份的最后一天，则将日期调整为目标月份的最后一天
            if (DateUtil.dayOfMonth(formatDateTime) > lastDayOfMonth) {
                formatDateTime = DateUtil.offsetDay(formatDateTime, lastDayOfMonth - DateUtil.dayOfMonth(formatDateTime));
                formatDateTime = DateUtil.parseDate(targetYear + "-" + targetMonth + "-" +  formatDateTime.dayOfMonth() );
            }else{
                formatDateTime = DateUtil.parseDate(targetYear + "-" + targetMonth + "-" +  formatDateTime.dayOfMonth() );
            }

            setEndtime(id, DateUtil.formatDate(formatDateTime));

            return success(endtimeMapper.selectById(id));

        }




    }


    @PostMapping("/setEndtime")
    @ApiOperation("重点工作信息设置截止时间")
    public CommonResult<Boolean> setEndtime(@Valid  Long id  , String time) {

        InforEndtimeDO pre =   endtimeMapper.selectById(id);

        if(pre==null){

            endtimeMapper.insert(InforEndtimeDO.builder().endtime(time).tenant_id(id).build());

        }else{

            endtimeMapper.updateById(InforEndtimeDO.builder().endtime(time).tenant_id(id).build());
        }


        return success(true);
    }

    @GetMapping("/importExcelTemplateMonth")
    @ApiOperation("月度任务导入模板")
    @PreAuthorize("@ss.hasPermission('patrol:plinfor:export')")
    @OperateLog(type = EXPORT)
    public void exportPlinforAddExcelMonth( HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("月度任务导入模板", "UTF-8").replaceAll("\\+", "%20");
        // 设置文件名称
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<InforImportMonthVO> datas = new ArrayList<>();
        InforImportMonthVO inforImportMonthVO = new InforImportMonthVO();
        AdminUserRespDTO userRespDTO =  adminUserApi.getUser(getLoginUserId()).getData();
        DeptRespDTO dept = deptApi.getDept(userRespDTO.getDeptId()).getCheckedData();
        inforImportMonthVO.setSusername(userRespDTO.getNickname());
        inforImportMonthVO.setSdepname(dept.getName());
        inforImportMonthVO.setMobile(userRespDTO.getMobile());
        datas.add(inforImportMonthVO);
        //机构下的所有部门
        CommonResult<List<DeptRespDTO>> allDepts = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId());
        List<DeptRespDTO> data1 = allDepts.getData();
        List<String> list1=data1.stream().map(DeptRespDTO::getName).collect(Collectors.toList());


        List<String> dictTypes =new ArrayList<>();
        //任务类型
        dictTypes.add("oa-work-new-taskType");
        List<DictDataRespDTO> data = dictDataApi.getByDictTypes(dictTypes).getData();
        List<String> list2=data.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());
//        dictTypes.clear();
        //紧急程度
//        dictTypes.add("oa-work-new-urgencyLevel");
//        data=dictDataApi.getByDictTypes(dictTypes).getData();
//        List<String> list3=data.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());


        EasyExcel.write(response.getOutputStream(), InforImportMonthVO.class)
                // 导出Excel时在此处注册handler
                .registerWriteHandler(new CustomSheetWriteHandler(list1,list2,"month"))
                .sheet(0)
                .doWrite(datas);
    }

    @GetMapping("/importExcelTemplateYear")
    @ApiOperation("年度任务导入模板")
    @PreAuthorize("@ss.hasPermission('patrol:plinfor:export')")
    @OperateLog(type = EXPORT)
    public void exportPlinforAddExcelYear( HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(" 年度任务导入模板", "UTF-8").replaceAll("\\+", "%20");
        // 设置文件名称
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<InforImportYearVO> datas = new ArrayList<>();
        InforImportYearVO inforImportYearVO = new InforImportYearVO();
        AdminUserRespDTO userRespDTO =  adminUserApi.getUser(getLoginUserId()).getData();
        DeptRespDTO dept = deptApi.getDept(userRespDTO.getDeptId()).getCheckedData();
        inforImportYearVO.setSusername(userRespDTO.getNickname());
        inforImportYearVO.setSdepname(dept.getName());
        inforImportYearVO.setMobile(userRespDTO.getMobile());
        datas.add(inforImportYearVO);
        //机构下的所有部门
        CommonResult<List<DeptRespDTO>> allDepts = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId());
        List<DeptRespDTO> data1 = allDepts.getData();
        List<String> list1=data1.stream().map(DeptRespDTO::getName).collect(Collectors.toList());


        List<String> dictTypes =new ArrayList<>();
        //任务类型
        dictTypes.add("oa-work-new-taskType");
        List<DictDataRespDTO> data = dictDataApi.getByDictTypes(dictTypes).getData();
        List<String> list2=data.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());
//        dictTypes.clear();
        //紧急程度
//        dictTypes.add("oa-work-new-urgencyLevel");
//        data=dictDataApi.getByDictTypes(dictTypes).getData();
//        List<String> list3=data.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream(), InforImportYearVO.class)
                // 导出Excel时在此处注册handler
                .registerWriteHandler(new CustomSheetWriteHandler(list1,list2,"year"))
                .sheet(0)
                .doWrite(datas);

    }

    @GetMapping("/importExcelTemplateTemporary")
    @ApiOperation("临时任务导入模板")
    @PreAuthorize("@ss.hasPermission('patrol:plinfor:export')")
    @OperateLog(type = EXPORT)
    public void exportPlinforAddExcelTemporary( HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("临时任务导入模板", "UTF-8").replaceAll("\\+", "%20");
        // 设置文件名称
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<InforImportTemporaryVO> datas = new ArrayList<>();
        InforImportTemporaryVO inforImportTemporaryVO = new InforImportTemporaryVO();
        AdminUserRespDTO userRespDTO =  adminUserApi.getUser(getLoginUserId()).getData();
        DeptRespDTO dept = deptApi.getDept(userRespDTO.getDeptId()).getCheckedData();
        inforImportTemporaryVO.setSusername(userRespDTO.getNickname());
        inforImportTemporaryVO.setSdepname(dept.getName());
        inforImportTemporaryVO.setMobile(userRespDTO.getMobile());
        datas.add(inforImportTemporaryVO);
        //机构下的所有部门
        CommonResult<List<DeptRespDTO>> allDepts = deptApi.getAllDepts(SecurityFrameworkUtils.getTenantId());
        List<DeptRespDTO> data1 = allDepts.getData();
        List<String> list1=data1.stream().map(DeptRespDTO::getName).collect(Collectors.toList());
        List<String> dictTypes =new ArrayList<>();
        //任务类型
        dictTypes.add("oa-work-new-taskType");
        List<DictDataRespDTO> data = dictDataApi.getByDictTypes(dictTypes).getData();
        List<String> list2=data.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());
        dictTypes.clear();
        //紧急程度
        dictTypes.add("oa-work-new-urgencyLevel");
        data=dictDataApi.getByDictTypes(dictTypes).getData();
        List<String> list3=data.stream().map(DictDataRespDTO::getLabel).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream(), InforImportTemporaryVO.class)
                    // 导出Excel时在此处注册handler
                    .registerWriteHandler(new CustomSheetWriteHandler(list1,list2,list3,"temporary"))
                    .sheet(0)
                    .doWrite(datas);


    }

    @SneakyThrows
    @Validated
    @PostMapping("/importJudge")
    @ApiOperation("中心工作导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('system:user:importJudge')")
    @OperateLog(type = IMPORT)
    public ImportOAResult<List<InforCreateReqVO>> importExcel(@RequestParam("file") MultipartFile file,@RequestParam("yeartag")Integer yeartag,@RequestParam("isDraft")Integer isDraft) throws Exception {
//        List<InforImportMonthVO> list = ExcelUtils.read(file, InforImportMonthVO.class);
//        List<InforCreateReqVO> inforCreateReqVOS = InforConvert.INSTANCE.convertListMonthToCreate(list);
//        ExcelValidator.valid(list,1);
//        return success(null);


        if (file == null) return ImportOAResult.error(610, "未传入文件，导入失败");
        ArrayList<InforImportVO> list = new ArrayList<>();
        ImportOAResult<List<InforCreateReqVO>> listCommonResult=new ImportOAResult<>();
        listCommonResult.setCode(0);
        AnalysisEventListener listener = new AnalysisEventListener() {
            @Override
            public void invoke(Object data, AnalysisContext context) {
                //获取到每一行数据，逐行进行处理
                InforImportVO inforImportVO=(InforImportVO)data;
                list.add(inforImportVO);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("导入数据完毕");
            }
        };
        try {
            EasyExcel.read(file.getInputStream(),InforImportVO.class, listener).sheet(0).doRead();
        } catch (IOException e) {
            log.error("导入出错：{}", e.getMessage());
        }

        for (int i = 0; i < list.size(); i++) {
            if(!list.get(i).getSusername().equals(adminUserApi.getUser(getLoginUserId()).getData().getNickname())){
                listCommonResult.setMsg2("发起人信息与当前登录用户不匹配，导入失败");
                return listCommonResult;
            }

        }
        //有重复名字的集合
        List<String> repeatNameList = operationMapper.selectRepeatNameList(getTenantId());

        List<String> dictTypes =new ArrayList<>();
        String format = "yyyy-MM-dd HH:mm:ss"; // 指定的时间格式
        //任务类型
        dictTypes.add("oa-work-new-taskType");
        List<DictDataRespDTO> taskTypeList = dictDataApi.getByDictTypes(dictTypes).getData();
        dictTypes.clear();
        //紧急程度
        dictTypes.add("oa-work-new-urgencyLevel");
        List<DictDataRespDTO> degreeList=dictDataApi.getByDictTypes(dictTypes).getData();



        for (InforImportVO inforImportVO : list) {
            for (DictDataRespDTO datum : taskTypeList) {
                if(inforImportVO.getTasktype()!=null&&inforImportVO.getTasktype().equals(datum.getLabel())){
                    inforImportVO.setTasktype(datum.getValue());
                }else if(inforImportVO.getTasktype()==null){
                    listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因任务类型未选择下拉框里的数据，导入失败");
                    return listCommonResult;
                }
            }

            if(yeartag==0){
                for (DictDataRespDTO dictDataRespDTO : degreeList) {
                    if(inforImportVO.getDegree()!=null&&inforImportVO.getDegree().equals(dictDataRespDTO.getLabel())){
                        inforImportVO.setDegree(dictDataRespDTO.getValue());
                    }else if(inforImportVO.getDegree()==null){
                        listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因紧急程度未选择下拉框里的数据，导入失败");
                        return listCommonResult;
                    }
                }
            }


            if(inforImportVO.getStarttime()==null){
                listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因未填写开始时间，导入失败");
                return listCommonResult;
            }else {
                inforImportVO.setStarttime(inforImportVO.getStarttime()+":00");
                try {
                    DateUtils.parseDateStrictly(inforImportVO.getStarttime(), format);
                } catch (ParseException e) {
                    listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因开始时间格式异常，导入失败");
                    return listCommonResult;
                }
            }

            if(inforImportVO.getEndtime()==null){
                listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因未填写截止时间，导入失败");
                return listCommonResult;
            }else {
                inforImportVO.setEndtime(inforImportVO.getEndtime()+":00");
                try {
                    DateUtils.parseDateStrictly(inforImportVO.getEndtime(), format);
                } catch (ParseException e) {
                    listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因截止时间格式异常，导入失败");
                    return listCommonResult;
                }
            }

            if(inforImportVO.getTasktype()==null){
                listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因任务类型未选择下拉框里的数据，导入失败");
                return listCommonResult;
            }else {
                try {
                    int num = Integer.parseInt(inforImportVO.getTasktype());
                } catch (NumberFormatException e) {
                    listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因任务类型未选择下拉框里的数据，导入失败");
                    return listCommonResult;
                }
            }

            if(yeartag==0){
                if(inforImportVO.getDegree()==null){
                    listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因紧急程度未选择下拉框里的数据，导入失败");
                    return listCommonResult;
                }else {
                    try {
                        int num = Integer.parseInt(inforImportVO.getDegree());
                    } catch (NumberFormatException e) {
                        listCommonResult.setMsg2(inforImportVO.getTaskcontent()+"任务因紧急程度未选择下拉框里的数据，导入失败");
                        return listCommonResult;
                    }
                }
            }

            }



        List<InforCreateReqVO> inforCreateReqVOS = InforConvert.INSTANCE.convertListMonthToCreate(list);
        for (int i = 0; i < list.size(); i++) {
            List<UserinforDO> userinforDOList =new ArrayList<>();
            if(list.get(i).getTaskcontent()==null||list.get(i).getTaskcontent()==""){
                listCommonResult.setMsg2("有重点工作事项为空的数据，导入失败");
                return listCommonResult;
            }
            //utype=0
            if(list.get(i).getMdepname()!=null){
                CommonResult<DeptRespDTO> deptByTenantAndName = deptApi.getDeptByTenantAndName(getTenantId(), list.get(i).getMdepname());
                if(deptByTenantAndName.getData()==null){
                    listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因填写牵头部门不属于下拉框的内容，导入失败");
                    return listCommonResult;
                }else {
                    UserinforDO userinforDO=new UserinforDO();
                    userinforDO.setUtype(0);
                    userinforDO.setUname(null);
                    userinforDO.setUid(null);
                    userinforDO.setDptid(deptByTenantAndName.getData().getId().intValue());
                    userinforDO.setDptname(deptByTenantAndName.getData().getName());
                    userinforDO.setTreecode(deptByTenantAndName.getData().getId().intValue());
                    userinforDO.setTenantId(getTenantId());
                    userinforDOList.add(userinforDO);
                }

            }else {
                listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因未填写牵头部门，导入失败");
                return listCommonResult;
            }
            //utype=1
            if(yeartag==2){
                if(list.get(i).getMusername()!=null){
                    CommonResult<DeptRespDTO> deptByTenantAndName = deptApi.getDeptByTenantAndName(getTenantId(), list.get(i).getMdepname());
                    Long deptId=deptByTenantAndName.getData().getId();
                    List<String> nicknames = adminUserApi.getUsersByDeptId(deptId);

                    List<String> musernameList = Arrays.asList(list.get(i).getMusername().split("[,，]"));
                    for (String musername : musernameList) {
                        if(repeatNameList.contains(musername)){
                            listCommonResult.setMsg1("当前提交的任务"+list.get(i).getTaskcontent()+"中存在名称重复,请存草稿检查后提交");
                            continue;
                        }
                        if(!nicknames.contains(musername)){
                            listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因重点工作事项承办人"+musername+"不在部门"+list.get(i).getMdepname()+"下，导入失败");
                            return listCommonResult;
                        }

                        CommonResult<AdminUserRespDTO> user = adminUserApi.getUserByNickname(musername);
                        if(user.getData()!=null){
                            CommonResult<DeptRespDTO> dept = deptApi.getDept(user.getData().getDeptId());
                            UserinforDO userinforDO=new UserinforDO();
                            userinforDO.setUtype(1);
                            userinforDO.setUname(user.getData().getNickname());
                            userinforDO.setUid(user.getData().getId().intValue());
                            userinforDO.setDptid(dept.getData().getId().intValue());
                            userinforDO.setDptname(dept.getData().getName());
                            userinforDO.setTenantId(getTenantId());
                            userinforDOList.add(userinforDO);
                        }else {
                            listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因查询不到"+musername+"导入失败");
                        }

                    }
                }else {
                    listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因未填写重点工作事项承办人，导入失败");
                    return listCommonResult;
                }
            }
            //utype=1 临时因excel表头单独处理
            if(yeartag<2){
                CommonResult<DeptRespDTO> deptByTenantAndName = deptApi.getDeptByTenantAndName(getTenantId(), list.get(i).getMdepname());
                Long deptId=deptByTenantAndName.getData().getId();
                List<String> nicknames = adminUserApi.getUsersByDeptId(deptId);

                if(list.get(i).getMusername1()!=null){
                    List<String> musernameList = Arrays.asList(list.get(i).getMusername1().split("[,，]"));
                    for (String musername : musernameList) {
                        if(repeatNameList.contains(musername)){
                            listCommonResult.setMsg1("当前提交的任务"+list.get(i).getTaskcontent()+"中存在名称重复,请存草稿检查后提交");
                            continue;
                        }
                        if(!nicknames.contains(musername)){
                            listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因牵头部门办理人"+musername+"不在部门"+list.get(i).getMdepname()+"下，导入失败");
                            return listCommonResult;
                        }

                        CommonResult<AdminUserRespDTO> user = adminUserApi.getUserByNickname(musername);
                        if(user.getData()!=null){
                            CommonResult<DeptRespDTO> dept = deptApi.getDept(user.getData().getDeptId());
                            UserinforDO userinforDO=new UserinforDO();
                            userinforDO.setUtype(1);
                            userinforDO.setUname(user.getData().getNickname());
                            userinforDO.setUid(user.getData().getId().intValue());
                            userinforDO.setDptid(dept.getData().getId().intValue());
                            userinforDO.setDptname(dept.getData().getName());
                            userinforDO.setTenantId(getTenantId());
                            userinforDOList.add(userinforDO);
                        }else {
                            listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因查询不到"+musername+"导入失败");
                        }

                    }
                }else {
                    listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因未填写牵头部门办理人，导入失败");
                    return listCommonResult;
                }
            }
            //utype=2
            //临时，年度默认的督办人是自己
            //校验临时，年度的督办人是否有自己
            if(yeartag<=1&&(list.get(i).getSupervisor()==null||!list.get(i).getSupervisor().contains(adminUserApi.getUser(getLoginUserId()).getData().getNickname()))){
                if(list.get(i).getSupervisor()==null){
                    list.get(i).setSupervisor(adminUserApi.getUser(getLoginUserId()).getData().getNickname());
                }else {
                    list.get(i).setSupervisor(list.get(i).getSupervisor()+","+adminUserApi.getUser(getLoginUserId()).getData().getNickname());
                }
            }
            if(list.get(i).getSupervisor()!=null){
                List<String> supervisorList = Arrays.asList(list.get(i).getSupervisor().split("[,，]"));

                for (String supervisor : supervisorList) {
                    if(repeatNameList.contains(supervisor)){
                        listCommonResult.setMsg1("当前提交的任务"+list.get(i).getTaskcontent()+"中存在名称重复,请存草稿检查后提交");
                        continue;
                    }
                    CommonResult<AdminUserRespDTO> user = adminUserApi.getUserByNickname(supervisor);
                    if(user.getData()!=null){
                        CommonResult<DeptRespDTO> dept = deptApi.getDept(user.getData().getDeptId());
                        UserinforDO userinforDO=new UserinforDO();
                        userinforDO.setUtype(2);
                        userinforDO.setUname(user.getData().getNickname());
                        userinforDO.setUid(user.getData().getId().intValue());
                        userinforDO.setDptid(dept.getData().getId().intValue());
                        userinforDO.setDptname(dept.getData().getName());
                        userinforDO.setTenantId(getTenantId());
                        userinforDOList.add(userinforDO);
                    }else {
                        listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因查询不到"+supervisor+"导入失败");
                    }

                }
            }
            //utype=3
            if(list.get(i).getSuperdpt()!=null){
                List<String> superdptList = Arrays.asList(list.get(i).getSuperdpt().split("[,，]"));

                for (String superdpt : superdptList) {
                    CommonResult<DeptRespDTO> deptByTenantAndName = deptApi.getDeptByTenantAndName(getTenantId(), superdpt);
                    if(deptByTenantAndName.getData()==null){
                        listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因填写责任部门不属于下拉框的内容，导入失败");
                        return listCommonResult;
                    }else {
                        UserinforDO userinforDO=new UserinforDO();
                        userinforDO.setUtype(3);
                        userinforDO.setDptid(deptByTenantAndName.getData().getId().intValue());
                        userinforDO.setDptname(deptByTenantAndName.getData().getName());
                        userinforDO.setTenantId(getTenantId());
                        userinforDOList.add(userinforDO);
                    }
                }


            }
            //utype=4
            if(list.get(i).getMleader()!=null){

                CommonResult<AdminUserRespDTO> user = adminUserApi.getUserByNickname(list.get(i).getMleader());
                CommonResult<DeptRespDTO> dept = deptApi.getDept(user.getData().getDeptId());
                if(user.getData()!=null){
                    UserinforDO userinforDO=new UserinforDO();
                    userinforDO.setUtype(4);
                    userinforDO.setTenantId(getTenantId());
                    userinforDO.setUname(user.getData().getNickname());
                    userinforDO.setUid(user.getData().getId().intValue());
                    userinforDO.setDptid(dept.getData().getId().intValue());
                    userinforDO.setDptname(dept.getData().getName());
                    userinforDO.setTenantId(getTenantId());
                    userinforDOList.add(userinforDO);
                }else {
                    listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因查询不到"+list.get(i).getMleader()+"导入失败");
                }

            }else {
                listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因查询不到"+list.get(i).getMleader()+"导入失败");
                return listCommonResult;
            }
            //utype=5
            if(list.get(i).getSleader()!=null){
                List<String> sleaderList = Arrays.asList(list.get(i).getSleader().split("[,，]"));
                for (String sleader : sleaderList) {
                    if(repeatNameList.contains(sleader)){
                        listCommonResult.setMsg1("当前提交的任务"+list.get(i).getTaskcontent()+"中存在名称重复,请存草稿检查后提交");
                        continue;
                    }
                    CommonResult<AdminUserRespDTO> user = adminUserApi.getUserByNickname(sleader);
                    if(user.getData()!=null){
                        CommonResult<DeptRespDTO> dept = deptApi.getDept(user.getData().getDeptId());
                        UserinforDO userinforDO=new UserinforDO();
                        userinforDO.setUtype(5);
                        userinforDO.setUname(user.getData().getNickname());
                        userinforDO.setUid(user.getData().getId().intValue());
                        userinforDO.setDptid(dept.getData().getId().intValue());
                        userinforDO.setDptname(dept.getData().getName());
                        userinforDO.setTenantId(getTenantId());
                        userinforDOList.add(userinforDO);
                    }else {
                        listCommonResult.setMsg2(list.get(i).getTaskcontent()+"任务因查询不到"+sleader+"导入失败");
                    }
                }
            }
            AdminUserRespDTO suser = adminUserApi.getUser(getLoginUserId()).getData();
            DeptRespDTO sdept = deptApi.getDept(suser.getDeptId()).getData();

            inforCreateReqVOS.get(i).setUserinforDOList(userinforDOList);
            inforCreateReqVOS.get(i).setYeartag(yeartag);
            inforCreateReqVOS.get(i).setSuserid(getLoginUserId().intValue());
            inforCreateReqVOS.get(i).setSdeptid(sdept.getId().intValue());
            if(yeartag==2){
                if(isDraft==0){
                    inforCreateReqVOS.get(i).setStatus(4);
                }
                if(isDraft==1||listCommonResult.getMsg1()!=null){
                    inforCreateReqVOS.get(i).setStatus(1);
                }
            }
            if(yeartag<=1){
                if(isDraft==0){
                    inforCreateReqVOS.get(i).setStatus(20);
                }
                if(isDraft==1||listCommonResult.getMsg1()!=null){
                    inforCreateReqVOS.get(i).setStatus(10);
                }
            }
        }
        listCommonResult.setData(inforCreateReqVOS);
        listCommonResult.setCode(0);
//        for (InforCreateReqVO inforCreateReqVO : inforCreateReqVOS) {
//            createInfor(inforCreateReqVO);
//        }

        return listCommonResult;
    }


    @PostMapping("/import")
    @ApiOperation("批量导入重点工作信息")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @PreAuthorize("@ss.hasPermission('yeartask:infor:import')")
    public CommonResult<UpdateAll> createInfor(@Valid @RequestBody List<InforCreateReqVO> inforCreateReqVOS) throws ParseException {

        int success = 0;
        int fail =0;
        int size=inforCreateReqVOS.size();
        for (InforCreateReqVO inforCreateReqVO : inforCreateReqVOS) {
            CommonResult<Integer> infor = createInfor(inforCreateReqVO);
            if(infor.getData()!=null){
                success++;
            }
        }
        fail=size-success;

        return success(UpdateAll.builder().success(success).fail(fail).build());
    }

    @PostMapping("/setEndtime11")
    @ApiOperation("异步测试DubboService")
    public CommonResult<List<String>> setEndtimeaaa() {

//        deptApi.asynTest("0");
//        deptApi.syncTest("0");
        List<String> repeatNameList = operationMapper.selectRepeatNameList(getTenantId());

        return success(repeatNameList);
    }


    @PostMapping("/timeTest")
    @ApiOperation("时间测试DubboService  LocalDateTime秒数被去掉了  data 相差 10多个小时")
    public CommonResult<Boolean> timeTest() {

        LocalDateTime time  = LocalDateTime.now() ;
        ZoneId zoneId = ZoneId.systemDefault();

        log.info("时间测试DubboService  begin {}  " , DateUtil.formatDateTime( Date.from(time.atZone(zoneId).toInstant()) ));

        deptApi.timeTest(time);

        return success(true);
    }


    @PostMapping("/testcreate")
    @ApiOperation("异步测试DubboService")
    @PermitAll
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 5000)
    @TenantIgnore
    public CommonResult<Boolean> setEndtimeaaa(@Valid @RequestBody   String code ) {

        return success(true);
    }

    @PostMapping("/testpermision")
    @ApiOperation("permision测试")
    @PermitAll
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 5000)
    @TenantIgnore
    public CommonResult<Boolean> testpermision(@Valid @RequestBody   String code ) {

        deptApi.sendMessageRefresh(code);
        return success(true);
    }

    @PostMapping("/repeatTest")
//    @Idempotent(timeout = 10, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    public CommonResult<Boolean> repeatTest(@RequestBody String code) {
        return success(true);
    }


    @GetMapping("/judgeRole")
    @ApiOperation("判断角色")
    public CommonResult<InforRoleVO> judgeRole() {

        InforRoleVO inforRoleVO = inforService.judgeRole();

        return success(inforRoleVO);
    }

    @GetMapping("/getAllTotal")
    @ApiOperation("获取徽标数")
    public CommonResult<InforTotalVO> getAllTotal() {
        InforTotalVO inforTotalVO=new InforTotalVO();

        //月度

        //提交
        InforPageReqVO inforPageReqVO1=new InforPageReqVO();
        inforPageReqVO1.setPageNo(1);
        inforPageReqVO1.setPageSize(100000);
        inforPageReqVO1.setYeartag(2);
        inforPageReqVO1.setStatuslecode(30);
        inforPageReqVO1.setSuserid(getLoginUserId().intValue());
        List<InforRespVO> list = getInforPage(inforPageReqVO1).getData().getList();
        List<InforRespVO> submit = list.stream().filter(inforRespVO -> inforRespVO.getStatus() != 1).collect(Collectors.toList());
        inforTotalVO.setSubmitTotal((long)submit.size());
        //填报审批
        InforPageReqVO inforPageReqVO2=new InforPageReqVO();
        inforPageReqVO2.setStatus(4);
        inforPageReqVO2.setYeartag(2);
        inforPageReqVO2.setOrder(1);
        inforPageReqVO2.setMuseridLeader(getLoginUserId().intValue());
        inforPageReqVO2.setHhandle(0);
        if(judgeRole().getData().getReportOfficer()||judgeRole().getData().getIsDirector()){
            inforPageReqVO2.setMuseridLeader(getLoginUserId().intValue());
            inforPageReqVO2.setHhandle(0);
        }
        if(judgeRole().getData().getIsAdmin()||judgeRole().getData().getIsHeadmaster()){
            inforPageReqVO2.setMuseridLeader(null);
            inforPageReqVO2.setSuseridLeader(getLoginUserId().intValue());
            if(judgeRole().getData().getIsAdmin()){
                inforPageReqVO2.setHhandle(1);
            }
            if(judgeRole().getData().getIsHeadmaster()){
                inforPageReqVO2.setHhandle(3);
            }
        }
        Long handleTotal = getInforPage(inforPageReqVO2).getData().getTotal();
        inforTotalVO.setHandleTotal(handleTotal);
        //办理情况审批
        InforPageReqVO inforPageReqVO3=new InforPageReqVO();
        inforPageReqVO3.setPageNo(1);
        inforPageReqVO3.setPageSize(10);
        inforPageReqVO3.setYeartag(2);
        inforPageReqVO3.setStatus(30);
        inforPageReqVO3.setOrder(1);
        inforPageReqVO3.setMuseridLeader(getLoginUserId().intValue());
        inforPageReqVO3.setShandle(1);
        if(judgeRole().getData().getReportOfficer()||judgeRole().getData().getIsDirector()){
            inforPageReqVO3.setMuseridLeader(getLoginUserId().intValue());
//            if(judgeRole().getData().getReportOfficer()){
//                inforPageReqVO3.setShandle(1);
//            }
//            if(judgeRole().getData().getIsDirector()){
//                inforPageReqVO3.setShandle(1);
//            }
            inforPageReqVO3.setShandle(1);
        }
        if(judgeRole().getData().getIsAdmin()||judgeRole().getData().getIsHeadmaster()){
            inforPageReqVO3.setMuseridLeader(null);
            inforPageReqVO3.setSuseridLeader(getLoginUserId().intValue());
            if(judgeRole().getData().getIsAdmin()){
                inforPageReqVO3.setShandle(3);
            }
            if(judgeRole().getData().getIsHeadmaster()){
                inforPageReqVO3.setShandle(4);
            }
        }
        Long shandleTotle = getInforPage(inforPageReqVO3).getData().getTotal();
        inforTotalVO.setShandleTotal(shandleTotle);
        //进行中
        InforPageReqVO inforPageReqVO4=new InforPageReqVO();
        inforPageReqVO4.setPageNo(1);
        inforPageReqVO4.setPageSize(10);
        inforPageReqVO4.setYeartag(2);
        inforPageReqVO4.setOrder(1);
        List<Integer> statusList=new ArrayList<>();
        statusList.add(4);
        statusList.add(30);
        inforPageReqVO4.setStatusList(statusList);
        inforPageReqVO4.setSuserid(getLoginUserId().intValue());
        if(judgeRole().getData().getReportOfficer()){
            inforPageReqVO4.setSuserid(getLoginUserId().intValue());
        }
        if(judgeRole().getData().getIsAdmin()){
            inforPageReqVO4.setSuserid(null);
            inforPageReqVO4.setSuseridLeader(getLoginUserId().intValue());
        }
        Long progressTotal = getInforPage(inforPageReqVO4).getData().getTotal();
        inforTotalVO.setProgressTotal(progressTotal);
        //我督办的
        InforPageReqVO inforPageReqVO5=new InforPageReqVO();
        inforPageReqVO5.setPageNo(1);
        inforPageReqVO5.setPageSize(100000);
        inforPageReqVO5.setYeartag(2);
        inforPageReqVO5.setOrder(1);
        inforPageReqVO5.setSuperuserid(getLoginUserId().intValue());
        List<InforRespVO> inforPageReqVOList5 = getInforPage(inforPageReqVO5).getData().getList().stream().filter(i -> i.getStatus() != 50).collect(Collectors.toList());
        Long superviseMonthTotal = (long)inforPageReqVOList5.size();
        inforTotalVO.setSuperviseMonthTotal(superviseMonthTotal);

        inforTotalVO.setMonthTotal(inforTotalVO.getSubmitTotal()+inforTotalVO.getHandleTotal()+inforTotalVO.getShandleTotal()+inforTotalVO.getProgressTotal()+inforTotalVO.getSuperviseMonthTotal());

        //临时
        //待签收
        InforPageReqVO inforPageReqVO6=new InforPageReqVO();
        inforPageReqVO6.setPageNo(1);
        inforPageReqVO6.setPageSize(10);
        inforPageReqVO6.setYeartag(0);
        inforPageReqVO6.setStatus(20);
        inforPageReqVO6.setMuserid(getLoginUserId().intValue());
        Long signTemporaryTotal = getInforPage(inforPageReqVO6).getData().getTotal();
        inforTotalVO.setSignTemporaryTotal(signTemporaryTotal);
        //待办理
        InforPageReqVO inforPageReqVO7=new InforPageReqVO();
        inforPageReqVO7.setPageNo(1);
        inforPageReqVO7.setPageSize(10);
        inforPageReqVO7.setYeartag(0);
        inforPageReqVO7.setStatus(30);
        inforPageReqVO7.setMuserid(getLoginUserId().intValue());
        Long handleTemporaryTotal = getInforPage(inforPageReqVO7).getData().getTotal();
        inforTotalVO.setHandleTemporaryTotal(handleTemporaryTotal);
        //我发起的
        InforPageReqVO inforPageReqVO8=new InforPageReqVO();
        inforPageReqVO8.setPageNo(1);
        inforPageReqVO8.setPageSize(10);
        inforPageReqVO8.setYeartag(0);
        inforPageReqVO8.setSuserid(getLoginUserId().intValue());
        Long myTemporaryTotal = getInforPage(inforPageReqVO8).getData().getTotal();
        inforTotalVO.setMyTemporaryTotal(myTemporaryTotal);
        //我督办的
        InforPageReqVO inforPageReqVO9=new InforPageReqVO();
        inforPageReqVO9.setPageNo(1);
        inforPageReqVO9.setPageSize(100000);
        inforPageReqVO9.setYeartag(0);
        inforPageReqVO9.setSuperuserid(getLoginUserId().intValue());
        List<InforRespVO> inforPageReqVOList9 = getInforPage(inforPageReqVO9).getData().getList();
        Long superviseTemporaryTotal = (long)inforPageReqVOList9.stream().filter(inforRespVO -> inforRespVO.getStatus() != 50).collect(Collectors.toList()).size();
        inforTotalVO.setSuperviseTemporaryTotal(superviseTemporaryTotal);
        inforTotalVO.setTemporaryTotal(inforTotalVO.getSignTemporaryTotal()+inforTotalVO.getHandleTemporaryTotal()+inforTotalVO.getMyTemporaryTotal()+inforTotalVO.getSuperviseTemporaryTotal());

        //年度
        //待签收
        InforPageReqVO inforPageReqVO10=new InforPageReqVO();
        inforPageReqVO10.setPageNo(1);
        inforPageReqVO10.setPageSize(10);
        inforPageReqVO10.setYeartag(1);
        inforPageReqVO10.setStatus(20);
        inforPageReqVO10.setMuserid(getLoginUserId().intValue());
        Long signYearTotal = getInforPage(inforPageReqVO10).getData().getTotal();
        inforTotalVO.setSignYearTotal(signYearTotal);
        //待办理
        InforPageReqVO inforPageReqVO11=new InforPageReqVO();
        inforPageReqVO11.setPageNo(1);
        inforPageReqVO11.setPageSize(10);
        inforPageReqVO11.setYeartag(1);
        inforPageReqVO11.setStatus(30);
        inforPageReqVO11.setMuserid(getLoginUserId().intValue());
        Long handleYearTotal = getInforPage(inforPageReqVO11).getData().getTotal();
        inforTotalVO.setHandleYearTotal(handleYearTotal);
        //我发起的
        InforPageReqVO inforPageReqVO12=new InforPageReqVO();
        inforPageReqVO12.setPageNo(1);
        inforPageReqVO12.setPageSize(10);
        inforPageReqVO12.setYeartag(1);
        inforPageReqVO12.setSuserid(getLoginUserId().intValue());
        Long myYearTotal = getInforPage(inforPageReqVO12).getData().getTotal();
        inforTotalVO.setMyYearTotal(myYearTotal);
        //我督办的
        InforPageReqVO inforPageReqVO13=new InforPageReqVO();
        inforPageReqVO13.setPageNo(1);
        inforPageReqVO13.setPageSize(100000);
        inforPageReqVO13.setYeartag(1);
        inforPageReqVO13.setSuperuserid(getLoginUserId().intValue());
        List<InforRespVO> inforPageReqVOList13 = getInforPage(inforPageReqVO13).getData().getList();
        Long superviseYearTotal = (long)inforPageReqVOList13.stream().filter(inforRespVO -> inforRespVO.getStatus() != 50).collect(Collectors.toList()).size();
        inforTotalVO.setSuperviseYearTotal(superviseYearTotal);
        inforTotalVO.setYearTotal(inforTotalVO.getSignYearTotal()+inforTotalVO.getHandleYearTotal()+inforTotalVO.getMyYearTotal()+inforTotalVO.getSuperviseYearTotal());

        //小程序我发起的
        InforPageReqVO inforPageReqVO14=new InforPageReqVO();
        inforPageReqVO14.setPageNo(1);
        inforPageReqVO14.setPageSize(10);
        inforPageReqVO14.setNeeddraft(0);
        inforPageReqVO14.setOrder(1);
        inforPageReqVO14.setIsXcx(true);
        inforPageReqVO14.setSuserid(getLoginUserId().intValue());
        Long myXcxTotal=getInforPage(inforPageReqVO14).getData().getTotal();
        inforTotalVO.setMyXcxTotal(myXcxTotal);

        return success(inforTotalVO);
    }

    @GetMapping("/judgeUserIsDirectorByDeptid")
    @ApiOperation("根据传入的部门id判断登录的用户是否为该部门的处长")
    public boolean judgeUserIsDirectorByDeptid(@RequestParam("deptid") Long deptid){
        CommonResult<DeptRespDTO> dept = deptApi.getDept(deptid);
        if(dept.getData().getLeaderUserId()==null){
            return false;
        }else {
            return dept.getData().getLeaderUserId().equals(getLoginUserId());
        }

    }




    //30分钟执行一次
    @Scheduled(cron = "0 0 0 1 * ?")
    public void refreshDatabase() throws Exception {
        // 定时任务开始
        inforScheduledTasks();
    }


    @Klock(
            lockType = LockType.Reentrant, // 使用可重入锁
            waitTime = 10, // 获取锁的等待时间
            leaseTime = 120, // 锁的持续时间，单位是秒
            lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST, // 获取锁失败时的策略
            releaseTimeoutStrategy = ReleaseTimeoutStrategy.FAIL_FAST // 释放锁失败时的策略
    )
     public void inforScheduledTasks() throws Exception{
        //        LambdaQueryWrapper<InforDO> lambdaQueryWrapper=new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(InforDO::getYeartag,2);
//        lambdaQueryWrapper.eq(InforDO::getStatus,30);
//        lambdaQueryWrapper.in(InforDO::getId,idList);
        List<InforDO> inforDOS = inforMapper.selectMonthInfor();

        if(inforDOS!=null&&inforDOS.size()>0){
            for (InforDO inforDO : inforDOS) {
                if (inforDO.getIsHandle()==1){
                    if(inforDO.getHandleCount()<=1){
                        inforDO.setHandleCount(inforDO.getHandleCount()+1);
                    }
                }
                inforDO.setIsHandle(0);
            }
            inforMapper.updateBatch(inforDOS);
        }

     }


    @GetMapping("/getRoleByInforId")
    @ApiOperation("根据传入的部门id判断登录的用户是否为该部门的处长")
    public CommonResult<roleIdVO> judgeUserIsDirectorByDeptid(@RequestParam("inforid") Integer inforid){
        roleIdVO roleIdVO=new roleIdVO();
        InforDO inforDO = inforMapper.selectInforDoById(inforid);
        CommonResult<DeptRespDTO> dept = deptApi.getDept((long) inforDO.getSdeptid());
        //处长
        Long leaderUserId = dept.getData().getLeaderUserId();
        //管理员
        List<Long> adminList = userinforMapper.selectByTenantId(SecurityFrameworkUtils.getTenantId());
        //校长
        List<Long> headmasterList = userinforMapper.selectPrincipalByTenantId(getTenantId());
        roleIdVO.setLeaderUserId(leaderUserId);
        roleIdVO.setAdminList(adminList);
        roleIdVO.setHeadmasterList(headmasterList);

        return success(roleIdVO);
    }

    @PostMapping("/test1")
//    @Idempotent(timeout = 10, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    public CommonResult<Boolean> test1() throws Exception {
        inforScheduledTasks();

        return success(true);
    }

}
