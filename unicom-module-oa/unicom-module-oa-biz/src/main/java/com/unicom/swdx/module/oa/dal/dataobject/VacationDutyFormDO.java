package com.unicom.swdx.module.oa.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 寒暑假坐值班表单
 * <AUTHOR>
 */
@TableName(value = "oa_vacation_duty_form",autoResultMap = true)
@Data
@KeySequence("oa_vacation_duty_form_id_seq")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VacationDutyFormDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 对应的寒暑假值班id
     */
    private Long vacationDutyId;

    /**
     * 对应的寒暑假值班的processInstanceId
     */
    @TableField(exist = false)
    private String processInstanceId;

    /**
     * 坐值班人员id
     */
    private String personnelId;

    /**
     * 处室id
     */
    private String deptId;

    /**
     * 处室名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 坐值班人员名称
     */
    private String personnel;

    /**
     * 是否通知
     */
    private boolean notify;

    /**
     * 通知时间
     */
    private String notifyTime;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 办公电话
     */
    private String telephone;

    /**
     * 坐值班类型 1坐班 2值班
     */
    private String dutyType;
}
