package com.unicom.swdx.module.oa.convert;

import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.api.dto.OutReportDTO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportCreateReqVO;
import com.unicom.swdx.module.oa.controller.admin.oaOutReport.vo.OutReportRespVO;
import com.unicom.swdx.module.oa.dal.dataobject.OALeaveDO;
import com.unicom.swdx.module.oa.dal.dataobject.OutReportDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OutReportConvert {

    OutReportConvert INSTANCE = Mappers.getMapper(OutReportConvert.class);

    OutReportDO convert(OutReportCreateReqVO bean);

    OutReportRespVO convert(OutReportDO bean);


    OutReportDTO convertDTO(OutReportDO bean);
}
