package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.LectureDTO;
import com.unicom.swdx.module.oa.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 外出讲学")
public interface LectureApi {

    String PREFIX = ApiConstants.PREFIX + "/lecture";

    @GetMapping(PREFIX + "/getItemId")
    LectureDTO getItemId(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/getDateById")
    Map<String, LocalDate> getDateById(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/geDeptId")
    CommonResult<Long> getDeptId(@RequestParam("processInstanceId") String processInstanceId);

}
