package com.unicom.swdx.module.oa.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PostTypeEnum {

    /**
     * 流程涉及的岗位类型
     */
    CLERK("科员及以下","swdx-clerk"),

    INVESTIGATOR("调研员","swdx-investigator"),

    VICE_DIRECTOR("副主任","swdx-vice-director"),

    DIRECTOR("主任", "swdx-director"),//部门负责人

    INSPECTOR_2("二级巡视员", "swdx-inspector-2"),

    INSPECTOR_1("一级巡视员", "swdx-inspector-1"),

    PROVINCE_LEADER("省管领导干部", "swdx-province-leader"),

    SCHOOL_LEADER("校（院）领导", "swdx-school-leader"),

    //分管日常工作的副校长（副院长）/常务副校（院）长
    VICE_CHANCELLOR("分管日常工作的副校长（副院长）", "swdx-vice-chancellor"),

    /**
     * 流程涉及的其他岗位类型
     */
    HR_CHARGE_LEAVE_DATA("人事处请假统计负责人","oa-hr-charge-leave-data"),

    HR_LEAVE_DEAL("人事处请假备案负责人","oa-hr-leave-deal"),

    OFFICE_CHARGE_LECTURE("办公室外出讲学备案负责人","oa-office-charge-lecture"),

    JWB_CHARGE_LECTURE("教务部外出讲学备案负责人","oa-jwb-charge-lecture"),

    HR_OUT_REPORT_DEAL("人事部外出报告备案负责人","oa-hr-charge-out"),

    OA_SECRETARY("秘书科", "oa-secretary"),

    OFFICE_OUT_REPORT_DEAL("办公室外出报告备案负责人","oa-office-charge-out");





    /**
     * 岗位/职级类型
     */
//    private final Integer postType;
    /**
     * 岗位/职级名称
     */
    private final String postName;
    /**
     * 岗位/职级标识
     */
    private final String postCode;

}
