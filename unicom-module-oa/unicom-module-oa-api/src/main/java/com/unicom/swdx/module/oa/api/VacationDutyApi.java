package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 寒暑假排班")
public interface VacationDutyApi {

    String PREFIX = ApiConstants.PREFIX + "/vacationDuty";

    @GetMapping(PREFIX + "/getDeptId")
    CommonResult<Long> getDeptId(@RequestParam("processInstanceId") String processInstanceId);
}
