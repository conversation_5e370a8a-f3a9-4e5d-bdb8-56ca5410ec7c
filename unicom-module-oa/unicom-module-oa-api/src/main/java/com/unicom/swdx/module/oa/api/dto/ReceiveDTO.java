package com.unicom.swdx.module.oa.api.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ReceiveDTO {

    /**
     * 流程发起人
     */
    private Long promoterUserId;

    /**
     * 抄送的用户id集合
     */
    private List<Long> userIds;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 事项id
     */
    private Long itemId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

}
