package com.unicom.swdx.module.oa.api;


import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.ScheduleDTO;
import com.unicom.swdx.module.oa.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 一周工作安排")
public interface ScheduleApi {
    String PREFIX = ApiConstants.PREFIX + "/weeklyWorkSchedule";

    @GetMapping(PREFIX + "/getItemId")
    ScheduleDTO getItemId(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/updateStatusById")
    void updateStatusById(@RequestParam("id") Long id, @RequestParam("status") Integer status);

    @GetMapping(PREFIX + "/getCopyTo")
    String getCopyTo(@RequestParam("ScheduleId") Long scheduleId);

    @GetMapping(PREFIX + "/getProcessInstanceId")
    String getProcessInstanceId(@RequestParam("ScheduleId") Long scheduleId);

    @GetMapping(PREFIX + "/getDeptId")
    CommonResult<Long> getDeptId(@RequestParam("processInstanceId") String processInstanceId);
}
