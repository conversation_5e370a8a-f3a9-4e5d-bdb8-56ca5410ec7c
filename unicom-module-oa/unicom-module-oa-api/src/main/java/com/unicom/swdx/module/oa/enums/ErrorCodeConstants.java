package com.unicom.swdx.module.oa.enums;

import com.unicom.swdx.framework.common.exception.ErrorCode;

/**
 * 小OA 错误码枚举类
 *
 * 小OA，使用 1-005-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== OA 各模块通用 1-005-000-000 ==========
    ErrorCode APPROVALS_IS_NULL = new ErrorCode(1005000000, "至少选择一个审批人");
    ErrorCode PARAMETER_ERROR = new ErrorCode(1005000001, "参数错误");
    ErrorCode APPROVAL_NOT_FOUND = new ErrorCode(1005000002, "该流程暂无审批人，请联系系统管理员进行处理");

    // ========== OA 请假模块 1-005-001-000 ==========
    ErrorCode FLOW_CONF_ERROR = new ErrorCode(1005001001, "操作流程失败");
    ErrorCode POST_ERROR = new ErrorCode(1005001002, "未查询到符合的职级和岗位，不可提交请假流程");

    ErrorCode POST_ERROR_OUT_REPORT = new ErrorCode(1005001002, "未查询到符合的职级和岗位，不可提交外出报告流程");

    ErrorCode OUT_REPORT_DEAL_TIME_AFTER_END_TIME = new ErrorCode(1005001003, "仅提前回校时，走销单流程，如需延长外出时间请再次发起外出报告审批");
    ErrorCode LEAVE_END_TIME_BEFORE_START_TIME = new ErrorCode(1005001008, "请假结束时间，需要在开始时间之后");

    ErrorCode LEAVE_DEAL_TIME_AFTER_END_TIME = new ErrorCode(1005001009, "仅提前回校时，走假期变更流程，如需延长假期请再次发起请假审批");
    ErrorCode LEAVE_NOT_EXIST = new ErrorCode(1005001004, "请假记录不存在");
    ErrorCode LEAVE_TIME_OVERLAP = new ErrorCode(1005001005, "请假时间重叠");
    ErrorCode LEAVE_DAY_GT_30 = new ErrorCode(1005001006, "请假超过30天请线下申请");
    ErrorCode LEAVE_DAY_CAN_NOT_BE_0 = new ErrorCode(1005001007, "请假天数不能为0");

    // ========== 外出讲学 模块 1005000000 ==========
    ErrorCode POST_IS_NULL = new ErrorCode(1005002000, "用户相关岗位不存在，不可提交外出讲学流程");
    ErrorCode LECTURE_NOT_EXIST = new ErrorCode(1005002001, "外出讲学记录不存在");

    ErrorCode INFOR_NOT_EXISTS = new ErrorCode(1005002002, "重点工作信息不存在");

    ErrorCode USERINFOR_NOT_EXISTS = new ErrorCode(1005002003, "重点任务关系不存在");

    ErrorCode USERINFOR_NOT_NULL= new ErrorCode(1005002004, "主办人不能为空");

    ErrorCode LECTURE_TIME_OVERLAP= new ErrorCode(1005002005, "外出讲学时间重叠");


    ErrorCode SCHEDULE_NOT_EXIST = new ErrorCode(1005003001, "工作安排上报记录不存在");

    ErrorCode SUMMARY_NOT_EXIST = new ErrorCode(1005003002, "工作安排汇总记录不存在");



    ErrorCode USER_NOT_MATCH = new ErrorCode(10006, "签字用户不匹配");


    ErrorCode SUMMARY_POST_ERROR = new ErrorCode(1005003003, "未查询到符合的职级和岗位，不可提交汇总流程");

    ErrorCode LEADER_IS_NULL = new ErrorCode(1005003004, "未查询到部门负责人，不可提交上报流程");

    // ErrorCode VACATION_DUTY_IMPORT_ERROR = new ErrorCode(1005004001, "导入人员信息有误！");

    ErrorCode VACATION_DUTY_IMPORT_LENGTH_ERROR = new ErrorCode(1005004002, "导入数据不能为空！");

    ErrorCode VACATION_DUTY_LENGTH_ERROR = new ErrorCode(1005004003, "数据不能为空！");

    ErrorCode VACATION_DUTY_IMPORT_TIME_ERROR = new ErrorCode(1005004004, "请核对导入时间格式！");

    ErrorCode VACATION_DUTY_IMPORT_NOT_NULL_ERROR = new ErrorCode(1005004005, "导入数据必填项字段不能为空！");

    ErrorCode VACATION_DUTY_IMPORT_OVERFLOW_ERROR = new ErrorCode(1005004006, "导入数据数量不能超过50条！");

    ErrorCode VACATION_DUTY_NOT_EXIST = new ErrorCode(1005004007, "寒暑假坐值班记录不存在！");

    ErrorCode VACATION_DUTY_IMPORT_TYPE_ERROR = new ErrorCode(1005004008, "导入数据类型错误！");

    ErrorCode VACATION_DUTY_IMPORT_FILE_ERROR = new ErrorCode(1005004009, "导入数据文件错误！");

    ErrorCode VACATION_DUTY_IMPORT_FILE_TYPE_ERROR = new ErrorCode(1005004010, "导入数据文件错误，请下载模板进行导入！");
}
