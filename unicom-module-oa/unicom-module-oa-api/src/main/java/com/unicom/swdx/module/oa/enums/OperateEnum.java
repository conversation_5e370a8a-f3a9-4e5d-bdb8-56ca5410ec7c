package com.unicom.swdx.module.oa.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OperateEnum {

    NO_OPERATE(0, "没有任何可操作的按钮"),
    APPROVE(1,"审批"),
    SELECT_APPROVALS(2, "选择审批人"),
    UPDATE_LEAVE_END_TIME(3, "假期变更");

    /**
     * 可操作的类型
     */
    private final Integer operateType;
    /**
     * 可展示的操作名称
     */
    private final String operateName;

}
