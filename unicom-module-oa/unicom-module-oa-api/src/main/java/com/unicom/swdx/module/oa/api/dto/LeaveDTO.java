package com.unicom.swdx.module.oa.api.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LeaveDTO {

    private Long id;

    private Long tenantId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 销假时间
     */
    private LocalDateTime dealTime;

    /**
     * 是否销假
     */
    private Boolean isDealt;

}
