package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.ReceiveDTO;
import com.unicom.swdx.module.oa.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 我收到的")
public interface ReceiveApi {

    String PREFIX = ApiConstants.PREFIX + "/receive";

    /**
     * 保存抄送信息
     */
    @PostMapping(PREFIX + "/save")
    @ApiOperation("保存抄送信息")
    void save(@RequestBody ReceiveDTO receiveDTO);

    /**
     * 请假销单
     */
    @PostMapping(PREFIX + "/save_leave")
    @ApiOperation("保存请假销单信息")
    void saveLeave(@RequestBody ReceiveDTO receiveDTO);

    /**
     * 一周工作安排
     */
    @PostMapping(PREFIX + "/save_summary")
    @ApiOperation("一周工作安排")
    void saveWorkSummary(@RequestBody ReceiveDTO receiveDTO);

    @GetMapping("/received-userid")
    @ApiOperation("获取已抄送的用户id")
    CommonResult<List<Long>> getReceivedUserIds(@RequestParam("processInstanceId") String processInstanceId);

}
