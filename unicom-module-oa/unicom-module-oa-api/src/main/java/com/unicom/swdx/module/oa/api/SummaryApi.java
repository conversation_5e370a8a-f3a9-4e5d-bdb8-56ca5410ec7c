package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.module.oa.api.dto.ScheduleDTO;
import com.unicom.swdx.module.oa.api.dto.SummaryDTO;
import com.unicom.swdx.module.oa.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 一周工作汇总")
public interface SummaryApi {
    String PREFIX = ApiConstants.PREFIX + "/weeklyWorkSummary";

    @GetMapping(PREFIX + "/getItemId")
    SummaryDTO getItemId(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/updateStatusById")
    void updateStatusById(@RequestParam("id") Long id, @RequestParam("status") Integer status);

    @GetMapping(PREFIX + "/getWorkScheduleId")
    String getWorkScheduleId(@RequestParam("id") String processInstanceId);


    @GetMapping(PREFIX + "/getYear")
    Integer getYear(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "/getSemester")
    String getSemester(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "/getWeek")
    Integer getWeek(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/getDateById")
    Map<String, LocalDate> getDateById(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/getUsersById")
    List<Long> getUsersById(@RequestParam("processInstanceId") String processInstanceId);

    @GetMapping(PREFIX + "/getCopyTo")
    String getCopyTo(@RequestParam("summaryId") Long summaryId);
}
