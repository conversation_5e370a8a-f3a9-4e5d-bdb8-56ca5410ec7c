package com.unicom.swdx.module.oa.api;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.oa.api.dto.LeaveDTO;
import com.unicom.swdx.module.oa.api.dto.OutReportDTO;
import com.unicom.swdx.module.oa.enums.ApiConstants;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 请假审批")
public interface OutReportApi {

    String PREFIX = ApiConstants.PREFIX + "/outReport";

    @GetMapping(PREFIX + "/getItemId")
    OutReportDTO getItemId(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping(PREFIX + "/updateResultById")
    void updateResultById(@RequestParam("id") Long id, @RequestParam("result") Integer result);

    @PostMapping(PREFIX + "/dealOutReportByProcessId")
    void dealOutReportByProcessId(@RequestParam("id") String id);
    //@GetMapping
    @PostMapping(PREFIX + "/getDateById")
    Map<String, LocalDate> getDateById(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getDeptId")
    CommonResult<Long> getDeptId(@RequestParam("processInstanceId") String processInstanceId);

}
