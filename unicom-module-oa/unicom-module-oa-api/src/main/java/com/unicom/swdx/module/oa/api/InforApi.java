//package com.unicom.swdx.module.oa.api;
//
//import com.unicom.swdx.module.oa.enums.ApiConstants;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.GetMapping;
//
//import java.util.Set;
//
//@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
//@Api(tags = "RPC 服务 - 中心工作")
//public interface InforApi {
//
//    String PREFIX = ApiConstants.PREFIX + "/infor";
//    @GetMapping(PREFIX+"/getLeadleaders")
//    Set<Integer> getLeadleaders();
//}
